"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PhoneService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const phone_entity_1 = require("./phone.entity");
let PhoneService = class PhoneService {
    phoneRepository;
    constructor(phoneRepository) {
        this.phoneRepository = phoneRepository;
    }
    async create(createPhoneDto) {
        const existingPhone = await this.phoneRepository.findOne({
            where: {
                model: createPhoneDto.model,
                brandId: createPhoneDto.brandId
            }
        });
        if (existingPhone) {
            throw new common_1.ConflictException(`Celular '${createPhoneDto.model}' já existe para esta marca`);
        }
        const phoneData = {
            ...createPhoneDto,
            price: Number(createPhoneDto.price),
            brandId: Number(createPhoneDto.brandId)
        };
        const phone = this.phoneRepository.create(phoneData);
        return this.phoneRepository.save(phone);
    }
    findAll() {
        return this.phoneRepository.find({
            relations: ['brand', 'accessories'],
        });
    }
    findOne(id) {
        return this.phoneRepository.findOne({
            where: { id },
            relations: ['brand', 'accessories'],
        });
    }
    findByBrand(brandId) {
        return this.phoneRepository.find({
            where: { brandId },
            relations: ['brand', 'accessories'],
        });
    }
    async update(id, updatePhoneDto) {
        if (updatePhoneDto.price) {
            const existingPhone = await this.phoneRepository.findOne({ where: { id } });
            if (!existingPhone) {
                throw new common_1.NotFoundException(`Celular com ID ${id} não encontrado`);
            }
            const currentPrice = Number(existingPhone.price);
            const newPrice = Number(updatePhoneDto.price);
            const reductionPercentage = ((currentPrice - newPrice) / currentPrice) * 100;
            if (reductionPercentage > 50) {
                throw new common_1.BadRequestException(`Redução de preço de ${reductionPercentage.toFixed(1)}% não permitida. Máximo permitido: 50%`);
            }
        }
        const updateData = { ...updatePhoneDto };
        if (updateData.price !== undefined) {
            updateData.price = Number(updateData.price);
        }
        if (updateData.brandId !== undefined) {
            updateData.brandId = Number(updateData.brandId);
        }
        return this.phoneRepository.update(id, updateData);
    }
    async remove(id) {
        const phone = await this.phoneRepository.findOne({ where: { id } });
        if (!phone) {
            throw new common_1.NotFoundException(`Celular com ID ${id} não encontrado`);
        }
        const phoneInSales = await this.phoneRepository
            .createQueryBuilder('phone')
            .innerJoin('sale_items', 'si', 'si.product_id = phone.id AND si.productType = :type', { type: 'phone' })
            .where('phone.id = :id', { id })
            .getOne();
        if (phoneInSales) {
            throw new common_1.BadRequestException(`Não é possível deletar celular que está associado a vendas. Modelo: ${phone.model}`);
        }
        return this.phoneRepository.delete(id);
    }
};
exports.PhoneService = PhoneService;
exports.PhoneService = PhoneService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(phone_entity_1.Phone)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PhoneService);
//# sourceMappingURL=phone.service.js.map