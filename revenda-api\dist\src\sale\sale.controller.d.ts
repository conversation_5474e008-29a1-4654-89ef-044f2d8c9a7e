import { SaleService } from './sale.service';
import { CreateSaleDto } from './dto/create-sale.dto';
import { UpdateSaleDto } from './dto/update-sale.dto';
export declare class SaleController {
    private readonly saleService;
    constructor(saleService: SaleService);
    create(createSaleDto: CreateSaleDto): Promise<import("./sale.entity").Sale | null>;
    findAll(status?: string): Promise<import("./sale.entity").Sale[]>;
    findOne(id: string): Promise<import("./sale.entity").Sale | null>;
    findByCustomer(customerId: string): Promise<import("./sale.entity").Sale[]>;
    findByStore(storeId: string): Promise<import("./sale.entity").Sale[]>;
    update(id: string, updateSaleDto: UpdateSaleDto): Promise<import("./sale.entity").Sale | null>;
    updateStatus(id: string, status: string): Promise<import("typeorm").UpdateResult>;
    remove(id: string): Promise<import("typeorm").DeleteResult>;
    getDashboardStats(): Promise<{
        totalSales: number;
        totalRevenue: number;
        monthlyRevenue: number;
        pendingSales: number;
    }>;
    getSalesByMonth(): Promise<any[]>;
    getTopProducts(): Promise<any[]>;
    getRecentSales(limit?: string): Promise<import("./sale.entity").Sale[]>;
}
