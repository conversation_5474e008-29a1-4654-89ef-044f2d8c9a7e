{"ast": null, "code": "var _AppRoutingModule;\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: 'home',\n  pathMatch: 'full'\n}, {\n  path: 'home',\n  loadChildren: () => import('./home/<USER>').then(m => m.HomePageModule)\n}, {\n  path: 'phones',\n  loadChildren: () => import('./phones/phones.module').then(m => m.PhonesPageModule)\n}, {\n  path: 'brands',\n  loadChildren: () => import('./brands/brands.module').then(m => m.BrandsPageModule)\n}, {\n  path: 'accessories',\n  loadChildren: () => import('./accessories/accessories.module').then(m => m.AccessoriesPageModule)\n}, {\n  path: 'stores',\n  loadChildren: () => import('./stores/stores.module').then(m => m.StoresPageModule)\n}, {\n  path: 'customers',\n  loadChildren: () => import('./customers/customers.module').then(m => m.CustomersPageModule)\n}, {\n  path: 'sales',\n  loadChildren: () => import('./sales/sales.module').then(m => m.SalesPageModule)\n}];\nexport class AppRoutingModule {}\n_AppRoutingModule = AppRoutingModule;\n_AppRoutingModule.ɵfac = function AppRoutingModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AppRoutingModule)();\n};\n_AppRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _AppRoutingModule\n});\n_AppRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [RouterModule.forRoot(routes), RouterModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "HomePageModule", "PhonesPageModule", "BrandsPageModule", "AccessoriesPageModule", "StoresPageModule", "CustomersPageModule", "SalesPageModule", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { PreloadAllModules, RouterModule, Routes } from '@angular/router';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    redirectTo: 'home',\r\n    pathMatch: 'full'\r\n  },\r\n  {\r\n    path: 'home',\r\n    loadChildren: () => import('./home/<USER>').then(m => m.HomePageModule)\r\n  },\r\n  {\r\n    path: 'phones',\r\n    loadChildren: () => import('./phones/phones.module').then(m => m.PhonesPageModule)\r\n  },\r\n  {\r\n    path: 'brands',\r\n    loadChildren: () => import('./brands/brands.module').then(m => m.BrandsPageModule)\r\n  },\r\n  {\r\n    path: 'accessories',\r\n    loadChildren: () => import('./accessories/accessories.module').then(m => m.AccessoriesPageModule)\r\n  },\r\n  {\r\n    path: 'stores',\r\n    loadChildren: () => import('./stores/stores.module').then( m => m.StoresPageModule)\r\n  },\r\n  {\r\n    path: 'customers',\r\n    loadChildren: () => import('./customers/customers.module').then( m => m.CustomersPageModule)\r\n  },\r\n  {\r\n    path: 'sales',\r\n    loadChildren: () => import('./sales/sales.module').then( m => m.SalesPageModule)\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [\r\n    RouterModule.forRoot(routes)\r\n  ],\r\n  exports: [RouterModule]\r\n})\r\nexport class AppRoutingModule {}\r\n"], "mappings": ";AACA,SAA4BA,YAAY,QAAgB,iBAAiB;;;AAEzE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,MAAM;EAClBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc;CAC5E,EACD;EACEN,IAAI,EAAE,QAAQ;EACdG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,gBAAgB;CAClF,EACD;EACEP,IAAI,EAAE,QAAQ;EACdG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,gBAAgB;CAClF,EACD;EACER,IAAI,EAAE,aAAa;EACnBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,qBAAqB;CACjG,EACD;EACET,IAAI,EAAE,QAAQ;EACdG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACK,gBAAgB;CACnF,EACD;EACEV,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACM,mBAAmB;CAC5F,EACD;EACEX,IAAI,EAAE,OAAO;EACbG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACO,eAAe;CAChF,CACF;AAQD,OAAM,MAAOC,gBAAgB;oBAAhBA,gBAAgB;;mCAAhBA,iBAAgB;AAAA;;QAAhBA;AAAgB;;YAJzBf,YAAY,CAACgB,OAAO,CAACf,MAAM,CAAC,EAEpBD,YAAY;AAAA;;2EAEXe,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAlB,YAAA;IAAAmB,OAAA,GAFjBnB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}