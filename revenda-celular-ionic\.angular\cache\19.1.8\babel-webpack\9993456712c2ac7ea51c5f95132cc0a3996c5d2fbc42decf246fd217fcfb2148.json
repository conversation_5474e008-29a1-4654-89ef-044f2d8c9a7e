{"ast": null, "code": "var _HomePageRoutingModule;\nimport { RouterModule } from '@angular/router';\nimport { HomePage } from './home.page';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: HomePage\n}];\nexport class HomePageRoutingModule {}\n_HomePageRoutingModule = HomePageRoutingModule;\n_HomePageRoutingModule.ɵfac = function HomePageRoutingModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HomePageRoutingModule)();\n};\n_HomePageRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _HomePageRoutingModule\n});\n_HomePageRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [RouterModule.forChild(routes), RouterModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HomePageRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "HomePage", "routes", "path", "component", "HomePageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\home\\home-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { HomePage } from './home.page';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: HomePage\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class HomePageRoutingModule {}\r\n"], "mappings": ";AACA,SAAiBA,YAAY,QAAQ,iBAAiB;AAEtD,SAASC,QAAQ,QAAQ,aAAa;;;AAEtC,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,qBAAqB;yBAArBA,qBAAqB;;mCAArBA,sBAAqB;AAAA;;QAArBA;AAAqB;;YAHtBL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;AAAA;;2EAEXK,qBAAqB;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAFtBT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}