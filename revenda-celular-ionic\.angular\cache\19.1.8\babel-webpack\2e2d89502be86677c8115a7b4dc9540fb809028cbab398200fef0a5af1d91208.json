{"ast": null, "code": "var _HomePage;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../sales/services/sale.service\";\nimport * as i2 from \"../phones/services/phone.service\";\nimport * as i3 from \"../accessories/services/accessory.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ionic/angular\";\nimport * as i6 from \"@angular/router\";\nconst _c0 = a0 => [\"/sales/details\", a0];\nconst _forTrack0 = ($index, $item) => $item.productId;\nconst _forTrack1 = ($index, $item) => $item.id;\nfunction HomePage_For_64_ion_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 23);\n  }\n}\nfunction HomePage_For_64_ion_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 24);\n  }\n}\nfunction HomePage_For_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\");\n    i0.ɵɵtemplate(1, HomePage_For_64_ion_icon_1_Template, 1, 0, \"ion-icon\", 20)(2, HomePage_For_64_ion_icon_2_Template, 1, 0, \"ion-icon\", 21);\n    i0.ɵɵelementStart(3, \"ion-label\")(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"ion-note\", 22);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r1.productType === \"phone\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r1.productType === \"accessory\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r1.name || (product_r1.productType === \"phone\" ? \"Celular\" : \"Acess\\u00F3rio\") + \" #\" + product_r1.productId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", product_r1.totalQuantity, \" unidades vendidas\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 5, product_r1.totalRevenue, \"BRL\"));\n  }\n}\nfunction HomePage_ForEmpty_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\")(1, \"ion-label\");\n    i0.ɵɵtext(2, \"Nenhum produto vendido no per\\u00EDodo\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HomePage_For_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\", 19)(1, \"ion-label\")(2, \"h2\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-text\", 25);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 26)(12, \"ion-badge\", 27);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ion-text\", 28)(15, \"h3\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"currency\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const sale_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(14, _c0, sale_r2.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Venda #\", sale_r2.id, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 8, sale_r2.date, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Cliente: \", (sale_r2.customer == null ? null : sale_r2.customer.name) || \"N/A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Loja: \", (sale_r2.store == null ? null : sale_r2.store.name) || \"N/A\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", ctx_r2.getStatusColor(sale_r2.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getStatusLabel(sale_r2.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 11, sale_r2.totalValue, \"BRL\"));\n  }\n}\nfunction HomePage_ForEmpty_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\")(1, \"ion-label\");\n    i0.ɵɵtext(2, \"Nenhuma venda recente\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class HomePage {\n  constructor(saleService, phoneService, accessoryService) {\n    this.saleService = saleService;\n    this.phoneService = phoneService;\n    this.accessoryService = accessoryService;\n    this.dashboardStats = {\n      totalSales: 0,\n      totalRevenue: 0,\n      monthlyRevenue: 0,\n      pendingSales: 0\n    };\n    this.topProducts = [];\n    this.monthlyData = [];\n    this.recentSales = [];\n  }\n  ngOnInit() {\n    this.loadDashboardData();\n  }\n  ionViewWillEnter() {\n    this.loadDashboardData();\n  }\n  loadDashboardData() {\n    // Carregar estatísticas do dashboard\n    this.saleService.getDashboardStats().subscribe({\n      next: stats => {\n        this.dashboardStats = stats;\n      },\n      error: error => {\n        console.error('Erro ao carregar estatísticas', error);\n      }\n    });\n    // Carregar dados mensais\n    this.saleService.getSalesByMonth().subscribe({\n      next: data => {\n        this.monthlyData = data;\n      },\n      error: error => {\n        console.error('Erro ao carregar dados mensais', error);\n      }\n    });\n    // Carregar top produtos\n    this.saleService.getTopProducts().subscribe({\n      next: products => {\n        this.topProducts = products;\n        this.loadProductNames();\n      },\n      error: error => {\n        console.error('Erro ao carregar top produtos', error);\n      }\n    });\n    // Carregar vendas recentes\n    this.saleService.getRecentSales(5).subscribe({\n      next: sales => {\n        this.recentSales = sales;\n      },\n      error: error => {\n        console.error('Erro ao carregar vendas recentes', error);\n      }\n    });\n  }\n  loadProductNames() {\n    // Carregar nomes dos produtos para os top produtos\n    this.topProducts.forEach(product => {\n      if (product.productType === 'phone') {\n        this.phoneService.getById(product.productId).subscribe({\n          next: phone => {\n            product.name = phone.model;\n          },\n          error: error => {\n            console.error('Erro ao carregar nome do celular', error);\n            product.name = `Celular #${product.productId}`;\n          }\n        });\n      } else if (product.productType === 'accessory') {\n        this.accessoryService.getById(product.productId).subscribe({\n          next: accessory => {\n            product.name = accessory.name;\n          },\n          error: error => {\n            console.error('Erro ao carregar nome do acessório', error);\n            product.name = `Acessório #${product.productId}`;\n          }\n        });\n      }\n    });\n  }\n  getAverageTicket() {\n    return this.dashboardStats.totalSales > 0 ? this.dashboardStats.totalRevenue / this.dashboardStats.totalSales : 0;\n  }\n  getMonthName(month) {\n    const months = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];\n    return months[month - 1] || '';\n  }\n  getStatusLabel(status) {\n    const statusMap = {\n      'pending': 'Pendente',\n      'completed': 'Concluída',\n      'canceled': 'Cancelada'\n    };\n    return statusMap[status] || status;\n  }\n  getStatusColor(status) {\n    const colorMap = {\n      'pending': 'warning',\n      'completed': 'success',\n      'canceled': 'danger'\n    };\n    return colorMap[status] || 'medium';\n  }\n}\n_HomePage = HomePage;\n_HomePage.ɵfac = function HomePage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HomePage)(i0.ɵɵdirectiveInject(i1.SaleService), i0.ɵɵdirectiveInject(i2.PhoneService), i0.ɵɵdirectiveInject(i3.AccessoryService));\n};\n_HomePage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _HomePage,\n  selectors: [[\"app-home\"]],\n  standalone: false,\n  decls: 78,\n  vars: 19,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [3, \"fullscreen\"], [1, \"dashboard-container\"], [1, \"summary-card\"], [1, \"financial-summary\"], [1, \"summary-item\"], [1, \"stats-container\"], [1, \"stats-card\"], [1, \"stats-grid\"], [1, \"stats-item\"], [\"name\", \"bag-check-outline\", \"color\", \"secondary\"], [\"name\", \"time-outline\", \"color\", \"warning\"], [\"name\", \"trending-up-outline\", \"color\", \"success\"], [\"lines\", \"none\"], [1, \"recent-sales-card\"], [\"fill\", \"clear\", \"routerLink\", \"/sales\"], [\"name\", \"arrow-forward-outline\", \"slot\", \"end\"], [\"button\", \"\", 3, \"routerLink\"], [\"name\", \"phone-portrait-outline\", \"slot\", \"start\", 4, \"ngIf\"], [\"name\", \"hardware-chip-outline\", \"slot\", \"start\", 4, \"ngIf\"], [\"slot\", \"end\", \"color\", \"secondary\"], [\"name\", \"phone-portrait-outline\", \"slot\", \"start\"], [\"name\", \"hardware-chip-outline\", \"slot\", \"start\"], [\"color\", \"medium\"], [\"slot\", \"end\", 1, \"sale-info\"], [3, \"color\"], [\"color\", \"dark\"]],\n  template: function HomePage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-menu-button\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5, \"Dashboard\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 3)(7, \"div\", 4)(8, \"ion-card\", 5)(9, \"ion-card-header\")(10, \"ion-card-title\");\n      i0.ɵɵtext(11, \"Resumo Financeiro\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(12, \"ion-card-content\")(13, \"div\", 6)(14, \"div\", 7)(15, \"h2\");\n      i0.ɵɵtext(16);\n      i0.ɵɵpipe(17, \"currency\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"p\");\n      i0.ɵɵtext(19, \"Faturamento Total\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(20, \"div\", 7)(21, \"h2\");\n      i0.ɵɵtext(22);\n      i0.ɵɵpipe(23, \"currency\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"p\");\n      i0.ɵɵtext(25, \"Faturamento do M\\u00EAs\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(26, \"div\", 7)(27, \"h2\");\n      i0.ɵɵtext(28);\n      i0.ɵɵpipe(29, \"currency\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"p\");\n      i0.ɵɵtext(31, \"Ticket M\\u00E9dio\");\n      i0.ɵɵelementEnd()()()()();\n      i0.ɵɵelementStart(32, \"div\", 8)(33, \"ion-card\", 9)(34, \"ion-card-header\")(35, \"ion-card-title\");\n      i0.ɵɵtext(36, \"Vendas\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(37, \"ion-card-content\")(38, \"div\", 10)(39, \"div\", 11);\n      i0.ɵɵelement(40, \"ion-icon\", 12);\n      i0.ɵɵelementStart(41, \"h3\");\n      i0.ɵɵtext(42);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"p\");\n      i0.ɵɵtext(44, \"Total de Vendas\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(45, \"div\", 11);\n      i0.ɵɵelement(46, \"ion-icon\", 13);\n      i0.ɵɵelementStart(47, \"h3\");\n      i0.ɵɵtext(48);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"p\");\n      i0.ɵɵtext(50, \"Vendas Pendentes\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(51, \"div\", 11);\n      i0.ɵɵelement(52, \"ion-icon\", 14);\n      i0.ɵɵelementStart(53, \"h3\");\n      i0.ɵɵtext(54);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(55, \"p\");\n      i0.ɵɵtext(56, \"Meses com Vendas\");\n      i0.ɵɵelementEnd()()()()();\n      i0.ɵɵelementStart(57, \"ion-card\", 9)(58, \"ion-card-header\")(59, \"ion-card-title\");\n      i0.ɵɵtext(60, \"Produtos Mais Vendidos\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(61, \"ion-card-content\")(62, \"ion-list\", 15);\n      i0.ɵɵrepeaterCreate(63, HomePage_For_64_Template, 11, 8, \"ion-item\", null, _forTrack0, false, HomePage_ForEmpty_65_Template, 3, 0, \"ion-item\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(66, \"ion-card\", 16)(67, \"ion-card-header\")(68, \"ion-card-title\");\n      i0.ɵɵtext(69, \"\\u00DAltimas Vendas\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(70, \"ion-button\", 17);\n      i0.ɵɵtext(71, \" Ver Todas \");\n      i0.ɵɵelement(72, \"ion-icon\", 18);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(73, \"ion-card-content\")(74, \"ion-list\");\n      i0.ɵɵrepeaterCreate(75, HomePage_For_76_Template, 18, 16, \"ion-item\", 19, _forTrack1, false, HomePage_ForEmpty_77_Template, 3, 0, \"ion-item\");\n      i0.ɵɵelementEnd()()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(10);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 10, ctx.dashboardStats.totalRevenue, \"BRL\"));\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 13, ctx.dashboardStats.monthlyRevenue, \"BRL\"));\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(29, 16, ctx.getAverageTicket(), \"BRL\"));\n      i0.ɵɵadvance(14);\n      i0.ɵɵtextInterpolate(ctx.dashboardStats.totalSales);\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate(ctx.dashboardStats.pendingSales);\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate(ctx.monthlyData.length);\n      i0.ɵɵadvance(9);\n      i0.ɵɵrepeater(ctx.topProducts);\n      i0.ɵɵadvance(12);\n      i0.ɵɵrepeater(ctx.recentSales);\n    }\n  },\n  dependencies: [i4.NgIf, i5.IonBadge, i5.IonButton, i5.IonButtons, i5.IonCard, i5.IonCardContent, i5.IonCardHeader, i5.IonCardTitle, i5.IonContent, i5.IonHeader, i5.IonIcon, i5.IonItem, i5.IonLabel, i5.IonList, i5.IonMenuButton, i5.IonNote, i5.IonText, i5.IonTitle, i5.IonToolbar, i5.RouterLinkDelegate, i6.RouterLink, i4.CurrencyPipe, i4.DatePipe],\n  styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.summary-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.summary-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\\n  background-color: var(--ion-color-secondary);\\n  color: white;\\n  border-radius: 12px 12px 0 0;\\n}\\n.summary-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.financial-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 10px 0;\\n}\\n.financial-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 10px;\\n  min-width: 120px;\\n}\\n.financial-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  margin: 0;\\n  color: var(--ion-color-secondary);\\n}\\n.financial-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0;\\n  color: var(--ion-color-medium);\\n  font-size: 0.9rem;\\n}\\n\\n.stats-container[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n@media (min-width: 768px) {\\n  .stats-container[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr;\\n  }\\n}\\n\\n.stats-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  height: 100%;\\n}\\n.stats-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\\n  background-color: var(--ion-color-light);\\n}\\n.stats-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\\n  gap: 15px;\\n  padding: 10px 0;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stats-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 10px;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stats-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 8px;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stats-item[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  margin: 0;\\n  color: var(--ion-color-dark);\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stats-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0;\\n  color: var(--ion-color-medium);\\n  font-size: 0.9rem;\\n}\\n\\n.recent-sales-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.recent-sales-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.recent-sales-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n}\\n.recent-sales-card[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\\n  --padding-start: 16px;\\n  --padding-end: 16px;\\n  --padding-top: 12px;\\n  --padding-bottom: 12px;\\n}\\n.recent-sales-card[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.sale-info[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n.sale-info[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  padding: 6px 8px;\\n  border-radius: 4px;\\n}\\n.sale-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  margin: 8px 0 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtemplate", "HomePage_For_64_ion_icon_1_Template", "HomePage_For_64_ion_icon_2_Template", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "product_r1", "productType", "ɵɵtextInterpolate", "name", "productId", "ɵɵtextInterpolate1", "totalQuantity", "ɵɵpipeBind2", "totalRevenue", "ɵɵpureFunction1", "_c0", "sale_r2", "id", "date", "customer", "store", "ctx_r2", "getStatusColor", "status", "getStatusLabel", "totalValue", "HomePage", "constructor", "saleService", "phoneService", "accessoryService", "dashboardStats", "totalSales", "monthlyRevenue", "pendingSales", "topProducts", "monthlyData", "recentSales", "ngOnInit", "loadDashboardData", "ionViewWillEnter", "getDashboardStats", "subscribe", "next", "stats", "error", "console", "getSalesByMonth", "data", "getTopProducts", "products", "loadProductNames", "getRecentSales", "sales", "for<PERSON>ach", "product", "getById", "phone", "model", "accessory", "getAverageTicket", "getMonthName", "month", "months", "statusMap", "colorMap", "ɵɵdirectiveInject", "i1", "SaleService", "i2", "PhoneService", "i3", "AccessoryService", "selectors", "standalone", "decls", "vars", "consts", "template", "HomePage_Template", "rf", "ctx", "ɵɵrepeaterCreate", "HomePage_For_64_Template", "_forTrack0", "HomePage_ForEmpty_65_Template", "HomePage_For_76_Template", "_forTrack1", "HomePage_ForEmpty_77_Template", "length", "ɵɵrepeater"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\home\\home.page.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\home\\home.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SaleService } from '../sales/services/sale.service';\r\nimport { Sale, DashboardStats } from '../sales/models/sale.type';\r\nimport { PhoneService } from '../phones/services/phone.service';\r\nimport { AccessoryService } from '../accessories/services/accessory.service';\r\n\r\ninterface TopProduct {\r\n  productId: number;\r\n  productType: 'phone' | 'accessory';\r\n  name?: string;\r\n  totalQuantity: number;\r\n  totalRevenue: number;\r\n}\r\n\r\ninterface MonthlyData {\r\n  month: number;\r\n  year: number;\r\n  count: number;\r\n  revenue: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: 'home.page.html',\r\n  styleUrls: ['home.page.scss'],\r\n  standalone: false,\r\n})\r\nexport class HomePage implements OnInit {\r\n  dashboardStats: DashboardStats = {\r\n    totalSales: 0,\r\n    totalRevenue: 0,\r\n    monthlyRevenue: 0,\r\n    pendingSales: 0\r\n  };\r\n\r\n  topProducts: TopProduct[] = [];\r\n  monthlyData: MonthlyData[] = [];\r\n  recentSales: Sale[] = [];\r\n\r\n  constructor(\r\n    private saleService: SaleService,\r\n    private phoneService: PhoneService,\r\n    private accessoryService: AccessoryService\r\n  ) {}\r\n  \r\n  ngOnInit() {\r\n    this.loadDashboardData();\r\n  }\r\n  \r\n  ionViewWillEnter() {\r\n    this.loadDashboardData();\r\n  }\r\n  \r\n  loadDashboardData() {\r\n    // Carregar estatísticas do dashboard\r\n    this.saleService.getDashboardStats().subscribe({\r\n      next: (stats) => {\r\n        this.dashboardStats = stats;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar estatísticas', error);\r\n      }\r\n    });\r\n\r\n    // Carregar dados mensais\r\n    this.saleService.getSalesByMonth().subscribe({\r\n      next: (data) => {\r\n        this.monthlyData = data;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar dados mensais', error);\r\n      }\r\n    });\r\n\r\n    // Carregar top produtos\r\n    this.saleService.getTopProducts().subscribe({\r\n      next: (products) => {\r\n        this.topProducts = products;\r\n        this.loadProductNames();\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar top produtos', error);\r\n      }\r\n    });\r\n\r\n    // Carregar vendas recentes\r\n    this.saleService.getRecentSales(5).subscribe({\r\n      next: (sales) => {\r\n        this.recentSales = sales;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar vendas recentes', error);\r\n      }\r\n    });\r\n  }\r\n  \r\n  loadProductNames() {\r\n    // Carregar nomes dos produtos para os top produtos\r\n    this.topProducts.forEach(product => {\r\n      if (product.productType === 'phone') {\r\n        this.phoneService.getById(product.productId).subscribe({\r\n          next: (phone) => {\r\n            product.name = phone.model;\r\n          },\r\n          error: (error) => {\r\n            console.error('Erro ao carregar nome do celular', error);\r\n            product.name = `Celular #${product.productId}`;\r\n          }\r\n        });\r\n      } else if (product.productType === 'accessory') {\r\n        this.accessoryService.getById(product.productId).subscribe({\r\n          next: (accessory) => {\r\n            product.name = accessory.name;\r\n          },\r\n          error: (error) => {\r\n            console.error('Erro ao carregar nome do acessório', error);\r\n            product.name = `Acessório #${product.productId}`;\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  getAverageTicket(): number {\r\n    return this.dashboardStats.totalSales > 0\r\n      ? this.dashboardStats.totalRevenue / this.dashboardStats.totalSales\r\n      : 0;\r\n  }\r\n  \r\n  getMonthName(month: number): string {\r\n    const months = [\r\n      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',\r\n      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'\r\n    ];\r\n    return months[month - 1] || '';\r\n  }\r\n  \r\n  getStatusLabel(status: string): string {\r\n    const statusMap: {[key: string]: string} = {\r\n      'pending': 'Pendente',\r\n      'completed': 'Concluída',\r\n      'canceled': 'Cancelada'\r\n    };\r\n    return statusMap[status] || status;\r\n  }\r\n  \r\n  getStatusColor(status: string): string {\r\n    const colorMap: {[key: string]: string} = {\r\n      'pending': 'warning',\r\n      'completed': 'success',\r\n      'canceled': 'danger'\r\n    };\r\n    return colorMap[status] || 'medium';\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-menu-button></ion-menu-button>\r\n    </ion-buttons>\r\n    <ion-title>Dashboard</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div class=\"dashboard-container\">\r\n    <ion-card class=\"summary-card\">\r\n      <ion-card-header>\r\n        <ion-card-title>Resumo Financeiro</ion-card-title>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <div class=\"financial-summary\">\r\n          <div class=\"summary-item\">\r\n            <h2>{{ dashboardStats.totalRevenue | currency:'BRL' }}</h2>\r\n            <p>Faturamento Total</p>\r\n          </div>\r\n          <div class=\"summary-item\">\r\n            <h2>{{ dashboardStats.monthlyRevenue | currency:'BRL' }}</h2>\r\n            <p>Faturamento do Mês</p>\r\n          </div>\r\n          <div class=\"summary-item\">\r\n            <h2>{{ getAverageTicket() | currency:'BRL' }}</h2>\r\n            <p>Ticket Médio</p>\r\n          </div>\r\n        </div>\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <div class=\"stats-container\">\r\n      <ion-card class=\"stats-card\">\r\n        <ion-card-header>\r\n          <ion-card-title>Vendas</ion-card-title>\r\n        </ion-card-header>\r\n        <ion-card-content>\r\n          <div class=\"stats-grid\">\r\n            <div class=\"stats-item\">\r\n              <ion-icon name=\"bag-check-outline\" color=\"secondary\"></ion-icon>\r\n              <h3>{{ dashboardStats.totalSales }}</h3>\r\n              <p>Total de Vendas</p>\r\n            </div>\r\n            <div class=\"stats-item\">\r\n              <ion-icon name=\"time-outline\" color=\"warning\"></ion-icon>\r\n              <h3>{{ dashboardStats.pendingSales }}</h3>\r\n              <p>Vendas Pendentes</p>\r\n            </div>\r\n            <div class=\"stats-item\">\r\n              <ion-icon name=\"trending-up-outline\" color=\"success\"></ion-icon>\r\n              <h3>{{ monthlyData.length }}</h3>\r\n              <p>Meses com Vendas</p>\r\n            </div>\r\n          </div>\r\n        </ion-card-content>\r\n      </ion-card>\r\n\r\n      <ion-card class=\"stats-card\">\r\n        <ion-card-header>\r\n          <ion-card-title>Produtos Mais Vendidos</ion-card-title>\r\n        </ion-card-header>\r\n        <ion-card-content>\r\n          <ion-list lines=\"none\">\r\n            @for(product of topProducts; track product.productId) {\r\n              <ion-item>\r\n                <ion-icon name=\"phone-portrait-outline\" slot=\"start\" *ngIf=\"product.productType === 'phone'\"></ion-icon>\r\n                <ion-icon name=\"hardware-chip-outline\" slot=\"start\" *ngIf=\"product.productType === 'accessory'\"></ion-icon>\r\n                <ion-label>\r\n                  <h3>{{ product.name || (product.productType === 'phone' ? 'Celular' : 'Acessório') + ' #' + product.productId }}</h3>\r\n                  <p>{{ product.totalQuantity }} unidades vendidas</p>\r\n                </ion-label>\r\n                <ion-note slot=\"end\" color=\"secondary\">{{ product.totalRevenue | currency:'BRL' }}</ion-note>\r\n              </ion-item>\r\n            }\r\n            @empty {\r\n              <ion-item>\r\n                <ion-label>Nenhum produto vendido no período</ion-label>\r\n              </ion-item>\r\n            }\r\n          </ion-list>\r\n        </ion-card-content>\r\n      </ion-card>\r\n    </div>\r\n\r\n    <ion-card class=\"recent-sales-card\">\r\n      <ion-card-header>\r\n        <ion-card-title>Últimas Vendas</ion-card-title>\r\n        <ion-button fill=\"clear\" routerLink=\"/sales\">\r\n          Ver Todas\r\n          <ion-icon name=\"arrow-forward-outline\" slot=\"end\"></ion-icon>\r\n        </ion-button>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <ion-list>\r\n          @for(sale of recentSales; track sale.id) {\r\n            <ion-item button [routerLink]=\"['/sales/details', sale.id]\">\r\n              <ion-label>\r\n                <h2>Venda #{{ sale.id }}</h2>\r\n                <ion-text color=\"medium\">{{ sale.date | date:'dd/MM/yyyy' }}</ion-text>\r\n                <p>Cliente: {{ sale.customer?.name || 'N/A' }}</p>\r\n                <p>Loja: {{ sale.store?.name || 'N/A' }}</p>\r\n              </ion-label>\r\n              <div class=\"sale-info\" slot=\"end\">\r\n                <ion-badge [color]=\"getStatusColor(sale.status)\">{{ getStatusLabel(sale.status) }}</ion-badge>\r\n                <ion-text color=\"dark\">\r\n                  <h3>{{ sale.totalValue | currency:'BRL' }}</h3>\r\n                </ion-text>\r\n              </div>\r\n            </ion-item>\r\n          }\r\n          @empty {\r\n            <ion-item>\r\n              <ion-label>Nenhuma venda recente</ion-label>\r\n            </ion-item>\r\n          }\r\n        </ion-list>\r\n      </ion-card-content>\r\n    </ion-card>\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;;;;ICmEgBA,EAAA,CAAAC,SAAA,mBAAwG;;;;;IACxGD,EAAA,CAAAC,SAAA,mBAA2G;;;;;IAF7GD,EAAA,CAAAE,cAAA,eAAU;IAERF,EADA,CAAAG,UAAA,IAAAC,mCAAA,uBAA6F,IAAAC,mCAAA,uBACG;IAE9FL,EADF,CAAAE,cAAA,gBAAW,SACL;IAAAF,EAAA,CAAAM,MAAA,GAA4G;IAAAN,EAAA,CAAAO,YAAA,EAAK;IACrHP,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAM,MAAA,GAA6C;IAClDN,EADkD,CAAAO,YAAA,EAAI,EAC1C;IACZP,EAAA,CAAAE,cAAA,mBAAuC;IAAAF,EAAA,CAAAM,MAAA,GAA2C;;IACpFN,EADoF,CAAAO,YAAA,EAAW,EACpF;;;;IAP6CP,EAAA,CAAAQ,SAAA,EAAqC;IAArCR,EAAA,CAAAS,UAAA,SAAAC,UAAA,CAAAC,WAAA,aAAqC;IACtCX,EAAA,CAAAQ,SAAA,EAAyC;IAAzCR,EAAA,CAAAS,UAAA,SAAAC,UAAA,CAAAC,WAAA,iBAAyC;IAExFX,EAAA,CAAAQ,SAAA,GAA4G;IAA5GR,EAAA,CAAAY,iBAAA,CAAAF,UAAA,CAAAG,IAAA,KAAAH,UAAA,CAAAC,WAAA,sDAAAD,UAAA,CAAAI,SAAA,CAA4G;IAC7Gd,EAAA,CAAAQ,SAAA,GAA6C;IAA7CR,EAAA,CAAAe,kBAAA,KAAAL,UAAA,CAAAM,aAAA,uBAA6C;IAEXhB,EAAA,CAAAQ,SAAA,GAA2C;IAA3CR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAiB,WAAA,QAAAP,UAAA,CAAAQ,YAAA,SAA2C;;;;;IAKlFlB,EADF,CAAAE,cAAA,eAAU,gBACG;IAAAF,EAAA,CAAAM,MAAA,6CAAiC;IAC9CN,EAD8C,CAAAO,YAAA,EAAY,EAC/C;;;;;IAoBTP,EAFJ,CAAAE,cAAA,mBAA4D,gBAC/C,SACL;IAAAF,EAAA,CAAAM,MAAA,GAAoB;IAAAN,EAAA,CAAAO,YAAA,EAAK;IAC7BP,EAAA,CAAAE,cAAA,mBAAyB;IAAAF,EAAA,CAAAM,MAAA,GAAmC;;IAAAN,EAAA,CAAAO,YAAA,EAAW;IACvEP,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAM,MAAA,GAA2C;IAAAN,EAAA,CAAAO,YAAA,EAAI;IAClDP,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAM,MAAA,IAAqC;IAC1CN,EAD0C,CAAAO,YAAA,EAAI,EAClC;IAEVP,EADF,CAAAE,cAAA,eAAkC,qBACiB;IAAAF,EAAA,CAAAM,MAAA,IAAiC;IAAAN,EAAA,CAAAO,YAAA,EAAY;IAE5FP,EADF,CAAAE,cAAA,oBAAuB,UACjB;IAAAF,EAAA,CAAAM,MAAA,IAAsC;;IAGhDN,EAHgD,CAAAO,YAAA,EAAK,EACtC,EACP,EACG;;;;;IAbMP,EAAA,CAAAS,UAAA,eAAAT,EAAA,CAAAmB,eAAA,KAAAC,GAAA,EAAAC,OAAA,CAAAC,EAAA,EAA0C;IAEnDtB,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAe,kBAAA,YAAAM,OAAA,CAAAC,EAAA,KAAoB;IACCtB,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAiB,WAAA,OAAAI,OAAA,CAAAE,IAAA,gBAAmC;IACzDvB,EAAA,CAAAQ,SAAA,GAA2C;IAA3CR,EAAA,CAAAe,kBAAA,eAAAM,OAAA,CAAAG,QAAA,kBAAAH,OAAA,CAAAG,QAAA,CAAAX,IAAA,eAA2C;IAC3Cb,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAe,kBAAA,YAAAM,OAAA,CAAAI,KAAA,kBAAAJ,OAAA,CAAAI,KAAA,CAAAZ,IAAA,eAAqC;IAG7Bb,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAS,UAAA,UAAAiB,MAAA,CAAAC,cAAA,CAAAN,OAAA,CAAAO,MAAA,EAAqC;IAAC5B,EAAA,CAAAQ,SAAA,EAAiC;IAAjCR,EAAA,CAAAY,iBAAA,CAAAc,MAAA,CAAAG,cAAA,CAAAR,OAAA,CAAAO,MAAA,EAAiC;IAE5E5B,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAiB,WAAA,SAAAI,OAAA,CAAAS,UAAA,SAAsC;;;;;IAO9C9B,EADF,CAAAE,cAAA,eAAU,gBACG;IAAAF,EAAA,CAAAM,MAAA,4BAAqB;IAClCN,EADkC,CAAAO,YAAA,EAAY,EACnC;;;ADxFvB,OAAM,MAAOwB,QAAQ;EAYnBC,YACUC,WAAwB,EACxBC,YAA0B,EAC1BC,gBAAkC;IAFlC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAd1B,KAAAC,cAAc,GAAmB;MAC/BC,UAAU,EAAE,CAAC;MACbnB,YAAY,EAAE,CAAC;MACfoB,cAAc,EAAE,CAAC;MACjBC,YAAY,EAAE;KACf;IAED,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,WAAW,GAAkB,EAAE;IAC/B,KAAAC,WAAW,GAAW,EAAE;EAMrB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACD,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACX,WAAW,CAACa,iBAAiB,EAAE,CAACC,SAAS,CAAC;MAC7CC,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAACb,cAAc,GAAGa,KAAK;MAC7B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;IAEF;IACA,IAAI,CAACjB,WAAW,CAACmB,eAAe,EAAE,CAACL,SAAS,CAAC;MAC3CC,IAAI,EAAGK,IAAI,IAAI;QACb,IAAI,CAACZ,WAAW,GAAGY,IAAI;MACzB,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;IAEF;IACA,IAAI,CAACjB,WAAW,CAACqB,cAAc,EAAE,CAACP,SAAS,CAAC;MAC1CC,IAAI,EAAGO,QAAQ,IAAI;QACjB,IAAI,CAACf,WAAW,GAAGe,QAAQ;QAC3B,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC;MACDN,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;IAEF;IACA,IAAI,CAACjB,WAAW,CAACwB,cAAc,CAAC,CAAC,CAAC,CAACV,SAAS,CAAC;MAC3CC,IAAI,EAAGU,KAAK,IAAI;QACd,IAAI,CAAChB,WAAW,GAAGgB,KAAK;MAC1B,CAAC;MACDR,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;KACD,CAAC;EACJ;EAEAM,gBAAgBA,CAAA;IACd;IACA,IAAI,CAAChB,WAAW,CAACmB,OAAO,CAACC,OAAO,IAAG;MACjC,IAAIA,OAAO,CAACjD,WAAW,KAAK,OAAO,EAAE;QACnC,IAAI,CAACuB,YAAY,CAAC2B,OAAO,CAACD,OAAO,CAAC9C,SAAS,CAAC,CAACiC,SAAS,CAAC;UACrDC,IAAI,EAAGc,KAAK,IAAI;YACdF,OAAO,CAAC/C,IAAI,GAAGiD,KAAK,CAACC,KAAK;UAC5B,CAAC;UACDb,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;YACxDU,OAAO,CAAC/C,IAAI,GAAG,YAAY+C,OAAO,CAAC9C,SAAS,EAAE;UAChD;SACD,CAAC;MACJ,CAAC,MAAM,IAAI8C,OAAO,CAACjD,WAAW,KAAK,WAAW,EAAE;QAC9C,IAAI,CAACwB,gBAAgB,CAAC0B,OAAO,CAACD,OAAO,CAAC9C,SAAS,CAAC,CAACiC,SAAS,CAAC;UACzDC,IAAI,EAAGgB,SAAS,IAAI;YAClBJ,OAAO,CAAC/C,IAAI,GAAGmD,SAAS,CAACnD,IAAI;UAC/B,CAAC;UACDqC,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;YAC1DU,OAAO,CAAC/C,IAAI,GAAG,cAAc+C,OAAO,CAAC9C,SAAS,EAAE;UAClD;SACD,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEAmD,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC7B,cAAc,CAACC,UAAU,GAAG,CAAC,GACrC,IAAI,CAACD,cAAc,CAAClB,YAAY,GAAG,IAAI,CAACkB,cAAc,CAACC,UAAU,GACjE,CAAC;EACP;EAEA6B,YAAYA,CAACC,KAAa;IACxB,MAAMC,MAAM,GAAG,CACb,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACxC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CACzC;IACD,OAAOA,MAAM,CAACD,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE;EAChC;EAEAtC,cAAcA,CAACD,MAAc;IAC3B,MAAMyC,SAAS,GAA4B;MACzC,SAAS,EAAE,UAAU;MACrB,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE;KACb;IACD,OAAOA,SAAS,CAACzC,MAAM,CAAC,IAAIA,MAAM;EACpC;EAEAD,cAAcA,CAACC,MAAc;IAC3B,MAAM0C,QAAQ,GAA4B;MACxC,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE;KACb;IACD,OAAOA,QAAQ,CAAC1C,MAAM,CAAC,IAAI,QAAQ;EACrC;;YA9HWG,QAAQ;;mCAARA,SAAQ,EAAA/B,EAAA,CAAAuE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzE,EAAA,CAAAuE,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAA3E,EAAA,CAAAuE,iBAAA,CAAAK,EAAA,CAAAC,gBAAA;AAAA;;QAAR9C,SAAQ;EAAA+C,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCzBjBrF,EAFJ,CAAAE,cAAA,oBAAiC,qBACA,qBACH;MACxBF,EAAA,CAAAC,SAAA,sBAAmC;MACrCD,EAAA,CAAAO,YAAA,EAAc;MACdP,EAAA,CAAAE,cAAA,gBAAW;MAAAF,EAAA,CAAAM,MAAA,gBAAS;MAExBN,EAFwB,CAAAO,YAAA,EAAY,EACpB,EACH;MAMLP,EAJR,CAAAE,cAAA,qBAAiC,aACE,kBACA,sBACZ,sBACC;MAAAF,EAAA,CAAAM,MAAA,yBAAiB;MACnCN,EADmC,CAAAO,YAAA,EAAiB,EAClC;MAIZP,EAHN,CAAAE,cAAA,wBAAkB,cACe,cACH,UACpB;MAAAF,EAAA,CAAAM,MAAA,IAAkD;;MAAAN,EAAA,CAAAO,YAAA,EAAK;MAC3DP,EAAA,CAAAE,cAAA,SAAG;MAAAF,EAAA,CAAAM,MAAA,yBAAiB;MACtBN,EADsB,CAAAO,YAAA,EAAI,EACpB;MAEJP,EADF,CAAAE,cAAA,cAA0B,UACpB;MAAAF,EAAA,CAAAM,MAAA,IAAoD;;MAAAN,EAAA,CAAAO,YAAA,EAAK;MAC7DP,EAAA,CAAAE,cAAA,SAAG;MAAAF,EAAA,CAAAM,MAAA,+BAAkB;MACvBN,EADuB,CAAAO,YAAA,EAAI,EACrB;MAEJP,EADF,CAAAE,cAAA,cAA0B,UACpB;MAAAF,EAAA,CAAAM,MAAA,IAAyC;;MAAAN,EAAA,CAAAO,YAAA,EAAK;MAClDP,EAAA,CAAAE,cAAA,SAAG;MAAAF,EAAA,CAAAM,MAAA,yBAAY;MAIvBN,EAJuB,CAAAO,YAAA,EAAI,EACf,EACF,EACW,EACV;MAKLP,EAHN,CAAAE,cAAA,cAA6B,mBACE,uBACV,sBACC;MAAAF,EAAA,CAAAM,MAAA,cAAM;MACxBN,EADwB,CAAAO,YAAA,EAAiB,EACvB;MAGdP,EAFJ,CAAAE,cAAA,wBAAkB,eACQ,eACE;MACtBF,EAAA,CAAAC,SAAA,oBAAgE;MAChED,EAAA,CAAAE,cAAA,UAAI;MAAAF,EAAA,CAAAM,MAAA,IAA+B;MAAAN,EAAA,CAAAO,YAAA,EAAK;MACxCP,EAAA,CAAAE,cAAA,SAAG;MAAAF,EAAA,CAAAM,MAAA,uBAAe;MACpBN,EADoB,CAAAO,YAAA,EAAI,EAClB;MACNP,EAAA,CAAAE,cAAA,eAAwB;MACtBF,EAAA,CAAAC,SAAA,oBAAyD;MACzDD,EAAA,CAAAE,cAAA,UAAI;MAAAF,EAAA,CAAAM,MAAA,IAAiC;MAAAN,EAAA,CAAAO,YAAA,EAAK;MAC1CP,EAAA,CAAAE,cAAA,SAAG;MAAAF,EAAA,CAAAM,MAAA,wBAAgB;MACrBN,EADqB,CAAAO,YAAA,EAAI,EACnB;MACNP,EAAA,CAAAE,cAAA,eAAwB;MACtBF,EAAA,CAAAC,SAAA,oBAAgE;MAChED,EAAA,CAAAE,cAAA,UAAI;MAAAF,EAAA,CAAAM,MAAA,IAAwB;MAAAN,EAAA,CAAAO,YAAA,EAAK;MACjCP,EAAA,CAAAE,cAAA,SAAG;MAAAF,EAAA,CAAAM,MAAA,wBAAgB;MAI3BN,EAJ2B,CAAAO,YAAA,EAAI,EACnB,EACF,EACW,EACV;MAIPP,EAFJ,CAAAE,cAAA,mBAA6B,uBACV,sBACC;MAAAF,EAAA,CAAAM,MAAA,8BAAsB;MACxCN,EADwC,CAAAO,YAAA,EAAiB,EACvC;MAEhBP,EADF,CAAAE,cAAA,wBAAkB,oBACO;MACrBF,EAAA,CAAAuF,gBAAA,KAAAC,wBAAA,2BAAAC,UAAA,SAAAC,6BAAA,mBAeC;MAIT1F,EAHM,CAAAO,YAAA,EAAW,EACM,EACV,EACP;MAIFP,EAFJ,CAAAE,cAAA,oBAAoC,uBACjB,sBACC;MAAAF,EAAA,CAAAM,MAAA,2BAAc;MAAAN,EAAA,CAAAO,YAAA,EAAiB;MAC/CP,EAAA,CAAAE,cAAA,sBAA6C;MAC3CF,EAAA,CAAAM,MAAA,mBACA;MAAAN,EAAA,CAAAC,SAAA,oBAA6D;MAEjED,EADE,CAAAO,YAAA,EAAa,EACG;MAEhBP,EADF,CAAAE,cAAA,wBAAkB,gBACN;MACRF,EAAA,CAAAuF,gBAAA,KAAAI,wBAAA,0BAAAC,UAAA,SAAAC,6BAAA,mBAoBC;MAKX7F,EAJQ,CAAAO,YAAA,EAAW,EACM,EACV,EACP,EACM;;;MAzHFP,EAAA,CAAAS,UAAA,qBAAoB;MASnBT,EAAA,CAAAQ,SAAA,GAAmB;MAAnBR,EAAA,CAAAS,UAAA,oBAAmB;MAShBT,EAAA,CAAAQ,SAAA,IAAkD;MAAlDR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAiB,WAAA,SAAAqE,GAAA,CAAAlD,cAAA,CAAAlB,YAAA,SAAkD;MAIlDlB,EAAA,CAAAQ,SAAA,GAAoD;MAApDR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAiB,WAAA,SAAAqE,GAAA,CAAAlD,cAAA,CAAAE,cAAA,SAAoD;MAIpDtC,EAAA,CAAAQ,SAAA,GAAyC;MAAzCR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAiB,WAAA,SAAAqE,GAAA,CAAArB,gBAAA,WAAyC;MAgBvCjE,EAAA,CAAAQ,SAAA,IAA+B;MAA/BR,EAAA,CAAAY,iBAAA,CAAA0E,GAAA,CAAAlD,cAAA,CAAAC,UAAA,CAA+B;MAK/BrC,EAAA,CAAAQ,SAAA,GAAiC;MAAjCR,EAAA,CAAAY,iBAAA,CAAA0E,GAAA,CAAAlD,cAAA,CAAAG,YAAA,CAAiC;MAKjCvC,EAAA,CAAAQ,SAAA,GAAwB;MAAxBR,EAAA,CAAAY,iBAAA,CAAA0E,GAAA,CAAA7C,WAAA,CAAAqD,MAAA,CAAwB;MAa9B9F,EAAA,CAAAQ,SAAA,GAeC;MAfDR,EAAA,CAAA+F,UAAA,CAAAT,GAAA,CAAA9C,WAAA,CAeC;MAgBHxC,EAAA,CAAAQ,SAAA,IAoBC;MApBDR,EAAA,CAAA+F,UAAA,CAAAT,GAAA,CAAA5C,WAAA,CAoBC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}