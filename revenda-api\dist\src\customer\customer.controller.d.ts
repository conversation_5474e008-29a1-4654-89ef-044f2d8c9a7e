import { CustomerService } from './customer.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
export declare class CustomerController {
    private readonly customerService;
    constructor(customerService: CustomerService);
    create(createCustomerDto: CreateCustomerDto): Promise<import("./customer.entity").Customer>;
    findAll(): Promise<import("./customer.entity").Customer[]>;
    findOne(id: string): Promise<import("./customer.entity").Customer | null>;
    findByEmail(email: string): Promise<import("./customer.entity").Customer | null>;
    update(id: string, updateCustomerDto: UpdateCustomerDto): Promise<import("typeorm").UpdateResult>;
    remove(id: string): Promise<import("typeorm").DeleteResult>;
}
