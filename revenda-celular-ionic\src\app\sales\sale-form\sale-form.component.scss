.form-container {
  padding: 16px;
  max-width: 800px;
  margin: 0 auto;
}

.form-buttons {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: flex-end;
}

.item-form {
  border: 1px solid var(--ion-color-light);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  background-color: rgba(var(--ion-color-light-rgb), 0.5);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.item-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  gap: 10px;
}

ion-item-divider {
  margin-top: 24px;
  margin-bottom: 16px;
  --background: var(--ion-color-secondary);
  --color: var(--ion-color-secondary-contrast);
  border-radius: 8px;
  padding: 8px 16px;
  
  .add-item-button {
    --background: transparent;
    --color: var(--ion-color-secondary-contrast);
    --border-color: var(--ion-color-secondary-contrast);
    --border-width: 1px;
    --border-style: solid;
    --border-radius: 6px;
    --padding-start: 12px;
    --padding-end: 12px;
  }
}

ion-item {
  --padding-start: 0;
  margin-bottom: 12px;
  --background: transparent;
  
  p {
    font-size: 12px;
    color: var(--ion-color-danger);
    padding-left: 16px;
    margin: 4px 0;
  }
}

ion-input, ion-select {
  --background: var(--ion-background-color);
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  --border-radius: 8px;
  --border-width: 1px;
  --border-color: var(--ion-color-light-shade);
  margin-top: 4px;
}
