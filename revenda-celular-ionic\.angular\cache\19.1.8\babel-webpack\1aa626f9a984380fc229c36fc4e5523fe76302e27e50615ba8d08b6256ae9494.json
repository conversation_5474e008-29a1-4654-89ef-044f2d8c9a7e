{"ast": null, "code": "var _PhoneService;\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PhoneService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.baseUrl}/phones`;\n  }\n  getById(phoneId) {\n    return this.http.get(`${this.apiUrl}/${phoneId}`);\n  }\n  getList() {\n    return this.http.get(this.apiUrl);\n  }\n  getByBrand(brandId) {\n    return this.http.get(`${this.apiUrl}/brand/${brandId}`);\n  }\n  add(phone) {\n    return this.http.post(this.apiUrl, phone);\n  }\n  update(id, phone) {\n    return this.http.patch(`${this.apiUrl}/${id}`, phone);\n  }\n  save(phone) {\n    if (phone.id) {\n      const updateData = {\n        model: phone.model,\n        image: phone.image,\n        releaseDate: phone.releaseDate,\n        price: typeof phone.price === 'string' ? parseFloat(phone.price) : phone.price,\n        category: phone.category,\n        brandId: phone.brandId\n      };\n      return this.update(phone.id, updateData);\n    } else {\n      const createData = {\n        model: phone.model,\n        image: phone.image,\n        releaseDate: phone.releaseDate,\n        price: typeof phone.price === 'string' ? parseFloat(phone.price) : phone.price,\n        category: phone.category,\n        brandId: phone.brandId\n      };\n      return this.add(createData);\n    }\n  }\n  remove(phone) {\n    return this.http.delete(`${this.apiUrl}/${phone.id}`);\n  }\n}\n_PhoneService = PhoneService;\n_PhoneService.ɵfac = function PhoneService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _PhoneService)(i0.ɵɵinject(i1.HttpClient));\n};\n_PhoneService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _PhoneService,\n  factory: _PhoneService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["environment", "PhoneService", "constructor", "http", "apiUrl", "baseUrl", "getById", "phoneId", "get", "getList", "get<PERSON><PERSON><PERSON>rand", "brandId", "add", "phone", "post", "update", "id", "patch", "save", "updateData", "model", "image", "releaseDate", "price", "parseFloat", "category", "createData", "remove", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\phones\\services\\phone.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Phone, CreatePhoneDto, UpdatePhoneDto } from '../models/phone.type';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { environment } from '../../../environments/environment';\r\nimport { Observable } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PhoneService {\r\n\r\n  private readonly apiUrl = `${environment.baseUrl}/phones`;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getById(phoneId: number): Observable<Phone> {\r\n    return this.http.get<Phone>(`${this.apiUrl}/${phoneId}`);\r\n  }\r\n\r\n  getList(): Observable<Phone[]> {\r\n    return this.http.get<Phone[]>(this.apiUrl);\r\n  }\r\n\r\n  getByBrand(brandId: number): Observable<Phone[]> {\r\n    return this.http.get<Phone[]>(`${this.apiUrl}/brand/${brandId}`);\r\n  }\r\n\r\n  private add(phone: CreatePhoneDto): Observable<Phone> {\r\n    return this.http.post<Phone>(this.apiUrl, phone);\r\n  }\r\n\r\n  private update(id: number, phone: UpdatePhoneDto): Observable<any> {\r\n    return this.http.patch(`${this.apiUrl}/${id}`, phone);\r\n  }\r\n\r\n  save(phone: Phone): Observable<any> {\r\n    if (phone.id) {\r\n      const updateData: UpdatePhoneDto = {\r\n        model: phone.model,\r\n        image: phone.image,\r\n        releaseDate: phone.releaseDate,\r\n        price: typeof phone.price === 'string' ? parseFloat(phone.price) : phone.price,\r\n        category: phone.category,\r\n        brandId: phone.brandId\r\n      };\r\n      return this.update(phone.id, updateData);\r\n    } else {\r\n      const createData: CreatePhoneDto = {\r\n        model: phone.model,\r\n        image: phone.image,\r\n        releaseDate: phone.releaseDate,\r\n        price: typeof phone.price === 'string' ? parseFloat(phone.price) : phone.price,\r\n        category: phone.category,\r\n        brandId: phone.brandId\r\n      };\r\n      return this.add(createData);\r\n    }\r\n  }\r\n\r\n  remove(phone: Phone): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/${phone.id}`);\r\n  }\r\n}\r\n"], "mappings": ";AAGA,SAASA,WAAW,QAAQ,mCAAmC;;;AAM/D,OAAM,MAAOC,YAAY;EAIvBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,OAAO,SAAS;EAEjB;EAExCC,OAAOA,CAACC,OAAe;IACrB,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAQ,GAAG,IAAI,CAACJ,MAAM,IAAIG,OAAO,EAAE,CAAC;EAC1D;EAEAE,OAAOA,CAAA;IACL,OAAO,IAAI,CAACN,IAAI,CAACK,GAAG,CAAU,IAAI,CAACJ,MAAM,CAAC;EAC5C;EAEAM,UAAUA,CAACC,OAAe;IACxB,OAAO,IAAI,CAACR,IAAI,CAACK,GAAG,CAAU,GAAG,IAAI,CAACJ,MAAM,UAAUO,OAAO,EAAE,CAAC;EAClE;EAEQC,GAAGA,CAACC,KAAqB;IAC/B,OAAO,IAAI,CAACV,IAAI,CAACW,IAAI,CAAQ,IAAI,CAACV,MAAM,EAAES,KAAK,CAAC;EAClD;EAEQE,MAAMA,CAACC,EAAU,EAAEH,KAAqB;IAC9C,OAAO,IAAI,CAACV,IAAI,CAACc,KAAK,CAAC,GAAG,IAAI,CAACb,MAAM,IAAIY,EAAE,EAAE,EAAEH,KAAK,CAAC;EACvD;EAEAK,IAAIA,CAACL,KAAY;IACf,IAAIA,KAAK,CAACG,EAAE,EAAE;MACZ,MAAMG,UAAU,GAAmB;QACjCC,KAAK,EAAEP,KAAK,CAACO,KAAK;QAClBC,KAAK,EAAER,KAAK,CAACQ,KAAK;QAClBC,WAAW,EAAET,KAAK,CAACS,WAAW;QAC9BC,KAAK,EAAE,OAAOV,KAAK,CAACU,KAAK,KAAK,QAAQ,GAAGC,UAAU,CAACX,KAAK,CAACU,KAAK,CAAC,GAAGV,KAAK,CAACU,KAAK;QAC9EE,QAAQ,EAAEZ,KAAK,CAACY,QAAQ;QACxBd,OAAO,EAAEE,KAAK,CAACF;OAChB;MACD,OAAO,IAAI,CAACI,MAAM,CAACF,KAAK,CAACG,EAAE,EAAEG,UAAU,CAAC;IAC1C,CAAC,MAAM;MACL,MAAMO,UAAU,GAAmB;QACjCN,KAAK,EAAEP,KAAK,CAACO,KAAK;QAClBC,KAAK,EAAER,KAAK,CAACQ,KAAK;QAClBC,WAAW,EAAET,KAAK,CAACS,WAAW;QAC9BC,KAAK,EAAE,OAAOV,KAAK,CAACU,KAAK,KAAK,QAAQ,GAAGC,UAAU,CAACX,KAAK,CAACU,KAAK,CAAC,GAAGV,KAAK,CAACU,KAAK;QAC9EE,QAAQ,EAAEZ,KAAK,CAACY,QAAQ;QACxBd,OAAO,EAAEE,KAAK,CAACF;OAChB;MACD,OAAO,IAAI,CAACC,GAAG,CAACc,UAAU,CAAC;IAC7B;EACF;EAEAC,MAAMA,CAACd,KAAY;IACjB,OAAO,IAAI,CAACV,IAAI,CAACyB,MAAM,CAAC,GAAG,IAAI,CAACxB,MAAM,IAAIS,KAAK,CAACG,EAAE,EAAE,CAAC;EACvD;;gBApDWf,YAAY;;mCAAZA,aAAY,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAZ/B,aAAY;EAAAgC,OAAA,EAAZhC,aAAY,CAAAiC,IAAA;EAAAC,UAAA,EAFX;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}