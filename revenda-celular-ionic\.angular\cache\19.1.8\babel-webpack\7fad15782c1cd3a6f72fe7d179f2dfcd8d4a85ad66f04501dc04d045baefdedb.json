{"ast": null, "code": "var _BrandService;\nimport { environment } from \"../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class BrandService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.baseUrl}/brands`;\n  }\n  getBrands() {\n    return this.http.get(this.apiUrl);\n  }\n  getById(brandId) {\n    return this.http.get(`${this.apiUrl}/${brandId}`);\n  }\n  add(brand) {\n    return this.http.post(this.apiUrl, brand);\n  }\n  update(id, brand) {\n    return this.http.patch(`${this.apiUrl}/${id}`, brand);\n  }\n  save(brand) {\n    if (brand.id) {\n      const updateData = {\n        name: brand.name,\n        country: brand.country\n      };\n      return this.update(brand.id, updateData);\n    } else {\n      const createData = {\n        name: brand.name,\n        country: brand.country\n      };\n      return this.add(createData);\n    }\n  }\n  remove(brand) {\n    return this.http.delete(`${this.apiUrl}/${brand.id}`);\n  }\n}\n_BrandService = BrandService;\n_BrandService.ɵfac = function BrandService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BrandService)(i0.ɵɵinject(i1.HttpClient));\n};\n_BrandService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _BrandService,\n  factory: _BrandService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["environment", "BrandService", "constructor", "http", "apiUrl", "baseUrl", "getBrands", "get", "getById", "brandId", "add", "brand", "post", "update", "id", "patch", "save", "updateData", "name", "country", "createData", "remove", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\brands\\services\\brand.service.ts"], "sourcesContent": ["import { HttpClient } from \"@angular/common/http\";\r\nimport { Injectable } from \"@angular/core\";\r\nimport { Brand, CreateBrandDto, UpdateBrandDto } from \"../models/brand.type\";\r\nimport { Observable } from \"rxjs\";\r\nimport { environment } from \"../../../environments/environment\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class BrandService {\r\n  private readonly apiUrl = `${environment.baseUrl}/brands`;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getBrands(): Observable<Brand[]> {\r\n    return this.http.get<Brand[]>(this.apiUrl);\r\n  }\r\n\r\n  getById(brandId: number): Observable<Brand> {\r\n    return this.http.get<Brand>(`${this.apiUrl}/${brandId}`);\r\n  }\r\n\r\n  private add(brand: CreateBrandDto): Observable<Brand> {\r\n    return this.http.post<Brand>(this.apiUrl, brand);\r\n  }\r\n\r\n  private update(id: number, brand: UpdateBrandDto): Observable<any> {\r\n    return this.http.patch(`${this.apiUrl}/${id}`, brand);\r\n  }\r\n\r\n  save(brand: Brand): Observable<any> {\r\n    if (brand.id) {\r\n      const updateData: UpdateBrandDto = {\r\n        name: brand.name,\r\n        country: brand.country\r\n      };\r\n      return this.update(brand.id, updateData);\r\n    } else {\r\n      const createData: CreateBrandDto = {\r\n        name: brand.name,\r\n        country: brand.country\r\n      };\r\n      return this.add(createData);\r\n    }\r\n  }\r\n\r\n  remove(brand: Brand): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/${brand.id}`);\r\n  }\r\n}\r\n"], "mappings": ";AAIA,SAASA,WAAW,QAAQ,mCAAmC;;;AAK/D,OAAM,MAAOC,YAAY;EAGvBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,OAAO,SAAS;EAElB;EAEvCC,SAASA,CAAA;IACP,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAU,IAAI,CAACH,MAAM,CAAC;EAC5C;EAEAI,OAAOA,CAACC,OAAe;IACrB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAQ,GAAG,IAAI,CAACH,MAAM,IAAIK,OAAO,EAAE,CAAC;EAC1D;EAEQC,GAAGA,CAACC,KAAqB;IAC/B,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAQ,IAAI,CAACR,MAAM,EAAEO,KAAK,CAAC;EAClD;EAEQE,MAAMA,CAACC,EAAU,EAAEH,KAAqB;IAC9C,OAAO,IAAI,CAACR,IAAI,CAACY,KAAK,CAAC,GAAG,IAAI,CAACX,MAAM,IAAIU,EAAE,EAAE,EAAEH,KAAK,CAAC;EACvD;EAEAK,IAAIA,CAACL,KAAY;IACf,IAAIA,KAAK,CAACG,EAAE,EAAE;MACZ,MAAMG,UAAU,GAAmB;QACjCC,IAAI,EAAEP,KAAK,CAACO,IAAI;QAChBC,OAAO,EAAER,KAAK,CAACQ;OAChB;MACD,OAAO,IAAI,CAACN,MAAM,CAACF,KAAK,CAACG,EAAE,EAAEG,UAAU,CAAC;IAC1C,CAAC,MAAM;MACL,MAAMG,UAAU,GAAmB;QACjCF,IAAI,EAAEP,KAAK,CAACO,IAAI;QAChBC,OAAO,EAAER,KAAK,CAACQ;OAChB;MACD,OAAO,IAAI,CAACT,GAAG,CAACU,UAAU,CAAC;IAC7B;EACF;EAEAC,MAAMA,CAACV,KAAY;IACjB,OAAO,IAAI,CAACR,IAAI,CAACmB,MAAM,CAAC,GAAG,IAAI,CAAClB,MAAM,IAAIO,KAAK,CAACG,EAAE,EAAE,CAAC;EACvD;;gBAvCWb,YAAY;;mCAAZA,aAAY,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAZzB,aAAY;EAAA0B,OAAA,EAAZ1B,aAAY,CAAA2B,IAAA;EAAAC,UAAA,EAFX;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}