{"ast": null, "code": "var _CustomersPageRoutingModule;\nimport { RouterModule } from '@angular/router';\nimport { CustomersPage } from './customers.page';\nimport { CustomerFormComponent } from './customer-form/customer-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: CustomersPage\n}, {\n  path: 'new',\n  component: CustomerFormComponent\n}, {\n  path: 'edit/:id',\n  component: CustomerFormComponent\n}];\nexport class CustomersPageRoutingModule {}\n_CustomersPageRoutingModule = CustomersPageRoutingModule;\n_CustomersPageRoutingModule.ɵfac = function CustomersPageRoutingModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CustomersPageRoutingModule)();\n};\n_CustomersPageRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _CustomersPageRoutingModule\n});\n_CustomersPageRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [RouterModule.forChild(routes), RouterModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomersPageRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "CustomersPage", "CustomerFormComponent", "routes", "path", "component", "CustomersPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\customers\\customers-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\nimport { CustomersPage } from './customers.page';\r\nimport { CustomerFormComponent } from './customer-form/customer-form.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: CustomersPage\r\n  },\r\n  {\r\n    path: 'new',\r\n    component: CustomerFormComponent\r\n  },\r\n  {\r\n    path: 'edit/:id',\r\n    component: CustomerFormComponent\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class CustomersPageRoutingModule {}\r\n"], "mappings": ";AACA,SAAiBA,YAAY,QAAQ,iBAAiB;AACtD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,qBAAqB,QAAQ,yCAAyC;;;AAE/E,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,0BAA0B;8BAA1BA,0BAA0B;;mCAA1BA,2BAA0B;AAAA;;QAA1BA;AAA0B;;YAH3BN,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC,EAC7BH,YAAY;AAAA;;2EAEXM,0BAA0B;IAAAE,OAAA,GAAAC,EAAA,CAAAT,YAAA;IAAAU,OAAA,GAF3BV,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}