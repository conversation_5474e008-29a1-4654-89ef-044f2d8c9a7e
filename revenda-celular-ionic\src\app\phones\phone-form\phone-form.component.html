<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Cadastro de Celulares</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="form-container">
    <form [formGroup]="phoneForm">
      <ion-list>
        <ion-item>
          <ion-input formControlName="model" labelPlacement="floating" label="Modelo: " type="text"></ion-input>
          <p>
          @if(hasError('model', 'required')) {
            O campo é obrigatório
          }
          @if(hasError('model', 'minlength')) {
            O campo deve ter no mínimo 3 caracteres
          }
          @if(hasError('model', 'maxlength')) {
            O campo deve ter no máximo 150 caracteres
          }
          @if(hasError('model', 'invalidModel')) {
            Modelo não pode conter palavras como 'teste' ou 'exemplo'
          }
          </p>
        </ion-item>
        <ion-item>
          <ion-input formControlName="image" labelPlacement="floating" label="Imagem (URL)" type="url"></ion-input>
          <p>
          @if(hasError('image', 'required')) {
            O campo é obrigatório
          }
          @if(hasError('image', 'invalidUrl')) {
            O campo de imagem deve ser uma URL válida
          }
          </p>
        </ion-item>
        <ion-item>
          <ion-input formControlName="releaseDate" labelPlacement="floating" label="Lançamento" [maskito]="dateMask"
            [maskitoElement]="maskitoElement" />
          <p>
          @if(hasError('releaseDate', 'futureDate')) {
            Data de lançamento não pode ser no futuro
          }
          @if(hasError('releaseDate', 'tooOld')) {
            Data de lançamento deve ser posterior ao ano 2000
          }
          </p>
        </ion-item>
        <ion-item>
          <ion-input formControlName="price" labelPlacement="floating" label="Preço (R$)" type="number" step="0.01" min="0" />
          <p>
          @if(hasError('price', 'required')) {
            O campo é obrigatório
          }
          @if(hasError('price', 'min')) {
            O preço deve ser maior ou igual a zero
          }
          @if(hasError('price', 'tooExpensive')) {
            Preço não pode ser superior a R$ 50.000
          }
          </p>
        </ion-item>
        <ion-item>
          <ion-select formControlName="category" labelPlacement="floating" label="Categoria">
            @for(category of categories; track category) {
              <ion-select-option [value]="category">{{ category }}</ion-select-option>
            }
          </ion-select>
          <p>
          @if(hasError('category', 'required')) {
            O campo é obrigatório
          }
          </p>
        </ion-item>
        <ion-item>
          <ion-select formControlName="brandId" [compareWith]="compareWith" label="Marca" label-placement="floating">
            @for(brand of brands; track brand) {
              <ion-select-option [value]="brand.id">{{brand.name}}</ion-select-option>
            }
          </ion-select>
          <p>
          @if(hasError('brandId', 'required')) {
            O campo é obrigatório
          }
          </p>
        </ion-item>
      </ion-list>
      <ion-fab vertical="bottom" horizontal="end" slot="fixed">
        <ion-fab-button [disabled]="phoneForm.invalid" (click)="save()">
          <ion-icon name="checkmark"></ion-icon>
        </ion-fab-button>
      </ion-fab>
    </form>
  </div>
</ion-content>
