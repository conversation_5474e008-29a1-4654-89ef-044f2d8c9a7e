// Estilos padronizados para lista
ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 70px;
}

ion-avatar {
  width: 50px;
  height: 50px;
  margin-right: 16px;

  .brand-avatar {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--ion-color-primary-tint);
    border-radius: 8px;

    ion-icon {
      font-size: 24px;
      color: var(--ion-color-primary);
    }
  }
}

ion-label {
  h2 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 4px;
    color: var(--ion-color-primary);
  }

  p {
    font-size: 13px;
    margin: 2px 0;
    color: var(--ion-color-dark);

    strong {
      color: var(--ion-color-primary);
    }
  }
}

ion-button {
  margin-left: 4px;

  &[color="danger"] {
    --color: var(--ion-color-danger);
  }
}

// Manter estilos antigos para compatibilidade
.brand-info {
  width: 100%;

  h2 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 4px;
  }

  h3, h4 {
    font-size: 14px;
    margin: 4px 0;
    font-weight: normal;
  }

  ion-button {
    margin-top: 8px;
    margin-right: 8px;
  }
}
