{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _SaleDetailsComponent;\nimport { SaleStatus, PaymentMethods } from '../models/sale.type';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/sale.service\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = a0 => [\"/sales/edit\", a0];\nfunction SaleDetailsComponent_Conditional_7_p_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.sale.store == null ? null : ctx_r1.sale.store.address);\n  }\n}\nfunction SaleDetailsComponent_Conditional_7_p_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", (ctx_r1.sale.store == null ? null : ctx_r1.sale.store.city) || \"\", \"\", (ctx_r1.sale.store == null ? null : ctx_r1.sale.store.city) && (ctx_r1.sale.store == null ? null : ctx_r1.sale.store.state) ? \"/\" : \"\", \"\", (ctx_r1.sale.store == null ? null : ctx_r1.sale.store.state) || \"\", \" \");\n  }\n}\nfunction SaleDetailsComponent_Conditional_7_p_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Telefone: \", ctx_r1.sale.store == null ? null : ctx_r1.sale.store.phone, \"\");\n  }\n}\nfunction SaleDetailsComponent_Conditional_7_p_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Gerente: \", ctx_r1.sale.store == null ? null : ctx_r1.sale.store.manager, \"\");\n  }\n}\nfunction SaleDetailsComponent_Conditional_7_For_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\")(1, \"ion-label\")(2, \"h2\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"h3\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"currency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", item_r3.productType === \"phone\" ? \"Celular\" : \"Acess\\u00F3rio\", \": \", item_r3.product ? ctx_r1.getProductName(item_r3.product, item_r3.productType) : \"Produto #\" + item_r3.productId, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Quantidade: \", item_r3.quantity, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Pre\\u00E7o Unit\\u00E1rio: \", i0.ɵɵpipeBind2(8, 5, item_r3.unitPrice, \"BRL\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Subtotal: \", i0.ɵɵpipeBind2(11, 8, item_r3.subtotal, \"BRL\"), \"\");\n  }\n}\nfunction SaleDetailsComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-card\")(1, \"ion-card-header\")(2, \"ion-card-title\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-card-subtitle\")(5, \"ion-badge\", 6);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ion-badge\", 7);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"ion-card-content\")(10, \"ion-list\")(11, \"ion-item\")(12, \"ion-label\")(13, \"h2\");\n    i0.ɵɵtext(14, \"Data da Venda\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"ion-item\")(19, \"ion-label\")(20, \"h2\");\n    i0.ɵɵtext(21, \"Cliente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"p\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"p\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"ion-item\")(29, \"ion-label\")(30, \"h2\");\n    i0.ɵɵtext(31, \"Loja\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, SaleDetailsComponent_Conditional_7_p_34_Template, 2, 1, \"p\", 8)(35, SaleDetailsComponent_Conditional_7_p_35_Template, 2, 3, \"p\", 8)(36, SaleDetailsComponent_Conditional_7_p_36_Template, 2, 1, \"p\", 8)(37, SaleDetailsComponent_Conditional_7_p_37_Template, 2, 1, \"p\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"ion-item\")(39, \"ion-label\")(40, \"h2\");\n    i0.ɵɵtext(41, \"Vendedor\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"p\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(44, \"ion-card\")(45, \"ion-card-header\")(46, \"ion-card-title\");\n    i0.ɵɵtext(47, \"Itens da Venda\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"ion-card-content\")(49, \"ion-list\");\n    i0.ɵɵrepeaterCreate(50, SaleDetailsComponent_Conditional_7_For_51_Template, 12, 11, \"ion-item\", null, i0.ɵɵrepeaterTrackByIndex);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 9)(53, \"h2\");\n    i0.ɵɵtext(54);\n    i0.ɵɵpipe(55, \"currency\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(56, \"div\", 10)(57, \"ion-button\", 11);\n    i0.ɵɵelement(58, \"ion-icon\", 12);\n    i0.ɵɵtext(59, \" Editar Venda \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"ion-button\", 13);\n    i0.ɵɵlistener(\"click\", function SaleDetailsComponent_Conditional_7_Template_ion_button_click_60_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelSale());\n    });\n    i0.ɵɵelement(61, \"ion-icon\", 14);\n    i0.ɵɵtext(62, \" Cancelar Venda \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"ion-button\", 15);\n    i0.ɵɵelement(64, \"ion-icon\", 16);\n    i0.ɵɵtext(65, \" Voltar para Lista \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Venda #\", ctx_r1.sale.id, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", ctx_r1.getStatusColor(ctx_r1.sale.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.getStatusLabel(ctx_r1.sale.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getPaymentMethodLabel(ctx_r1.sale.paymentMethod));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 18, ctx_r1.sale.date, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate((ctx_r1.sale.customer == null ? null : ctx_r1.sale.customer.name) || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((ctx_r1.sale.customer == null ? null : ctx_r1.sale.customer.email) || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((ctx_r1.sale.customer == null ? null : ctx_r1.sale.customer.phone) || \"N/A\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate((ctx_r1.sale.store == null ? null : ctx_r1.sale.store.name) || \"N/A\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sale.store == null ? null : ctx_r1.sale.store.address);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.sale.store == null ? null : ctx_r1.sale.store.city) || (ctx_r1.sale.store == null ? null : ctx_r1.sale.store.state));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sale.store == null ? null : ctx_r1.sale.store.phone);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.sale.store == null ? null : ctx_r1.sale.store.manager);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.sale.seller);\n    i0.ɵɵadvance(7);\n    i0.ɵɵrepeater(ctx_r1.sale.items);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Valor Total: \", i0.ɵɵpipeBind2(55, 21, ctx_r1.sale.totalValue, \"BRL\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(24, _c0, ctx_r1.sale.id))(\"disabled\", ctx_r1.sale.status === \"canceled\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.sale.status === \"canceled\");\n  }\n}\nfunction SaleDetailsComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"ion-spinner\");\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Carregando detalhes da venda...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class SaleDetailsComponent {\n  constructor(route, router, saleService, toastController, alertController) {\n    this.route = route;\n    this.router = router;\n    this.saleService = saleService;\n    this.toastController = toastController;\n    this.alertController = alertController;\n  }\n  ngOnInit() {\n    this.route.paramMap.subscribe(params => {\n      const id = params.get('id');\n      if (id) {\n        this.loadSale(id);\n      } else {\n        this.router.navigate(['/sales']);\n      }\n    });\n  }\n  loadSale(id) {\n    this.saleService.getById(+id).subscribe({\n      next: sale => {\n        this.sale = sale;\n      },\n      error: error => {\n        console.error('Erro ao carregar venda', error);\n        this.showError('Erro ao carregar detalhes da venda');\n        this.router.navigate(['/sales']);\n      }\n    });\n  }\n  showError(message) {\n    this.toastController.create({\n      message: message,\n      duration: 3000,\n      color: 'danger'\n    }).then(toast => toast.present());\n  }\n  getStatusLabel(status) {\n    const statusObj = SaleStatus.find(s => s.value === status);\n    return statusObj ? statusObj.label : status;\n  }\n  getPaymentMethodLabel(method) {\n    const methodObj = PaymentMethods.find(m => m.value === method);\n    return methodObj ? methodObj.label : method;\n  }\n  getStatusColor(status) {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'canceled':\n        return 'danger';\n      default:\n        return 'medium';\n    }\n  }\n  getProductName(product, type) {\n    if (!product) return 'Produto não encontrado';\n    if (type === 'phone') {\n      return product.model || 'Celular';\n    } else {\n      return product.name || 'Acessório';\n    }\n  }\n  cancelSale() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.sale) return;\n      const alert = yield _this.alertController.create({\n        header: 'Confirmar Cancelamento',\n        message: `Tem certeza que deseja cancelar a venda #${_this.sale.id}?`,\n        buttons: [{\n          text: 'Cancelar',\n          role: 'cancel'\n        }, {\n          text: 'Confirmar',\n          handler: () => {\n            var _this$sale;\n            if ((_this$sale = _this.sale) !== null && _this$sale !== void 0 && _this$sale.id) {\n              _this.saleService.updateStatus(_this.sale.id, 'canceled').subscribe({\n                next: () => {\n                  _this.toastController.create({\n                    message: 'Venda cancelada com sucesso!',\n                    duration: 3000,\n                    color: 'success'\n                  }).then(toast => toast.present());\n                  if (_this.sale) {\n                    _this.sale.status = 'canceled';\n                  }\n                },\n                error: error => {\n                  console.error('Erro ao cancelar venda', error);\n                  _this.toastController.create({\n                    message: 'Erro ao cancelar venda',\n                    duration: 3000,\n                    color: 'danger'\n                  }).then(toast => toast.present());\n                }\n              });\n            }\n          }\n        }]\n      });\n      yield alert.present();\n    })();\n  }\n}\n_SaleDetailsComponent = SaleDetailsComponent;\n_SaleDetailsComponent.ɵfac = function SaleDetailsComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SaleDetailsComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.SaleService), i0.ɵɵdirectiveInject(i3.ToastController), i0.ɵɵdirectiveInject(i3.AlertController));\n};\n_SaleDetailsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _SaleDetailsComponent,\n  selectors: [[\"app-sale-details\"]],\n  standalone: false,\n  decls: 9,\n  vars: 3,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [\"defaultHref\", \"/sales\"], [3, \"fullscreen\"], [1, \"loading-container\"], [3, \"color\"], [\"color\", \"primary\"], [4, \"ngIf\"], [1, \"total-section\"], [1, \"action-buttons\"], [\"expand\", \"block\", 3, \"routerLink\", \"disabled\"], [\"name\", \"create\", \"slot\", \"start\"], [\"expand\", \"block\", \"color\", \"danger\", 3, \"click\", \"disabled\"], [\"name\", \"close-circle\", \"slot\", \"start\"], [\"expand\", \"block\", \"fill\", \"outline\", \"routerLink\", \"/sales\"], [\"name\", \"arrow-back\", \"slot\", \"start\"]],\n  template: function SaleDetailsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-back-button\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5, \"Detalhes da Venda\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 4);\n      i0.ɵɵtemplate(7, SaleDetailsComponent_Conditional_7_Template, 66, 26)(8, SaleDetailsComponent_Conditional_8_Template, 4, 0, \"div\", 5);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.sale ? 7 : 8);\n    }\n  },\n  dependencies: [i4.NgIf, i3.IonBadge, i3.IonButton, i3.IonButtons, i3.IonCard, i3.IonCardContent, i3.IonCardHeader, i3.IonCardSubtitle, i3.IonCardTitle, i3.IonContent, i3.IonHeader, i3.IonIcon, i3.IonItem, i3.IonLabel, i3.IonList, i3.IonSpinner, i3.IonTitle, i3.IonToolbar, i3.IonBackButton, i3.RouterLinkDelegate, i1.RouterLink, i4.CurrencyPipe, i4.DatePipe],\n  styles: [\"ion-card[_ngcontent-%COMP%] {\\n  margin: 16px;\\n  border-radius: 12px;\\n  overflow: hidden;\\n}\\n\\nion-badge[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  padding: 6px 10px;\\n  font-size: 12px;\\n  font-weight: 500;\\n}\\n\\n.total-section[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  padding-top: 16px;\\n  border-top: 1px solid var(--ion-color-light);\\n  text-align: right;\\n}\\n.total-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  color: var(--ion-color-secondary);\\n  font-size: 20px;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: flex-end;\\n  gap: 10px;\\n}\\n.action-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\\n  --border-radius: 8px;\\n  font-weight: 500;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n}\\n.loading-container[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  --color: var(--ion-color-secondary);\\n}\\n\\nion-card-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\nion-card-content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\\n  --padding-start: 0;\\n  --inner-padding-end: 0;\\n  --background: transparent;\\n  --border-color: transparent;\\n}\\nion-card-content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  margin: 6px 0;\\n}\\nion-card-content[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--ion-color-dark);\\n  margin-right: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["SaleStatus", "PaymentMethods", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "sale", "store", "address", "ɵɵtextInterpolate3", "city", "state", "ɵɵtextInterpolate1", "phone", "manager", "ɵɵtextInterpolate2", "item_r3", "productType", "product", "getProductName", "productId", "quantity", "ɵɵpipeBind2", "unitPrice", "subtotal", "ɵɵtemplate", "SaleDetailsComponent_Conditional_7_p_34_Template", "SaleDetailsComponent_Conditional_7_p_35_Template", "SaleDetailsComponent_Conditional_7_p_36_Template", "SaleDetailsComponent_Conditional_7_p_37_Template", "ɵɵrepeaterCreate", "SaleDetailsComponent_Conditional_7_For_51_Template", "ɵɵrepeaterTrackByIndex", "ɵɵelement", "ɵɵlistener", "SaleDetailsComponent_Conditional_7_Template_ion_button_click_60_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "cancelSale", "id", "ɵɵproperty", "getStatusColor", "status", "getStatusLabel", "getPaymentMethodLabel", "paymentMethod", "date", "customer", "name", "email", "seller", "ɵɵrepeater", "items", "totalValue", "ɵɵpureFunction1", "_c0", "SaleDetailsComponent", "constructor", "route", "router", "saleService", "toastController", "alertController", "ngOnInit", "paramMap", "subscribe", "params", "get", "loadSale", "navigate", "getById", "next", "error", "console", "showError", "message", "create", "duration", "color", "then", "toast", "present", "statusObj", "find", "s", "value", "label", "method", "methodObj", "m", "type", "model", "_this", "_asyncToGenerator", "alert", "header", "buttons", "text", "role", "handler", "_this$sale", "updateStatus", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "SaleService", "i3", "ToastController", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectors", "standalone", "decls", "vars", "consts", "template", "SaleDetailsComponent_Template", "rf", "ctx", "SaleDetailsComponent_Conditional_7_Template", "SaleDetailsComponent_Conditional_8_Template", "ɵɵconditional"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\sales\\sale-details\\sale-details.component.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\sales\\sale-details\\sale-details.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ToastController, AlertController } from '@ionic/angular';\r\nimport { SaleService } from '../services/sale.service';\r\nimport { Sale, SaleStatus, PaymentMethods } from '../models/sale.type';\r\nimport { Phone } from 'src/app/phones/models/phone.type';\r\nimport { Accessory } from 'src/app/accessories/models/accessory.type';\r\n\r\n@Component({\r\n  selector: 'app-sale-details',\r\n  templateUrl: './sale-details.component.html',\r\n  styleUrls: ['./sale-details.component.scss'],\r\n  standalone: false,\r\n})\r\nexport class SaleDetailsComponent implements OnInit {\r\n  sale!: Sale;\r\n  \r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private saleService: SaleService,\r\n    private toastController: ToastController,\r\n    private alertController: AlertController\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.route.paramMap.subscribe(params => {\r\n      const id = params.get('id');\r\n      if (id) {\r\n        this.loadSale(id);\r\n      } else {\r\n        this.router.navigate(['/sales']);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadSale(id: string | number) {\r\n    this.saleService.getById(+id).subscribe({\r\n      next: (sale) => {\r\n        this.sale = sale;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar venda', error);\r\n        this.showError('Erro ao carregar detalhes da venda');\r\n        this.router.navigate(['/sales']);\r\n      }\r\n    });\r\n  }\r\n\r\n  private showError(message: string) {\r\n    this.toastController.create({\r\n      message: message,\r\n      duration: 3000,\r\n      color: 'danger'\r\n    }).then(toast => toast.present());\r\n  }\r\n\r\n  getStatusLabel(status: string): string {\r\n    const statusObj = SaleStatus.find(s => s.value === status);\r\n    return statusObj ? statusObj.label : status;\r\n  }\r\n\r\n  getPaymentMethodLabel(method: string): string {\r\n    const methodObj = PaymentMethods.find(m => m.value === method);\r\n    return methodObj ? methodObj.label : method;\r\n  }\r\n\r\n  getStatusColor(status: string): string {\r\n    switch (status) {\r\n      case 'completed': return 'success';\r\n      case 'pending': return 'warning';\r\n      case 'canceled': return 'danger';\r\n      default: return 'medium';\r\n    }\r\n  }\r\n  \r\n  getProductName(product: Phone | Accessory | undefined, type: 'phone' | 'accessory'): string {\r\n    if (!product) return 'Produto não encontrado';\r\n\r\n    if (type === 'phone') {\r\n      return (product as Phone).model || 'Celular';\r\n    } else {\r\n      return (product as Accessory).name || 'Acessório';\r\n    }\r\n  }\r\n\r\n  async cancelSale() {\r\n    if (!this.sale) return;\r\n    \r\n    const alert = await this.alertController.create({\r\n      header: 'Confirmar Cancelamento',\r\n      message: `Tem certeza que deseja cancelar a venda #${this.sale.id}?`,\r\n      buttons: [\r\n        {\r\n          text: 'Cancelar',\r\n          role: 'cancel'\r\n        },\r\n        {\r\n          text: 'Confirmar',\r\n          handler: () => {\r\n            if (this.sale?.id) {\r\n              this.saleService.updateStatus(this.sale.id, 'canceled').subscribe({\r\n              next: () => {\r\n                this.toastController.create({\r\n                  message: 'Venda cancelada com sucesso!',\r\n                  duration: 3000,\r\n                  color: 'success'\r\n                }).then(toast => toast.present());\r\n                \r\n                if (this.sale) {\r\n                  this.sale.status = 'canceled';\r\n                }\r\n              },\r\n              error: (error) => {\r\n                console.error('Erro ao cancelar venda', error);\r\n                this.toastController.create({\r\n                  message: 'Erro ao cancelar venda',\r\n                  duration: 3000,\r\n                  color: 'danger'\r\n                }).then(toast => toast.present());\r\n              }\r\n              });\r\n            }\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-back-button defaultHref=\"/sales\"></ion-back-button>\r\n    </ion-buttons>\r\n    <ion-title>Detalhes da Venda</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  @if(sale) {\r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Venda #{{ sale.id }}</ion-card-title>\r\n        <ion-card-subtitle>\r\n          <ion-badge [color]=\"getStatusColor(sale.status)\">{{ getStatusLabel(sale.status) }}</ion-badge>\r\n          <ion-badge color=\"primary\">{{ getPaymentMethodLabel(sale.paymentMethod) }}</ion-badge>\r\n        </ion-card-subtitle>\r\n      </ion-card-header>\r\n      \r\n      <ion-card-content>\r\n        <ion-list>\r\n          <ion-item>\r\n            <ion-label>\r\n              <h2>Data da Venda</h2>\r\n              <p>{{ sale.date | date: 'dd/MM/yyyy' }}</p>\r\n            </ion-label>\r\n          </ion-item>\r\n          \r\n          <ion-item>\r\n            <ion-label>\r\n              <h2>Cliente</h2>\r\n              <p>{{ sale.customer?.name || 'N/A' }}</p>\r\n              <p>{{ sale.customer?.email || 'N/A' }}</p>\r\n              <p>{{ sale.customer?.phone || 'N/A' }}</p>\r\n            </ion-label>\r\n          </ion-item>\r\n          \r\n          <ion-item>\r\n            <ion-label>\r\n              <h2>Loja</h2>\r\n              <p>{{ sale.store?.name || 'N/A' }}</p>\r\n              <p *ngIf=\"sale.store?.address\">{{ sale.store?.address }}</p>\r\n              <p *ngIf=\"sale.store?.city || sale.store?.state\">\r\n                {{ sale.store?.city || '' }}{{ sale.store?.city && sale.store?.state ? '/' : '' }}{{ sale.store?.state || '' }}\r\n              </p>\r\n              <p *ngIf=\"sale.store?.phone\">Telefone: {{ sale.store?.phone }}</p>\r\n              <p *ngIf=\"sale.store?.manager\">Gerente: {{ sale.store?.manager }}</p>\r\n            </ion-label>\r\n          </ion-item>\r\n          \r\n          <ion-item>\r\n            <ion-label>\r\n              <h2>Vendedor</h2>\r\n              <p>{{ sale.seller }}</p>\r\n            </ion-label>\r\n          </ion-item>\r\n        </ion-list>\r\n      </ion-card-content>\r\n    </ion-card>\r\n    \r\n    <ion-card>\r\n      <ion-card-header>\r\n        <ion-card-title>Itens da Venda</ion-card-title>\r\n      </ion-card-header>\r\n      \r\n      <ion-card-content>\r\n        <ion-list>\r\n          @for(item of sale.items; track $index) {\r\n            <ion-item>\r\n              <ion-label>\r\n                <h2>{{ item.productType === 'phone' ? 'Celular' : 'Acessório' }}:\r\n                  {{ item.product ? getProductName(item.product, item.productType) : 'Produto #' + item.productId }}\r\n                </h2>\r\n                <p>Quantidade: {{ item.quantity }}</p>\r\n                <p>Preço Unitário: {{ item.unitPrice | currency: 'BRL' }}</p>\r\n                <h3>Subtotal: {{ item.subtotal | currency: 'BRL' }}</h3>\r\n              </ion-label>\r\n            </ion-item>\r\n          }\r\n        </ion-list>\r\n        \r\n        <div class=\"total-section\">\r\n          <h2>Valor Total: {{ sale.totalValue | currency: 'BRL' }}</h2>\r\n        </div>\r\n      </ion-card-content>\r\n    </ion-card>\r\n    \r\n    <div class=\"action-buttons\">\r\n      <ion-button expand=\"block\" [routerLink]=\"['/sales/edit', sale.id]\" [disabled]=\"sale.status === 'canceled'\">\r\n        <ion-icon name=\"create\" slot=\"start\"></ion-icon>\r\n        Editar Venda\r\n      </ion-button>\r\n      \r\n      <ion-button expand=\"block\" color=\"danger\" (click)=\"cancelSale()\" [disabled]=\"sale.status === 'canceled'\">\r\n        <ion-icon name=\"close-circle\" slot=\"start\"></ion-icon>\r\n        Cancelar Venda\r\n      </ion-button>\r\n      \r\n      <ion-button expand=\"block\" fill=\"outline\" routerLink=\"/sales\">\r\n        <ion-icon name=\"arrow-back\" slot=\"start\"></ion-icon>\r\n        Voltar para Lista\r\n      </ion-button>\r\n    </div>\r\n  } @else {\r\n    <div class=\"loading-container\">\r\n      <ion-spinner></ion-spinner>\r\n      <p>Carregando detalhes da venda...</p>\r\n    </div>\r\n  }\r\n</ion-content>\r\n"], "mappings": ";;AAIA,SAAeA,UAAU,EAAEC,cAAc,QAAQ,qBAAqB;;;;;;;;;ICsCxDC,EAAA,CAAAC,cAAA,QAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA7BH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,OAAA,CAAyB;;;;;IACxDT,EAAA,CAAAC,cAAA,QAAiD;IAC/CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAU,kBAAA,OAAAJ,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAG,IAAA,cAAAL,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAG,IAAA,MAAAL,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAI,KAAA,mBAAAN,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAI,KAAA,aACF;;;;;IACAZ,EAAA,CAAAC,cAAA,QAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAArCH,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAa,kBAAA,eAAAP,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAM,KAAA,KAAiC;;;;;IAC9Dd,EAAA,CAAAC,cAAA,QAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAtCH,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAa,kBAAA,cAAAP,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAO,OAAA,KAAkC;;;;;IAwB/Df,EAFJ,CAAAC,cAAA,eAAU,gBACG,SACL;IAAAD,EAAA,CAAAE,MAAA,GAEJ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsD;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7DH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAA+C;;IAEvDF,EAFuD,CAAAG,YAAA,EAAK,EAC9C,EACH;;;;;IAPHH,EAAA,CAAAI,SAAA,GAEJ;IAFIJ,EAAA,CAAAgB,kBAAA,KAAAC,OAAA,CAAAC,WAAA,mDAAAD,OAAA,CAAAE,OAAA,GAAAb,MAAA,CAAAc,cAAA,CAAAH,OAAA,CAAAE,OAAA,EAAAF,OAAA,CAAAC,WAAA,kBAAAD,OAAA,CAAAI,SAAA,MAEJ;IACGrB,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAa,kBAAA,iBAAAI,OAAA,CAAAK,QAAA,KAA+B;IAC/BtB,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAa,kBAAA,+BAAAb,EAAA,CAAAuB,WAAA,OAAAN,OAAA,CAAAO,SAAA,aAAsD;IACrDxB,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAa,kBAAA,eAAAb,EAAA,CAAAuB,WAAA,QAAAN,OAAA,CAAAQ,QAAA,aAA+C;;;;;;IA/D3DzB,EAFJ,CAAAC,cAAA,eAAU,sBACS,qBACC;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAEnDH,EADF,CAAAC,cAAA,wBAAmB,mBACgC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC9FH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAE9EF,EAF8E,CAAAG,YAAA,EAAY,EACpE,EACJ;IAMVH,EAJR,CAAAC,cAAA,uBAAkB,gBACN,gBACE,iBACG,UACL;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAoC;;IAE3CF,EAF2C,CAAAG,YAAA,EAAI,EACjC,EACH;IAIPH,EAFJ,CAAAC,cAAA,gBAAU,iBACG,UACL;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACzCH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC1CH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAE1CF,EAF0C,CAAAG,YAAA,EAAI,EAChC,EACH;IAIPH,EAFJ,CAAAC,cAAA,gBAAU,iBACG,UACL;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACbH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAMtCH,EALA,CAAA0B,UAAA,KAAAC,gDAAA,eAA+B,KAAAC,gDAAA,eACkB,KAAAC,gDAAA,eAGpB,KAAAC,gDAAA,eACE;IAEnC9B,EADE,CAAAG,YAAA,EAAY,EACH;IAIPH,EAFJ,CAAAC,cAAA,gBAAU,iBACG,UACL;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAK9BF,EAL8B,CAAAG,YAAA,EAAI,EACd,EACH,EACF,EACM,EACV;IAIPH,EAFJ,CAAAC,cAAA,gBAAU,uBACS,sBACC;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAChCF,EADgC,CAAAG,YAAA,EAAiB,EAC/B;IAGhBH,EADF,CAAAC,cAAA,wBAAkB,gBACN;IACRD,EAAA,CAAA+B,gBAAA,KAAAC,kDAAA,4BAAAhC,EAAA,CAAAiC,sBAAA,CAWC;IACHjC,EAAA,CAAAG,YAAA,EAAW;IAGTH,EADF,CAAAC,cAAA,cAA2B,UACrB;IAAAD,EAAA,CAAAE,MAAA,IAAoD;;IAG9DF,EAH8D,CAAAG,YAAA,EAAK,EACzD,EACW,EACV;IAGTH,EADF,CAAAC,cAAA,eAA4B,sBACiF;IACzGD,EAAA,CAAAkC,SAAA,oBAAgD;IAChDlC,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAEbH,EAAA,CAAAC,cAAA,sBAAyG;IAA/DD,EAAA,CAAAmC,UAAA,mBAAAC,yEAAA;MAAApC,EAAA,CAAAqC,aAAA,CAAAC,GAAA;MAAA,MAAAhC,MAAA,GAAAN,EAAA,CAAAuC,aAAA;MAAA,OAAAvC,EAAA,CAAAwC,WAAA,CAASlC,MAAA,CAAAmC,UAAA,EAAY;IAAA,EAAC;IAC9DzC,EAAA,CAAAkC,SAAA,oBAAsD;IACtDlC,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAEbH,EAAA,CAAAC,cAAA,sBAA8D;IAC5DD,EAAA,CAAAkC,SAAA,oBAAoD;IACpDlC,EAAA,CAAAE,MAAA,2BACF;IACFF,EADE,CAAAG,YAAA,EAAa,EACT;;;;IA1FcH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAa,kBAAA,YAAAP,MAAA,CAAAC,IAAA,CAAAmC,EAAA,KAAoB;IAEvB1C,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAA2C,UAAA,UAAArC,MAAA,CAAAsC,cAAA,CAAAtC,MAAA,CAAAC,IAAA,CAAAsC,MAAA,EAAqC;IAAC7C,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAwC,cAAA,CAAAxC,MAAA,CAAAC,IAAA,CAAAsC,MAAA,EAAiC;IACvD7C,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAyC,qBAAA,CAAAzC,MAAA,CAAAC,IAAA,CAAAyC,aAAA,EAA+C;IASnEhD,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAuB,WAAA,SAAAjB,MAAA,CAAAC,IAAA,CAAA0C,IAAA,gBAAoC;IAOpCjD,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,IAAA,CAAA2C,QAAA,kBAAA5C,MAAA,CAAAC,IAAA,CAAA2C,QAAA,CAAAC,IAAA,WAAkC;IAClCnD,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,IAAA,CAAA2C,QAAA,kBAAA5C,MAAA,CAAAC,IAAA,CAAA2C,QAAA,CAAAE,KAAA,WAAmC;IACnCpD,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,IAAA,CAAA2C,QAAA,kBAAA5C,MAAA,CAAAC,IAAA,CAAA2C,QAAA,CAAApC,KAAA,WAAmC;IAOnCd,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,EAAAC,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAA2C,IAAA,WAA+B;IAC9BnD,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAA2C,UAAA,SAAArC,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,OAAA,CAAyB;IACzBT,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAA2C,UAAA,UAAArC,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAG,IAAA,MAAAL,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAI,KAAA,EAA2C;IAG3CZ,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAA2C,UAAA,SAAArC,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAM,KAAA,CAAuB;IACvBd,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAA2C,UAAA,SAAArC,MAAA,CAAAC,IAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAO,OAAA,CAAyB;IAO1Bf,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,IAAA,CAAA8C,MAAA,CAAiB;IAcxBrD,EAAA,CAAAI,SAAA,GAWC;IAXDJ,EAAA,CAAAsD,UAAA,CAAAhD,MAAA,CAAAC,IAAA,CAAAgD,KAAA,CAWC;IAIGvD,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAa,kBAAA,kBAAAb,EAAA,CAAAuB,WAAA,SAAAjB,MAAA,CAAAC,IAAA,CAAAiD,UAAA,aAAoD;IAMjCxD,EAAA,CAAAI,SAAA,GAAuC;IAACJ,EAAxC,CAAA2C,UAAA,eAAA3C,EAAA,CAAAyD,eAAA,KAAAC,GAAA,EAAApD,MAAA,CAAAC,IAAA,CAAAmC,EAAA,EAAuC,aAAApC,MAAA,CAAAC,IAAA,CAAAsC,MAAA,gBAAwC;IAKzC7C,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAA2C,UAAA,aAAArC,MAAA,CAAAC,IAAA,CAAAsC,MAAA,gBAAuC;;;;;IAW1G7C,EAAA,CAAAC,cAAA,aAA+B;IAC7BD,EAAA,CAAAkC,SAAA,kBAA2B;IAC3BlC,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IACpCF,EADoC,CAAAG,YAAA,EAAI,EAClC;;;AD9FV,OAAM,MAAOwD,oBAAoB;EAG/BC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,eAAgC,EAChCC,eAAgC;IAJhC,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;EACrB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,MAAM3B,EAAE,GAAG2B,MAAM,CAACC,GAAG,CAAC,IAAI,CAAC;MAC3B,IAAI5B,EAAE,EAAE;QACN,IAAI,CAAC6B,QAAQ,CAAC7B,EAAE,CAAC;MACnB,CAAC,MAAM;QACL,IAAI,CAACoB,MAAM,CAACU,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC;IACF,CAAC,CAAC;EACJ;EAEAD,QAAQA,CAAC7B,EAAmB;IAC1B,IAAI,CAACqB,WAAW,CAACU,OAAO,CAAC,CAAC/B,EAAE,CAAC,CAAC0B,SAAS,CAAC;MACtCM,IAAI,EAAGnE,IAAI,IAAI;QACb,IAAI,CAACA,IAAI,GAAGA,IAAI;MAClB,CAAC;MACDoE,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACE,SAAS,CAAC,oCAAoC,CAAC;QACpD,IAAI,CAACf,MAAM,CAACU,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC;KACD,CAAC;EACJ;EAEQK,SAASA,CAACC,OAAe;IAC/B,IAAI,CAACd,eAAe,CAACe,MAAM,CAAC;MAC1BD,OAAO,EAAEA,OAAO;MAChBE,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE;KACR,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;EACnC;EAEAtC,cAAcA,CAACD,MAAc;IAC3B,MAAMwC,SAAS,GAAGvF,UAAU,CAACwF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAK3C,MAAM,CAAC;IAC1D,OAAOwC,SAAS,GAAGA,SAAS,CAACI,KAAK,GAAG5C,MAAM;EAC7C;EAEAE,qBAAqBA,CAAC2C,MAAc;IAClC,MAAMC,SAAS,GAAG5F,cAAc,CAACuF,IAAI,CAACM,CAAC,IAAIA,CAAC,CAACJ,KAAK,KAAKE,MAAM,CAAC;IAC9D,OAAOC,SAAS,GAAGA,SAAS,CAACF,KAAK,GAAGC,MAAM;EAC7C;EAEA9C,cAAcA,CAACC,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,UAAU;QAAE,OAAO,QAAQ;MAChC;QAAS,OAAO,QAAQ;IAC1B;EACF;EAEAzB,cAAcA,CAACD,OAAsC,EAAE0E,IAA2B;IAChF,IAAI,CAAC1E,OAAO,EAAE,OAAO,wBAAwB;IAE7C,IAAI0E,IAAI,KAAK,OAAO,EAAE;MACpB,OAAQ1E,OAAiB,CAAC2E,KAAK,IAAI,SAAS;IAC9C,CAAC,MAAM;MACL,OAAQ3E,OAAqB,CAACgC,IAAI,IAAI,WAAW;IACnD;EACF;EAEMV,UAAUA,CAAA;IAAA,IAAAsD,KAAA;IAAA,OAAAC,iBAAA;MACd,IAAI,CAACD,KAAI,CAACxF,IAAI,EAAE;MAEhB,MAAM0F,KAAK,SAASF,KAAI,CAAC9B,eAAe,CAACc,MAAM,CAAC;QAC9CmB,MAAM,EAAE,wBAAwB;QAChCpB,OAAO,EAAE,4CAA4CiB,KAAI,CAACxF,IAAI,CAACmC,EAAE,GAAG;QACpEyD,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE;SACP,EACD;UACED,IAAI,EAAE,WAAW;UACjBE,OAAO,EAAEA,CAAA,KAAK;YAAA,IAAAC,UAAA;YACZ,KAAAA,UAAA,GAAIR,KAAI,CAACxF,IAAI,cAAAgG,UAAA,eAATA,UAAA,CAAW7D,EAAE,EAAE;cACjBqD,KAAI,CAAChC,WAAW,CAACyC,YAAY,CAACT,KAAI,CAACxF,IAAI,CAACmC,EAAE,EAAE,UAAU,CAAC,CAAC0B,SAAS,CAAC;gBAClEM,IAAI,EAAEA,CAAA,KAAK;kBACTqB,KAAI,CAAC/B,eAAe,CAACe,MAAM,CAAC;oBAC1BD,OAAO,EAAE,8BAA8B;oBACvCE,QAAQ,EAAE,IAAI;oBACdC,KAAK,EAAE;mBACR,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;kBAEjC,IAAIW,KAAI,CAACxF,IAAI,EAAE;oBACbwF,KAAI,CAACxF,IAAI,CAACsC,MAAM,GAAG,UAAU;kBAC/B;gBACF,CAAC;gBACD8B,KAAK,EAAGA,KAAK,IAAI;kBACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;kBAC9CoB,KAAI,CAAC/B,eAAe,CAACe,MAAM,CAAC;oBAC1BD,OAAO,EAAE,wBAAwB;oBACjCE,QAAQ,EAAE,IAAI;oBACdC,KAAK,EAAE;mBACR,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;gBACnC;eACC,CAAC;YACJ;UACF;SACD;OAEJ,CAAC;MAEF,MAAMa,KAAK,CAACb,OAAO,EAAE;IAAC;EACxB;;wBAnHWzB,oBAAoB;;mCAApBA,qBAAoB,EAAA3D,EAAA,CAAAyG,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA3G,EAAA,CAAAyG,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA5G,EAAA,CAAAyG,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA9G,EAAA,CAAAyG,iBAAA,CAAAM,EAAA,CAAAC,eAAA,GAAAhH,EAAA,CAAAyG,iBAAA,CAAAM,EAAA,CAAAE,eAAA;AAAA;;QAApBtD,qBAAoB;EAAAuD,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCZ7BzH,EAFJ,CAAAC,cAAA,oBAAiC,qBACA,qBACH;MACxBD,EAAA,CAAAkC,SAAA,yBAAwD;MAC1DlC,EAAA,CAAAG,YAAA,EAAc;MACdH,EAAA,CAAAC,cAAA,gBAAW;MAAAD,EAAA,CAAAE,MAAA,wBAAiB;MAEhCF,EAFgC,CAAAG,YAAA,EAAY,EAC5B,EACH;MAEbH,EAAA,CAAAC,cAAA,qBAAiC;MA+F7BD,EA9FF,CAAA0B,UAAA,IAAAiG,2CAAA,SAAW,IAAAC,2CAAA,iBA8FF;MAMX5H,EAAA,CAAAG,YAAA,EAAc;;;MA9GFH,EAAA,CAAA2C,UAAA,qBAAoB;MASnB3C,EAAA,CAAAI,SAAA,GAAmB;MAAnBJ,EAAA,CAAA2C,UAAA,oBAAmB;MAC9B3C,EAAA,CAAAI,SAAA,EAmGC;MAnGDJ,EAAA,CAAA6H,aAAA,CAAAH,GAAA,CAAAnH,IAAA,SAmGC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}