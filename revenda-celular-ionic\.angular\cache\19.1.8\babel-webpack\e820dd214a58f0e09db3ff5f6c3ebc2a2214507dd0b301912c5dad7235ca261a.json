{"ast": null, "code": "export function applyPolyfills() {\n  var promises = [];\n  if (typeof window !== 'undefined') {\n    var win = window;\n    if (!win.customElements || win.Element && (!win.Element.prototype.closest || !win.Element.prototype.matches || !win.Element.prototype.remove || !win.Element.prototype.getRootNode)) {\n      promises.push(import(/* webpackChunkName: \"polyfills-dom\" */'./dom.js'));\n    }\n    var checkIfURLIsSupported = function () {\n      try {\n        var u = new URL('b', 'http://a');\n        u.pathname = 'c%20d';\n        return u.href === 'http://a/c%20d' && u.searchParams;\n      } catch (e) {\n        return false;\n      }\n    };\n    if ('function' !== typeof Object.assign || !Object.entries || !Array.prototype.find || !Array.prototype.includes || !String.prototype.startsWith || !String.prototype.endsWith || win.NodeList && !win.NodeList.prototype.forEach || !win.fetch || !checkIfURLIsSupported() || typeof WeakMap == 'undefined') {\n      promises.push(import(/* webpackChunkName: \"polyfills-core-js\" */'./core-js.js'));\n    }\n  }\n  return Promise.all(promises);\n}", "map": {"version": 3, "names": ["applyPolyfills", "promises", "window", "win", "customElements", "Element", "prototype", "closest", "matches", "remove", "getRootNode", "push", "checkIfURLIsSupported", "u", "URL", "pathname", "href", "searchParams", "e", "Object", "assign", "entries", "Array", "find", "includes", "String", "startsWith", "endsWith", "NodeList", "for<PERSON>ach", "fetch", "WeakMap", "Promise", "all"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/polyfills/index.js"], "sourcesContent": ["export function applyPolyfills() {\n  var promises = [];\n  if (typeof window !== 'undefined') {\n    var win = window;\n\n    if (!win.customElements ||\n      (win.Element && (!win.Element.prototype.closest || !win.Element.prototype.matches || !win.Element.prototype.remove || !win.Element.prototype.getRootNode))) {\n      promises.push(import(/* webpackChunkName: \"polyfills-dom\" */ './dom.js'));\n    }\n\n    var checkIfURLIsSupported = function() {\n      try {\n        var u = new URL('b', 'http://a');\n        u.pathname = 'c%20d';\n        return (u.href === 'http://a/c%20d') && u.searchParams;\n      } catch (e) {\n        return false;\n      }\n    };\n\n    if (\n      'function' !== typeof Object.assign || !Object.entries ||\n      !Array.prototype.find || !Array.prototype.includes ||\n      !String.prototype.startsWith || !String.prototype.endsWith ||\n      (win.NodeList && !win.NodeList.prototype.forEach) ||\n      !win.fetch ||\n      !checkIfURLIsSupported() ||\n      typeof WeakMap == 'undefined'\n    ) {\n      promises.push(import(/* webpackChunkName: \"polyfills-core-js\" */ './core-js.js'));\n    }\n  }\n  return Promise.all(promises);\n}\n"], "mappings": "AAAA,OAAO,SAASA,cAAcA,CAAA,EAAG;EAC/B,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,IAAIC,GAAG,GAAGD,MAAM;IAEhB,IAAI,CAACC,GAAG,CAACC,cAAc,IACpBD,GAAG,CAACE,OAAO,KAAK,CAACF,GAAG,CAACE,OAAO,CAACC,SAAS,CAACC,OAAO,IAAI,CAACJ,GAAG,CAACE,OAAO,CAACC,SAAS,CAACE,OAAO,IAAI,CAACL,GAAG,CAACE,OAAO,CAACC,SAAS,CAACG,MAAM,IAAI,CAACN,GAAG,CAACE,OAAO,CAACC,SAAS,CAACI,WAAW,CAAE,EAAE;MAC5JT,QAAQ,CAACU,IAAI,CAAC,MAAM,CAAC,uCAAwC,UAAU,CAAC,CAAC;IAC3E;IAEA,IAAIC,qBAAqB,GAAG,SAAAA,CAAA,EAAW;MACrC,IAAI;QACF,IAAIC,CAAC,GAAG,IAAIC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC;QAChCD,CAAC,CAACE,QAAQ,GAAG,OAAO;QACpB,OAAQF,CAAC,CAACG,IAAI,KAAK,gBAAgB,IAAKH,CAAC,CAACI,YAAY;MACxD,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV,OAAO,KAAK;MACd;IACF,CAAC;IAED,IACE,UAAU,KAAK,OAAOC,MAAM,CAACC,MAAM,IAAI,CAACD,MAAM,CAACE,OAAO,IACtD,CAACC,KAAK,CAAChB,SAAS,CAACiB,IAAI,IAAI,CAACD,KAAK,CAAChB,SAAS,CAACkB,QAAQ,IAClD,CAACC,MAAM,CAACnB,SAAS,CAACoB,UAAU,IAAI,CAACD,MAAM,CAACnB,SAAS,CAACqB,QAAQ,IACzDxB,GAAG,CAACyB,QAAQ,IAAI,CAACzB,GAAG,CAACyB,QAAQ,CAACtB,SAAS,CAACuB,OAAQ,IACjD,CAAC1B,GAAG,CAAC2B,KAAK,IACV,CAAClB,qBAAqB,CAAC,CAAC,IACxB,OAAOmB,OAAO,IAAI,WAAW,EAC7B;MACA9B,QAAQ,CAACU,IAAI,CAAC,MAAM,CAAC,2CAA4C,cAAc,CAAC,CAAC;IACnF;EACF;EACA,OAAOqB,OAAO,CAACC,GAAG,CAAChC,QAAQ,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}