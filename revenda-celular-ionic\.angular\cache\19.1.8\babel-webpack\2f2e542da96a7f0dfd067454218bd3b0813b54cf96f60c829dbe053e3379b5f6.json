{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Does a simple sanitization of all elements\n * in an untrusted string\n */\nconst sanitizeDOMString = untrustedString => {\n  try {\n    if (untrustedString instanceof IonicSafeString) {\n      return untrustedString.value;\n    }\n    if (!isSanitizerEnabled() || typeof untrustedString !== 'string' || untrustedString === '') {\n      return untrustedString;\n    }\n    /**\n     * onload is fired when appending to a document\n     * fragment in Chrome. If a string\n     * contains onload then we should not\n     * attempt to add this to the fragment.\n     */\n    if (untrustedString.includes('onload=')) {\n      return '';\n    }\n    /**\n     * Create a document fragment\n     * separate from the main DOM,\n     * create a div to do our work in\n     */\n    const documentFragment = document.createDocumentFragment();\n    const workingDiv = document.createElement('div');\n    documentFragment.appendChild(workingDiv);\n    workingDiv.innerHTML = untrustedString;\n    /**\n     * Remove any elements\n     * that are blocked\n     */\n    blockedTags.forEach(blockedTag => {\n      const getElementsToRemove = documentFragment.querySelectorAll(blockedTag);\n      for (let elementIndex = getElementsToRemove.length - 1; elementIndex >= 0; elementIndex--) {\n        const element = getElementsToRemove[elementIndex];\n        if (element.parentNode) {\n          element.parentNode.removeChild(element);\n        } else {\n          documentFragment.removeChild(element);\n        }\n        /**\n         * We still need to sanitize\n         * the children of this element\n         * as they are left behind\n         */\n        const childElements = getElementChildren(element);\n        /* eslint-disable-next-line */\n        for (let childIndex = 0; childIndex < childElements.length; childIndex++) {\n          sanitizeElement(childElements[childIndex]);\n        }\n      }\n    });\n    /**\n     * Go through remaining elements and remove\n     * non-allowed attribs\n     */\n    // IE does not support .children on document fragments, only .childNodes\n    const dfChildren = getElementChildren(documentFragment);\n    /* eslint-disable-next-line */\n    for (let childIndex = 0; childIndex < dfChildren.length; childIndex++) {\n      sanitizeElement(dfChildren[childIndex]);\n    }\n    // Append document fragment to div\n    const fragmentDiv = document.createElement('div');\n    fragmentDiv.appendChild(documentFragment);\n    // First child is always the div we did our work in\n    const getInnerDiv = fragmentDiv.querySelector('div');\n    return getInnerDiv !== null ? getInnerDiv.innerHTML : fragmentDiv.innerHTML;\n  } catch (err) {\n    console.error(err);\n    return '';\n  }\n};\n/**\n * Clean up current element based on allowed attributes\n * and then recursively dig down into any child elements to\n * clean those up as well\n */\n// TODO(FW-2832): type (using Element triggers other type errors as well)\nconst sanitizeElement = element => {\n  // IE uses childNodes, so ignore nodes that are not elements\n  if (element.nodeType && element.nodeType !== 1) {\n    return;\n  }\n  /**\n   * If attributes is not a NamedNodeMap\n   * then we should remove the element entirely.\n   * This helps avoid DOM Clobbering attacks where\n   * attributes is overridden.\n   */\n  if (typeof NamedNodeMap !== 'undefined' && !(element.attributes instanceof NamedNodeMap)) {\n    element.remove();\n    return;\n  }\n  for (let i = element.attributes.length - 1; i >= 0; i--) {\n    const attribute = element.attributes.item(i);\n    const attributeName = attribute.name;\n    // remove non-allowed attribs\n    if (!allowedAttributes.includes(attributeName.toLowerCase())) {\n      element.removeAttribute(attributeName);\n      continue;\n    }\n    // clean up any allowed attribs\n    // that attempt to do any JS funny-business\n    const attributeValue = attribute.value;\n    /**\n     * We also need to check the property value\n     * as javascript: can allow special characters\n     * such as &Tab; and still be valid (i.e. java&Tab;script)\n     */\n    const propertyValue = element[attributeName];\n    /* eslint-disable */\n    if (attributeValue != null && attributeValue.toLowerCase().includes('javascript:') || propertyValue != null && propertyValue.toLowerCase().includes('javascript:')) {\n      element.removeAttribute(attributeName);\n    }\n    /* eslint-enable */\n  }\n  /**\n   * Sanitize any nested children\n   */\n  const childElements = getElementChildren(element);\n  /* eslint-disable-next-line */\n  for (let i = 0; i < childElements.length; i++) {\n    sanitizeElement(childElements[i]);\n  }\n};\n/**\n * IE doesn't always support .children\n * so we revert to .childNodes instead\n */\n// TODO(FW-2832): type\nconst getElementChildren = el => {\n  return el.children != null ? el.children : el.childNodes;\n};\nconst isSanitizerEnabled = () => {\n  var _a;\n  const win = window;\n  const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n  if (config) {\n    if (config.get) {\n      return config.get('sanitizerEnabled', true);\n    } else {\n      return config.sanitizerEnabled === true || config.sanitizerEnabled === undefined;\n    }\n  }\n  return true;\n};\nconst allowedAttributes = ['class', 'id', 'href', 'src', 'name', 'slot'];\nconst blockedTags = ['script', 'style', 'iframe', 'meta', 'link', 'object', 'embed'];\nclass IonicSafeString {\n  constructor(value) {\n    this.value = value;\n  }\n}\nconst setupConfig = config => {\n  const win = window;\n  const Ionic = win.Ionic;\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n  if (Ionic && Ionic.config && Ionic.config.constructor.name !== 'Object') {\n    return;\n  }\n  win.Ionic = win.Ionic || {};\n  win.Ionic.config = Object.assign(Object.assign({}, win.Ionic.config), config);\n  return win.Ionic.config;\n};\nconst getMode = () => {\n  var _a;\n  const win = window;\n  const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n  if (config) {\n    if (config.mode) {\n      return config.mode;\n    } else {\n      return config.get('mode');\n    }\n  }\n  return 'md';\n};\nconst ENABLE_HTML_CONTENT_DEFAULT = false;\nexport { ENABLE_HTML_CONTENT_DEFAULT as E, IonicSafeString as I, sanitizeDOMString as a, getMode as g, setupConfig as s };", "map": {"version": 3, "names": ["sanitizeDOMString", "untrustedString", "IonicSafeString", "value", "isSanitizerEnabled", "includes", "documentFragment", "document", "createDocumentFragment", "workingDiv", "createElement", "append<PERSON><PERSON><PERSON>", "innerHTML", "blockedTags", "for<PERSON>ach", "blockedTag", "getElementsToRemove", "querySelectorAll", "elementIndex", "length", "element", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "childElements", "get<PERSON>lement<PERSON><PERSON><PERSON><PERSON>", "childIndex", "sanitizeElement", "df<PERSON><PERSON><PERSON><PERSON>", "fragmentDiv", "getInnerDiv", "querySelector", "err", "console", "error", "nodeType", "NamedNodeMap", "attributes", "remove", "i", "attribute", "item", "attributeName", "name", "allowedAttributes", "toLowerCase", "removeAttribute", "attributeValue", "propertyValue", "el", "children", "childNodes", "_a", "win", "window", "config", "<PERSON><PERSON>", "get", "sanitizerEnabled", "undefined", "constructor", "setupConfig", "Object", "assign", "getMode", "mode", "ENABLE_HTML_CONTENT_DEFAULT", "E", "I", "a", "g", "s"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/components/config.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Does a simple sanitization of all elements\n * in an untrusted string\n */\nconst sanitizeDOMString = (untrustedString) => {\n    try {\n        if (untrustedString instanceof IonicSafeString) {\n            return untrustedString.value;\n        }\n        if (!isSanitizerEnabled() || typeof untrustedString !== 'string' || untrustedString === '') {\n            return untrustedString;\n        }\n        /**\n         * onload is fired when appending to a document\n         * fragment in Chrome. If a string\n         * contains onload then we should not\n         * attempt to add this to the fragment.\n         */\n        if (untrustedString.includes('onload=')) {\n            return '';\n        }\n        /**\n         * Create a document fragment\n         * separate from the main DOM,\n         * create a div to do our work in\n         */\n        const documentFragment = document.createDocumentFragment();\n        const workingDiv = document.createElement('div');\n        documentFragment.appendChild(workingDiv);\n        workingDiv.innerHTML = untrustedString;\n        /**\n         * Remove any elements\n         * that are blocked\n         */\n        blockedTags.forEach((blockedTag) => {\n            const getElementsToRemove = documentFragment.querySelectorAll(blockedTag);\n            for (let elementIndex = getElementsToRemove.length - 1; elementIndex >= 0; elementIndex--) {\n                const element = getElementsToRemove[elementIndex];\n                if (element.parentNode) {\n                    element.parentNode.removeChild(element);\n                }\n                else {\n                    documentFragment.removeChild(element);\n                }\n                /**\n                 * We still need to sanitize\n                 * the children of this element\n                 * as they are left behind\n                 */\n                const childElements = getElementChildren(element);\n                /* eslint-disable-next-line */\n                for (let childIndex = 0; childIndex < childElements.length; childIndex++) {\n                    sanitizeElement(childElements[childIndex]);\n                }\n            }\n        });\n        /**\n         * Go through remaining elements and remove\n         * non-allowed attribs\n         */\n        // IE does not support .children on document fragments, only .childNodes\n        const dfChildren = getElementChildren(documentFragment);\n        /* eslint-disable-next-line */\n        for (let childIndex = 0; childIndex < dfChildren.length; childIndex++) {\n            sanitizeElement(dfChildren[childIndex]);\n        }\n        // Append document fragment to div\n        const fragmentDiv = document.createElement('div');\n        fragmentDiv.appendChild(documentFragment);\n        // First child is always the div we did our work in\n        const getInnerDiv = fragmentDiv.querySelector('div');\n        return getInnerDiv !== null ? getInnerDiv.innerHTML : fragmentDiv.innerHTML;\n    }\n    catch (err) {\n        console.error(err);\n        return '';\n    }\n};\n/**\n * Clean up current element based on allowed attributes\n * and then recursively dig down into any child elements to\n * clean those up as well\n */\n// TODO(FW-2832): type (using Element triggers other type errors as well)\nconst sanitizeElement = (element) => {\n    // IE uses childNodes, so ignore nodes that are not elements\n    if (element.nodeType && element.nodeType !== 1) {\n        return;\n    }\n    /**\n     * If attributes is not a NamedNodeMap\n     * then we should remove the element entirely.\n     * This helps avoid DOM Clobbering attacks where\n     * attributes is overridden.\n     */\n    if (typeof NamedNodeMap !== 'undefined' && !(element.attributes instanceof NamedNodeMap)) {\n        element.remove();\n        return;\n    }\n    for (let i = element.attributes.length - 1; i >= 0; i--) {\n        const attribute = element.attributes.item(i);\n        const attributeName = attribute.name;\n        // remove non-allowed attribs\n        if (!allowedAttributes.includes(attributeName.toLowerCase())) {\n            element.removeAttribute(attributeName);\n            continue;\n        }\n        // clean up any allowed attribs\n        // that attempt to do any JS funny-business\n        const attributeValue = attribute.value;\n        /**\n         * We also need to check the property value\n         * as javascript: can allow special characters\n         * such as &Tab; and still be valid (i.e. java&Tab;script)\n         */\n        const propertyValue = element[attributeName];\n        /* eslint-disable */\n        if ((attributeValue != null && attributeValue.toLowerCase().includes('javascript:')) ||\n            (propertyValue != null && propertyValue.toLowerCase().includes('javascript:'))) {\n            element.removeAttribute(attributeName);\n        }\n        /* eslint-enable */\n    }\n    /**\n     * Sanitize any nested children\n     */\n    const childElements = getElementChildren(element);\n    /* eslint-disable-next-line */\n    for (let i = 0; i < childElements.length; i++) {\n        sanitizeElement(childElements[i]);\n    }\n};\n/**\n * IE doesn't always support .children\n * so we revert to .childNodes instead\n */\n// TODO(FW-2832): type\nconst getElementChildren = (el) => {\n    return el.children != null ? el.children : el.childNodes;\n};\nconst isSanitizerEnabled = () => {\n    var _a;\n    const win = window;\n    const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n    if (config) {\n        if (config.get) {\n            return config.get('sanitizerEnabled', true);\n        }\n        else {\n            return config.sanitizerEnabled === true || config.sanitizerEnabled === undefined;\n        }\n    }\n    return true;\n};\nconst allowedAttributes = ['class', 'id', 'href', 'src', 'name', 'slot'];\nconst blockedTags = ['script', 'style', 'iframe', 'meta', 'link', 'object', 'embed'];\nclass IonicSafeString {\n    constructor(value) {\n        this.value = value;\n    }\n}\n\nconst setupConfig = (config) => {\n    const win = window;\n    const Ionic = win.Ionic;\n    // eslint-disable-next-line @typescript-eslint/prefer-optional-chain\n    if (Ionic && Ionic.config && Ionic.config.constructor.name !== 'Object') {\n        return;\n    }\n    win.Ionic = win.Ionic || {};\n    win.Ionic.config = Object.assign(Object.assign({}, win.Ionic.config), config);\n    return win.Ionic.config;\n};\nconst getMode = () => {\n    var _a;\n    const win = window;\n    const config = (_a = win === null || win === void 0 ? void 0 : win.Ionic) === null || _a === void 0 ? void 0 : _a.config;\n    if (config) {\n        if (config.mode) {\n            return config.mode;\n        }\n        else {\n            return config.get('mode');\n        }\n    }\n    return 'md';\n};\nconst ENABLE_HTML_CONTENT_DEFAULT = false;\n\nexport { ENABLE_HTML_CONTENT_DEFAULT as E, IonicSafeString as I, sanitizeDOMString as a, getMode as g, setupConfig as s };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,iBAAiB,GAAIC,eAAe,IAAK;EAC3C,IAAI;IACA,IAAIA,eAAe,YAAYC,eAAe,EAAE;MAC5C,OAAOD,eAAe,CAACE,KAAK;IAChC;IACA,IAAI,CAACC,kBAAkB,CAAC,CAAC,IAAI,OAAOH,eAAe,KAAK,QAAQ,IAAIA,eAAe,KAAK,EAAE,EAAE;MACxF,OAAOA,eAAe;IAC1B;IACA;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIA,eAAe,CAACI,QAAQ,CAAC,SAAS,CAAC,EAAE;MACrC,OAAO,EAAE;IACb;IACA;AACR;AACA;AACA;AACA;IACQ,MAAMC,gBAAgB,GAAGC,QAAQ,CAACC,sBAAsB,CAAC,CAAC;IAC1D,MAAMC,UAAU,GAAGF,QAAQ,CAACG,aAAa,CAAC,KAAK,CAAC;IAChDJ,gBAAgB,CAACK,WAAW,CAACF,UAAU,CAAC;IACxCA,UAAU,CAACG,SAAS,GAAGX,eAAe;IACtC;AACR;AACA;AACA;IACQY,WAAW,CAACC,OAAO,CAAEC,UAAU,IAAK;MAChC,MAAMC,mBAAmB,GAAGV,gBAAgB,CAACW,gBAAgB,CAACF,UAAU,CAAC;MACzE,KAAK,IAAIG,YAAY,GAAGF,mBAAmB,CAACG,MAAM,GAAG,CAAC,EAAED,YAAY,IAAI,CAAC,EAAEA,YAAY,EAAE,EAAE;QACvF,MAAME,OAAO,GAAGJ,mBAAmB,CAACE,YAAY,CAAC;QACjD,IAAIE,OAAO,CAACC,UAAU,EAAE;UACpBD,OAAO,CAACC,UAAU,CAACC,WAAW,CAACF,OAAO,CAAC;QAC3C,CAAC,MACI;UACDd,gBAAgB,CAACgB,WAAW,CAACF,OAAO,CAAC;QACzC;QACA;AAChB;AACA;AACA;AACA;QACgB,MAAMG,aAAa,GAAGC,kBAAkB,CAACJ,OAAO,CAAC;QACjD;QACA,KAAK,IAAIK,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGF,aAAa,CAACJ,MAAM,EAAEM,UAAU,EAAE,EAAE;UACtEC,eAAe,CAACH,aAAa,CAACE,UAAU,CAAC,CAAC;QAC9C;MACJ;IACJ,CAAC,CAAC;IACF;AACR;AACA;AACA;IACQ;IACA,MAAME,UAAU,GAAGH,kBAAkB,CAAClB,gBAAgB,CAAC;IACvD;IACA,KAAK,IAAImB,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGE,UAAU,CAACR,MAAM,EAAEM,UAAU,EAAE,EAAE;MACnEC,eAAe,CAACC,UAAU,CAACF,UAAU,CAAC,CAAC;IAC3C;IACA;IACA,MAAMG,WAAW,GAAGrB,QAAQ,CAACG,aAAa,CAAC,KAAK,CAAC;IACjDkB,WAAW,CAACjB,WAAW,CAACL,gBAAgB,CAAC;IACzC;IACA,MAAMuB,WAAW,GAAGD,WAAW,CAACE,aAAa,CAAC,KAAK,CAAC;IACpD,OAAOD,WAAW,KAAK,IAAI,GAAGA,WAAW,CAACjB,SAAS,GAAGgB,WAAW,CAAChB,SAAS;EAC/E,CAAC,CACD,OAAOmB,GAAG,EAAE;IACRC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;IAClB,OAAO,EAAE;EACb;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAML,eAAe,GAAIN,OAAO,IAAK;EACjC;EACA,IAAIA,OAAO,CAACc,QAAQ,IAAId,OAAO,CAACc,QAAQ,KAAK,CAAC,EAAE;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI,OAAOC,YAAY,KAAK,WAAW,IAAI,EAAEf,OAAO,CAACgB,UAAU,YAAYD,YAAY,CAAC,EAAE;IACtFf,OAAO,CAACiB,MAAM,CAAC,CAAC;IAChB;EACJ;EACA,KAAK,IAAIC,CAAC,GAAGlB,OAAO,CAACgB,UAAU,CAACjB,MAAM,GAAG,CAAC,EAAEmB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACrD,MAAMC,SAAS,GAAGnB,OAAO,CAACgB,UAAU,CAACI,IAAI,CAACF,CAAC,CAAC;IAC5C,MAAMG,aAAa,GAAGF,SAAS,CAACG,IAAI;IACpC;IACA,IAAI,CAACC,iBAAiB,CAACtC,QAAQ,CAACoC,aAAa,CAACG,WAAW,CAAC,CAAC,CAAC,EAAE;MAC1DxB,OAAO,CAACyB,eAAe,CAACJ,aAAa,CAAC;MACtC;IACJ;IACA;IACA;IACA,MAAMK,cAAc,GAAGP,SAAS,CAACpC,KAAK;IACtC;AACR;AACA;AACA;AACA;IACQ,MAAM4C,aAAa,GAAG3B,OAAO,CAACqB,aAAa,CAAC;IAC5C;IACA,IAAKK,cAAc,IAAI,IAAI,IAAIA,cAAc,CAACF,WAAW,CAAC,CAAC,CAACvC,QAAQ,CAAC,aAAa,CAAC,IAC9E0C,aAAa,IAAI,IAAI,IAAIA,aAAa,CAACH,WAAW,CAAC,CAAC,CAACvC,QAAQ,CAAC,aAAa,CAAE,EAAE;MAChFe,OAAO,CAACyB,eAAe,CAACJ,aAAa,CAAC;IAC1C;IACA;EACJ;EACA;AACJ;AACA;EACI,MAAMlB,aAAa,GAAGC,kBAAkB,CAACJ,OAAO,CAAC;EACjD;EACA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,aAAa,CAACJ,MAAM,EAAEmB,CAAC,EAAE,EAAE;IAC3CZ,eAAe,CAACH,aAAa,CAACe,CAAC,CAAC,CAAC;EACrC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMd,kBAAkB,GAAIwB,EAAE,IAAK;EAC/B,OAAOA,EAAE,CAACC,QAAQ,IAAI,IAAI,GAAGD,EAAE,CAACC,QAAQ,GAAGD,EAAE,CAACE,UAAU;AAC5D,CAAC;AACD,MAAM9C,kBAAkB,GAAGA,CAAA,KAAM;EAC7B,IAAI+C,EAAE;EACN,MAAMC,GAAG,GAAGC,MAAM;EAClB,MAAMC,MAAM,GAAG,CAACH,EAAE,GAAGC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACG,KAAK,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,MAAM;EACxH,IAAIA,MAAM,EAAE;IACR,IAAIA,MAAM,CAACE,GAAG,EAAE;MACZ,OAAOF,MAAM,CAACE,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC;IAC/C,CAAC,MACI;MACD,OAAOF,MAAM,CAACG,gBAAgB,KAAK,IAAI,IAAIH,MAAM,CAACG,gBAAgB,KAAKC,SAAS;IACpF;EACJ;EACA,OAAO,IAAI;AACf,CAAC;AACD,MAAMf,iBAAiB,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;AACxE,MAAM9B,WAAW,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;AACpF,MAAMX,eAAe,CAAC;EAClByD,WAAWA,CAACxD,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;AACJ;AAEA,MAAMyD,WAAW,GAAIN,MAAM,IAAK;EAC5B,MAAMF,GAAG,GAAGC,MAAM;EAClB,MAAME,KAAK,GAAGH,GAAG,CAACG,KAAK;EACvB;EACA,IAAIA,KAAK,IAAIA,KAAK,CAACD,MAAM,IAAIC,KAAK,CAACD,MAAM,CAACK,WAAW,CAACjB,IAAI,KAAK,QAAQ,EAAE;IACrE;EACJ;EACAU,GAAG,CAACG,KAAK,GAAGH,GAAG,CAACG,KAAK,IAAI,CAAC,CAAC;EAC3BH,GAAG,CAACG,KAAK,CAACD,MAAM,GAAGO,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,GAAG,CAACG,KAAK,CAACD,MAAM,CAAC,EAAEA,MAAM,CAAC;EAC7E,OAAOF,GAAG,CAACG,KAAK,CAACD,MAAM;AAC3B,CAAC;AACD,MAAMS,OAAO,GAAGA,CAAA,KAAM;EAClB,IAAIZ,EAAE;EACN,MAAMC,GAAG,GAAGC,MAAM;EAClB,MAAMC,MAAM,GAAG,CAACH,EAAE,GAAGC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACG,KAAK,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,MAAM;EACxH,IAAIA,MAAM,EAAE;IACR,IAAIA,MAAM,CAACU,IAAI,EAAE;MACb,OAAOV,MAAM,CAACU,IAAI;IACtB,CAAC,MACI;MACD,OAAOV,MAAM,CAACE,GAAG,CAAC,MAAM,CAAC;IAC7B;EACJ;EACA,OAAO,IAAI;AACf,CAAC;AACD,MAAMS,2BAA2B,GAAG,KAAK;AAEzC,SAASA,2BAA2B,IAAIC,CAAC,EAAEhE,eAAe,IAAIiE,CAAC,EAAEnE,iBAAiB,IAAIoE,CAAC,EAAEL,OAAO,IAAIM,CAAC,EAAET,WAAW,IAAIU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}