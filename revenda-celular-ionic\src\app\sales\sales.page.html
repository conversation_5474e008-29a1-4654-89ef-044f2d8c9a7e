<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Vendas</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Vendas</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-card>
    <ion-card-header>
      <ion-card-title>Filtros</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-grid>
        <ion-row>
          <ion-col size="12" size-md="6">
            <ion-item>
              <ion-select label="Loja" labelPlacement="floating" [(ngModel)]="filters.storeId">
                <ion-select-option [value]="null">Todas</ion-select-option>
                @for(store of storesList; track store.id) {
                  <ion-select-option [value]="store.id">{{ store.name }}</ion-select-option>
                }
              </ion-select>
            </ion-item>
          </ion-col>
          <ion-col size="12" size-md="6">
            <ion-item>
              <ion-select label="Status" labelPlacement="floating" [(ngModel)]="filters.status">
                <ion-select-option [value]="null">Todos</ion-select-option>
                @for(status of saleStatusOptions; track status.value) {
                  <ion-select-option [value]="status.value">{{ status.label }}</ion-select-option>
                }
              </ion-select>
            </ion-item>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <ion-button expand="block" (click)="applyFilters()">
              <ion-icon name="search" slot="start"></ion-icon>
              Filtrar
            </ion-button>
          </ion-col>
          <ion-col>
            <ion-button expand="block" fill="outline" (click)="clearFilters()">
              <ion-icon name="refresh" slot="start"></ion-icon>
              Limpar
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card-content>
  </ion-card>

  <ion-list>
    @for(sale of salesList; track sale.id) {
    <ion-item>
      <ion-text class="sale-info">
        <h2>Venda #{{ sale.id }}</h2>
        <h3><strong>Data:</strong> {{ sale.date | date: 'dd/MM/yyyy' }}</h3>
        <h4><strong>Cliente:</strong> {{ sale.customer?.name || 'N/A' }}</h4>
        <h4><strong>Loja:</strong> {{ sale.store?.name || 'N/A' }}</h4>
        <h4><strong>Valor Total:</strong> {{ sale.totalValue | currency: 'BRL' }}</h4>
        <h4>
          <ion-badge [color]="getStatusColor(sale.status)">{{ getStatusLabel(sale.status) }}</ion-badge>
          <ion-badge color="primary">{{ getPaymentMethodLabel(sale.paymentMethod) }}</ion-badge>
        </h4>
        <ion-button size="small" [routerLink]="['details', sale.id]">
          <ion-icon name="eye" slot="start"></ion-icon>
          Detalhes
        </ion-button>
        <ion-button size="small" [routerLink]="['edit', sale.id]" [disabled]="sale.status === 'canceled'">
          <ion-icon name="create" slot="start"></ion-icon>
          Editar
        </ion-button>
        <ion-button size="small" (click)="cancelSale(sale)" color="danger" 
                   [disabled]="sale.status === 'canceled'">
          <ion-icon name="close-circle" slot="end"></ion-icon>
          Cancelar
        </ion-button>
      </ion-text>
    </ion-item>
    }
    @empty {
    <ion-item>Nenhuma venda encontrada com os filtros selecionados.</ion-item>
    }
  </ion-list>

  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button [routerLink]="['new']">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
