<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Vendas</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Vendas</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-card>
    <ion-card-header>
      <ion-card-title>Filtros</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-grid>
        <ion-row>
          <ion-col size="12" size-md="6">
            <ion-item>
              <ion-select label="Loja" labelPlacement="floating" [(ngModel)]="filters.storeId">
                <ion-select-option [value]="null">Todas</ion-select-option>
                @for(store of storesList; track store.id) {
                  <ion-select-option [value]="store.id">{{ store.name }}</ion-select-option>
                }
              </ion-select>
            </ion-item>
          </ion-col>
          <ion-col size="12" size-md="6">
            <ion-item>
              <ion-select label="Status" labelPlacement="floating" [(ngModel)]="filters.status">
                <ion-select-option [value]="null">Todos</ion-select-option>
                @for(status of saleStatusOptions; track status.value) {
                  <ion-select-option [value]="status.value">{{ status.label }}</ion-select-option>
                }
              </ion-select>
            </ion-item>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <ion-button expand="block" (click)="applyFilters()">
              <ion-icon name="search" slot="start"></ion-icon>
              Filtrar
            </ion-button>
          </ion-col>
          <ion-col>
            <ion-button expand="block" fill="outline" (click)="clearFilters()">
              <ion-icon name="refresh" slot="start"></ion-icon>
              Limpar
            </ion-button>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card-content>
  </ion-card>

  <ion-list>
    @for(sale of salesList; track sale.id) {
    <ion-item>
      <ion-avatar slot="start">
        <div class="sale-avatar">
          <ion-icon name="receipt-outline"></ion-icon>
        </div>
      </ion-avatar>
      <ion-label>
        <h2>Venda #{{ sale.id }}</h2>
        <p><strong>Data:</strong> {{ sale.date | date: 'dd/MM/yyyy' }}</p>
        <p><strong>Cliente:</strong> {{ sale.customer?.name || 'N/A' }}</p>
        <p><strong>Loja:</strong> {{ sale.store?.name || 'N/A' }}</p>
        <p><strong>Valor Total:</strong> {{ sale.totalValue | currency: 'BRL' }}</p>
        <p><strong>Vendedor:</strong> {{ sale.seller || 'N/A' }}</p>
        <div class="badges">
          <ion-badge [color]="getStatusColor(sale.status)">{{ getStatusLabel(sale.status) }}</ion-badge>
          <ion-badge color="primary">{{ getPaymentMethodLabel(sale.paymentMethod) }}</ion-badge>
        </div>
      </ion-label>
      <ion-button slot="end" size="small" [routerLink]="['details', sale.id]">
        <ion-icon name="eye" slot="icon-only"></ion-icon>
      </ion-button>
      <ion-button slot="end" size="small" [routerLink]="['edit', sale.id]" [disabled]="sale.status === 'canceled'">
        <ion-icon name="create" slot="icon-only"></ion-icon>
      </ion-button>
      <ion-button slot="end" size="small" color="danger" (click)="cancelSale(sale)" [disabled]="sale.status === 'canceled'">
        <ion-icon name="close-circle" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-item>
    }
    @empty {
    <ion-item>Nenhuma venda encontrada com os filtros selecionados.</ion-item>
    }
  </ion-list>

  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button [routerLink]="['new']">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
