{"version": 3, "file": "1703000004000-CreateAccessoryPhoneCompatibilityTable.js", "sourceRoot": "", "sources": ["../../../../src/db/migrations/1703000004000-CreateAccessoryPhoneCompatibilityTable.ts"], "names": [], "mappings": ";;;AAAA,qCAAkF;AAElF,MAAa,mDAAmD;IACvD,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,WAAW,CAC3B,IAAI,eAAK,CAAC;YACR,IAAI,EAAE,+BAA+B;YACrC,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,KAAK;oBACX,SAAS,EAAE,IAAI;iBAChB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,KAAK;oBACX,SAAS,EAAE,IAAI;iBAChB;aACF;SACF,CAAC,EACF,IAAI,CACL,CAAC;QAEF,MAAM,WAAW,CAAC,gBAAgB,CAChC,+BAA+B,EAC/B,IAAI,yBAAe,CAAC;YAClB,WAAW,EAAE,CAAC,cAAc,CAAC;YAC7B,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,aAAa;YAClC,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,gBAAgB,CAChC,+BAA+B,EAC/B,IAAI,yBAAe,CAAC;YAClB,WAAW,EAAE,CAAC,UAAU,CAAC;YACzB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,QAAQ;YAC7B,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB,CAAC,CACH,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,SAAS,CAAC,+BAA+B,CAAC,CAAC;IAC/D,CAAC;CACF;AA/CD,kHA+CC"}