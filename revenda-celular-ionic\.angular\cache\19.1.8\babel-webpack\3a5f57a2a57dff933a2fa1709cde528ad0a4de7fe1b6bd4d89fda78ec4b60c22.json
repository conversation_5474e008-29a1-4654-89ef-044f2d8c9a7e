{"ast": null, "code": "var _StoreFormComponent;\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/store.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/forms\";\nconst _forTrack0 = ($index, $item) => $item.value;\nfunction StoreFormComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction StoreFormComponent_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00EDnimo 3 caracteres \");\n  }\n}\nfunction StoreFormComponent_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00E1ximo 100 caracteres \");\n  }\n}\nfunction StoreFormComponent_Conditional_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction StoreFormComponent_Conditional_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction StoreFormComponent_For_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const state_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", state_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(state_r1);\n  }\n}\nfunction StoreFormComponent_Conditional_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction StoreFormComponent_Conditional_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction StoreFormComponent_Conditional_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction StoreFormComponent_For_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r2.label);\n  }\n}\nfunction StoreFormComponent_Conditional_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nexport class StoreFormComponent {\n  constructor(storeService, router, activatedRoute, toastController) {\n    this.storeService = storeService;\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    this.toastController = toastController;\n    this.storeForm = new FormGroup({\n      name: new FormControl('', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]),\n      address: new FormControl('', [Validators.required]),\n      city: new FormControl('', [Validators.required]),\n      state: new FormControl('', [Validators.required]),\n      phone: new FormControl('', [Validators.required]),\n      manager: new FormControl('', [Validators.required]),\n      isHeadquarters: new FormControl(false),\n      status: new FormControl('active', [Validators.required])\n    });\n    this.stateOptions = ['AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'];\n    this.statusOptions = [{\n      value: 'active',\n      label: 'Ativa'\n    }, {\n      value: 'inactive',\n      label: 'Inativa'\n    }, {\n      value: 'underMaintenance',\n      label: 'Em Manutenção'\n    }];\n  }\n  ngOnInit() {\n    const storeId = this.activatedRoute.snapshot.params['id'];\n    if (storeId) {\n      this.storeService.getById(storeId).subscribe({\n        next: store => {\n          if (store) {\n            this.storeId = storeId;\n            this.storeForm.patchValue(store);\n          }\n        },\n        error: error => {\n          alert('Erro ao carregar a loja com id ' + storeId);\n          console.error(error);\n        }\n      });\n    }\n  }\n  hasError(field, error) {\n    var _formControl$errors;\n    const formControl = this.storeForm.get(field);\n    return (formControl === null || formControl === void 0 ? void 0 : formControl.touched) && (formControl === null || formControl === void 0 || (_formControl$errors = formControl.errors) === null || _formControl$errors === void 0 ? void 0 : _formControl$errors[error]);\n  }\n  save() {\n    const {\n      value\n    } = this.storeForm;\n    this.storeService.save({\n      ...value,\n      id: this.storeId\n    }).subscribe({\n      next: () => {\n        this.toastController.create({\n          message: 'Loja salva com sucesso!',\n          duration: 3000\n        }).then(toast => toast.present());\n        this.router.navigate(['/stores']);\n      },\n      error: error => {\n        alert('Erro ao salvar a loja ' + value.name + '!');\n        console.error(error);\n      }\n    });\n  }\n}\n_StoreFormComponent = StoreFormComponent;\n_StoreFormComponent.ɵfac = function StoreFormComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _StoreFormComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ToastController));\n};\n_StoreFormComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _StoreFormComponent,\n  selectors: [[\"app-store-form\"]],\n  standalone: false,\n  decls: 51,\n  vars: 13,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [3, \"fullscreen\"], [1, \"form-container\"], [3, \"formGroup\"], [\"formControlName\", \"name\", \"labelPlacement\", \"floating\", \"label\", \"Nome\", \"type\", \"text\"], [\"formControlName\", \"address\", \"labelPlacement\", \"floating\", \"label\", \"Endere\\u00E7o\", \"type\", \"text\"], [\"formControlName\", \"city\", \"labelPlacement\", \"floating\", \"label\", \"Cidade\", \"type\", \"text\"], [\"formControlName\", \"state\", \"labelPlacement\", \"floating\", \"label\", \"Estado\"], [3, \"value\"], [\"formControlName\", \"phone\", \"labelPlacement\", \"floating\", \"label\", \"Telefone\", \"type\", \"tel\"], [\"formControlName\", \"manager\", \"labelPlacement\", \"floating\", \"label\", \"Gerente\", \"type\", \"text\"], [\"formControlName\", \"isHeadquarters\", \"slot\", \"end\"], [\"formControlName\", \"status\", \"labelPlacement\", \"floating\", \"label\", \"Status\"], [\"vertical\", \"bottom\", \"horizontal\", \"end\", \"slot\", \"fixed\"], [3, \"click\", \"disabled\"], [\"name\", \"checkmark\"]],\n  template: function StoreFormComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-menu-button\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5, \"Cadastro de Lojas\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 3)(7, \"div\", 4)(8, \"form\", 5)(9, \"ion-list\")(10, \"ion-item\");\n      i0.ɵɵelement(11, \"ion-input\", 6);\n      i0.ɵɵelementStart(12, \"p\");\n      i0.ɵɵtemplate(13, StoreFormComponent_Conditional_13_Template, 1, 0)(14, StoreFormComponent_Conditional_14_Template, 1, 0)(15, StoreFormComponent_Conditional_15_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(16, \"ion-item\");\n      i0.ɵɵelement(17, \"ion-input\", 7);\n      i0.ɵɵelementStart(18, \"p\");\n      i0.ɵɵtemplate(19, StoreFormComponent_Conditional_19_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(20, \"ion-item\");\n      i0.ɵɵelement(21, \"ion-input\", 8);\n      i0.ɵɵelementStart(22, \"p\");\n      i0.ɵɵtemplate(23, StoreFormComponent_Conditional_23_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(24, \"ion-item\")(25, \"ion-select\", 9);\n      i0.ɵɵrepeaterCreate(26, StoreFormComponent_For_27_Template, 2, 2, \"ion-select-option\", 10, i0.ɵɵrepeaterTrackByIdentity);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(28, \"p\");\n      i0.ɵɵtemplate(29, StoreFormComponent_Conditional_29_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(30, \"ion-item\");\n      i0.ɵɵelement(31, \"ion-input\", 11);\n      i0.ɵɵelementStart(32, \"p\");\n      i0.ɵɵtemplate(33, StoreFormComponent_Conditional_33_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(34, \"ion-item\");\n      i0.ɵɵelement(35, \"ion-input\", 12);\n      i0.ɵɵelementStart(36, \"p\");\n      i0.ɵɵtemplate(37, StoreFormComponent_Conditional_37_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(38, \"ion-item\")(39, \"ion-label\");\n      i0.ɵɵtext(40, \"Matriz:\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(41, \"ion-toggle\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(42, \"ion-item\")(43, \"ion-select\", 14);\n      i0.ɵɵrepeaterCreate(44, StoreFormComponent_For_45_Template, 2, 2, \"ion-select-option\", 10, _forTrack0);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(46, \"p\");\n      i0.ɵɵtemplate(47, StoreFormComponent_Conditional_47_Template, 1, 0);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(48, \"ion-fab\", 15)(49, \"ion-fab-button\", 16);\n      i0.ɵɵlistener(\"click\", function StoreFormComponent_Template_ion_fab_button_click_49_listener() {\n        return ctx.save();\n      });\n      i0.ɵɵelement(50, \"ion-icon\", 17);\n      i0.ɵɵelementEnd()()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.storeForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"required\") ? 13 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"minlength\") ? 14 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"maxlength\") ? 15 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"address\", \"required\") ? 19 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"city\", \"required\") ? 23 : -1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵrepeater(ctx.stateOptions);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"state\", \"required\") ? 29 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"phone\", \"required\") ? 33 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"manager\", \"required\") ? 37 : -1);\n      i0.ɵɵadvance(7);\n      i0.ɵɵrepeater(ctx.statusOptions);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"status\", \"required\") ? 47 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"disabled\", ctx.storeForm.invalid);\n    }\n  },\n  dependencies: [i4.ɵNgNoValidate, i4.NgControlStatus, i4.NgControlStatusGroup, i3.IonButtons, i3.IonContent, i3.IonFab, i3.IonFabButton, i3.IonHeader, i3.IonIcon, i3.IonInput, i3.IonItem, i3.IonLabel, i3.IonList, i3.IonMenuButton, i3.IonSelect, i3.IonSelectOption, i3.IonTitle, i3.IonToggle, i3.IonToolbar, i3.BooleanValueAccessor, i3.SelectValueAccessor, i3.TextValueAccessor, i4.FormGroupDirective, i4.FormControlName],\n  styles: [\".form-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\nion-item[_ngcontent-%COMP%] {\\n  --padding-start: 0;\\n  margin-bottom: 8px;\\n}\\nion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--ion-color-danger);\\n  padding-left: 16px;\\n}\\n\\nion-toggle[_ngcontent-%COMP%] {\\n  padding-right: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmVzL3N0b3JlLWZvcm0vc3RvcmUtZm9ybS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFDRjs7QUFFQTtFQUNFLGtCQUFBO0VBQ0Esa0JBQUE7QUFDRjtBQUNFO0VBQ0UsZUFBQTtFQUNBLDhCQUFBO0VBQ0Esa0JBQUE7QUFDSjs7QUFHQTtFQUNFLG1CQUFBO0FBQUYiLCJzb3VyY2VzQ29udGVudCI6WyIuZm9ybS1jb250YWluZXIge1xyXG4gIHBhZGRpbmc6IDE2cHg7XHJcbiAgbWF4LXdpZHRoOiA4MDBweDtcclxuICBtYXJnaW46IDAgYXV0bztcclxufVxyXG5cclxuaW9uLWl0ZW0ge1xyXG4gIC0tcGFkZGluZy1zdGFydDogMDtcclxuICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgXHJcbiAgcCB7XHJcbiAgICBmb250LXNpemU6IDEycHg7XHJcbiAgICBjb2xvcjogdmFyKC0taW9uLWNvbG9yLWRhbmdlcik7XHJcbiAgICBwYWRkaW5nLWxlZnQ6IDE2cHg7XHJcbiAgfVxyXG59XHJcblxyXG5pb24tdG9nZ2xlIHtcclxuICBwYWRkaW5nLXJpZ2h0OiAxNnB4O1xyXG59XHJcblxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n});", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "i0", "ɵɵtext", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵproperty", "state_r1", "ɵɵadvance", "ɵɵtextInterpolate", "option_r2", "value", "label", "StoreFormComponent", "constructor", "storeService", "router", "activatedRoute", "toastController", "storeForm", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "address", "city", "state", "phone", "manager", "isHeadquarters", "status", "stateOptions", "statusOptions", "ngOnInit", "storeId", "snapshot", "params", "getById", "subscribe", "next", "store", "patchValue", "error", "alert", "console", "<PERSON><PERSON><PERSON><PERSON>", "field", "_formControl$errors", "formControl", "get", "touched", "errors", "save", "id", "create", "message", "duration", "then", "toast", "present", "navigate", "ɵɵdirectiveInject", "i1", "StoreService", "i2", "Router", "ActivatedRoute", "i3", "ToastController", "selectors", "standalone", "decls", "vars", "consts", "template", "StoreFormComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "StoreFormComponent_Conditional_13_Template", "StoreFormComponent_Conditional_14_Template", "StoreFormComponent_Conditional_15_Template", "StoreFormComponent_Conditional_19_Template", "StoreFormComponent_Conditional_23_Template", "ɵɵrepeaterCreate", "StoreFormComponent_For_27_Template", "ɵɵrepeaterTrackByIdentity", "StoreFormComponent_Conditional_29_Template", "StoreFormComponent_Conditional_33_Template", "StoreFormComponent_Conditional_37_Template", "StoreFormComponent_For_45_Template", "_forTrack0", "StoreFormComponent_Conditional_47_Template", "ɵɵlistener", "StoreFormComponent_Template_ion_fab_button_click_49_listener", "ɵɵconditional", "ɵɵrepeater", "invalid"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\stores\\store-form\\store-form.component.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\stores\\store-form\\store-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ToastController } from '@ionic/angular';\r\nimport { StoreService } from '../services/store.service';\r\n\r\n@Component({\r\n  selector: 'app-store-form',\r\n  templateUrl: './store-form.component.html',\r\n  styleUrls: ['./store-form.component.scss'],\r\n  standalone: false,\r\n})\r\nexport class StoreFormComponent implements OnInit {\r\n  storeForm: FormGroup = new FormGroup({\r\n    name: new FormControl('', [\r\n      Validators.required, Validators.minLength(3), Validators.maxLength(100)\r\n    ]),\r\n    address: new FormControl('', [Validators.required]),\r\n    city: new FormControl('', [Validators.required]),\r\n    state: new FormControl('', [Validators.required]),\r\n    phone: new FormControl('', [Validators.required]),\r\n    manager: new FormControl('', [Validators.required]),\r\n    isHeadquarters: new FormControl(false),\r\n    status: new FormControl('active', [Validators.required])\r\n  });\r\n\r\n  storeId!: number;\r\n  \r\n  stateOptions: string[] = [\r\n    'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA', 'MT', 'MS', 'MG', \r\n    'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'\r\n  ];\r\n  \r\n  statusOptions = [\r\n    { value: 'active', label: 'Ativa' },\r\n    { value: 'inactive', label: 'Inativa' },\r\n    { value: 'underMaintenance', label: 'Em Manutenção' }\r\n  ];\r\n\r\n  constructor(\r\n    private storeService: StoreService,\r\n    private router: Router,\r\n    private activatedRoute: ActivatedRoute,\r\n    private toastController: ToastController\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    const storeId = this.activatedRoute.snapshot.params['id'];\r\n    if (storeId) {\r\n      this.storeService.getById(storeId).subscribe({\r\n        next: (store) => {\r\n          if (store) {\r\n            this.storeId = storeId;\r\n            this.storeForm.patchValue(store);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          alert('Erro ao carregar a loja com id ' + storeId);\r\n          console.error(error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  hasError(field: string, error: string) {\r\n    const formControl = this.storeForm.get(field);\r\n    return formControl?.touched && formControl?.errors?.[error];\r\n  }\r\n\r\n  save() {\r\n    const { value } = this.storeForm;\r\n    \r\n    this.storeService.save({\r\n      ...value,\r\n      id: this.storeId\r\n    }).subscribe({\r\n      next: () => {\r\n        this.toastController.create({\r\n          message: 'Loja salva com sucesso!',\r\n          duration: 3000,\r\n        }).then(toast => toast.present());\r\n        this.router.navigate(['/stores']);\r\n      },\r\n      error: (error) => {\r\n        alert('Erro ao salvar a loja ' + value.name + '!');\r\n        console.error(error);\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-menu-button></ion-menu-button>\r\n    </ion-buttons>\r\n    <ion-title>Cadastro de Lojas</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div class=\"form-container\">\r\n    <form [formGroup]=\"storeForm\">\r\n      <ion-list>\r\n        <ion-item>\r\n          <ion-input formControlName=\"name\" labelPlacement=\"floating\" label=\"Nome\" type=\"text\"></ion-input>\r\n          <p>\r\n          @if(hasError('name', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          @if(hasError('name', 'minlength')) {\r\n            O campo deve ter no mínimo 3 caracteres\r\n          }\r\n          @if(hasError('name', 'maxlength')) {\r\n            O campo deve ter no máximo 100 caracteres\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"address\" labelPlacement=\"floating\" label=\"Endereço\" type=\"text\"></ion-input>\r\n          <p>\r\n          @if(hasError('address', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"city\" labelPlacement=\"floating\" label=\"Cidade\" type=\"text\"></ion-input>\r\n          <p>\r\n          @if(hasError('city', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-select formControlName=\"state\" labelPlacement=\"floating\" label=\"Estado\">\r\n            @for(state of stateOptions; track state) {\r\n              <ion-select-option [value]=\"state\">{{ state }}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n          @if(hasError('state', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"phone\" labelPlacement=\"floating\" label=\"Telefone\" type=\"tel\"></ion-input>\r\n          <p>\r\n          @if(hasError('phone', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"manager\" labelPlacement=\"floating\" label=\"Gerente\" type=\"text\"></ion-input>\r\n          <p>\r\n          @if(hasError('manager', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-label>Matriz:</ion-label>\r\n          <ion-toggle formControlName=\"isHeadquarters\" slot=\"end\"></ion-toggle>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-select formControlName=\"status\" labelPlacement=\"floating\" label=\"Status\">\r\n            @for(option of statusOptions; track option.value) {\r\n              <ion-select-option [value]=\"option.value\">{{ option.label }}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n          @if(hasError('status', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          </p>\r\n        </ion-item>\r\n      </ion-list>\r\n      \r\n      <ion-fab vertical=\"bottom\" horizontal=\"end\" slot=\"fixed\">\r\n        <ion-fab-button [disabled]=\"storeForm.invalid\" (click)=\"save()\">\r\n          <ion-icon name=\"checkmark\"></ion-icon>\r\n        </ion-fab-button>\r\n      </ion-fab>\r\n    </form>\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;;;;;;;;;ICgBvDC,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,qDACF;;;;;IAEED,EAAA,CAAAC,MAAA,uDACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAOID,EAAA,CAAAE,cAAA,4BAAmC;IAAAF,EAAA,CAAAC,MAAA,GAAW;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAA/CH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAe;IAACL,EAAA,CAAAM,SAAA,EAAW;IAAXN,EAAA,CAAAO,iBAAA,CAAAF,QAAA,CAAW;;;;;IAKhDL,EAAA,CAAAC,MAAA,wCACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAYID,EAAA,CAAAE,cAAA,4BAA0C;IAAAF,EAAA,CAAAC,MAAA,GAAkB;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAA7DH,EAAA,CAAAI,UAAA,UAAAI,SAAA,CAAAC,KAAA,CAAsB;IAACT,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAO,iBAAA,CAAAC,SAAA,CAAAE,KAAA,CAAkB;;;;;IAK9DV,EAAA,CAAAC,MAAA,wCACF;;;AD/EV,OAAM,MAAOU,kBAAkB;EA2B7BC,YACUC,YAA0B,EAC1BC,MAAc,EACdC,cAA8B,EAC9BC,eAAgC;IAHhC,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IA9BzB,KAAAC,SAAS,GAAc,IAAInB,SAAS,CAAC;MACnCoB,IAAI,EAAE,IAAIrB,WAAW,CAAC,EAAE,EAAE,CACxBE,UAAU,CAACoB,QAAQ,EAAEpB,UAAU,CAACqB,SAAS,CAAC,CAAC,CAAC,EAAErB,UAAU,CAACsB,SAAS,CAAC,GAAG,CAAC,CACxE,CAAC;MACFC,OAAO,EAAE,IAAIzB,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACoB,QAAQ,CAAC,CAAC;MACnDI,IAAI,EAAE,IAAI1B,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACoB,QAAQ,CAAC,CAAC;MAChDK,KAAK,EAAE,IAAI3B,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACoB,QAAQ,CAAC,CAAC;MACjDM,KAAK,EAAE,IAAI5B,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACoB,QAAQ,CAAC,CAAC;MACjDO,OAAO,EAAE,IAAI7B,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACoB,QAAQ,CAAC,CAAC;MACnDQ,cAAc,EAAE,IAAI9B,WAAW,CAAC,KAAK,CAAC;MACtC+B,MAAM,EAAE,IAAI/B,WAAW,CAAC,QAAQ,EAAE,CAACE,UAAU,CAACoB,QAAQ,CAAC;KACxD,CAAC;IAIF,KAAAU,YAAY,GAAa,CACvB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC5E,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CACnF;IAED,KAAAC,aAAa,GAAG,CACd;MAAErB,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,EACnC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAS,CAAE,EACvC;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAe,CAAE,CACtD;EAOG;EAEJqB,QAAQA,CAAA;IACN,MAAMC,OAAO,GAAG,IAAI,CAACjB,cAAc,CAACkB,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;IACzD,IAAIF,OAAO,EAAE;MACX,IAAI,CAACnB,YAAY,CAACsB,OAAO,CAACH,OAAO,CAAC,CAACI,SAAS,CAAC;QAC3CC,IAAI,EAAGC,KAAK,IAAI;UACd,IAAIA,KAAK,EAAE;YACT,IAAI,CAACN,OAAO,GAAGA,OAAO;YACtB,IAAI,CAACf,SAAS,CAACsB,UAAU,CAACD,KAAK,CAAC;UAClC;QACF,CAAC;QACDE,KAAK,EAAGA,KAAK,IAAI;UACfC,KAAK,CAAC,iCAAiC,GAAGT,OAAO,CAAC;UAClDU,OAAO,CAACF,KAAK,CAACA,KAAK,CAAC;QACtB;OACD,CAAC;IACJ;EACF;EAEAG,QAAQA,CAACC,KAAa,EAAEJ,KAAa;IAAA,IAAAK,mBAAA;IACnC,MAAMC,WAAW,GAAG,IAAI,CAAC7B,SAAS,CAAC8B,GAAG,CAACH,KAAK,CAAC;IAC7C,OAAO,CAAAE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEE,OAAO,MAAIF,WAAW,aAAXA,WAAW,gBAAAD,mBAAA,GAAXC,WAAW,CAAEG,MAAM,cAAAJ,mBAAA,uBAAnBA,mBAAA,CAAsBL,KAAK,CAAC;EAC7D;EAEAU,IAAIA,CAAA;IACF,MAAM;MAAEzC;IAAK,CAAE,GAAG,IAAI,CAACQ,SAAS;IAEhC,IAAI,CAACJ,YAAY,CAACqC,IAAI,CAAC;MACrB,GAAGzC,KAAK;MACR0C,EAAE,EAAE,IAAI,CAACnB;KACV,CAAC,CAACI,SAAS,CAAC;MACXC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACrB,eAAe,CAACoC,MAAM,CAAC;UAC1BC,OAAO,EAAE,yBAAyB;UAClCC,QAAQ,EAAE;SACX,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC3C,MAAM,CAAC4C,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACnC,CAAC;MACDlB,KAAK,EAAGA,KAAK,IAAI;QACfC,KAAK,CAAC,wBAAwB,GAAGhC,KAAK,CAACS,IAAI,GAAG,GAAG,CAAC;QAClDwB,OAAO,CAACF,KAAK,CAACA,KAAK,CAAC;MACtB;KACD,CAAC;EACJ;;sBA5EW7B,kBAAkB;;mCAAlBA,mBAAkB,EAAAX,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA/D,EAAA,CAAA2D,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAhE,EAAA,CAAA2D,iBAAA,CAAAM,EAAA,CAAAC,eAAA;AAAA;;QAAlBvD,mBAAkB;EAAAwD,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCV3B1E,EAFJ,CAAAE,cAAA,oBAAiC,qBACA,qBACH;MACxBF,EAAA,CAAA4E,SAAA,sBAAmC;MACrC5E,EAAA,CAAAG,YAAA,EAAc;MACdH,EAAA,CAAAE,cAAA,gBAAW;MAAAF,EAAA,CAAAC,MAAA,wBAAiB;MAEhCD,EAFgC,CAAAG,YAAA,EAAY,EAC5B,EACH;MAMLH,EAJR,CAAAE,cAAA,qBAAiC,aACH,cACI,eAClB,gBACE;MACRF,EAAA,CAAA4E,SAAA,oBAAiG;MACjG5E,EAAA,CAAAE,cAAA,SAAG;MAOHF,EANA,CAAA6E,UAAA,KAAAC,0CAAA,OAAmC,KAAAC,0CAAA,OAGC,KAAAC,0CAAA,OAGA;MAItChF,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA4E,SAAA,oBAAwG;MACxG5E,EAAA,CAAAE,cAAA,SAAG;MACHF,EAAA,CAAA6E,UAAA,KAAAI,0CAAA,OAAsC;MAIxCjF,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA4E,SAAA,oBAAmG;MACnG5E,EAAA,CAAAE,cAAA,SAAG;MACHF,EAAA,CAAA6E,UAAA,KAAAK,0CAAA,OAAmC;MAIrClF,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,qBACqE;MAC3EF,EAAA,CAAAmF,gBAAA,KAAAC,kCAAA,iCAAApF,EAAA,CAAAqF,yBAAA,CAEC;MACHrF,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACHF,EAAA,CAAA6E,UAAA,KAAAS,0CAAA,OAAoC;MAItCtF,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA4E,SAAA,qBAAqG;MACrG5E,EAAA,CAAAE,cAAA,SAAG;MACHF,EAAA,CAAA6E,UAAA,KAAAU,0CAAA,OAAoC;MAItCvF,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA4E,SAAA,qBAAuG;MACvG5E,EAAA,CAAAE,cAAA,SAAG;MACHF,EAAA,CAAA6E,UAAA,KAAAW,0CAAA,OAAsC;MAIxCxF,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,iBACG;MAAAF,EAAA,CAAAC,MAAA,eAAO;MAAAD,EAAA,CAAAG,YAAA,EAAY;MAC9BH,EAAA,CAAA4E,SAAA,sBAAqE;MACvE5E,EAAA,CAAAG,YAAA,EAAW;MAGTH,EADF,CAAAE,cAAA,gBAAU,sBACsE;MAC5EF,EAAA,CAAAmF,gBAAA,KAAAM,kCAAA,iCAAAC,UAAA,CAEC;MACH1F,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACHF,EAAA,CAAA6E,UAAA,KAAAc,0CAAA,OAAqC;MAKzC3F,EAFI,CAAAG,YAAA,EAAI,EACK,EACF;MAGTH,EADF,CAAAE,cAAA,mBAAyD,0BACS;MAAjBF,EAAA,CAAA4F,UAAA,mBAAAC,6DAAA;QAAA,OAASlB,GAAA,CAAAzB,IAAA,EAAM;MAAA,EAAC;MAC7DlD,EAAA,CAAA4E,SAAA,oBAAsC;MAKhD5E,EAJQ,CAAAG,YAAA,EAAiB,EACT,EACL,EACH,EACM;;;MAvGFH,EAAA,CAAAI,UAAA,qBAAoB;MASnBJ,EAAA,CAAAM,SAAA,GAAmB;MAAnBN,EAAA,CAAAI,UAAA,oBAAmB;MAEtBJ,EAAA,CAAAM,SAAA,GAAuB;MAAvBN,EAAA,CAAAI,UAAA,cAAAuE,GAAA,CAAA1D,SAAA,CAAuB;MAKvBjB,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8F,aAAA,CAAAnB,GAAA,CAAAhC,QAAA,+BAEC;MACD3C,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA8F,aAAA,CAAAnB,GAAA,CAAAhC,QAAA,gCAEC;MACD3C,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA8F,aAAA,CAAAnB,GAAA,CAAAhC,QAAA,gCAEC;MAOD3C,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8F,aAAA,CAAAnB,GAAA,CAAAhC,QAAA,kCAEC;MAOD3C,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8F,aAAA,CAAAnB,GAAA,CAAAhC,QAAA,+BAEC;MAMC3C,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA+F,UAAA,CAAApB,GAAA,CAAA9C,YAAA,CAEC;MAGH7B,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8F,aAAA,CAAAnB,GAAA,CAAAhC,QAAA,gCAEC;MAOD3C,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8F,aAAA,CAAAnB,GAAA,CAAAhC,QAAA,gCAEC;MAOD3C,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8F,aAAA,CAAAnB,GAAA,CAAAhC,QAAA,kCAEC;MAWC3C,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA+F,UAAA,CAAApB,GAAA,CAAA7C,aAAA,CAEC;MAGH9B,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8F,aAAA,CAAAnB,GAAA,CAAAhC,QAAA,iCAEC;MAMa3C,EAAA,CAAAM,SAAA,GAA8B;MAA9BN,EAAA,CAAAI,UAAA,aAAAuE,GAAA,CAAA1D,SAAA,CAAA+E,OAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}