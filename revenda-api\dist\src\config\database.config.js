"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDatabaseConfig = void 0;
const brand_entity_1 = require("../brand/brand.entity");
const phone_entity_1 = require("../phone/phone.entity");
const accessory_entity_1 = require("../accessory/accessory.entity");
const store_entity_1 = require("../store/store.entity");
const customer_entity_1 = require("../customer/customer.entity");
const sale_entity_1 = require("../sale/sale.entity");
const sale_item_entity_1 = require("../sale/sale-item.entity");
const getDatabaseConfig = (configService) => ({
    type: 'postgres',
    host: configService.get('DB_HOST'),
    port: configService.get('DB_PORT'),
    username: configService.get('DB_USERNAME'),
    password: configService.get('DB_PASSWORD'),
    database: configService.get('DB_DATABASE'),
    entities: [brand_entity_1.Brand, phone_entity_1.Phone, accessory_entity_1.Accessory, store_entity_1.Store, customer_entity_1.Customer, sale_entity_1.Sale, sale_item_entity_1.SaleItem],
    migrations: [__dirname + '/../db/migrations/*{.ts,.js}', __dirname + '/../db/seeds/*{.ts,.js}'],
    synchronize: false,
    logging: configService.get('NODE_ENV') === 'development',
    migrationsRun: true,
});
exports.getDatabaseConfig = getDatabaseConfig;
//# sourceMappingURL=database.config.js.map