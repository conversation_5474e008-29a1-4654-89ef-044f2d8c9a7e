<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Cadastro de Clientes</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="form-container">
    <form [formGroup]="customerForm">
      <ion-list>
        <ion-item>
          <ion-input formControlName="name" labelPlacement="floating" label="Nome" type="text"></ion-input>
          <p>
            @if(hasError('name', 'required')) {
            O campo é obrigatório
            }
            @if(hasError('name', 'minlength')) {
            O campo deve ter no mínimo 3 caracteres
            }
            @if(hasError('name', 'maxlength')) {
            O campo deve ter no máximo 100 caracteres
            }
            @if(hasError('name', 'pattern')) {
            O nome deve conter apenas letras e espaços
            }
          </p>
        </ion-item>

        <ion-item>
          <ion-input formControlName="email" labelPlacement="floating" label="Email" type="email"></ion-input>
          <p>
            @if(hasError('email', 'required')) {
            O campo é obrigatório
            }
            @if(hasError('email', 'email')) {
            Email inválido
            }
            @if(hasError('email', 'invalidDomain')) {
            Use um email de domínio válido (gmail.com, hotmail.com, outlook.com, yahoo.com)
            }
          </p>
        </ion-item>

        <ion-item>
          <ion-input formControlName="phone" labelPlacement="floating" label="Telefone" type="tel" [maskito]="phoneMask"
            [maskitoElement]="maskitoElement"></ion-input>
          <p>
            @if(hasError('phone', 'required')) {
            O campo é obrigatório
            }
            @if(hasError('phone', 'invalidPhone')) {
            Telefone deve ter 11 dígitos (com DDD)
            }
          </p>
        </ion-item>

        <ion-item>
          <ion-input formControlName="birthDate" labelPlacement="floating" label="Data de Nascimento"
            [maskito]="dateMask" [maskitoElement]="maskitoElement"></ion-input>
          <p>
            @if(hasError('birthDate', 'required')) {
            O campo é obrigatório
            }
            @if(hasError('birthDate', 'tooYoung')) {
            Cliente deve ter pelo menos 16 anos
            }
            @if(hasError('birthDate', 'tooOld')) {
            Data de nascimento inválida
            }
          </p>
        </ion-item>

        <ion-item>
          <ion-input formControlName="address" labelPlacement="floating" label="Endereço" type="text"></ion-input>
          <p>
            @if(hasError('address', 'required')) {
            O campo é obrigatório
            }
            @if(hasError('address', 'minlength')) {
            Endereço deve ter pelo menos 10 caracteres
            }
          </p>
        </ion-item>

        <ion-item>
          <ion-select formControlName="customerType" labelPlacement="floating" label="Tipo de Cliente">
            @for(type of customerTypes; track type.value) {
            <ion-select-option [value]="type.value">{{ type.label }}</ion-select-option>
            }
          </ion-select>
          <p>
            @if(hasError('customerType', 'required')) {
            O campo é obrigatório
            }
          </p>
        </ion-item>

        <ion-item>
          <ion-label>Cliente Ativo:</ion-label>
          <ion-toggle formControlName="active" slot="end"></ion-toggle>
        </ion-item>
      </ion-list>

      <ion-fab vertical="bottom" horizontal="end" slot="fixed">
        <ion-fab-button [disabled]="customerForm.invalid" (click)="save()">
          <ion-icon name="checkmark"></ion-icon>
        </ion-fab-button>
      </ion-fab>
    </form>
  </div>
</ion-content>