# 🎨 TESTES ESPECÍFICOS DO FRONTEND - VALIDATORS CUSTOMIZADOS

## 🎯 **COMO USAR ESTE GUIA**
1. <PERSON><PERSON> o frontend: `ionic serve`
2. Navegue para cada formulário
3. Teste os cenários descritos abaixo
4. <PERSON><PERSON><PERSON><PERSON> se as mensagens de erro aparecem corretamente

---

## 👤 **FORMULÁRIO DE CLIENTES** (`/customers/new`)

### **📧 Validator: Email Domain**
```
❌ Teste 1: "<EMAIL>"
   Resultado esperado: "Apenas domínios permitidos (gmail, hotmail, outlook, yahoo)"

❌ Teste 2: "<EMAIL>"
   Resultado esperado: "Apenas domínios permitidos (gmail, hotmail, outlook, yahoo)"

✅ Teste 3: "<EMAIL>"
   Resultado esperado: Campo aceito sem erro

✅ Teste 4: "<EMAIL>"
   Resultado esperado: Campo aceito sem erro

✅ Teste 5: "<EMAIL>"
   Resultado esperado: Campo aceito sem erro

✅ Teste 6: "<EMAIL>"
   Resultado esperado: Campo aceito sem erro
```

### **📱 Validator: Telefone (11 dígitos)**
```
❌ Teste 1: "123456789"
   Resultado esperado: "Telefone deve ter 11 dígitos"

❌ Teste 2: "119876543210"
   Resultado esperado: "Telefone deve ter 11 dígitos"

❌ Teste 3: "abc1234567"
   Resultado esperado: "Telefone deve conter apenas números"

✅ Teste 4: "11987654321"
   Resultado esperado: Campo aceito sem erro
```

### **🎂 Validator: Idade (16-120 anos)**
```
❌ Teste 1: Data nascimento "2010-01-01" (muito jovem)
   Resultado esperado: "Idade mínima é 16 anos"

❌ Teste 2: Data nascimento "1900-01-01" (muito velho)
   Resultado esperado: "Idade máxima é 120 anos"

✅ Teste 3: Data nascimento "1990-05-15"
   Resultado esperado: Campo aceito sem erro

✅ Teste 4: Data nascimento "2007-01-01" (exatamente 16 anos)
   Resultado esperado: Campo aceito sem erro
```

### **👤 Validator: Nome (apenas letras e espaços)**
```
❌ Teste 1: "João123"
   Resultado esperado: "Nome deve conter apenas letras e espaços"

❌ Teste 2: "João@Silva"
   Resultado esperado: "Nome deve conter apenas letras e espaços"

❌ Teste 3: "João_Silva"
   Resultado esperado: "Nome deve conter apenas letras e espaços"

✅ Teste 4: "João Silva"
   Resultado esperado: Campo aceito sem erro

✅ Teste 5: "Maria José da Silva"
   Resultado esperado: Campo aceito sem erro

✅ Teste 6: "José"
   Resultado esperado: Campo aceito sem erro
```

### **🏠 Validator: Endereço (mínimo 10 caracteres)**
```
❌ Teste 1: "Rua A"
   Resultado esperado: "Endereço deve ter no mínimo 10 caracteres"

❌ Teste 2: "R. B, 123"
   Resultado esperado: "Endereço deve ter no mínimo 10 caracteres"

✅ Teste 3: "Rua das Flores, 123"
   Resultado esperado: Campo aceito sem erro

✅ Teste 4: "Av. Paulista, 1000, Bela Vista"
   Resultado esperado: Campo aceito sem erro
```

---

## 🏪 **FORMULÁRIO DE LOJAS** (`/stores/new`)

### **🏢 Validator: Nome da Loja (deve conter palavra identificadora)**
```
❌ Teste 1: "Estabelecimento Central"
   Resultado esperado: "Nome deve conter uma palavra identificadora (loja, store, shop)"

❌ Teste 2: "Comércio de Eletrônicos"
   Resultado esperado: "Nome deve conter uma palavra identificadora (loja, store, shop)"

❌ Teste 3: "Tech Center"
   Resultado esperado: "Nome deve conter uma palavra identificadora (loja, store, shop)"

✅ Teste 4: "Loja Tech Center"
   Resultado esperado: Campo aceito sem erro

✅ Teste 5: "Tech Store"
   Resultado esperado: Campo aceito sem erro

✅ Teste 6: "Mobile Shop"
   Resultado esperado: Campo aceito sem erro

✅ Teste 7: "LOJA CENTRAL" (maiúscula)
   Resultado esperado: Campo aceito sem erro
```

### **📞 Validator: Telefone (10 ou 11 dígitos)**
```
❌ Teste 1: "123456789"
   Resultado esperado: "Telefone deve ter 10 ou 11 dígitos"

❌ Teste 2: "119876543210"
   Resultado esperado: "Telefone deve ter 10 ou 11 dígitos"

✅ Teste 3: "1133334444" (10 dígitos)
   Resultado esperado: Campo aceito sem erro

✅ Teste 4: "11987654321" (11 dígitos)
   Resultado esperado: Campo aceito sem erro
```

### **🏙️ Validator: Cidade (apenas letras e espaços)**
```
❌ Teste 1: "São Paulo123"
   Resultado esperado: "Cidade deve conter apenas letras e espaços"

❌ Teste 2: "Rio-de-Janeiro"
   Resultado esperado: "Cidade deve conter apenas letras e espaços"

✅ Teste 3: "São Paulo"
   Resultado esperado: Campo aceito sem erro

✅ Teste 4: "Rio de Janeiro"
   Resultado esperado: Campo aceito sem erro
```

### **👨‍💼 Validator: Gerente (apenas letras e espaços)**
```
❌ Teste 1: "João123"
   Resultado esperado: "Gerente deve conter apenas letras e espaços"

❌ Teste 2: "Maria@Silva"
   Resultado esperado: "Gerente deve conter apenas letras e espaços"

✅ Teste 3: "Maria Santos"
   Resultado esperado: Campo aceito sem erro

✅ Teste 4: "José da Silva"
   Resultado esperado: Campo aceito sem erro
```

---

## 📱 **FORMULÁRIO DE CELULARES** (`/phones/new`)

### **📱 Validator: Modelo (não pode conter palavras proibidas)**
```
❌ Teste 1: "iPhone teste"
   Resultado esperado: "Modelo não pode conter palavras como 'teste' ou 'exemplo'"

❌ Teste 2: "Samsung test"
   Resultado esperado: "Modelo não pode conter palavras como 'teste' ou 'exemplo'"

❌ Teste 3: "Celular exemplo"
   Resultado esperado: "Modelo não pode conter palavras como 'teste' ou 'exemplo'"

❌ Teste 4: "TESTE Galaxy" (maiúscula)
   Resultado esperado: "Modelo não pode conter palavras como 'teste' ou 'exemplo'"

✅ Teste 5: "iPhone 15 Pro"
   Resultado esperado: Campo aceito sem erro

✅ Teste 6: "Samsung Galaxy S23"
   Resultado esperado: Campo aceito sem erro
```

### **📅 Validator: Data de Lançamento (não pode ser futura nem muito antiga)**
```
❌ Teste 1: "2025-12-31" (futuro)
   Resultado esperado: "Data de lançamento não pode ser no futuro"

❌ Teste 2: "1999-01-01" (muito antiga)
   Resultado esperado: "Data de lançamento deve ser posterior ao ano 2000"

✅ Teste 3: "2023-09-15"
   Resultado esperado: Campo aceito sem erro

✅ Teste 4: "2000-01-01" (limite mínimo)
   Resultado esperado: Campo aceito sem erro

✅ Teste 5: Data de hoje
   Resultado esperado: Campo aceito sem erro
```

### **💰 Validator: Preço (máximo R$ 50.000)**
```
❌ Teste 1: "60000"
   Resultado esperado: "Preço não pode ser superior a R$ 50.000"

❌ Teste 2: "50000.01"
   Resultado esperado: "Preço não pode ser superior a R$ 50.000"

✅ Teste 3: "5999.99"
   Resultado esperado: Campo aceito sem erro

✅ Teste 4: "50000" (limite máximo)
   Resultado esperado: Campo aceito sem erro

✅ Teste 5: "100"
   Resultado esperado: Campo aceito sem erro
```

### **📦 Validator: Estoque (máximo 10.000 unidades)**
```
❌ Teste 1: "15000"
   Resultado esperado: "Estoque não pode ser superior a 10.000 unidades"

❌ Teste 2: "10001"
   Resultado esperado: "Estoque não pode ser superior a 10.000 unidades"

✅ Teste 3: "100"
   Resultado esperado: Campo aceito sem erro

✅ Teste 4: "10000" (limite máximo)
   Resultado esperado: Campo aceito sem erro

✅ Teste 5: "0"
   Resultado esperado: Campo aceito sem erro
```

---

## 🔌 **FORMULÁRIO DE ACESSÓRIOS** (`/accessories/new`)

### **🏷️ Validator: Nome (deve conter palavra identificadora)**
```
❌ Teste 1: "Produto Universal"
   Resultado esperado: "Nome deve conter uma palavra identificadora (case, capa, carregador, fone, película, suporte)"

❌ Teste 2: "Acessório Premium"
   Resultado esperado: "Nome deve conter uma palavra identificadora (case, capa, carregador, fone, película, suporte)"

✅ Teste 3: "Capa Protetora"
   Resultado esperado: Campo aceito sem erro

✅ Teste 4: "Carregador Rápido"
   Resultado esperado: Campo aceito sem erro

✅ Teste 5: "Fone Bluetooth"
   Resultado esperado: Campo aceito sem erro

✅ Teste 6: "Película de Vidro"
   Resultado esperado: Campo aceito sem erro

✅ Teste 7: "Suporte Veicular"
   Resultado esperado: Campo aceito sem erro

✅ Teste 8: "Case Premium"
   Resultado esperado: Campo aceito sem erro
```

### **📝 Validator: Descrição (não pode conter palavras negativas)**
```
❌ Teste 1: "Produto ruim de qualidade"
   Resultado esperado: "Descrição não pode conter palavras negativas"

❌ Teste 2: "Acessório péssimo"
   Resultado esperado: "Descrição não pode conter palavras negativas"

❌ Teste 3: "Qualidade horrível"
   Resultado esperado: "Descrição não pode conter palavras negativas"

✅ Teste 4: "Capa de alta qualidade"
   Resultado esperado: Campo aceito sem erro

✅ Teste 5: "Excelente proteção"
   Resultado esperado: Campo aceito sem erro

✅ Teste 6: "Produto premium com ótimo acabamento"
   Resultado esperado: Campo aceito sem erro
```

### **💰 Validator: Preço (máximo R$ 5.000)**
```
❌ Teste 1: "6000"
   Resultado esperado: "Preço não pode ser superior a R$ 5.000"

❌ Teste 2: "5000.01"
   Resultado esperado: "Preço não pode ser superior a R$ 5.000"

✅ Teste 3: "89.90"
   Resultado esperado: Campo aceito sem erro

✅ Teste 4: "5000" (limite máximo)
   Resultado esperado: Campo aceito sem erro

✅ Teste 5: "29.99"
   Resultado esperado: Campo aceito sem erro
```

---

## 🧾 **FORMULÁRIO DE VENDAS** (`/sales/new`)

### **📦 Teste: Controle de Estoque**

#### **Cenário 1: Produto sem estoque definido**
```
1. Selecionar produto antigo (sem campo stock)
2. Inserir quantidade: 1
3. Verificar indicador: "1/∞" em verde
4. Resultado esperado: Deve permitir a venda
```

#### **Cenário 2: Produto com estoque suficiente**
```
1. Selecionar produto com estoque = 10
2. Inserir quantidade: 5
3. Verificar indicador: "5/10" em verde
4. Resultado esperado: Deve permitir a venda
```

#### **Cenário 3: Produto no limite do estoque**
```
1. Selecionar produto com estoque = 5
2. Inserir quantidade: 5
3. Verificar indicador: "5/5" em amarelo
4. Resultado esperado: Deve permitir a venda
```

#### **Cenário 4: Produto acima do estoque**
```
1. Selecionar produto com estoque = 3
2. Inserir quantidade: 5
3. Verificar indicador: "5/3" em vermelho
4. Verificar erro: "Quantidade excede estoque disponível"
5. Resultado esperado: Não deve permitir salvar
```

#### **Cenário 5: Produto sem estoque**
```
1. Selecionar produto com estoque = 0
2. Tentar inserir quantidade: 1
3. Verificar erro: "Produto sem estoque disponível"
4. Resultado esperado: Não deve permitir a venda
```

### **🔄 Teste: Reset de Campos**

#### **Cenário 1: Alterar tipo de produto**
```
1. Selecionar tipo: "Phone"
2. Selecionar produto: "iPhone 15"
3. Inserir quantidade: 3
4. Alterar tipo para: "Accessory"
5. Verificar: Todos os campos devem ser resetados
6. Resultado esperado: Produto vazio, quantidade = 1, preço = 0
```

#### **Cenário 2: Alterar produto**
```
1. Selecionar produto: "iPhone 15" (preço R$ 5.999)
2. Inserir quantidade: 3
3. Alterar produto para: "Samsung Galaxy" (preço R$ 3.999)
4. Verificar: Quantidade deve voltar para 1, preço deve atualizar
5. Resultado esperado: Quantidade = 1, preço = R$ 3.999
```

---

## ✅ **CHECKLIST DE VALIDAÇÃO**

### **Para cada formulário, verificar:**
- [ ] Mensagens de erro aparecem em tempo real
- [ ] Mensagens desaparecem quando erro é corrigido
- [ ] Botão "Salvar" fica desabilitado com erros
- [ ] Formulário só submete quando todos os campos são válidos
- [ ] Validators customizados funcionam corretamente
- [ ] Navegação entre campos funciona normalmente

### **Para o formulário de vendas, verificar:**
- [ ] Indicador de estoque aparece corretamente
- [ ] Cores do indicador mudam conforme a situação
- [ ] Reset de campos funciona ao alterar tipo/produto
- [ ] Cálculo de subtotal funciona automaticamente
- [ ] Validação de estoque impede vendas inválidas

---

## 🐛 **PROBLEMAS COMUNS E SOLUÇÕES**

### **Problema: Mensagem de erro não aparece**
- Verificar se o campo foi "tocado" (touched)
- Verificar se o validator está sendo chamado
- Verificar se o método `hasError()` está correto

### **Problema: Validator não funciona**
- Verificar se o validator está vinculado ao FormControl
- Verificar se a lógica do validator está correta
- Verificar se o validator retorna o objeto de erro correto

### **Problema: Reset de campos não funciona**
- Verificar se o listener está configurado corretamente
- Verificar se `emitEvent: false` está sendo usado
- Verificar se `updateTotals()` está sendo chamado

---

**🎯 Use este guia para garantir que todos os validators e funcionalidades do frontend estejam funcionando perfeitamente!**
