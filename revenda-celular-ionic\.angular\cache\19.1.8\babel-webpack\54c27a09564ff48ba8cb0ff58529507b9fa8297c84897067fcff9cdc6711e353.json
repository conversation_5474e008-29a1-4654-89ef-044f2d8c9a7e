{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst NAMESPACE = 'ionic';\nconst BUILD = /* ionic */{\n  allRenderFn: false,\n  appendChildSlotFix: true,\n  asyncLoading: true,\n  asyncQueue: false,\n  attachStyles: true,\n  cloneNodeFix: true,\n  cmpDidLoad: true,\n  cmpDidRender: true,\n  cmpDidUnload: false,\n  cmpDidUpdate: true,\n  cmpShouldUpdate: false,\n  cmpWillLoad: true,\n  cmpWillRender: true,\n  cmpWillUpdate: false,\n  connectedCallback: true,\n  constructableCSS: true,\n  cssAnnotations: true,\n  devTools: false,\n  disconnectedCallback: true,\n  element: false,\n  event: true,\n  experimentalScopedSlotChanges: true,\n  experimentalSlotFixes: true,\n  formAssociated: false,\n  hasRenderFn: true,\n  hostListener: true,\n  hostListenerTarget: true,\n  hostListenerTargetBody: true,\n  hostListenerTargetDocument: true,\n  hostListenerTargetParent: false,\n  hostListenerTargetWindow: true,\n  hotModuleReplacement: false,\n  hydrateClientSide: true,\n  hydrateServerSide: false,\n  hydratedAttribute: false,\n  hydratedClass: true,\n  hydratedSelectorName: \"hydrated\",\n  initializeNextTick: false,\n  invisiblePrehydration: true,\n  isDebug: false,\n  isDev: false,\n  isTesting: false,\n  lazyLoad: true,\n  lifecycle: true,\n  lifecycleDOMEvents: false,\n  member: true,\n  method: true,\n  mode: true,\n  observeAttribute: true,\n  profile: false,\n  prop: true,\n  propBoolean: true,\n  propMutable: true,\n  propNumber: true,\n  propString: true,\n  reflect: true,\n  scoped: true,\n  scopedSlotTextContentFix: true,\n  scriptDataOpts: false,\n  shadowDelegatesFocus: true,\n  shadowDom: true,\n  slot: true,\n  slotChildNodesFix: true,\n  slotRelocation: true,\n  state: true,\n  style: true,\n  svg: true,\n  taskQueue: true,\n  transformTagName: false,\n  updatable: true,\n  vdomAttribute: true,\n  vdomClass: true,\n  vdomFunctional: true,\n  vdomKey: true,\n  vdomListener: true,\n  vdomPropOrAttr: true,\n  vdomRef: true,\n  vdomRender: true,\n  vdomStyle: true,\n  vdomText: true,\n  vdomXlink: true,\n  watchCallback: true\n};\n\n/*\n Stencil Client Platform v4.20.0 | MIT Licensed | https://stenciljs.com\n */\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\nvar Build = {\n  isDev: false,\n  isBrowser: true,\n  isServer: false,\n  isTesting: false\n};\nvar hostRefs = /* @__PURE__ */new WeakMap();\nvar getHostRef = ref => hostRefs.get(ref);\nvar registerInstance = (lazyInstance, hostRef) => hostRefs.set(hostRef.$lazyInstance$ = lazyInstance, hostRef);\nvar registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: /* @__PURE__ */new Map()\n  };\n  {\n    hostRef.$onInstancePromise$ = new Promise(r => hostRef.$onInstanceResolve$ = r);\n  }\n  {\n    hostRef.$onReadyPromise$ = new Promise(r => hostRef.$onReadyResolve$ = r);\n    hostElement[\"s-p\"] = [];\n    hostElement[\"s-rc\"] = [];\n  }\n  return hostRefs.set(hostElement, hostRef);\n};\nvar isMemberInElement = (elm, memberName) => memberName in elm;\nvar consoleError = (e, el) => (0, console.error)(e, el);\n\n// src/client/client-load-module.ts\nvar cmpModules = /* @__PURE__ */new Map();\nvar loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  const exportName = cmpMeta.$tagName$.replace(/-/g, \"_\");\n  const bundleId = cmpMeta.$lazyBundleId$;\n  if (!bundleId) {\n    return void 0;\n  }\n  const module = cmpModules.get(bundleId);\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(/* @vite-ignore */\n  /* webpackInclude: /\\.entry\\.js$/ */\n  /* webpackExclude: /\\.system\\.entry\\.js$/ */\n  /* webpackMode: \"lazy\" */\n  `./${bundleId}.entry.js${\"\"}`).then(importedModule => {\n    {\n      cmpModules.set(bundleId, importedModule);\n    }\n    return importedModule[exportName];\n  }, consoleError);\n};\n\n// src/client/client-style.ts\nvar styles = /* @__PURE__ */new Map();\nvar modeResolutionChain = [];\n\n// src/runtime/runtime-constants.ts\nvar CONTENT_REF_ID = \"r\";\nvar ORG_LOCATION_ID = \"o\";\nvar SLOT_NODE_ID = \"s\";\nvar TEXT_NODE_ID = \"t\";\nvar HYDRATE_ID = \"s-id\";\nvar HYDRATED_STYLE_ID = \"sty-id\";\nvar HYDRATE_CHILD_ID = \"c-id\";\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar SLOT_FB_CSS = \"slot-fb{display:contents}slot-fb[hidden]{display:none}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar win = typeof window !== \"undefined\" ? window : {};\nvar doc = win.document || {\n  head: {}\n};\nvar H = win.HTMLElement || class {};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: h2 => h2(),\n  raf: h2 => requestAnimationFrame(h2),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nvar setPlatformHelpers = helpers => {\n  Object.assign(plt, helpers);\n};\nvar supportsShadow = BUILD.shadowDom;\nvar supportsListenerOptions = /* @__PURE__ */(() => {\n  let supportsListenerOptions2 = false;\n  try {\n    doc.addEventListener(\"e\", null, Object.defineProperty({}, \"passive\", {\n      get() {\n        supportsListenerOptions2 = true;\n      }\n    }));\n  } catch (e) {}\n  return supportsListenerOptions2;\n})();\nvar promiseResolve = v => Promise.resolve(v);\nvar supportsConstructableStylesheets = /* @__PURE__ */(() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {}\n  return false;\n})();\nvar queuePending = false;\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueTask = (queue, write) => cb => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nvar consume = queue => {\n  for (let i2 = 0; i2 < queue.length; i2++) {\n    try {\n      queue[i2](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nvar flush = () => {\n  consume(queueDomReads);\n  {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = cb => promiseResolve().then(cb);\nvar readTask = /* @__PURE__ */queueTask(queueDomReads, false);\nvar writeTask = /* @__PURE__ */queueTask(queueDomWrites, true);\n\n// src/runtime/asset-path.ts\nvar getAssetPath = path => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\n\n// src/utils/constants.ts\nvar EMPTY_OBJ = {};\nvar SVG_NS = \"http://www.w3.org/2000/svg\";\nvar HTML_NS = \"http://www.w3.org/1999/xhtml\";\n\n// src/utils/helpers.ts\nvar isDef = v => v != null;\nvar isComplexType = o => {\n  o = typeof o;\n  return o === \"object\" || o === \"function\";\n};\n\n// src/utils/query-nonce-meta-tag-content.ts\nfunction queryNonceMetaTagContent(doc2) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc2.head) == null ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) == null ? void 0 : _b.getAttribute(\"content\")) != null ? _c : void 0;\n}\n\n// src/utils/result.ts\nvar result_exports = {};\n__export(result_exports, {\n  err: () => err,\n  map: () => map,\n  ok: () => ok,\n  unwrap: () => unwrap,\n  unwrapErr: () => unwrapErr\n});\nvar ok = value => ({\n  isOk: true,\n  isErr: false,\n  value\n});\nvar err = value => ({\n  isOk: false,\n  isErr: true,\n  value\n});\nfunction map(result, fn) {\n  if (result.isOk) {\n    const val = fn(result.value);\n    if (val instanceof Promise) {\n      return val.then(newVal => ok(newVal));\n    } else {\n      return ok(val);\n    }\n  }\n  if (result.isErr) {\n    const value = result.value;\n    return err(value);\n  }\n  throw \"should never get here\";\n}\nvar unwrap = result => {\n  if (result.isOk) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar unwrapErr = result => {\n  if (result.isErr) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar createTime = (fnName, tagName = \"\") => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nvar uniqueTime = (key, measureText) => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nvar h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = c => {\n    for (let i2 = 0; i2 < c.length; i2++) {\n      child = c[i2];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== \"boolean\") {\n        if (simple = typeof nodeName !== \"function\" && !isComplexType(child)) {\n          child = String(child);\n        }\n        if (simple && lastSimple) {\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== \"object\" ? classData : Object.keys(classData).filter(k => classData[k]).join(\" \");\n      }\n    }\n  }\n  if (typeof nodeName === \"function\") {\n    return nodeName(vnodeData === null ? {} : vnodeData, vNodeChildren, vdomFnUtils);\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  {\n    vnode.$key$ = key;\n  }\n  {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\nvar newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  {\n    vnode.$attrs$ = null;\n  }\n  {\n    vnode.$key$ = null;\n  }\n  {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nvar Host = {};\nvar isHost = node => node && node.$tag$ === Host;\nvar vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\nvar convertToPublic = node => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\nvar convertToPrivate = node => {\n  if (typeof node.vtag === \"function\") {\n    const vnodeData = {\n      ...node.vattrs\n    };\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...(node.vchildren || []));\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\n\n// src/runtime/client-hydrate.ts\nvar initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  const endHydrate = createTime(\"hydrateClient\", tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const shadowRootNodes = shadowRoot ? [] : null;\n  const vnode = hostRef.$vnode$ = newVNode(tagName, null);\n  if (!plt.$orgLocNodes$) {\n    initializeDocumentHydrate(doc.body, plt.$orgLocNodes$ = /* @__PURE__ */new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId);\n  childRenderNodes.map(c => {\n    const orgLocationId = c.$hostId$ + \".\" + c.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = c.$elm$;\n    if (orgLocationNode && supportsShadow && orgLocationNode[\"s-en\"] === \"\") {\n      orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n    }\n    if (!shadowRoot) {\n      node[\"s-hn\"] = tagName;\n      if (orgLocationNode) {\n        node[\"s-ol\"] = orgLocationNode;\n        node[\"s-ol\"][\"s-nr\"] = node;\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  });\n  if (shadowRoot) {\n    shadowRootNodes.map(shadowRootNode => {\n      if (shadowRootNode) {\n        shadowRoot.appendChild(shadowRootNode);\n      }\n    });\n  }\n  endHydrate();\n};\nvar clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i2;\n  if (node.nodeType === 1 /* ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      childIdSplt = childNodeType.split(\".\");\n      if (childIdSplt[0] === hostId || childIdSplt[0] === \"0\") {\n        childVNode = {\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          $attrs$: null,\n          $children$: null,\n          $key$: null,\n          $name$: null,\n          $text$: null\n        };\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        parentVNode.$children$[childVNode.$index$] = childVNode;\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    if (node.shadowRoot) {\n      for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {\n        clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.shadowRoot.childNodes[i2], hostId);\n      }\n    }\n    for (i2 = node.childNodes.length - 1; i2 >= 0; i2--) {\n      clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.childNodes[i2], hostId);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[1] === hostId || childIdSplt[1] === \"0\") {\n      childNodeType = childIdSplt[0];\n      childVNode = {\n        $flags$: 0,\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4],\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      };\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = node.nextSibling;\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          node.remove();\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n          if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        if (childNodeType === SLOT_NODE_ID) {\n          childVNode.$tag$ = \"slot\";\n          if (childIdSplt[5]) {\n            node[\"s-sn\"] = childVNode.$name$ = childIdSplt[5];\n          } else {\n            node[\"s-sn\"] = \"\";\n          }\n          node[\"s-sr\"] = true;\n          if (shadowRootNodes) {\n            childVNode.$elm$ = doc.createElement(childVNode.$tag$);\n            if (childVNode.$name$) {\n              childVNode.$elm$.setAttribute(\"name\", childVNode.$name$);\n            }\n            node.parentNode.insertBefore(childVNode.$elm$, node);\n            node.remove();\n            if (childVNode.$depth$ === \"0\") {\n              shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n            }\n          }\n          slotNodes.push(childVNode);\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        } else if (childNodeType === CONTENT_REF_ID) {\n          if (shadowRootNodes) {\n            node.remove();\n          } else {\n            hostElm[\"s-cr\"] = node;\n            node[\"s-cn\"] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === \"style\") {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = \"0\";\n    parentVNode.$children$ = [vnode];\n  }\n};\nvar initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* ElementNode */) {\n    let i2 = 0;\n    if (node.shadowRoot) {\n      for (; i2 < node.shadowRoot.childNodes.length; i2++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);\n      }\n    }\n    for (i2 = 0; i2 < node.childNodes.length; i2++) {\n      initializeDocumentHydrate(node.childNodes[i2], orgLocNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    const childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + \".\" + childIdSplt[2], node);\n      node.nodeValue = \"\";\n      node[\"s-en\"] = childIdSplt[3];\n    }\n  }\n};\n\n// src/runtime/mode.ts\nvar computeMode = elm => modeResolutionChain.map(h2 => h2(elm)).find(m => !!m);\nvar setMode = handler => modeResolutionChain.push(handler);\nvar getMode = ref => getHostRef(ref).$modeName$;\nvar parsePropertyValue = (propValue, propType) => {\n  if (propValue != null && !isComplexType(propValue)) {\n    if (propType & 4 /* Boolean */) {\n      return propValue === \"false\" ? false : propValue === \"\" || !!propValue;\n    }\n    if (propType & 2 /* Number */) {\n      return parseFloat(propValue);\n    }\n    if (propType & 1 /* String */) {\n      return String(propValue);\n    }\n    return propValue;\n  }\n  return propValue;\n};\nvar getElement = ref => getHostRef(ref).$hostElement$;\n\n// src/runtime/event-emitter.ts\nvar createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: detail => {\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* Bubbles */),\n        composed: !!(flags & 2 /* Composed */),\n        cancelable: !!(flags & 1 /* Cancellable */),\n        detail\n      });\n    }\n  };\n};\nvar emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\nvar rootAppliedStyles = /* @__PURE__ */new WeakMap();\nvar registerStyle = (scopeId2, cssText, allowCS) => {\n  let style = styles.get(scopeId2);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === \"string\") {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId2, style);\n};\nvar addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId2 = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId2);\n  styleContainerNode = styleContainerNode.nodeType === 11 /* DocumentFragment */ ? styleContainerNode : doc;\n  if (style) {\n    if (typeof style === \"string\") {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */new Set());\n      }\n      if (!appliedStyles.has(scopeId2)) {\n        if (styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`))) {\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = doc.createElement(\"style\");\n          styleElm.innerHTML = style;\n          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n          if (nonce != null) {\n            styleElm.setAttribute(\"nonce\", nonce);\n          }\n          const injectStyle =\n          /**\n           * we render a scoped component\n           */\n          !(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) ||\n          /**\n          * we are using shadow dom and render the style tag within the shadowRoot\n          */\n          cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */ && styleContainerNode.nodeName !== \"HEAD\";\n          if (injectStyle) {\n            styleContainerNode.insertBefore(styleElm, styleContainerNode.querySelector(\"link\"));\n          }\n        }\n        if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId2);\n        }\n      }\n    } else if (!styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId2;\n};\nvar attachStyles = hostRef => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime(\"attachStyles\", cmpMeta.$tagName$);\n  const scopeId2 = addStyle(elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(), cmpMeta, hostRef.$modeName$);\n  if (flags & 10 /* needsScopedEncapsulation */ && flags & 2 /* scopedCssEncapsulation */) {\n    elm[\"s-sc\"] = scopeId2;\n    elm.classList.add(scopeId2 + \"-h\");\n    if (flags & 2 /* scopedCssEncapsulation */) {\n      elm.classList.add(scopeId2 + \"-s\");\n    }\n  }\n  endAttachStyles();\n};\nvar getScopeId = (cmp, mode) => \"sc-\" + (mode && cmp.$flags$ & 32 /* hasMode */ ? cmp.$tagName$ + \"-\" + mode : cmp.$tagName$);\nvar setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {\n  if (oldValue !== newValue) {\n    let isProp = isMemberInElement(elm, memberName);\n    let ln = memberName.toLowerCase();\n    if (memberName === \"class\") {\n      const classList = elm.classList;\n      const oldClasses = parseClassList(oldValue);\n      const newClasses = parseClassList(newValue);\n      classList.remove(...oldClasses.filter(c => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter(c => c && !oldClasses.includes(c)));\n    } else if (memberName === \"style\") {\n      {\n        for (const prop in oldValue) {\n          if (!newValue || newValue[prop] == null) {\n            if (prop.includes(\"-\")) {\n              elm.style.removeProperty(prop);\n            } else {\n              elm.style[prop] = \"\";\n            }\n          }\n        }\n      }\n      for (const prop in newValue) {\n        if (!oldValue || newValue[prop] !== oldValue[prop]) {\n          if (prop.includes(\"-\")) {\n            elm.style.setProperty(prop, newValue[prop]);\n          } else {\n            elm.style[prop] = newValue[prop];\n          }\n        }\n      }\n    } else if (memberName === \"key\") ;else if (memberName === \"ref\") {\n      if (newValue) {\n        newValue(elm);\n      }\n    } else if (!isProp && memberName[0] === \"o\" && memberName[1] === \"n\") {\n      if (memberName[2] === \"-\") {\n        memberName = memberName.slice(3);\n      } else if (isMemberInElement(win, ln)) {\n        memberName = ln.slice(2);\n      } else {\n        memberName = ln[2] + memberName.slice(3);\n      }\n      if (oldValue || newValue) {\n        const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n        memberName = memberName.replace(CAPTURE_EVENT_REGEX, \"\");\n        if (oldValue) {\n          plt.rel(elm, memberName, oldValue, capture);\n        }\n        if (newValue) {\n          plt.ael(elm, memberName, newValue, capture);\n        }\n      }\n    } else {\n      const isComplex = isComplexType(newValue);\n      if ((isProp || isComplex && newValue !== null) && !isSvg) {\n        try {\n          if (!elm.tagName.includes(\"-\")) {\n            const n = newValue == null ? \"\" : newValue;\n            if (memberName === \"list\") {\n              isProp = false;\n            } else if (oldValue == null || elm[memberName] != n) {\n              elm[memberName] = n;\n            }\n          } else {\n            elm[memberName] = newValue;\n          }\n        } catch (e) {}\n      }\n      let xlink = false;\n      {\n        if (ln !== (ln = ln.replace(/^xlink\\:?/, \"\"))) {\n          memberName = ln;\n          xlink = true;\n        }\n      }\n      if (newValue == null || newValue === false) {\n        if (newValue !== false || elm.getAttribute(memberName) === \"\") {\n          if (xlink) {\n            elm.removeAttributeNS(XLINK_NS, memberName);\n          } else {\n            elm.removeAttribute(memberName);\n          }\n        }\n      } else if ((!isProp || flags & 4 /* isHost */ || isSvg) && !isComplex) {\n        newValue = newValue === true ? \"\" : newValue;\n        if (xlink) {\n          elm.setAttributeNS(XLINK_NS, memberName, newValue);\n        } else {\n          elm.setAttribute(memberName, newValue);\n        }\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = value => !value ? [] : value.split(parseClassListRegex);\nvar CAPTURE_EVENT_SUFFIX = \"Capture\";\nvar CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + \"$\");\n\n// src/runtime/vdom/update-element.ts\nvar updateElement = (oldVnode, newVnode, isSvgMode2) => {\n  const elm = newVnode.$elm$.nodeType === 11 /* DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || EMPTY_OBJ;\n  const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;\n  {\n    for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], void 0, isSvgMode2, newVnode.$flags$);\n      }\n    }\n  }\n  for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode2, newVnode.$flags$);\n  }\n};\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes(\"ref\") ?\n  // we need to sort these to ensure that `'ref'` is the last attr\n  [...attrNames.filter(attr => attr !== \"ref\"), \"ref\"] :\n  // no need to sort, return the original array\n  attrNames;\n}\n\n// src/runtime/vdom/vdom-render.ts\nvar scopeId;\nvar contentRef;\nvar hostTagName;\nvar useNativeShadowDom = false;\nvar checkSlotFallbackVisibility = false;\nvar checkSlotRelocate = false;\nvar isSvgMode = false;\nvar createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {\n  var _a;\n  const newVNode2 = newParentVNode.$children$[childIndex];\n  let i2 = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (!useNativeShadowDom) {\n    checkSlotRelocate = true;\n    if (newVNode2.$tag$ === \"slot\") {\n      if (scopeId) {\n        parentElm.classList.add(scopeId + \"-s\");\n      }\n      newVNode2.$flags$ |= newVNode2.$children$ ?\n      // slot element has fallback content\n      // still create an element that \"mocks\" the slot element\n      2 /* isSlotFallback */ :\n      // slot element does not have fallback content\n      // create an html comment we'll use to always reference\n      // where actual slot content should sit next to\n      1 /* isSlotReference */;\n    }\n  }\n  if (newVNode2.$text$ !== null) {\n    elm = newVNode2.$elm$ = doc.createTextNode(newVNode2.$text$);\n  } else if (newVNode2.$flags$ & 1 /* isSlotReference */) {\n    elm = newVNode2.$elm$ = doc.createTextNode(\"\");\n  } else {\n    if (!isSvgMode) {\n      isSvgMode = newVNode2.$tag$ === \"svg\";\n    }\n    elm = newVNode2.$elm$ = doc.createElementNS(isSvgMode ? SVG_NS : HTML_NS, !useNativeShadowDom && BUILD.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$);\n    if (isSvgMode && newVNode2.$tag$ === \"foreignObject\") {\n      isSvgMode = false;\n    }\n    {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n    const rootNode = elm.getRootNode();\n    const isElementWithinShadowRoot = !rootNode.querySelector(\"body\");\n    if (!isElementWithinShadowRoot && BUILD.scoped && isDef(scopeId) && elm[\"s-si\"] !== scopeId) {\n      elm.classList.add(elm[\"s-si\"] = scopeId);\n    }\n    {\n      updateElementScopeIds(elm, parentElm);\n    }\n    if (newVNode2.$children$) {\n      for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {\n        childNode = createElm(oldParentVNode, newVNode2, i2, elm);\n        if (childNode) {\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    {\n      if (newVNode2.$tag$ === \"svg\") {\n        isSvgMode = false;\n      } else if (elm.tagName === \"foreignObject\") {\n        isSvgMode = true;\n      }\n    }\n  }\n  elm[\"s-hn\"] = hostTagName;\n  {\n    if (newVNode2.$flags$ & (2 /* isSlotFallback */ | 1 /* isSlotReference */)) {\n      elm[\"s-sr\"] = true;\n      elm[\"s-cr\"] = contentRef;\n      elm[\"s-sn\"] = newVNode2.$name$ || \"\";\n      elm[\"s-rf\"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {\n        {\n          relocateToHostRoot(oldParentVNode.$elm$);\n        }\n      }\n    }\n  }\n  return elm;\n};\nvar relocateToHostRoot = parentElm => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const host = parentElm.closest(hostTagName.toLowerCase());\n  if (host != null) {\n    const contentRefNode = Array.from(host.childNodes).find(ref => ref[\"s-cr\"]);\n    const childNodeArray = Array.from(parentElm.childNodes);\n    for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {\n      if (childNode[\"s-sh\"] != null) {\n        insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);\n        childNode[\"s-sh\"] = void 0;\n        checkSlotRelocate = true;\n      }\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.childNodes);\n  if (parentElm[\"s-sr\"] && BUILD.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node[\"s-sn\"] === parentElm[\"s-sn\"] && node[\"s-sh\"] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {\n    const childNode = oldSlotChildNodes[i2];\n    if (childNode[\"s-hn\"] !== hostTagName && childNode[\"s-ol\"]) {\n      insertBefore(parentReferenceNode(childNode), childNode, referenceNode(childNode));\n      childNode[\"s-ol\"].remove();\n      childNode[\"s-ol\"] = void 0;\n      childNode[\"s-sh\"] = void 0;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = parentElm[\"s-cr\"] && parentElm[\"s-cr\"].parentNode || parentElm;\n  let childNode;\n  if (containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx, parentElm);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        insertBefore(containerElm, childNode, referenceNode(before));\n      }\n    }\n  }\n};\nvar removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        {\n          checkSlotFallbackVisibility = true;\n          if (elm[\"s-ol\"]) {\n            elm[\"s-ol\"].remove();\n          } else {\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        elm.remove();\n      }\n    }\n  }\n};\nvar updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i2 = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      if (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\") {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      if (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\") {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      idxInOld = -1;\n      {\n        for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {\n          if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {\n            idxInOld = i2;\n            break;\n          }\n        }\n      }\n      if (idxInOld >= 0) {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld, parentElm);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          oldCh[idxInOld] = void 0;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx, parentElm);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        {\n          insertBefore(parentReferenceNode(oldStartVnode.$elm$), node, referenceNode(oldStartVnode.$elm$));\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    addVnodes(parentElm, newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$, newVNode2, newCh, newStartIdx, newEndIdx);\n  } else if (newStartIdx > newEndIdx) {\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\nvar isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (leftVNode.$tag$ === \"slot\") {\n      if (\n      // The component gets hydrated and no VDOM has been initialized.\n      // Here the comparison can't happen as $name$ property is not set for `leftNode`.\n      \"$nodeId$\" in leftVNode && isInitialRender &&\n      // `leftNode` is not from type HTMLComment which would cause many\n      // hydration comments to be removed\n      leftVNode.$elm$.nodeType !== 8) {\n        return false;\n      }\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    if (!isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nvar referenceNode = node => {\n  return node && node[\"s-ol\"] || node;\n};\nvar parentReferenceNode = node => (node[\"s-ol\"] ? node[\"s-ol\"] : node).parentNode;\nvar patch = (oldVNode, newVNode2, isInitialRender = false) => {\n  const elm = newVNode2.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode2.$children$;\n  const tag = newVNode2.$tag$;\n  const text = newVNode2.$text$;\n  let defaultHolder;\n  if (text === null) {\n    {\n      isSvgMode = tag === \"svg\" ? true : tag === \"foreignObject\" ? false : isSvgMode;\n    }\n    {\n      if (tag === \"slot\" && !useNativeShadowDom) {\n        if (oldVNode.$name$ !== newVNode2.$name$) {\n          newVNode2.$elm$[\"s-sn\"] = newVNode2.$name$ || \"\";\n          relocateToHostRoot(newVNode2.$elm$.parentElement);\n        }\n      } else {\n        updateElement(oldVNode, newVNode2, isSvgMode);\n      }\n    }\n    if (oldChildren !== null && newChildren !== null) {\n      updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      if (oldVNode.$text$ !== null) {\n        elm.textContent = \"\";\n      }\n      addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);\n    } else if (\n    // don't do this on initial render as it can cause non-hydrated content to be removed\n    !isInitialRender && BUILD.updatable && oldChildren !== null) {\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (isSvgMode && tag === \"svg\") {\n      isSvgMode = false;\n    }\n  } else if (defaultHolder = elm[\"s-cr\"]) {\n    defaultHolder.parentNode.textContent = text;\n  } else if (oldVNode.$text$ !== text) {\n    elm.data = text;\n  }\n};\nvar updateFallbackSlotVisibility = elm => {\n  const childNodes = elm.childNodes;\n  for (const childNode of childNodes) {\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      if (childNode[\"s-sr\"]) {\n        const slotName = childNode[\"s-sn\"];\n        childNode.hidden = false;\n        for (const siblingNode of childNodes) {\n          if (siblingNode !== childNode) {\n            if (siblingNode[\"s-hn\"] !== childNode[\"s-hn\"] || slotName !== \"\") {\n              if (siblingNode.nodeType === 1 /* ElementNode */ && (slotName === siblingNode.getAttribute(\"slot\") || slotName === siblingNode[\"s-sn\"]) || siblingNode.nodeType === 3 /* TextNode */ && slotName === siblingNode[\"s-sn\"]) {\n                childNode.hidden = true;\n                break;\n              }\n            } else {\n              if (siblingNode.nodeType === 1 /* ElementNode */ || siblingNode.nodeType === 3 /* TextNode */ && siblingNode.textContent.trim() !== \"\") {\n                childNode.hidden = true;\n                break;\n              }\n            }\n          }\n        }\n      }\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\nvar relocateNodes = [];\nvar markSlotContentForRelocation = elm => {\n  let node;\n  let hostContentNodes;\n  let j;\n  for (const childNode of elm.childNodes) {\n    if (childNode[\"s-sr\"] && (node = childNode[\"s-cr\"]) && node.parentNode) {\n      hostContentNodes = node.parentNode.childNodes;\n      const slotName = childNode[\"s-sn\"];\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        if (!node[\"s-cn\"] && !node[\"s-nr\"] && node[\"s-hn\"] !== childNode[\"s-hn\"] && (!node[\"s-sh\"] || node[\"s-sh\"] !== childNode[\"s-hn\"])) {\n          if (isNodeLocatedInSlot(node, slotName)) {\n            let relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n            checkSlotFallbackVisibility = true;\n            node[\"s-sn\"] = node[\"s-sn\"] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node[\"s-sr\"]) {\n              relocateNodes.map(relocateNode => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node[\"s-sn\"])) {\n                  relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some(r => r.$nodeToRelocate$ === node)) {\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\nvar isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n    if (nodeToRelocate.getAttribute(\"slot\") === null && slotName === \"\") {\n      return true;\n    }\n    if (nodeToRelocate.getAttribute(\"slot\") === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate[\"s-sn\"] === slotName) {\n    return true;\n  }\n  return slotName === \"\";\n};\nvar nullifyVNodeRefs = vNode => {\n  {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar insertBefore = (parent, newNode, reference) => {\n  const inserted = parent == null ? void 0 : parent.insertBefore(newNode, reference);\n  {\n    updateElementScopeIds(newNode, parent);\n  }\n  return inserted;\n};\nvar findScopeIds = element => {\n  const scopeIds = [];\n  if (element) {\n    scopeIds.push(...(element[\"s-scs\"] || []), element[\"s-si\"], element[\"s-sc\"], ...findScopeIds(element.parentElement));\n  }\n  return scopeIds;\n};\nvar updateElementScopeIds = (element, parent, iterateChildNodes = false) => {\n  var _a;\n  if (element && parent && element.nodeType === 1 /* ElementNode */) {\n    const scopeIds = new Set(findScopeIds(parent).filter(Boolean));\n    if (scopeIds.size) {\n      (_a = element.classList) == null ? void 0 : _a.add(...(element[\"s-scs\"] = [...scopeIds]));\n      if (element[\"s-ol\"] || iterateChildNodes) {\n        for (const childNode of Array.from(element.childNodes)) {\n          updateElementScopeIds(childNode, element, true);\n        }\n      }\n    }\n  }\n};\nvar renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d, _e;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]);\n  }\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      if (hostElm.hasAttribute(key) && ![\"key\", \"ref\", \"style\", \"class\"].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = hostElm.shadowRoot || hostElm;\n  {\n    scopeId = hostElm[\"s-sc\"];\n  }\n  useNativeShadowDom = (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) !== 0;\n  {\n    contentRef = hostElm[\"s-cr\"];\n    checkSlotFallbackVisibility = false;\n  }\n  patch(oldVNode, rootVnode, isInitialLoad);\n  {\n    plt.$flags$ |= 1 /* isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate[\"s-ol\"]) {\n          const orgLocationNode = doc.createTextNode(\"\");\n          orgLocationNode[\"s-nr\"] = nodeToRelocate;\n          insertBefore(nodeToRelocate.parentNode, nodeToRelocate[\"s-ol\"] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          let insertBeforeNode = slotRefNode.nextSibling;\n          if (insertBeforeNode && insertBeforeNode.nodeType === 1 /* ElementNode */) {\n            let orgLocationNode = (_a = nodeToRelocate[\"s-ol\"]) == null ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode[\"s-nr\"]) != null ? _b : null;\n              if (refNode && refNode[\"s-sn\"] === nodeToRelocate[\"s-sn\"] && parentNodeRef === refNode.parentNode) {\n                refNode = refNode.nextSibling;\n                while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode[\"s-sr\"])) {\n                  refNode = refNode == null ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode[\"s-nr\"]) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          if (!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode || nodeToRelocate.nextSibling !== insertBeforeNode) {\n            if (nodeToRelocate !== insertBeforeNode) {\n              insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);\n              if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n                nodeToRelocate.hidden = (_c = nodeToRelocate[\"s-ih\"]) != null ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode[\"s-rf\"] === \"function\" && slotRefNode[\"s-rf\"](nodeToRelocate);\n        } else {\n          if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n            if (isInitialLoad) {\n              nodeToRelocate[\"s-ih\"] = (_d = nodeToRelocate.hidden) != null ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    plt.$flags$ &= ~1 /* isTmpDisconnected */;\n    relocateNodes.length = 0;\n  }\n  if (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n    for (const childNode of rootVnode.$elm$.childNodes) {\n      if (childNode[\"s-hn\"] !== hostTagName && !childNode[\"s-sh\"]) {\n        if (isInitialLoad && childNode[\"s-ih\"] == null) {\n          childNode[\"s-ih\"] = (_e = childNode.hidden) != null ? _e : false;\n        }\n        childNode.hidden = true;\n      }\n    }\n  }\n  contentRef = void 0;\n};\n\n// src/runtime/update-component.ts\nvar attachToAncestor = (hostRef, ancestorComponent) => {\n  if (ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent[\"s-p\"]) {\n    ancestorComponent[\"s-p\"].push(new Promise(r => hostRef.$onRenderResolve$ = r));\n  }\n};\nvar scheduleUpdate = (hostRef, isInitialLoad) => {\n  {\n    hostRef.$flags$ |= 16 /* isQueuedForUpdate */;\n  }\n  if (hostRef.$flags$ & 4 /* isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return writeTask(dispatch);\n};\nvar dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime(\"scheduleUpdate\", hostRef.$cmpMeta$.$tagName$);\n  const instance = hostRef.$lazyInstance$;\n  if (!instance) {\n    throw new Error(`Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \\`externalRuntime: true\\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);\n  }\n  let maybePromise;\n  if (isInitialLoad) {\n    {\n      hostRef.$flags$ |= 256 /* isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));\n        hostRef.$queuedListeners$ = void 0;\n      }\n    }\n    {\n      maybePromise = safeCall(instance, \"componentWillLoad\");\n    }\n  }\n  {\n    maybePromise = enqueue(maybePromise, () => safeCall(instance, \"componentWillRender\"));\n  }\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\nvar enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch(err2 => {\n  console.error(err2);\n  fn();\n}) : fn();\nvar isPromisey = maybePromise => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === \"function\";\nvar updateComponent = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (hostRef, instance, isInitialLoad) {\n    var _a;\n    const elm = hostRef.$hostElement$;\n    const endUpdate = createTime(\"update\", hostRef.$cmpMeta$.$tagName$);\n    const rc = elm[\"s-rc\"];\n    if (isInitialLoad) {\n      attachStyles(hostRef);\n    }\n    const endRender = createTime(\"render\", hostRef.$cmpMeta$.$tagName$);\n    {\n      callRender(hostRef, instance, elm, isInitialLoad);\n    }\n    if (rc) {\n      rc.map(cb => cb());\n      elm[\"s-rc\"] = void 0;\n    }\n    endRender();\n    endUpdate();\n    {\n      const childrenPromises = (_a = elm[\"s-p\"]) != null ? _a : [];\n      const postUpdate = () => postUpdateComponent(hostRef);\n      if (childrenPromises.length === 0) {\n        postUpdate();\n      } else {\n        Promise.all(childrenPromises).then(postUpdate);\n        hostRef.$flags$ |= 4 /* isWaitingForChildren */;\n        childrenPromises.length = 0;\n      }\n    }\n  });\n  return function updateComponent(_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}();\nvar callRender = (hostRef, instance, elm, isInitialLoad) => {\n  try {\n    instance = instance.render && instance.render();\n    {\n      hostRef.$flags$ &= ~16 /* isQueuedForUpdate */;\n    }\n    {\n      hostRef.$flags$ |= 2 /* hasRendered */;\n    }\n    {\n      {\n        {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  return null;\n};\nvar postUpdateComponent = hostRef => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime(\"postUpdate\", tagName);\n  const instance = hostRef.$lazyInstance$;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  {\n    safeCall(instance, \"componentDidRender\");\n  }\n  if (!(hostRef.$flags$ & 64 /* hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* hasLoadedComponent */;\n    {\n      addHydratedFlag(elm);\n    }\n    {\n      safeCall(instance, \"componentDidLoad\");\n    }\n    endPostUpdate();\n    {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad();\n      }\n    }\n  } else {\n    {\n      safeCall(instance, \"componentDidUpdate\");\n    }\n    endPostUpdate();\n  }\n  {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = void 0;\n    }\n    if (hostRef.$flags$ & 512 /* needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* isWaitingForChildren */ | 512 /* needsRerender */);\n  }\n};\nvar forceUpdate = ref => {\n  {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    return isConnected;\n  }\n};\nvar appDidLoad = who => {\n  {\n    addHydratedFlag(doc.documentElement);\n  }\n  nextTick(() => emitEvent(win, \"appload\", {\n    detail: {\n      namespace: NAMESPACE\n    }\n  }));\n};\nvar safeCall = (instance, method, arg) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  return void 0;\n};\nvar addHydratedFlag = elm => {\n  var _a;\n  return elm.classList.add((_a = BUILD.hydratedSelectorName) != null ? _a : \"hydrated\");\n};\n\n// src/runtime/set-value.ts\nvar getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nvar setValue = (ref, propName, newVal, cmpMeta) => {\n  const hostRef = getHostRef(ref);\n  if (!hostRef) {\n    throw new Error(`Couldn't find host element for \"${cmpMeta.$tagName$}\" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/ionic-team/stencil/issues/5457).`);\n  }\n  const elm = hostRef.$hostElement$;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = hostRef.$lazyInstance$;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!(flags & 8 /* isConstructingInstance */) || oldVal === void 0) && didValueChange) {\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (instance) {\n      if (cmpMeta.$watchers$ && flags & 128 /* isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          watchMethods.map(watchMethodName => {\n            try {\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if ((flags & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n\n// src/runtime/proxy-component.ts\nvar proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a, _b;\n  const prototype = Cstr.prototype;\n  if (cmpMeta.$members$ || cmpMeta.$watchers$ || Cstr.watchers) {\n    if (Cstr.watchers && !cmpMeta.$watchers$) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n    members.map(([memberName, [memberFlags]]) => {\n      if (memberFlags & 31 /* Prop */ || flags & 2 /* proxyState */ && memberFlags & 32 /* State */) {\n        Object.defineProperty(prototype, memberName, {\n          get() {\n            return getValue(this, memberName);\n          },\n          set(newValue) {\n            setValue(this, memberName, newValue, cmpMeta);\n          },\n          configurable: true,\n          enumerable: true\n        });\n      } else if (flags & 1 /* isElementConstructor */ && memberFlags & 64 /* Method */) {\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a2;\n            const ref = getHostRef(this);\n            return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {\n              var _a3;\n              return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if (flags & 1 /* isElementConstructor */) {\n      const attrNameToPropName = /* @__PURE__ */new Map();\n      prototype.attributeChangedCallback = function (attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a2;\n          const propName = attrNameToPropName.get(attrName);\n          if (this.hasOwnProperty(propName)) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === \"number\" &&\n          // cast type to number to avoid TS compiler issues\n          this[propName] == newValue) {\n            return;\n          } else if (propName == null) {\n            const hostRef = getHostRef(this);\n            const flags2 = hostRef == null ? void 0 : hostRef.$flags$;\n            if (flags2 && !(flags2 & 8 /* isConstructingInstance */) && flags2 & 128 /* isWatchReady */ && newValue !== oldValue) {\n              const instance = hostRef.$lazyInstance$;\n              const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];\n              entry == null ? void 0 : entry.forEach(callbackName => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          this[propName] = newValue === null && typeof this[propName] === \"boolean\" ? false : newValue;\n        });\n      };\n      Cstr.observedAttributes = Array.from(/* @__PURE__ */new Set([...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}), ...members.filter(([_, m]) => m[0] & 15 /* HasAttribute */).map(([propName, m]) => {\n        var _a2;\n        const attrName = m[1] || propName;\n        attrNameToPropName.set(attrName, propName);\n        if (m[0] & 512 /* ReflectAttr */) {\n          (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);\n        }\n        return attrName;\n      })]));\n    }\n  }\n  return Cstr;\n};\n\n// src/runtime/initialize-component.ts\nvar initializeComponent = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (elm, hostRef, cmpMeta, hmrVersionId) {\n    let Cstr;\n    if ((hostRef.$flags$ & 32 /* hasInitializedComponent */) === 0) {\n      hostRef.$flags$ |= 32 /* hasInitializedComponent */;\n      const bundleId = cmpMeta.$lazyBundleId$;\n      if (bundleId) {\n        const CstrImport = loadModule(cmpMeta);\n        if (CstrImport && \"then\" in CstrImport) {\n          const endLoad = uniqueTime();\n          Cstr = yield CstrImport;\n          endLoad();\n        } else {\n          Cstr = CstrImport;\n        }\n        if (!Cstr) {\n          throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n        }\n        if (!Cstr.isProxied) {\n          {\n            cmpMeta.$watchers$ = Cstr.watchers;\n          }\n          proxyComponent(Cstr, cmpMeta, 2 /* proxyState */);\n          Cstr.isProxied = true;\n        }\n        const endNewInstance = createTime(\"createInstance\", cmpMeta.$tagName$);\n        {\n          hostRef.$flags$ |= 8 /* isConstructingInstance */;\n        }\n        try {\n          new Cstr(hostRef);\n        } catch (e) {\n          consoleError(e);\n        }\n        {\n          hostRef.$flags$ &= ~8 /* isConstructingInstance */;\n        }\n        {\n          hostRef.$flags$ |= 128 /* isWatchReady */;\n        }\n        endNewInstance();\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else {\n        Cstr = elm.constructor;\n        const cmpTag = elm.localName;\n        customElements.whenDefined(cmpTag).then(() => hostRef.$flags$ |= 128 /* isWatchReady */);\n      }\n      if (Cstr && Cstr.style) {\n        let style;\n        if (typeof Cstr.style === \"string\") {\n          style = Cstr.style;\n        } else if (typeof Cstr.style !== \"string\") {\n          hostRef.$modeName$ = computeMode(elm);\n          if (hostRef.$modeName$) {\n            style = Cstr.style[hostRef.$modeName$];\n          }\n        }\n        const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);\n        if (!styles.has(scopeId2)) {\n          const endRegisterStyles = createTime(\"registerStyles\", cmpMeta.$tagName$);\n          registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */));\n          endRegisterStyles();\n        }\n      }\n    }\n    const ancestorComponent = hostRef.$ancestorComponent$;\n    const schedule = () => scheduleUpdate(hostRef, true);\n    if (ancestorComponent && ancestorComponent[\"s-rc\"]) {\n      ancestorComponent[\"s-rc\"].push(schedule);\n    } else {\n      schedule();\n    }\n  });\n  return function initializeComponent(_x4, _x5, _x6, _x7) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nvar fireConnectedCallback = instance => {\n  {\n    safeCall(instance, \"connectedCallback\");\n  }\n};\n\n// src/runtime/connected-callback.ts\nvar connectedCallback = elm => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime(\"connectedCallback\", cmpMeta.$tagName$);\n    if (!(hostRef.$flags$ & 1 /* hasConnected */)) {\n      hostRef.$flags$ |= 1 /* hasConnected */;\n      let hostId;\n      {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            const scopeId2 = addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute(\"s-mode\"));\n            elm.classList.remove(scopeId2 + \"-h\", scopeId2 + \"-s\");\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (!hostId) {\n        if (\n        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* hasSlotRelocation */ | 8 /* needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      {\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          if (ancestorComponent.nodeType === 1 /* ElementNode */ && ancestorComponent.hasAttribute(\"s-id\") && ancestorComponent[\"s-p\"] || ancestorComponent[\"s-p\"]) {\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      if (cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$);\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));\n      }\n    }\n    endConnected();\n  }\n};\nvar setContentReference = elm => {\n  const contentRefElm = elm[\"s-cr\"] = doc.createComment(\"\");\n  contentRefElm[\"s-cn\"] = true;\n  insertBefore(elm, contentRefElm, elm.firstChild);\n};\nvar disconnectInstance = instance => {\n  {\n    safeCall(instance, \"disconnectedCallback\");\n  }\n};\nvar disconnectedCallback = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (elm) {\n    if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n      const hostRef = getHostRef(elm);\n      {\n        if (hostRef.$rmListeners$) {\n          hostRef.$rmListeners$.map(rmListener => rmListener());\n          hostRef.$rmListeners$ = void 0;\n        }\n      }\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        disconnectInstance(hostRef.$lazyInstance$);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));\n      }\n    }\n  });\n  return function disconnectedCallback(_x8) {\n    return _ref3.apply(this, arguments);\n  };\n}();\nvar patchPseudoShadowDom = (hostElementPrototype, descriptorPrototype) => {\n  patchCloneNode(hostElementPrototype);\n  patchSlotAppendChild(hostElementPrototype);\n  patchSlotAppend(hostElementPrototype);\n  patchSlotPrepend(hostElementPrototype);\n  patchSlotInsertAdjacentElement(hostElementPrototype);\n  patchSlotInsertAdjacentHTML(hostElementPrototype);\n  patchSlotInsertAdjacentText(hostElementPrototype);\n  patchTextContent(hostElementPrototype);\n  patchChildSlotNodes(hostElementPrototype, descriptorPrototype);\n  patchSlotRemoveChild(hostElementPrototype);\n};\nvar patchCloneNode = HostElementPrototype => {\n  const orgCloneNode = HostElementPrototype.cloneNode;\n  HostElementPrototype.cloneNode = function (deep) {\n    const srcNode = this;\n    const isShadowDom = srcNode.shadowRoot && supportsShadow;\n    const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n    if (!isShadowDom && deep) {\n      let i2 = 0;\n      let slotted, nonStencilNode;\n      const stencilPrivates = [\"s-id\", \"s-cr\", \"s-lr\", \"s-rc\", \"s-sc\", \"s-p\", \"s-cn\", \"s-sr\", \"s-sn\", \"s-hn\", \"s-ol\", \"s-nr\", \"s-si\", \"s-rf\", \"s-scs\"];\n      for (; i2 < srcNode.childNodes.length; i2++) {\n        slotted = srcNode.childNodes[i2][\"s-nr\"];\n        nonStencilNode = stencilPrivates.every(privateField => !srcNode.childNodes[i2][privateField]);\n        if (slotted) {\n          if (clonedNode.__appendChild) {\n            clonedNode.__appendChild(slotted.cloneNode(true));\n          } else {\n            clonedNode.appendChild(slotted.cloneNode(true));\n          }\n        }\n        if (nonStencilNode) {\n          clonedNode.appendChild(srcNode.childNodes[i2].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nvar patchSlotAppendChild = HostElementPrototype => {\n  HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n  HostElementPrototype.appendChild = function (newChild) {\n    const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n    const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n    if (slotNode) {\n      const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n      const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n      const insertedNode = insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      updateFallbackSlotVisibility(this);\n      return insertedNode;\n    }\n    return this.__appendChild(newChild);\n  };\n};\nvar patchSlotRemoveChild = ElementPrototype => {\n  ElementPrototype.__removeChild = ElementPrototype.removeChild;\n  ElementPrototype.removeChild = function (toRemove) {\n    if (toRemove && typeof toRemove[\"s-sn\"] !== \"undefined\") {\n      const slotNode = getHostSlotNode(this.childNodes, toRemove[\"s-sn\"], this.tagName);\n      if (slotNode) {\n        const slotChildNodes = getHostSlotChildNodes(slotNode, toRemove[\"s-sn\"]);\n        const existingNode = slotChildNodes.find(n => n === toRemove);\n        if (existingNode) {\n          existingNode.remove();\n          updateFallbackSlotVisibility(this);\n          return;\n        }\n      }\n    }\n    return this.__removeChild(toRemove);\n  };\n};\nvar patchSlotPrepend = HostElementPrototype => {\n  const originalPrepend = HostElementPrototype.prepend;\n  HostElementPrototype.prepend = function (...newChildren) {\n    newChildren.forEach(newChild => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n      const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n      if (slotNode) {\n        const slotPlaceholder = document.createTextNode(\"\");\n        slotPlaceholder[\"s-nr\"] = newChild;\n        slotNode[\"s-cr\"].parentNode.__appendChild(slotPlaceholder);\n        newChild[\"s-ol\"] = slotPlaceholder;\n        const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n        const appendAfter = slotChildNodes[0];\n        return insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      }\n      if (newChild.nodeType === 1 && !!newChild.getAttribute(\"slot\")) {\n        newChild.hidden = true;\n      }\n      return originalPrepend.call(this, newChild);\n    });\n  };\n};\nvar patchSlotAppend = HostElementPrototype => {\n  HostElementPrototype.append = function (...newChildren) {\n    newChildren.forEach(newChild => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      this.appendChild(newChild);\n    });\n  };\n};\nvar patchSlotInsertAdjacentHTML = HostElementPrototype => {\n  const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n  HostElementPrototype.insertAdjacentHTML = function (position, text) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentHtml.call(this, position, text);\n    }\n    const container = this.ownerDocument.createElement(\"_\");\n    let node;\n    container.innerHTML = text;\n    if (position === \"afterbegin\") {\n      while (node = container.firstChild) {\n        this.prepend(node);\n      }\n    } else if (position === \"beforeend\") {\n      while (node = container.firstChild) {\n        this.append(node);\n      }\n    }\n  };\n};\nvar patchSlotInsertAdjacentText = HostElementPrototype => {\n  HostElementPrototype.insertAdjacentText = function (position, text) {\n    this.insertAdjacentHTML(position, text);\n  };\n};\nvar patchSlotInsertAdjacentElement = HostElementPrototype => {\n  const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n  HostElementPrototype.insertAdjacentElement = function (position, element) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentElement.call(this, position, element);\n    }\n    if (position === \"afterbegin\") {\n      this.prepend(element);\n      return element;\n    } else if (position === \"beforeend\") {\n      this.append(element);\n      return element;\n    }\n    return element;\n  };\n};\nvar patchTextContent = hostElementPrototype => {\n  const descriptor = Object.getOwnPropertyDescriptor(Node.prototype, \"textContent\");\n  Object.defineProperty(hostElementPrototype, \"__textContent\", descriptor);\n  {\n    Object.defineProperty(hostElementPrototype, \"textContent\", {\n      // To mimic shadow root behavior, we need to return the text content of all\n      // nodes in a slot reference node\n      get() {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        const textContent = slotRefNodes.map(node => {\n          var _a, _b;\n          const text = [];\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            if (slotContent.nodeType === 3 /* TEXT_NODE */ || slotContent.nodeType === 1 /* ELEMENT_NODE */) {\n              text.push((_b = (_a = slotContent.textContent) == null ? void 0 : _a.trim()) != null ? _b : \"\");\n            }\n            slotContent = slotContent.nextSibling;\n          }\n          return text.filter(ref => ref !== \"\").join(\" \");\n        }).filter(text => text !== \"\").join(\" \");\n        return \" \" + textContent + \" \";\n      },\n      // To mimic shadow root behavior, we need to overwrite all nodes in a slot\n      // reference node. If a default slot reference node exists, the text content will be\n      // placed there. Otherwise, the new text node will be hidden\n      set(value) {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        slotRefNodes.forEach(node => {\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            const tmp = slotContent;\n            slotContent = slotContent.nextSibling;\n            tmp.remove();\n          }\n          if (node[\"s-sn\"] === \"\") {\n            const textNode = this.ownerDocument.createTextNode(value);\n            textNode[\"s-sn\"] = \"\";\n            insertBefore(node.parentElement, textNode, node.nextSibling);\n          } else {\n            node.remove();\n          }\n        });\n      }\n    });\n  }\n};\nvar patchChildSlotNodes = (elm, cmpMeta) => {\n  class FakeNodeList extends Array {\n    item(n) {\n      return this[n];\n    }\n  }\n  if (cmpMeta.$flags$ & 8 /* needsShadowDomShim */) {\n    const childNodesFn = elm.__lookupGetter__(\"childNodes\");\n    Object.defineProperty(elm, \"children\", {\n      get() {\n        return this.childNodes.map(n => n.nodeType === 1);\n      }\n    });\n    Object.defineProperty(elm, \"childElementCount\", {\n      get() {\n        return elm.children.length;\n      }\n    });\n    Object.defineProperty(elm, \"childNodes\", {\n      get() {\n        const childNodes = childNodesFn.call(this);\n        if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0 && getHostRef(this).$flags$ & 2 /* hasRendered */) {\n          const result = new FakeNodeList();\n          for (let i2 = 0; i2 < childNodes.length; i2++) {\n            const slot = childNodes[i2][\"s-nr\"];\n            if (slot) {\n              result.push(slot);\n            }\n          }\n          return result;\n        }\n        return FakeNodeList.from(childNodes);\n      }\n    });\n  }\n};\nvar getAllChildSlotNodes = childNodes => {\n  const slotRefNodes = [];\n  for (const childNode of Array.from(childNodes)) {\n    if (childNode[\"s-sr\"]) {\n      slotRefNodes.push(childNode);\n    }\n    slotRefNodes.push(...getAllChildSlotNodes(childNode.childNodes));\n  }\n  return slotRefNodes;\n};\nvar getSlotName = node => node[\"s-sn\"] || node.nodeType === 1 && node.getAttribute(\"slot\") || \"\";\nvar getHostSlotNode = (childNodes, slotName, hostName) => {\n  let i2 = 0;\n  let childNode;\n  for (; i2 < childNodes.length; i2++) {\n    childNode = childNodes[i2];\n    if (childNode[\"s-sr\"] && childNode[\"s-sn\"] === slotName && childNode[\"s-hn\"] === hostName) {\n      return childNode;\n    }\n    childNode = getHostSlotNode(childNode.childNodes, slotName, hostName);\n    if (childNode) {\n      return childNode;\n    }\n  }\n  return null;\n};\nvar getHostSlotChildNodes = (n, slotName) => {\n  const childNodes = [n];\n  while ((n = n.nextSibling) && n[\"s-sn\"] === slotName) {\n    childNodes.push(n);\n  }\n  return childNodes;\n};\n\n// src/runtime/bootstrap-lazy.ts\nvar bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  const endBootstrap = createTime();\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements2 = win.customElements;\n  const head = doc.head;\n  const metaCharset = /* @__PURE__ */head.querySelector(\"meta[charset]\");\n  const dataStyles = /* @__PURE__ */doc.createElement(\"style\");\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || \"./\", doc.baseURI).href;\n  {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map(lazyBundle => {\n    lazyBundle[1].map(compactMeta => {\n      var _a2;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      {\n        cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};\n      }\n      const tagName = cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          super(self);\n          this.hasRegisteredEventListeners = false;\n          self = this;\n          registerHost(self, cmpMeta);\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            {\n              if (!self.shadowRoot) {\n                {\n                  self.attachShadow({\n                    mode: \"open\",\n                    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n                  });\n                }\n              } else {\n                if (self.shadowRoot.mode !== \"open\") {\n                  throw new Error(`Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`);\n                }\n              }\n            }\n          }\n        }\n        connectedCallback() {\n          const hostRef = getHostRef(this);\n          if (!this.hasRegisteredEventListeners) {\n            this.hasRegisteredEventListeners = true;\n            addHostEventListeners(this, hostRef, cmpMeta.$listeners$);\n          }\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      {\n        if (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchPseudoShadowDom(HostElement.prototype, cmpMeta);\n        }\n      }\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements2.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements2.define(tagName, proxyComponent(HostElement, cmpMeta, 1 /* isElementConstructor */));\n      }\n    });\n  });\n  if (cmpTags.length > 0) {\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    {\n      dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;\n    }\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute(\"data-styles\", \"\");\n      const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n      if (nonce != null) {\n        dataStyles.setAttribute(\"nonce\", nonce);\n      }\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map(host => host.connectedCallback());\n  } else {\n    {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  endBootstrap();\n};\nvar addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (listeners) {\n    listeners.map(([flags, name, method]) => {\n      const target = getHostListenerTarget(elm, flags);\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nvar hostListenerProxy = (hostRef, methodName) => ev => {\n  var _a;\n  try {\n    {\n      if (hostRef.$flags$ & 256 /* isListenReady */) {\n        (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    }\n  } catch (e) {\n    consoleError(e);\n  }\n};\nvar getHostListenerTarget = (elm, flags) => {\n  if (flags & 4 /* TargetDocument */) return doc;\n  if (flags & 8 /* TargetWindow */) return win;\n  if (flags & 16 /* TargetBody */) return doc.body;\n  return elm;\n};\nvar hostListenerOpts = flags => supportsListenerOptions ? {\n  passive: (flags & 1 /* Passive */) !== 0,\n  capture: (flags & 2 /* Capture */) !== 0\n} : (flags & 2 /* Capture */) !== 0;\n\n// src/runtime/nonce.ts\nvar setNonce = nonce => plt.$nonce$ = nonce;\nexport { Build as B, H, setPlatformHelpers as a, bootstrapLazy as b, setMode as c, createEvent as d, readTask as e, Host as f, getMode as g, h, getElement as i, forceUpdate as j, getAssetPath as k, promiseResolve as p, registerInstance as r, setNonce as s, writeTask as w };", "map": {"version": 3, "names": ["NAMESPACE", "BUILD", "allRenderFn", "appendChildSlotFix", "asyncLoading", "asyncQueue", "attachStyles", "cloneNodeFix", "cmpDidLoad", "cmpDidRender", "cmpDidUnload", "cmpDidUpdate", "cmpShouldUpdate", "cmpWillLoad", "cmpWillRender", "cmpWillUpdate", "connectedCallback", "constructableCSS", "cssAnnotations", "devTools", "disconnectedCallback", "element", "event", "experimentalScopedSlotChanges", "experimentalSlotFixes", "formAssociated", "hasRenderFn", "hostListener", "hostListenerTarget", "hostListenerTargetBody", "hostListenerTargetDocument", "hostListenerTargetParent", "hostListenerTargetWindow", "hotModuleReplacement", "hydrateClientSide", "hydrateServerSide", "hydratedAttribute", "hydratedClass", "hydratedSelectorName", "initializeNextTick", "invisiblePrehydration", "isDebug", "isDev", "isTesting", "lazyLoad", "lifecycle", "lifecycleDOMEvents", "member", "method", "mode", "observeAttribute", "profile", "prop", "propBoolean", "propMutable", "propNumber", "propString", "reflect", "scoped", "scopedSlotTextContentFix", "scriptDataOpts", "shadowDelegatesFocus", "shadowDom", "slot", "slotChildNodesFix", "slotRelocation", "state", "style", "svg", "taskQueue", "transformTagName", "updatable", "vdomAttribute", "vdomClass", "vdomFunctional", "vdomKey", "vdomListener", "vdomPropOrAttr", "vdomRef", "v<PERSON><PERSON><PERSON>", "vdomStyle", "vdomText", "vdomXlink", "watchCallback", "__defProp", "Object", "defineProperty", "__export", "target", "all", "name", "get", "enumerable", "Build", "<PERSON><PERSON><PERSON><PERSON>", "isServer", "hostRefs", "WeakMap", "getHostRef", "ref", "registerInstance", "lazyInstance", "hostRef", "set", "$lazyInstance$", "registerHost", "hostElement", "cmpMeta", "$flags$", "$hostElement$", "$cmpMeta$", "$instanceValues$", "Map", "$onInstancePromise$", "Promise", "r", "$onInstanceResolve$", "$onReadyPromise$", "$onReadyResolve$", "isMemberInElement", "elm", "memberName", "consoleError", "e", "el", "console", "error", "cmpModules", "loadModule", "hmrVersionId", "exportName", "$tagName$", "replace", "bundleId", "$lazyBundleId$", "module", "then", "importedModule", "styles", "modeResolutionChain", "CONTENT_REF_ID", "ORG_LOCATION_ID", "SLOT_NODE_ID", "TEXT_NODE_ID", "HYDRATE_ID", "HYDRATED_STYLE_ID", "HYDRATE_CHILD_ID", "HYDRATED_CSS", "SLOT_FB_CSS", "XLINK_NS", "win", "window", "doc", "document", "head", "H", "HTMLElement", "plt", "$resourcesUrl$", "jmp", "h2", "raf", "requestAnimationFrame", "ael", "eventName", "listener", "opts", "addEventListener", "rel", "removeEventListener", "ce", "CustomEvent", "setPlatformHelpers", "helpers", "assign", "supportsShadow", "supportsListenerOptions", "supportsListenerOptions2", "promiseResolve", "v", "resolve", "supportsConstructableStylesheets", "CSSStyleSheet", "replaceSync", "queuePending", "queueDomReads", "queueDomWrites", "queueTask", "queue", "write", "cb", "push", "nextTick", "flush", "consume", "i2", "length", "performance", "now", "readTask", "writeTask", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path", "assetUrl", "URL", "origin", "location", "href", "pathname", "EMPTY_OBJ", "SVG_NS", "HTML_NS", "isDef", "isComplexType", "o", "queryNonceMetaTagContent", "doc2", "_a", "_b", "_c", "querySelector", "getAttribute", "result_exports", "err", "map", "ok", "unwrap", "unwrapErr", "value", "isOk", "isErr", "result", "fn", "val", "newVal", "createTime", "fnName", "tagName", "uniqueTime", "key", "measureText", "h", "nodeName", "vnodeData", "children", "child", "slotName", "simple", "lastSimple", "vNodeChildren", "walk", "c", "Array", "isArray", "String", "$text$", "newVNode", "classData", "className", "class", "keys", "filter", "k", "join", "vdomFnUtils", "vnode", "$attrs$", "$children$", "$key$", "$name$", "tag", "text", "$tag$", "$elm$", "Host", "isHost", "node", "for<PERSON>ach", "convertToPublic", "convertToPrivate", "vattrs", "vchildren", "vkey", "vname", "vtag", "vtext", "initializeClientHydrate", "hostElm", "hostId", "endHydrate", "shadowRoot", "childRenderNodes", "slotNodes", "shadowRootNodes", "$vnode$", "$orgLocNodes$", "initializeDocumentHydrate", "body", "removeAttribute", "clientHydrate", "orgLocationId", "$hostId$", "$nodeId$", "orgLocationNode", "parentNode", "insertBefore", "nextS<PERSON>ling", "delete", "shadowRootNode", "append<PERSON><PERSON><PERSON>", "parentVNode", "childNodeType", "childIdSplt", "childVNode", "nodeType", "split", "$depth$", "$index$", "toLowerCase", "childNodes", "nodeValue", "textContent", "remove", "createElement", "setAttribute", "orgLocNodes", "computeMode", "find", "m", "setMode", "handler", "getMode", "$modeName$", "parsePropertyValue", "propValue", "propType", "parseFloat", "getElement", "createEvent", "flags", "emit", "detail", "emitEvent", "bubbles", "composed", "cancelable", "ev", "dispatchEvent", "rootAppliedStyles", "registerStyle", "scopeId2", "cssText", "allowCS", "addStyle", "styleContainerNode", "getScopeId", "appliedStyles", "styleElm", "Set", "has", "host", "innerHTML", "nonce", "$nonce$", "injectStyle", "add", "adoptedStyleSheets", "includes", "endAttachStyles", "getRootNode", "classList", "cmp", "setAccessor", "oldValue", "newValue", "isSvg", "isProp", "ln", "oldClasses", "parseClassList", "newClasses", "removeProperty", "setProperty", "slice", "capture", "endsWith", "CAPTURE_EVENT_SUFFIX", "CAPTURE_EVENT_REGEX", "isComplex", "n", "xlink", "removeAttributeNS", "setAttributeNS", "parseClassListRegex", "RegExp", "updateElement", "oldVnode", "newVnode", "isSvgMode2", "oldVnodeAttrs", "newVnodeAttrs", "sortedAttrNames", "attrNames", "attr", "scopeId", "contentRef", "hostTagName", "useNativeShadowDom", "checkSlotFallbackVisibility", "checkSlotRelocate", "isSvgMode", "createElm", "oldParentVNode", "newParentVNode", "childIndex", "parentElm", "newVNode2", "childNode", "oldVNode", "createTextNode", "createElementNS", "rootNode", "isElementWithinShadowRoot", "updateElementScopeIds", "relocateToHostRoot", "closest", "contentRefNode", "from", "child<PERSON>odeA<PERSON>y", "reverse", "putBackInOriginalLocation", "recursive", "oldSlotChildNodes", "parentReferenceNode", "referenceNode", "addVnodes", "before", "vnodes", "startIdx", "endIdx", "containerElm", "removeVnodes", "index", "nullifyVNodeRefs", "update<PERSON><PERSON><PERSON>n", "oldCh", "newCh", "isInitialRender", "oldStartIdx", "newStartIdx", "idxInOld", "oldEndIdx", "oldStartVnode", "oldEndVnode", "newEndIdx", "newStartVnode", "newEndVnode", "elmToMove", "isSameVnode", "patch", "leftVNode", "rightVNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultHolder", "parentElement", "data", "updateFallbackSlotVisibility", "hidden", "siblingNode", "trim", "relocateNodes", "markSlotContentForRelocation", "hostContentNodes", "j", "isNodeLocatedInSlot", "relocateNodeData", "$nodeToRelocate$", "$slotRefNode$", "relocateNode", "some", "nodeToRelocate", "vNode", "parent", "newNode", "reference", "inserted", "findScopeIds", "scopeIds", "iterateChildNodes", "Boolean", "size", "renderVdom", "renderFnResults", "isInitialLoad", "_d", "_e", "rootVnode", "$attrsToReflect$", "propName", "attribute", "hasAttribute", "relocateData", "slotRefNode", "parentNodeRef", "insertBeforeNode", "previousSibling", "refNode", "attachToAncestor", "ancestorComponent", "$onRenderResolve$", "scheduleUpdate", "$ancestorComponent$", "dispatch", "dispatchHooks", "endSchedule", "instance", "Error", "<PERSON><PERSON><PERSON><PERSON>", "$queuedListeners$", "methodName", "safeCall", "enqueue", "updateComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catch", "err2", "_ref", "_asyncToGenerator", "endUpdate", "rc", "endRender", "callRender", "childrenPromises", "postUpdate", "postUpdateComponent", "_x", "_x2", "_x3", "apply", "arguments", "render", "endPostUpdate", "addHydratedFlag", "appDidLoad", "forceUpdate", "isConnected", "who", "documentElement", "namespace", "arg", "getValue", "setValue", "oldVal", "$members$", "areBothNaN", "Number", "isNaN", "didValueChange", "$watchers$", "watchMethods", "watchMethodName", "proxyComponent", "Cstr", "prototype", "watchers", "members", "entries", "memberFlags", "configurable", "args", "_a2", "_a3", "attrNameToPropName", "attributeChangedCallback", "attrName", "hasOwnProperty", "flags2", "entry", "callback<PERSON><PERSON>", "call", "observedAttributes", "_", "initializeComponent", "_ref2", "CstrImport", "endLoad", "isProxied", "endNewInstance", "fireConnectedCallback", "constructor", "cmpTag", "localName", "customElements", "whenDefined", "endRegisterStyles", "schedule", "_x4", "_x5", "_x6", "_x7", "endConnected", "setContentReference", "addHostEventListeners", "$listeners$", "contentRefElm", "createComment", "<PERSON><PERSON><PERSON><PERSON>", "disconnectInstance", "_ref3", "$rmListeners$", "rmListener", "_x8", "patchPseudoShadowDom", "hostElementPrototype", "descriptorPrototype", "patchCloneNode", "patchSlotAppendChild", "patchSlotAppend", "patchSlotPrepend", "patchSlotInsertAdjacentElement", "patchSlotInsertAdjacentHTML", "patchSlotInsertAdjacentText", "patchTextContent", "patchChildSlotNodes", "patchSlotRemoveChild", "HostElementPrototype", "orgCloneNode", "cloneNode", "deep", "srcNode", "isShadowDom", "clonedNode", "slotted", "nonStencilNode", "stencilPrivates", "every", "privateField", "__append<PERSON><PERSON>d", "<PERSON><PERSON><PERSON><PERSON>", "getSlotName", "slotNode", "getHostSlotNode", "slotChildNodes", "getHostSlotChildNodes", "appendAfter", "insertedNode", "ElementPrototype", "__remove<PERSON><PERSON>d", "<PERSON><PERSON><PERSON><PERSON>", "toRemove", "existingNode", "originalPrepend", "prepend", "ownerDocument", "slotPlaceholder", "append", "originalInsertAdjacentHtml", "insertAdjacentHTML", "position", "container", "insertAdjacentText", "originalInsertAdjacentElement", "insertAdjacentElement", "descriptor", "getOwnPropertyDescriptor", "Node", "slotRefNodes", "getAllChildSlotNodes", "slotContent", "tmp", "textNode", "FakeNodeList", "item", "childNodesFn", "__lookupGetter__", "hostName", "bootstrapLazy", "lazyB<PERSON>les", "options", "endBootstrap", "cmpTags", "exclude", "customElements2", "metaCharset", "dataStyles", "deferredConnectedCallbacks", "appLoadFallback", "isBootstrapping", "resourcesUrl", "baseURI", "hasSlotRelocation", "lazyBundle", "compactMeta", "HostElement", "self", "hasRegisteredEventListeners", "attachShadow", "delegatesFocus", "clearTimeout", "componentOnReady", "define", "sort", "setTimeout", "listeners", "attachParentListeners", "getHostListenerTarget", "hostListenerProxy", "hostListenerOpts", "passive", "setNonce", "B", "a", "b", "d", "f", "g", "i", "p", "s", "w"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/index-28849c61.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst NAMESPACE = 'ionic';\nconst BUILD = /* ionic */ { allRenderFn: false, appendChildSlotFix: true, asyncLoading: true, asyncQueue: false, attachStyles: true, cloneNodeFix: true, cmpDidLoad: true, cmpDidRender: true, cmpDidUnload: false, cmpDidUpdate: true, cmpShouldUpdate: false, cmpWillLoad: true, cmpWillRender: true, cmpWillUpdate: false, connectedCallback: true, constructableCSS: true, cssAnnotations: true, devTools: false, disconnectedCallback: true, element: false, event: true, experimentalScopedSlotChanges: true, experimentalSlotFixes: true, formAssociated: false, hasRenderFn: true, hostListener: true, hostListenerTarget: true, hostListenerTargetBody: true, hostListenerTargetDocument: true, hostListenerTargetParent: false, hostListenerTargetWindow: true, hotModuleReplacement: false, hydrateClientSide: true, hydrateServerSide: false, hydratedAttribute: false, hydratedClass: true, hydratedSelectorName: \"hydrated\", initializeNextTick: false, invisiblePrehydration: true, isDebug: false, isDev: false, isTesting: false, lazyLoad: true, lifecycle: true, lifecycleDOMEvents: false, member: true, method: true, mode: true, observeAttribute: true, profile: false, prop: true, propBoolean: true, propMutable: true, propNumber: true, propString: true, reflect: true, scoped: true, scopedSlotTextContentFix: true, scriptDataOpts: false, shadowDelegatesFocus: true, shadowDom: true, slot: true, slotChildNodesFix: true, slotRelocation: true, state: true, style: true, svg: true, taskQueue: true, transformTagName: false, updatable: true, vdomAttribute: true, vdomClass: true, vdomFunctional: true, vdomKey: true, vdomListener: true, vdomPropOrAttr: true, vdomRef: true, vdomRender: true, vdomStyle: true, vdomText: true, vdomXlink: true, watchCallback: true };\n\n/*\n Stencil Client Platform v4.20.0 | MIT Licensed | https://stenciljs.com\n */\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar Build = {\n  isDev: false,\n  isBrowser: true,\n  isServer: false,\n  isTesting: false\n};\nvar hostRefs = /* @__PURE__ */ new WeakMap();\nvar getHostRef = (ref) => hostRefs.get(ref);\nvar registerInstance = (lazyInstance, hostRef) => hostRefs.set(hostRef.$lazyInstance$ = lazyInstance, hostRef);\nvar registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: /* @__PURE__ */ new Map()\n  };\n  {\n    hostRef.$onInstancePromise$ = new Promise((r) => hostRef.$onInstanceResolve$ = r);\n  }\n  {\n    hostRef.$onReadyPromise$ = new Promise((r) => hostRef.$onReadyResolve$ = r);\n    hostElement[\"s-p\"] = [];\n    hostElement[\"s-rc\"] = [];\n  }\n  return hostRefs.set(hostElement, hostRef);\n};\nvar isMemberInElement = (elm, memberName) => memberName in elm;\nvar consoleError = (e, el) => (0, console.error)(e, el);\n\n// src/client/client-load-module.ts\nvar cmpModules = /* @__PURE__ */ new Map();\nvar loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  const exportName = cmpMeta.$tagName$.replace(/-/g, \"_\");\n  const bundleId = cmpMeta.$lazyBundleId$;\n  if (!bundleId) {\n    return void 0;\n  }\n  const module = cmpModules.get(bundleId) ;\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(\n    /* @vite-ignore */\n    /* webpackInclude: /\\.entry\\.js$/ */\n    /* webpackExclude: /\\.system\\.entry\\.js$/ */\n    /* webpackMode: \"lazy\" */\n    `./${bundleId}.entry.js${\"\"}`\n  ).then((importedModule) => {\n    {\n      cmpModules.set(bundleId, importedModule);\n    }\n    return importedModule[exportName];\n  }, consoleError);\n};\n\n// src/client/client-style.ts\nvar styles = /* @__PURE__ */ new Map();\nvar modeResolutionChain = [];\n\n// src/runtime/runtime-constants.ts\nvar CONTENT_REF_ID = \"r\";\nvar ORG_LOCATION_ID = \"o\";\nvar SLOT_NODE_ID = \"s\";\nvar TEXT_NODE_ID = \"t\";\nvar HYDRATE_ID = \"s-id\";\nvar HYDRATED_STYLE_ID = \"sty-id\";\nvar HYDRATE_CHILD_ID = \"c-id\";\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar SLOT_FB_CSS = \"slot-fb{display:contents}slot-fb[hidden]{display:none}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar win = typeof window !== \"undefined\" ? window : {};\nvar doc = win.document || { head: {} };\nvar H = win.HTMLElement || class {\n};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: (h2) => h2(),\n  raf: (h2) => requestAnimationFrame(h2),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nvar setPlatformHelpers = (helpers) => {\n  Object.assign(plt, helpers);\n};\nvar supportsShadow = BUILD.shadowDom;\nvar supportsListenerOptions = /* @__PURE__ */ (() => {\n  let supportsListenerOptions2 = false;\n  try {\n    doc.addEventListener(\n      \"e\",\n      null,\n      Object.defineProperty({}, \"passive\", {\n        get() {\n          supportsListenerOptions2 = true;\n        }\n      })\n    );\n  } catch (e) {\n  }\n  return supportsListenerOptions2;\n})();\nvar promiseResolve = (v) => Promise.resolve(v);\nvar supportsConstructableStylesheets = /* @__PURE__ */ (() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {\n  }\n  return false;\n})() ;\nvar queuePending = false;\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueTask = (queue, write) => (cb) => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nvar consume = (queue) => {\n  for (let i2 = 0; i2 < queue.length; i2++) {\n    try {\n      queue[i2](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nvar flush = () => {\n  consume(queueDomReads);\n  {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = (cb) => promiseResolve().then(cb);\nvar readTask = /* @__PURE__ */ queueTask(queueDomReads, false);\nvar writeTask = /* @__PURE__ */ queueTask(queueDomWrites, true);\n\n// src/runtime/asset-path.ts\nvar getAssetPath = (path) => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\n\n// src/utils/constants.ts\nvar EMPTY_OBJ = {};\nvar SVG_NS = \"http://www.w3.org/2000/svg\";\nvar HTML_NS = \"http://www.w3.org/1999/xhtml\";\n\n// src/utils/helpers.ts\nvar isDef = (v) => v != null;\nvar isComplexType = (o) => {\n  o = typeof o;\n  return o === \"object\" || o === \"function\";\n};\n\n// src/utils/query-nonce-meta-tag-content.ts\nfunction queryNonceMetaTagContent(doc2) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc2.head) == null ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) == null ? void 0 : _b.getAttribute(\"content\")) != null ? _c : void 0;\n}\n\n// src/utils/result.ts\nvar result_exports = {};\n__export(result_exports, {\n  err: () => err,\n  map: () => map,\n  ok: () => ok,\n  unwrap: () => unwrap,\n  unwrapErr: () => unwrapErr\n});\nvar ok = (value) => ({\n  isOk: true,\n  isErr: false,\n  value\n});\nvar err = (value) => ({\n  isOk: false,\n  isErr: true,\n  value\n});\nfunction map(result, fn) {\n  if (result.isOk) {\n    const val = fn(result.value);\n    if (val instanceof Promise) {\n      return val.then((newVal) => ok(newVal));\n    } else {\n      return ok(val);\n    }\n  }\n  if (result.isErr) {\n    const value = result.value;\n    return err(value);\n  }\n  throw \"should never get here\";\n}\nvar unwrap = (result) => {\n  if (result.isOk) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar unwrapErr = (result) => {\n  if (result.isErr) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar createTime = (fnName, tagName = \"\") => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nvar uniqueTime = (key, measureText) => {\n  {\n    return () => {\n      return;\n    };\n  }\n};\nvar h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = (c) => {\n    for (let i2 = 0; i2 < c.length; i2++) {\n      child = c[i2];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== \"boolean\") {\n        if (simple = typeof nodeName !== \"function\" && !isComplexType(child)) {\n          child = String(child);\n        }\n        if (simple && lastSimple) {\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== \"object\" ? classData : Object.keys(classData).filter((k) => classData[k]).join(\" \");\n      }\n    }\n  }\n  if (typeof nodeName === \"function\") {\n    return nodeName(\n      vnodeData === null ? {} : vnodeData,\n      vNodeChildren,\n      vdomFnUtils\n    );\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  {\n    vnode.$key$ = key;\n  }\n  {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\nvar newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  {\n    vnode.$attrs$ = null;\n  }\n  {\n    vnode.$key$ = null;\n  }\n  {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nvar Host = {};\nvar isHost = (node) => node && node.$tag$ === Host;\nvar vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\nvar convertToPublic = (node) => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\nvar convertToPrivate = (node) => {\n  if (typeof node.vtag === \"function\") {\n    const vnodeData = { ...node.vattrs };\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...node.vchildren || []);\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\n\n// src/runtime/client-hydrate.ts\nvar initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  const endHydrate = createTime(\"hydrateClient\", tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const shadowRootNodes = shadowRoot ? [] : null;\n  const vnode = hostRef.$vnode$ = newVNode(tagName, null);\n  if (!plt.$orgLocNodes$) {\n    initializeDocumentHydrate(doc.body, plt.$orgLocNodes$ = /* @__PURE__ */ new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId);\n  childRenderNodes.map((c) => {\n    const orgLocationId = c.$hostId$ + \".\" + c.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = c.$elm$;\n    if (orgLocationNode && supportsShadow && orgLocationNode[\"s-en\"] === \"\") {\n      orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n    }\n    if (!shadowRoot) {\n      node[\"s-hn\"] = tagName;\n      if (orgLocationNode) {\n        node[\"s-ol\"] = orgLocationNode;\n        node[\"s-ol\"][\"s-nr\"] = node;\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  });\n  if (shadowRoot) {\n    shadowRootNodes.map((shadowRootNode) => {\n      if (shadowRootNode) {\n        shadowRoot.appendChild(shadowRootNode);\n      }\n    });\n  }\n  endHydrate();\n};\nvar clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i2;\n  if (node.nodeType === 1 /* ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      childIdSplt = childNodeType.split(\".\");\n      if (childIdSplt[0] === hostId || childIdSplt[0] === \"0\") {\n        childVNode = {\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          $attrs$: null,\n          $children$: null,\n          $key$: null,\n          $name$: null,\n          $text$: null\n        };\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        parentVNode.$children$[childVNode.$index$] = childVNode;\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    if (node.shadowRoot) {\n      for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {\n        clientHydrate(\n          parentVNode,\n          childRenderNodes,\n          slotNodes,\n          shadowRootNodes,\n          hostElm,\n          node.shadowRoot.childNodes[i2],\n          hostId\n        );\n      }\n    }\n    for (i2 = node.childNodes.length - 1; i2 >= 0; i2--) {\n      clientHydrate(\n        parentVNode,\n        childRenderNodes,\n        slotNodes,\n        shadowRootNodes,\n        hostElm,\n        node.childNodes[i2],\n        hostId\n      );\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[1] === hostId || childIdSplt[1] === \"0\") {\n      childNodeType = childIdSplt[0];\n      childVNode = {\n        $flags$: 0,\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4],\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      };\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = node.nextSibling;\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          node.remove();\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n          if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        if (childNodeType === SLOT_NODE_ID) {\n          childVNode.$tag$ = \"slot\";\n          if (childIdSplt[5]) {\n            node[\"s-sn\"] = childVNode.$name$ = childIdSplt[5];\n          } else {\n            node[\"s-sn\"] = \"\";\n          }\n          node[\"s-sr\"] = true;\n          if (shadowRootNodes) {\n            childVNode.$elm$ = doc.createElement(childVNode.$tag$);\n            if (childVNode.$name$) {\n              childVNode.$elm$.setAttribute(\"name\", childVNode.$name$);\n            }\n            node.parentNode.insertBefore(childVNode.$elm$, node);\n            node.remove();\n            if (childVNode.$depth$ === \"0\") {\n              shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n            }\n          }\n          slotNodes.push(childVNode);\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        } else if (childNodeType === CONTENT_REF_ID) {\n          if (shadowRootNodes) {\n            node.remove();\n          } else {\n            hostElm[\"s-cr\"] = node;\n            node[\"s-cn\"] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === \"style\") {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = \"0\";\n    parentVNode.$children$ = [vnode];\n  }\n};\nvar initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* ElementNode */) {\n    let i2 = 0;\n    if (node.shadowRoot) {\n      for (; i2 < node.shadowRoot.childNodes.length; i2++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);\n      }\n    }\n    for (i2 = 0; i2 < node.childNodes.length; i2++) {\n      initializeDocumentHydrate(node.childNodes[i2], orgLocNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    const childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + \".\" + childIdSplt[2], node);\n      node.nodeValue = \"\";\n      node[\"s-en\"] = childIdSplt[3];\n    }\n  }\n};\n\n// src/runtime/mode.ts\nvar computeMode = (elm) => modeResolutionChain.map((h2) => h2(elm)).find((m) => !!m);\nvar setMode = (handler) => modeResolutionChain.push(handler);\nvar getMode = (ref) => getHostRef(ref).$modeName$;\nvar parsePropertyValue = (propValue, propType) => {\n  if (propValue != null && !isComplexType(propValue)) {\n    if (propType & 4 /* Boolean */) {\n      return propValue === \"false\" ? false : propValue === \"\" || !!propValue;\n    }\n    if (propType & 2 /* Number */) {\n      return parseFloat(propValue);\n    }\n    if (propType & 1 /* String */) {\n      return String(propValue);\n    }\n    return propValue;\n  }\n  return propValue;\n};\nvar getElement = (ref) => getHostRef(ref).$hostElement$ ;\n\n// src/runtime/event-emitter.ts\nvar createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: (detail) => {\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* Bubbles */),\n        composed: !!(flags & 2 /* Composed */),\n        cancelable: !!(flags & 1 /* Cancellable */),\n        detail\n      });\n    }\n  };\n};\nvar emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\nvar rootAppliedStyles = /* @__PURE__ */ new WeakMap();\nvar registerStyle = (scopeId2, cssText, allowCS) => {\n  let style = styles.get(scopeId2);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === \"string\") {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId2, style);\n};\nvar addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId2 = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId2);\n  styleContainerNode = styleContainerNode.nodeType === 11 /* DocumentFragment */ ? styleContainerNode : doc;\n  if (style) {\n    if (typeof style === \"string\") {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */ new Set());\n      }\n      if (!appliedStyles.has(scopeId2)) {\n        if (styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`))) {\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = doc.createElement(\"style\");\n          styleElm.innerHTML = style;\n          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n          if (nonce != null) {\n            styleElm.setAttribute(\"nonce\", nonce);\n          }\n          const injectStyle = (\n            /**\n             * we render a scoped component\n             */\n            !(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) || /**\n             * we are using shadow dom and render the style tag within the shadowRoot\n             */\n            cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */ && styleContainerNode.nodeName !== \"HEAD\"\n          );\n          if (injectStyle) {\n            styleContainerNode.insertBefore(styleElm, styleContainerNode.querySelector(\"link\"));\n          }\n        }\n        if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId2);\n        }\n      }\n    } else if (!styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId2;\n};\nvar attachStyles = (hostRef) => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime(\"attachStyles\", cmpMeta.$tagName$);\n  const scopeId2 = addStyle(\n    elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(),\n    cmpMeta,\n    hostRef.$modeName$\n  );\n  if (flags & 10 /* needsScopedEncapsulation */ && flags & 2 /* scopedCssEncapsulation */) {\n    elm[\"s-sc\"] = scopeId2;\n    elm.classList.add(scopeId2 + \"-h\");\n    if (flags & 2 /* scopedCssEncapsulation */) {\n      elm.classList.add(scopeId2 + \"-s\");\n    }\n  }\n  endAttachStyles();\n};\nvar getScopeId = (cmp, mode) => \"sc-\" + (mode && cmp.$flags$ & 32 /* hasMode */ ? cmp.$tagName$ + \"-\" + mode : cmp.$tagName$);\nvar setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {\n  if (oldValue !== newValue) {\n    let isProp = isMemberInElement(elm, memberName);\n    let ln = memberName.toLowerCase();\n    if (memberName === \"class\") {\n      const classList = elm.classList;\n      const oldClasses = parseClassList(oldValue);\n      const newClasses = parseClassList(newValue);\n      classList.remove(...oldClasses.filter((c) => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter((c) => c && !oldClasses.includes(c)));\n    } else if (memberName === \"style\") {\n      {\n        for (const prop in oldValue) {\n          if (!newValue || newValue[prop] == null) {\n            if (prop.includes(\"-\")) {\n              elm.style.removeProperty(prop);\n            } else {\n              elm.style[prop] = \"\";\n            }\n          }\n        }\n      }\n      for (const prop in newValue) {\n        if (!oldValue || newValue[prop] !== oldValue[prop]) {\n          if (prop.includes(\"-\")) {\n            elm.style.setProperty(prop, newValue[prop]);\n          } else {\n            elm.style[prop] = newValue[prop];\n          }\n        }\n      }\n    } else if (memberName === \"key\") ; else if (memberName === \"ref\") {\n      if (newValue) {\n        newValue(elm);\n      }\n    } else if ((!isProp ) && memberName[0] === \"o\" && memberName[1] === \"n\") {\n      if (memberName[2] === \"-\") {\n        memberName = memberName.slice(3);\n      } else if (isMemberInElement(win, ln)) {\n        memberName = ln.slice(2);\n      } else {\n        memberName = ln[2] + memberName.slice(3);\n      }\n      if (oldValue || newValue) {\n        const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n        memberName = memberName.replace(CAPTURE_EVENT_REGEX, \"\");\n        if (oldValue) {\n          plt.rel(elm, memberName, oldValue, capture);\n        }\n        if (newValue) {\n          plt.ael(elm, memberName, newValue, capture);\n        }\n      }\n    } else {\n      const isComplex = isComplexType(newValue);\n      if ((isProp || isComplex && newValue !== null) && !isSvg) {\n        try {\n          if (!elm.tagName.includes(\"-\")) {\n            const n = newValue == null ? \"\" : newValue;\n            if (memberName === \"list\") {\n              isProp = false;\n            } else if (oldValue == null || elm[memberName] != n) {\n              elm[memberName] = n;\n            }\n          } else {\n            elm[memberName] = newValue;\n          }\n        } catch (e) {\n        }\n      }\n      let xlink = false;\n      {\n        if (ln !== (ln = ln.replace(/^xlink\\:?/, \"\"))) {\n          memberName = ln;\n          xlink = true;\n        }\n      }\n      if (newValue == null || newValue === false) {\n        if (newValue !== false || elm.getAttribute(memberName) === \"\") {\n          if (xlink) {\n            elm.removeAttributeNS(XLINK_NS, memberName);\n          } else {\n            elm.removeAttribute(memberName);\n          }\n        }\n      } else if ((!isProp || flags & 4 /* isHost */ || isSvg) && !isComplex) {\n        newValue = newValue === true ? \"\" : newValue;\n        if (xlink) {\n          elm.setAttributeNS(XLINK_NS, memberName, newValue);\n        } else {\n          elm.setAttribute(memberName, newValue);\n        }\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = (value) => !value ? [] : value.split(parseClassListRegex);\nvar CAPTURE_EVENT_SUFFIX = \"Capture\";\nvar CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + \"$\");\n\n// src/runtime/vdom/update-element.ts\nvar updateElement = (oldVnode, newVnode, isSvgMode2) => {\n  const elm = newVnode.$elm$.nodeType === 11 /* DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || EMPTY_OBJ;\n  const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;\n  {\n    for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], void 0, isSvgMode2, newVnode.$flags$);\n      }\n    }\n  }\n  for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode2, newVnode.$flags$);\n  }\n};\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes(\"ref\") ? (\n    // we need to sort these to ensure that `'ref'` is the last attr\n    [...attrNames.filter((attr) => attr !== \"ref\"), \"ref\"]\n  ) : (\n    // no need to sort, return the original array\n    attrNames\n  );\n}\n\n// src/runtime/vdom/vdom-render.ts\nvar scopeId;\nvar contentRef;\nvar hostTagName;\nvar useNativeShadowDom = false;\nvar checkSlotFallbackVisibility = false;\nvar checkSlotRelocate = false;\nvar isSvgMode = false;\nvar createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {\n  var _a;\n  const newVNode2 = newParentVNode.$children$[childIndex];\n  let i2 = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (!useNativeShadowDom) {\n    checkSlotRelocate = true;\n    if (newVNode2.$tag$ === \"slot\") {\n      if (scopeId) {\n        parentElm.classList.add(scopeId + \"-s\");\n      }\n      newVNode2.$flags$ |= newVNode2.$children$ ? (\n        // slot element has fallback content\n        // still create an element that \"mocks\" the slot element\n        2 /* isSlotFallback */\n      ) : (\n        // slot element does not have fallback content\n        // create an html comment we'll use to always reference\n        // where actual slot content should sit next to\n        1 /* isSlotReference */\n      );\n    }\n  }\n  if (newVNode2.$text$ !== null) {\n    elm = newVNode2.$elm$ = doc.createTextNode(newVNode2.$text$);\n  } else if (newVNode2.$flags$ & 1 /* isSlotReference */) {\n    elm = newVNode2.$elm$ = doc.createTextNode(\"\");\n  } else {\n    if (!isSvgMode) {\n      isSvgMode = newVNode2.$tag$ === \"svg\";\n    }\n    elm = newVNode2.$elm$ = doc.createElementNS(\n      isSvgMode ? SVG_NS : HTML_NS,\n      !useNativeShadowDom && BUILD.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$\n    ) ;\n    if (isSvgMode && newVNode2.$tag$ === \"foreignObject\") {\n      isSvgMode = false;\n    }\n    {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n    const rootNode = elm.getRootNode();\n    const isElementWithinShadowRoot = !rootNode.querySelector(\"body\");\n    if (!isElementWithinShadowRoot && BUILD.scoped && isDef(scopeId) && elm[\"s-si\"] !== scopeId) {\n      elm.classList.add(elm[\"s-si\"] = scopeId);\n    }\n    {\n      updateElementScopeIds(elm, parentElm);\n    }\n    if (newVNode2.$children$) {\n      for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {\n        childNode = createElm(oldParentVNode, newVNode2, i2, elm);\n        if (childNode) {\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    {\n      if (newVNode2.$tag$ === \"svg\") {\n        isSvgMode = false;\n      } else if (elm.tagName === \"foreignObject\") {\n        isSvgMode = true;\n      }\n    }\n  }\n  elm[\"s-hn\"] = hostTagName;\n  {\n    if (newVNode2.$flags$ & (2 /* isSlotFallback */ | 1 /* isSlotReference */)) {\n      elm[\"s-sr\"] = true;\n      elm[\"s-cr\"] = contentRef;\n      elm[\"s-sn\"] = newVNode2.$name$ || \"\";\n      elm[\"s-rf\"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {\n        {\n          relocateToHostRoot(oldParentVNode.$elm$);\n        }\n      }\n    }\n  }\n  return elm;\n};\nvar relocateToHostRoot = (parentElm) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const host = parentElm.closest(hostTagName.toLowerCase());\n  if (host != null) {\n    const contentRefNode = Array.from(host.childNodes).find((ref) => ref[\"s-cr\"]);\n    const childNodeArray = Array.from(parentElm.childNodes);\n    for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {\n      if (childNode[\"s-sh\"] != null) {\n        insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);\n        childNode[\"s-sh\"] = void 0;\n        checkSlotRelocate = true;\n      }\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.childNodes);\n  if (parentElm[\"s-sr\"] && BUILD.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node[\"s-sn\"] === parentElm[\"s-sn\"] && node[\"s-sh\"] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {\n    const childNode = oldSlotChildNodes[i2];\n    if (childNode[\"s-hn\"] !== hostTagName && childNode[\"s-ol\"]) {\n      insertBefore(parentReferenceNode(childNode), childNode, referenceNode(childNode));\n      childNode[\"s-ol\"].remove();\n      childNode[\"s-ol\"] = void 0;\n      childNode[\"s-sh\"] = void 0;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = parentElm[\"s-cr\"] && parentElm[\"s-cr\"].parentNode || parentElm;\n  let childNode;\n  if (containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx, parentElm);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        insertBefore(containerElm, childNode, referenceNode(before) );\n      }\n    }\n  }\n};\nvar removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        {\n          checkSlotFallbackVisibility = true;\n          if (elm[\"s-ol\"]) {\n            elm[\"s-ol\"].remove();\n          } else {\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        elm.remove();\n      }\n    }\n  }\n};\nvar updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i2 = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      if ((oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      if ((oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      idxInOld = -1;\n      {\n        for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {\n          if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {\n            idxInOld = i2;\n            break;\n          }\n        }\n      }\n      if (idxInOld >= 0) {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld, parentElm);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          oldCh[idxInOld] = void 0;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx, parentElm);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        {\n          insertBefore(parentReferenceNode(oldStartVnode.$elm$), node, referenceNode(oldStartVnode.$elm$));\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    addVnodes(\n      parentElm,\n      newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$,\n      newVNode2,\n      newCh,\n      newStartIdx,\n      newEndIdx\n    );\n  } else if (newStartIdx > newEndIdx) {\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\nvar isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (leftVNode.$tag$ === \"slot\") {\n      if (\n        // The component gets hydrated and no VDOM has been initialized.\n        // Here the comparison can't happen as $name$ property is not set for `leftNode`.\n        \"$nodeId$\" in leftVNode && isInitialRender && // `leftNode` is not from type HTMLComment which would cause many\n        // hydration comments to be removed\n        leftVNode.$elm$.nodeType !== 8\n      ) {\n        return false;\n      }\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    if (!isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nvar referenceNode = (node) => {\n  return node && node[\"s-ol\"] || node;\n};\nvar parentReferenceNode = (node) => (node[\"s-ol\"] ? node[\"s-ol\"] : node).parentNode;\nvar patch = (oldVNode, newVNode2, isInitialRender = false) => {\n  const elm = newVNode2.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode2.$children$;\n  const tag = newVNode2.$tag$;\n  const text = newVNode2.$text$;\n  let defaultHolder;\n  if (text === null) {\n    {\n      isSvgMode = tag === \"svg\" ? true : tag === \"foreignObject\" ? false : isSvgMode;\n    }\n    {\n      if (tag === \"slot\" && !useNativeShadowDom) {\n        if (oldVNode.$name$ !== newVNode2.$name$) {\n          newVNode2.$elm$[\"s-sn\"] = newVNode2.$name$ || \"\";\n          relocateToHostRoot(newVNode2.$elm$.parentElement);\n        }\n      } else {\n        updateElement(oldVNode, newVNode2, isSvgMode);\n      }\n    }\n    if (oldChildren !== null && newChildren !== null) {\n      updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      if (oldVNode.$text$ !== null) {\n        elm.textContent = \"\";\n      }\n      addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);\n    } else if (\n      // don't do this on initial render as it can cause non-hydrated content to be removed\n      !isInitialRender && BUILD.updatable && oldChildren !== null\n    ) {\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (isSvgMode && tag === \"svg\") {\n      isSvgMode = false;\n    }\n  } else if ((defaultHolder = elm[\"s-cr\"])) {\n    defaultHolder.parentNode.textContent = text;\n  } else if (oldVNode.$text$ !== text) {\n    elm.data = text;\n  }\n};\nvar updateFallbackSlotVisibility = (elm) => {\n  const childNodes = elm.childNodes;\n  for (const childNode of childNodes) {\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      if (childNode[\"s-sr\"]) {\n        const slotName = childNode[\"s-sn\"];\n        childNode.hidden = false;\n        for (const siblingNode of childNodes) {\n          if (siblingNode !== childNode) {\n            if (siblingNode[\"s-hn\"] !== childNode[\"s-hn\"] || slotName !== \"\") {\n              if (siblingNode.nodeType === 1 /* ElementNode */ && (slotName === siblingNode.getAttribute(\"slot\") || slotName === siblingNode[\"s-sn\"]) || siblingNode.nodeType === 3 /* TextNode */ && slotName === siblingNode[\"s-sn\"]) {\n                childNode.hidden = true;\n                break;\n              }\n            } else {\n              if (siblingNode.nodeType === 1 /* ElementNode */ || siblingNode.nodeType === 3 /* TextNode */ && siblingNode.textContent.trim() !== \"\") {\n                childNode.hidden = true;\n                break;\n              }\n            }\n          }\n        }\n      }\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\nvar relocateNodes = [];\nvar markSlotContentForRelocation = (elm) => {\n  let node;\n  let hostContentNodes;\n  let j;\n  for (const childNode of elm.childNodes) {\n    if (childNode[\"s-sr\"] && (node = childNode[\"s-cr\"]) && node.parentNode) {\n      hostContentNodes = node.parentNode.childNodes;\n      const slotName = childNode[\"s-sn\"];\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        if (!node[\"s-cn\"] && !node[\"s-nr\"] && node[\"s-hn\"] !== childNode[\"s-hn\"] && (!node[\"s-sh\"] || node[\"s-sh\"] !== childNode[\"s-hn\"])) {\n          if (isNodeLocatedInSlot(node, slotName)) {\n            let relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n            checkSlotFallbackVisibility = true;\n            node[\"s-sn\"] = node[\"s-sn\"] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node[\"s-sr\"]) {\n              relocateNodes.map((relocateNode) => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node[\"s-sn\"])) {\n                  relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some((r) => r.$nodeToRelocate$ === node)) {\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\nvar isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n    if (nodeToRelocate.getAttribute(\"slot\") === null && slotName === \"\") {\n      return true;\n    }\n    if (nodeToRelocate.getAttribute(\"slot\") === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate[\"s-sn\"] === slotName) {\n    return true;\n  }\n  return slotName === \"\";\n};\nvar nullifyVNodeRefs = (vNode) => {\n  {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar insertBefore = (parent, newNode, reference) => {\n  const inserted = parent == null ? void 0 : parent.insertBefore(newNode, reference);\n  {\n    updateElementScopeIds(newNode, parent);\n  }\n  return inserted;\n};\nvar findScopeIds = (element) => {\n  const scopeIds = [];\n  if (element) {\n    scopeIds.push(\n      ...element[\"s-scs\"] || [],\n      element[\"s-si\"],\n      element[\"s-sc\"],\n      ...findScopeIds(element.parentElement)\n    );\n  }\n  return scopeIds;\n};\nvar updateElementScopeIds = (element, parent, iterateChildNodes = false) => {\n  var _a;\n  if (element && parent && element.nodeType === 1 /* ElementNode */) {\n    const scopeIds = new Set(findScopeIds(parent).filter(Boolean));\n    if (scopeIds.size) {\n      (_a = element.classList) == null ? void 0 : _a.add(...element[\"s-scs\"] = [...scopeIds]);\n      if (element[\"s-ol\"] || iterateChildNodes) {\n        for (const childNode of Array.from(element.childNodes)) {\n          updateElementScopeIds(childNode, element, true);\n        }\n      }\n    }\n  }\n};\nvar renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d, _e;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(\n      ([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]\n    );\n  }\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      if (hostElm.hasAttribute(key) && ![\"key\", \"ref\", \"style\", \"class\"].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = hostElm.shadowRoot || hostElm ;\n  {\n    scopeId = hostElm[\"s-sc\"];\n  }\n  useNativeShadowDom = (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) !== 0;\n  {\n    contentRef = hostElm[\"s-cr\"];\n    checkSlotFallbackVisibility = false;\n  }\n  patch(oldVNode, rootVnode, isInitialLoad);\n  {\n    plt.$flags$ |= 1 /* isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate[\"s-ol\"]) {\n          const orgLocationNode = doc.createTextNode(\"\");\n          orgLocationNode[\"s-nr\"] = nodeToRelocate;\n          insertBefore(nodeToRelocate.parentNode, nodeToRelocate[\"s-ol\"] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          let insertBeforeNode = slotRefNode.nextSibling;\n          if (insertBeforeNode && insertBeforeNode.nodeType === 1 /* ElementNode */) {\n            let orgLocationNode = (_a = nodeToRelocate[\"s-ol\"]) == null ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode[\"s-nr\"]) != null ? _b : null;\n              if (refNode && refNode[\"s-sn\"] === nodeToRelocate[\"s-sn\"] && parentNodeRef === refNode.parentNode) {\n                refNode = refNode.nextSibling;\n                while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode[\"s-sr\"])) {\n                  refNode = refNode == null ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode[\"s-nr\"]) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          if (!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode || nodeToRelocate.nextSibling !== insertBeforeNode) {\n            if (nodeToRelocate !== insertBeforeNode) {\n              insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);\n              if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n                nodeToRelocate.hidden = (_c = nodeToRelocate[\"s-ih\"]) != null ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode[\"s-rf\"] === \"function\" && slotRefNode[\"s-rf\"](nodeToRelocate);\n        } else {\n          if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n            if (isInitialLoad) {\n              nodeToRelocate[\"s-ih\"] = (_d = nodeToRelocate.hidden) != null ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    plt.$flags$ &= ~1 /* isTmpDisconnected */;\n    relocateNodes.length = 0;\n  }\n  if (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n    for (const childNode of rootVnode.$elm$.childNodes) {\n      if (childNode[\"s-hn\"] !== hostTagName && !childNode[\"s-sh\"]) {\n        if (isInitialLoad && childNode[\"s-ih\"] == null) {\n          childNode[\"s-ih\"] = (_e = childNode.hidden) != null ? _e : false;\n        }\n        childNode.hidden = true;\n      }\n    }\n  }\n  contentRef = void 0;\n};\n\n// src/runtime/update-component.ts\nvar attachToAncestor = (hostRef, ancestorComponent) => {\n  if (ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent[\"s-p\"]) {\n    ancestorComponent[\"s-p\"].push(new Promise((r) => hostRef.$onRenderResolve$ = r));\n  }\n};\nvar scheduleUpdate = (hostRef, isInitialLoad) => {\n  {\n    hostRef.$flags$ |= 16 /* isQueuedForUpdate */;\n  }\n  if (hostRef.$flags$ & 4 /* isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return writeTask(dispatch) ;\n};\nvar dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime(\"scheduleUpdate\", hostRef.$cmpMeta$.$tagName$);\n  const instance = hostRef.$lazyInstance$ ;\n  if (!instance) {\n    throw new Error(\n      `Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \\`externalRuntime: true\\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`\n    );\n  }\n  let maybePromise;\n  if (isInitialLoad) {\n    {\n      hostRef.$flags$ |= 256 /* isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));\n        hostRef.$queuedListeners$ = void 0;\n      }\n    }\n    {\n      maybePromise = safeCall(instance, \"componentWillLoad\");\n    }\n  }\n  {\n    maybePromise = enqueue(maybePromise, () => safeCall(instance, \"componentWillRender\"));\n  }\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\nvar enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch((err2) => {\n  console.error(err2);\n  fn();\n}) : fn();\nvar isPromisey = (maybePromise) => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === \"function\";\nvar updateComponent = async (hostRef, instance, isInitialLoad) => {\n  var _a;\n  const elm = hostRef.$hostElement$;\n  const endUpdate = createTime(\"update\", hostRef.$cmpMeta$.$tagName$);\n  const rc = elm[\"s-rc\"];\n  if (isInitialLoad) {\n    attachStyles(hostRef);\n  }\n  const endRender = createTime(\"render\", hostRef.$cmpMeta$.$tagName$);\n  {\n    callRender(hostRef, instance, elm, isInitialLoad);\n  }\n  if (rc) {\n    rc.map((cb) => cb());\n    elm[\"s-rc\"] = void 0;\n  }\n  endRender();\n  endUpdate();\n  {\n    const childrenPromises = (_a = elm[\"s-p\"]) != null ? _a : [];\n    const postUpdate = () => postUpdateComponent(hostRef);\n    if (childrenPromises.length === 0) {\n      postUpdate();\n    } else {\n      Promise.all(childrenPromises).then(postUpdate);\n      hostRef.$flags$ |= 4 /* isWaitingForChildren */;\n      childrenPromises.length = 0;\n    }\n  }\n};\nvar callRender = (hostRef, instance, elm, isInitialLoad) => {\n  try {\n    instance = instance.render && instance.render();\n    {\n      hostRef.$flags$ &= ~16 /* isQueuedForUpdate */;\n    }\n    {\n      hostRef.$flags$ |= 2 /* hasRendered */;\n    }\n    {\n      {\n        {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  return null;\n};\nvar postUpdateComponent = (hostRef) => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime(\"postUpdate\", tagName);\n  const instance = hostRef.$lazyInstance$ ;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  {\n    safeCall(instance, \"componentDidRender\");\n  }\n  if (!(hostRef.$flags$ & 64 /* hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* hasLoadedComponent */;\n    {\n      addHydratedFlag(elm);\n    }\n    {\n      safeCall(instance, \"componentDidLoad\");\n    }\n    endPostUpdate();\n    {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad();\n      }\n    }\n  } else {\n    {\n      safeCall(instance, \"componentDidUpdate\");\n    }\n    endPostUpdate();\n  }\n  {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = void 0;\n    }\n    if (hostRef.$flags$ & 512 /* needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* isWaitingForChildren */ | 512 /* needsRerender */);\n  }\n};\nvar forceUpdate = (ref) => {\n  {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    return isConnected;\n  }\n};\nvar appDidLoad = (who) => {\n  {\n    addHydratedFlag(doc.documentElement);\n  }\n  nextTick(() => emitEvent(win, \"appload\", { detail: { namespace: NAMESPACE } }));\n};\nvar safeCall = (instance, method, arg) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  return void 0;\n};\nvar addHydratedFlag = (elm) => {\n  var _a;\n  return elm.classList.add((_a = BUILD.hydratedSelectorName) != null ? _a : \"hydrated\") ;\n};\n\n// src/runtime/set-value.ts\nvar getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nvar setValue = (ref, propName, newVal, cmpMeta) => {\n  const hostRef = getHostRef(ref);\n  if (!hostRef) {\n    throw new Error(\n      `Couldn't find host element for \"${cmpMeta.$tagName$}\" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/ionic-team/stencil/issues/5457).`\n    );\n  }\n  const elm = hostRef.$hostElement$ ;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = hostRef.$lazyInstance$ ;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!(flags & 8 /* isConstructingInstance */) || oldVal === void 0) && didValueChange) {\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (instance) {\n      if (cmpMeta.$watchers$ && flags & 128 /* isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          watchMethods.map((watchMethodName) => {\n            try {\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if ((flags & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n\n// src/runtime/proxy-component.ts\nvar proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a, _b;\n  const prototype = Cstr.prototype;\n  if (cmpMeta.$members$ || (cmpMeta.$watchers$ || Cstr.watchers)) {\n    if (Cstr.watchers && !cmpMeta.$watchers$) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n    members.map(([memberName, [memberFlags]]) => {\n      if ((memberFlags & 31 /* Prop */ || (flags & 2 /* proxyState */) && memberFlags & 32 /* State */)) {\n        Object.defineProperty(prototype, memberName, {\n          get() {\n            return getValue(this, memberName);\n          },\n          set(newValue) {\n            setValue(this, memberName, newValue, cmpMeta);\n          },\n          configurable: true,\n          enumerable: true\n        });\n      } else if (flags & 1 /* isElementConstructor */ && memberFlags & 64 /* Method */) {\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a2;\n            const ref = getHostRef(this);\n            return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {\n              var _a3;\n              return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if ((flags & 1 /* isElementConstructor */)) {\n      const attrNameToPropName = /* @__PURE__ */ new Map();\n      prototype.attributeChangedCallback = function(attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a2;\n          const propName = attrNameToPropName.get(attrName);\n          if (this.hasOwnProperty(propName)) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === \"number\" && // cast type to number to avoid TS compiler issues\n          this[propName] == newValue) {\n            return;\n          } else if (propName == null) {\n            const hostRef = getHostRef(this);\n            const flags2 = hostRef == null ? void 0 : hostRef.$flags$;\n            if (flags2 && !(flags2 & 8 /* isConstructingInstance */) && flags2 & 128 /* isWatchReady */ && newValue !== oldValue) {\n              const instance = hostRef.$lazyInstance$ ;\n              const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];\n              entry == null ? void 0 : entry.forEach((callbackName) => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          this[propName] = newValue === null && typeof this[propName] === \"boolean\" ? false : newValue;\n        });\n      };\n      Cstr.observedAttributes = Array.from(\n        /* @__PURE__ */ new Set([\n          ...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}),\n          ...members.filter(([_, m]) => m[0] & 15 /* HasAttribute */).map(([propName, m]) => {\n            var _a2;\n            const attrName = m[1] || propName;\n            attrNameToPropName.set(attrName, propName);\n            if (m[0] & 512 /* ReflectAttr */) {\n              (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);\n            }\n            return attrName;\n          })\n        ])\n      );\n    }\n  }\n  return Cstr;\n};\n\n// src/runtime/initialize-component.ts\nvar initializeComponent = async (elm, hostRef, cmpMeta, hmrVersionId) => {\n  let Cstr;\n  if ((hostRef.$flags$ & 32 /* hasInitializedComponent */) === 0) {\n    hostRef.$flags$ |= 32 /* hasInitializedComponent */;\n    const bundleId = cmpMeta.$lazyBundleId$;\n    if (bundleId) {\n      const CstrImport = loadModule(cmpMeta);\n      if (CstrImport && \"then\" in CstrImport) {\n        const endLoad = uniqueTime();\n        Cstr = await CstrImport;\n        endLoad();\n      } else {\n        Cstr = CstrImport;\n      }\n      if (!Cstr) {\n        throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n      }\n      if (!Cstr.isProxied) {\n        {\n          cmpMeta.$watchers$ = Cstr.watchers;\n        }\n        proxyComponent(Cstr, cmpMeta, 2 /* proxyState */);\n        Cstr.isProxied = true;\n      }\n      const endNewInstance = createTime(\"createInstance\", cmpMeta.$tagName$);\n      {\n        hostRef.$flags$ |= 8 /* isConstructingInstance */;\n      }\n      try {\n        new Cstr(hostRef);\n      } catch (e) {\n        consoleError(e);\n      }\n      {\n        hostRef.$flags$ &= ~8 /* isConstructingInstance */;\n      }\n      {\n        hostRef.$flags$ |= 128 /* isWatchReady */;\n      }\n      endNewInstance();\n      fireConnectedCallback(hostRef.$lazyInstance$);\n    } else {\n      Cstr = elm.constructor;\n      const cmpTag = elm.localName;\n      customElements.whenDefined(cmpTag).then(() => hostRef.$flags$ |= 128 /* isWatchReady */);\n    }\n    if (Cstr && Cstr.style) {\n      let style;\n      if (typeof Cstr.style === \"string\") {\n        style = Cstr.style;\n      } else if (typeof Cstr.style !== \"string\") {\n        hostRef.$modeName$ = computeMode(elm);\n        if (hostRef.$modeName$) {\n          style = Cstr.style[hostRef.$modeName$];\n        }\n      }\n      const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);\n      if (!styles.has(scopeId2)) {\n        const endRegisterStyles = createTime(\"registerStyles\", cmpMeta.$tagName$);\n        registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */));\n        endRegisterStyles();\n      }\n    }\n  }\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  const schedule = () => scheduleUpdate(hostRef, true);\n  if (ancestorComponent && ancestorComponent[\"s-rc\"]) {\n    ancestorComponent[\"s-rc\"].push(schedule);\n  } else {\n    schedule();\n  }\n};\nvar fireConnectedCallback = (instance) => {\n  {\n    safeCall(instance, \"connectedCallback\");\n  }\n};\n\n// src/runtime/connected-callback.ts\nvar connectedCallback = (elm) => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime(\"connectedCallback\", cmpMeta.$tagName$);\n    if (!(hostRef.$flags$ & 1 /* hasConnected */)) {\n      hostRef.$flags$ |= 1 /* hasConnected */;\n      let hostId;\n      {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            const scopeId2 = addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute(\"s-mode\")) ;\n            elm.classList.remove(scopeId2 + \"-h\", scopeId2 + \"-s\");\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (!hostId) {\n        if (// TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* hasSlotRelocation */ | 8 /* needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      {\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          if (ancestorComponent.nodeType === 1 /* ElementNode */ && ancestorComponent.hasAttribute(\"s-id\") && ancestorComponent[\"s-p\"] || ancestorComponent[\"s-p\"]) {\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      if (cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$);\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));\n      }\n    }\n    endConnected();\n  }\n};\nvar setContentReference = (elm) => {\n  const contentRefElm = elm[\"s-cr\"] = doc.createComment(\n    \"\"\n  );\n  contentRefElm[\"s-cn\"] = true;\n  insertBefore(elm, contentRefElm, elm.firstChild);\n};\nvar disconnectInstance = (instance) => {\n  {\n    safeCall(instance, \"disconnectedCallback\");\n  }\n};\nvar disconnectedCallback = async (elm) => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    {\n      if (hostRef.$rmListeners$) {\n        hostRef.$rmListeners$.map((rmListener) => rmListener());\n        hostRef.$rmListeners$ = void 0;\n      }\n    }\n    if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n      disconnectInstance(hostRef.$lazyInstance$);\n    } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n      hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));\n    }\n  }\n};\nvar patchPseudoShadowDom = (hostElementPrototype, descriptorPrototype) => {\n  patchCloneNode(hostElementPrototype);\n  patchSlotAppendChild(hostElementPrototype);\n  patchSlotAppend(hostElementPrototype);\n  patchSlotPrepend(hostElementPrototype);\n  patchSlotInsertAdjacentElement(hostElementPrototype);\n  patchSlotInsertAdjacentHTML(hostElementPrototype);\n  patchSlotInsertAdjacentText(hostElementPrototype);\n  patchTextContent(hostElementPrototype);\n  patchChildSlotNodes(hostElementPrototype, descriptorPrototype);\n  patchSlotRemoveChild(hostElementPrototype);\n};\nvar patchCloneNode = (HostElementPrototype) => {\n  const orgCloneNode = HostElementPrototype.cloneNode;\n  HostElementPrototype.cloneNode = function(deep) {\n    const srcNode = this;\n    const isShadowDom = srcNode.shadowRoot && supportsShadow ;\n    const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n    if (!isShadowDom && deep) {\n      let i2 = 0;\n      let slotted, nonStencilNode;\n      const stencilPrivates = [\n        \"s-id\",\n        \"s-cr\",\n        \"s-lr\",\n        \"s-rc\",\n        \"s-sc\",\n        \"s-p\",\n        \"s-cn\",\n        \"s-sr\",\n        \"s-sn\",\n        \"s-hn\",\n        \"s-ol\",\n        \"s-nr\",\n        \"s-si\",\n        \"s-rf\",\n        \"s-scs\"\n      ];\n      for (; i2 < srcNode.childNodes.length; i2++) {\n        slotted = srcNode.childNodes[i2][\"s-nr\"];\n        nonStencilNode = stencilPrivates.every((privateField) => !srcNode.childNodes[i2][privateField]);\n        if (slotted) {\n          if (clonedNode.__appendChild) {\n            clonedNode.__appendChild(slotted.cloneNode(true));\n          } else {\n            clonedNode.appendChild(slotted.cloneNode(true));\n          }\n        }\n        if (nonStencilNode) {\n          clonedNode.appendChild(srcNode.childNodes[i2].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nvar patchSlotAppendChild = (HostElementPrototype) => {\n  HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n  HostElementPrototype.appendChild = function(newChild) {\n    const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n    const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n    if (slotNode) {\n      const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n      const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n      const insertedNode = insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      updateFallbackSlotVisibility(this);\n      return insertedNode;\n    }\n    return this.__appendChild(newChild);\n  };\n};\nvar patchSlotRemoveChild = (ElementPrototype) => {\n  ElementPrototype.__removeChild = ElementPrototype.removeChild;\n  ElementPrototype.removeChild = function(toRemove) {\n    if (toRemove && typeof toRemove[\"s-sn\"] !== \"undefined\") {\n      const slotNode = getHostSlotNode(this.childNodes, toRemove[\"s-sn\"], this.tagName);\n      if (slotNode) {\n        const slotChildNodes = getHostSlotChildNodes(slotNode, toRemove[\"s-sn\"]);\n        const existingNode = slotChildNodes.find((n) => n === toRemove);\n        if (existingNode) {\n          existingNode.remove();\n          updateFallbackSlotVisibility(this);\n          return;\n        }\n      }\n    }\n    return this.__removeChild(toRemove);\n  };\n};\nvar patchSlotPrepend = (HostElementPrototype) => {\n  const originalPrepend = HostElementPrototype.prepend;\n  HostElementPrototype.prepend = function(...newChildren) {\n    newChildren.forEach((newChild) => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n      const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n      if (slotNode) {\n        const slotPlaceholder = document.createTextNode(\"\");\n        slotPlaceholder[\"s-nr\"] = newChild;\n        slotNode[\"s-cr\"].parentNode.__appendChild(slotPlaceholder);\n        newChild[\"s-ol\"] = slotPlaceholder;\n        const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n        const appendAfter = slotChildNodes[0];\n        return insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      }\n      if (newChild.nodeType === 1 && !!newChild.getAttribute(\"slot\")) {\n        newChild.hidden = true;\n      }\n      return originalPrepend.call(this, newChild);\n    });\n  };\n};\nvar patchSlotAppend = (HostElementPrototype) => {\n  HostElementPrototype.append = function(...newChildren) {\n    newChildren.forEach((newChild) => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      this.appendChild(newChild);\n    });\n  };\n};\nvar patchSlotInsertAdjacentHTML = (HostElementPrototype) => {\n  const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n  HostElementPrototype.insertAdjacentHTML = function(position, text) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentHtml.call(this, position, text);\n    }\n    const container = this.ownerDocument.createElement(\"_\");\n    let node;\n    container.innerHTML = text;\n    if (position === \"afterbegin\") {\n      while (node = container.firstChild) {\n        this.prepend(node);\n      }\n    } else if (position === \"beforeend\") {\n      while (node = container.firstChild) {\n        this.append(node);\n      }\n    }\n  };\n};\nvar patchSlotInsertAdjacentText = (HostElementPrototype) => {\n  HostElementPrototype.insertAdjacentText = function(position, text) {\n    this.insertAdjacentHTML(position, text);\n  };\n};\nvar patchSlotInsertAdjacentElement = (HostElementPrototype) => {\n  const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n  HostElementPrototype.insertAdjacentElement = function(position, element) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentElement.call(this, position, element);\n    }\n    if (position === \"afterbegin\") {\n      this.prepend(element);\n      return element;\n    } else if (position === \"beforeend\") {\n      this.append(element);\n      return element;\n    }\n    return element;\n  };\n};\nvar patchTextContent = (hostElementPrototype) => {\n  const descriptor = Object.getOwnPropertyDescriptor(Node.prototype, \"textContent\");\n  Object.defineProperty(hostElementPrototype, \"__textContent\", descriptor);\n  {\n    Object.defineProperty(hostElementPrototype, \"textContent\", {\n      // To mimic shadow root behavior, we need to return the text content of all\n      // nodes in a slot reference node\n      get() {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        const textContent = slotRefNodes.map((node) => {\n          var _a, _b;\n          const text = [];\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            if (slotContent.nodeType === 3 /* TEXT_NODE */ || slotContent.nodeType === 1 /* ELEMENT_NODE */) {\n              text.push((_b = (_a = slotContent.textContent) == null ? void 0 : _a.trim()) != null ? _b : \"\");\n            }\n            slotContent = slotContent.nextSibling;\n          }\n          return text.filter((ref) => ref !== \"\").join(\" \");\n        }).filter((text) => text !== \"\").join(\" \");\n        return \" \" + textContent + \" \";\n      },\n      // To mimic shadow root behavior, we need to overwrite all nodes in a slot\n      // reference node. If a default slot reference node exists, the text content will be\n      // placed there. Otherwise, the new text node will be hidden\n      set(value) {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        slotRefNodes.forEach((node) => {\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            const tmp = slotContent;\n            slotContent = slotContent.nextSibling;\n            tmp.remove();\n          }\n          if (node[\"s-sn\"] === \"\") {\n            const textNode = this.ownerDocument.createTextNode(value);\n            textNode[\"s-sn\"] = \"\";\n            insertBefore(node.parentElement, textNode, node.nextSibling);\n          } else {\n            node.remove();\n          }\n        });\n      }\n    });\n  }\n};\nvar patchChildSlotNodes = (elm, cmpMeta) => {\n  class FakeNodeList extends Array {\n    item(n) {\n      return this[n];\n    }\n  }\n  if (cmpMeta.$flags$ & 8 /* needsShadowDomShim */) {\n    const childNodesFn = elm.__lookupGetter__(\"childNodes\");\n    Object.defineProperty(elm, \"children\", {\n      get() {\n        return this.childNodes.map((n) => n.nodeType === 1);\n      }\n    });\n    Object.defineProperty(elm, \"childElementCount\", {\n      get() {\n        return elm.children.length;\n      }\n    });\n    Object.defineProperty(elm, \"childNodes\", {\n      get() {\n        const childNodes = childNodesFn.call(this);\n        if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0 && getHostRef(this).$flags$ & 2 /* hasRendered */) {\n          const result = new FakeNodeList();\n          for (let i2 = 0; i2 < childNodes.length; i2++) {\n            const slot = childNodes[i2][\"s-nr\"];\n            if (slot) {\n              result.push(slot);\n            }\n          }\n          return result;\n        }\n        return FakeNodeList.from(childNodes);\n      }\n    });\n  }\n};\nvar getAllChildSlotNodes = (childNodes) => {\n  const slotRefNodes = [];\n  for (const childNode of Array.from(childNodes)) {\n    if (childNode[\"s-sr\"]) {\n      slotRefNodes.push(childNode);\n    }\n    slotRefNodes.push(...getAllChildSlotNodes(childNode.childNodes));\n  }\n  return slotRefNodes;\n};\nvar getSlotName = (node) => node[\"s-sn\"] || node.nodeType === 1 && node.getAttribute(\"slot\") || \"\";\nvar getHostSlotNode = (childNodes, slotName, hostName) => {\n  let i2 = 0;\n  let childNode;\n  for (; i2 < childNodes.length; i2++) {\n    childNode = childNodes[i2];\n    if (childNode[\"s-sr\"] && childNode[\"s-sn\"] === slotName && childNode[\"s-hn\"] === hostName) {\n      return childNode;\n    }\n    childNode = getHostSlotNode(childNode.childNodes, slotName, hostName);\n    if (childNode) {\n      return childNode;\n    }\n  }\n  return null;\n};\nvar getHostSlotChildNodes = (n, slotName) => {\n  const childNodes = [n];\n  while ((n = n.nextSibling) && n[\"s-sn\"] === slotName) {\n    childNodes.push(n);\n  }\n  return childNodes;\n};\n\n// src/runtime/bootstrap-lazy.ts\nvar bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  const endBootstrap = createTime();\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements2 = win.customElements;\n  const head = doc.head;\n  const metaCharset = /* @__PURE__ */ head.querySelector(\"meta[charset]\");\n  const dataStyles = /* @__PURE__ */ doc.createElement(\"style\");\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || \"./\", doc.baseURI).href;\n  {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map((lazyBundle) => {\n    lazyBundle[1].map((compactMeta) => {\n      var _a2;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      {\n        cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};\n      }\n      const tagName = cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          super(self);\n          this.hasRegisteredEventListeners = false;\n          self = this;\n          registerHost(self, cmpMeta);\n          if (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            {\n              if (!self.shadowRoot) {\n                {\n                  self.attachShadow({\n                    mode: \"open\",\n                    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n                  });\n                }\n              } else {\n                if (self.shadowRoot.mode !== \"open\") {\n                  throw new Error(\n                    `Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`\n                  );\n                }\n              }\n            }\n          }\n        }\n        connectedCallback() {\n          const hostRef = getHostRef(this);\n          if (!this.hasRegisteredEventListeners) {\n            this.hasRegisteredEventListeners = true;\n            addHostEventListeners(this, hostRef, cmpMeta.$listeners$);\n          }\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      {\n        if (cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchPseudoShadowDom(HostElement.prototype, cmpMeta);\n        }\n      }\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements2.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements2.define(\n          tagName,\n          proxyComponent(HostElement, cmpMeta, 1 /* isElementConstructor */)\n        );\n      }\n    });\n  });\n  if (cmpTags.length > 0) {\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    {\n      dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;\n    }\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute(\"data-styles\", \"\");\n      const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n      if (nonce != null) {\n        dataStyles.setAttribute(\"nonce\", nonce);\n      }\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map((host) => host.connectedCallback());\n  } else {\n    {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  endBootstrap();\n};\nvar addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (listeners) {\n    listeners.map(([flags, name, method]) => {\n      const target = getHostListenerTarget(elm, flags) ;\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nvar hostListenerProxy = (hostRef, methodName) => (ev) => {\n  var _a;\n  try {\n    {\n      if (hostRef.$flags$ & 256 /* isListenReady */) {\n        (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    }\n  } catch (e) {\n    consoleError(e);\n  }\n};\nvar getHostListenerTarget = (elm, flags) => {\n  if (flags & 4 /* TargetDocument */) return doc;\n  if (flags & 8 /* TargetWindow */) return win;\n  if (flags & 16 /* TargetBody */) return doc.body;\n  return elm;\n};\nvar hostListenerOpts = (flags) => supportsListenerOptions ? {\n  passive: (flags & 1 /* Passive */) !== 0,\n  capture: (flags & 2 /* Capture */) !== 0\n} : (flags & 2 /* Capture */) !== 0;\n\n// src/runtime/nonce.ts\nvar setNonce = (nonce) => plt.$nonce$ = nonce;\n\nexport { Build as B, H, setPlatformHelpers as a, bootstrapLazy as b, setMode as c, createEvent as d, readTask as e, Host as f, getMode as g, h, getElement as i, forceUpdate as j, getAssetPath as k, promiseResolve as p, registerInstance as r, setNonce as s, writeTask as w };\n"], "mappings": ";AAAA;AACA;AACA;AACA,MAAMA,SAAS,GAAG,OAAO;AACzB,MAAMC,KAAK,GAAG,WAAY;EAAEC,WAAW,EAAE,KAAK;EAAEC,kBAAkB,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,UAAU,EAAE,KAAK;EAAEC,YAAY,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,UAAU,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,YAAY,EAAE,KAAK;EAAEC,YAAY,EAAE,IAAI;EAAEC,eAAe,EAAE,KAAK;EAAEC,WAAW,EAAE,IAAI;EAAEC,aAAa,EAAE,IAAI;EAAEC,aAAa,EAAE,KAAK;EAAEC,iBAAiB,EAAE,IAAI;EAAEC,gBAAgB,EAAE,IAAI;EAAEC,cAAc,EAAE,IAAI;EAAEC,QAAQ,EAAE,KAAK;EAAEC,oBAAoB,EAAE,IAAI;EAAEC,OAAO,EAAE,KAAK;EAAEC,KAAK,EAAE,IAAI;EAAEC,6BAA6B,EAAE,IAAI;EAAEC,qBAAqB,EAAE,IAAI;EAAEC,cAAc,EAAE,KAAK;EAAEC,WAAW,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,kBAAkB,EAAE,IAAI;EAAEC,sBAAsB,EAAE,IAAI;EAAEC,0BAA0B,EAAE,IAAI;EAAEC,wBAAwB,EAAE,KAAK;EAAEC,wBAAwB,EAAE,IAAI;EAAEC,oBAAoB,EAAE,KAAK;EAAEC,iBAAiB,EAAE,IAAI;EAAEC,iBAAiB,EAAE,KAAK;EAAEC,iBAAiB,EAAE,KAAK;EAAEC,aAAa,EAAE,IAAI;EAAEC,oBAAoB,EAAE,UAAU;EAAEC,kBAAkB,EAAE,KAAK;EAAEC,qBAAqB,EAAE,IAAI;EAAEC,OAAO,EAAE,KAAK;EAAEC,KAAK,EAAE,KAAK;EAAEC,SAAS,EAAE,KAAK;EAAEC,QAAQ,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,kBAAkB,EAAE,KAAK;EAAEC,MAAM,EAAE,IAAI;EAAEC,MAAM,EAAE,IAAI;EAAEC,IAAI,EAAE,IAAI;EAAEC,gBAAgB,EAAE,IAAI;EAAEC,OAAO,EAAE,KAAK;EAAEC,IAAI,EAAE,IAAI;EAAEC,WAAW,EAAE,IAAI;EAAEC,WAAW,EAAE,IAAI;EAAEC,UAAU,EAAE,IAAI;EAAEC,UAAU,EAAE,IAAI;EAAEC,OAAO,EAAE,IAAI;EAAEC,MAAM,EAAE,IAAI;EAAEC,wBAAwB,EAAE,IAAI;EAAEC,cAAc,EAAE,KAAK;EAAEC,oBAAoB,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,IAAI,EAAE,IAAI;EAAEC,iBAAiB,EAAE,IAAI;EAAEC,cAAc,EAAE,IAAI;EAAEC,KAAK,EAAE,IAAI;EAAEC,KAAK,EAAE,IAAI;EAAEC,GAAG,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,gBAAgB,EAAE,KAAK;EAAEC,SAAS,EAAE,IAAI;EAAEC,aAAa,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,IAAI;EAAEC,UAAU,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,QAAQ,EAAE,IAAI;EAAEC,SAAS,EAAE,IAAI;EAAEC,aAAa,EAAE;AAAK,CAAC;;AAE9sD;AACA;AACA;AACA,IAAIC,SAAS,GAAGC,MAAM,CAACC,cAAc;AACrC,IAAIC,QAAQ,GAAGA,CAACC,MAAM,EAAEC,GAAG,KAAK;EAC9B,KAAK,IAAIC,IAAI,IAAID,GAAG,EAClBL,SAAS,CAACI,MAAM,EAAEE,IAAI,EAAE;IAAEC,GAAG,EAAEF,GAAG,CAACC,IAAI,CAAC;IAAEE,UAAU,EAAE;EAAK,CAAC,CAAC;AACjE,CAAC;AACD,IAAIC,KAAK,GAAG;EACVnD,KAAK,EAAE,KAAK;EACZoD,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,KAAK;EACfpD,SAAS,EAAE;AACb,CAAC;AACD,IAAIqD,QAAQ,GAAG,eAAgB,IAAIC,OAAO,CAAC,CAAC;AAC5C,IAAIC,UAAU,GAAIC,GAAG,IAAKH,QAAQ,CAACL,GAAG,CAACQ,GAAG,CAAC;AAC3C,IAAIC,gBAAgB,GAAGA,CAACC,YAAY,EAAEC,OAAO,KAAKN,QAAQ,CAACO,GAAG,CAACD,OAAO,CAACE,cAAc,GAAGH,YAAY,EAAEC,OAAO,CAAC;AAC9G,IAAIG,YAAY,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC3C,MAAML,OAAO,GAAG;IACdM,OAAO,EAAE,CAAC;IACVC,aAAa,EAAEH,WAAW;IAC1BI,SAAS,EAAEH,OAAO;IAClBI,gBAAgB,EAAE,eAAgB,IAAIC,GAAG,CAAC;EAC5C,CAAC;EACD;IACEV,OAAO,CAACW,mBAAmB,GAAG,IAAIC,OAAO,CAAEC,CAAC,IAAKb,OAAO,CAACc,mBAAmB,GAAGD,CAAC,CAAC;EACnF;EACA;IACEb,OAAO,CAACe,gBAAgB,GAAG,IAAIH,OAAO,CAAEC,CAAC,IAAKb,OAAO,CAACgB,gBAAgB,GAAGH,CAAC,CAAC;IAC3ET,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE;IACvBA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE;EAC1B;EACA,OAAOV,QAAQ,CAACO,GAAG,CAACG,WAAW,EAAEJ,OAAO,CAAC;AAC3C,CAAC;AACD,IAAIiB,iBAAiB,GAAGA,CAACC,GAAG,EAAEC,UAAU,KAAKA,UAAU,IAAID,GAAG;AAC9D,IAAIE,YAAY,GAAGA,CAACC,CAAC,EAAEC,EAAE,KAAK,CAAC,CAAC,EAAEC,OAAO,CAACC,KAAK,EAAEH,CAAC,EAAEC,EAAE,CAAC;;AAEvD;AACA,IAAIG,UAAU,GAAG,eAAgB,IAAIf,GAAG,CAAC,CAAC;AAC1C,IAAIgB,UAAU,GAAGA,CAACrB,OAAO,EAAEL,OAAO,EAAE2B,YAAY,KAAK;EACnD,MAAMC,UAAU,GAAGvB,OAAO,CAACwB,SAAS,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACvD,MAAMC,QAAQ,GAAG1B,OAAO,CAAC2B,cAAc;EACvC,IAAI,CAACD,QAAQ,EAAE;IACb,OAAO,KAAK,CAAC;EACf;EACA,MAAME,MAAM,GAAGR,UAAU,CAACpC,GAAG,CAAC0C,QAAQ,CAAC;EACvC,IAAIE,MAAM,EAAE;IACV,OAAOA,MAAM,CAACL,UAAU,CAAC;EAC3B;EACA;EACA,OAAO,MAAM,CACX;EACA;EACA;EACA;EACA,KAAKG,QAAQ,YAAY,EAAE,EAC7B,CAAC,CAACG,IAAI,CAAEC,cAAc,IAAK;IACzB;MACEV,UAAU,CAACxB,GAAG,CAAC8B,QAAQ,EAAEI,cAAc,CAAC;IAC1C;IACA,OAAOA,cAAc,CAACP,UAAU,CAAC;EACnC,CAAC,EAAER,YAAY,CAAC;AAClB,CAAC;;AAED;AACA,IAAIgB,MAAM,GAAG,eAAgB,IAAI1B,GAAG,CAAC,CAAC;AACtC,IAAI2B,mBAAmB,GAAG,EAAE;;AAE5B;AACA,IAAIC,cAAc,GAAG,GAAG;AACxB,IAAIC,eAAe,GAAG,GAAG;AACzB,IAAIC,YAAY,GAAG,GAAG;AACtB,IAAIC,YAAY,GAAG,GAAG;AACtB,IAAIC,UAAU,GAAG,MAAM;AACvB,IAAIC,iBAAiB,GAAG,QAAQ;AAChC,IAAIC,gBAAgB,GAAG,MAAM;AAC7B,IAAIC,YAAY,GAAG,kDAAkD;AACrE,IAAIC,WAAW,GAAG,wDAAwD;AAC1E,IAAIC,QAAQ,GAAG,8BAA8B;AAC7C,IAAIC,GAAG,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,CAAC,CAAC;AACrD,IAAIC,GAAG,GAAGF,GAAG,CAACG,QAAQ,IAAI;EAAEC,IAAI,EAAE,CAAC;AAAE,CAAC;AACtC,IAAIC,CAAC,GAAGL,GAAG,CAACM,WAAW,IAAI,MAAM,EAChC;AACD,IAAIC,GAAG,GAAG;EACRjD,OAAO,EAAE,CAAC;EACVkD,cAAc,EAAE,EAAE;EAClBC,GAAG,EAAGC,EAAE,IAAKA,EAAE,CAAC,CAAC;EACjBC,GAAG,EAAGD,EAAE,IAAKE,qBAAqB,CAACF,EAAE,CAAC;EACtCG,GAAG,EAAEA,CAACvC,EAAE,EAAEwC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,KAAK1C,EAAE,CAAC2C,gBAAgB,CAACH,SAAS,EAAEC,QAAQ,EAAEC,IAAI,CAAC;EACtFE,GAAG,EAAEA,CAAC5C,EAAE,EAAEwC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,KAAK1C,EAAE,CAAC6C,mBAAmB,CAACL,SAAS,EAAEC,QAAQ,EAAEC,IAAI,CAAC;EACzFI,EAAE,EAAEA,CAACN,SAAS,EAAEE,IAAI,KAAK,IAAIK,WAAW,CAACP,SAAS,EAAEE,IAAI;AAC1D,CAAC;AACD,IAAIM,kBAAkB,GAAIC,OAAO,IAAK;EACpCxF,MAAM,CAACyF,MAAM,CAACjB,GAAG,EAAEgB,OAAO,CAAC;AAC7B,CAAC;AACD,IAAIE,cAAc,GAAG9K,KAAK,CAAC6D,SAAS;AACpC,IAAIkH,uBAAuB,GAAG,eAAgB,CAAC,MAAM;EACnD,IAAIC,wBAAwB,GAAG,KAAK;EACpC,IAAI;IACFzB,GAAG,CAACe,gBAAgB,CAClB,GAAG,EACH,IAAI,EACJlF,MAAM,CAACC,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;MACnCK,GAAGA,CAAA,EAAG;QACJsF,wBAAwB,GAAG,IAAI;MACjC;IACF,CAAC,CACH,CAAC;EACH,CAAC,CAAC,OAAOtD,CAAC,EAAE,CACZ;EACA,OAAOsD,wBAAwB;AACjC,CAAC,EAAE,CAAC;AACJ,IAAIC,cAAc,GAAIC,CAAC,IAAKjE,OAAO,CAACkE,OAAO,CAACD,CAAC,CAAC;AAC9C,IAAIE,gCAAgC,GAAG,eAAgB,CAAC,MAAM;EAC5D,IAAI;IACF,IAAIC,aAAa,CAAC,CAAC;IACnB,OAAO,OAAO,IAAIA,aAAa,CAAC,CAAC,CAACC,WAAW,KAAK,UAAU;EAC9D,CAAC,CAAC,OAAO5D,CAAC,EAAE,CACZ;EACA,OAAO,KAAK;AACd,CAAC,EAAE,CAAC;AACJ,IAAI6D,YAAY,GAAG,KAAK;AACxB,IAAIC,aAAa,GAAG,EAAE;AACtB,IAAIC,cAAc,GAAG,EAAE;AACvB,IAAIC,SAAS,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAMC,EAAE,IAAK;EACxCF,KAAK,CAACG,IAAI,CAACD,EAAE,CAAC;EACd,IAAI,CAACN,YAAY,EAAE;IACjBA,YAAY,GAAG,IAAI;IACnB,IAAIK,KAAK,IAAIhC,GAAG,CAACjD,OAAO,GAAG,CAAC,CAAC,iBAAiB;MAC5CoF,QAAQ,CAACC,KAAK,CAAC;IACjB,CAAC,MAAM;MACLpC,GAAG,CAACI,GAAG,CAACgC,KAAK,CAAC;IAChB;EACF;AACF,CAAC;AACD,IAAIC,OAAO,GAAIN,KAAK,IAAK;EACvB,KAAK,IAAIO,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGP,KAAK,CAACQ,MAAM,EAAED,EAAE,EAAE,EAAE;IACxC,IAAI;MACFP,KAAK,CAACO,EAAE,CAAC,CAACE,WAAW,CAACC,GAAG,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAO3E,CAAC,EAAE;MACVD,YAAY,CAACC,CAAC,CAAC;IACjB;EACF;EACAiE,KAAK,CAACQ,MAAM,GAAG,CAAC;AAClB,CAAC;AACD,IAAIH,KAAK,GAAGA,CAAA,KAAM;EAChBC,OAAO,CAACT,aAAa,CAAC;EACtB;IACES,OAAO,CAACR,cAAc,CAAC;IACvB,IAAIF,YAAY,GAAGC,aAAa,CAACW,MAAM,GAAG,CAAC,EAAE;MAC3CvC,GAAG,CAACI,GAAG,CAACgC,KAAK,CAAC;IAChB;EACF;AACF,CAAC;AACD,IAAID,QAAQ,GAAIF,EAAE,IAAKZ,cAAc,CAAC,CAAC,CAAC1C,IAAI,CAACsD,EAAE,CAAC;AAChD,IAAIS,QAAQ,GAAG,eAAgBZ,SAAS,CAACF,aAAa,EAAE,KAAK,CAAC;AAC9D,IAAIe,SAAS,GAAG,eAAgBb,SAAS,CAACD,cAAc,EAAE,IAAI,CAAC;;AAE/D;AACA,IAAIe,YAAY,GAAIC,IAAI,IAAK;EAC3B,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAACF,IAAI,EAAE7C,GAAG,CAACC,cAAc,CAAC;EAClD,OAAO6C,QAAQ,CAACE,MAAM,KAAKvD,GAAG,CAACwD,QAAQ,CAACD,MAAM,GAAGF,QAAQ,CAACI,IAAI,GAAGJ,QAAQ,CAACK,QAAQ;AACpF,CAAC;;AAED;AACA,IAAIC,SAAS,GAAG,CAAC,CAAC;AAClB,IAAIC,MAAM,GAAG,4BAA4B;AACzC,IAAIC,OAAO,GAAG,8BAA8B;;AAE5C;AACA,IAAIC,KAAK,GAAIjC,CAAC,IAAKA,CAAC,IAAI,IAAI;AAC5B,IAAIkC,aAAa,GAAIC,CAAC,IAAK;EACzBA,CAAC,GAAG,OAAOA,CAAC;EACZ,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,UAAU;AAC3C,CAAC;;AAED;AACA,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EACtC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGD,IAAI,CAAC9D,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+D,EAAE,CAACG,aAAa,CAAC,wBAAwB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,YAAY,CAAC,SAAS,CAAC,KAAK,IAAI,GAAGF,EAAE,GAAG,KAAK,CAAC;AACzK;;AAEA;AACA,IAAIG,cAAc,GAAG,CAAC,CAAC;AACvBvI,QAAQ,CAACuI,cAAc,EAAE;EACvBC,GAAG,EAAEA,CAAA,KAAMA,GAAG;EACdC,GAAG,EAAEA,CAAA,KAAMA,GAAG;EACdC,EAAE,EAAEA,CAAA,KAAMA,EAAE;EACZC,MAAM,EAAEA,CAAA,KAAMA,MAAM;EACpBC,SAAS,EAAEA,CAAA,KAAMA;AACnB,CAAC,CAAC;AACF,IAAIF,EAAE,GAAIG,KAAK,KAAM;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,KAAK;EACZF;AACF,CAAC,CAAC;AACF,IAAIL,GAAG,GAAIK,KAAK,KAAM;EACpBC,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE,IAAI;EACXF;AACF,CAAC,CAAC;AACF,SAASJ,GAAGA,CAACO,MAAM,EAAEC,EAAE,EAAE;EACvB,IAAID,MAAM,CAACF,IAAI,EAAE;IACf,MAAMI,GAAG,GAAGD,EAAE,CAACD,MAAM,CAACH,KAAK,CAAC;IAC5B,IAAIK,GAAG,YAAYvH,OAAO,EAAE;MAC1B,OAAOuH,GAAG,CAACjG,IAAI,CAAEkG,MAAM,IAAKT,EAAE,CAACS,MAAM,CAAC,CAAC;IACzC,CAAC,MAAM;MACL,OAAOT,EAAE,CAACQ,GAAG,CAAC;IAChB;EACF;EACA,IAAIF,MAAM,CAACD,KAAK,EAAE;IAChB,MAAMF,KAAK,GAAGG,MAAM,CAACH,KAAK;IAC1B,OAAOL,GAAG,CAACK,KAAK,CAAC;EACnB;EACA,MAAM,uBAAuB;AAC/B;AACA,IAAIF,MAAM,GAAIK,MAAM,IAAK;EACvB,IAAIA,MAAM,CAACF,IAAI,EAAE;IACf,OAAOE,MAAM,CAACH,KAAK;EACrB,CAAC,MAAM;IACL,MAAMG,MAAM,CAACH,KAAK;EACpB;AACF,CAAC;AACD,IAAID,SAAS,GAAII,MAAM,IAAK;EAC1B,IAAIA,MAAM,CAACD,KAAK,EAAE;IAChB,OAAOC,MAAM,CAACH,KAAK;EACrB,CAAC,MAAM;IACL,MAAMG,MAAM,CAACH,KAAK;EACpB;AACF,CAAC;AACD,IAAIO,UAAU,GAAGA,CAACC,MAAM,EAAEC,OAAO,GAAG,EAAE,KAAK;EACzC;IACE,OAAO,MAAM;MACX;IACF,CAAC;EACH;AACF,CAAC;AACD,IAAIC,UAAU,GAAGA,CAACC,GAAG,EAAEC,WAAW,KAAK;EACrC;IACE,OAAO,MAAM;MACX;IACF,CAAC;EACH;AACF,CAAC;AACD,IAAIC,CAAC,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAE,GAAGC,QAAQ,KAAK;EAC5C,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIN,GAAG,GAAG,IAAI;EACd,IAAIO,QAAQ,GAAG,IAAI;EACnB,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,UAAU,GAAG,KAAK;EACtB,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,IAAI,GAAIC,CAAC,IAAK;IAClB,KAAK,IAAIxD,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGwD,CAAC,CAACvD,MAAM,EAAED,EAAE,EAAE,EAAE;MACpCkD,KAAK,GAAGM,CAAC,CAACxD,EAAE,CAAC;MACb,IAAIyD,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,EAAE;QACxBK,IAAI,CAACL,KAAK,CAAC;MACb,CAAC,MAAM,IAAIA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;QACtD,IAAIE,MAAM,GAAG,OAAOL,QAAQ,KAAK,UAAU,IAAI,CAAC7B,aAAa,CAACgC,KAAK,CAAC,EAAE;UACpEA,KAAK,GAAGS,MAAM,CAACT,KAAK,CAAC;QACvB;QACA,IAAIE,MAAM,IAAIC,UAAU,EAAE;UACxBC,aAAa,CAACA,aAAa,CAACrD,MAAM,GAAG,CAAC,CAAC,CAAC2D,MAAM,IAAIV,KAAK;QACzD,CAAC,MAAM;UACLI,aAAa,CAAC1D,IAAI,CAACwD,MAAM,GAAGS,QAAQ,CAAC,IAAI,EAAEX,KAAK,CAAC,GAAGA,KAAK,CAAC;QAC5D;QACAG,UAAU,GAAGD,MAAM;MACrB;IACF;EACF,CAAC;EACDG,IAAI,CAACN,QAAQ,CAAC;EACd,IAAID,SAAS,EAAE;IACb,IAAIA,SAAS,CAACJ,GAAG,EAAE;MACjBA,GAAG,GAAGI,SAAS,CAACJ,GAAG;IACrB;IACA,IAAII,SAAS,CAACzJ,IAAI,EAAE;MAClB4J,QAAQ,GAAGH,SAAS,CAACzJ,IAAI;IAC3B;IACA;MACE,MAAMuK,SAAS,GAAGd,SAAS,CAACe,SAAS,IAAIf,SAAS,CAACgB,KAAK;MACxD,IAAIF,SAAS,EAAE;QACbd,SAAS,CAACgB,KAAK,GAAG,OAAOF,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG5K,MAAM,CAAC+K,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,CAAEC,CAAC,IAAKL,SAAS,CAACK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MAC5H;IACF;EACF;EACA,IAAI,OAAOrB,QAAQ,KAAK,UAAU,EAAE;IAClC,OAAOA,QAAQ,CACbC,SAAS,KAAK,IAAI,GAAG,CAAC,CAAC,GAAGA,SAAS,EACnCM,aAAa,EACbe,WACF,CAAC;EACH;EACA,MAAMC,KAAK,GAAGT,QAAQ,CAACd,QAAQ,EAAE,IAAI,CAAC;EACtCuB,KAAK,CAACC,OAAO,GAAGvB,SAAS;EACzB,IAAIM,aAAa,CAACrD,MAAM,GAAG,CAAC,EAAE;IAC5BqE,KAAK,CAACE,UAAU,GAAGlB,aAAa;EAClC;EACA;IACEgB,KAAK,CAACG,KAAK,GAAG7B,GAAG;EACnB;EACA;IACE0B,KAAK,CAACI,MAAM,GAAGvB,QAAQ;EACzB;EACA,OAAOmB,KAAK;AACd,CAAC;AACD,IAAIT,QAAQ,GAAGA,CAACc,GAAG,EAAEC,IAAI,KAAK;EAC5B,MAAMN,KAAK,GAAG;IACZ7J,OAAO,EAAE,CAAC;IACVoK,KAAK,EAAEF,GAAG;IACVf,MAAM,EAAEgB,IAAI;IACZE,KAAK,EAAE,IAAI;IACXN,UAAU,EAAE;EACd,CAAC;EACD;IACEF,KAAK,CAACC,OAAO,GAAG,IAAI;EACtB;EACA;IACED,KAAK,CAACG,KAAK,GAAG,IAAI;EACpB;EACA;IACEH,KAAK,CAACI,MAAM,GAAG,IAAI;EACrB;EACA,OAAOJ,KAAK;AACd,CAAC;AACD,IAAIS,IAAI,GAAG,CAAC,CAAC;AACb,IAAIC,MAAM,GAAIC,IAAI,IAAKA,IAAI,IAAIA,IAAI,CAACJ,KAAK,KAAKE,IAAI;AAClD,IAAIV,WAAW,GAAG;EAChBa,OAAO,EAAEA,CAACjC,QAAQ,EAAEtD,EAAE,KAAKsD,QAAQ,CAACpB,GAAG,CAACsD,eAAe,CAAC,CAACD,OAAO,CAACvF,EAAE,CAAC;EACpEkC,GAAG,EAAEA,CAACoB,QAAQ,EAAEtD,EAAE,KAAKsD,QAAQ,CAACpB,GAAG,CAACsD,eAAe,CAAC,CAACtD,GAAG,CAAClC,EAAE,CAAC,CAACkC,GAAG,CAACuD,gBAAgB;AACnF,CAAC;AACD,IAAID,eAAe,GAAIF,IAAI,KAAM;EAC/BI,MAAM,EAAEJ,IAAI,CAACV,OAAO;EACpBe,SAAS,EAAEL,IAAI,CAACT,UAAU;EAC1Be,IAAI,EAAEN,IAAI,CAACR,KAAK;EAChBe,KAAK,EAAEP,IAAI,CAACP,MAAM;EAClBe,IAAI,EAAER,IAAI,CAACJ,KAAK;EAChBa,KAAK,EAAET,IAAI,CAACrB;AACd,CAAC,CAAC;AACF,IAAIwB,gBAAgB,GAAIH,IAAI,IAAK;EAC/B,IAAI,OAAOA,IAAI,CAACQ,IAAI,KAAK,UAAU,EAAE;IACnC,MAAMzC,SAAS,GAAG;MAAE,GAAGiC,IAAI,CAACI;IAAO,CAAC;IACpC,IAAIJ,IAAI,CAACM,IAAI,EAAE;MACbvC,SAAS,CAACJ,GAAG,GAAGqC,IAAI,CAACM,IAAI;IAC3B;IACA,IAAIN,IAAI,CAACO,KAAK,EAAE;MACdxC,SAAS,CAACzJ,IAAI,GAAG0L,IAAI,CAACO,KAAK;IAC7B;IACA,OAAO1C,CAAC,CAACmC,IAAI,CAACQ,IAAI,EAAEzC,SAAS,EAAE,IAAGiC,IAAI,CAACK,SAAS,IAAI,EAAE,EAAC;EACzD;EACA,MAAMhB,KAAK,GAAGT,QAAQ,CAACoB,IAAI,CAACQ,IAAI,EAAER,IAAI,CAACS,KAAK,CAAC;EAC7CpB,KAAK,CAACC,OAAO,GAAGU,IAAI,CAACI,MAAM;EAC3Bf,KAAK,CAACE,UAAU,GAAGS,IAAI,CAACK,SAAS;EACjChB,KAAK,CAACG,KAAK,GAAGQ,IAAI,CAACM,IAAI;EACvBjB,KAAK,CAACI,MAAM,GAAGO,IAAI,CAACO,KAAK;EACzB,OAAOlB,KAAK;AACd,CAAC;;AAED;AACA,IAAIqB,uBAAuB,GAAGA,CAACC,OAAO,EAAElD,OAAO,EAAEmD,MAAM,EAAE1L,OAAO,KAAK;EACnE,MAAM2L,UAAU,GAAGtD,UAAU,CAAC,eAAe,EAAEE,OAAO,CAAC;EACvD,MAAMqD,UAAU,GAAGH,OAAO,CAACG,UAAU;EACrC,MAAMC,gBAAgB,GAAG,EAAE;EAC3B,MAAMC,SAAS,GAAG,EAAE;EACpB,MAAMC,eAAe,GAAGH,UAAU,GAAG,EAAE,GAAG,IAAI;EAC9C,MAAMzB,KAAK,GAAGnK,OAAO,CAACgM,OAAO,GAAGtC,QAAQ,CAACnB,OAAO,EAAE,IAAI,CAAC;EACvD,IAAI,CAAChF,GAAG,CAAC0I,aAAa,EAAE;IACtBC,yBAAyB,CAAChJ,GAAG,CAACiJ,IAAI,EAAE5I,GAAG,CAAC0I,aAAa,GAAG,eAAgB,IAAIvL,GAAG,CAAC,CAAC,CAAC;EACpF;EACA+K,OAAO,CAAC/I,UAAU,CAAC,GAAGgJ,MAAM;EAC5BD,OAAO,CAACW,eAAe,CAAC1J,UAAU,CAAC;EACnC2J,aAAa,CAAClC,KAAK,EAAE0B,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,EAAEN,OAAO,EAAEA,OAAO,EAAEC,MAAM,CAAC;EAC5FG,gBAAgB,CAACnE,GAAG,CAAE2B,CAAC,IAAK;IAC1B,MAAMiD,aAAa,GAAGjD,CAAC,CAACkD,QAAQ,GAAG,GAAG,GAAGlD,CAAC,CAACmD,QAAQ;IACnD,MAAMC,eAAe,GAAGlJ,GAAG,CAAC0I,aAAa,CAAC5M,GAAG,CAACiN,aAAa,CAAC;IAC5D,MAAMxB,IAAI,GAAGzB,CAAC,CAACsB,KAAK;IACpB,IAAI8B,eAAe,IAAIhI,cAAc,IAAIgI,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;MACvEA,eAAe,CAACC,UAAU,CAACC,YAAY,CAAC7B,IAAI,EAAE2B,eAAe,CAACG,WAAW,CAAC;IAC5E;IACA,IAAI,CAAChB,UAAU,EAAE;MACfd,IAAI,CAAC,MAAM,CAAC,GAAGvC,OAAO;MACtB,IAAIkE,eAAe,EAAE;QACnB3B,IAAI,CAAC,MAAM,CAAC,GAAG2B,eAAe;QAC9B3B,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAGA,IAAI;MAC7B;IACF;IACAvH,GAAG,CAAC0I,aAAa,CAACY,MAAM,CAACP,aAAa,CAAC;EACzC,CAAC,CAAC;EACF,IAAIV,UAAU,EAAE;IACdG,eAAe,CAACrE,GAAG,CAAEoF,cAAc,IAAK;MACtC,IAAIA,cAAc,EAAE;QAClBlB,UAAU,CAACmB,WAAW,CAACD,cAAc,CAAC;MACxC;IACF,CAAC,CAAC;EACJ;EACAnB,UAAU,CAAC,CAAC;AACd,CAAC;AACD,IAAIU,aAAa,GAAGA,CAACW,WAAW,EAAEnB,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,EAAEN,OAAO,EAAEX,IAAI,EAAEY,MAAM,KAAK;EACxG,IAAIuB,aAAa;EACjB,IAAIC,WAAW;EACf,IAAIC,UAAU;EACd,IAAItH,EAAE;EACN,IAAIiF,IAAI,CAACsC,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACzCH,aAAa,GAAGnC,IAAI,CAACvD,YAAY,CAAC3E,gBAAgB,CAAC;IACnD,IAAIqK,aAAa,EAAE;MACjBC,WAAW,GAAGD,aAAa,CAACI,KAAK,CAAC,GAAG,CAAC;MACtC,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAKxB,MAAM,IAAIwB,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACvDC,UAAU,GAAG;UACX7M,OAAO,EAAE,CAAC;UACViM,QAAQ,EAAEW,WAAW,CAAC,CAAC,CAAC;UACxBV,QAAQ,EAAEU,WAAW,CAAC,CAAC,CAAC;UACxBI,OAAO,EAAEJ,WAAW,CAAC,CAAC,CAAC;UACvBK,OAAO,EAAEL,WAAW,CAAC,CAAC,CAAC;UACvBxC,KAAK,EAAEI,IAAI,CAACvC,OAAO,CAACiF,WAAW,CAAC,CAAC;UACjC7C,KAAK,EAAEG,IAAI;UACXV,OAAO,EAAE,IAAI;UACbC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,IAAI;UACZd,MAAM,EAAE;QACV,CAAC;QACDoC,gBAAgB,CAACpG,IAAI,CAAC0H,UAAU,CAAC;QACjCrC,IAAI,CAACsB,eAAe,CAACxJ,gBAAgB,CAAC;QACtC,IAAI,CAACoK,WAAW,CAAC3C,UAAU,EAAE;UAC3B2C,WAAW,CAAC3C,UAAU,GAAG,EAAE;QAC7B;QACA2C,WAAW,CAAC3C,UAAU,CAAC8C,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU;QACvDH,WAAW,GAAGG,UAAU;QACxB,IAAIpB,eAAe,IAAIoB,UAAU,CAACG,OAAO,KAAK,GAAG,EAAE;UACjDvB,eAAe,CAACoB,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU,CAACxC,KAAK;QACxD;MACF;IACF;IACA,IAAIG,IAAI,CAACc,UAAU,EAAE;MACnB,KAAK/F,EAAE,GAAGiF,IAAI,CAACc,UAAU,CAAC6B,UAAU,CAAC3H,MAAM,GAAG,CAAC,EAAED,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;QAC9DwG,aAAa,CACXW,WAAW,EACXnB,gBAAgB,EAChBC,SAAS,EACTC,eAAe,EACfN,OAAO,EACPX,IAAI,CAACc,UAAU,CAAC6B,UAAU,CAAC5H,EAAE,CAAC,EAC9B6F,MACF,CAAC;MACH;IACF;IACA,KAAK7F,EAAE,GAAGiF,IAAI,CAAC2C,UAAU,CAAC3H,MAAM,GAAG,CAAC,EAAED,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;MACnDwG,aAAa,CACXW,WAAW,EACXnB,gBAAgB,EAChBC,SAAS,EACTC,eAAe,EACfN,OAAO,EACPX,IAAI,CAAC2C,UAAU,CAAC5H,EAAE,CAAC,EACnB6F,MACF,CAAC;IACH;EACF,CAAC,MAAM,IAAIZ,IAAI,CAACsC,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IAChDF,WAAW,GAAGpC,IAAI,CAAC4C,SAAS,CAACL,KAAK,CAAC,GAAG,CAAC;IACvC,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAKxB,MAAM,IAAIwB,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACvDD,aAAa,GAAGC,WAAW,CAAC,CAAC,CAAC;MAC9BC,UAAU,GAAG;QACX7M,OAAO,EAAE,CAAC;QACViM,QAAQ,EAAEW,WAAW,CAAC,CAAC,CAAC;QACxBV,QAAQ,EAAEU,WAAW,CAAC,CAAC,CAAC;QACxBI,OAAO,EAAEJ,WAAW,CAAC,CAAC,CAAC;QACvBK,OAAO,EAAEL,WAAW,CAAC,CAAC,CAAC;QACvBvC,KAAK,EAAEG,IAAI;QACXV,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZG,KAAK,EAAE,IAAI;QACXjB,MAAM,EAAE;MACV,CAAC;MACD,IAAIwD,aAAa,KAAKxK,YAAY,EAAE;QAClC0K,UAAU,CAACxC,KAAK,GAAGG,IAAI,CAAC8B,WAAW;QACnC,IAAIO,UAAU,CAACxC,KAAK,IAAIwC,UAAU,CAACxC,KAAK,CAACyC,QAAQ,KAAK,CAAC,CAAC,gBAAgB;UACtED,UAAU,CAAC1D,MAAM,GAAG0D,UAAU,CAACxC,KAAK,CAACgD,WAAW;UAChD9B,gBAAgB,CAACpG,IAAI,CAAC0H,UAAU,CAAC;UACjCrC,IAAI,CAAC8C,MAAM,CAAC,CAAC;UACb,IAAI,CAACZ,WAAW,CAAC3C,UAAU,EAAE;YAC3B2C,WAAW,CAAC3C,UAAU,GAAG,EAAE;UAC7B;UACA2C,WAAW,CAAC3C,UAAU,CAAC8C,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU;UACvD,IAAIpB,eAAe,IAAIoB,UAAU,CAACG,OAAO,KAAK,GAAG,EAAE;YACjDvB,eAAe,CAACoB,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU,CAACxC,KAAK;UACxD;QACF;MACF,CAAC,MAAM,IAAIwC,UAAU,CAACZ,QAAQ,KAAKb,MAAM,EAAE;QACzC,IAAIuB,aAAa,KAAKzK,YAAY,EAAE;UAClC2K,UAAU,CAACzC,KAAK,GAAG,MAAM;UACzB,IAAIwC,WAAW,CAAC,CAAC,CAAC,EAAE;YAClBpC,IAAI,CAAC,MAAM,CAAC,GAAGqC,UAAU,CAAC5C,MAAM,GAAG2C,WAAW,CAAC,CAAC,CAAC;UACnD,CAAC,MAAM;YACLpC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;UACnB;UACAA,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;UACnB,IAAIiB,eAAe,EAAE;YACnBoB,UAAU,CAACxC,KAAK,GAAGzH,GAAG,CAAC2K,aAAa,CAACV,UAAU,CAACzC,KAAK,CAAC;YACtD,IAAIyC,UAAU,CAAC5C,MAAM,EAAE;cACrB4C,UAAU,CAACxC,KAAK,CAACmD,YAAY,CAAC,MAAM,EAAEX,UAAU,CAAC5C,MAAM,CAAC;YAC1D;YACAO,IAAI,CAAC4B,UAAU,CAACC,YAAY,CAACQ,UAAU,CAACxC,KAAK,EAAEG,IAAI,CAAC;YACpDA,IAAI,CAAC8C,MAAM,CAAC,CAAC;YACb,IAAIT,UAAU,CAACG,OAAO,KAAK,GAAG,EAAE;cAC9BvB,eAAe,CAACoB,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU,CAACxC,KAAK;YACxD;UACF;UACAmB,SAAS,CAACrG,IAAI,CAAC0H,UAAU,CAAC;UAC1B,IAAI,CAACH,WAAW,CAAC3C,UAAU,EAAE;YAC3B2C,WAAW,CAAC3C,UAAU,GAAG,EAAE;UAC7B;UACA2C,WAAW,CAAC3C,UAAU,CAAC8C,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU;QACzD,CAAC,MAAM,IAAIF,aAAa,KAAK3K,cAAc,EAAE;UAC3C,IAAIyJ,eAAe,EAAE;YACnBjB,IAAI,CAAC8C,MAAM,CAAC,CAAC;UACf,CAAC,MAAM;YACLnC,OAAO,CAAC,MAAM,CAAC,GAAGX,IAAI;YACtBA,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;UACrB;QACF;MACF;IACF;EACF,CAAC,MAAM,IAAIkC,WAAW,IAAIA,WAAW,CAACtC,KAAK,KAAK,OAAO,EAAE;IACvD,MAAMP,KAAK,GAAGT,QAAQ,CAAC,IAAI,EAAEoB,IAAI,CAAC6C,WAAW,CAAC;IAC9CxD,KAAK,CAACQ,KAAK,GAAGG,IAAI;IAClBX,KAAK,CAACoD,OAAO,GAAG,GAAG;IACnBP,WAAW,CAAC3C,UAAU,GAAG,CAACF,KAAK,CAAC;EAClC;AACF,CAAC;AACD,IAAI+B,yBAAyB,GAAGA,CAACpB,IAAI,EAAEiD,WAAW,KAAK;EACrD,IAAIjD,IAAI,CAACsC,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACzC,IAAIvH,EAAE,GAAG,CAAC;IACV,IAAIiF,IAAI,CAACc,UAAU,EAAE;MACnB,OAAO/F,EAAE,GAAGiF,IAAI,CAACc,UAAU,CAAC6B,UAAU,CAAC3H,MAAM,EAAED,EAAE,EAAE,EAAE;QACnDqG,yBAAyB,CAACpB,IAAI,CAACc,UAAU,CAAC6B,UAAU,CAAC5H,EAAE,CAAC,EAAEkI,WAAW,CAAC;MACxE;IACF;IACA,KAAKlI,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGiF,IAAI,CAAC2C,UAAU,CAAC3H,MAAM,EAAED,EAAE,EAAE,EAAE;MAC9CqG,yBAAyB,CAACpB,IAAI,CAAC2C,UAAU,CAAC5H,EAAE,CAAC,EAAEkI,WAAW,CAAC;IAC7D;EACF,CAAC,MAAM,IAAIjD,IAAI,CAACsC,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IAChD,MAAMF,WAAW,GAAGpC,IAAI,CAAC4C,SAAS,CAACL,KAAK,CAAC,GAAG,CAAC;IAC7C,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAK3K,eAAe,EAAE;MACtCwL,WAAW,CAAC9N,GAAG,CAACiN,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,WAAW,CAAC,CAAC,CAAC,EAAEpC,IAAI,CAAC;MAC5DA,IAAI,CAAC4C,SAAS,GAAG,EAAE;MACnB5C,IAAI,CAAC,MAAM,CAAC,GAAGoC,WAAW,CAAC,CAAC,CAAC;IAC/B;EACF;AACF,CAAC;;AAED;AACA,IAAIc,WAAW,GAAI9M,GAAG,IAAKmB,mBAAmB,CAACqF,GAAG,CAAEhE,EAAE,IAAKA,EAAE,CAACxC,GAAG,CAAC,CAAC,CAAC+M,IAAI,CAAEC,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;AACpF,IAAIC,OAAO,GAAIC,OAAO,IAAK/L,mBAAmB,CAACoD,IAAI,CAAC2I,OAAO,CAAC;AAC5D,IAAIC,OAAO,GAAIxO,GAAG,IAAKD,UAAU,CAACC,GAAG,CAAC,CAACyO,UAAU;AACjD,IAAIC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,QAAQ,KAAK;EAChD,IAAID,SAAS,IAAI,IAAI,IAAI,CAACzH,aAAa,CAACyH,SAAS,CAAC,EAAE;IAClD,IAAIC,QAAQ,GAAG,CAAC,CAAC,eAAe;MAC9B,OAAOD,SAAS,KAAK,OAAO,GAAG,KAAK,GAAGA,SAAS,KAAK,EAAE,IAAI,CAAC,CAACA,SAAS;IACxE;IACA,IAAIC,QAAQ,GAAG,CAAC,CAAC,cAAc;MAC7B,OAAOC,UAAU,CAACF,SAAS,CAAC;IAC9B;IACA,IAAIC,QAAQ,GAAG,CAAC,CAAC,cAAc;MAC7B,OAAOjF,MAAM,CAACgF,SAAS,CAAC;IAC1B;IACA,OAAOA,SAAS;EAClB;EACA,OAAOA,SAAS;AAClB,CAAC;AACD,IAAIG,UAAU,GAAI9O,GAAG,IAAKD,UAAU,CAACC,GAAG,CAAC,CAACU,aAAa;;AAEvD;AACA,IAAIqO,WAAW,GAAGA,CAAC/O,GAAG,EAAET,IAAI,EAAEyP,KAAK,KAAK;EACtC,MAAM3N,GAAG,GAAGyN,UAAU,CAAC9O,GAAG,CAAC;EAC3B,OAAO;IACLiP,IAAI,EAAGC,MAAM,IAAK;MAChB,OAAOC,SAAS,CAAC9N,GAAG,EAAE9B,IAAI,EAAE;QAC1B6P,OAAO,EAAE,CAAC,EAAEJ,KAAK,GAAG,CAAC,CAAC,cAAc;QACpCK,QAAQ,EAAE,CAAC,EAAEL,KAAK,GAAG,CAAC,CAAC,eAAe;QACtCM,UAAU,EAAE,CAAC,EAAEN,KAAK,GAAG,CAAC,CAAC,kBAAkB;QAC3CE;MACF,CAAC,CAAC;IACJ;EACF,CAAC;AACH,CAAC;AACD,IAAIC,SAAS,GAAGA,CAAC9N,GAAG,EAAE9B,IAAI,EAAE4E,IAAI,KAAK;EACnC,MAAMoL,EAAE,GAAG7L,GAAG,CAACa,EAAE,CAAChF,IAAI,EAAE4E,IAAI,CAAC;EAC7B9C,GAAG,CAACmO,aAAa,CAACD,EAAE,CAAC;EACrB,OAAOA,EAAE;AACX,CAAC;AACD,IAAIE,iBAAiB,GAAG,eAAgB,IAAI3P,OAAO,CAAC,CAAC;AACrD,IAAI4P,aAAa,GAAGA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,KAAK;EAClD,IAAI7R,KAAK,GAAGuE,MAAM,CAAC/C,GAAG,CAACmQ,QAAQ,CAAC;EAChC,IAAIzK,gCAAgC,IAAI2K,OAAO,EAAE;IAC/C7R,KAAK,GAAGA,KAAK,IAAI,IAAImH,aAAa,CAAC,CAAC;IACpC,IAAI,OAAOnH,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAG4R,OAAO;IACjB,CAAC,MAAM;MACL5R,KAAK,CAACoH,WAAW,CAACwK,OAAO,CAAC;IAC5B;EACF,CAAC,MAAM;IACL5R,KAAK,GAAG4R,OAAO;EACjB;EACArN,MAAM,CAACnC,GAAG,CAACuP,QAAQ,EAAE3R,KAAK,CAAC;AAC7B,CAAC;AACD,IAAI8R,QAAQ,GAAGA,CAACC,kBAAkB,EAAEvP,OAAO,EAAE1D,IAAI,KAAK;EACpD,IAAIwK,EAAE;EACN,MAAMqI,QAAQ,GAAGK,UAAU,CAACxP,OAAO,EAAE1D,IAAI,CAAC;EAC1C,MAAMkB,KAAK,GAAGuE,MAAM,CAAC/C,GAAG,CAACmQ,QAAQ,CAAC;EAClCI,kBAAkB,GAAGA,kBAAkB,CAACxC,QAAQ,KAAK,EAAE,CAAC,yBAAyBwC,kBAAkB,GAAG1M,GAAG;EACzG,IAAIrF,KAAK,EAAE;IACT,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B+R,kBAAkB,GAAGA,kBAAkB,CAACxM,IAAI,IAAIwM,kBAAkB;MAClE,IAAIE,aAAa,GAAGR,iBAAiB,CAACjQ,GAAG,CAACuQ,kBAAkB,CAAC;MAC7D,IAAIG,QAAQ;MACZ,IAAI,CAACD,aAAa,EAAE;QAClBR,iBAAiB,CAACrP,GAAG,CAAC2P,kBAAkB,EAAEE,aAAa,GAAG,eAAgB,IAAIE,GAAG,CAAC,CAAC,CAAC;MACtF;MACA,IAAI,CAACF,aAAa,CAACG,GAAG,CAACT,QAAQ,CAAC,EAAE;QAChC,IAAII,kBAAkB,CAACM,IAAI,KAAKH,QAAQ,GAAGH,kBAAkB,CAACtI,aAAa,CAAC,IAAI3E,iBAAiB,KAAK6M,QAAQ,IAAI,CAAC,CAAC,EAAE;UACpHO,QAAQ,CAACI,SAAS,GAAGtS,KAAK;QAC5B,CAAC,MAAM;UACLkS,QAAQ,GAAG7M,GAAG,CAAC2K,aAAa,CAAC,OAAO,CAAC;UACrCkC,QAAQ,CAACI,SAAS,GAAGtS,KAAK;UAC1B,MAAMuS,KAAK,GAAG,CAACjJ,EAAE,GAAG5D,GAAG,CAAC8M,OAAO,KAAK,IAAI,GAAGlJ,EAAE,GAAGF,wBAAwB,CAAC/D,GAAG,CAAC;UAC7E,IAAIkN,KAAK,IAAI,IAAI,EAAE;YACjBL,QAAQ,CAACjC,YAAY,CAAC,OAAO,EAAEsC,KAAK,CAAC;UACvC;UACA,MAAME,WAAW;UACf;AACZ;AACA;UACY,EAAEjQ,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,6BAA6B;UAAI;AACnE;AACA;UACYD,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,gCAAgCsP,kBAAkB,CAAChH,QAAQ,KAAK,MACrF;UACD,IAAI0H,WAAW,EAAE;YACfV,kBAAkB,CAACjD,YAAY,CAACoD,QAAQ,EAAEH,kBAAkB,CAACtI,aAAa,CAAC,MAAM,CAAC,CAAC;UACrF;QACF;QACA,IAAIjH,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,yBAAyB;UAC/CyP,QAAQ,CAACI,SAAS,IAAIrN,WAAW;QACnC;QACA,IAAIgN,aAAa,EAAE;UACjBA,aAAa,CAACS,GAAG,CAACf,QAAQ,CAAC;QAC7B;MACF;IACF,CAAC,MAAM,IAAI,CAACI,kBAAkB,CAACY,kBAAkB,CAACC,QAAQ,CAAC5S,KAAK,CAAC,EAAE;MACjE+R,kBAAkB,CAACY,kBAAkB,GAAG,CAAC,GAAGZ,kBAAkB,CAACY,kBAAkB,EAAE3S,KAAK,CAAC;IAC3F;EACF;EACA,OAAO2R,QAAQ;AACjB,CAAC;AACD,IAAIxV,YAAY,GAAIgG,OAAO,IAAK;EAC9B,MAAMK,OAAO,GAAGL,OAAO,CAACQ,SAAS;EACjC,MAAMU,GAAG,GAAGlB,OAAO,CAACO,aAAa;EACjC,MAAMsO,KAAK,GAAGxO,OAAO,CAACC,OAAO;EAC7B,MAAMoQ,eAAe,GAAGrI,UAAU,CAAC,cAAc,EAAEhI,OAAO,CAACwB,SAAS,CAAC;EACrE,MAAM2N,QAAQ,GAAGG,QAAQ,CACvBzO,GAAG,CAAC0K,UAAU,GAAG1K,GAAG,CAAC0K,UAAU,GAAG1K,GAAG,CAACyP,WAAW,CAAC,CAAC,EACnDtQ,OAAO,EACPL,OAAO,CAACsO,UACV,CAAC;EACD,IAAIO,KAAK,GAAG,EAAE,CAAC,kCAAkCA,KAAK,GAAG,CAAC,CAAC,8BAA8B;IACvF3N,GAAG,CAAC,MAAM,CAAC,GAAGsO,QAAQ;IACtBtO,GAAG,CAAC0P,SAAS,CAACL,GAAG,CAACf,QAAQ,GAAG,IAAI,CAAC;IAClC,IAAIX,KAAK,GAAG,CAAC,CAAC,8BAA8B;MAC1C3N,GAAG,CAAC0P,SAAS,CAACL,GAAG,CAACf,QAAQ,GAAG,IAAI,CAAC;IACpC;EACF;EACAkB,eAAe,CAAC,CAAC;AACnB,CAAC;AACD,IAAIb,UAAU,GAAGA,CAACgB,GAAG,EAAElU,IAAI,KAAK,KAAK,IAAIA,IAAI,IAAIkU,GAAG,CAACvQ,OAAO,GAAG,EAAE,CAAC,gBAAgBuQ,GAAG,CAAChP,SAAS,GAAG,GAAG,GAAGlF,IAAI,GAAGkU,GAAG,CAAChP,SAAS,CAAC;AAC7H,IAAIiP,WAAW,GAAGA,CAAC5P,GAAG,EAAEC,UAAU,EAAE4P,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAEpC,KAAK,KAAK;EACvE,IAAIkC,QAAQ,KAAKC,QAAQ,EAAE;IACzB,IAAIE,MAAM,GAAGjQ,iBAAiB,CAACC,GAAG,EAAEC,UAAU,CAAC;IAC/C,IAAIgQ,EAAE,GAAGhQ,UAAU,CAACqM,WAAW,CAAC,CAAC;IACjC,IAAIrM,UAAU,KAAK,OAAO,EAAE;MAC1B,MAAMyP,SAAS,GAAG1P,GAAG,CAAC0P,SAAS;MAC/B,MAAMQ,UAAU,GAAGC,cAAc,CAACN,QAAQ,CAAC;MAC3C,MAAMO,UAAU,GAAGD,cAAc,CAACL,QAAQ,CAAC;MAC3CJ,SAAS,CAAChD,MAAM,CAAC,GAAGwD,UAAU,CAACrH,MAAM,CAAEV,CAAC,IAAKA,CAAC,IAAI,CAACiI,UAAU,CAACb,QAAQ,CAACpH,CAAC,CAAC,CAAC,CAAC;MAC3EuH,SAAS,CAACL,GAAG,CAAC,GAAGe,UAAU,CAACvH,MAAM,CAAEV,CAAC,IAAKA,CAAC,IAAI,CAAC+H,UAAU,CAACX,QAAQ,CAACpH,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC,MAAM,IAAIlI,UAAU,KAAK,OAAO,EAAE;MACjC;QACE,KAAK,MAAMrE,IAAI,IAAIiU,QAAQ,EAAE;UAC3B,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAAClU,IAAI,CAAC,IAAI,IAAI,EAAE;YACvC,IAAIA,IAAI,CAAC2T,QAAQ,CAAC,GAAG,CAAC,EAAE;cACtBvP,GAAG,CAACrD,KAAK,CAAC0T,cAAc,CAACzU,IAAI,CAAC;YAChC,CAAC,MAAM;cACLoE,GAAG,CAACrD,KAAK,CAACf,IAAI,CAAC,GAAG,EAAE;YACtB;UACF;QACF;MACF;MACA,KAAK,MAAMA,IAAI,IAAIkU,QAAQ,EAAE;QAC3B,IAAI,CAACD,QAAQ,IAAIC,QAAQ,CAAClU,IAAI,CAAC,KAAKiU,QAAQ,CAACjU,IAAI,CAAC,EAAE;UAClD,IAAIA,IAAI,CAAC2T,QAAQ,CAAC,GAAG,CAAC,EAAE;YACtBvP,GAAG,CAACrD,KAAK,CAAC2T,WAAW,CAAC1U,IAAI,EAAEkU,QAAQ,CAAClU,IAAI,CAAC,CAAC;UAC7C,CAAC,MAAM;YACLoE,GAAG,CAACrD,KAAK,CAACf,IAAI,CAAC,GAAGkU,QAAQ,CAAClU,IAAI,CAAC;UAClC;QACF;MACF;IACF,CAAC,MAAM,IAAIqE,UAAU,KAAK,KAAK,EAAE,CAAC,KAAM,IAAIA,UAAU,KAAK,KAAK,EAAE;MAChE,IAAI6P,QAAQ,EAAE;QACZA,QAAQ,CAAC9P,GAAG,CAAC;MACf;IACF,CAAC,MAAM,IAAK,CAACgQ,MAAM,IAAM/P,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACvE,IAAIA,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACzBA,UAAU,GAAGA,UAAU,CAACsQ,KAAK,CAAC,CAAC,CAAC;MAClC,CAAC,MAAM,IAAIxQ,iBAAiB,CAAC+B,GAAG,EAAEmO,EAAE,CAAC,EAAE;QACrChQ,UAAU,GAAGgQ,EAAE,CAACM,KAAK,CAAC,CAAC,CAAC;MAC1B,CAAC,MAAM;QACLtQ,UAAU,GAAGgQ,EAAE,CAAC,CAAC,CAAC,GAAGhQ,UAAU,CAACsQ,KAAK,CAAC,CAAC,CAAC;MAC1C;MACA,IAAIV,QAAQ,IAAIC,QAAQ,EAAE;QACxB,MAAMU,OAAO,GAAGvQ,UAAU,CAACwQ,QAAQ,CAACC,oBAAoB,CAAC;QACzDzQ,UAAU,GAAGA,UAAU,CAACW,OAAO,CAAC+P,mBAAmB,EAAE,EAAE,CAAC;QACxD,IAAId,QAAQ,EAAE;UACZxN,GAAG,CAACW,GAAG,CAAChD,GAAG,EAAEC,UAAU,EAAE4P,QAAQ,EAAEW,OAAO,CAAC;QAC7C;QACA,IAAIV,QAAQ,EAAE;UACZzN,GAAG,CAACM,GAAG,CAAC3C,GAAG,EAAEC,UAAU,EAAE6P,QAAQ,EAAEU,OAAO,CAAC;QAC7C;MACF;IACF,CAAC,MAAM;MACL,MAAMI,SAAS,GAAG/K,aAAa,CAACiK,QAAQ,CAAC;MACzC,IAAI,CAACE,MAAM,IAAIY,SAAS,IAAId,QAAQ,KAAK,IAAI,KAAK,CAACC,KAAK,EAAE;QACxD,IAAI;UACF,IAAI,CAAC/P,GAAG,CAACqH,OAAO,CAACkI,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC9B,MAAMsB,CAAC,GAAGf,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAGA,QAAQ;YAC1C,IAAI7P,UAAU,KAAK,MAAM,EAAE;cACzB+P,MAAM,GAAG,KAAK;YAChB,CAAC,MAAM,IAAIH,QAAQ,IAAI,IAAI,IAAI7P,GAAG,CAACC,UAAU,CAAC,IAAI4Q,CAAC,EAAE;cACnD7Q,GAAG,CAACC,UAAU,CAAC,GAAG4Q,CAAC;YACrB;UACF,CAAC,MAAM;YACL7Q,GAAG,CAACC,UAAU,CAAC,GAAG6P,QAAQ;UAC5B;QACF,CAAC,CAAC,OAAO3P,CAAC,EAAE,CACZ;MACF;MACA,IAAI2Q,KAAK,GAAG,KAAK;MACjB;QACE,IAAIb,EAAE,MAAMA,EAAE,GAAGA,EAAE,CAACrP,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,EAAE;UAC7CX,UAAU,GAAGgQ,EAAE;UACfa,KAAK,GAAG,IAAI;QACd;MACF;MACA,IAAIhB,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,KAAK,EAAE;QAC1C,IAAIA,QAAQ,KAAK,KAAK,IAAI9P,GAAG,CAACqG,YAAY,CAACpG,UAAU,CAAC,KAAK,EAAE,EAAE;UAC7D,IAAI6Q,KAAK,EAAE;YACT9Q,GAAG,CAAC+Q,iBAAiB,CAAClP,QAAQ,EAAE5B,UAAU,CAAC;UAC7C,CAAC,MAAM;YACLD,GAAG,CAACkL,eAAe,CAACjL,UAAU,CAAC;UACjC;QACF;MACF,CAAC,MAAM,IAAI,CAAC,CAAC+P,MAAM,IAAIrC,KAAK,GAAG,CAAC,CAAC,gBAAgBoC,KAAK,KAAK,CAACa,SAAS,EAAE;QACrEd,QAAQ,GAAGA,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAGA,QAAQ;QAC5C,IAAIgB,KAAK,EAAE;UACT9Q,GAAG,CAACgR,cAAc,CAACnP,QAAQ,EAAE5B,UAAU,EAAE6P,QAAQ,CAAC;QACpD,CAAC,MAAM;UACL9P,GAAG,CAAC4M,YAAY,CAAC3M,UAAU,EAAE6P,QAAQ,CAAC;QACxC;MACF;IACF;EACF;AACF,CAAC;AACD,IAAImB,mBAAmB,GAAG,IAAI;AAC9B,IAAId,cAAc,GAAIvJ,KAAK,IAAK,CAACA,KAAK,GAAG,EAAE,GAAGA,KAAK,CAACuF,KAAK,CAAC8E,mBAAmB,CAAC;AAC9E,IAAIP,oBAAoB,GAAG,SAAS;AACpC,IAAIC,mBAAmB,GAAG,IAAIO,MAAM,CAACR,oBAAoB,GAAG,GAAG,CAAC;;AAEhE;AACA,IAAIS,aAAa,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,KAAK;EACtD,MAAMtR,GAAG,GAAGqR,QAAQ,CAAC5H,KAAK,CAACyC,QAAQ,KAAK,EAAE,CAAC,0BAA0BmF,QAAQ,CAAC5H,KAAK,CAACuF,IAAI,GAAGqC,QAAQ,CAAC5H,KAAK,CAACuF,IAAI,GAAGqC,QAAQ,CAAC5H,KAAK;EAC/H,MAAM8H,aAAa,GAAGH,QAAQ,IAAIA,QAAQ,CAAClI,OAAO,IAAIzD,SAAS;EAC/D,MAAM+L,aAAa,GAAGH,QAAQ,CAACnI,OAAO,IAAIzD,SAAS;EACnD;IACE,KAAK,MAAMxF,UAAU,IAAIwR,eAAe,CAAC5T,MAAM,CAAC+K,IAAI,CAAC2I,aAAa,CAAC,CAAC,EAAE;MACpE,IAAI,EAAEtR,UAAU,IAAIuR,aAAa,CAAC,EAAE;QAClC5B,WAAW,CAAC5P,GAAG,EAAEC,UAAU,EAAEsR,aAAa,CAACtR,UAAU,CAAC,EAAE,KAAK,CAAC,EAAEqR,UAAU,EAAED,QAAQ,CAACjS,OAAO,CAAC;MAC/F;IACF;EACF;EACA,KAAK,MAAMa,UAAU,IAAIwR,eAAe,CAAC5T,MAAM,CAAC+K,IAAI,CAAC4I,aAAa,CAAC,CAAC,EAAE;IACpE5B,WAAW,CAAC5P,GAAG,EAAEC,UAAU,EAAEsR,aAAa,CAACtR,UAAU,CAAC,EAAEuR,aAAa,CAACvR,UAAU,CAAC,EAAEqR,UAAU,EAAED,QAAQ,CAACjS,OAAO,CAAC;EAClH;AACF,CAAC;AACD,SAASqS,eAAeA,CAACC,SAAS,EAAE;EAClC,OAAOA,SAAS,CAACnC,QAAQ,CAAC,KAAK,CAAC;EAC9B;EACA,CAAC,GAAGmC,SAAS,CAAC7I,MAAM,CAAE8I,IAAI,IAAKA,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK,CAAC;EAEtD;EACAD,SACD;AACH;;AAEA;AACA,IAAIE,OAAO;AACX,IAAIC,UAAU;AACd,IAAIC,WAAW;AACf,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,2BAA2B,GAAG,KAAK;AACvC,IAAIC,iBAAiB,GAAG,KAAK;AAC7B,IAAIC,SAAS,GAAG,KAAK;AACrB,IAAIC,SAAS,GAAGA,CAACC,cAAc,EAAEC,cAAc,EAAEC,UAAU,EAAEC,SAAS,KAAK;EACzE,IAAItM,EAAE;EACN,MAAMuM,SAAS,GAAGH,cAAc,CAAClJ,UAAU,CAACmJ,UAAU,CAAC;EACvD,IAAI3N,EAAE,GAAG,CAAC;EACV,IAAI3E,GAAG;EACP,IAAIyS,SAAS;EACb,IAAIC,QAAQ;EACZ,IAAI,CAACX,kBAAkB,EAAE;IACvBE,iBAAiB,GAAG,IAAI;IACxB,IAAIO,SAAS,CAAChJ,KAAK,KAAK,MAAM,EAAE;MAC9B,IAAIoI,OAAO,EAAE;QACXW,SAAS,CAAC7C,SAAS,CAACL,GAAG,CAACuC,OAAO,GAAG,IAAI,CAAC;MACzC;MACAY,SAAS,CAACpT,OAAO,IAAIoT,SAAS,CAACrJ,UAAU;MACvC;MACA;MACA,CAAC,CAAC;MAEF;MACA;MACA;MACA,CAAC,CAAC,qBACH;IACH;EACF;EACA,IAAIqJ,SAAS,CAACjK,MAAM,KAAK,IAAI,EAAE;IAC7BvI,GAAG,GAAGwS,SAAS,CAAC/I,KAAK,GAAGzH,GAAG,CAAC2Q,cAAc,CAACH,SAAS,CAACjK,MAAM,CAAC;EAC9D,CAAC,MAAM,IAAIiK,SAAS,CAACpT,OAAO,GAAG,CAAC,CAAC,uBAAuB;IACtDY,GAAG,GAAGwS,SAAS,CAAC/I,KAAK,GAAGzH,GAAG,CAAC2Q,cAAc,CAAC,EAAE,CAAC;EAChD,CAAC,MAAM;IACL,IAAI,CAACT,SAAS,EAAE;MACdA,SAAS,GAAGM,SAAS,CAAChJ,KAAK,KAAK,KAAK;IACvC;IACAxJ,GAAG,GAAGwS,SAAS,CAAC/I,KAAK,GAAGzH,GAAG,CAAC4Q,eAAe,CACzCV,SAAS,GAAGxM,MAAM,GAAGC,OAAO,EAC5B,CAACoM,kBAAkB,IAAItZ,KAAK,CAACgE,cAAc,IAAI+V,SAAS,CAACpT,OAAO,GAAG,CAAC,CAAC,uBAAuB,SAAS,GAAGoT,SAAS,CAAChJ,KACpH,CAAC;IACD,IAAI0I,SAAS,IAAIM,SAAS,CAAChJ,KAAK,KAAK,eAAe,EAAE;MACpD0I,SAAS,GAAG,KAAK;IACnB;IACA;MACEf,aAAa,CAAC,IAAI,EAAEqB,SAAS,EAAEN,SAAS,CAAC;IAC3C;IACA,MAAMW,QAAQ,GAAG7S,GAAG,CAACyP,WAAW,CAAC,CAAC;IAClC,MAAMqD,yBAAyB,GAAG,CAACD,QAAQ,CAACzM,aAAa,CAAC,MAAM,CAAC;IACjE,IAAI,CAAC0M,yBAAyB,IAAIra,KAAK,CAACyD,MAAM,IAAI0J,KAAK,CAACgM,OAAO,CAAC,IAAI5R,GAAG,CAAC,MAAM,CAAC,KAAK4R,OAAO,EAAE;MAC3F5R,GAAG,CAAC0P,SAAS,CAACL,GAAG,CAACrP,GAAG,CAAC,MAAM,CAAC,GAAG4R,OAAO,CAAC;IAC1C;IACA;MACEmB,qBAAqB,CAAC/S,GAAG,EAAEuS,SAAS,CAAC;IACvC;IACA,IAAIC,SAAS,CAACrJ,UAAU,EAAE;MACxB,KAAKxE,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG6N,SAAS,CAACrJ,UAAU,CAACvE,MAAM,EAAE,EAAED,EAAE,EAAE;QACnD8N,SAAS,GAAGN,SAAS,CAACC,cAAc,EAAEI,SAAS,EAAE7N,EAAE,EAAE3E,GAAG,CAAC;QACzD,IAAIyS,SAAS,EAAE;UACbzS,GAAG,CAAC6L,WAAW,CAAC4G,SAAS,CAAC;QAC5B;MACF;IACF;IACA;MACE,IAAID,SAAS,CAAChJ,KAAK,KAAK,KAAK,EAAE;QAC7B0I,SAAS,GAAG,KAAK;MACnB,CAAC,MAAM,IAAIlS,GAAG,CAACqH,OAAO,KAAK,eAAe,EAAE;QAC1C6K,SAAS,GAAG,IAAI;MAClB;IACF;EACF;EACAlS,GAAG,CAAC,MAAM,CAAC,GAAG8R,WAAW;EACzB;IACE,IAAIU,SAAS,CAACpT,OAAO,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC,sBAAsB,EAAE;MAC1EY,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI;MAClBA,GAAG,CAAC,MAAM,CAAC,GAAG6R,UAAU;MACxB7R,GAAG,CAAC,MAAM,CAAC,GAAGwS,SAAS,CAACnJ,MAAM,IAAI,EAAE;MACpCrJ,GAAG,CAAC,MAAM,CAAC,GAAG,CAACiG,EAAE,GAAGuM,SAAS,CAACtJ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGjD,EAAE,CAACtH,GAAG;MAChE+T,QAAQ,GAAGN,cAAc,IAAIA,cAAc,CAACjJ,UAAU,IAAIiJ,cAAc,CAACjJ,UAAU,CAACmJ,UAAU,CAAC;MAC/F,IAAII,QAAQ,IAAIA,QAAQ,CAAClJ,KAAK,KAAKgJ,SAAS,CAAChJ,KAAK,IAAI4I,cAAc,CAAC3I,KAAK,EAAE;QAC1E;UACEuJ,kBAAkB,CAACZ,cAAc,CAAC3I,KAAK,CAAC;QAC1C;MACF;IACF;EACF;EACA,OAAOzJ,GAAG;AACZ,CAAC;AACD,IAAIgT,kBAAkB,GAAIT,SAAS,IAAK;EACtClQ,GAAG,CAACjD,OAAO,IAAI,CAAC,CAAC;EACjB,MAAM4P,IAAI,GAAGuD,SAAS,CAACU,OAAO,CAACnB,WAAW,CAACxF,WAAW,CAAC,CAAC,CAAC;EACzD,IAAI0C,IAAI,IAAI,IAAI,EAAE;IAChB,MAAMkE,cAAc,GAAG9K,KAAK,CAAC+K,IAAI,CAACnE,IAAI,CAACzC,UAAU,CAAC,CAACQ,IAAI,CAAEpO,GAAG,IAAKA,GAAG,CAAC,MAAM,CAAC,CAAC;IAC7E,MAAMyU,cAAc,GAAGhL,KAAK,CAAC+K,IAAI,CAACZ,SAAS,CAAChG,UAAU,CAAC;IACvD,KAAK,MAAMkG,SAAS,IAAIS,cAAc,GAAGE,cAAc,CAACC,OAAO,CAAC,CAAC,GAAGD,cAAc,EAAE;MAClF,IAAIX,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAC7BhH,YAAY,CAACuD,IAAI,EAAEyD,SAAS,EAAES,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAG,IAAI,CAAC;QAC7ET,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;QAC1BR,iBAAiB,GAAG,IAAI;MAC1B;IACF;EACF;EACA5P,GAAG,CAACjD,OAAO,IAAI,CAAC,CAAC,CAAC;AACpB,CAAC;AACD,IAAIkU,yBAAyB,GAAGA,CAACf,SAAS,EAAEgB,SAAS,KAAK;EACxDlR,GAAG,CAACjD,OAAO,IAAI,CAAC,CAAC;EACjB,MAAMoU,iBAAiB,GAAGpL,KAAK,CAAC+K,IAAI,CAACZ,SAAS,CAAChG,UAAU,CAAC;EAC1D,IAAIgG,SAAS,CAAC,MAAM,CAAC,IAAI9Z,KAAK,CAACuB,qBAAqB,EAAE;IACpD,IAAI4P,IAAI,GAAG2I,SAAS;IACpB,OAAO3I,IAAI,GAAGA,IAAI,CAAC8B,WAAW,EAAE;MAC9B,IAAI9B,IAAI,IAAIA,IAAI,CAAC,MAAM,CAAC,KAAK2I,SAAS,CAAC,MAAM,CAAC,IAAI3I,IAAI,CAAC,MAAM,CAAC,KAAKkI,WAAW,EAAE;QAC9E0B,iBAAiB,CAACjP,IAAI,CAACqF,IAAI,CAAC;MAC9B;IACF;EACF;EACA,KAAK,IAAIjF,EAAE,GAAG6O,iBAAiB,CAAC5O,MAAM,GAAG,CAAC,EAAED,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;IACzD,MAAM8N,SAAS,GAAGe,iBAAiB,CAAC7O,EAAE,CAAC;IACvC,IAAI8N,SAAS,CAAC,MAAM,CAAC,KAAKX,WAAW,IAAIW,SAAS,CAAC,MAAM,CAAC,EAAE;MAC1DhH,YAAY,CAACgI,mBAAmB,CAAChB,SAAS,CAAC,EAAEA,SAAS,EAAEiB,aAAa,CAACjB,SAAS,CAAC,CAAC;MACjFA,SAAS,CAAC,MAAM,CAAC,CAAC/F,MAAM,CAAC,CAAC;MAC1B+F,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;MAC1BA,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;MAC1BR,iBAAiB,GAAG,IAAI;IAC1B;IACA,IAAIsB,SAAS,EAAE;MACbD,yBAAyB,CAACb,SAAS,EAAEc,SAAS,CAAC;IACjD;EACF;EACAlR,GAAG,CAACjD,OAAO,IAAI,CAAC,CAAC,CAAC;AACpB,CAAC;AACD,IAAIuU,SAAS,GAAGA,CAACpB,SAAS,EAAEqB,MAAM,EAAE9H,WAAW,EAAE+H,MAAM,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAC5E,IAAIC,YAAY,GAAGzB,SAAS,CAAC,MAAM,CAAC,IAAIA,SAAS,CAAC,MAAM,CAAC,CAAC/G,UAAU,IAAI+G,SAAS;EACjF,IAAIE,SAAS;EACb,IAAIuB,YAAY,CAACtJ,UAAU,IAAIsJ,YAAY,CAAC3M,OAAO,KAAKyK,WAAW,EAAE;IACnEkC,YAAY,GAAGA,YAAY,CAACtJ,UAAU;EACxC;EACA,OAAOoJ,QAAQ,IAAIC,MAAM,EAAE,EAAED,QAAQ,EAAE;IACrC,IAAID,MAAM,CAACC,QAAQ,CAAC,EAAE;MACpBrB,SAAS,GAAGN,SAAS,CAAC,IAAI,EAAErG,WAAW,EAAEgI,QAAQ,EAAEvB,SAAS,CAAC;MAC7D,IAAIE,SAAS,EAAE;QACboB,MAAM,CAACC,QAAQ,CAAC,CAACrK,KAAK,GAAGgJ,SAAS;QAClChH,YAAY,CAACuI,YAAY,EAAEvB,SAAS,EAAEiB,aAAa,CAACE,MAAM,CAAE,CAAC;MAC/D;IACF;EACF;AACF,CAAC;AACD,IAAIK,YAAY,GAAGA,CAACJ,MAAM,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAC/C,KAAK,IAAIG,KAAK,GAAGJ,QAAQ,EAAEI,KAAK,IAAIH,MAAM,EAAE,EAAEG,KAAK,EAAE;IACnD,MAAMjL,KAAK,GAAG4K,MAAM,CAACK,KAAK,CAAC;IAC3B,IAAIjL,KAAK,EAAE;MACT,MAAMjJ,GAAG,GAAGiJ,KAAK,CAACQ,KAAK;MACvB0K,gBAAgB,CAAClL,KAAK,CAAC;MACvB,IAAIjJ,GAAG,EAAE;QACP;UACEgS,2BAA2B,GAAG,IAAI;UAClC,IAAIhS,GAAG,CAAC,MAAM,CAAC,EAAE;YACfA,GAAG,CAAC,MAAM,CAAC,CAAC0M,MAAM,CAAC,CAAC;UACtB,CAAC,MAAM;YACL4G,yBAAyB,CAACtT,GAAG,EAAE,IAAI,CAAC;UACtC;QACF;QACAA,GAAG,CAAC0M,MAAM,CAAC,CAAC;MACd;IACF;EACF;AACF,CAAC;AACD,IAAI0H,cAAc,GAAGA,CAAC7B,SAAS,EAAE8B,KAAK,EAAE7B,SAAS,EAAE8B,KAAK,EAAEC,eAAe,GAAG,KAAK,KAAK;EACpF,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAI/P,EAAE,GAAG,CAAC;EACV,IAAIgQ,SAAS,GAAGN,KAAK,CAACzP,MAAM,GAAG,CAAC;EAChC,IAAIgQ,aAAa,GAAGP,KAAK,CAAC,CAAC,CAAC;EAC5B,IAAIQ,WAAW,GAAGR,KAAK,CAACM,SAAS,CAAC;EAClC,IAAIG,SAAS,GAAGR,KAAK,CAAC1P,MAAM,GAAG,CAAC;EAChC,IAAImQ,aAAa,GAAGT,KAAK,CAAC,CAAC,CAAC;EAC5B,IAAIU,WAAW,GAAGV,KAAK,CAACQ,SAAS,CAAC;EAClC,IAAIlL,IAAI;EACR,IAAIqL,SAAS;EACb,OAAOT,WAAW,IAAIG,SAAS,IAAIF,WAAW,IAAIK,SAAS,EAAE;IAC3D,IAAIF,aAAa,IAAI,IAAI,EAAE;MACzBA,aAAa,GAAGP,KAAK,CAAC,EAAEG,WAAW,CAAC;IACtC,CAAC,MAAM,IAAIK,WAAW,IAAI,IAAI,EAAE;MAC9BA,WAAW,GAAGR,KAAK,CAAC,EAAEM,SAAS,CAAC;IAClC,CAAC,MAAM,IAAII,aAAa,IAAI,IAAI,EAAE;MAChCA,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;IACtC,CAAC,MAAM,IAAIO,WAAW,IAAI,IAAI,EAAE;MAC9BA,WAAW,GAAGV,KAAK,CAAC,EAAEQ,SAAS,CAAC;IAClC,CAAC,MAAM,IAAII,WAAW,CAACN,aAAa,EAAEG,aAAa,EAAER,eAAe,CAAC,EAAE;MACrEY,KAAK,CAACP,aAAa,EAAEG,aAAa,EAAER,eAAe,CAAC;MACpDK,aAAa,GAAGP,KAAK,CAAC,EAAEG,WAAW,CAAC;MACpCO,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;IACtC,CAAC,MAAM,IAAIS,WAAW,CAACL,WAAW,EAAEG,WAAW,EAAET,eAAe,CAAC,EAAE;MACjEY,KAAK,CAACN,WAAW,EAAEG,WAAW,EAAET,eAAe,CAAC;MAChDM,WAAW,GAAGR,KAAK,CAAC,EAAEM,SAAS,CAAC;MAChCK,WAAW,GAAGV,KAAK,CAAC,EAAEQ,SAAS,CAAC;IAClC,CAAC,MAAM,IAAII,WAAW,CAACN,aAAa,EAAEI,WAAW,EAAET,eAAe,CAAC,EAAE;MACnE,IAAKK,aAAa,CAACpL,KAAK,KAAK,MAAM,IAAIwL,WAAW,CAACxL,KAAK,KAAK,MAAM,EAAG;QACpE8J,yBAAyB,CAACsB,aAAa,CAACnL,KAAK,CAAC+B,UAAU,EAAE,KAAK,CAAC;MAClE;MACA2J,KAAK,CAACP,aAAa,EAAEI,WAAW,EAAET,eAAe,CAAC;MAClD9I,YAAY,CAAC8G,SAAS,EAAEqC,aAAa,CAACnL,KAAK,EAAEoL,WAAW,CAACpL,KAAK,CAACiC,WAAW,CAAC;MAC3EkJ,aAAa,GAAGP,KAAK,CAAC,EAAEG,WAAW,CAAC;MACpCQ,WAAW,GAAGV,KAAK,CAAC,EAAEQ,SAAS,CAAC;IAClC,CAAC,MAAM,IAAII,WAAW,CAACL,WAAW,EAAEE,aAAa,EAAER,eAAe,CAAC,EAAE;MACnE,IAAKK,aAAa,CAACpL,KAAK,KAAK,MAAM,IAAIwL,WAAW,CAACxL,KAAK,KAAK,MAAM,EAAG;QACpE8J,yBAAyB,CAACuB,WAAW,CAACpL,KAAK,CAAC+B,UAAU,EAAE,KAAK,CAAC;MAChE;MACA2J,KAAK,CAACN,WAAW,EAAEE,aAAa,EAAER,eAAe,CAAC;MAClD9I,YAAY,CAAC8G,SAAS,EAAEsC,WAAW,CAACpL,KAAK,EAAEmL,aAAa,CAACnL,KAAK,CAAC;MAC/DoL,WAAW,GAAGR,KAAK,CAAC,EAAEM,SAAS,CAAC;MAChCI,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;IACtC,CAAC,MAAM;MACLC,QAAQ,GAAG,CAAC,CAAC;MACb;QACE,KAAK/P,EAAE,GAAG6P,WAAW,EAAE7P,EAAE,IAAIgQ,SAAS,EAAE,EAAEhQ,EAAE,EAAE;UAC5C,IAAI0P,KAAK,CAAC1P,EAAE,CAAC,IAAI0P,KAAK,CAAC1P,EAAE,CAAC,CAACyE,KAAK,KAAK,IAAI,IAAIiL,KAAK,CAAC1P,EAAE,CAAC,CAACyE,KAAK,KAAK2L,aAAa,CAAC3L,KAAK,EAAE;YACpFsL,QAAQ,GAAG/P,EAAE;YACb;UACF;QACF;MACF;MACA,IAAI+P,QAAQ,IAAI,CAAC,EAAE;QACjBO,SAAS,GAAGZ,KAAK,CAACK,QAAQ,CAAC;QAC3B,IAAIO,SAAS,CAACzL,KAAK,KAAKuL,aAAa,CAACvL,KAAK,EAAE;UAC3CI,IAAI,GAAGuI,SAAS,CAACkC,KAAK,IAAIA,KAAK,CAACI,WAAW,CAAC,EAAEjC,SAAS,EAAEkC,QAAQ,EAAEnC,SAAS,CAAC;QAC/E,CAAC,MAAM;UACL4C,KAAK,CAACF,SAAS,EAAEF,aAAa,EAAER,eAAe,CAAC;UAChDF,KAAK,CAACK,QAAQ,CAAC,GAAG,KAAK,CAAC;UACxB9K,IAAI,GAAGqL,SAAS,CAACxL,KAAK;QACxB;QACAsL,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;MACtC,CAAC,MAAM;QACL7K,IAAI,GAAGuI,SAAS,CAACkC,KAAK,IAAIA,KAAK,CAACI,WAAW,CAAC,EAAEjC,SAAS,EAAEiC,WAAW,EAAElC,SAAS,CAAC;QAChFwC,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;MACtC;MACA,IAAI7K,IAAI,EAAE;QACR;UACE6B,YAAY,CAACgI,mBAAmB,CAACmB,aAAa,CAACnL,KAAK,CAAC,EAAEG,IAAI,EAAE8J,aAAa,CAACkB,aAAa,CAACnL,KAAK,CAAC,CAAC;QAClG;MACF;IACF;EACF;EACA,IAAI+K,WAAW,GAAGG,SAAS,EAAE;IAC3BhB,SAAS,CACPpB,SAAS,EACT+B,KAAK,CAACQ,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGR,KAAK,CAACQ,SAAS,GAAG,CAAC,CAAC,CAACrL,KAAK,EAChE+I,SAAS,EACT8B,KAAK,EACLG,WAAW,EACXK,SACF,CAAC;EACH,CAAC,MAAM,IAAIL,WAAW,GAAGK,SAAS,EAAE;IAClCb,YAAY,CAACI,KAAK,EAAEG,WAAW,EAAEG,SAAS,CAAC;EAC7C;AACF,CAAC;AACD,IAAIO,WAAW,GAAGA,CAACE,SAAS,EAAEC,UAAU,EAAEd,eAAe,GAAG,KAAK,KAAK;EACpE,IAAIa,SAAS,CAAC5L,KAAK,KAAK6L,UAAU,CAAC7L,KAAK,EAAE;IACxC,IAAI4L,SAAS,CAAC5L,KAAK,KAAK,MAAM,EAAE;MAC9B;MACE;MACA;MACA,UAAU,IAAI4L,SAAS,IAAIb,eAAe;MAAI;MAC9C;MACAa,SAAS,CAAC3L,KAAK,CAACyC,QAAQ,KAAK,CAAC,EAC9B;QACA,OAAO,KAAK;MACd;MACA,OAAOkJ,SAAS,CAAC/L,MAAM,KAAKgM,UAAU,CAAChM,MAAM;IAC/C;IACA,IAAI,CAACkL,eAAe,EAAE;MACpB,OAAOa,SAAS,CAAChM,KAAK,KAAKiM,UAAU,CAACjM,KAAK;IAC7C;IACA,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;AACD,IAAIsK,aAAa,GAAI9J,IAAI,IAAK;EAC5B,OAAOA,IAAI,IAAIA,IAAI,CAAC,MAAM,CAAC,IAAIA,IAAI;AACrC,CAAC;AACD,IAAI6J,mBAAmB,GAAI7J,IAAI,IAAK,CAACA,IAAI,CAAC,MAAM,CAAC,GAAGA,IAAI,CAAC,MAAM,CAAC,GAAGA,IAAI,EAAE4B,UAAU;AACnF,IAAI2J,KAAK,GAAGA,CAACzC,QAAQ,EAAEF,SAAS,EAAE+B,eAAe,GAAG,KAAK,KAAK;EAC5D,MAAMvU,GAAG,GAAGwS,SAAS,CAAC/I,KAAK,GAAGiJ,QAAQ,CAACjJ,KAAK;EAC5C,MAAM6L,WAAW,GAAG5C,QAAQ,CAACvJ,UAAU;EACvC,MAAMoM,WAAW,GAAG/C,SAAS,CAACrJ,UAAU;EACxC,MAAMG,GAAG,GAAGkJ,SAAS,CAAChJ,KAAK;EAC3B,MAAMD,IAAI,GAAGiJ,SAAS,CAACjK,MAAM;EAC7B,IAAIiN,aAAa;EACjB,IAAIjM,IAAI,KAAK,IAAI,EAAE;IACjB;MACE2I,SAAS,GAAG5I,GAAG,KAAK,KAAK,GAAG,IAAI,GAAGA,GAAG,KAAK,eAAe,GAAG,KAAK,GAAG4I,SAAS;IAChF;IACA;MACE,IAAI5I,GAAG,KAAK,MAAM,IAAI,CAACyI,kBAAkB,EAAE;QACzC,IAAIW,QAAQ,CAACrJ,MAAM,KAAKmJ,SAAS,CAACnJ,MAAM,EAAE;UACxCmJ,SAAS,CAAC/I,KAAK,CAAC,MAAM,CAAC,GAAG+I,SAAS,CAACnJ,MAAM,IAAI,EAAE;UAChD2J,kBAAkB,CAACR,SAAS,CAAC/I,KAAK,CAACgM,aAAa,CAAC;QACnD;MACF,CAAC,MAAM;QACLtE,aAAa,CAACuB,QAAQ,EAAEF,SAAS,EAAEN,SAAS,CAAC;MAC/C;IACF;IACA,IAAIoD,WAAW,KAAK,IAAI,IAAIC,WAAW,KAAK,IAAI,EAAE;MAChDnB,cAAc,CAACpU,GAAG,EAAEsV,WAAW,EAAE9C,SAAS,EAAE+C,WAAW,EAAEhB,eAAe,CAAC;IAC3E,CAAC,MAAM,IAAIgB,WAAW,KAAK,IAAI,EAAE;MAC/B,IAAI7C,QAAQ,CAACnK,MAAM,KAAK,IAAI,EAAE;QAC5BvI,GAAG,CAACyM,WAAW,GAAG,EAAE;MACtB;MACAkH,SAAS,CAAC3T,GAAG,EAAE,IAAI,EAAEwS,SAAS,EAAE+C,WAAW,EAAE,CAAC,EAAEA,WAAW,CAAC3Q,MAAM,GAAG,CAAC,CAAC;IACzE,CAAC,MAAM;IACL;IACA,CAAC2P,eAAe,IAAI9b,KAAK,CAACsE,SAAS,IAAIuY,WAAW,KAAK,IAAI,EAC3D;MACArB,YAAY,CAACqB,WAAW,EAAE,CAAC,EAAEA,WAAW,CAAC1Q,MAAM,GAAG,CAAC,CAAC;IACtD;IACA,IAAIsN,SAAS,IAAI5I,GAAG,KAAK,KAAK,EAAE;MAC9B4I,SAAS,GAAG,KAAK;IACnB;EACF,CAAC,MAAM,IAAKsD,aAAa,GAAGxV,GAAG,CAAC,MAAM,CAAC,EAAG;IACxCwV,aAAa,CAAChK,UAAU,CAACiB,WAAW,GAAGlD,IAAI;EAC7C,CAAC,MAAM,IAAImJ,QAAQ,CAACnK,MAAM,KAAKgB,IAAI,EAAE;IACnCvJ,GAAG,CAAC0V,IAAI,GAAGnM,IAAI;EACjB;AACF,CAAC;AACD,IAAIoM,4BAA4B,GAAI3V,GAAG,IAAK;EAC1C,MAAMuM,UAAU,GAAGvM,GAAG,CAACuM,UAAU;EACjC,KAAK,MAAMkG,SAAS,IAAIlG,UAAU,EAAE;IAClC,IAAIkG,SAAS,CAACvG,QAAQ,KAAK,CAAC,CAAC,mBAAmB;MAC9C,IAAIuG,SAAS,CAAC,MAAM,CAAC,EAAE;QACrB,MAAM3K,QAAQ,GAAG2K,SAAS,CAAC,MAAM,CAAC;QAClCA,SAAS,CAACmD,MAAM,GAAG,KAAK;QACxB,KAAK,MAAMC,WAAW,IAAItJ,UAAU,EAAE;UACpC,IAAIsJ,WAAW,KAAKpD,SAAS,EAAE;YAC7B,IAAIoD,WAAW,CAAC,MAAM,CAAC,KAAKpD,SAAS,CAAC,MAAM,CAAC,IAAI3K,QAAQ,KAAK,EAAE,EAAE;cAChE,IAAI+N,WAAW,CAAC3J,QAAQ,KAAK,CAAC,CAAC,sBAAsBpE,QAAQ,KAAK+N,WAAW,CAACxP,YAAY,CAAC,MAAM,CAAC,IAAIyB,QAAQ,KAAK+N,WAAW,CAAC,MAAM,CAAC,CAAC,IAAIA,WAAW,CAAC3J,QAAQ,KAAK,CAAC,CAAC,kBAAkBpE,QAAQ,KAAK+N,WAAW,CAAC,MAAM,CAAC,EAAE;gBACxNpD,SAAS,CAACmD,MAAM,GAAG,IAAI;gBACvB;cACF;YACF,CAAC,MAAM;cACL,IAAIC,WAAW,CAAC3J,QAAQ,KAAK,CAAC,CAAC,qBAAqB2J,WAAW,CAAC3J,QAAQ,KAAK,CAAC,CAAC,kBAAkB2J,WAAW,CAACpJ,WAAW,CAACqJ,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;gBACtIrD,SAAS,CAACmD,MAAM,GAAG,IAAI;gBACvB;cACF;YACF;UACF;QACF;MACF;MACAD,4BAA4B,CAAClD,SAAS,CAAC;IACzC;EACF;AACF,CAAC;AACD,IAAIsD,aAAa,GAAG,EAAE;AACtB,IAAIC,4BAA4B,GAAIhW,GAAG,IAAK;EAC1C,IAAI4J,IAAI;EACR,IAAIqM,gBAAgB;EACpB,IAAIC,CAAC;EACL,KAAK,MAAMzD,SAAS,IAAIzS,GAAG,CAACuM,UAAU,EAAE;IACtC,IAAIkG,SAAS,CAAC,MAAM,CAAC,KAAK7I,IAAI,GAAG6I,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI7I,IAAI,CAAC4B,UAAU,EAAE;MACtEyK,gBAAgB,GAAGrM,IAAI,CAAC4B,UAAU,CAACe,UAAU;MAC7C,MAAMzE,QAAQ,GAAG2K,SAAS,CAAC,MAAM,CAAC;MAClC,KAAKyD,CAAC,GAAGD,gBAAgB,CAACrR,MAAM,GAAG,CAAC,EAAEsR,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACjDtM,IAAI,GAAGqM,gBAAgB,CAACC,CAAC,CAAC;QAC1B,IAAI,CAACtM,IAAI,CAAC,MAAM,CAAC,IAAI,CAACA,IAAI,CAAC,MAAM,CAAC,IAAIA,IAAI,CAAC,MAAM,CAAC,KAAK6I,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC7I,IAAI,CAAC,MAAM,CAAC,IAAIA,IAAI,CAAC,MAAM,CAAC,KAAK6I,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE;UACjI,IAAI0D,mBAAmB,CAACvM,IAAI,EAAE9B,QAAQ,CAAC,EAAE;YACvC,IAAIsO,gBAAgB,GAAGL,aAAa,CAAChJ,IAAI,CAAEpN,CAAC,IAAKA,CAAC,CAAC0W,gBAAgB,KAAKzM,IAAI,CAAC;YAC7EoI,2BAA2B,GAAG,IAAI;YAClCpI,IAAI,CAAC,MAAM,CAAC,GAAGA,IAAI,CAAC,MAAM,CAAC,IAAI9B,QAAQ;YACvC,IAAIsO,gBAAgB,EAAE;cACpBA,gBAAgB,CAACC,gBAAgB,CAAC,MAAM,CAAC,GAAG5D,SAAS,CAAC,MAAM,CAAC;cAC7D2D,gBAAgB,CAACE,aAAa,GAAG7D,SAAS;YAC5C,CAAC,MAAM;cACL7I,IAAI,CAAC,MAAM,CAAC,GAAG6I,SAAS,CAAC,MAAM,CAAC;cAChCsD,aAAa,CAACxR,IAAI,CAAC;gBACjB+R,aAAa,EAAE7D,SAAS;gBACxB4D,gBAAgB,EAAEzM;cACpB,CAAC,CAAC;YACJ;YACA,IAAIA,IAAI,CAAC,MAAM,CAAC,EAAE;cAChBmM,aAAa,CAACvP,GAAG,CAAE+P,YAAY,IAAK;gBAClC,IAAIJ,mBAAmB,CAACI,YAAY,CAACF,gBAAgB,EAAEzM,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;kBACpEwM,gBAAgB,GAAGL,aAAa,CAAChJ,IAAI,CAAEpN,CAAC,IAAKA,CAAC,CAAC0W,gBAAgB,KAAKzM,IAAI,CAAC;kBACzE,IAAIwM,gBAAgB,IAAI,CAACG,YAAY,CAACD,aAAa,EAAE;oBACnDC,YAAY,CAACD,aAAa,GAAGF,gBAAgB,CAACE,aAAa;kBAC7D;gBACF;cACF,CAAC,CAAC;YACJ;UACF,CAAC,MAAM,IAAI,CAACP,aAAa,CAACS,IAAI,CAAE7W,CAAC,IAAKA,CAAC,CAAC0W,gBAAgB,KAAKzM,IAAI,CAAC,EAAE;YAClEmM,aAAa,CAACxR,IAAI,CAAC;cACjB8R,gBAAgB,EAAEzM;YACpB,CAAC,CAAC;UACJ;QACF;MACF;IACF;IACA,IAAI6I,SAAS,CAACvG,QAAQ,KAAK,CAAC,CAAC,mBAAmB;MAC9C8J,4BAA4B,CAACvD,SAAS,CAAC;IACzC;EACF;AACF,CAAC;AACD,IAAI0D,mBAAmB,GAAGA,CAACM,cAAc,EAAE3O,QAAQ,KAAK;EACtD,IAAI2O,cAAc,CAACvK,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACnD,IAAIuK,cAAc,CAACpQ,YAAY,CAAC,MAAM,CAAC,KAAK,IAAI,IAAIyB,QAAQ,KAAK,EAAE,EAAE;MACnE,OAAO,IAAI;IACb;IACA,IAAI2O,cAAc,CAACpQ,YAAY,CAAC,MAAM,CAAC,KAAKyB,QAAQ,EAAE;MACpD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EACA,IAAI2O,cAAc,CAAC,MAAM,CAAC,KAAK3O,QAAQ,EAAE;IACvC,OAAO,IAAI;EACb;EACA,OAAOA,QAAQ,KAAK,EAAE;AACxB,CAAC;AACD,IAAIqM,gBAAgB,GAAIuC,KAAK,IAAK;EAChC;IACEA,KAAK,CAACxN,OAAO,IAAIwN,KAAK,CAACxN,OAAO,CAACvK,GAAG,IAAI+X,KAAK,CAACxN,OAAO,CAACvK,GAAG,CAAC,IAAI,CAAC;IAC7D+X,KAAK,CAACvN,UAAU,IAAIuN,KAAK,CAACvN,UAAU,CAAC3C,GAAG,CAAC2N,gBAAgB,CAAC;EAC5D;AACF,CAAC;AACD,IAAI1I,YAAY,GAAGA,CAACkL,MAAM,EAAEC,OAAO,EAAEC,SAAS,KAAK;EACjD,MAAMC,QAAQ,GAAGH,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAClL,YAAY,CAACmL,OAAO,EAAEC,SAAS,CAAC;EAClF;IACE9D,qBAAqB,CAAC6D,OAAO,EAAED,MAAM,CAAC;EACxC;EACA,OAAOG,QAAQ;AACjB,CAAC;AACD,IAAIC,YAAY,GAAIld,OAAO,IAAK;EAC9B,MAAMmd,QAAQ,GAAG,EAAE;EACnB,IAAInd,OAAO,EAAE;IACXmd,QAAQ,CAACzS,IAAI,CACX,IAAG1K,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,GACzBA,OAAO,CAAC,MAAM,CAAC,EACfA,OAAO,CAAC,MAAM,CAAC,EACf,GAAGkd,YAAY,CAACld,OAAO,CAAC4b,aAAa,CACvC,CAAC;EACH;EACA,OAAOuB,QAAQ;AACjB,CAAC;AACD,IAAIjE,qBAAqB,GAAGA,CAAClZ,OAAO,EAAE8c,MAAM,EAAEM,iBAAiB,GAAG,KAAK,KAAK;EAC1E,IAAIhR,EAAE;EACN,IAAIpM,OAAO,IAAI8c,MAAM,IAAI9c,OAAO,CAACqS,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACjE,MAAM8K,QAAQ,GAAG,IAAIlI,GAAG,CAACiI,YAAY,CAACJ,MAAM,CAAC,CAAC9N,MAAM,CAACqO,OAAO,CAAC,CAAC;IAC9D,IAAIF,QAAQ,CAACG,IAAI,EAAE;MACjB,CAAClR,EAAE,GAAGpM,OAAO,CAAC6V,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGzJ,EAAE,CAACoJ,GAAG,CAAC,IAAGxV,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAGmd,QAAQ,CAAC,EAAC;MACvF,IAAInd,OAAO,CAAC,MAAM,CAAC,IAAIod,iBAAiB,EAAE;QACxC,KAAK,MAAMxE,SAAS,IAAIrK,KAAK,CAAC+K,IAAI,CAACtZ,OAAO,CAAC0S,UAAU,CAAC,EAAE;UACtDwG,qBAAqB,CAACN,SAAS,EAAE5Y,OAAO,EAAE,IAAI,CAAC;QACjD;MACF;IACF;EACF;AACF,CAAC;AACD,IAAIud,UAAU,GAAGA,CAACtY,OAAO,EAAEuY,eAAe,EAAEC,aAAa,GAAG,KAAK,KAAK;EACpE,IAAIrR,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEoR,EAAE,EAAEC,EAAE;EACtB,MAAMjN,OAAO,GAAGzL,OAAO,CAACO,aAAa;EACrC,MAAMF,OAAO,GAAGL,OAAO,CAACQ,SAAS;EACjC,MAAMoT,QAAQ,GAAG5T,OAAO,CAACgM,OAAO,IAAItC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;EACxD,MAAMiP,SAAS,GAAG9N,MAAM,CAAC0N,eAAe,CAAC,GAAGA,eAAe,GAAG5P,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE4P,eAAe,CAAC;EAC5FvF,WAAW,GAAGvH,OAAO,CAAClD,OAAO;EAC7B,IAAIlI,OAAO,CAACuY,gBAAgB,EAAE;IAC5BD,SAAS,CAACvO,OAAO,GAAGuO,SAAS,CAACvO,OAAO,IAAI,CAAC,CAAC;IAC3C/J,OAAO,CAACuY,gBAAgB,CAAClR,GAAG,CAC1B,CAAC,CAACmR,QAAQ,EAAEC,SAAS,CAAC,KAAKH,SAAS,CAACvO,OAAO,CAAC0O,SAAS,CAAC,GAAGrN,OAAO,CAACoN,QAAQ,CAC5E,CAAC;EACH;EACA,IAAIL,aAAa,IAAIG,SAAS,CAACvO,OAAO,EAAE;IACtC,KAAK,MAAM3B,GAAG,IAAI1J,MAAM,CAAC+K,IAAI,CAAC6O,SAAS,CAACvO,OAAO,CAAC,EAAE;MAChD,IAAIqB,OAAO,CAACsN,YAAY,CAACtQ,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAACgI,QAAQ,CAAChI,GAAG,CAAC,EAAE;QAChFkQ,SAAS,CAACvO,OAAO,CAAC3B,GAAG,CAAC,GAAGgD,OAAO,CAAChD,GAAG,CAAC;MACvC;IACF;EACF;EACAkQ,SAAS,CAACjO,KAAK,GAAG,IAAI;EACtBiO,SAAS,CAACrY,OAAO,IAAI,CAAC,CAAC;EACvBN,OAAO,CAACgM,OAAO,GAAG2M,SAAS;EAC3BA,SAAS,CAAChO,KAAK,GAAGiJ,QAAQ,CAACjJ,KAAK,GAAGc,OAAO,CAACG,UAAU,IAAIH,OAAO;EAChE;IACEqH,OAAO,GAAGrH,OAAO,CAAC,MAAM,CAAC;EAC3B;EACAwH,kBAAkB,GAAG,CAAC5S,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,kCAAkC,CAAC;EAC7E;IACEyS,UAAU,GAAGtH,OAAO,CAAC,MAAM,CAAC;IAC5ByH,2BAA2B,GAAG,KAAK;EACrC;EACAmD,KAAK,CAACzC,QAAQ,EAAE+E,SAAS,EAAEH,aAAa,CAAC;EACzC;IACEjV,GAAG,CAACjD,OAAO,IAAI,CAAC,CAAC;IACjB,IAAI6S,iBAAiB,EAAE;MACrB+D,4BAA4B,CAACyB,SAAS,CAAChO,KAAK,CAAC;MAC7C,KAAK,MAAMqO,YAAY,IAAI/B,aAAa,EAAE;QACxC,MAAMU,cAAc,GAAGqB,YAAY,CAACzB,gBAAgB;QACpD,IAAI,CAACI,cAAc,CAAC,MAAM,CAAC,EAAE;UAC3B,MAAMlL,eAAe,GAAGvJ,GAAG,CAAC2Q,cAAc,CAAC,EAAE,CAAC;UAC9CpH,eAAe,CAAC,MAAM,CAAC,GAAGkL,cAAc;UACxChL,YAAY,CAACgL,cAAc,CAACjL,UAAU,EAAEiL,cAAc,CAAC,MAAM,CAAC,GAAGlL,eAAe,EAAEkL,cAAc,CAAC;QACnG;MACF;MACA,KAAK,MAAMqB,YAAY,IAAI/B,aAAa,EAAE;QACxC,MAAMU,cAAc,GAAGqB,YAAY,CAACzB,gBAAgB;QACpD,MAAM0B,WAAW,GAAGD,YAAY,CAACxB,aAAa;QAC9C,IAAIyB,WAAW,EAAE;UACf,MAAMC,aAAa,GAAGD,WAAW,CAACvM,UAAU;UAC5C,IAAIyM,gBAAgB,GAAGF,WAAW,CAACrM,WAAW;UAC9C,IAAIuM,gBAAgB,IAAIA,gBAAgB,CAAC/L,QAAQ,KAAK,CAAC,CAAC,mBAAmB;YACzE,IAAIX,eAAe,GAAG,CAACtF,EAAE,GAAGwQ,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGxQ,EAAE,CAACiS,eAAe;YACzF,OAAO3M,eAAe,EAAE;cACtB,IAAI4M,OAAO,GAAG,CAACjS,EAAE,GAAGqF,eAAe,CAAC,MAAM,CAAC,KAAK,IAAI,GAAGrF,EAAE,GAAG,IAAI;cAChE,IAAIiS,OAAO,IAAIA,OAAO,CAAC,MAAM,CAAC,KAAK1B,cAAc,CAAC,MAAM,CAAC,IAAIuB,aAAa,KAAKG,OAAO,CAAC3M,UAAU,EAAE;gBACjG2M,OAAO,GAAGA,OAAO,CAACzM,WAAW;gBAC7B,OAAOyM,OAAO,KAAK1B,cAAc,KAAK0B,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;kBACjFA,OAAO,GAAGA,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACzM,WAAW;gBAC1D;gBACA,IAAI,CAACyM,OAAO,IAAI,CAACA,OAAO,CAAC,MAAM,CAAC,EAAE;kBAChCF,gBAAgB,GAAGE,OAAO;kBAC1B;gBACF;cACF;cACA5M,eAAe,GAAGA,eAAe,CAAC2M,eAAe;YACnD;UACF;UACA,IAAI,CAACD,gBAAgB,IAAID,aAAa,KAAKvB,cAAc,CAACjL,UAAU,IAAIiL,cAAc,CAAC/K,WAAW,KAAKuM,gBAAgB,EAAE;YACvH,IAAIxB,cAAc,KAAKwB,gBAAgB,EAAE;cACvCxM,YAAY,CAACuM,aAAa,EAAEvB,cAAc,EAAEwB,gBAAgB,CAAC;cAC7D,IAAIxB,cAAc,CAACvK,QAAQ,KAAK,CAAC,CAAC,mBAAmB;gBACnDuK,cAAc,CAACb,MAAM,GAAG,CAACzP,EAAE,GAAGsQ,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAGtQ,EAAE,GAAG,KAAK;cAC5E;YACF;UACF;UACAsQ,cAAc,IAAI,OAAOsB,WAAW,CAAC,MAAM,CAAC,KAAK,UAAU,IAAIA,WAAW,CAAC,MAAM,CAAC,CAACtB,cAAc,CAAC;QACpG,CAAC,MAAM;UACL,IAAIA,cAAc,CAACvK,QAAQ,KAAK,CAAC,CAAC,mBAAmB;YACnD,IAAIoL,aAAa,EAAE;cACjBb,cAAc,CAAC,MAAM,CAAC,GAAG,CAACc,EAAE,GAAGd,cAAc,CAACb,MAAM,KAAK,IAAI,GAAG2B,EAAE,GAAG,KAAK;YAC5E;YACAd,cAAc,CAACb,MAAM,GAAG,IAAI;UAC9B;QACF;MACF;IACF;IACA,IAAI5D,2BAA2B,EAAE;MAC/B2D,4BAA4B,CAAC8B,SAAS,CAAChO,KAAK,CAAC;IAC/C;IACApH,GAAG,CAACjD,OAAO,IAAI,CAAC,CAAC,CAAC;IAClB2W,aAAa,CAACnR,MAAM,GAAG,CAAC;EAC1B;EACA,IAAIzF,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;IACpD,KAAK,MAAMqT,SAAS,IAAIgF,SAAS,CAAChO,KAAK,CAAC8C,UAAU,EAAE;MAClD,IAAIkG,SAAS,CAAC,MAAM,CAAC,KAAKX,WAAW,IAAI,CAACW,SAAS,CAAC,MAAM,CAAC,EAAE;QAC3D,IAAI6E,aAAa,IAAI7E,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;UAC9CA,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC+E,EAAE,GAAG/E,SAAS,CAACmD,MAAM,KAAK,IAAI,GAAG4B,EAAE,GAAG,KAAK;QAClE;QACA/E,SAAS,CAACmD,MAAM,GAAG,IAAI;MACzB;IACF;EACF;EACA/D,UAAU,GAAG,KAAK,CAAC;AACrB,CAAC;;AAED;AACA,IAAIuG,gBAAgB,GAAGA,CAACtZ,OAAO,EAAEuZ,iBAAiB,KAAK;EACrD,IAAIA,iBAAiB,IAAI,CAACvZ,OAAO,CAACwZ,iBAAiB,IAAID,iBAAiB,CAAC,KAAK,CAAC,EAAE;IAC/EA,iBAAiB,CAAC,KAAK,CAAC,CAAC9T,IAAI,CAAC,IAAI7E,OAAO,CAAEC,CAAC,IAAKb,OAAO,CAACwZ,iBAAiB,GAAG3Y,CAAC,CAAC,CAAC;EAClF;AACF,CAAC;AACD,IAAI4Y,cAAc,GAAGA,CAACzZ,OAAO,EAAEwY,aAAa,KAAK;EAC/C;IACExY,OAAO,CAACM,OAAO,IAAI,EAAE,CAAC;EACxB;EACA,IAAIN,OAAO,CAACM,OAAO,GAAG,CAAC,CAAC,4BAA4B;IAClDN,OAAO,CAACM,OAAO,IAAI,GAAG,CAAC;IACvB;EACF;EACAgZ,gBAAgB,CAACtZ,OAAO,EAAEA,OAAO,CAAC0Z,mBAAmB,CAAC;EACtD,MAAMC,QAAQ,GAAGA,CAAA,KAAMC,aAAa,CAAC5Z,OAAO,EAAEwY,aAAa,CAAC;EAC5D,OAAOtS,SAAS,CAACyT,QAAQ,CAAC;AAC5B,CAAC;AACD,IAAIC,aAAa,GAAGA,CAAC5Z,OAAO,EAAEwY,aAAa,KAAK;EAC9C,MAAMtX,GAAG,GAAGlB,OAAO,CAACO,aAAa;EACjC,MAAMsZ,WAAW,GAAGxR,UAAU,CAAC,gBAAgB,EAAErI,OAAO,CAACQ,SAAS,CAACqB,SAAS,CAAC;EAC7E,MAAMiY,QAAQ,GAAG9Z,OAAO,CAACE,cAAc;EACvC,IAAI,CAAC4Z,QAAQ,EAAE;IACb,MAAM,IAAIC,KAAK,CACb,2BAA2B7Y,GAAG,CAACqH,OAAO,CAACiF,WAAW,CAAC,CAAC,yNACtD,CAAC;EACH;EACA,IAAIwM,YAAY;EAChB,IAAIxB,aAAa,EAAE;IACjB;MACExY,OAAO,CAACM,OAAO,IAAI,GAAG,CAAC;MACvB,IAAIN,OAAO,CAACia,iBAAiB,EAAE;QAC7Bja,OAAO,CAACia,iBAAiB,CAACvS,GAAG,CAAC,CAAC,CAACwS,UAAU,EAAElf,KAAK,CAAC,KAAKmf,QAAQ,CAACL,QAAQ,EAAEI,UAAU,EAAElf,KAAK,CAAC,CAAC;QAC7FgF,OAAO,CAACia,iBAAiB,GAAG,KAAK,CAAC;MACpC;IACF;IACA;MACED,YAAY,GAAGG,QAAQ,CAACL,QAAQ,EAAE,mBAAmB,CAAC;IACxD;EACF;EACA;IACEE,YAAY,GAAGI,OAAO,CAACJ,YAAY,EAAE,MAAMG,QAAQ,CAACL,QAAQ,EAAE,qBAAqB,CAAC,CAAC;EACvF;EACAD,WAAW,CAAC,CAAC;EACb,OAAOO,OAAO,CAACJ,YAAY,EAAE,MAAMK,eAAe,CAACra,OAAO,EAAE8Z,QAAQ,EAAEtB,aAAa,CAAC,CAAC;AACvF,CAAC;AACD,IAAI4B,OAAO,GAAGA,CAACJ,YAAY,EAAE9R,EAAE,KAAKoS,UAAU,CAACN,YAAY,CAAC,GAAGA,YAAY,CAAC9X,IAAI,CAACgG,EAAE,CAAC,CAACqS,KAAK,CAAEC,IAAI,IAAK;EACnGjZ,OAAO,CAACC,KAAK,CAACgZ,IAAI,CAAC;EACnBtS,EAAE,CAAC,CAAC;AACN,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC;AACT,IAAIoS,UAAU,GAAIN,YAAY,IAAKA,YAAY,YAAYpZ,OAAO,IAAIoZ,YAAY,IAAIA,YAAY,CAAC9X,IAAI,IAAI,OAAO8X,YAAY,CAAC9X,IAAI,KAAK,UAAU;AAClJ,IAAImY,eAAe;EAAA,IAAAI,IAAA,GAAAC,iBAAA,CAAG,WAAO1a,OAAO,EAAE8Z,QAAQ,EAAEtB,aAAa,EAAK;IAChE,IAAIrR,EAAE;IACN,MAAMjG,GAAG,GAAGlB,OAAO,CAACO,aAAa;IACjC,MAAMoa,SAAS,GAAGtS,UAAU,CAAC,QAAQ,EAAErI,OAAO,CAACQ,SAAS,CAACqB,SAAS,CAAC;IACnE,MAAM+Y,EAAE,GAAG1Z,GAAG,CAAC,MAAM,CAAC;IACtB,IAAIsX,aAAa,EAAE;MACjBxe,YAAY,CAACgG,OAAO,CAAC;IACvB;IACA,MAAM6a,SAAS,GAAGxS,UAAU,CAAC,QAAQ,EAAErI,OAAO,CAACQ,SAAS,CAACqB,SAAS,CAAC;IACnE;MACEiZ,UAAU,CAAC9a,OAAO,EAAE8Z,QAAQ,EAAE5Y,GAAG,EAAEsX,aAAa,CAAC;IACnD;IACA,IAAIoC,EAAE,EAAE;MACNA,EAAE,CAAClT,GAAG,CAAElC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACpBtE,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;IACtB;IACA2Z,SAAS,CAAC,CAAC;IACXF,SAAS,CAAC,CAAC;IACX;MACE,MAAMI,gBAAgB,GAAG,CAAC5T,EAAE,GAAGjG,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,GAAGiG,EAAE,GAAG,EAAE;MAC5D,MAAM6T,UAAU,GAAGA,CAAA,KAAMC,mBAAmB,CAACjb,OAAO,CAAC;MACrD,IAAI+a,gBAAgB,CAACjV,MAAM,KAAK,CAAC,EAAE;QACjCkV,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLpa,OAAO,CAACzB,GAAG,CAAC4b,gBAAgB,CAAC,CAAC7Y,IAAI,CAAC8Y,UAAU,CAAC;QAC9Chb,OAAO,CAACM,OAAO,IAAI,CAAC,CAAC;QACrBya,gBAAgB,CAACjV,MAAM,GAAG,CAAC;MAC7B;IACF;EACF,CAAC;EAAA,gBA7BGuU,eAAeA,CAAAa,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAX,IAAA,CAAAY,KAAA,OAAAC,SAAA;EAAA;AAAA,GA6BlB;AACD,IAAIR,UAAU,GAAGA,CAAC9a,OAAO,EAAE8Z,QAAQ,EAAE5Y,GAAG,EAAEsX,aAAa,KAAK;EAC1D,IAAI;IACFsB,QAAQ,GAAGA,QAAQ,CAACyB,MAAM,IAAIzB,QAAQ,CAACyB,MAAM,CAAC,CAAC;IAC/C;MACEvb,OAAO,CAACM,OAAO,IAAI,CAAC,EAAE,CAAC;IACzB;IACA;MACEN,OAAO,CAACM,OAAO,IAAI,CAAC,CAAC;IACvB;IACA;MACE;QACE;UACEgY,UAAU,CAACtY,OAAO,EAAE8Z,QAAQ,EAAEtB,aAAa,CAAC;QAC9C;MACF;IACF;EACF,CAAC,CAAC,OAAOnX,CAAC,EAAE;IACVD,YAAY,CAACC,CAAC,EAAErB,OAAO,CAACO,aAAa,CAAC;EACxC;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAI0a,mBAAmB,GAAIjb,OAAO,IAAK;EACrC,MAAMuI,OAAO,GAAGvI,OAAO,CAACQ,SAAS,CAACqB,SAAS;EAC3C,MAAMX,GAAG,GAAGlB,OAAO,CAACO,aAAa;EACjC,MAAMib,aAAa,GAAGnT,UAAU,CAAC,YAAY,EAAEE,OAAO,CAAC;EACvD,MAAMuR,QAAQ,GAAG9Z,OAAO,CAACE,cAAc;EACvC,MAAMqZ,iBAAiB,GAAGvZ,OAAO,CAAC0Z,mBAAmB;EACrD;IACES,QAAQ,CAACL,QAAQ,EAAE,oBAAoB,CAAC;EAC1C;EACA,IAAI,EAAE9Z,OAAO,CAACM,OAAO,GAAG,EAAE,CAAC,yBAAyB,EAAE;IACpDN,OAAO,CAACM,OAAO,IAAI,EAAE,CAAC;IACtB;MACEmb,eAAe,CAACva,GAAG,CAAC;IACtB;IACA;MACEiZ,QAAQ,CAACL,QAAQ,EAAE,kBAAkB,CAAC;IACxC;IACA0B,aAAa,CAAC,CAAC;IACf;MACExb,OAAO,CAACgB,gBAAgB,CAACE,GAAG,CAAC;MAC7B,IAAI,CAACqY,iBAAiB,EAAE;QACtBmC,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC,MAAM;IACL;MACEvB,QAAQ,CAACL,QAAQ,EAAE,oBAAoB,CAAC;IAC1C;IACA0B,aAAa,CAAC,CAAC;EACjB;EACA;IACExb,OAAO,CAACc,mBAAmB,CAACI,GAAG,CAAC;EAClC;EACA;IACE,IAAIlB,OAAO,CAACwZ,iBAAiB,EAAE;MAC7BxZ,OAAO,CAACwZ,iBAAiB,CAAC,CAAC;MAC3BxZ,OAAO,CAACwZ,iBAAiB,GAAG,KAAK,CAAC;IACpC;IACA,IAAIxZ,OAAO,CAACM,OAAO,GAAG,GAAG,CAAC,qBAAqB;MAC7CoF,QAAQ,CAAC,MAAM+T,cAAc,CAACzZ,OAAO,EAAE,KAAK,CAAC,CAAC;IAChD;IACAA,OAAO,CAACM,OAAO,IAAI,EAAE,CAAC,CAAC,6BAA6B,GAAG,CAAC,oBAAoB;EAC9E;AACF,CAAC;AACD,IAAIqb,WAAW,GAAI9b,GAAG,IAAK;EACzB;IACE,MAAMG,OAAO,GAAGJ,UAAU,CAACC,GAAG,CAAC;IAC/B,MAAM+b,WAAW,GAAG5b,OAAO,CAACO,aAAa,CAACqb,WAAW;IACrD,IAAIA,WAAW,IAAI,CAAC5b,OAAO,CAACM,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,CAAC,wBAAwB,MAAM,CAAC,CAAC,mBAAmB;MACjHmZ,cAAc,CAACzZ,OAAO,EAAE,KAAK,CAAC;IAChC;IACA,OAAO4b,WAAW;EACpB;AACF,CAAC;AACD,IAAIF,UAAU,GAAIG,GAAG,IAAK;EACxB;IACEJ,eAAe,CAACvY,GAAG,CAAC4Y,eAAe,CAAC;EACtC;EACApW,QAAQ,CAAC,MAAMsJ,SAAS,CAAChM,GAAG,EAAE,SAAS,EAAE;IAAE+L,MAAM,EAAE;MAAEgN,SAAS,EAAEriB;IAAU;EAAE,CAAC,CAAC,CAAC;AACjF,CAAC;AACD,IAAIygB,QAAQ,GAAGA,CAACL,QAAQ,EAAEpd,MAAM,EAAEsf,GAAG,KAAK;EACxC,IAAIlC,QAAQ,IAAIA,QAAQ,CAACpd,MAAM,CAAC,EAAE;IAChC,IAAI;MACF,OAAOod,QAAQ,CAACpd,MAAM,CAAC,CAACsf,GAAG,CAAC;IAC9B,CAAC,CAAC,OAAO3a,CAAC,EAAE;MACVD,YAAY,CAACC,CAAC,CAAC;IACjB;EACF;EACA,OAAO,KAAK,CAAC;AACf,CAAC;AACD,IAAIoa,eAAe,GAAIva,GAAG,IAAK;EAC7B,IAAIiG,EAAE;EACN,OAAOjG,GAAG,CAAC0P,SAAS,CAACL,GAAG,CAAC,CAACpJ,EAAE,GAAGxN,KAAK,CAACqC,oBAAoB,KAAK,IAAI,GAAGmL,EAAE,GAAG,UAAU,CAAC;AACvF,CAAC;;AAED;AACA,IAAI8U,QAAQ,GAAGA,CAACpc,GAAG,EAAEgZ,QAAQ,KAAKjZ,UAAU,CAACC,GAAG,CAAC,CAACY,gBAAgB,CAACpB,GAAG,CAACwZ,QAAQ,CAAC;AAChF,IAAIqD,QAAQ,GAAGA,CAACrc,GAAG,EAAEgZ,QAAQ,EAAEzQ,MAAM,EAAE/H,OAAO,KAAK;EACjD,MAAML,OAAO,GAAGJ,UAAU,CAACC,GAAG,CAAC;EAC/B,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAI+Z,KAAK,CACb,mCAAmC1Z,OAAO,CAACwB,SAAS,+YACtD,CAAC;EACH;EACA,MAAMX,GAAG,GAAGlB,OAAO,CAACO,aAAa;EACjC,MAAM4b,MAAM,GAAGnc,OAAO,CAACS,gBAAgB,CAACpB,GAAG,CAACwZ,QAAQ,CAAC;EACrD,MAAMhK,KAAK,GAAG7O,OAAO,CAACM,OAAO;EAC7B,MAAMwZ,QAAQ,GAAG9Z,OAAO,CAACE,cAAc;EACvCkI,MAAM,GAAGmG,kBAAkB,CAACnG,MAAM,EAAE/H,OAAO,CAAC+b,SAAS,CAACvD,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnE,MAAMwD,UAAU,GAAGC,MAAM,CAACC,KAAK,CAACJ,MAAM,CAAC,IAAIG,MAAM,CAACC,KAAK,CAACnU,MAAM,CAAC;EAC/D,MAAMoU,cAAc,GAAGpU,MAAM,KAAK+T,MAAM,IAAI,CAACE,UAAU;EACvD,IAAI,CAAC,EAAExN,KAAK,GAAG,CAAC,CAAC,6BAA6B,IAAIsN,MAAM,KAAK,KAAK,CAAC,KAAKK,cAAc,EAAE;IACtFxc,OAAO,CAACS,gBAAgB,CAACR,GAAG,CAAC4Y,QAAQ,EAAEzQ,MAAM,CAAC;IAC9C,IAAI0R,QAAQ,EAAE;MACZ,IAAIzZ,OAAO,CAACoc,UAAU,IAAI5N,KAAK,GAAG,GAAG,CAAC,oBAAoB;QACxD,MAAM6N,YAAY,GAAGrc,OAAO,CAACoc,UAAU,CAAC5D,QAAQ,CAAC;QACjD,IAAI6D,YAAY,EAAE;UAChBA,YAAY,CAAChV,GAAG,CAAEiV,eAAe,IAAK;YACpC,IAAI;cACF7C,QAAQ,CAAC6C,eAAe,CAAC,CAACvU,MAAM,EAAE+T,MAAM,EAAEtD,QAAQ,CAAC;YACrD,CAAC,CAAC,OAAOxX,CAAC,EAAE;cACVD,YAAY,CAACC,CAAC,EAAEH,GAAG,CAAC;YACtB;UACF,CAAC,CAAC;QACJ;MACF;MACA,IAAI,CAAC2N,KAAK,IAAI,CAAC,CAAC,oBAAoB,EAAE,CAAC,wBAAwB,MAAM,CAAC,CAAC,mBAAmB;QACxF4K,cAAc,CAACzZ,OAAO,EAAE,KAAK,CAAC;MAChC;IACF;EACF;AACF,CAAC;;AAED;AACA,IAAI4c,cAAc,GAAGA,CAACC,IAAI,EAAExc,OAAO,EAAEwO,KAAK,KAAK;EAC7C,IAAI1H,EAAE,EAAEC,EAAE;EACV,MAAM0V,SAAS,GAAGD,IAAI,CAACC,SAAS;EAChC,IAAIzc,OAAO,CAAC+b,SAAS,IAAK/b,OAAO,CAACoc,UAAU,IAAII,IAAI,CAACE,QAAS,EAAE;IAC9D,IAAIF,IAAI,CAACE,QAAQ,IAAI,CAAC1c,OAAO,CAACoc,UAAU,EAAE;MACxCpc,OAAO,CAACoc,UAAU,GAAGI,IAAI,CAACE,QAAQ;IACpC;IACA,MAAMC,OAAO,GAAGje,MAAM,CAACke,OAAO,CAAC,CAAC9V,EAAE,GAAG9G,OAAO,CAAC+b,SAAS,KAAK,IAAI,GAAGjV,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1E6V,OAAO,CAACtV,GAAG,CAAC,CAAC,CAACvG,UAAU,EAAE,CAAC+b,WAAW,CAAC,CAAC,KAAK;MAC3C,IAAKA,WAAW,GAAG,EAAE,CAAC,cAAerO,KAAK,GAAG,CAAC,CAAC,oBAAqBqO,WAAW,GAAG,EAAE,CAAC,aAAc;QACjGne,MAAM,CAACC,cAAc,CAAC8d,SAAS,EAAE3b,UAAU,EAAE;UAC3C9B,GAAGA,CAAA,EAAG;YACJ,OAAO4c,QAAQ,CAAC,IAAI,EAAE9a,UAAU,CAAC;UACnC,CAAC;UACDlB,GAAGA,CAAC+Q,QAAQ,EAAE;YACZkL,QAAQ,CAAC,IAAI,EAAE/a,UAAU,EAAE6P,QAAQ,EAAE3Q,OAAO,CAAC;UAC/C,CAAC;UACD8c,YAAY,EAAE,IAAI;UAClB7d,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIuP,KAAK,GAAG,CAAC,CAAC,8BAA8BqO,WAAW,GAAG,EAAE,CAAC,cAAc;QAChFne,MAAM,CAACC,cAAc,CAAC8d,SAAS,EAAE3b,UAAU,EAAE;UAC3C2G,KAAKA,CAAC,GAAGsV,IAAI,EAAE;YACb,IAAIC,GAAG;YACP,MAAMxd,GAAG,GAAGD,UAAU,CAAC,IAAI,CAAC;YAC5B,OAAO,CAACyd,GAAG,GAAGxd,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACc,mBAAmB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG0c,GAAG,CAACnb,IAAI,CAAC,MAAM;cAC9F,IAAIob,GAAG;cACP,OAAO,CAACA,GAAG,GAAGzd,GAAG,CAACK,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGod,GAAG,CAACnc,UAAU,CAAC,CAAC,GAAGic,IAAI,CAAC;YAC/E,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAKvO,KAAK,GAAG,CAAC,CAAC,4BAA6B;MAC1C,MAAM0O,kBAAkB,GAAG,eAAgB,IAAI7c,GAAG,CAAC,CAAC;MACpDoc,SAAS,CAACU,wBAAwB,GAAG,UAASC,QAAQ,EAAE1M,QAAQ,EAAEC,QAAQ,EAAE;QAC1EzN,GAAG,CAACE,GAAG,CAAC,MAAM;UACZ,IAAI4Z,GAAG;UACP,MAAMxE,QAAQ,GAAG0E,kBAAkB,CAACle,GAAG,CAACoe,QAAQ,CAAC;UACjD,IAAI,IAAI,CAACC,cAAc,CAAC7E,QAAQ,CAAC,EAAE;YACjC7H,QAAQ,GAAG,IAAI,CAAC6H,QAAQ,CAAC;YACzB,OAAO,IAAI,CAACA,QAAQ,CAAC;UACvB,CAAC,MAAM,IAAIiE,SAAS,CAACY,cAAc,CAAC7E,QAAQ,CAAC,IAAI,OAAO,IAAI,CAACA,QAAQ,CAAC,KAAK,QAAQ;UAAI;UACvF,IAAI,CAACA,QAAQ,CAAC,IAAI7H,QAAQ,EAAE;YAC1B;UACF,CAAC,MAAM,IAAI6H,QAAQ,IAAI,IAAI,EAAE;YAC3B,MAAM7Y,OAAO,GAAGJ,UAAU,CAAC,IAAI,CAAC;YAChC,MAAM+d,MAAM,GAAG3d,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACM,OAAO;YACzD,IAAIqd,MAAM,IAAI,EAAEA,MAAM,GAAG,CAAC,CAAC,6BAA6B,IAAIA,MAAM,GAAG,GAAG,CAAC,sBAAsB3M,QAAQ,KAAKD,QAAQ,EAAE;cACpH,MAAM+I,QAAQ,GAAG9Z,OAAO,CAACE,cAAc;cACvC,MAAM0d,KAAK,GAAG,CAACP,GAAG,GAAGhd,OAAO,CAACoc,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGY,GAAG,CAACI,QAAQ,CAAC;cACzEG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC7S,OAAO,CAAE8S,YAAY,IAAK;gBACvD,IAAI/D,QAAQ,CAAC+D,YAAY,CAAC,IAAI,IAAI,EAAE;kBAClC/D,QAAQ,CAAC+D,YAAY,CAAC,CAACC,IAAI,CAAChE,QAAQ,EAAE9I,QAAQ,EAAED,QAAQ,EAAE0M,QAAQ,CAAC;gBACrE;cACF,CAAC,CAAC;YACJ;YACA;UACF;UACA,IAAI,CAAC5E,QAAQ,CAAC,GAAG7H,QAAQ,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC6H,QAAQ,CAAC,KAAK,SAAS,GAAG,KAAK,GAAG7H,QAAQ;QAC9F,CAAC,CAAC;MACJ,CAAC;MACD6L,IAAI,CAACkB,kBAAkB,GAAGzU,KAAK,CAAC+K,IAAI,CAClC,eAAgB,IAAIrE,GAAG,CAAC,CACtB,GAAGjR,MAAM,CAAC+K,IAAI,CAAC,CAAC1C,EAAE,GAAG/G,OAAO,CAACoc,UAAU,KAAK,IAAI,GAAGrV,EAAE,GAAG,CAAC,CAAC,CAAC,EAC3D,GAAG4V,OAAO,CAACjT,MAAM,CAAC,CAAC,CAACiU,CAAC,EAAE9P,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,CAACxG,GAAG,CAAC,CAAC,CAACmR,QAAQ,EAAE3K,CAAC,CAAC,KAAK;QACjF,IAAImP,GAAG;QACP,MAAMI,QAAQ,GAAGvP,CAAC,CAAC,CAAC,CAAC,IAAI2K,QAAQ;QACjC0E,kBAAkB,CAACtd,GAAG,CAACwd,QAAQ,EAAE5E,QAAQ,CAAC;QAC1C,IAAI3K,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,mBAAmB;UAChC,CAACmP,GAAG,GAAGhd,OAAO,CAACuY,gBAAgB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyE,GAAG,CAAC5X,IAAI,CAAC,CAACoT,QAAQ,EAAE4E,QAAQ,CAAC,CAAC;QACpF;QACA,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH,CACH,CAAC;IACH;EACF;EACA,OAAOZ,IAAI;AACb,CAAC;;AAED;AACA,IAAIoB,mBAAmB;EAAA,IAAAC,KAAA,GAAAxD,iBAAA,CAAG,WAAOxZ,GAAG,EAAElB,OAAO,EAAEK,OAAO,EAAEsB,YAAY,EAAK;IACvE,IAAIkb,IAAI;IACR,IAAI,CAAC7c,OAAO,CAACM,OAAO,GAAG,EAAE,CAAC,mCAAmC,CAAC,EAAE;MAC9DN,OAAO,CAACM,OAAO,IAAI,EAAE,CAAC;MACtB,MAAMyB,QAAQ,GAAG1B,OAAO,CAAC2B,cAAc;MACvC,IAAID,QAAQ,EAAE;QACZ,MAAMoc,UAAU,GAAGzc,UAAU,CAACrB,OAAO,CAAC;QACtC,IAAI8d,UAAU,IAAI,MAAM,IAAIA,UAAU,EAAE;UACtC,MAAMC,OAAO,GAAG5V,UAAU,CAAC,CAAC;UAC5BqU,IAAI,SAASsB,UAAU;UACvBC,OAAO,CAAC,CAAC;QACX,CAAC,MAAM;UACLvB,IAAI,GAAGsB,UAAU;QACnB;QACA,IAAI,CAACtB,IAAI,EAAE;UACT,MAAM,IAAI9C,KAAK,CAAC,oBAAoB1Z,OAAO,CAACwB,SAAS,IAAI7B,OAAO,CAACsO,UAAU,iBAAiB,CAAC;QAC/F;QACA,IAAI,CAACuO,IAAI,CAACwB,SAAS,EAAE;UACnB;YACEhe,OAAO,CAACoc,UAAU,GAAGI,IAAI,CAACE,QAAQ;UACpC;UACAH,cAAc,CAACC,IAAI,EAAExc,OAAO,EAAE,CAAC,CAAC,gBAAgB,CAAC;UACjDwc,IAAI,CAACwB,SAAS,GAAG,IAAI;QACvB;QACA,MAAMC,cAAc,GAAGjW,UAAU,CAAC,gBAAgB,EAAEhI,OAAO,CAACwB,SAAS,CAAC;QACtE;UACE7B,OAAO,CAACM,OAAO,IAAI,CAAC,CAAC;QACvB;QACA,IAAI;UACF,IAAIuc,IAAI,CAAC7c,OAAO,CAAC;QACnB,CAAC,CAAC,OAAOqB,CAAC,EAAE;UACVD,YAAY,CAACC,CAAC,CAAC;QACjB;QACA;UACErB,OAAO,CAACM,OAAO,IAAI,CAAC,CAAC,CAAC;QACxB;QACA;UACEN,OAAO,CAACM,OAAO,IAAI,GAAG,CAAC;QACzB;QACAge,cAAc,CAAC,CAAC;QAChBC,qBAAqB,CAACve,OAAO,CAACE,cAAc,CAAC;MAC/C,CAAC,MAAM;QACL2c,IAAI,GAAG3b,GAAG,CAACsd,WAAW;QACtB,MAAMC,MAAM,GAAGvd,GAAG,CAACwd,SAAS;QAC5BC,cAAc,CAACC,WAAW,CAACH,MAAM,CAAC,CAACvc,IAAI,CAAC,MAAMlC,OAAO,CAACM,OAAO,IAAI,GAAG,CAAC,kBAAkB,CAAC;MAC1F;MACA,IAAIuc,IAAI,IAAIA,IAAI,CAAChf,KAAK,EAAE;QACtB,IAAIA,KAAK;QACT,IAAI,OAAOgf,IAAI,CAAChf,KAAK,KAAK,QAAQ,EAAE;UAClCA,KAAK,GAAGgf,IAAI,CAAChf,KAAK;QACpB,CAAC,MAAM,IAAI,OAAOgf,IAAI,CAAChf,KAAK,KAAK,QAAQ,EAAE;UACzCmC,OAAO,CAACsO,UAAU,GAAGN,WAAW,CAAC9M,GAAG,CAAC;UACrC,IAAIlB,OAAO,CAACsO,UAAU,EAAE;YACtBzQ,KAAK,GAAGgf,IAAI,CAAChf,KAAK,CAACmC,OAAO,CAACsO,UAAU,CAAC;UACxC;QACF;QACA,MAAMkB,QAAQ,GAAGK,UAAU,CAACxP,OAAO,EAAEL,OAAO,CAACsO,UAAU,CAAC;QACxD,IAAI,CAAClM,MAAM,CAAC6N,GAAG,CAACT,QAAQ,CAAC,EAAE;UACzB,MAAMqP,iBAAiB,GAAGxW,UAAU,CAAC,gBAAgB,EAAEhI,OAAO,CAACwB,SAAS,CAAC;UACzE0N,aAAa,CAACC,QAAQ,EAAE3R,KAAK,EAAE,CAAC,EAAEwC,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,6BAA6B,CAAC;UACpFue,iBAAiB,CAAC,CAAC;QACrB;MACF;IACF;IACA,MAAMtF,iBAAiB,GAAGvZ,OAAO,CAAC0Z,mBAAmB;IACrD,MAAMoF,QAAQ,GAAGA,CAAA,KAAMrF,cAAc,CAACzZ,OAAO,EAAE,IAAI,CAAC;IACpD,IAAIuZ,iBAAiB,IAAIA,iBAAiB,CAAC,MAAM,CAAC,EAAE;MAClDA,iBAAiB,CAAC,MAAM,CAAC,CAAC9T,IAAI,CAACqZ,QAAQ,CAAC;IAC1C,CAAC,MAAM;MACLA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAAA,gBAvEGb,mBAAmBA,CAAAc,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAhB,KAAA,CAAA7C,KAAA,OAAAC,SAAA;EAAA;AAAA,GAuEtB;AACD,IAAIiD,qBAAqB,GAAIzE,QAAQ,IAAK;EACxC;IACEK,QAAQ,CAACL,QAAQ,EAAE,mBAAmB,CAAC;EACzC;AACF,CAAC;;AAED;AACA,IAAIpf,iBAAiB,GAAIwG,GAAG,IAAK;EAC/B,IAAI,CAACqC,GAAG,CAACjD,OAAO,GAAG,CAAC,CAAC,6BAA6B,CAAC,EAAE;IACnD,MAAMN,OAAO,GAAGJ,UAAU,CAACsB,GAAG,CAAC;IAC/B,MAAMb,OAAO,GAAGL,OAAO,CAACQ,SAAS;IACjC,MAAM2e,YAAY,GAAG9W,UAAU,CAAC,mBAAmB,EAAEhI,OAAO,CAACwB,SAAS,CAAC;IACvE,IAAI,EAAE7B,OAAO,CAACM,OAAO,GAAG,CAAC,CAAC,mBAAmB,EAAE;MAC7CN,OAAO,CAACM,OAAO,IAAI,CAAC,CAAC;MACrB,IAAIoL,MAAM;MACV;QACEA,MAAM,GAAGxK,GAAG,CAACqG,YAAY,CAAC7E,UAAU,CAAC;QACrC,IAAIgJ,MAAM,EAAE;UACV,IAAIrL,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;YACpD,MAAMkP,QAAQ,GAAGG,QAAQ,CAACzO,GAAG,CAAC0K,UAAU,EAAEvL,OAAO,EAAEa,GAAG,CAACqG,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC9ErG,GAAG,CAAC0P,SAAS,CAAChD,MAAM,CAAC4B,QAAQ,GAAG,IAAI,EAAEA,QAAQ,GAAG,IAAI,CAAC;UACxD;UACAhE,uBAAuB,CAACtK,GAAG,EAAEb,OAAO,CAACwB,SAAS,EAAE6J,MAAM,EAAE1L,OAAO,CAAC;QAClE;MACF;MACA,IAAI,CAAC0L,MAAM,EAAE;QACX;QAAI;QACJrL,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC,0BAA0B,CAAC,CAAC,yBAAyB,EAAE;UAC1E8e,mBAAmB,CAACle,GAAG,CAAC;QAC1B;MACF;MACA;QACE,IAAIqY,iBAAiB,GAAGrY,GAAG;QAC3B,OAAOqY,iBAAiB,GAAGA,iBAAiB,CAAC7M,UAAU,IAAI6M,iBAAiB,CAACrJ,IAAI,EAAE;UACjF,IAAIqJ,iBAAiB,CAACnM,QAAQ,KAAK,CAAC,CAAC,qBAAqBmM,iBAAiB,CAACR,YAAY,CAAC,MAAM,CAAC,IAAIQ,iBAAiB,CAAC,KAAK,CAAC,IAAIA,iBAAiB,CAAC,KAAK,CAAC,EAAE;YACxJD,gBAAgB,CAACtZ,OAAO,EAAEA,OAAO,CAAC0Z,mBAAmB,GAAGH,iBAAiB,CAAC;YAC1E;UACF;QACF;MACF;MACA,IAAIlZ,OAAO,CAAC+b,SAAS,EAAE;QACrBrd,MAAM,CAACke,OAAO,CAAC5c,OAAO,CAAC+b,SAAS,CAAC,CAAC1U,GAAG,CAAC,CAAC,CAACvG,UAAU,EAAE,CAAC+b,WAAW,CAAC,CAAC,KAAK;UACrE,IAAIA,WAAW,GAAG,EAAE,CAAC,cAAchc,GAAG,CAACwc,cAAc,CAACvc,UAAU,CAAC,EAAE;YACjE,MAAM2G,KAAK,GAAG5G,GAAG,CAACC,UAAU,CAAC;YAC7B,OAAOD,GAAG,CAACC,UAAU,CAAC;YACtBD,GAAG,CAACC,UAAU,CAAC,GAAG2G,KAAK;UACzB;QACF,CAAC,CAAC;MACJ;MACA;QACEmW,mBAAmB,CAAC/c,GAAG,EAAElB,OAAO,EAAEK,OAAO,CAAC;MAC5C;IACF,CAAC,MAAM;MACLgf,qBAAqB,CAACne,GAAG,EAAElB,OAAO,EAAEK,OAAO,CAACif,WAAW,CAAC;MACxD,IAAItf,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,cAAc,EAAE;QACrDqe,qBAAqB,CAACve,OAAO,CAACE,cAAc,CAAC;MAC/C,CAAC,MAAM,IAAIF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe,gBAAgB,EAAE;QAC9Df,OAAO,CAACe,gBAAgB,CAACmB,IAAI,CAAC,MAAMqc,qBAAqB,CAACve,OAAO,CAACE,cAAc,CAAC,CAAC;MACpF;IACF;IACAif,YAAY,CAAC,CAAC;EAChB;AACF,CAAC;AACD,IAAIC,mBAAmB,GAAIle,GAAG,IAAK;EACjC,MAAMqe,aAAa,GAAGre,GAAG,CAAC,MAAM,CAAC,GAAGgC,GAAG,CAACsc,aAAa,CACnD,EACF,CAAC;EACDD,aAAa,CAAC,MAAM,CAAC,GAAG,IAAI;EAC5B5S,YAAY,CAACzL,GAAG,EAAEqe,aAAa,EAAEre,GAAG,CAACue,UAAU,CAAC;AAClD,CAAC;AACD,IAAIC,kBAAkB,GAAI5F,QAAQ,IAAK;EACrC;IACEK,QAAQ,CAACL,QAAQ,EAAE,sBAAsB,CAAC;EAC5C;AACF,CAAC;AACD,IAAIhf,oBAAoB;EAAA,IAAA6kB,KAAA,GAAAjF,iBAAA,CAAG,WAAOxZ,GAAG,EAAK;IACxC,IAAI,CAACqC,GAAG,CAACjD,OAAO,GAAG,CAAC,CAAC,6BAA6B,CAAC,EAAE;MACnD,MAAMN,OAAO,GAAGJ,UAAU,CAACsB,GAAG,CAAC;MAC/B;QACE,IAAIlB,OAAO,CAAC4f,aAAa,EAAE;UACzB5f,OAAO,CAAC4f,aAAa,CAAClY,GAAG,CAAEmY,UAAU,IAAKA,UAAU,CAAC,CAAC,CAAC;UACvD7f,OAAO,CAAC4f,aAAa,GAAG,KAAK,CAAC;QAChC;MACF;MACA,IAAI5f,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,cAAc,EAAE;QACrDwf,kBAAkB,CAAC1f,OAAO,CAACE,cAAc,CAAC;MAC5C,CAAC,MAAM,IAAIF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACe,gBAAgB,EAAE;QAC9Df,OAAO,CAACe,gBAAgB,CAACmB,IAAI,CAAC,MAAMwd,kBAAkB,CAAC1f,OAAO,CAACE,cAAc,CAAC,CAAC;MACjF;IACF;EACF,CAAC;EAAA,gBAfGpF,oBAAoBA,CAAAglB,GAAA;IAAA,OAAAH,KAAA,CAAAtE,KAAA,OAAAC,SAAA;EAAA;AAAA,GAevB;AACD,IAAIyE,oBAAoB,GAAGA,CAACC,oBAAoB,EAAEC,mBAAmB,KAAK;EACxEC,cAAc,CAACF,oBAAoB,CAAC;EACpCG,oBAAoB,CAACH,oBAAoB,CAAC;EAC1CI,eAAe,CAACJ,oBAAoB,CAAC;EACrCK,gBAAgB,CAACL,oBAAoB,CAAC;EACtCM,8BAA8B,CAACN,oBAAoB,CAAC;EACpDO,2BAA2B,CAACP,oBAAoB,CAAC;EACjDQ,2BAA2B,CAACR,oBAAoB,CAAC;EACjDS,gBAAgB,CAACT,oBAAoB,CAAC;EACtCU,mBAAmB,CAACV,oBAAoB,EAAEC,mBAAmB,CAAC;EAC9DU,oBAAoB,CAACX,oBAAoB,CAAC;AAC5C,CAAC;AACD,IAAIE,cAAc,GAAIU,oBAAoB,IAAK;EAC7C,MAAMC,YAAY,GAAGD,oBAAoB,CAACE,SAAS;EACnDF,oBAAoB,CAACE,SAAS,GAAG,UAASC,IAAI,EAAE;IAC9C,MAAMC,OAAO,GAAG,IAAI;IACpB,MAAMC,WAAW,GAAGD,OAAO,CAACpV,UAAU,IAAInH,cAAc;IACxD,MAAMyc,UAAU,GAAGL,YAAY,CAAC/C,IAAI,CAACkD,OAAO,EAAEC,WAAW,GAAGF,IAAI,GAAG,KAAK,CAAC;IACzE,IAAI,CAACE,WAAW,IAAIF,IAAI,EAAE;MACxB,IAAIlb,EAAE,GAAG,CAAC;MACV,IAAIsb,OAAO,EAAEC,cAAc;MAC3B,MAAMC,eAAe,GAAG,CACtB,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,CACR;MACD,OAAOxb,EAAE,GAAGmb,OAAO,CAACvT,UAAU,CAAC3H,MAAM,EAAED,EAAE,EAAE,EAAE;QAC3Csb,OAAO,GAAGH,OAAO,CAACvT,UAAU,CAAC5H,EAAE,CAAC,CAAC,MAAM,CAAC;QACxCub,cAAc,GAAGC,eAAe,CAACC,KAAK,CAAEC,YAAY,IAAK,CAACP,OAAO,CAACvT,UAAU,CAAC5H,EAAE,CAAC,CAAC0b,YAAY,CAAC,CAAC;QAC/F,IAAIJ,OAAO,EAAE;UACX,IAAID,UAAU,CAACM,aAAa,EAAE;YAC5BN,UAAU,CAACM,aAAa,CAACL,OAAO,CAACL,SAAS,CAAC,IAAI,CAAC,CAAC;UACnD,CAAC,MAAM;YACLI,UAAU,CAACnU,WAAW,CAACoU,OAAO,CAACL,SAAS,CAAC,IAAI,CAAC,CAAC;UACjD;QACF;QACA,IAAIM,cAAc,EAAE;UAClBF,UAAU,CAACnU,WAAW,CAACiU,OAAO,CAACvT,UAAU,CAAC5H,EAAE,CAAC,CAACib,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE;MACF;IACF;IACA,OAAOI,UAAU;EACnB,CAAC;AACH,CAAC;AACD,IAAIf,oBAAoB,GAAIS,oBAAoB,IAAK;EACnDA,oBAAoB,CAACY,aAAa,GAAGZ,oBAAoB,CAAC7T,WAAW;EACrE6T,oBAAoB,CAAC7T,WAAW,GAAG,UAAS0U,QAAQ,EAAE;IACpD,MAAMzY,QAAQ,GAAGyY,QAAQ,CAAC,MAAM,CAAC,GAAGC,WAAW,CAACD,QAAQ,CAAC;IACzD,MAAME,QAAQ,GAAGC,eAAe,CAAC,IAAI,CAACnU,UAAU,EAAEzE,QAAQ,EAAE,IAAI,CAACT,OAAO,CAAC;IACzE,IAAIoZ,QAAQ,EAAE;MACZ,MAAME,cAAc,GAAGC,qBAAqB,CAACH,QAAQ,EAAE3Y,QAAQ,CAAC;MAChE,MAAM+Y,WAAW,GAAGF,cAAc,CAACA,cAAc,CAAC/b,MAAM,GAAG,CAAC,CAAC;MAC7D,MAAMkc,YAAY,GAAGrV,YAAY,CAACoV,WAAW,CAACrV,UAAU,EAAE+U,QAAQ,EAAEM,WAAW,CAACnV,WAAW,CAAC;MAC5FiK,4BAA4B,CAAC,IAAI,CAAC;MAClC,OAAOmL,YAAY;IACrB;IACA,OAAO,IAAI,CAACR,aAAa,CAACC,QAAQ,CAAC;EACrC,CAAC;AACH,CAAC;AACD,IAAId,oBAAoB,GAAIsB,gBAAgB,IAAK;EAC/CA,gBAAgB,CAACC,aAAa,GAAGD,gBAAgB,CAACE,WAAW;EAC7DF,gBAAgB,CAACE,WAAW,GAAG,UAASC,QAAQ,EAAE;IAChD,IAAIA,QAAQ,IAAI,OAAOA,QAAQ,CAAC,MAAM,CAAC,KAAK,WAAW,EAAE;MACvD,MAAMT,QAAQ,GAAGC,eAAe,CAAC,IAAI,CAACnU,UAAU,EAAE2U,QAAQ,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC7Z,OAAO,CAAC;MACjF,IAAIoZ,QAAQ,EAAE;QACZ,MAAME,cAAc,GAAGC,qBAAqB,CAACH,QAAQ,EAAES,QAAQ,CAAC,MAAM,CAAC,CAAC;QACxE,MAAMC,YAAY,GAAGR,cAAc,CAAC5T,IAAI,CAAE8D,CAAC,IAAKA,CAAC,KAAKqQ,QAAQ,CAAC;QAC/D,IAAIC,YAAY,EAAE;UAChBA,YAAY,CAACzU,MAAM,CAAC,CAAC;UACrBiJ,4BAA4B,CAAC,IAAI,CAAC;UAClC;QACF;MACF;IACF;IACA,OAAO,IAAI,CAACqL,aAAa,CAACE,QAAQ,CAAC;EACrC,CAAC;AACH,CAAC;AACD,IAAI/B,gBAAgB,GAAIO,oBAAoB,IAAK;EAC/C,MAAM0B,eAAe,GAAG1B,oBAAoB,CAAC2B,OAAO;EACpD3B,oBAAoB,CAAC2B,OAAO,GAAG,UAAS,GAAG9L,WAAW,EAAE;IACtDA,WAAW,CAAC1L,OAAO,CAAE0W,QAAQ,IAAK;MAChC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAG,IAAI,CAACe,aAAa,CAAC3O,cAAc,CAAC4N,QAAQ,CAAC;MACxD;MACA,MAAMzY,QAAQ,GAAGyY,QAAQ,CAAC,MAAM,CAAC,GAAGC,WAAW,CAACD,QAAQ,CAAC;MACzD,MAAME,QAAQ,GAAGC,eAAe,CAAC,IAAI,CAACnU,UAAU,EAAEzE,QAAQ,EAAE,IAAI,CAACT,OAAO,CAAC;MACzE,IAAIoZ,QAAQ,EAAE;QACZ,MAAMc,eAAe,GAAGtf,QAAQ,CAAC0Q,cAAc,CAAC,EAAE,CAAC;QACnD4O,eAAe,CAAC,MAAM,CAAC,GAAGhB,QAAQ;QAClCE,QAAQ,CAAC,MAAM,CAAC,CAACjV,UAAU,CAAC8U,aAAa,CAACiB,eAAe,CAAC;QAC1DhB,QAAQ,CAAC,MAAM,CAAC,GAAGgB,eAAe;QAClC,MAAMZ,cAAc,GAAGC,qBAAqB,CAACH,QAAQ,EAAE3Y,QAAQ,CAAC;QAChE,MAAM+Y,WAAW,GAAGF,cAAc,CAAC,CAAC,CAAC;QACrC,OAAOlV,YAAY,CAACoV,WAAW,CAACrV,UAAU,EAAE+U,QAAQ,EAAEM,WAAW,CAACnV,WAAW,CAAC;MAChF;MACA,IAAI6U,QAAQ,CAACrU,QAAQ,KAAK,CAAC,IAAI,CAAC,CAACqU,QAAQ,CAACla,YAAY,CAAC,MAAM,CAAC,EAAE;QAC9Dka,QAAQ,CAAC3K,MAAM,GAAG,IAAI;MACxB;MACA,OAAOwL,eAAe,CAACxE,IAAI,CAAC,IAAI,EAAE2D,QAAQ,CAAC;IAC7C,CAAC,CAAC;EACJ,CAAC;AACH,CAAC;AACD,IAAIrB,eAAe,GAAIQ,oBAAoB,IAAK;EAC9CA,oBAAoB,CAAC8B,MAAM,GAAG,UAAS,GAAGjM,WAAW,EAAE;IACrDA,WAAW,CAAC1L,OAAO,CAAE0W,QAAQ,IAAK;MAChC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAG,IAAI,CAACe,aAAa,CAAC3O,cAAc,CAAC4N,QAAQ,CAAC;MACxD;MACA,IAAI,CAAC1U,WAAW,CAAC0U,QAAQ,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC;AACH,CAAC;AACD,IAAIlB,2BAA2B,GAAIK,oBAAoB,IAAK;EAC1D,MAAM+B,0BAA0B,GAAG/B,oBAAoB,CAACgC,kBAAkB;EAC1EhC,oBAAoB,CAACgC,kBAAkB,GAAG,UAASC,QAAQ,EAAEpY,IAAI,EAAE;IACjE,IAAIoY,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,WAAW,EAAE;MACzD,OAAOF,0BAA0B,CAAC7E,IAAI,CAAC,IAAI,EAAE+E,QAAQ,EAAEpY,IAAI,CAAC;IAC9D;IACA,MAAMqY,SAAS,GAAG,IAAI,CAACN,aAAa,CAAC3U,aAAa,CAAC,GAAG,CAAC;IACvD,IAAI/C,IAAI;IACRgY,SAAS,CAAC3S,SAAS,GAAG1F,IAAI;IAC1B,IAAIoY,QAAQ,KAAK,YAAY,EAAE;MAC7B,OAAO/X,IAAI,GAAGgY,SAAS,CAACrD,UAAU,EAAE;QAClC,IAAI,CAAC8C,OAAO,CAACzX,IAAI,CAAC;MACpB;IACF,CAAC,MAAM,IAAI+X,QAAQ,KAAK,WAAW,EAAE;MACnC,OAAO/X,IAAI,GAAGgY,SAAS,CAACrD,UAAU,EAAE;QAClC,IAAI,CAACiD,MAAM,CAAC5X,IAAI,CAAC;MACnB;IACF;EACF,CAAC;AACH,CAAC;AACD,IAAI0V,2BAA2B,GAAII,oBAAoB,IAAK;EAC1DA,oBAAoB,CAACmC,kBAAkB,GAAG,UAASF,QAAQ,EAAEpY,IAAI,EAAE;IACjE,IAAI,CAACmY,kBAAkB,CAACC,QAAQ,EAAEpY,IAAI,CAAC;EACzC,CAAC;AACH,CAAC;AACD,IAAI6V,8BAA8B,GAAIM,oBAAoB,IAAK;EAC7D,MAAMoC,6BAA6B,GAAGpC,oBAAoB,CAACqC,qBAAqB;EAChFrC,oBAAoB,CAACqC,qBAAqB,GAAG,UAASJ,QAAQ,EAAE9nB,OAAO,EAAE;IACvE,IAAI8nB,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,WAAW,EAAE;MACzD,OAAOG,6BAA6B,CAAClF,IAAI,CAAC,IAAI,EAAE+E,QAAQ,EAAE9nB,OAAO,CAAC;IACpE;IACA,IAAI8nB,QAAQ,KAAK,YAAY,EAAE;MAC7B,IAAI,CAACN,OAAO,CAACxnB,OAAO,CAAC;MACrB,OAAOA,OAAO;IAChB,CAAC,MAAM,IAAI8nB,QAAQ,KAAK,WAAW,EAAE;MACnC,IAAI,CAACH,MAAM,CAAC3nB,OAAO,CAAC;MACpB,OAAOA,OAAO;IAChB;IACA,OAAOA,OAAO;EAChB,CAAC;AACH,CAAC;AACD,IAAI0lB,gBAAgB,GAAIT,oBAAoB,IAAK;EAC/C,MAAMkD,UAAU,GAAGnkB,MAAM,CAACokB,wBAAwB,CAACC,IAAI,CAACtG,SAAS,EAAE,aAAa,CAAC;EACjF/d,MAAM,CAACC,cAAc,CAACghB,oBAAoB,EAAE,eAAe,EAAEkD,UAAU,CAAC;EACxE;IACEnkB,MAAM,CAACC,cAAc,CAACghB,oBAAoB,EAAE,aAAa,EAAE;MACzD;MACA;MACA3gB,GAAGA,CAAA,EAAG;QACJ,MAAMgkB,YAAY,GAAGC,oBAAoB,CAAC,IAAI,CAAC7V,UAAU,CAAC;QAC1D,MAAME,WAAW,GAAG0V,YAAY,CAAC3b,GAAG,CAAEoD,IAAI,IAAK;UAC7C,IAAI3D,EAAE,EAAEC,EAAE;UACV,MAAMqD,IAAI,GAAG,EAAE;UACf,IAAI8Y,WAAW,GAAGzY,IAAI,CAAC8B,WAAW;UAClC,OAAO2W,WAAW,IAAIA,WAAW,CAAC,MAAM,CAAC,KAAKzY,IAAI,CAAC,MAAM,CAAC,EAAE;YAC1D,IAAIyY,WAAW,CAACnW,QAAQ,KAAK,CAAC,CAAC,mBAAmBmW,WAAW,CAACnW,QAAQ,KAAK,CAAC,CAAC,oBAAoB;cAC/F3C,IAAI,CAAChF,IAAI,CAAC,CAAC2B,EAAE,GAAG,CAACD,EAAE,GAAGoc,WAAW,CAAC5V,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGxG,EAAE,CAAC6P,IAAI,CAAC,CAAC,KAAK,IAAI,GAAG5P,EAAE,GAAG,EAAE,CAAC;YACjG;YACAmc,WAAW,GAAGA,WAAW,CAAC3W,WAAW;UACvC;UACA,OAAOnC,IAAI,CAACV,MAAM,CAAElK,GAAG,IAAKA,GAAG,KAAK,EAAE,CAAC,CAACoK,IAAI,CAAC,GAAG,CAAC;QACnD,CAAC,CAAC,CAACF,MAAM,CAAEU,IAAI,IAAKA,IAAI,KAAK,EAAE,CAAC,CAACR,IAAI,CAAC,GAAG,CAAC;QAC1C,OAAO,GAAG,GAAG0D,WAAW,GAAG,GAAG;MAChC,CAAC;MACD;MACA;MACA;MACA1N,GAAGA,CAAC6H,KAAK,EAAE;QACT,MAAMub,YAAY,GAAGC,oBAAoB,CAAC,IAAI,CAAC7V,UAAU,CAAC;QAC1D4V,YAAY,CAACtY,OAAO,CAAED,IAAI,IAAK;UAC7B,IAAIyY,WAAW,GAAGzY,IAAI,CAAC8B,WAAW;UAClC,OAAO2W,WAAW,IAAIA,WAAW,CAAC,MAAM,CAAC,KAAKzY,IAAI,CAAC,MAAM,CAAC,EAAE;YAC1D,MAAM0Y,GAAG,GAAGD,WAAW;YACvBA,WAAW,GAAGA,WAAW,CAAC3W,WAAW;YACrC4W,GAAG,CAAC5V,MAAM,CAAC,CAAC;UACd;UACA,IAAI9C,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;YACvB,MAAM2Y,QAAQ,GAAG,IAAI,CAACjB,aAAa,CAAC3O,cAAc,CAAC/L,KAAK,CAAC;YACzD2b,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE;YACrB9W,YAAY,CAAC7B,IAAI,CAAC6L,aAAa,EAAE8M,QAAQ,EAAE3Y,IAAI,CAAC8B,WAAW,CAAC;UAC9D,CAAC,MAAM;YACL9B,IAAI,CAAC8C,MAAM,CAAC,CAAC;UACf;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAI8S,mBAAmB,GAAGA,CAACxf,GAAG,EAAEb,OAAO,KAAK;EAC1C,MAAMqjB,YAAY,SAASpa,KAAK,CAAC;IAC/Bqa,IAAIA,CAAC5R,CAAC,EAAE;MACN,OAAO,IAAI,CAACA,CAAC,CAAC;IAChB;EACF;EACA,IAAI1R,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,0BAA0B;IAChD,MAAMsjB,YAAY,GAAG1iB,GAAG,CAAC2iB,gBAAgB,CAAC,YAAY,CAAC;IACvD9kB,MAAM,CAACC,cAAc,CAACkC,GAAG,EAAE,UAAU,EAAE;MACrC7B,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACoO,UAAU,CAAC/F,GAAG,CAAEqK,CAAC,IAAKA,CAAC,CAAC3E,QAAQ,KAAK,CAAC,CAAC;MACrD;IACF,CAAC,CAAC;IACFrO,MAAM,CAACC,cAAc,CAACkC,GAAG,EAAE,mBAAmB,EAAE;MAC9C7B,GAAGA,CAAA,EAAG;QACJ,OAAO6B,GAAG,CAAC4H,QAAQ,CAAChD,MAAM;MAC5B;IACF,CAAC,CAAC;IACF/G,MAAM,CAACC,cAAc,CAACkC,GAAG,EAAE,YAAY,EAAE;MACvC7B,GAAGA,CAAA,EAAG;QACJ,MAAMoO,UAAU,GAAGmW,YAAY,CAAC9F,IAAI,CAAC,IAAI,CAAC;QAC1C,IAAI,CAACva,GAAG,CAACjD,OAAO,GAAG,CAAC,CAAC,6BAA6B,CAAC,IAAIV,UAAU,CAAC,IAAI,CAAC,CAACU,OAAO,GAAG,CAAC,CAAC,mBAAmB;UACrG,MAAM2H,MAAM,GAAG,IAAIyb,YAAY,CAAC,CAAC;UACjC,KAAK,IAAI7d,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG4H,UAAU,CAAC3H,MAAM,EAAED,EAAE,EAAE,EAAE;YAC7C,MAAMpI,IAAI,GAAGgQ,UAAU,CAAC5H,EAAE,CAAC,CAAC,MAAM,CAAC;YACnC,IAAIpI,IAAI,EAAE;cACRwK,MAAM,CAACxC,IAAI,CAAChI,IAAI,CAAC;YACnB;UACF;UACA,OAAOwK,MAAM;QACf;QACA,OAAOyb,YAAY,CAACrP,IAAI,CAAC5G,UAAU,CAAC;MACtC;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAI6V,oBAAoB,GAAI7V,UAAU,IAAK;EACzC,MAAM4V,YAAY,GAAG,EAAE;EACvB,KAAK,MAAM1P,SAAS,IAAIrK,KAAK,CAAC+K,IAAI,CAAC5G,UAAU,CAAC,EAAE;IAC9C,IAAIkG,SAAS,CAAC,MAAM,CAAC,EAAE;MACrB0P,YAAY,CAAC5d,IAAI,CAACkO,SAAS,CAAC;IAC9B;IACA0P,YAAY,CAAC5d,IAAI,CAAC,GAAG6d,oBAAoB,CAAC3P,SAAS,CAAClG,UAAU,CAAC,CAAC;EAClE;EACA,OAAO4V,YAAY;AACrB,CAAC;AACD,IAAI3B,WAAW,GAAI5W,IAAI,IAAKA,IAAI,CAAC,MAAM,CAAC,IAAIA,IAAI,CAACsC,QAAQ,KAAK,CAAC,IAAItC,IAAI,CAACvD,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE;AAClG,IAAIqa,eAAe,GAAGA,CAACnU,UAAU,EAAEzE,QAAQ,EAAE8a,QAAQ,KAAK;EACxD,IAAIje,EAAE,GAAG,CAAC;EACV,IAAI8N,SAAS;EACb,OAAO9N,EAAE,GAAG4H,UAAU,CAAC3H,MAAM,EAAED,EAAE,EAAE,EAAE;IACnC8N,SAAS,GAAGlG,UAAU,CAAC5H,EAAE,CAAC;IAC1B,IAAI8N,SAAS,CAAC,MAAM,CAAC,IAAIA,SAAS,CAAC,MAAM,CAAC,KAAK3K,QAAQ,IAAI2K,SAAS,CAAC,MAAM,CAAC,KAAKmQ,QAAQ,EAAE;MACzF,OAAOnQ,SAAS;IAClB;IACAA,SAAS,GAAGiO,eAAe,CAACjO,SAAS,CAAClG,UAAU,EAAEzE,QAAQ,EAAE8a,QAAQ,CAAC;IACrE,IAAInQ,SAAS,EAAE;MACb,OAAOA,SAAS;IAClB;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAImO,qBAAqB,GAAGA,CAAC/P,CAAC,EAAE/I,QAAQ,KAAK;EAC3C,MAAMyE,UAAU,GAAG,CAACsE,CAAC,CAAC;EACtB,OAAO,CAACA,CAAC,GAAGA,CAAC,CAACnF,WAAW,KAAKmF,CAAC,CAAC,MAAM,CAAC,KAAK/I,QAAQ,EAAE;IACpDyE,UAAU,CAAChI,IAAI,CAACsM,CAAC,CAAC;EACpB;EACA,OAAOtE,UAAU;AACnB,CAAC;;AAED;AACA,IAAIsW,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACjD,IAAI9c,EAAE;EACN,MAAM+c,YAAY,GAAG7b,UAAU,CAAC,CAAC;EACjC,MAAM8b,OAAO,GAAG,EAAE;EAClB,MAAMC,OAAO,GAAGH,OAAO,CAACG,OAAO,IAAI,EAAE;EACrC,MAAMC,eAAe,GAAGrhB,GAAG,CAAC2b,cAAc;EAC1C,MAAMvb,IAAI,GAAGF,GAAG,CAACE,IAAI;EACrB,MAAMkhB,WAAW,GAAG,eAAgBlhB,IAAI,CAACkE,aAAa,CAAC,eAAe,CAAC;EACvE,MAAMid,UAAU,GAAG,eAAgBrhB,GAAG,CAAC2K,aAAa,CAAC,OAAO,CAAC;EAC7D,MAAM2W,0BAA0B,GAAG,EAAE;EACrC,IAAIC,eAAe;EACnB,IAAIC,eAAe,GAAG,IAAI;EAC1B3lB,MAAM,CAACyF,MAAM,CAACjB,GAAG,EAAE0gB,OAAO,CAAC;EAC3B1gB,GAAG,CAACC,cAAc,GAAG,IAAI8C,GAAG,CAAC2d,OAAO,CAACU,YAAY,IAAI,IAAI,EAAEzhB,GAAG,CAAC0hB,OAAO,CAAC,CAACne,IAAI;EAC5E;IACElD,GAAG,CAACjD,OAAO,IAAI,CAAC,CAAC;EACnB;EACA,IAAIukB,iBAAiB,GAAG,KAAK;EAC7Bb,WAAW,CAACtc,GAAG,CAAEod,UAAU,IAAK;IAC9BA,UAAU,CAAC,CAAC,CAAC,CAACpd,GAAG,CAAEqd,WAAW,IAAK;MACjC,IAAI1H,GAAG;MACP,MAAMhd,OAAO,GAAG;QACdC,OAAO,EAAEykB,WAAW,CAAC,CAAC,CAAC;QACvBljB,SAAS,EAAEkjB,WAAW,CAAC,CAAC,CAAC;QACzB3I,SAAS,EAAE2I,WAAW,CAAC,CAAC,CAAC;QACzBzF,WAAW,EAAEyF,WAAW,CAAC,CAAC;MAC5B,CAAC;MACD,IAAI1kB,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,yBAAyB;QAC/CukB,iBAAiB,GAAG,IAAI;MAC1B;MACA;QACExkB,OAAO,CAAC+b,SAAS,GAAG2I,WAAW,CAAC,CAAC,CAAC;MACpC;MACA;QACE1kB,OAAO,CAACif,WAAW,GAAGyF,WAAW,CAAC,CAAC,CAAC;MACtC;MACA;QACE1kB,OAAO,CAACuY,gBAAgB,GAAG,EAAE;MAC/B;MACA;QACEvY,OAAO,CAACoc,UAAU,GAAG,CAACY,GAAG,GAAG0H,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG1H,GAAG,GAAG,CAAC,CAAC;MAChE;MACA,MAAM9U,OAAO,GAAGlI,OAAO,CAACwB,SAAS;MACjC,MAAMmjB,WAAW,GAAG,cAAc1hB,WAAW,CAAC;QAC5C;QACAkb,WAAWA,CAACyG,IAAI,EAAE;UAChB,KAAK,CAACA,IAAI,CAAC;UACX,IAAI,CAACC,2BAA2B,GAAG,KAAK;UACxCD,IAAI,GAAG,IAAI;UACX9kB,YAAY,CAAC8kB,IAAI,EAAE5kB,OAAO,CAAC;UAC3B,IAAIA,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;YACpD;cACE,IAAI,CAAC2kB,IAAI,CAACrZ,UAAU,EAAE;gBACpB;kBACEqZ,IAAI,CAACE,YAAY,CAAC;oBAChBxoB,IAAI,EAAE,MAAM;oBACZyoB,cAAc,EAAE,CAAC,EAAE/kB,OAAO,CAACC,OAAO,GAAG,EAAE,CAAC;kBAC1C,CAAC,CAAC;gBACJ;cACF,CAAC,MAAM;gBACL,IAAI2kB,IAAI,CAACrZ,UAAU,CAACjP,IAAI,KAAK,MAAM,EAAE;kBACnC,MAAM,IAAIod,KAAK,CACb,6CAA6C1Z,OAAO,CAACwB,SAAS,oBAAoBojB,IAAI,CAACrZ,UAAU,CAACjP,IAAI,+CACxG,CAAC;gBACH;cACF;YACF;UACF;QACF;QACAjC,iBAAiBA,CAAA,EAAG;UAClB,MAAMsF,OAAO,GAAGJ,UAAU,CAAC,IAAI,CAAC;UAChC,IAAI,CAAC,IAAI,CAACslB,2BAA2B,EAAE;YACrC,IAAI,CAACA,2BAA2B,GAAG,IAAI;YACvC7F,qBAAqB,CAAC,IAAI,EAAErf,OAAO,EAAEK,OAAO,CAACif,WAAW,CAAC;UAC3D;UACA,IAAImF,eAAe,EAAE;YACnBY,YAAY,CAACZ,eAAe,CAAC;YAC7BA,eAAe,GAAG,IAAI;UACxB;UACA,IAAIC,eAAe,EAAE;YACnBF,0BAA0B,CAAC/e,IAAI,CAAC,IAAI,CAAC;UACvC,CAAC,MAAM;YACLlC,GAAG,CAACE,GAAG,CAAC,MAAM/I,iBAAiB,CAAC,IAAI,CAAC,CAAC;UACxC;QACF;QACAI,oBAAoBA,CAAA,EAAG;UACrByI,GAAG,CAACE,GAAG,CAAC,MAAM3I,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC3C;QACAwqB,gBAAgBA,CAAA,EAAG;UACjB,OAAO1lB,UAAU,CAAC,IAAI,CAAC,CAACmB,gBAAgB;QAC1C;MACF,CAAC;MACD;QACE,IAAIV,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;UACpDyf,oBAAoB,CAACiF,WAAW,CAAClI,SAAS,EAAEzc,OAAO,CAAC;QACtD;MACF;MACAA,OAAO,CAAC2B,cAAc,GAAG8iB,UAAU,CAAC,CAAC,CAAC;MACtC,IAAI,CAACV,OAAO,CAAC3T,QAAQ,CAAClI,OAAO,CAAC,IAAI,CAAC8b,eAAe,CAAChlB,GAAG,CAACkJ,OAAO,CAAC,EAAE;QAC/D4b,OAAO,CAAC1e,IAAI,CAAC8C,OAAO,CAAC;QACrB8b,eAAe,CAACkB,MAAM,CACpBhd,OAAO,EACPqU,cAAc,CAACoI,WAAW,EAAE3kB,OAAO,EAAE,CAAC,CAAC,0BAA0B,CACnE,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAI8jB,OAAO,CAACre,MAAM,GAAG,CAAC,EAAE;IACtB,IAAI+e,iBAAiB,EAAE;MACrBN,UAAU,CAAC5W,WAAW,IAAI7K,WAAW;IACvC;IACA;MACEyhB,UAAU,CAAC5W,WAAW,IAAIwW,OAAO,CAACqB,IAAI,CAAC,CAAC,GAAG3iB,YAAY;IACzD;IACA,IAAI0hB,UAAU,CAACpU,SAAS,CAACrK,MAAM,EAAE;MAC/Bye,UAAU,CAACzW,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC;MAC1C,MAAMsC,KAAK,GAAG,CAACjJ,EAAE,GAAG5D,GAAG,CAAC8M,OAAO,KAAK,IAAI,GAAGlJ,EAAE,GAAGF,wBAAwB,CAAC/D,GAAG,CAAC;MAC7E,IAAIkN,KAAK,IAAI,IAAI,EAAE;QACjBmU,UAAU,CAACzW,YAAY,CAAC,OAAO,EAAEsC,KAAK,CAAC;MACzC;MACAhN,IAAI,CAACuJ,YAAY,CAAC4X,UAAU,EAAED,WAAW,GAAGA,WAAW,CAAC1X,WAAW,GAAGxJ,IAAI,CAACqc,UAAU,CAAC;IACxF;EACF;EACAiF,eAAe,GAAG,KAAK;EACvB,IAAIF,0BAA0B,CAAC1e,MAAM,EAAE;IACrC0e,0BAA0B,CAAC9c,GAAG,CAAEwI,IAAI,IAAKA,IAAI,CAACxV,iBAAiB,CAAC,CAAC,CAAC;EACpE,CAAC,MAAM;IACL;MACE6I,GAAG,CAACE,GAAG,CAAC,MAAMghB,eAAe,GAAGgB,UAAU,CAAC/J,UAAU,EAAE,EAAE,CAAC,CAAC;IAC7D;EACF;EACAwI,YAAY,CAAC,CAAC;AAChB,CAAC;AACD,IAAI7E,qBAAqB,GAAGA,CAACne,GAAG,EAAElB,OAAO,EAAE0lB,SAAS,EAAEC,qBAAqB,KAAK;EAC9E,IAAID,SAAS,EAAE;IACbA,SAAS,CAAChe,GAAG,CAAC,CAAC,CAACmH,KAAK,EAAEzP,IAAI,EAAE1C,MAAM,CAAC,KAAK;MACvC,MAAMwC,MAAM,GAAG0mB,qBAAqB,CAAC1kB,GAAG,EAAE2N,KAAK,CAAC;MAChD,MAAMT,OAAO,GAAGyX,iBAAiB,CAAC7lB,OAAO,EAAEtD,MAAM,CAAC;MAClD,MAAMsH,IAAI,GAAG8hB,gBAAgB,CAACjX,KAAK,CAAC;MACpCtL,GAAG,CAACM,GAAG,CAAC3E,MAAM,EAAEE,IAAI,EAAEgP,OAAO,EAAEpK,IAAI,CAAC;MACpC,CAAChE,OAAO,CAAC4f,aAAa,GAAG5f,OAAO,CAAC4f,aAAa,IAAI,EAAE,EAAEna,IAAI,CAAC,MAAMlC,GAAG,CAACW,GAAG,CAAChF,MAAM,EAAEE,IAAI,EAAEgP,OAAO,EAAEpK,IAAI,CAAC,CAAC;IACxG,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAI6hB,iBAAiB,GAAGA,CAAC7lB,OAAO,EAAEka,UAAU,KAAM9K,EAAE,IAAK;EACvD,IAAIjI,EAAE;EACN,IAAI;IACF;MACE,IAAInH,OAAO,CAACM,OAAO,GAAG,GAAG,CAAC,qBAAqB;QAC7C,CAAC6G,EAAE,GAAGnH,OAAO,CAACE,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiH,EAAE,CAAC+S,UAAU,CAAC,CAAC9K,EAAE,CAAC;MACrE,CAAC,MAAM;QACL,CAACpP,OAAO,CAACia,iBAAiB,GAAGja,OAAO,CAACia,iBAAiB,IAAI,EAAE,EAAExU,IAAI,CAAC,CAACyU,UAAU,EAAE9K,EAAE,CAAC,CAAC;MACtF;IACF;EACF,CAAC,CAAC,OAAO/N,CAAC,EAAE;IACVD,YAAY,CAACC,CAAC,CAAC;EACjB;AACF,CAAC;AACD,IAAIukB,qBAAqB,GAAGA,CAAC1kB,GAAG,EAAE2N,KAAK,KAAK;EAC1C,IAAIA,KAAK,GAAG,CAAC,CAAC,sBAAsB,OAAO3L,GAAG;EAC9C,IAAI2L,KAAK,GAAG,CAAC,CAAC,oBAAoB,OAAO7L,GAAG;EAC5C,IAAI6L,KAAK,GAAG,EAAE,CAAC,kBAAkB,OAAO3L,GAAG,CAACiJ,IAAI;EAChD,OAAOjL,GAAG;AACZ,CAAC;AACD,IAAI4kB,gBAAgB,GAAIjX,KAAK,IAAKnK,uBAAuB,GAAG;EAC1DqhB,OAAO,EAAE,CAAClX,KAAK,GAAG,CAAC,CAAC,mBAAmB,CAAC;EACxC6C,OAAO,EAAE,CAAC7C,KAAK,GAAG,CAAC,CAAC,mBAAmB;AACzC,CAAC,GAAG,CAACA,KAAK,GAAG,CAAC,CAAC,mBAAmB,CAAC;;AAEnC;AACA,IAAImX,QAAQ,GAAI5V,KAAK,IAAK7M,GAAG,CAAC8M,OAAO,GAAGD,KAAK;AAE7C,SAAS7Q,KAAK,IAAI0mB,CAAC,EAAE5iB,CAAC,EAAEiB,kBAAkB,IAAI4hB,CAAC,EAAEnC,aAAa,IAAIoC,CAAC,EAAEhY,OAAO,IAAI9E,CAAC,EAAEuF,WAAW,IAAIwX,CAAC,EAAEngB,QAAQ,IAAI5E,CAAC,EAAEuJ,IAAI,IAAIyb,CAAC,EAAEhY,OAAO,IAAIiY,CAAC,EAAE3d,CAAC,EAAEgG,UAAU,IAAI4X,CAAC,EAAE5K,WAAW,IAAIvE,CAAC,EAAEjR,YAAY,IAAI6D,CAAC,EAAEpF,cAAc,IAAI4hB,CAAC,EAAE1mB,gBAAgB,IAAIe,CAAC,EAAEmlB,QAAQ,IAAIS,CAAC,EAAEvgB,SAAS,IAAIwgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}