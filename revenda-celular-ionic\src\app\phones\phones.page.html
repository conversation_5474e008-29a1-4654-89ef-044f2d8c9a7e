<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Celulares</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Celulares</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-list>
    @for(phone of phonesList; track phone.id) {
    <ion-item>
      <ion-text class="phone-info">
        <h2>{{ phone.model }}</h2>
        <h3><strong>Categoria:</strong> {{ phone.category }}</h3>
        <h4><strong>Lançamento:</strong> {{ phone.releaseDate | date: 'dd/MM/yyyy' }}</h4>
        <h4><strong>Preço:</strong> {{ phone.price | currency: 'BRL' }}</h4>
        <h4>
          <strong>Marcas:</strong>
          @for(brand of phone.brands; track brand.id; let last = $last) {
            {{ brand.name }}{{ !last ? ', ' : '' }}
          }
        </h4>
        <ion-button size="small" [routerLink]="['edit', phone.id]">
          <ion-icon name="create" slot="start"></ion-icon>
          Editar
        </ion-button>
        <ion-button size="small" (click)="remove(phone)">
          <ion-icon name="trash" slot="end"></ion-icon>
          Excluir
        </ion-button>
      </ion-text>
    </ion-item>
    }
    @empty {
    <ion-item>Lista de celulares vazia, cadastre um novo celular!</ion-item>
    }
  </ion-list>
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button [routerLink]="['new']">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
