<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Celulares</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Celulares</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-list>
    @for(phone of phonesList; track phone.id) {
    <ion-item>
      <ion-avatar slot="start">
        <img [src]="phone.image" [alt]="phone.model" (error)="onImageError($event)" />
      </ion-avatar>
      <ion-label>
        <h2>{{ phone.model }}</h2>
        <h3><strong>Categoria:</strong> {{ phone.category }}</h3>
        <p><strong>Lançamento:</strong> {{ phone.releaseDate | date: 'dd/MM/yyyy' }}</p>
        <p><strong>Preço:</strong> {{ phone.price | currency: 'BRL' }}</p>
        <p><strong>Marca:</strong> {{ phone.brand?.name || 'Não informada' }}</p>
        @if(phone.stock !== undefined) {
          <p><strong>Estoque:</strong> {{ phone.stock }} unidades</p>
        }
      </ion-label>
      <ion-button slot="end" size="small" [routerLink]="['edit', phone.id]">
        <ion-icon name="create" slot="icon-only"></ion-icon>
      </ion-button>
      <ion-button slot="end" size="small" color="danger" (click)="remove(phone)">
        <ion-icon name="trash" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-item>
    }
    @empty {
    <ion-item>Lista de celulares vazia, cadastre um novo celular!</ion-item>
    }
  </ion-list>
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button [routerLink]="['new']">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
