import { Repository } from 'typeorm';
import { Store } from './store.entity';
import { CreateStoreDto } from './dto/create-store.dto';
import { UpdateStoreDto } from './dto/update-store.dto';
export declare class StoreService {
    private storeRepository;
    constructor(storeRepository: Repository<Store>);
    create(createStoreDto: CreateStoreDto): Promise<Store>;
    findAll(): Promise<Store[]>;
    findOne(id: number): Promise<Store | null>;
    findActive(): Promise<Store[]>;
    update(id: number, updateStoreDto: UpdateStoreDto): Promise<import("typeorm").UpdateResult>;
    remove(id: number): Promise<import("typeorm").DeleteResult>;
}
