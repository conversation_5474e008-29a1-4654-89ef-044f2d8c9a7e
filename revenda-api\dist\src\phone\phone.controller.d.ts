import { PhoneService } from './phone.service';
import { CreatePhoneDto } from './dto/create-phone.dto';
import { UpdatePhoneDto } from './dto/update-phone.dto';
export declare class PhoneController {
    private readonly phoneService;
    constructor(phoneService: PhoneService);
    create(createPhoneDto: CreatePhoneDto): Promise<import("./phone.entity").Phone>;
    findAll(): Promise<import("./phone.entity").Phone[]>;
    findOne(id: string): Promise<import("./phone.entity").Phone | null>;
    findByBrand(brandId: string): Promise<import("./phone.entity").Phone[]>;
    update(id: string, updatePhoneDto: UpdatePhoneDto): Promise<import("typeorm").UpdateResult>;
    remove(id: string): Promise<import("typeorm").DeleteResult>;
}
