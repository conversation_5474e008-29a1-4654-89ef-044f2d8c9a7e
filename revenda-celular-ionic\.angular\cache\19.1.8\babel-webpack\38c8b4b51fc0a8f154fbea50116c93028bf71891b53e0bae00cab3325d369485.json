{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, h, f as Host, i as getElement } from './index-28849c61.js';\nimport { f as findClosestIonContent, i as isIonContent, d as disableContentScrollY, r as resetContentScrollY, a as findIonContent, p as printIonContentErrorMsg } from './index-5cc724f3.js';\nimport { C as CoreDelegate, a as attachComponent, d as detachComponent } from './framework-delegate-63d1a679.js';\nimport { g as getElementRoot, j as clamp, r as raf, h as inheritAttributes, k as hasLazyBuild } from './helpers-da915de8.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { g as getCapacitor } from './capacitor-59395cbd.js';\nimport { G as GESTURE, O as OVERLAY_GESTURE_PRIORITY, F as FOCUS_TRAP_DISABLE_CLASS, e as createTriggerController, B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod } from './overlays-e7b9d6d9.js';\nimport { g as getClassMap } from './theme-01f3f29c.js';\nimport { e as deepReady, w as waitForMount } from './index-3ad7f18b.js';\nimport { b as getIonMode, c as config } from './ionic-global-c81d82ab.js';\nimport { KEYBOARD_DID_OPEN } from './keyboard-52278bd7.js';\nimport { c as createAnimation } from './animation-eab5a4ca.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { createGesture } from './index-39782642.js';\nimport { w as win } from './index-a5d50daf.js';\nimport './hardware-back-button-06ef3c3e.js';\nimport './gesture-controller-314a54f6.js';\nimport './keyboard-73175e24.js';\nvar Style;\n(function (Style) {\n  Style[\"Dark\"] = \"DARK\";\n  Style[\"Light\"] = \"LIGHT\";\n  Style[\"Default\"] = \"DEFAULT\";\n})(Style || (Style = {}));\nconst StatusBar = {\n  getEngine() {\n    const capacitor = getCapacitor();\n    if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('StatusBar')) {\n      return capacitor.Plugins.StatusBar;\n    }\n    return undefined;\n  },\n  setStyle(options) {\n    const engine = this.getEngine();\n    if (!engine) {\n      return;\n    }\n    engine.setStyle(options);\n  },\n  getStyle: function () {\n    var _ref = _asyncToGenerator(function* () {\n      const engine = this.getEngine();\n      if (!engine) {\n        return Style.Default;\n      }\n      const {\n        style\n      } = yield engine.getInfo();\n      return style;\n    });\n    return function getStyle() {\n      return _ref.apply(this, arguments);\n    };\n  }()\n};\n\n/**\n * Use y = mx + b to\n * figure out the backdrop value\n * at a particular x coordinate. This\n * is useful when the backdrop does\n * not begin to fade in until after\n * the 0 breakpoint.\n */\nconst getBackdropValueForSheet = (x, backdropBreakpoint) => {\n  /**\n   * We will use these points:\n   * (backdropBreakpoint, 0)\n   * (maxBreakpoint, 1)\n   * We know that at the beginning breakpoint,\n   * the backdrop will be hidden. We also\n   * know that at the maxBreakpoint, the backdrop\n   * must be fully visible. maxBreakpoint should\n   * always be 1 even if the maximum value\n   * of the breakpoints array is not 1 since\n   * the animation runs from a progress of 0\n   * to a progress of 1.\n   * m = (y2 - y1) / (x2 - x1)\n   *\n   * This is simplified from:\n   * m = (1 - 0) / (maxBreakpoint - backdropBreakpoint)\n   *\n   * If the backdropBreakpoint is 1, we return 0 as the\n   * backdrop is completely hidden.\n   *\n   */\n  if (backdropBreakpoint === 1) {\n    return 0;\n  }\n  const slope = 1 / (1 - backdropBreakpoint);\n  /**\n   * From here, compute b which is\n   * the backdrop opacity if the offset\n   * is 0. If the backdrop does not\n   * begin to fade in until after the\n   * 0 breakpoint, this b value will be\n   * negative. This is fine as we never pass\n   * b directly into the animation keyframes.\n   * b = y - mx\n   * Use a known point: (backdropBreakpoint, 0)\n   * This is simplified from:\n   * b = 0 - (backdropBreakpoint * slope)\n   */\n  const b = -(backdropBreakpoint * slope);\n  /**\n   * Finally, we can now determine the\n   * backdrop offset given an arbitrary\n   * gesture offset.\n   */\n  return x * slope + b;\n};\n/**\n * The tablet/desktop card modal activates\n * when the window width is >= 768.\n * At that point, the presenting element\n * is not transformed, so we do not need to\n * adjust the status bar color.\n *\n */\nconst setCardStatusBarDark = () => {\n  if (!win || win.innerWidth >= 768) {\n    return;\n  }\n  StatusBar.setStyle({\n    style: Style.Dark\n  });\n};\nconst setCardStatusBarDefault = (defaultStyle = Style.Default) => {\n  if (!win || win.innerWidth >= 768) {\n    return;\n  }\n  StatusBar.setStyle({\n    style: defaultStyle\n  });\n};\nconst handleCanDismiss = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (el, animation) {\n    /**\n     * If canDismiss is not a function\n     * then we can return early. If canDismiss is `true`,\n     * then canDismissBlocksGesture is `false` as canDismiss\n     * will never interrupt the gesture. As a result,\n     * this code block is never reached. If canDismiss is `false`,\n     * then we never dismiss.\n     */\n    if (typeof el.canDismiss !== 'function') {\n      return;\n    }\n    /**\n     * Run the canDismiss callback.\n     * If the function returns `true`,\n     * then we can proceed with dismiss.\n     */\n    const shouldDismiss = yield el.canDismiss(undefined, GESTURE);\n    if (!shouldDismiss) {\n      return;\n    }\n    /**\n     * If canDismiss resolved after the snap\n     * back animation finished, we can\n     * dismiss immediately.\n     *\n     * If canDismiss resolved before the snap\n     * back animation finished, we need to\n     * wait until the snap back animation is\n     * done before dismissing.\n     */\n    if (animation.isRunning()) {\n      animation.onFinish(() => {\n        el.dismiss(undefined, 'handler');\n      }, {\n        oneTimeCallback: true\n      });\n    } else {\n      el.dismiss(undefined, 'handler');\n    }\n  });\n  return function handleCanDismiss(_x, _x2) {\n    return _ref2.apply(this, arguments);\n  };\n}();\n/**\n * This function lets us simulate a realistic spring-like animation\n * when swiping down on the modal.\n * There are two forces that we need to use to compute the spring physics:\n *\n * 1. Stiffness, k: This is a measure of resistance applied a spring.\n * 2. Dampening, c: This value has the effect of reducing or preventing oscillation.\n *\n * Using these two values, we can calculate the Spring Force and the Dampening Force\n * to compute the total force applied to a spring.\n *\n * Spring Force: This force pulls a spring back into its equilibrium position.\n * Hooke's Law tells us that that spring force (FS) = kX.\n * k is the stiffness of a spring, and X is the displacement of the spring from its\n * equilibrium position. In this case, it is the amount by which the free end\n * of a spring was displaced (stretched/pushed) from its \"relaxed\" position.\n *\n * Dampening Force: This force slows down motion. Without it, a spring would oscillate forever.\n * The dampening force, FD, can be found via this formula: FD = -cv\n * where c the dampening value and v is velocity.\n *\n * Therefore, the resulting force that is exerted on the block is:\n * F = FS + FD = -kX - cv\n *\n * Newton's 2nd Law tells us that F = ma:\n * ma = -kX - cv.\n *\n * For Ionic's purposes, we can assume that m = 1:\n * a = -kX - cv\n *\n * Imagine a block attached to the end of a spring. At equilibrium\n * the block is at position x = 1.\n * Pressing on the block moves it to position x = 0;\n * So, to calculate the displacement, we need to take the\n * current position and subtract the previous position from it.\n * X = x - x0 = 0 - 1 = -1.\n *\n * For Ionic's purposes, we are only pushing on the spring modal\n * so we have a max position of 1.\n * As a result, we can expand displacement to this formula:\n * X = x - 1\n *\n * a = -k(x - 1) - cv\n *\n * We can represent the motion of something as a function of time: f(t) = x.\n * The derivative of position gives us the velocity: f'(t)\n * The derivative of the velocity gives us the acceleration: f''(t)\n *\n * We can substitute the formula above with these values:\n *\n * f\"(t) = -k * (f(t) - 1) - c * f'(t)\n *\n * This is called a differential equation.\n *\n * We know that at t = 0, we are at x = 0 because the modal does not move: f(0) = 0\n * This means our velocity is also zero: f'(0) = 0.\n *\n * We can cheat a bit and plug the formula into Wolfram Alpha.\n * However, we need to pick stiffness and dampening values:\n * k = 0.57\n * c = 15\n *\n * I picked these as they are fairly close to native iOS's spring effect\n * with the modal.\n *\n * What we plug in is this: f(0) = 0; f'(0) = 0; f''(t) = -0.57(f(t) - 1) - 15f'(t)\n *\n * The result is a formula that lets us calculate the acceleration\n * for a given time t.\n * Note: This is the approximate form of the solution. Wolfram Alpha will\n * give you a complex differential equation too.\n */\nconst calculateSpringStep = t => {\n  return 0.00255275 * 2.71828 ** (-14.9619 * t) - 1.00255 * 2.71828 ** (-0.0380968 * t) + 1;\n};\n\n// Defaults for the card swipe animation\nconst SwipeToCloseDefaults = {\n  MIN_PRESENTING_SCALE: 0.915\n};\nconst createSwipeToCloseGesture = (el, animation, statusBarStyle, onDismiss) => {\n  /**\n   * The step value at which a card modal\n   * is eligible for dismissing via gesture.\n   */\n  const DISMISS_THRESHOLD = 0.5;\n  const height = el.offsetHeight;\n  let isOpen = false;\n  let canDismissBlocksGesture = false;\n  let contentEl = null;\n  let scrollEl = null;\n  const canDismissMaxStep = 0.2;\n  let initialScrollY = true;\n  let lastStep = 0;\n  const getScrollY = () => {\n    if (contentEl && isIonContent(contentEl)) {\n      return contentEl.scrollY;\n      /**\n       * Custom scroll containers are intended to be\n       * used with virtual scrolling, so we assume\n       * there is scrolling in this case.\n       */\n    } else {\n      return true;\n    }\n  };\n  const canStart = detail => {\n    const target = detail.event.target;\n    if (target === null || !target.closest) {\n      return true;\n    }\n    /**\n     * If we are swiping on the content,\n     * swiping should only be possible if\n     * the content is scrolled all the way\n     * to the top so that we do not interfere\n     * with scrolling.\n     *\n     * We cannot assume that the `ion-content`\n     * target will remain consistent between\n     * swipes. For example, when using\n     * ion-nav within a card modal it is\n     * possible to swipe, push a view, and then\n     * swipe again. The target content will not\n     * be the same between swipes.\n     */\n    contentEl = findClosestIonContent(target);\n    if (contentEl) {\n      /**\n       * The card should never swipe to close\n       * on the content with a refresher.\n       * Note: We cannot solve this by making the\n       * swipeToClose gesture have a higher priority\n       * than the refresher gesture as the iOS native\n       * refresh gesture uses a scroll listener in\n       * addition to a gesture.\n       *\n       * Note: Do not use getScrollElement here\n       * because we need this to be a synchronous\n       * operation, and getScrollElement is\n       * asynchronous.\n       */\n      if (isIonContent(contentEl)) {\n        const root = getElementRoot(contentEl);\n        scrollEl = root.querySelector('.inner-scroll');\n      } else {\n        scrollEl = contentEl;\n      }\n      const hasRefresherInContent = !!contentEl.querySelector('ion-refresher');\n      return !hasRefresherInContent && scrollEl.scrollTop === 0;\n    }\n    /**\n     * Card should be swipeable on all\n     * parts of the modal except for the footer.\n     */\n    const footer = target.closest('ion-footer');\n    if (footer === null) {\n      return true;\n    }\n    return false;\n  };\n  const onStart = detail => {\n    const {\n      deltaY\n    } = detail;\n    /**\n     * Get the initial scrollY value so\n     * that we can correctly reset the scrollY\n     * prop when the gesture ends.\n     */\n    initialScrollY = getScrollY();\n    /**\n     * If canDismiss is anything other than `true`\n     * then users should be able to swipe down\n     * until a threshold is hit. At that point,\n     * the card modal should not proceed any further.\n     * TODO (FW-937)\n     * Remove undefined check\n     */\n    canDismissBlocksGesture = el.canDismiss !== undefined && el.canDismiss !== true;\n    /**\n     * If we are pulling down, then\n     * it is possible we are pulling on the\n     * content. We do not want scrolling to\n     * happen at the same time as the gesture.\n     */\n    if (deltaY > 0 && contentEl) {\n      disableContentScrollY(contentEl);\n    }\n    animation.progressStart(true, isOpen ? 1 : 0);\n  };\n  const onMove = detail => {\n    const {\n      deltaY\n    } = detail;\n    /**\n     * If we are pulling down, then\n     * it is possible we are pulling on the\n     * content. We do not want scrolling to\n     * happen at the same time as the gesture.\n     */\n    if (deltaY > 0 && contentEl) {\n      disableContentScrollY(contentEl);\n    }\n    /**\n     * If we are swiping on the content\n     * then the swipe gesture should only\n     * happen if we are pulling down.\n     *\n     * However, if we pull up and\n     * then down such that the scroll position\n     * returns to 0, we should be able to swipe\n     * the card.\n     */\n    const step = detail.deltaY / height;\n    /**\n     * Check if user is swiping down and\n     * if we have a canDismiss value that\n     * should block the gesture from\n     * proceeding,\n     */\n    const isAttemptingDismissWithCanDismiss = step >= 0 && canDismissBlocksGesture;\n    /**\n     * If we are blocking the gesture from dismissing,\n     * set the max step value so that the sheet cannot be\n     * completely hidden.\n     */\n    const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n    /**\n     * If we are blocking the gesture from\n     * dismissing, calculate the spring modifier value\n     * this will be added to the starting breakpoint\n     * value to give the gesture a spring-like feeling.\n     * Note that the starting breakpoint is always 0,\n     * so we omit adding 0 to the result.\n     */\n    const processedStep = isAttemptingDismissWithCanDismiss ? calculateSpringStep(step / maxStep) : step;\n    const clampedStep = clamp(0.0001, processedStep, maxStep);\n    animation.progressStep(clampedStep);\n    /**\n     * When swiping down half way, the status bar style\n     * should be reset to its default value.\n     *\n     * We track lastStep so that we do not fire these\n     * functions on every onMove, only when the user has\n     * crossed a certain threshold.\n     */\n    if (clampedStep >= DISMISS_THRESHOLD && lastStep < DISMISS_THRESHOLD) {\n      setCardStatusBarDefault(statusBarStyle);\n      /**\n       * However, if we swipe back up, then the\n       * status bar style should be set to have light\n       * text on a dark background.\n       */\n    } else if (clampedStep < DISMISS_THRESHOLD && lastStep >= DISMISS_THRESHOLD) {\n      setCardStatusBarDark();\n    }\n    lastStep = clampedStep;\n  };\n  const onEnd = detail => {\n    const velocity = detail.velocityY;\n    const step = detail.deltaY / height;\n    const isAttemptingDismissWithCanDismiss = step >= 0 && canDismissBlocksGesture;\n    const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n    const processedStep = isAttemptingDismissWithCanDismiss ? calculateSpringStep(step / maxStep) : step;\n    const clampedStep = clamp(0.0001, processedStep, maxStep);\n    const threshold = (detail.deltaY + velocity * 1000) / height;\n    /**\n     * If canDismiss blocks\n     * the swipe gesture, then the\n     * animation can never complete until\n     * canDismiss is checked.\n     */\n    const shouldComplete = !isAttemptingDismissWithCanDismiss && threshold >= DISMISS_THRESHOLD;\n    let newStepValue = shouldComplete ? -0.001 : 0.001;\n    if (!shouldComplete) {\n      animation.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n      newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], clampedStep)[0];\n    } else {\n      animation.easing('cubic-bezier(0.32, 0.72, 0, 1)');\n      newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], clampedStep)[0];\n    }\n    const duration = shouldComplete ? computeDuration(step * height, velocity) : computeDuration((1 - clampedStep) * height, velocity);\n    isOpen = shouldComplete;\n    gesture.enable(false);\n    if (contentEl) {\n      resetContentScrollY(contentEl, initialScrollY);\n    }\n    animation.onFinish(() => {\n      if (!shouldComplete) {\n        gesture.enable(true);\n      }\n    }).progressEnd(shouldComplete ? 1 : 0, newStepValue, duration);\n    /**\n     * If the canDismiss value blocked the gesture\n     * from proceeding, then we should ignore whatever\n     * shouldComplete is. Whether or not the modal\n     * animation should complete is now determined by\n     * canDismiss.\n     *\n     * If the user swiped >25% of the way\n     * to the max step, then we should\n     * check canDismiss. 25% was chosen\n     * to avoid accidental swipes.\n     */\n    if (isAttemptingDismissWithCanDismiss && clampedStep > maxStep / 4) {\n      handleCanDismiss(el, animation);\n    } else if (shouldComplete) {\n      onDismiss();\n    }\n  };\n  const gesture = createGesture({\n    el,\n    gestureName: 'modalSwipeToClose',\n    gesturePriority: OVERLAY_GESTURE_PRIORITY,\n    direction: 'y',\n    threshold: 10,\n    canStart,\n    onStart,\n    onMove,\n    onEnd\n  });\n  return gesture;\n};\nconst computeDuration = (remaining, velocity) => {\n  return clamp(400, remaining / Math.abs(velocity * 1.1), 500);\n};\nconst createSheetEnterAnimation = opts => {\n  const {\n    currentBreakpoint,\n    backdropBreakpoint\n  } = opts;\n  /**\n   * If the backdropBreakpoint is undefined, then the backdrop\n   * should always fade in. If the backdropBreakpoint came before the\n   * current breakpoint, then the backdrop should be fading in.\n   */\n  const shouldShowBackdrop = backdropBreakpoint === undefined || backdropBreakpoint < currentBreakpoint;\n  const initialBackdrop = shouldShowBackdrop ? `calc(var(--backdrop-opacity) * ${currentBreakpoint})` : '0';\n  const backdropAnimation = createAnimation('backdropAnimation').fromTo('opacity', 0, initialBackdrop);\n  if (shouldShowBackdrop) {\n    backdropAnimation.beforeStyles({\n      'pointer-events': 'none'\n    }).afterClearStyles(['pointer-events']);\n  }\n  const wrapperAnimation = createAnimation('wrapperAnimation').keyframes([{\n    offset: 0,\n    opacity: 1,\n    transform: 'translateY(100%)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: `translateY(${100 - currentBreakpoint * 100}%)`\n  }]);\n  return {\n    wrapperAnimation,\n    backdropAnimation\n  };\n};\nconst createSheetLeaveAnimation = opts => {\n  const {\n    currentBreakpoint,\n    backdropBreakpoint\n  } = opts;\n  /**\n   * Backdrop does not always fade in from 0 to 1 if backdropBreakpoint\n   * is defined, so we need to account for that offset by figuring out\n   * what the current backdrop value should be.\n   */\n  const backdropValue = `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(currentBreakpoint, backdropBreakpoint)})`;\n  const defaultBackdrop = [{\n    offset: 0,\n    opacity: backdropValue\n  }, {\n    offset: 1,\n    opacity: 0\n  }];\n  const customBackdrop = [{\n    offset: 0,\n    opacity: backdropValue\n  }, {\n    offset: backdropBreakpoint,\n    opacity: 0\n  }, {\n    offset: 1,\n    opacity: 0\n  }];\n  const backdropAnimation = createAnimation('backdropAnimation').keyframes(backdropBreakpoint !== 0 ? customBackdrop : defaultBackdrop);\n  const wrapperAnimation = createAnimation('wrapperAnimation').keyframes([{\n    offset: 0,\n    opacity: 1,\n    transform: `translateY(${100 - currentBreakpoint * 100}%)`\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: `translateY(100%)`\n  }]);\n  return {\n    wrapperAnimation,\n    backdropAnimation\n  };\n};\nconst createEnterAnimation$1 = () => {\n  const backdropAnimation = createAnimation().fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  const wrapperAnimation = createAnimation().fromTo('transform', 'translateY(100vh)', 'translateY(0vh)');\n  return {\n    backdropAnimation,\n    wrapperAnimation\n  };\n};\n/**\n * iOS Modal Enter Animation for the Card presentation style\n */\nconst iosEnterAnimation = (baseEl, opts) => {\n  const {\n    presentingEl,\n    currentBreakpoint\n  } = opts;\n  const root = getElementRoot(baseEl);\n  const {\n    wrapperAnimation,\n    backdropAnimation\n  } = currentBreakpoint !== undefined ? createSheetEnterAnimation(opts) : createEnterAnimation$1();\n  backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n  wrapperAnimation.addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow')).beforeStyles({\n    opacity: 1\n  });\n  const baseAnimation = createAnimation('entering-base').addElement(baseEl).easing('cubic-bezier(0.32,0.72,0,1)').duration(500).addAnimation(wrapperAnimation);\n  if (presentingEl) {\n    const isMobile = window.innerWidth < 768;\n    const hasCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n    const presentingElRoot = getElementRoot(presentingEl);\n    const presentingAnimation = createAnimation().beforeStyles({\n      transform: 'translateY(0)',\n      'transform-origin': 'top center',\n      overflow: 'hidden'\n    });\n    const bodyEl = document.body;\n    if (isMobile) {\n      /**\n       * Fallback for browsers that does not support `max()` (ex: Firefox)\n       * No need to worry about statusbar padding since engines like Gecko\n       * are not used as the engine for standalone Cordova/Capacitor apps\n       */\n      const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n      const modalTransform = hasCardModal ? '-10px' : transformOffset;\n      const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n      const finalTransform = `translateY(${modalTransform}) scale(${toPresentingScale})`;\n      presentingAnimation.afterStyles({\n        transform: finalTransform\n      }).beforeAddWrite(() => bodyEl.style.setProperty('background-color', 'black')).addElement(presentingEl).keyframes([{\n        offset: 0,\n        filter: 'contrast(1)',\n        transform: 'translateY(0px) scale(1)',\n        borderRadius: '0px'\n      }, {\n        offset: 1,\n        filter: 'contrast(0.85)',\n        transform: finalTransform,\n        borderRadius: '10px 10px 0 0'\n      }]);\n      baseAnimation.addAnimation(presentingAnimation);\n    } else {\n      baseAnimation.addAnimation(backdropAnimation);\n      if (!hasCardModal) {\n        wrapperAnimation.fromTo('opacity', '0', '1');\n      } else {\n        const toPresentingScale = hasCardModal ? SwipeToCloseDefaults.MIN_PRESENTING_SCALE : 1;\n        const finalTransform = `translateY(-10px) scale(${toPresentingScale})`;\n        presentingAnimation.afterStyles({\n          transform: finalTransform\n        }).addElement(presentingElRoot.querySelector('.modal-wrapper')).keyframes([{\n          offset: 0,\n          filter: 'contrast(1)',\n          transform: 'translateY(0) scale(1)'\n        }, {\n          offset: 1,\n          filter: 'contrast(0.85)',\n          transform: finalTransform\n        }]);\n        const shadowAnimation = createAnimation().afterStyles({\n          transform: finalTransform\n        }).addElement(presentingElRoot.querySelector('.modal-shadow')).keyframes([{\n          offset: 0,\n          opacity: '1',\n          transform: 'translateY(0) scale(1)'\n        }, {\n          offset: 1,\n          opacity: '0',\n          transform: finalTransform\n        }]);\n        baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n      }\n    }\n  } else {\n    baseAnimation.addAnimation(backdropAnimation);\n  }\n  return baseAnimation;\n};\nconst createLeaveAnimation$1 = () => {\n  const backdropAnimation = createAnimation().fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  const wrapperAnimation = createAnimation().fromTo('transform', 'translateY(0vh)', 'translateY(100vh)');\n  return {\n    backdropAnimation,\n    wrapperAnimation\n  };\n};\n/**\n * iOS Modal Leave Animation\n */\nconst iosLeaveAnimation = (baseEl, opts, duration = 500) => {\n  const {\n    presentingEl,\n    currentBreakpoint\n  } = opts;\n  const root = getElementRoot(baseEl);\n  const {\n    wrapperAnimation,\n    backdropAnimation\n  } = currentBreakpoint !== undefined ? createSheetLeaveAnimation(opts) : createLeaveAnimation$1();\n  backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n  wrapperAnimation.addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow')).beforeStyles({\n    opacity: 1\n  });\n  const baseAnimation = createAnimation('leaving-base').addElement(baseEl).easing('cubic-bezier(0.32,0.72,0,1)').duration(duration).addAnimation(wrapperAnimation);\n  if (presentingEl) {\n    const isMobile = window.innerWidth < 768;\n    const hasCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n    const presentingElRoot = getElementRoot(presentingEl);\n    const presentingAnimation = createAnimation().beforeClearStyles(['transform']).afterClearStyles(['transform']).onFinish(currentStep => {\n      // only reset background color if this is the last card-style modal\n      if (currentStep !== 1) {\n        return;\n      }\n      presentingEl.style.setProperty('overflow', '');\n      const numModals = Array.from(bodyEl.querySelectorAll('ion-modal:not(.overlay-hidden)')).filter(m => m.presentingElement !== undefined).length;\n      if (numModals <= 1) {\n        bodyEl.style.setProperty('background-color', '');\n      }\n    });\n    const bodyEl = document.body;\n    if (isMobile) {\n      const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n      const modalTransform = hasCardModal ? '-10px' : transformOffset;\n      const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n      const finalTransform = `translateY(${modalTransform}) scale(${toPresentingScale})`;\n      presentingAnimation.addElement(presentingEl).keyframes([{\n        offset: 0,\n        filter: 'contrast(0.85)',\n        transform: finalTransform,\n        borderRadius: '10px 10px 0 0'\n      }, {\n        offset: 1,\n        filter: 'contrast(1)',\n        transform: 'translateY(0px) scale(1)',\n        borderRadius: '0px'\n      }]);\n      baseAnimation.addAnimation(presentingAnimation);\n    } else {\n      baseAnimation.addAnimation(backdropAnimation);\n      if (!hasCardModal) {\n        wrapperAnimation.fromTo('opacity', '1', '0');\n      } else {\n        const toPresentingScale = hasCardModal ? SwipeToCloseDefaults.MIN_PRESENTING_SCALE : 1;\n        const finalTransform = `translateY(-10px) scale(${toPresentingScale})`;\n        presentingAnimation.addElement(presentingElRoot.querySelector('.modal-wrapper')).afterStyles({\n          transform: 'translate3d(0, 0, 0)'\n        }).keyframes([{\n          offset: 0,\n          filter: 'contrast(0.85)',\n          transform: finalTransform\n        }, {\n          offset: 1,\n          filter: 'contrast(1)',\n          transform: 'translateY(0) scale(1)'\n        }]);\n        const shadowAnimation = createAnimation().addElement(presentingElRoot.querySelector('.modal-shadow')).afterStyles({\n          transform: 'translateY(0) scale(1)'\n        }).keyframes([{\n          offset: 0,\n          opacity: '0',\n          transform: finalTransform\n        }, {\n          offset: 1,\n          opacity: '1',\n          transform: 'translateY(0) scale(1)'\n        }]);\n        baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n      }\n    }\n  } else {\n    baseAnimation.addAnimation(backdropAnimation);\n  }\n  return baseAnimation;\n};\nconst createEnterAnimation = () => {\n  const backdropAnimation = createAnimation().fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  const wrapperAnimation = createAnimation().keyframes([{\n    offset: 0,\n    opacity: 0.01,\n    transform: 'translateY(40px)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: `translateY(0px)`\n  }]);\n  return {\n    backdropAnimation,\n    wrapperAnimation\n  };\n};\n/**\n * Md Modal Enter Animation\n */\nconst mdEnterAnimation = (baseEl, opts) => {\n  const {\n    currentBreakpoint\n  } = opts;\n  const root = getElementRoot(baseEl);\n  const {\n    wrapperAnimation,\n    backdropAnimation\n  } = currentBreakpoint !== undefined ? createSheetEnterAnimation(opts) : createEnterAnimation();\n  backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n  wrapperAnimation.addElement(root.querySelector('.modal-wrapper'));\n  return createAnimation().addElement(baseEl).easing('cubic-bezier(0.36,0.66,0.04,1)').duration(280).addAnimation([backdropAnimation, wrapperAnimation]);\n};\nconst createLeaveAnimation = () => {\n  const backdropAnimation = createAnimation().fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  const wrapperAnimation = createAnimation().keyframes([{\n    offset: 0,\n    opacity: 0.99,\n    transform: `translateY(0px)`\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: 'translateY(40px)'\n  }]);\n  return {\n    backdropAnimation,\n    wrapperAnimation\n  };\n};\n/**\n * Md Modal Leave Animation\n */\nconst mdLeaveAnimation = (baseEl, opts) => {\n  const {\n    currentBreakpoint\n  } = opts;\n  const root = getElementRoot(baseEl);\n  const {\n    wrapperAnimation,\n    backdropAnimation\n  } = currentBreakpoint !== undefined ? createSheetLeaveAnimation(opts) : createLeaveAnimation();\n  backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n  wrapperAnimation.addElement(root.querySelector('.modal-wrapper'));\n  return createAnimation().easing('cubic-bezier(0.47,0,0.745,0.715)').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\nconst createSheetGesture = (baseEl, backdropEl, wrapperEl, initialBreakpoint, backdropBreakpoint, animation, breakpoints = [], getCurrentBreakpoint, onDismiss, onBreakpointChange) => {\n  // Defaults for the sheet swipe animation\n  const defaultBackdrop = [{\n    offset: 0,\n    opacity: 'var(--backdrop-opacity)'\n  }, {\n    offset: 1,\n    opacity: 0.01\n  }];\n  const customBackdrop = [{\n    offset: 0,\n    opacity: 'var(--backdrop-opacity)'\n  }, {\n    offset: 1 - backdropBreakpoint,\n    opacity: 0\n  }, {\n    offset: 1,\n    opacity: 0\n  }];\n  const SheetDefaults = {\n    WRAPPER_KEYFRAMES: [{\n      offset: 0,\n      transform: 'translateY(0%)'\n    }, {\n      offset: 1,\n      transform: 'translateY(100%)'\n    }],\n    BACKDROP_KEYFRAMES: backdropBreakpoint !== 0 ? customBackdrop : defaultBackdrop\n  };\n  const contentEl = baseEl.querySelector('ion-content');\n  const height = wrapperEl.clientHeight;\n  let currentBreakpoint = initialBreakpoint;\n  let offset = 0;\n  let canDismissBlocksGesture = false;\n  const canDismissMaxStep = 0.95;\n  const wrapperAnimation = animation.childAnimations.find(ani => ani.id === 'wrapperAnimation');\n  const backdropAnimation = animation.childAnimations.find(ani => ani.id === 'backdropAnimation');\n  const maxBreakpoint = breakpoints[breakpoints.length - 1];\n  const minBreakpoint = breakpoints[0];\n  const enableBackdrop = () => {\n    baseEl.style.setProperty('pointer-events', 'auto');\n    backdropEl.style.setProperty('pointer-events', 'auto');\n    /**\n     * When the backdrop is enabled, elements such\n     * as inputs should not be focusable outside\n     * the sheet.\n     */\n    baseEl.classList.remove(FOCUS_TRAP_DISABLE_CLASS);\n  };\n  const disableBackdrop = () => {\n    baseEl.style.setProperty('pointer-events', 'none');\n    backdropEl.style.setProperty('pointer-events', 'none');\n    /**\n     * When the backdrop is enabled, elements such\n     * as inputs should not be focusable outside\n     * the sheet.\n     * Adding this class disables focus trapping\n     * for the sheet temporarily.\n     */\n    baseEl.classList.add(FOCUS_TRAP_DISABLE_CLASS);\n  };\n  /**\n   * After the entering animation completes,\n   * we need to set the animation to go from\n   * offset 0 to offset 1 so that users can\n   * swipe in any direction. We then set the\n   * animation offset to the current breakpoint\n   * so there is no flickering.\n   */\n  if (wrapperAnimation && backdropAnimation) {\n    wrapperAnimation.keyframes([...SheetDefaults.WRAPPER_KEYFRAMES]);\n    backdropAnimation.keyframes([...SheetDefaults.BACKDROP_KEYFRAMES]);\n    animation.progressStart(true, 1 - currentBreakpoint);\n    /**\n     * If backdrop is not enabled, then content\n     * behind modal should be clickable. To do this, we need\n     * to remove pointer-events from ion-modal as a whole.\n     * ion-backdrop and .modal-wrapper always have pointer-events: auto\n     * applied, so the modal content can still be interacted with.\n     */\n    const shouldEnableBackdrop = currentBreakpoint > backdropBreakpoint;\n    if (shouldEnableBackdrop) {\n      enableBackdrop();\n    } else {\n      disableBackdrop();\n    }\n  }\n  if (contentEl && currentBreakpoint !== maxBreakpoint) {\n    contentEl.scrollY = false;\n  }\n  const canStart = detail => {\n    /**\n     * If we are swiping on the content, swiping should only be possible if the content\n     * is scrolled all the way to the top so that we do not interfere with scrolling.\n     *\n     * We cannot assume that the `ion-content` target will remain consistent between swipes.\n     * For example, when using ion-nav within a modal it is possible to swipe, push a view,\n     * and then swipe again. The target content will not be the same between swipes.\n     */\n    const contentEl = findClosestIonContent(detail.event.target);\n    currentBreakpoint = getCurrentBreakpoint();\n    if (currentBreakpoint === 1 && contentEl) {\n      /**\n       * The modal should never swipe to close on the content with a refresher.\n       * Note 1: We cannot solve this by making this gesture have a higher priority than\n       * the refresher gesture as the iOS native refresh gesture uses a scroll listener in\n       * addition to a gesture.\n       *\n       * Note 2: Do not use getScrollElement here because we need this to be a synchronous\n       * operation, and getScrollElement is asynchronous.\n       */\n      const scrollEl = isIonContent(contentEl) ? getElementRoot(contentEl).querySelector('.inner-scroll') : contentEl;\n      const hasRefresherInContent = !!contentEl.querySelector('ion-refresher');\n      return !hasRefresherInContent && scrollEl.scrollTop === 0;\n    }\n    return true;\n  };\n  const onStart = detail => {\n    /**\n     * If canDismiss is anything other than `true`\n     * then users should be able to swipe down\n     * until a threshold is hit. At that point,\n     * the card modal should not proceed any further.\n     *\n     * canDismiss is never fired via gesture if there is\n     * no 0 breakpoint. However, it can be fired if the user\n     * presses Esc or the hardware back button.\n     * TODO (FW-937)\n     * Remove undefined check\n     */\n    canDismissBlocksGesture = baseEl.canDismiss !== undefined && baseEl.canDismiss !== true && minBreakpoint === 0;\n    /**\n     * If we are pulling down, then it is possible we are pulling on the content.\n     * We do not want scrolling to happen at the same time as the gesture.\n     */\n    if (detail.deltaY > 0 && contentEl) {\n      contentEl.scrollY = false;\n    }\n    raf(() => {\n      /**\n       * Dismisses the open keyboard when the sheet drag gesture is started.\n       * Sets the focus onto the modal element.\n       */\n      baseEl.focus();\n    });\n    animation.progressStart(true, 1 - currentBreakpoint);\n  };\n  const onMove = detail => {\n    /**\n     * If we are pulling down, then it is possible we are pulling on the content.\n     * We do not want scrolling to happen at the same time as the gesture.\n     * This accounts for when the user scrolls down, scrolls all the way up, and then\n     * pulls down again such that the modal should start to move.\n     */\n    if (detail.deltaY > 0 && contentEl) {\n      contentEl.scrollY = false;\n    }\n    /**\n     * Given the change in gesture position on the Y axis,\n     * compute where the offset of the animation should be\n     * relative to where the user dragged.\n     */\n    const initialStep = 1 - currentBreakpoint;\n    const secondToLastBreakpoint = breakpoints.length > 1 ? 1 - breakpoints[1] : undefined;\n    const step = initialStep + detail.deltaY / height;\n    const isAttemptingDismissWithCanDismiss = secondToLastBreakpoint !== undefined && step >= secondToLastBreakpoint && canDismissBlocksGesture;\n    /**\n     * If we are blocking the gesture from dismissing,\n     * set the max step value so that the sheet cannot be\n     * completely hidden.\n     */\n    const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n    /**\n     * If we are blocking the gesture from\n     * dismissing, calculate the spring modifier value\n     * this will be added to the starting breakpoint\n     * value to give the gesture a spring-like feeling.\n     * Note that when isAttemptingDismissWithCanDismiss is true,\n     * the modifier is always added to the breakpoint that\n     * appears right after the 0 breakpoint.\n     *\n     * Note that this modifier is essentially the progression\n     * between secondToLastBreakpoint and maxStep which is\n     * why we subtract secondToLastBreakpoint. This lets us get\n     * the result as a value from 0 to 1.\n     */\n    const processedStep = isAttemptingDismissWithCanDismiss && secondToLastBreakpoint !== undefined ? secondToLastBreakpoint + calculateSpringStep((step - secondToLastBreakpoint) / (maxStep - secondToLastBreakpoint)) : step;\n    offset = clamp(0.0001, processedStep, maxStep);\n    animation.progressStep(offset);\n  };\n  const onEnd = detail => {\n    /**\n     * When the gesture releases, we need to determine\n     * the closest breakpoint to snap to.\n     */\n    const velocity = detail.velocityY;\n    const threshold = (detail.deltaY + velocity * 350) / height;\n    const diff = currentBreakpoint - threshold;\n    const closest = breakpoints.reduce((a, b) => {\n      return Math.abs(b - diff) < Math.abs(a - diff) ? b : a;\n    });\n    moveSheetToBreakpoint({\n      breakpoint: closest,\n      breakpointOffset: offset,\n      canDismiss: canDismissBlocksGesture,\n      /**\n       * The swipe is user-driven, so we should\n       * always animate when the gesture ends.\n       */\n      animated: true\n    });\n  };\n  const moveSheetToBreakpoint = options => {\n    const {\n      breakpoint,\n      canDismiss,\n      breakpointOffset,\n      animated\n    } = options;\n    /**\n     * canDismiss should only prevent snapping\n     * when users are trying to dismiss. If canDismiss\n     * is present but the user is trying to swipe upwards,\n     * we should allow that to happen,\n     */\n    const shouldPreventDismiss = canDismiss && breakpoint === 0;\n    const snapToBreakpoint = shouldPreventDismiss ? currentBreakpoint : breakpoint;\n    const shouldRemainOpen = snapToBreakpoint !== 0;\n    currentBreakpoint = 0;\n    /**\n     * Update the animation so that it plays from\n     * the last offset to the closest snap point.\n     */\n    if (wrapperAnimation && backdropAnimation) {\n      wrapperAnimation.keyframes([{\n        offset: 0,\n        transform: `translateY(${breakpointOffset * 100}%)`\n      }, {\n        offset: 1,\n        transform: `translateY(${(1 - snapToBreakpoint) * 100}%)`\n      }]);\n      backdropAnimation.keyframes([{\n        offset: 0,\n        opacity: `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(1 - breakpointOffset, backdropBreakpoint)})`\n      }, {\n        offset: 1,\n        opacity: `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(snapToBreakpoint, backdropBreakpoint)})`\n      }]);\n      animation.progressStep(0);\n    }\n    /**\n     * Gesture should remain disabled until the\n     * snapping animation completes.\n     */\n    gesture.enable(false);\n    if (shouldPreventDismiss) {\n      handleCanDismiss(baseEl, animation);\n    } else if (!shouldRemainOpen) {\n      onDismiss();\n    }\n    /**\n     * If the sheet is going to be fully expanded then we should enable\n     * scrolling immediately. The sheet modal animation takes ~500ms to finish\n     * so if we wait until then there is a visible delay for when scrolling is\n     * re-enabled. Native iOS allows for scrolling on the sheet modal as soon\n     * as the gesture is released, so we align with that.\n     */\n    if (contentEl && snapToBreakpoint === breakpoints[breakpoints.length - 1]) {\n      contentEl.scrollY = true;\n    }\n    return new Promise(resolve => {\n      animation.onFinish(() => {\n        if (shouldRemainOpen) {\n          /**\n           * Once the snapping animation completes,\n           * we need to reset the animation to go\n           * from 0 to 1 so users can swipe in any direction.\n           * We then set the animation offset to the current\n           * breakpoint so that it starts at the snapped position.\n           */\n          if (wrapperAnimation && backdropAnimation) {\n            raf(() => {\n              wrapperAnimation.keyframes([...SheetDefaults.WRAPPER_KEYFRAMES]);\n              backdropAnimation.keyframes([...SheetDefaults.BACKDROP_KEYFRAMES]);\n              animation.progressStart(true, 1 - snapToBreakpoint);\n              currentBreakpoint = snapToBreakpoint;\n              onBreakpointChange(currentBreakpoint);\n              /**\n               * Backdrop should become enabled\n               * after the backdropBreakpoint value\n               */\n              const shouldEnableBackdrop = currentBreakpoint > backdropBreakpoint;\n              if (shouldEnableBackdrop) {\n                enableBackdrop();\n              } else {\n                disableBackdrop();\n              }\n              gesture.enable(true);\n              resolve();\n            });\n          } else {\n            gesture.enable(true);\n            resolve();\n          }\n        } else {\n          resolve();\n        }\n        /**\n         * This must be a one time callback\n         * otherwise a new callback will\n         * be added every time onEnd runs.\n         */\n      }, {\n        oneTimeCallback: true\n      }).progressEnd(1, 0, animated ? 500 : 0);\n    });\n  };\n  const gesture = createGesture({\n    el: wrapperEl,\n    gestureName: 'modalSheet',\n    gesturePriority: 40,\n    direction: 'y',\n    threshold: 10,\n    canStart,\n    onStart,\n    onMove,\n    onEnd\n  });\n  return {\n    gesture,\n    moveSheetToBreakpoint\n  };\n};\nconst modalIosCss = \":host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:\\\"\\\"}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}\";\nconst IonModalIosStyle0 = modalIosCss;\nconst modalMdCss = \":host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:\\\"\\\"}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}\";\nconst IonModalMdStyle0 = modalMdCss;\nconst Modal = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.didPresent = createEvent(this, \"ionModalDidPresent\", 7);\n    this.willPresent = createEvent(this, \"ionModalWillPresent\", 7);\n    this.willDismiss = createEvent(this, \"ionModalWillDismiss\", 7);\n    this.didDismiss = createEvent(this, \"ionModalDidDismiss\", 7);\n    this.ionBreakpointDidChange = createEvent(this, \"ionBreakpointDidChange\", 7);\n    this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n    this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n    this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n    this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n    this.ionMount = createEvent(this, \"ionMount\", 7);\n    this.lockController = createLockController();\n    this.triggerController = createTriggerController();\n    this.coreDelegate = CoreDelegate();\n    this.isSheetModal = false;\n    this.inheritedAttributes = {};\n    this.inline = false;\n    // Whether or not modal is being dismissed via gesture\n    this.gestureAnimationDismissing = false;\n    this.onHandleClick = () => {\n      const {\n        sheetTransition,\n        handleBehavior\n      } = this;\n      if (handleBehavior !== 'cycle' || sheetTransition !== undefined) {\n        /**\n         * The sheet modal should not advance to the next breakpoint\n         * if the handle behavior is not `cycle` or if the handle\n         * is clicked while the sheet is moving to a breakpoint.\n         */\n        return;\n      }\n      this.moveToNextBreakpoint();\n    };\n    this.onBackdropTap = () => {\n      const {\n        sheetTransition\n      } = this;\n      if (sheetTransition !== undefined) {\n        /**\n         * When the handle is double clicked at the largest breakpoint,\n         * it will start to move to the first breakpoint. While transitioning,\n         * the backdrop will often receive the second click. We prevent the\n         * backdrop from dismissing the modal while moving between breakpoints.\n         */\n        return;\n      }\n      this.dismiss(undefined, BACKDROP);\n    };\n    this.onLifecycle = modalEvent => {\n      const el = this.usersElement;\n      const name = LIFECYCLE_MAP[modalEvent.type];\n      if (el && name) {\n        const ev = new CustomEvent(name, {\n          bubbles: false,\n          cancelable: false,\n          detail: modalEvent.detail\n        });\n        el.dispatchEvent(ev);\n      }\n    };\n    this.presented = false;\n    this.hasController = false;\n    this.overlayIndex = undefined;\n    this.delegate = undefined;\n    this.keyboardClose = true;\n    this.enterAnimation = undefined;\n    this.leaveAnimation = undefined;\n    this.breakpoints = undefined;\n    this.initialBreakpoint = undefined;\n    this.backdropBreakpoint = 0;\n    this.handle = undefined;\n    this.handleBehavior = 'none';\n    this.component = undefined;\n    this.componentProps = undefined;\n    this.cssClass = undefined;\n    this.backdropDismiss = true;\n    this.showBackdrop = true;\n    this.animated = true;\n    this.presentingElement = undefined;\n    this.htmlAttributes = undefined;\n    this.isOpen = false;\n    this.trigger = undefined;\n    this.keepContentsMounted = false;\n    this.focusTrap = true;\n    this.canDismiss = true;\n  }\n  onIsOpenChange(newValue, oldValue) {\n    if (newValue === true && oldValue === false) {\n      this.present();\n    } else if (newValue === false && oldValue === true) {\n      this.dismiss();\n    }\n  }\n  triggerChanged() {\n    const {\n      trigger,\n      el,\n      triggerController\n    } = this;\n    if (trigger) {\n      triggerController.addClickListener(el, trigger);\n    }\n  }\n  breakpointsChanged(breakpoints) {\n    if (breakpoints !== undefined) {\n      this.sortedBreakpoints = breakpoints.sort((a, b) => a - b);\n    }\n  }\n  connectedCallback() {\n    const {\n      el\n    } = this;\n    prepareOverlay(el);\n    this.triggerChanged();\n  }\n  disconnectedCallback() {\n    this.triggerController.removeClickListener();\n  }\n  componentWillLoad() {\n    var _a;\n    const {\n      breakpoints,\n      initialBreakpoint,\n      el,\n      htmlAttributes\n    } = this;\n    const isSheetModal = this.isSheetModal = breakpoints !== undefined && initialBreakpoint !== undefined;\n    const attributesToInherit = ['aria-label', 'role'];\n    this.inheritedAttributes = inheritAttributes(el, attributesToInherit);\n    /**\n     * When using a controller modal you can set attributes\n     * using the htmlAttributes property. Since the above attributes\n     * need to be inherited inside of the modal, we need to look\n     * and see if these attributes are being set via htmlAttributes.\n     *\n     * We could alternatively move this to componentDidLoad to simplify the work\n     * here, but we'd then need to make inheritedAttributes a State variable,\n     * thus causing another render to always happen after the first render.\n     */\n    if (htmlAttributes !== undefined) {\n      attributesToInherit.forEach(attribute => {\n        const attributeValue = htmlAttributes[attribute];\n        if (attributeValue) {\n          /**\n           * If an attribute we need to inherit was\n           * set using htmlAttributes then add it to\n           * inheritedAttributes and remove it from htmlAttributes.\n           * This ensures the attribute is inherited and not\n           * set on the host.\n           *\n           * In this case, if an inherited attribute is set\n           * on the host element and using htmlAttributes then\n           * htmlAttributes wins, but that's not a pattern that we recommend.\n           * The only time you'd need htmlAttributes is when using modalController.\n           */\n          this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), {\n            [attribute]: htmlAttributes[attribute]\n          });\n          delete htmlAttributes[attribute];\n        }\n      });\n    }\n    if (isSheetModal) {\n      this.currentBreakpoint = this.initialBreakpoint;\n    }\n    if (breakpoints !== undefined && initialBreakpoint !== undefined && !breakpoints.includes(initialBreakpoint)) {\n      printIonWarning('Your breakpoints array must include the initialBreakpoint value.');\n    }\n    if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n      setOverlayId(this.el);\n    }\n  }\n  componentDidLoad() {\n    /**\n     * If modal was rendered with isOpen=\"true\"\n     * then we should open modal immediately.\n     */\n    if (this.isOpen === true) {\n      raf(() => this.present());\n    }\n    this.breakpointsChanged(this.breakpoints);\n    /**\n     * When binding values in frameworks such as Angular\n     * it is possible for the value to be set after the Web Component\n     * initializes but before the value watcher is set up in Stencil.\n     * As a result, the watcher callback may not be fired.\n     * We work around this by manually calling the watcher\n     * callback when the component has loaded and the watcher\n     * is configured.\n     */\n    this.triggerChanged();\n  }\n  /**\n   * Determines whether or not an overlay\n   * is being used inline or via a controller/JS\n   * and returns the correct delegate.\n   * By default, subsequent calls to getDelegate\n   * will use a cached version of the delegate.\n   * This is useful for calling dismiss after\n   * present so that the correct delegate is given.\n   */\n  getDelegate(force = false) {\n    if (this.workingDelegate && !force) {\n      return {\n        delegate: this.workingDelegate,\n        inline: this.inline\n      };\n    }\n    /**\n     * If using overlay inline\n     * we potentially need to use the coreDelegate\n     * so that this works in vanilla JS apps.\n     * If a developer has presented this component\n     * via a controller, then we can assume\n     * the component is already in the\n     * correct place.\n     */\n    const parentEl = this.el.parentNode;\n    const inline = this.inline = parentEl !== null && !this.hasController;\n    const delegate = this.workingDelegate = inline ? this.delegate || this.coreDelegate : this.delegate;\n    return {\n      inline,\n      delegate\n    };\n  }\n  /**\n   * Determines whether or not the\n   * modal is allowed to dismiss based\n   * on the state of the canDismiss prop.\n   */\n  checkCanDismiss(data, role) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const {\n        canDismiss\n      } = _this;\n      if (typeof canDismiss === 'function') {\n        return canDismiss(data, role);\n      }\n      return canDismiss;\n    })();\n  }\n  /**\n   * Present the modal overlay after it has been created.\n   */\n  present() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const unlock = yield _this2.lockController.lock();\n      if (_this2.presented) {\n        unlock();\n        return;\n      }\n      const {\n        presentingElement,\n        el\n      } = _this2;\n      /**\n       * If the modal is presented multiple times (inline modals), we\n       * need to reset the current breakpoint to the initial breakpoint.\n       */\n      _this2.currentBreakpoint = _this2.initialBreakpoint;\n      const {\n        inline,\n        delegate\n      } = _this2.getDelegate(true);\n      /**\n       * Emit ionMount so JS Frameworks have an opportunity\n       * to add the child component to the DOM. The child\n       * component will be assigned to this.usersElement below.\n       */\n      _this2.ionMount.emit();\n      _this2.usersElement = yield attachComponent(delegate, el, _this2.component, ['ion-page'], _this2.componentProps, inline);\n      /**\n       * When using the lazy loaded build of Stencil, we need to wait\n       * for every Stencil component instance to be ready before presenting\n       * otherwise there can be a flash of unstyled content. With the\n       * custom elements bundle we need to wait for the JS framework\n       * mount the inner contents of the overlay otherwise WebKit may\n       * get the transition incorrect.\n       */\n      if (hasLazyBuild(el)) {\n        yield deepReady(_this2.usersElement);\n        /**\n         * If keepContentsMounted=\"true\" then the\n         * JS Framework has already mounted the inner\n         * contents so there is no need to wait.\n         * Otherwise, we need to wait for the JS\n         * Framework to mount the inner contents\n         * of this component.\n         */\n      } else if (!_this2.keepContentsMounted) {\n        yield waitForMount();\n      }\n      writeTask(() => _this2.el.classList.add('show-modal'));\n      const hasCardModal = presentingElement !== undefined;\n      /**\n       * We need to change the status bar at the\n       * start of the animation so that it completes\n       * by the time the card animation is done.\n       */\n      if (hasCardModal && getIonMode(_this2) === 'ios') {\n        // Cache the original status bar color before the modal is presented\n        _this2.statusBarStyle = yield StatusBar.getStyle();\n        setCardStatusBarDark();\n      }\n      yield present(_this2, 'modalEnter', iosEnterAnimation, mdEnterAnimation, {\n        presentingEl: presentingElement,\n        currentBreakpoint: _this2.initialBreakpoint,\n        backdropBreakpoint: _this2.backdropBreakpoint\n      });\n      /* tslint:disable-next-line */\n      if (typeof window !== 'undefined') {\n        /**\n         * This needs to be setup before any\n         * non-transition async work so it can be dereferenced\n         * in the dismiss method. The dismiss method\n         * only waits for the entering transition\n         * to finish. It does not wait for all of the `present`\n         * method to resolve.\n         */\n        _this2.keyboardOpenCallback = () => {\n          if (_this2.gesture) {\n            /**\n             * When the native keyboard is opened and the webview\n             * is resized, the gesture implementation will become unresponsive\n             * and enter a free-scroll mode.\n             *\n             * When the keyboard is opened, we disable the gesture for\n             * a single frame and re-enable once the contents have repositioned\n             * from the keyboard placement.\n             */\n            _this2.gesture.enable(false);\n            raf(() => {\n              if (_this2.gesture) {\n                _this2.gesture.enable(true);\n              }\n            });\n          }\n        };\n        window.addEventListener(KEYBOARD_DID_OPEN, _this2.keyboardOpenCallback);\n      }\n      if (_this2.isSheetModal) {\n        _this2.initSheetGesture();\n      } else if (hasCardModal) {\n        _this2.initSwipeToClose();\n      }\n      unlock();\n    })();\n  }\n  initSwipeToClose() {\n    var _this3 = this;\n    var _a;\n    if (getIonMode(this) !== 'ios') {\n      return;\n    }\n    const {\n      el\n    } = this;\n    // All of the elements needed for the swipe gesture\n    // should be in the DOM and referenced by now, except\n    // for the presenting el\n    const animationBuilder = this.leaveAnimation || config.get('modalLeave', iosLeaveAnimation);\n    const ani = this.animation = animationBuilder(el, {\n      presentingEl: this.presentingElement\n    });\n    const contentEl = findIonContent(el);\n    if (!contentEl) {\n      printIonContentErrorMsg(el);\n      return;\n    }\n    const statusBarStyle = (_a = this.statusBarStyle) !== null && _a !== void 0 ? _a : Style.Default;\n    this.gesture = createSwipeToCloseGesture(el, ani, statusBarStyle, () => {\n      /**\n       * While the gesture animation is finishing\n       * it is possible for a user to tap the backdrop.\n       * This would result in the dismiss animation\n       * being played again. Typically this is avoided\n       * by setting `presented = false` on the overlay\n       * component; however, we cannot do that here as\n       * that would prevent the element from being\n       * removed from the DOM.\n       */\n      this.gestureAnimationDismissing = true;\n      /**\n       * Reset the status bar style as the dismiss animation\n       * starts otherwise the status bar will be the wrong\n       * color for the duration of the dismiss animation.\n       * The dismiss method does this as well, but\n       * in this case it's only called once the animation\n       * has finished.\n       */\n      setCardStatusBarDefault(this.statusBarStyle);\n      this.animation.onFinish(/*#__PURE__*/_asyncToGenerator(function* () {\n        yield _this3.dismiss(undefined, GESTURE);\n        _this3.gestureAnimationDismissing = false;\n      }));\n    });\n    this.gesture.enable(true);\n  }\n  initSheetGesture() {\n    const {\n      wrapperEl,\n      initialBreakpoint,\n      backdropBreakpoint\n    } = this;\n    if (!wrapperEl || initialBreakpoint === undefined) {\n      return;\n    }\n    const animationBuilder = this.enterAnimation || config.get('modalEnter', iosEnterAnimation);\n    const ani = this.animation = animationBuilder(this.el, {\n      presentingEl: this.presentingElement,\n      currentBreakpoint: initialBreakpoint,\n      backdropBreakpoint\n    });\n    ani.progressStart(true, 1);\n    const {\n      gesture,\n      moveSheetToBreakpoint\n    } = createSheetGesture(this.el, this.backdropEl, wrapperEl, initialBreakpoint, backdropBreakpoint, ani, this.sortedBreakpoints, () => {\n      var _a;\n      return (_a = this.currentBreakpoint) !== null && _a !== void 0 ? _a : 0;\n    }, () => this.sheetOnDismiss(), breakpoint => {\n      if (this.currentBreakpoint !== breakpoint) {\n        this.currentBreakpoint = breakpoint;\n        this.ionBreakpointDidChange.emit({\n          breakpoint\n        });\n      }\n    });\n    this.gesture = gesture;\n    this.moveSheetToBreakpoint = moveSheetToBreakpoint;\n    this.gesture.enable(true);\n  }\n  sheetOnDismiss() {\n    var _this4 = this;\n    /**\n     * While the gesture animation is finishing\n     * it is possible for a user to tap the backdrop.\n     * This would result in the dismiss animation\n     * being played again. Typically this is avoided\n     * by setting `presented = false` on the overlay\n     * component; however, we cannot do that here as\n     * that would prevent the element from being\n     * removed from the DOM.\n     */\n    this.gestureAnimationDismissing = true;\n    this.animation.onFinish(/*#__PURE__*/_asyncToGenerator(function* () {\n      _this4.currentBreakpoint = 0;\n      _this4.ionBreakpointDidChange.emit({\n        breakpoint: _this4.currentBreakpoint\n      });\n      yield _this4.dismiss(undefined, GESTURE);\n      _this4.gestureAnimationDismissing = false;\n    }));\n  }\n  /**\n   * Dismiss the modal overlay after it has been presented.\n   *\n   * @param data Any data to emit in the dismiss events.\n   * @param role The role of the element that is dismissing the modal. For example, 'cancel' or 'backdrop'.\n   *\n   * This is a no-op if the overlay has not been presented yet. If you want\n   * to remove an overlay from the DOM that was never presented, use the\n   * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n   */\n  dismiss(data, role) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      if (_this5.gestureAnimationDismissing && role !== GESTURE) {\n        return false;\n      }\n      /**\n       * Because the canDismiss check below is async,\n       * we need to claim a lock before the check happens,\n       * in case the dismiss transition does run.\n       */\n      const unlock = yield _this5.lockController.lock();\n      /**\n       * If a canDismiss handler is responsible\n       * for calling the dismiss method, we should\n       * not run the canDismiss check again.\n       */\n      if (role !== 'handler' && !(yield _this5.checkCanDismiss(data, role))) {\n        unlock();\n        return false;\n      }\n      const {\n        presentingElement\n      } = _this5;\n      /**\n       * We need to start the status bar change\n       * before the animation so that the change\n       * finishes when the dismiss animation does.\n       */\n      const hasCardModal = presentingElement !== undefined;\n      if (hasCardModal && getIonMode(_this5) === 'ios') {\n        setCardStatusBarDefault(_this5.statusBarStyle);\n      }\n      /* tslint:disable-next-line */\n      if (typeof window !== 'undefined' && _this5.keyboardOpenCallback) {\n        window.removeEventListener(KEYBOARD_DID_OPEN, _this5.keyboardOpenCallback);\n        _this5.keyboardOpenCallback = undefined;\n      }\n      const dismissed = yield dismiss(_this5, data, role, 'modalLeave', iosLeaveAnimation, mdLeaveAnimation, {\n        presentingEl: presentingElement,\n        currentBreakpoint: (_a = _this5.currentBreakpoint) !== null && _a !== void 0 ? _a : _this5.initialBreakpoint,\n        backdropBreakpoint: _this5.backdropBreakpoint\n      });\n      if (dismissed) {\n        const {\n          delegate\n        } = _this5.getDelegate();\n        yield detachComponent(delegate, _this5.usersElement);\n        writeTask(() => _this5.el.classList.remove('show-modal'));\n        if (_this5.animation) {\n          _this5.animation.destroy();\n        }\n        if (_this5.gesture) {\n          _this5.gesture.destroy();\n        }\n      }\n      _this5.currentBreakpoint = undefined;\n      _this5.animation = undefined;\n      unlock();\n      return dismissed;\n    })();\n  }\n  /**\n   * Returns a promise that resolves when the modal did dismiss.\n   */\n  onDidDismiss() {\n    return eventMethod(this.el, 'ionModalDidDismiss');\n  }\n  /**\n   * Returns a promise that resolves when the modal will dismiss.\n   */\n  onWillDismiss() {\n    return eventMethod(this.el, 'ionModalWillDismiss');\n  }\n  /**\n   * Move a sheet style modal to a specific breakpoint. The breakpoint value must\n   * be a value defined in your `breakpoints` array.\n   */\n  setCurrentBreakpoint(breakpoint) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this6.isSheetModal) {\n        printIonWarning('setCurrentBreakpoint is only supported on sheet modals.');\n        return;\n      }\n      if (!_this6.breakpoints.includes(breakpoint)) {\n        printIonWarning(`Attempted to set invalid breakpoint value ${breakpoint}. Please double check that the breakpoint value is part of your defined breakpoints.`);\n        return;\n      }\n      const {\n        currentBreakpoint,\n        moveSheetToBreakpoint,\n        canDismiss,\n        breakpoints,\n        animated\n      } = _this6;\n      if (currentBreakpoint === breakpoint) {\n        return;\n      }\n      if (moveSheetToBreakpoint) {\n        _this6.sheetTransition = moveSheetToBreakpoint({\n          breakpoint,\n          breakpointOffset: 1 - currentBreakpoint,\n          canDismiss: canDismiss !== undefined && canDismiss !== true && breakpoints[0] === 0,\n          animated\n        });\n        yield _this6.sheetTransition;\n        _this6.sheetTransition = undefined;\n      }\n    })();\n  }\n  /**\n   * Returns the current breakpoint of a sheet style modal\n   */\n  getCurrentBreakpoint() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      return _this7.currentBreakpoint;\n    })();\n  }\n  moveToNextBreakpoint() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        breakpoints,\n        currentBreakpoint\n      } = _this8;\n      if (!breakpoints || currentBreakpoint == null) {\n        /**\n         * If the modal does not have breakpoints and/or the current\n         * breakpoint is not set, we can't move to the next breakpoint.\n         */\n        return false;\n      }\n      const allowedBreakpoints = breakpoints.filter(b => b !== 0);\n      const currentBreakpointIndex = allowedBreakpoints.indexOf(currentBreakpoint);\n      const nextBreakpointIndex = (currentBreakpointIndex + 1) % allowedBreakpoints.length;\n      const nextBreakpoint = allowedBreakpoints[nextBreakpointIndex];\n      /**\n       * Sets the current breakpoint to the next available breakpoint.\n       * If the current breakpoint is the last breakpoint, we set the current\n       * breakpoint to the first non-zero breakpoint to avoid dismissing the sheet.\n       */\n      yield _this8.setCurrentBreakpoint(nextBreakpoint);\n      return true;\n    })();\n  }\n  render() {\n    const {\n      handle,\n      isSheetModal,\n      presentingElement,\n      htmlAttributes,\n      handleBehavior,\n      inheritedAttributes,\n      focusTrap\n    } = this;\n    const showHandle = handle !== false && isSheetModal;\n    const mode = getIonMode(this);\n    const isCardModal = presentingElement !== undefined && mode === 'ios';\n    const isHandleCycle = handleBehavior === 'cycle';\n    return h(Host, Object.assign({\n      key: 'b4da5111fe4719fa450c39b2d4bd884a302a7924',\n      \"no-router\": true,\n      tabindex: \"-1\"\n    }, htmlAttributes, {\n      style: {\n        zIndex: `${20000 + this.overlayIndex}`\n      },\n      class: Object.assign({\n        [mode]: true,\n        ['modal-default']: !isCardModal && !isSheetModal,\n        [`modal-card`]: isCardModal,\n        [`modal-sheet`]: isSheetModal,\n        'overlay-hidden': true,\n        [FOCUS_TRAP_DISABLE_CLASS]: focusTrap === false\n      }, getClassMap(this.cssClass)),\n      onIonBackdropTap: this.onBackdropTap,\n      onIonModalDidPresent: this.onLifecycle,\n      onIonModalWillPresent: this.onLifecycle,\n      onIonModalWillDismiss: this.onLifecycle,\n      onIonModalDidDismiss: this.onLifecycle\n    }), h(\"ion-backdrop\", {\n      key: 'c12dbf747e0eb914eaf1331798548ffc7e147763',\n      ref: el => this.backdropEl = el,\n      visible: this.showBackdrop,\n      tappable: this.backdropDismiss,\n      part: \"backdrop\"\n    }), mode === 'ios' && h(\"div\", {\n      key: 'da546ee80c6576b5acc66e959fd5009e0b9a8160',\n      class: \"modal-shadow\"\n    }), h(\"div\", Object.assign({\n      key: '306ebe6427440ad5f7ed36d590e562d15a503b75',\n      /*\n        role and aria-modal must be used on the\n        same element. They must also be set inside the\n        shadow DOM otherwise ion-button will not be highlighted\n        when using VoiceOver: https://bugs.webkit.org/show_bug.cgi?id=247134\n      */\n      role: \"dialog\"\n    }, inheritedAttributes, {\n      \"aria-modal\": \"true\",\n      class: \"modal-wrapper ion-overlay-wrapper\",\n      part: \"content\",\n      ref: el => this.wrapperEl = el\n    }), showHandle && h(\"button\", {\n      key: 'c5d17e346fe255a7c0cacbbf15c0083f2d09c488',\n      class: \"modal-handle\",\n      // Prevents the handle from receiving keyboard focus when it does not cycle\n      tabIndex: !isHandleCycle ? -1 : 0,\n      \"aria-label\": \"Activate to adjust the size of the dialog overlaying the screen\",\n      onClick: isHandleCycle ? this.onHandleClick : undefined,\n      part: \"handle\"\n    }), h(\"slot\", {\n      key: '5cc714170a00b67f3eda0cd1d6f37c1489a99c83'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"isOpen\": [\"onIsOpenChange\"],\n      \"trigger\": [\"triggerChanged\"]\n    };\n  }\n};\nconst LIFECYCLE_MAP = {\n  ionModalDidPresent: 'ionViewDidEnter',\n  ionModalWillPresent: 'ionViewWillEnter',\n  ionModalWillDismiss: 'ionViewWillLeave',\n  ionModalDidDismiss: 'ionViewDidLeave'\n};\nModal.style = {\n  ios: IonModalIosStyle0,\n  md: IonModalMdStyle0\n};\nexport { Modal as ion_modal };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "w", "writeTask", "h", "f", "Host", "i", "getElement", "findClosestIonContent", "isIonContent", "disableContentScrollY", "resetContentScrollY", "a", "find<PERSON><PERSON><PERSON><PERSON>nt", "p", "printIonContentErrorMsg", "C", "CoreDelegate", "attachComponent", "detachComponent", "g", "getElementRoot", "j", "clamp", "raf", "inheritAttributes", "k", "hasLazyBuild", "c", "createLockController", "printIonWarning", "getCapacitor", "G", "GESTURE", "O", "OVERLAY_GESTURE_PRIORITY", "F", "FOCUS_TRAP_DISABLE_CLASS", "e", "createTriggerController", "B", "BACKDROP", "prepareOverlay", "setOverlayId", "present", "dismiss", "eventMethod", "getClassMap", "deepReady", "waitForMount", "b", "getIonMode", "config", "KEYBOARD_DID_OPEN", "createAnimation", "getTimeGivenProgression", "createGesture", "win", "Style", "StatusBar", "getEngine", "capacitor", "isPluginAvailable", "Plugins", "undefined", "setStyle", "options", "engine", "getStyle", "_ref", "_asyncToGenerator", "<PERSON><PERSON><PERSON>", "style", "getInfo", "apply", "arguments", "getBackdropValueForSheet", "x", "backdropBreakpoint", "slope", "setCardStatusBarDark", "innerWidth", "Dark", "setCardStatusBarDefault", "defaultStyle", "handleCanDismiss", "_ref2", "el", "animation", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isRunning", "onFinish", "oneTimeCallback", "_x", "_x2", "calculateSpringStep", "t", "SwipeToCloseDefaults", "MIN_PRESENTING_SCALE", "createSwipeToCloseGesture", "statusBarStyle", "on<PERSON><PERSON><PERSON>", "DISMISS_THRESHOLD", "height", "offsetHeight", "isOpen", "canDismissBlocksGesture", "contentEl", "scrollEl", "canDismissMaxStep", "initialScrollY", "lastStep", "getScrollY", "scrollY", "canStart", "detail", "target", "event", "closest", "root", "querySelector", "hasRefresherInContent", "scrollTop", "footer", "onStart", "deltaY", "progressStart", "onMove", "step", "isAttemptingDismissWithCanDismiss", "maxStep", "processedStep", "clampedStep", "progressStep", "onEnd", "velocity", "velocityY", "threshold", "shouldComplete", "newStepValue", "easing", "duration", "computeDuration", "gesture", "enable", "progressEnd", "<PERSON><PERSON><PERSON>", "gesturePriority", "direction", "remaining", "Math", "abs", "createSheetEnterAnimation", "opts", "currentBreakpoint", "shouldShowBackdrop", "initialBackdrop", "backdropAnimation", "fromTo", "beforeStyles", "afterClearStyles", "wrapperAnimation", "keyframes", "offset", "opacity", "transform", "createSheetLeaveAnimation", "backdropValue", "defaultBackdrop", "customBackdrop", "createEnterAnimation$1", "iosEnterAnimation", "baseEl", "presentingEl", "addElement", "querySelectorAll", "baseAnimation", "addAnimation", "isMobile", "window", "hasCardModal", "tagName", "presentingElement", "presentingElRoot", "presentingAnimation", "overflow", "bodyEl", "document", "body", "transformOffset", "CSS", "supports", "modalTransform", "toPresentingScale", "finalTransform", "afterStyles", "beforeAddWrite", "setProperty", "filter", "borderRadius", "shadowAnimation", "createLeaveAnimation$1", "iosLeaveAnimation", "beforeClearStyles", "currentStep", "numModals", "Array", "from", "m", "length", "createEnterAnimation", "mdEnterAnimation", "createLeaveAnimation", "mdLeaveAnimation", "createSheetGesture", "backdropEl", "wrapperEl", "initialBreakpoint", "breakpoints", "getCurrentBreakpoint", "onBreakpointChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WRAPPER_KEYFRAMES", "BACKDROP_KEYFRAMES", "clientHeight", "childAnimations", "find", "ani", "id", "maxBreakpoint", "minBreakpoint", "enableBackdrop", "classList", "remove", "disableBackdrop", "add", "shouldEnableBackdrop", "focus", "initialStep", "secondToLastBreakpoint", "diff", "reduce", "moveSheetToBreakpoint", "breakpoint", "breakpointOffset", "animated", "shouldPreventDismiss", "snapToBreakpoint", "shouldRemainOpen", "Promise", "resolve", "modalIosCss", "IonModalIosStyle0", "modalMdCss", "IonModalMdStyle0", "Modal", "constructor", "hostRef", "didPresent", "willPresent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ionBreakpointDidChange", "didPresentShorthand", "willPresentShorthand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ionMount", "lockController", "triggerController", "coreDelegate", "isSheetModal", "inheritedAttributes", "inline", "gestureAnimationDismissing", "onHandleClick", "sheetTransition", "handleBehavior", "moveToNextBreakpoint", "onBackdropTap", "onLifecycle", "modalEvent", "usersElement", "name", "LIFECYCLE_MAP", "type", "ev", "CustomEvent", "bubbles", "cancelable", "dispatchEvent", "presented", "hasController", "overlayIndex", "delegate", "keyboardClose", "enterAnimation", "leaveAnimation", "handle", "component", "componentProps", "cssClass", "<PERSON><PERSON><PERSON><PERSON>", "showBackdrop", "htmlAttributes", "trigger", "keepContentsMounted", "focusTrap", "onIsOpenChange", "newValue", "oldValue", "triggerChanged", "addClickListener", "breakpointsChanged", "sortedBreakpoints", "sort", "connectedCallback", "disconnectedCallback", "removeClickListener", "componentWillLoad", "_a", "attributesToInherit", "for<PERSON>ach", "attribute", "attributeValue", "Object", "assign", "includes", "componentDidLoad", "getDelegate", "force", "workingDelegate", "parentEl", "parentNode", "checkCanDismiss", "data", "role", "_this", "_this2", "unlock", "lock", "emit", "keyboardOpenCallback", "addEventListener", "initSheetGesture", "initSwipeToClose", "_this3", "animationBuilder", "get", "sheetOn<PERSON><PERSON>iss", "_this4", "_this5", "removeEventListener", "dismissed", "destroy", "onDid<PERSON><PERSON><PERSON>", "on<PERSON>ill<PERSON><PERSON>iss", "setCurrentBreakpoint", "_this6", "_this7", "_this8", "allowedBreakpoints", "currentBreakpointIndex", "indexOf", "nextBreakpointIndex", "nextBreakpoint", "render", "showHandle", "mode", "isCardModal", "isHandleCycle", "key", "tabindex", "zIndex", "class", "onIonBackdropTap", "onIonModalDidPresent", "onIonModalWillPresent", "onIonModalWillDismiss", "onIonModalDidDismiss", "ref", "visible", "tappable", "part", "tabIndex", "onClick", "watchers", "ionModalDidPresent", "ionModalWillPresent", "ionModalWillDismiss", "ionModalDidDismiss", "ios", "md", "ion_modal"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/ion-modal.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, w as writeTask, h, f as Host, i as getElement } from './index-28849c61.js';\nimport { f as findClosestIonContent, i as isIonContent, d as disableContentScrollY, r as resetContentScrollY, a as findIonContent, p as printIonContentErrorMsg } from './index-5cc724f3.js';\nimport { C as CoreDelegate, a as attachComponent, d as detachComponent } from './framework-delegate-63d1a679.js';\nimport { g as getElementRoot, j as clamp, r as raf, h as inheritAttributes, k as hasLazyBuild } from './helpers-da915de8.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { g as getCapacitor } from './capacitor-59395cbd.js';\nimport { G as GESTURE, O as OVERLAY_GESTURE_PRIORITY, F as FOCUS_TRAP_DISABLE_CLASS, e as createTriggerController, B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod } from './overlays-e7b9d6d9.js';\nimport { g as getClassMap } from './theme-01f3f29c.js';\nimport { e as deepReady, w as waitForMount } from './index-3ad7f18b.js';\nimport { b as getIonMode, c as config } from './ionic-global-c81d82ab.js';\nimport { KEYBOARD_DID_OPEN } from './keyboard-52278bd7.js';\nimport { c as createAnimation } from './animation-eab5a4ca.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { createGesture } from './index-39782642.js';\nimport { w as win } from './index-a5d50daf.js';\nimport './hardware-back-button-06ef3c3e.js';\nimport './gesture-controller-314a54f6.js';\nimport './keyboard-73175e24.js';\n\nvar Style;\n(function (Style) {\n    Style[\"Dark\"] = \"DARK\";\n    Style[\"Light\"] = \"LIGHT\";\n    Style[\"Default\"] = \"DEFAULT\";\n})(Style || (Style = {}));\nconst StatusBar = {\n    getEngine() {\n        const capacitor = getCapacitor();\n        if (capacitor === null || capacitor === void 0 ? void 0 : capacitor.isPluginAvailable('StatusBar')) {\n            return capacitor.Plugins.StatusBar;\n        }\n        return undefined;\n    },\n    setStyle(options) {\n        const engine = this.getEngine();\n        if (!engine) {\n            return;\n        }\n        engine.setStyle(options);\n    },\n    getStyle: async function () {\n        const engine = this.getEngine();\n        if (!engine) {\n            return Style.Default;\n        }\n        const { style } = await engine.getInfo();\n        return style;\n    },\n};\n\n/**\n * Use y = mx + b to\n * figure out the backdrop value\n * at a particular x coordinate. This\n * is useful when the backdrop does\n * not begin to fade in until after\n * the 0 breakpoint.\n */\nconst getBackdropValueForSheet = (x, backdropBreakpoint) => {\n    /**\n     * We will use these points:\n     * (backdropBreakpoint, 0)\n     * (maxBreakpoint, 1)\n     * We know that at the beginning breakpoint,\n     * the backdrop will be hidden. We also\n     * know that at the maxBreakpoint, the backdrop\n     * must be fully visible. maxBreakpoint should\n     * always be 1 even if the maximum value\n     * of the breakpoints array is not 1 since\n     * the animation runs from a progress of 0\n     * to a progress of 1.\n     * m = (y2 - y1) / (x2 - x1)\n     *\n     * This is simplified from:\n     * m = (1 - 0) / (maxBreakpoint - backdropBreakpoint)\n     *\n     * If the backdropBreakpoint is 1, we return 0 as the\n     * backdrop is completely hidden.\n     *\n     */\n    if (backdropBreakpoint === 1) {\n        return 0;\n    }\n    const slope = 1 / (1 - backdropBreakpoint);\n    /**\n     * From here, compute b which is\n     * the backdrop opacity if the offset\n     * is 0. If the backdrop does not\n     * begin to fade in until after the\n     * 0 breakpoint, this b value will be\n     * negative. This is fine as we never pass\n     * b directly into the animation keyframes.\n     * b = y - mx\n     * Use a known point: (backdropBreakpoint, 0)\n     * This is simplified from:\n     * b = 0 - (backdropBreakpoint * slope)\n     */\n    const b = -(backdropBreakpoint * slope);\n    /**\n     * Finally, we can now determine the\n     * backdrop offset given an arbitrary\n     * gesture offset.\n     */\n    return x * slope + b;\n};\n/**\n * The tablet/desktop card modal activates\n * when the window width is >= 768.\n * At that point, the presenting element\n * is not transformed, so we do not need to\n * adjust the status bar color.\n *\n */\nconst setCardStatusBarDark = () => {\n    if (!win || win.innerWidth >= 768) {\n        return;\n    }\n    StatusBar.setStyle({ style: Style.Dark });\n};\nconst setCardStatusBarDefault = (defaultStyle = Style.Default) => {\n    if (!win || win.innerWidth >= 768) {\n        return;\n    }\n    StatusBar.setStyle({ style: defaultStyle });\n};\n\nconst handleCanDismiss = async (el, animation) => {\n    /**\n     * If canDismiss is not a function\n     * then we can return early. If canDismiss is `true`,\n     * then canDismissBlocksGesture is `false` as canDismiss\n     * will never interrupt the gesture. As a result,\n     * this code block is never reached. If canDismiss is `false`,\n     * then we never dismiss.\n     */\n    if (typeof el.canDismiss !== 'function') {\n        return;\n    }\n    /**\n     * Run the canDismiss callback.\n     * If the function returns `true`,\n     * then we can proceed with dismiss.\n     */\n    const shouldDismiss = await el.canDismiss(undefined, GESTURE);\n    if (!shouldDismiss) {\n        return;\n    }\n    /**\n     * If canDismiss resolved after the snap\n     * back animation finished, we can\n     * dismiss immediately.\n     *\n     * If canDismiss resolved before the snap\n     * back animation finished, we need to\n     * wait until the snap back animation is\n     * done before dismissing.\n     */\n    if (animation.isRunning()) {\n        animation.onFinish(() => {\n            el.dismiss(undefined, 'handler');\n        }, { oneTimeCallback: true });\n    }\n    else {\n        el.dismiss(undefined, 'handler');\n    }\n};\n/**\n * This function lets us simulate a realistic spring-like animation\n * when swiping down on the modal.\n * There are two forces that we need to use to compute the spring physics:\n *\n * 1. Stiffness, k: This is a measure of resistance applied a spring.\n * 2. Dampening, c: This value has the effect of reducing or preventing oscillation.\n *\n * Using these two values, we can calculate the Spring Force and the Dampening Force\n * to compute the total force applied to a spring.\n *\n * Spring Force: This force pulls a spring back into its equilibrium position.\n * Hooke's Law tells us that that spring force (FS) = kX.\n * k is the stiffness of a spring, and X is the displacement of the spring from its\n * equilibrium position. In this case, it is the amount by which the free end\n * of a spring was displaced (stretched/pushed) from its \"relaxed\" position.\n *\n * Dampening Force: This force slows down motion. Without it, a spring would oscillate forever.\n * The dampening force, FD, can be found via this formula: FD = -cv\n * where c the dampening value and v is velocity.\n *\n * Therefore, the resulting force that is exerted on the block is:\n * F = FS + FD = -kX - cv\n *\n * Newton's 2nd Law tells us that F = ma:\n * ma = -kX - cv.\n *\n * For Ionic's purposes, we can assume that m = 1:\n * a = -kX - cv\n *\n * Imagine a block attached to the end of a spring. At equilibrium\n * the block is at position x = 1.\n * Pressing on the block moves it to position x = 0;\n * So, to calculate the displacement, we need to take the\n * current position and subtract the previous position from it.\n * X = x - x0 = 0 - 1 = -1.\n *\n * For Ionic's purposes, we are only pushing on the spring modal\n * so we have a max position of 1.\n * As a result, we can expand displacement to this formula:\n * X = x - 1\n *\n * a = -k(x - 1) - cv\n *\n * We can represent the motion of something as a function of time: f(t) = x.\n * The derivative of position gives us the velocity: f'(t)\n * The derivative of the velocity gives us the acceleration: f''(t)\n *\n * We can substitute the formula above with these values:\n *\n * f\"(t) = -k * (f(t) - 1) - c * f'(t)\n *\n * This is called a differential equation.\n *\n * We know that at t = 0, we are at x = 0 because the modal does not move: f(0) = 0\n * This means our velocity is also zero: f'(0) = 0.\n *\n * We can cheat a bit and plug the formula into Wolfram Alpha.\n * However, we need to pick stiffness and dampening values:\n * k = 0.57\n * c = 15\n *\n * I picked these as they are fairly close to native iOS's spring effect\n * with the modal.\n *\n * What we plug in is this: f(0) = 0; f'(0) = 0; f''(t) = -0.57(f(t) - 1) - 15f'(t)\n *\n * The result is a formula that lets us calculate the acceleration\n * for a given time t.\n * Note: This is the approximate form of the solution. Wolfram Alpha will\n * give you a complex differential equation too.\n */\nconst calculateSpringStep = (t) => {\n    return 0.00255275 * 2.71828 ** (-14.9619 * t) - 1.00255 * 2.71828 ** (-0.0380968 * t) + 1;\n};\n\n// Defaults for the card swipe animation\nconst SwipeToCloseDefaults = {\n    MIN_PRESENTING_SCALE: 0.915,\n};\nconst createSwipeToCloseGesture = (el, animation, statusBarStyle, onDismiss) => {\n    /**\n     * The step value at which a card modal\n     * is eligible for dismissing via gesture.\n     */\n    const DISMISS_THRESHOLD = 0.5;\n    const height = el.offsetHeight;\n    let isOpen = false;\n    let canDismissBlocksGesture = false;\n    let contentEl = null;\n    let scrollEl = null;\n    const canDismissMaxStep = 0.2;\n    let initialScrollY = true;\n    let lastStep = 0;\n    const getScrollY = () => {\n        if (contentEl && isIonContent(contentEl)) {\n            return contentEl.scrollY;\n            /**\n             * Custom scroll containers are intended to be\n             * used with virtual scrolling, so we assume\n             * there is scrolling in this case.\n             */\n        }\n        else {\n            return true;\n        }\n    };\n    const canStart = (detail) => {\n        const target = detail.event.target;\n        if (target === null || !target.closest) {\n            return true;\n        }\n        /**\n         * If we are swiping on the content,\n         * swiping should only be possible if\n         * the content is scrolled all the way\n         * to the top so that we do not interfere\n         * with scrolling.\n         *\n         * We cannot assume that the `ion-content`\n         * target will remain consistent between\n         * swipes. For example, when using\n         * ion-nav within a card modal it is\n         * possible to swipe, push a view, and then\n         * swipe again. The target content will not\n         * be the same between swipes.\n         */\n        contentEl = findClosestIonContent(target);\n        if (contentEl) {\n            /**\n             * The card should never swipe to close\n             * on the content with a refresher.\n             * Note: We cannot solve this by making the\n             * swipeToClose gesture have a higher priority\n             * than the refresher gesture as the iOS native\n             * refresh gesture uses a scroll listener in\n             * addition to a gesture.\n             *\n             * Note: Do not use getScrollElement here\n             * because we need this to be a synchronous\n             * operation, and getScrollElement is\n             * asynchronous.\n             */\n            if (isIonContent(contentEl)) {\n                const root = getElementRoot(contentEl);\n                scrollEl = root.querySelector('.inner-scroll');\n            }\n            else {\n                scrollEl = contentEl;\n            }\n            const hasRefresherInContent = !!contentEl.querySelector('ion-refresher');\n            return !hasRefresherInContent && scrollEl.scrollTop === 0;\n        }\n        /**\n         * Card should be swipeable on all\n         * parts of the modal except for the footer.\n         */\n        const footer = target.closest('ion-footer');\n        if (footer === null) {\n            return true;\n        }\n        return false;\n    };\n    const onStart = (detail) => {\n        const { deltaY } = detail;\n        /**\n         * Get the initial scrollY value so\n         * that we can correctly reset the scrollY\n         * prop when the gesture ends.\n         */\n        initialScrollY = getScrollY();\n        /**\n         * If canDismiss is anything other than `true`\n         * then users should be able to swipe down\n         * until a threshold is hit. At that point,\n         * the card modal should not proceed any further.\n         * TODO (FW-937)\n         * Remove undefined check\n         */\n        canDismissBlocksGesture = el.canDismiss !== undefined && el.canDismiss !== true;\n        /**\n         * If we are pulling down, then\n         * it is possible we are pulling on the\n         * content. We do not want scrolling to\n         * happen at the same time as the gesture.\n         */\n        if (deltaY > 0 && contentEl) {\n            disableContentScrollY(contentEl);\n        }\n        animation.progressStart(true, isOpen ? 1 : 0);\n    };\n    const onMove = (detail) => {\n        const { deltaY } = detail;\n        /**\n         * If we are pulling down, then\n         * it is possible we are pulling on the\n         * content. We do not want scrolling to\n         * happen at the same time as the gesture.\n         */\n        if (deltaY > 0 && contentEl) {\n            disableContentScrollY(contentEl);\n        }\n        /**\n         * If we are swiping on the content\n         * then the swipe gesture should only\n         * happen if we are pulling down.\n         *\n         * However, if we pull up and\n         * then down such that the scroll position\n         * returns to 0, we should be able to swipe\n         * the card.\n         */\n        const step = detail.deltaY / height;\n        /**\n         * Check if user is swiping down and\n         * if we have a canDismiss value that\n         * should block the gesture from\n         * proceeding,\n         */\n        const isAttemptingDismissWithCanDismiss = step >= 0 && canDismissBlocksGesture;\n        /**\n         * If we are blocking the gesture from dismissing,\n         * set the max step value so that the sheet cannot be\n         * completely hidden.\n         */\n        const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n        /**\n         * If we are blocking the gesture from\n         * dismissing, calculate the spring modifier value\n         * this will be added to the starting breakpoint\n         * value to give the gesture a spring-like feeling.\n         * Note that the starting breakpoint is always 0,\n         * so we omit adding 0 to the result.\n         */\n        const processedStep = isAttemptingDismissWithCanDismiss ? calculateSpringStep(step / maxStep) : step;\n        const clampedStep = clamp(0.0001, processedStep, maxStep);\n        animation.progressStep(clampedStep);\n        /**\n         * When swiping down half way, the status bar style\n         * should be reset to its default value.\n         *\n         * We track lastStep so that we do not fire these\n         * functions on every onMove, only when the user has\n         * crossed a certain threshold.\n         */\n        if (clampedStep >= DISMISS_THRESHOLD && lastStep < DISMISS_THRESHOLD) {\n            setCardStatusBarDefault(statusBarStyle);\n            /**\n             * However, if we swipe back up, then the\n             * status bar style should be set to have light\n             * text on a dark background.\n             */\n        }\n        else if (clampedStep < DISMISS_THRESHOLD && lastStep >= DISMISS_THRESHOLD) {\n            setCardStatusBarDark();\n        }\n        lastStep = clampedStep;\n    };\n    const onEnd = (detail) => {\n        const velocity = detail.velocityY;\n        const step = detail.deltaY / height;\n        const isAttemptingDismissWithCanDismiss = step >= 0 && canDismissBlocksGesture;\n        const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n        const processedStep = isAttemptingDismissWithCanDismiss ? calculateSpringStep(step / maxStep) : step;\n        const clampedStep = clamp(0.0001, processedStep, maxStep);\n        const threshold = (detail.deltaY + velocity * 1000) / height;\n        /**\n         * If canDismiss blocks\n         * the swipe gesture, then the\n         * animation can never complete until\n         * canDismiss is checked.\n         */\n        const shouldComplete = !isAttemptingDismissWithCanDismiss && threshold >= DISMISS_THRESHOLD;\n        let newStepValue = shouldComplete ? -0.001 : 0.001;\n        if (!shouldComplete) {\n            animation.easing('cubic-bezier(1, 0, 0.68, 0.28)');\n            newStepValue += getTimeGivenProgression([0, 0], [1, 0], [0.68, 0.28], [1, 1], clampedStep)[0];\n        }\n        else {\n            animation.easing('cubic-bezier(0.32, 0.72, 0, 1)');\n            newStepValue += getTimeGivenProgression([0, 0], [0.32, 0.72], [0, 1], [1, 1], clampedStep)[0];\n        }\n        const duration = shouldComplete\n            ? computeDuration(step * height, velocity)\n            : computeDuration((1 - clampedStep) * height, velocity);\n        isOpen = shouldComplete;\n        gesture.enable(false);\n        if (contentEl) {\n            resetContentScrollY(contentEl, initialScrollY);\n        }\n        animation\n            .onFinish(() => {\n            if (!shouldComplete) {\n                gesture.enable(true);\n            }\n        })\n            .progressEnd(shouldComplete ? 1 : 0, newStepValue, duration);\n        /**\n         * If the canDismiss value blocked the gesture\n         * from proceeding, then we should ignore whatever\n         * shouldComplete is. Whether or not the modal\n         * animation should complete is now determined by\n         * canDismiss.\n         *\n         * If the user swiped >25% of the way\n         * to the max step, then we should\n         * check canDismiss. 25% was chosen\n         * to avoid accidental swipes.\n         */\n        if (isAttemptingDismissWithCanDismiss && clampedStep > maxStep / 4) {\n            handleCanDismiss(el, animation);\n        }\n        else if (shouldComplete) {\n            onDismiss();\n        }\n    };\n    const gesture = createGesture({\n        el,\n        gestureName: 'modalSwipeToClose',\n        gesturePriority: OVERLAY_GESTURE_PRIORITY,\n        direction: 'y',\n        threshold: 10,\n        canStart,\n        onStart,\n        onMove,\n        onEnd,\n    });\n    return gesture;\n};\nconst computeDuration = (remaining, velocity) => {\n    return clamp(400, remaining / Math.abs(velocity * 1.1), 500);\n};\n\nconst createSheetEnterAnimation = (opts) => {\n    const { currentBreakpoint, backdropBreakpoint } = opts;\n    /**\n     * If the backdropBreakpoint is undefined, then the backdrop\n     * should always fade in. If the backdropBreakpoint came before the\n     * current breakpoint, then the backdrop should be fading in.\n     */\n    const shouldShowBackdrop = backdropBreakpoint === undefined || backdropBreakpoint < currentBreakpoint;\n    const initialBackdrop = shouldShowBackdrop ? `calc(var(--backdrop-opacity) * ${currentBreakpoint})` : '0';\n    const backdropAnimation = createAnimation('backdropAnimation').fromTo('opacity', 0, initialBackdrop);\n    if (shouldShowBackdrop) {\n        backdropAnimation\n            .beforeStyles({\n            'pointer-events': 'none',\n        })\n            .afterClearStyles(['pointer-events']);\n    }\n    const wrapperAnimation = createAnimation('wrapperAnimation').keyframes([\n        { offset: 0, opacity: 1, transform: 'translateY(100%)' },\n        { offset: 1, opacity: 1, transform: `translateY(${100 - currentBreakpoint * 100}%)` },\n    ]);\n    return { wrapperAnimation, backdropAnimation };\n};\nconst createSheetLeaveAnimation = (opts) => {\n    const { currentBreakpoint, backdropBreakpoint } = opts;\n    /**\n     * Backdrop does not always fade in from 0 to 1 if backdropBreakpoint\n     * is defined, so we need to account for that offset by figuring out\n     * what the current backdrop value should be.\n     */\n    const backdropValue = `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(currentBreakpoint, backdropBreakpoint)})`;\n    const defaultBackdrop = [\n        { offset: 0, opacity: backdropValue },\n        { offset: 1, opacity: 0 },\n    ];\n    const customBackdrop = [\n        { offset: 0, opacity: backdropValue },\n        { offset: backdropBreakpoint, opacity: 0 },\n        { offset: 1, opacity: 0 },\n    ];\n    const backdropAnimation = createAnimation('backdropAnimation').keyframes(backdropBreakpoint !== 0 ? customBackdrop : defaultBackdrop);\n    const wrapperAnimation = createAnimation('wrapperAnimation').keyframes([\n        { offset: 0, opacity: 1, transform: `translateY(${100 - currentBreakpoint * 100}%)` },\n        { offset: 1, opacity: 1, transform: `translateY(100%)` },\n    ]);\n    return { wrapperAnimation, backdropAnimation };\n};\n\nconst createEnterAnimation$1 = () => {\n    const backdropAnimation = createAnimation()\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    const wrapperAnimation = createAnimation().fromTo('transform', 'translateY(100vh)', 'translateY(0vh)');\n    return { backdropAnimation, wrapperAnimation };\n};\n/**\n * iOS Modal Enter Animation for the Card presentation style\n */\nconst iosEnterAnimation = (baseEl, opts) => {\n    const { presentingEl, currentBreakpoint } = opts;\n    const root = getElementRoot(baseEl);\n    const { wrapperAnimation, backdropAnimation } = currentBreakpoint !== undefined ? createSheetEnterAnimation(opts) : createEnterAnimation$1();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n    wrapperAnimation.addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow')).beforeStyles({ opacity: 1 });\n    const baseAnimation = createAnimation('entering-base')\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.32,0.72,0,1)')\n        .duration(500)\n        .addAnimation(wrapperAnimation);\n    if (presentingEl) {\n        const isMobile = window.innerWidth < 768;\n        const hasCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n        const presentingElRoot = getElementRoot(presentingEl);\n        const presentingAnimation = createAnimation().beforeStyles({\n            transform: 'translateY(0)',\n            'transform-origin': 'top center',\n            overflow: 'hidden',\n        });\n        const bodyEl = document.body;\n        if (isMobile) {\n            /**\n             * Fallback for browsers that does not support `max()` (ex: Firefox)\n             * No need to worry about statusbar padding since engines like Gecko\n             * are not used as the engine for standalone Cordova/Capacitor apps\n             */\n            const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n            const modalTransform = hasCardModal ? '-10px' : transformOffset;\n            const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n            const finalTransform = `translateY(${modalTransform}) scale(${toPresentingScale})`;\n            presentingAnimation\n                .afterStyles({\n                transform: finalTransform,\n            })\n                .beforeAddWrite(() => bodyEl.style.setProperty('background-color', 'black'))\n                .addElement(presentingEl)\n                .keyframes([\n                { offset: 0, filter: 'contrast(1)', transform: 'translateY(0px) scale(1)', borderRadius: '0px' },\n                { offset: 1, filter: 'contrast(0.85)', transform: finalTransform, borderRadius: '10px 10px 0 0' },\n            ]);\n            baseAnimation.addAnimation(presentingAnimation);\n        }\n        else {\n            baseAnimation.addAnimation(backdropAnimation);\n            if (!hasCardModal) {\n                wrapperAnimation.fromTo('opacity', '0', '1');\n            }\n            else {\n                const toPresentingScale = hasCardModal ? SwipeToCloseDefaults.MIN_PRESENTING_SCALE : 1;\n                const finalTransform = `translateY(-10px) scale(${toPresentingScale})`;\n                presentingAnimation\n                    .afterStyles({\n                    transform: finalTransform,\n                })\n                    .addElement(presentingElRoot.querySelector('.modal-wrapper'))\n                    .keyframes([\n                    { offset: 0, filter: 'contrast(1)', transform: 'translateY(0) scale(1)' },\n                    { offset: 1, filter: 'contrast(0.85)', transform: finalTransform },\n                ]);\n                const shadowAnimation = createAnimation()\n                    .afterStyles({\n                    transform: finalTransform,\n                })\n                    .addElement(presentingElRoot.querySelector('.modal-shadow'))\n                    .keyframes([\n                    { offset: 0, opacity: '1', transform: 'translateY(0) scale(1)' },\n                    { offset: 1, opacity: '0', transform: finalTransform },\n                ]);\n                baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n            }\n        }\n    }\n    else {\n        baseAnimation.addAnimation(backdropAnimation);\n    }\n    return baseAnimation;\n};\n\nconst createLeaveAnimation$1 = () => {\n    const backdropAnimation = createAnimation().fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    const wrapperAnimation = createAnimation().fromTo('transform', 'translateY(0vh)', 'translateY(100vh)');\n    return { backdropAnimation, wrapperAnimation };\n};\n/**\n * iOS Modal Leave Animation\n */\nconst iosLeaveAnimation = (baseEl, opts, duration = 500) => {\n    const { presentingEl, currentBreakpoint } = opts;\n    const root = getElementRoot(baseEl);\n    const { wrapperAnimation, backdropAnimation } = currentBreakpoint !== undefined ? createSheetLeaveAnimation(opts) : createLeaveAnimation$1();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n    wrapperAnimation.addElement(root.querySelectorAll('.modal-wrapper, .modal-shadow')).beforeStyles({ opacity: 1 });\n    const baseAnimation = createAnimation('leaving-base')\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.32,0.72,0,1)')\n        .duration(duration)\n        .addAnimation(wrapperAnimation);\n    if (presentingEl) {\n        const isMobile = window.innerWidth < 768;\n        const hasCardModal = presentingEl.tagName === 'ION-MODAL' && presentingEl.presentingElement !== undefined;\n        const presentingElRoot = getElementRoot(presentingEl);\n        const presentingAnimation = createAnimation()\n            .beforeClearStyles(['transform'])\n            .afterClearStyles(['transform'])\n            .onFinish((currentStep) => {\n            // only reset background color if this is the last card-style modal\n            if (currentStep !== 1) {\n                return;\n            }\n            presentingEl.style.setProperty('overflow', '');\n            const numModals = Array.from(bodyEl.querySelectorAll('ion-modal:not(.overlay-hidden)')).filter((m) => m.presentingElement !== undefined).length;\n            if (numModals <= 1) {\n                bodyEl.style.setProperty('background-color', '');\n            }\n        });\n        const bodyEl = document.body;\n        if (isMobile) {\n            const transformOffset = !CSS.supports('width', 'max(0px, 1px)') ? '30px' : 'max(30px, var(--ion-safe-area-top))';\n            const modalTransform = hasCardModal ? '-10px' : transformOffset;\n            const toPresentingScale = SwipeToCloseDefaults.MIN_PRESENTING_SCALE;\n            const finalTransform = `translateY(${modalTransform}) scale(${toPresentingScale})`;\n            presentingAnimation.addElement(presentingEl).keyframes([\n                { offset: 0, filter: 'contrast(0.85)', transform: finalTransform, borderRadius: '10px 10px 0 0' },\n                { offset: 1, filter: 'contrast(1)', transform: 'translateY(0px) scale(1)', borderRadius: '0px' },\n            ]);\n            baseAnimation.addAnimation(presentingAnimation);\n        }\n        else {\n            baseAnimation.addAnimation(backdropAnimation);\n            if (!hasCardModal) {\n                wrapperAnimation.fromTo('opacity', '1', '0');\n            }\n            else {\n                const toPresentingScale = hasCardModal ? SwipeToCloseDefaults.MIN_PRESENTING_SCALE : 1;\n                const finalTransform = `translateY(-10px) scale(${toPresentingScale})`;\n                presentingAnimation\n                    .addElement(presentingElRoot.querySelector('.modal-wrapper'))\n                    .afterStyles({\n                    transform: 'translate3d(0, 0, 0)',\n                })\n                    .keyframes([\n                    { offset: 0, filter: 'contrast(0.85)', transform: finalTransform },\n                    { offset: 1, filter: 'contrast(1)', transform: 'translateY(0) scale(1)' },\n                ]);\n                const shadowAnimation = createAnimation()\n                    .addElement(presentingElRoot.querySelector('.modal-shadow'))\n                    .afterStyles({\n                    transform: 'translateY(0) scale(1)',\n                })\n                    .keyframes([\n                    { offset: 0, opacity: '0', transform: finalTransform },\n                    { offset: 1, opacity: '1', transform: 'translateY(0) scale(1)' },\n                ]);\n                baseAnimation.addAnimation([presentingAnimation, shadowAnimation]);\n            }\n        }\n    }\n    else {\n        baseAnimation.addAnimation(backdropAnimation);\n    }\n    return baseAnimation;\n};\n\nconst createEnterAnimation = () => {\n    const backdropAnimation = createAnimation()\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    const wrapperAnimation = createAnimation().keyframes([\n        { offset: 0, opacity: 0.01, transform: 'translateY(40px)' },\n        { offset: 1, opacity: 1, transform: `translateY(0px)` },\n    ]);\n    return { backdropAnimation, wrapperAnimation };\n};\n/**\n * Md Modal Enter Animation\n */\nconst mdEnterAnimation = (baseEl, opts) => {\n    const { currentBreakpoint } = opts;\n    const root = getElementRoot(baseEl);\n    const { wrapperAnimation, backdropAnimation } = currentBreakpoint !== undefined ? createSheetEnterAnimation(opts) : createEnterAnimation();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n    wrapperAnimation.addElement(root.querySelector('.modal-wrapper'));\n    return createAnimation()\n        .addElement(baseEl)\n        .easing('cubic-bezier(0.36,0.66,0.04,1)')\n        .duration(280)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst createLeaveAnimation = () => {\n    const backdropAnimation = createAnimation().fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    const wrapperAnimation = createAnimation().keyframes([\n        { offset: 0, opacity: 0.99, transform: `translateY(0px)` },\n        { offset: 1, opacity: 0, transform: 'translateY(40px)' },\n    ]);\n    return { backdropAnimation, wrapperAnimation };\n};\n/**\n * Md Modal Leave Animation\n */\nconst mdLeaveAnimation = (baseEl, opts) => {\n    const { currentBreakpoint } = opts;\n    const root = getElementRoot(baseEl);\n    const { wrapperAnimation, backdropAnimation } = currentBreakpoint !== undefined ? createSheetLeaveAnimation(opts) : createLeaveAnimation();\n    backdropAnimation.addElement(root.querySelector('ion-backdrop'));\n    wrapperAnimation.addElement(root.querySelector('.modal-wrapper'));\n    return createAnimation()\n        .easing('cubic-bezier(0.47,0,0.745,0.715)')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst createSheetGesture = (baseEl, backdropEl, wrapperEl, initialBreakpoint, backdropBreakpoint, animation, breakpoints = [], getCurrentBreakpoint, onDismiss, onBreakpointChange) => {\n    // Defaults for the sheet swipe animation\n    const defaultBackdrop = [\n        { offset: 0, opacity: 'var(--backdrop-opacity)' },\n        { offset: 1, opacity: 0.01 },\n    ];\n    const customBackdrop = [\n        { offset: 0, opacity: 'var(--backdrop-opacity)' },\n        { offset: 1 - backdropBreakpoint, opacity: 0 },\n        { offset: 1, opacity: 0 },\n    ];\n    const SheetDefaults = {\n        WRAPPER_KEYFRAMES: [\n            { offset: 0, transform: 'translateY(0%)' },\n            { offset: 1, transform: 'translateY(100%)' },\n        ],\n        BACKDROP_KEYFRAMES: backdropBreakpoint !== 0 ? customBackdrop : defaultBackdrop,\n    };\n    const contentEl = baseEl.querySelector('ion-content');\n    const height = wrapperEl.clientHeight;\n    let currentBreakpoint = initialBreakpoint;\n    let offset = 0;\n    let canDismissBlocksGesture = false;\n    const canDismissMaxStep = 0.95;\n    const wrapperAnimation = animation.childAnimations.find((ani) => ani.id === 'wrapperAnimation');\n    const backdropAnimation = animation.childAnimations.find((ani) => ani.id === 'backdropAnimation');\n    const maxBreakpoint = breakpoints[breakpoints.length - 1];\n    const minBreakpoint = breakpoints[0];\n    const enableBackdrop = () => {\n        baseEl.style.setProperty('pointer-events', 'auto');\n        backdropEl.style.setProperty('pointer-events', 'auto');\n        /**\n         * When the backdrop is enabled, elements such\n         * as inputs should not be focusable outside\n         * the sheet.\n         */\n        baseEl.classList.remove(FOCUS_TRAP_DISABLE_CLASS);\n    };\n    const disableBackdrop = () => {\n        baseEl.style.setProperty('pointer-events', 'none');\n        backdropEl.style.setProperty('pointer-events', 'none');\n        /**\n         * When the backdrop is enabled, elements such\n         * as inputs should not be focusable outside\n         * the sheet.\n         * Adding this class disables focus trapping\n         * for the sheet temporarily.\n         */\n        baseEl.classList.add(FOCUS_TRAP_DISABLE_CLASS);\n    };\n    /**\n     * After the entering animation completes,\n     * we need to set the animation to go from\n     * offset 0 to offset 1 so that users can\n     * swipe in any direction. We then set the\n     * animation offset to the current breakpoint\n     * so there is no flickering.\n     */\n    if (wrapperAnimation && backdropAnimation) {\n        wrapperAnimation.keyframes([...SheetDefaults.WRAPPER_KEYFRAMES]);\n        backdropAnimation.keyframes([...SheetDefaults.BACKDROP_KEYFRAMES]);\n        animation.progressStart(true, 1 - currentBreakpoint);\n        /**\n         * If backdrop is not enabled, then content\n         * behind modal should be clickable. To do this, we need\n         * to remove pointer-events from ion-modal as a whole.\n         * ion-backdrop and .modal-wrapper always have pointer-events: auto\n         * applied, so the modal content can still be interacted with.\n         */\n        const shouldEnableBackdrop = currentBreakpoint > backdropBreakpoint;\n        if (shouldEnableBackdrop) {\n            enableBackdrop();\n        }\n        else {\n            disableBackdrop();\n        }\n    }\n    if (contentEl && currentBreakpoint !== maxBreakpoint) {\n        contentEl.scrollY = false;\n    }\n    const canStart = (detail) => {\n        /**\n         * If we are swiping on the content, swiping should only be possible if the content\n         * is scrolled all the way to the top so that we do not interfere with scrolling.\n         *\n         * We cannot assume that the `ion-content` target will remain consistent between swipes.\n         * For example, when using ion-nav within a modal it is possible to swipe, push a view,\n         * and then swipe again. The target content will not be the same between swipes.\n         */\n        const contentEl = findClosestIonContent(detail.event.target);\n        currentBreakpoint = getCurrentBreakpoint();\n        if (currentBreakpoint === 1 && contentEl) {\n            /**\n             * The modal should never swipe to close on the content with a refresher.\n             * Note 1: We cannot solve this by making this gesture have a higher priority than\n             * the refresher gesture as the iOS native refresh gesture uses a scroll listener in\n             * addition to a gesture.\n             *\n             * Note 2: Do not use getScrollElement here because we need this to be a synchronous\n             * operation, and getScrollElement is asynchronous.\n             */\n            const scrollEl = isIonContent(contentEl) ? getElementRoot(contentEl).querySelector('.inner-scroll') : contentEl;\n            const hasRefresherInContent = !!contentEl.querySelector('ion-refresher');\n            return !hasRefresherInContent && scrollEl.scrollTop === 0;\n        }\n        return true;\n    };\n    const onStart = (detail) => {\n        /**\n         * If canDismiss is anything other than `true`\n         * then users should be able to swipe down\n         * until a threshold is hit. At that point,\n         * the card modal should not proceed any further.\n         *\n         * canDismiss is never fired via gesture if there is\n         * no 0 breakpoint. However, it can be fired if the user\n         * presses Esc or the hardware back button.\n         * TODO (FW-937)\n         * Remove undefined check\n         */\n        canDismissBlocksGesture = baseEl.canDismiss !== undefined && baseEl.canDismiss !== true && minBreakpoint === 0;\n        /**\n         * If we are pulling down, then it is possible we are pulling on the content.\n         * We do not want scrolling to happen at the same time as the gesture.\n         */\n        if (detail.deltaY > 0 && contentEl) {\n            contentEl.scrollY = false;\n        }\n        raf(() => {\n            /**\n             * Dismisses the open keyboard when the sheet drag gesture is started.\n             * Sets the focus onto the modal element.\n             */\n            baseEl.focus();\n        });\n        animation.progressStart(true, 1 - currentBreakpoint);\n    };\n    const onMove = (detail) => {\n        /**\n         * If we are pulling down, then it is possible we are pulling on the content.\n         * We do not want scrolling to happen at the same time as the gesture.\n         * This accounts for when the user scrolls down, scrolls all the way up, and then\n         * pulls down again such that the modal should start to move.\n         */\n        if (detail.deltaY > 0 && contentEl) {\n            contentEl.scrollY = false;\n        }\n        /**\n         * Given the change in gesture position on the Y axis,\n         * compute where the offset of the animation should be\n         * relative to where the user dragged.\n         */\n        const initialStep = 1 - currentBreakpoint;\n        const secondToLastBreakpoint = breakpoints.length > 1 ? 1 - breakpoints[1] : undefined;\n        const step = initialStep + detail.deltaY / height;\n        const isAttemptingDismissWithCanDismiss = secondToLastBreakpoint !== undefined && step >= secondToLastBreakpoint && canDismissBlocksGesture;\n        /**\n         * If we are blocking the gesture from dismissing,\n         * set the max step value so that the sheet cannot be\n         * completely hidden.\n         */\n        const maxStep = isAttemptingDismissWithCanDismiss ? canDismissMaxStep : 0.9999;\n        /**\n         * If we are blocking the gesture from\n         * dismissing, calculate the spring modifier value\n         * this will be added to the starting breakpoint\n         * value to give the gesture a spring-like feeling.\n         * Note that when isAttemptingDismissWithCanDismiss is true,\n         * the modifier is always added to the breakpoint that\n         * appears right after the 0 breakpoint.\n         *\n         * Note that this modifier is essentially the progression\n         * between secondToLastBreakpoint and maxStep which is\n         * why we subtract secondToLastBreakpoint. This lets us get\n         * the result as a value from 0 to 1.\n         */\n        const processedStep = isAttemptingDismissWithCanDismiss && secondToLastBreakpoint !== undefined\n            ? secondToLastBreakpoint +\n                calculateSpringStep((step - secondToLastBreakpoint) / (maxStep - secondToLastBreakpoint))\n            : step;\n        offset = clamp(0.0001, processedStep, maxStep);\n        animation.progressStep(offset);\n    };\n    const onEnd = (detail) => {\n        /**\n         * When the gesture releases, we need to determine\n         * the closest breakpoint to snap to.\n         */\n        const velocity = detail.velocityY;\n        const threshold = (detail.deltaY + velocity * 350) / height;\n        const diff = currentBreakpoint - threshold;\n        const closest = breakpoints.reduce((a, b) => {\n            return Math.abs(b - diff) < Math.abs(a - diff) ? b : a;\n        });\n        moveSheetToBreakpoint({\n            breakpoint: closest,\n            breakpointOffset: offset,\n            canDismiss: canDismissBlocksGesture,\n            /**\n             * The swipe is user-driven, so we should\n             * always animate when the gesture ends.\n             */\n            animated: true,\n        });\n    };\n    const moveSheetToBreakpoint = (options) => {\n        const { breakpoint, canDismiss, breakpointOffset, animated } = options;\n        /**\n         * canDismiss should only prevent snapping\n         * when users are trying to dismiss. If canDismiss\n         * is present but the user is trying to swipe upwards,\n         * we should allow that to happen,\n         */\n        const shouldPreventDismiss = canDismiss && breakpoint === 0;\n        const snapToBreakpoint = shouldPreventDismiss ? currentBreakpoint : breakpoint;\n        const shouldRemainOpen = snapToBreakpoint !== 0;\n        currentBreakpoint = 0;\n        /**\n         * Update the animation so that it plays from\n         * the last offset to the closest snap point.\n         */\n        if (wrapperAnimation && backdropAnimation) {\n            wrapperAnimation.keyframes([\n                { offset: 0, transform: `translateY(${breakpointOffset * 100}%)` },\n                { offset: 1, transform: `translateY(${(1 - snapToBreakpoint) * 100}%)` },\n            ]);\n            backdropAnimation.keyframes([\n                {\n                    offset: 0,\n                    opacity: `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(1 - breakpointOffset, backdropBreakpoint)})`,\n                },\n                {\n                    offset: 1,\n                    opacity: `calc(var(--backdrop-opacity) * ${getBackdropValueForSheet(snapToBreakpoint, backdropBreakpoint)})`,\n                },\n            ]);\n            animation.progressStep(0);\n        }\n        /**\n         * Gesture should remain disabled until the\n         * snapping animation completes.\n         */\n        gesture.enable(false);\n        if (shouldPreventDismiss) {\n            handleCanDismiss(baseEl, animation);\n        }\n        else if (!shouldRemainOpen) {\n            onDismiss();\n        }\n        /**\n         * If the sheet is going to be fully expanded then we should enable\n         * scrolling immediately. The sheet modal animation takes ~500ms to finish\n         * so if we wait until then there is a visible delay for when scrolling is\n         * re-enabled. Native iOS allows for scrolling on the sheet modal as soon\n         * as the gesture is released, so we align with that.\n         */\n        if (contentEl && snapToBreakpoint === breakpoints[breakpoints.length - 1]) {\n            contentEl.scrollY = true;\n        }\n        return new Promise((resolve) => {\n            animation\n                .onFinish(() => {\n                if (shouldRemainOpen) {\n                    /**\n                     * Once the snapping animation completes,\n                     * we need to reset the animation to go\n                     * from 0 to 1 so users can swipe in any direction.\n                     * We then set the animation offset to the current\n                     * breakpoint so that it starts at the snapped position.\n                     */\n                    if (wrapperAnimation && backdropAnimation) {\n                        raf(() => {\n                            wrapperAnimation.keyframes([...SheetDefaults.WRAPPER_KEYFRAMES]);\n                            backdropAnimation.keyframes([...SheetDefaults.BACKDROP_KEYFRAMES]);\n                            animation.progressStart(true, 1 - snapToBreakpoint);\n                            currentBreakpoint = snapToBreakpoint;\n                            onBreakpointChange(currentBreakpoint);\n                            /**\n                             * Backdrop should become enabled\n                             * after the backdropBreakpoint value\n                             */\n                            const shouldEnableBackdrop = currentBreakpoint > backdropBreakpoint;\n                            if (shouldEnableBackdrop) {\n                                enableBackdrop();\n                            }\n                            else {\n                                disableBackdrop();\n                            }\n                            gesture.enable(true);\n                            resolve();\n                        });\n                    }\n                    else {\n                        gesture.enable(true);\n                        resolve();\n                    }\n                }\n                else {\n                    resolve();\n                }\n                /**\n                 * This must be a one time callback\n                 * otherwise a new callback will\n                 * be added every time onEnd runs.\n                 */\n            }, { oneTimeCallback: true })\n                .progressEnd(1, 0, animated ? 500 : 0);\n        });\n    };\n    const gesture = createGesture({\n        el: wrapperEl,\n        gestureName: 'modalSheet',\n        gesturePriority: 40,\n        direction: 'y',\n        threshold: 10,\n        canStart,\n        onStart,\n        onMove,\n        onEnd,\n    });\n    return {\n        gesture,\n        moveSheetToBreakpoint,\n    };\n};\n\nconst modalIosCss = \":host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:\\\"\\\"}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}\";\nconst IonModalIosStyle0 = modalIosCss;\n\nconst modalMdCss = \":host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:\\\"\\\"}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}\";\nconst IonModalMdStyle0 = modalMdCss;\n\nconst Modal = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionModalDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionModalWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionModalWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionModalDidDismiss\", 7);\n        this.ionBreakpointDidChange = createEvent(this, \"ionBreakpointDidChange\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.ionMount = createEvent(this, \"ionMount\", 7);\n        this.lockController = createLockController();\n        this.triggerController = createTriggerController();\n        this.coreDelegate = CoreDelegate();\n        this.isSheetModal = false;\n        this.inheritedAttributes = {};\n        this.inline = false;\n        // Whether or not modal is being dismissed via gesture\n        this.gestureAnimationDismissing = false;\n        this.onHandleClick = () => {\n            const { sheetTransition, handleBehavior } = this;\n            if (handleBehavior !== 'cycle' || sheetTransition !== undefined) {\n                /**\n                 * The sheet modal should not advance to the next breakpoint\n                 * if the handle behavior is not `cycle` or if the handle\n                 * is clicked while the sheet is moving to a breakpoint.\n                 */\n                return;\n            }\n            this.moveToNextBreakpoint();\n        };\n        this.onBackdropTap = () => {\n            const { sheetTransition } = this;\n            if (sheetTransition !== undefined) {\n                /**\n                 * When the handle is double clicked at the largest breakpoint,\n                 * it will start to move to the first breakpoint. While transitioning,\n                 * the backdrop will often receive the second click. We prevent the\n                 * backdrop from dismissing the modal while moving between breakpoints.\n                 */\n                return;\n            }\n            this.dismiss(undefined, BACKDROP);\n        };\n        this.onLifecycle = (modalEvent) => {\n            const el = this.usersElement;\n            const name = LIFECYCLE_MAP[modalEvent.type];\n            if (el && name) {\n                const ev = new CustomEvent(name, {\n                    bubbles: false,\n                    cancelable: false,\n                    detail: modalEvent.detail,\n                });\n                el.dispatchEvent(ev);\n            }\n        };\n        this.presented = false;\n        this.hasController = false;\n        this.overlayIndex = undefined;\n        this.delegate = undefined;\n        this.keyboardClose = true;\n        this.enterAnimation = undefined;\n        this.leaveAnimation = undefined;\n        this.breakpoints = undefined;\n        this.initialBreakpoint = undefined;\n        this.backdropBreakpoint = 0;\n        this.handle = undefined;\n        this.handleBehavior = 'none';\n        this.component = undefined;\n        this.componentProps = undefined;\n        this.cssClass = undefined;\n        this.backdropDismiss = true;\n        this.showBackdrop = true;\n        this.animated = true;\n        this.presentingElement = undefined;\n        this.htmlAttributes = undefined;\n        this.isOpen = false;\n        this.trigger = undefined;\n        this.keepContentsMounted = false;\n        this.focusTrap = true;\n        this.canDismiss = true;\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    triggerChanged() {\n        const { trigger, el, triggerController } = this;\n        if (trigger) {\n            triggerController.addClickListener(el, trigger);\n        }\n    }\n    breakpointsChanged(breakpoints) {\n        if (breakpoints !== undefined) {\n            this.sortedBreakpoints = breakpoints.sort((a, b) => a - b);\n        }\n    }\n    connectedCallback() {\n        const { el } = this;\n        prepareOverlay(el);\n        this.triggerChanged();\n    }\n    disconnectedCallback() {\n        this.triggerController.removeClickListener();\n    }\n    componentWillLoad() {\n        var _a;\n        const { breakpoints, initialBreakpoint, el, htmlAttributes } = this;\n        const isSheetModal = (this.isSheetModal = breakpoints !== undefined && initialBreakpoint !== undefined);\n        const attributesToInherit = ['aria-label', 'role'];\n        this.inheritedAttributes = inheritAttributes(el, attributesToInherit);\n        /**\n         * When using a controller modal you can set attributes\n         * using the htmlAttributes property. Since the above attributes\n         * need to be inherited inside of the modal, we need to look\n         * and see if these attributes are being set via htmlAttributes.\n         *\n         * We could alternatively move this to componentDidLoad to simplify the work\n         * here, but we'd then need to make inheritedAttributes a State variable,\n         * thus causing another render to always happen after the first render.\n         */\n        if (htmlAttributes !== undefined) {\n            attributesToInherit.forEach((attribute) => {\n                const attributeValue = htmlAttributes[attribute];\n                if (attributeValue) {\n                    /**\n                     * If an attribute we need to inherit was\n                     * set using htmlAttributes then add it to\n                     * inheritedAttributes and remove it from htmlAttributes.\n                     * This ensures the attribute is inherited and not\n                     * set on the host.\n                     *\n                     * In this case, if an inherited attribute is set\n                     * on the host element and using htmlAttributes then\n                     * htmlAttributes wins, but that's not a pattern that we recommend.\n                     * The only time you'd need htmlAttributes is when using modalController.\n                     */\n                    this.inheritedAttributes = Object.assign(Object.assign({}, this.inheritedAttributes), { [attribute]: htmlAttributes[attribute] });\n                    delete htmlAttributes[attribute];\n                }\n            });\n        }\n        if (isSheetModal) {\n            this.currentBreakpoint = this.initialBreakpoint;\n        }\n        if (breakpoints !== undefined && initialBreakpoint !== undefined && !breakpoints.includes(initialBreakpoint)) {\n            printIonWarning('Your breakpoints array must include the initialBreakpoint value.');\n        }\n        if (!((_a = this.htmlAttributes) === null || _a === void 0 ? void 0 : _a.id)) {\n            setOverlayId(this.el);\n        }\n    }\n    componentDidLoad() {\n        /**\n         * If modal was rendered with isOpen=\"true\"\n         * then we should open modal immediately.\n         */\n        if (this.isOpen === true) {\n            raf(() => this.present());\n        }\n        this.breakpointsChanged(this.breakpoints);\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.triggerChanged();\n    }\n    /**\n     * Determines whether or not an overlay\n     * is being used inline or via a controller/JS\n     * and returns the correct delegate.\n     * By default, subsequent calls to getDelegate\n     * will use a cached version of the delegate.\n     * This is useful for calling dismiss after\n     * present so that the correct delegate is given.\n     */\n    getDelegate(force = false) {\n        if (this.workingDelegate && !force) {\n            return {\n                delegate: this.workingDelegate,\n                inline: this.inline,\n            };\n        }\n        /**\n         * If using overlay inline\n         * we potentially need to use the coreDelegate\n         * so that this works in vanilla JS apps.\n         * If a developer has presented this component\n         * via a controller, then we can assume\n         * the component is already in the\n         * correct place.\n         */\n        const parentEl = this.el.parentNode;\n        const inline = (this.inline = parentEl !== null && !this.hasController);\n        const delegate = (this.workingDelegate = inline ? this.delegate || this.coreDelegate : this.delegate);\n        return { inline, delegate };\n    }\n    /**\n     * Determines whether or not the\n     * modal is allowed to dismiss based\n     * on the state of the canDismiss prop.\n     */\n    async checkCanDismiss(data, role) {\n        const { canDismiss } = this;\n        if (typeof canDismiss === 'function') {\n            return canDismiss(data, role);\n        }\n        return canDismiss;\n    }\n    /**\n     * Present the modal overlay after it has been created.\n     */\n    async present() {\n        const unlock = await this.lockController.lock();\n        if (this.presented) {\n            unlock();\n            return;\n        }\n        const { presentingElement, el } = this;\n        /**\n         * If the modal is presented multiple times (inline modals), we\n         * need to reset the current breakpoint to the initial breakpoint.\n         */\n        this.currentBreakpoint = this.initialBreakpoint;\n        const { inline, delegate } = this.getDelegate(true);\n        /**\n         * Emit ionMount so JS Frameworks have an opportunity\n         * to add the child component to the DOM. The child\n         * component will be assigned to this.usersElement below.\n         */\n        this.ionMount.emit();\n        this.usersElement = await attachComponent(delegate, el, this.component, ['ion-page'], this.componentProps, inline);\n        /**\n         * When using the lazy loaded build of Stencil, we need to wait\n         * for every Stencil component instance to be ready before presenting\n         * otherwise there can be a flash of unstyled content. With the\n         * custom elements bundle we need to wait for the JS framework\n         * mount the inner contents of the overlay otherwise WebKit may\n         * get the transition incorrect.\n         */\n        if (hasLazyBuild(el)) {\n            await deepReady(this.usersElement);\n            /**\n             * If keepContentsMounted=\"true\" then the\n             * JS Framework has already mounted the inner\n             * contents so there is no need to wait.\n             * Otherwise, we need to wait for the JS\n             * Framework to mount the inner contents\n             * of this component.\n             */\n        }\n        else if (!this.keepContentsMounted) {\n            await waitForMount();\n        }\n        writeTask(() => this.el.classList.add('show-modal'));\n        const hasCardModal = presentingElement !== undefined;\n        /**\n         * We need to change the status bar at the\n         * start of the animation so that it completes\n         * by the time the card animation is done.\n         */\n        if (hasCardModal && getIonMode(this) === 'ios') {\n            // Cache the original status bar color before the modal is presented\n            this.statusBarStyle = await StatusBar.getStyle();\n            setCardStatusBarDark();\n        }\n        await present(this, 'modalEnter', iosEnterAnimation, mdEnterAnimation, {\n            presentingEl: presentingElement,\n            currentBreakpoint: this.initialBreakpoint,\n            backdropBreakpoint: this.backdropBreakpoint,\n        });\n        /* tslint:disable-next-line */\n        if (typeof window !== 'undefined') {\n            /**\n             * This needs to be setup before any\n             * non-transition async work so it can be dereferenced\n             * in the dismiss method. The dismiss method\n             * only waits for the entering transition\n             * to finish. It does not wait for all of the `present`\n             * method to resolve.\n             */\n            this.keyboardOpenCallback = () => {\n                if (this.gesture) {\n                    /**\n                     * When the native keyboard is opened and the webview\n                     * is resized, the gesture implementation will become unresponsive\n                     * and enter a free-scroll mode.\n                     *\n                     * When the keyboard is opened, we disable the gesture for\n                     * a single frame and re-enable once the contents have repositioned\n                     * from the keyboard placement.\n                     */\n                    this.gesture.enable(false);\n                    raf(() => {\n                        if (this.gesture) {\n                            this.gesture.enable(true);\n                        }\n                    });\n                }\n            };\n            window.addEventListener(KEYBOARD_DID_OPEN, this.keyboardOpenCallback);\n        }\n        if (this.isSheetModal) {\n            this.initSheetGesture();\n        }\n        else if (hasCardModal) {\n            this.initSwipeToClose();\n        }\n        unlock();\n    }\n    initSwipeToClose() {\n        var _a;\n        if (getIonMode(this) !== 'ios') {\n            return;\n        }\n        const { el } = this;\n        // All of the elements needed for the swipe gesture\n        // should be in the DOM and referenced by now, except\n        // for the presenting el\n        const animationBuilder = this.leaveAnimation || config.get('modalLeave', iosLeaveAnimation);\n        const ani = (this.animation = animationBuilder(el, { presentingEl: this.presentingElement }));\n        const contentEl = findIonContent(el);\n        if (!contentEl) {\n            printIonContentErrorMsg(el);\n            return;\n        }\n        const statusBarStyle = (_a = this.statusBarStyle) !== null && _a !== void 0 ? _a : Style.Default;\n        this.gesture = createSwipeToCloseGesture(el, ani, statusBarStyle, () => {\n            /**\n             * While the gesture animation is finishing\n             * it is possible for a user to tap the backdrop.\n             * This would result in the dismiss animation\n             * being played again. Typically this is avoided\n             * by setting `presented = false` on the overlay\n             * component; however, we cannot do that here as\n             * that would prevent the element from being\n             * removed from the DOM.\n             */\n            this.gestureAnimationDismissing = true;\n            /**\n             * Reset the status bar style as the dismiss animation\n             * starts otherwise the status bar will be the wrong\n             * color for the duration of the dismiss animation.\n             * The dismiss method does this as well, but\n             * in this case it's only called once the animation\n             * has finished.\n             */\n            setCardStatusBarDefault(this.statusBarStyle);\n            this.animation.onFinish(async () => {\n                await this.dismiss(undefined, GESTURE);\n                this.gestureAnimationDismissing = false;\n            });\n        });\n        this.gesture.enable(true);\n    }\n    initSheetGesture() {\n        const { wrapperEl, initialBreakpoint, backdropBreakpoint } = this;\n        if (!wrapperEl || initialBreakpoint === undefined) {\n            return;\n        }\n        const animationBuilder = this.enterAnimation || config.get('modalEnter', iosEnterAnimation);\n        const ani = (this.animation = animationBuilder(this.el, {\n            presentingEl: this.presentingElement,\n            currentBreakpoint: initialBreakpoint,\n            backdropBreakpoint,\n        }));\n        ani.progressStart(true, 1);\n        const { gesture, moveSheetToBreakpoint } = createSheetGesture(this.el, this.backdropEl, wrapperEl, initialBreakpoint, backdropBreakpoint, ani, this.sortedBreakpoints, () => { var _a; return (_a = this.currentBreakpoint) !== null && _a !== void 0 ? _a : 0; }, () => this.sheetOnDismiss(), (breakpoint) => {\n            if (this.currentBreakpoint !== breakpoint) {\n                this.currentBreakpoint = breakpoint;\n                this.ionBreakpointDidChange.emit({ breakpoint });\n            }\n        });\n        this.gesture = gesture;\n        this.moveSheetToBreakpoint = moveSheetToBreakpoint;\n        this.gesture.enable(true);\n    }\n    sheetOnDismiss() {\n        /**\n         * While the gesture animation is finishing\n         * it is possible for a user to tap the backdrop.\n         * This would result in the dismiss animation\n         * being played again. Typically this is avoided\n         * by setting `presented = false` on the overlay\n         * component; however, we cannot do that here as\n         * that would prevent the element from being\n         * removed from the DOM.\n         */\n        this.gestureAnimationDismissing = true;\n        this.animation.onFinish(async () => {\n            this.currentBreakpoint = 0;\n            this.ionBreakpointDidChange.emit({ breakpoint: this.currentBreakpoint });\n            await this.dismiss(undefined, GESTURE);\n            this.gestureAnimationDismissing = false;\n        });\n    }\n    /**\n     * Dismiss the modal overlay after it has been presented.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the modal. For example, 'cancel' or 'backdrop'.\n     *\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     */\n    async dismiss(data, role) {\n        var _a;\n        if (this.gestureAnimationDismissing && role !== GESTURE) {\n            return false;\n        }\n        /**\n         * Because the canDismiss check below is async,\n         * we need to claim a lock before the check happens,\n         * in case the dismiss transition does run.\n         */\n        const unlock = await this.lockController.lock();\n        /**\n         * If a canDismiss handler is responsible\n         * for calling the dismiss method, we should\n         * not run the canDismiss check again.\n         */\n        if (role !== 'handler' && !(await this.checkCanDismiss(data, role))) {\n            unlock();\n            return false;\n        }\n        const { presentingElement } = this;\n        /**\n         * We need to start the status bar change\n         * before the animation so that the change\n         * finishes when the dismiss animation does.\n         */\n        const hasCardModal = presentingElement !== undefined;\n        if (hasCardModal && getIonMode(this) === 'ios') {\n            setCardStatusBarDefault(this.statusBarStyle);\n        }\n        /* tslint:disable-next-line */\n        if (typeof window !== 'undefined' && this.keyboardOpenCallback) {\n            window.removeEventListener(KEYBOARD_DID_OPEN, this.keyboardOpenCallback);\n            this.keyboardOpenCallback = undefined;\n        }\n        const dismissed = await dismiss(this, data, role, 'modalLeave', iosLeaveAnimation, mdLeaveAnimation, {\n            presentingEl: presentingElement,\n            currentBreakpoint: (_a = this.currentBreakpoint) !== null && _a !== void 0 ? _a : this.initialBreakpoint,\n            backdropBreakpoint: this.backdropBreakpoint,\n        });\n        if (dismissed) {\n            const { delegate } = this.getDelegate();\n            await detachComponent(delegate, this.usersElement);\n            writeTask(() => this.el.classList.remove('show-modal'));\n            if (this.animation) {\n                this.animation.destroy();\n            }\n            if (this.gesture) {\n                this.gesture.destroy();\n            }\n        }\n        this.currentBreakpoint = undefined;\n        this.animation = undefined;\n        unlock();\n        return dismissed;\n    }\n    /**\n     * Returns a promise that resolves when the modal did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionModalDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the modal will dismiss.\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionModalWillDismiss');\n    }\n    /**\n     * Move a sheet style modal to a specific breakpoint. The breakpoint value must\n     * be a value defined in your `breakpoints` array.\n     */\n    async setCurrentBreakpoint(breakpoint) {\n        if (!this.isSheetModal) {\n            printIonWarning('setCurrentBreakpoint is only supported on sheet modals.');\n            return;\n        }\n        if (!this.breakpoints.includes(breakpoint)) {\n            printIonWarning(`Attempted to set invalid breakpoint value ${breakpoint}. Please double check that the breakpoint value is part of your defined breakpoints.`);\n            return;\n        }\n        const { currentBreakpoint, moveSheetToBreakpoint, canDismiss, breakpoints, animated } = this;\n        if (currentBreakpoint === breakpoint) {\n            return;\n        }\n        if (moveSheetToBreakpoint) {\n            this.sheetTransition = moveSheetToBreakpoint({\n                breakpoint,\n                breakpointOffset: 1 - currentBreakpoint,\n                canDismiss: canDismiss !== undefined && canDismiss !== true && breakpoints[0] === 0,\n                animated,\n            });\n            await this.sheetTransition;\n            this.sheetTransition = undefined;\n        }\n    }\n    /**\n     * Returns the current breakpoint of a sheet style modal\n     */\n    async getCurrentBreakpoint() {\n        return this.currentBreakpoint;\n    }\n    async moveToNextBreakpoint() {\n        const { breakpoints, currentBreakpoint } = this;\n        if (!breakpoints || currentBreakpoint == null) {\n            /**\n             * If the modal does not have breakpoints and/or the current\n             * breakpoint is not set, we can't move to the next breakpoint.\n             */\n            return false;\n        }\n        const allowedBreakpoints = breakpoints.filter((b) => b !== 0);\n        const currentBreakpointIndex = allowedBreakpoints.indexOf(currentBreakpoint);\n        const nextBreakpointIndex = (currentBreakpointIndex + 1) % allowedBreakpoints.length;\n        const nextBreakpoint = allowedBreakpoints[nextBreakpointIndex];\n        /**\n         * Sets the current breakpoint to the next available breakpoint.\n         * If the current breakpoint is the last breakpoint, we set the current\n         * breakpoint to the first non-zero breakpoint to avoid dismissing the sheet.\n         */\n        await this.setCurrentBreakpoint(nextBreakpoint);\n        return true;\n    }\n    render() {\n        const { handle, isSheetModal, presentingElement, htmlAttributes, handleBehavior, inheritedAttributes, focusTrap } = this;\n        const showHandle = handle !== false && isSheetModal;\n        const mode = getIonMode(this);\n        const isCardModal = presentingElement !== undefined && mode === 'ios';\n        const isHandleCycle = handleBehavior === 'cycle';\n        return (h(Host, Object.assign({ key: 'b4da5111fe4719fa450c39b2d4bd884a302a7924', \"no-router\": true, tabindex: \"-1\" }, htmlAttributes, { style: {\n                zIndex: `${20000 + this.overlayIndex}`,\n            }, class: Object.assign({ [mode]: true, ['modal-default']: !isCardModal && !isSheetModal, [`modal-card`]: isCardModal, [`modal-sheet`]: isSheetModal, 'overlay-hidden': true, [FOCUS_TRAP_DISABLE_CLASS]: focusTrap === false }, getClassMap(this.cssClass)), onIonBackdropTap: this.onBackdropTap, onIonModalDidPresent: this.onLifecycle, onIonModalWillPresent: this.onLifecycle, onIonModalWillDismiss: this.onLifecycle, onIonModalDidDismiss: this.onLifecycle }), h(\"ion-backdrop\", { key: 'c12dbf747e0eb914eaf1331798548ffc7e147763', ref: (el) => (this.backdropEl = el), visible: this.showBackdrop, tappable: this.backdropDismiss, part: \"backdrop\" }), mode === 'ios' && h(\"div\", { key: 'da546ee80c6576b5acc66e959fd5009e0b9a8160', class: \"modal-shadow\" }), h(\"div\", Object.assign({ key: '306ebe6427440ad5f7ed36d590e562d15a503b75',\n            /*\n              role and aria-modal must be used on the\n              same element. They must also be set inside the\n              shadow DOM otherwise ion-button will not be highlighted\n              when using VoiceOver: https://bugs.webkit.org/show_bug.cgi?id=247134\n            */\n            role: \"dialog\" }, inheritedAttributes, { \"aria-modal\": \"true\", class: \"modal-wrapper ion-overlay-wrapper\", part: \"content\", ref: (el) => (this.wrapperEl = el) }), showHandle && (h(\"button\", { key: 'c5d17e346fe255a7c0cacbbf15c0083f2d09c488', class: \"modal-handle\",\n            // Prevents the handle from receiving keyboard focus when it does not cycle\n            tabIndex: !isHandleCycle ? -1 : 0, \"aria-label\": \"Activate to adjust the size of the dialog overlaying the screen\", onClick: isHandleCycle ? this.onHandleClick : undefined, part: \"handle\" })), h(\"slot\", { key: '5cc714170a00b67f3eda0cd1d6f37c1489a99c83' }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"isOpen\": [\"onIsOpenChange\"],\n        \"trigger\": [\"triggerChanged\"]\n    }; }\n};\nconst LIFECYCLE_MAP = {\n    ionModalDidPresent: 'ionViewDidEnter',\n    ionModalWillPresent: 'ionViewWillEnter',\n    ionModalWillDismiss: 'ionViewWillLeave',\n    ionModalDidDismiss: 'ionViewDidLeave',\n};\nModal.style = {\n    ios: IonModalIosStyle0,\n    md: IonModalMdStyle0\n};\n\nexport { Modal as ion_modal };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC5H,SAASH,CAAC,IAAII,qBAAqB,EAAEF,CAAC,IAAIG,YAAY,EAAEV,CAAC,IAAIW,qBAAqB,EAAEb,CAAC,IAAIc,mBAAmB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,uBAAuB,QAAQ,qBAAqB;AAC5L,SAASC,CAAC,IAAIC,YAAY,EAAEL,CAAC,IAAIM,eAAe,EAAEnB,CAAC,IAAIoB,eAAe,QAAQ,kCAAkC;AAChH,SAASC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,KAAK,EAAE1B,CAAC,IAAI2B,GAAG,EAAErB,CAAC,IAAIsB,iBAAiB,EAAEC,CAAC,IAAIC,YAAY,QAAQ,uBAAuB;AAC5H,SAASC,CAAC,IAAIC,oBAAoB,QAAQ,+BAA+B;AACzE,SAASf,CAAC,IAAIgB,eAAe,QAAQ,qBAAqB;AAC1D,SAASV,CAAC,IAAIW,YAAY,QAAQ,yBAAyB;AAC3D,SAASC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,QAAQ,EAAEnB,CAAC,IAAIoB,cAAc,EAAEhB,CAAC,IAAIiB,YAAY,EAAEvC,CAAC,IAAIwC,OAAO,EAAExB,CAAC,IAAIyB,OAAO,EAAE1C,CAAC,IAAI2C,WAAW,QAAQ,wBAAwB;AACtP,SAAS1B,CAAC,IAAI2B,WAAW,QAAQ,qBAAqB;AACtD,SAAST,CAAC,IAAIU,SAAS,EAAE/C,CAAC,IAAIgD,YAAY,QAAQ,qBAAqB;AACvE,SAASC,CAAC,IAAIC,UAAU,EAAEvB,CAAC,IAAIwB,MAAM,QAAQ,4BAA4B;AACzE,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASzB,CAAC,IAAI0B,eAAe,QAAQ,yBAAyB;AAC9D,SAASlC,CAAC,IAAImC,uBAAuB,QAAQ,4BAA4B;AACzE,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASvD,CAAC,IAAIwD,GAAG,QAAQ,qBAAqB;AAC9C,OAAO,oCAAoC;AAC3C,OAAO,kCAAkC;AACzC,OAAO,wBAAwB;AAE/B,IAAIC,KAAK;AACT,CAAC,UAAUA,KAAK,EAAE;EACdA,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM;EACtBA,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO;EACxBA,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS;AAChC,CAAC,EAAEA,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB,MAAMC,SAAS,GAAG;EACdC,SAASA,CAAA,EAAG;IACR,MAAMC,SAAS,GAAG9B,YAAY,CAAC,CAAC;IAChC,IAAI8B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,iBAAiB,CAAC,WAAW,CAAC,EAAE;MAChG,OAAOD,SAAS,CAACE,OAAO,CAACJ,SAAS;IACtC;IACA,OAAOK,SAAS;EACpB,CAAC;EACDC,QAAQA,CAACC,OAAO,EAAE;IACd,MAAMC,MAAM,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACO,MAAM,EAAE;MACT;IACJ;IACAA,MAAM,CAACF,QAAQ,CAACC,OAAO,CAAC;EAC5B,CAAC;EACDE,QAAQ;IAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAE,aAAkB;MACxB,MAAMH,MAAM,GAAG,IAAI,CAACP,SAAS,CAAC,CAAC;MAC/B,IAAI,CAACO,MAAM,EAAE;QACT,OAAOT,KAAK,CAACa,OAAO;MACxB;MACA,MAAM;QAAEC;MAAM,CAAC,SAASL,MAAM,CAACM,OAAO,CAAC,CAAC;MACxC,OAAOD,KAAK;IAChB,CAAC;IAAA,gBAPDJ,QAAQA,CAAA;MAAA,OAAAC,IAAA,CAAAK,KAAA,OAAAC,SAAA;IAAA;EAAA;AAQZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAGA,CAACC,CAAC,EAAEC,kBAAkB,KAAK;EACxD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIA,kBAAkB,KAAK,CAAC,EAAE;IAC1B,OAAO,CAAC;EACZ;EACA,MAAMC,KAAK,GAAG,CAAC,IAAI,CAAC,GAAGD,kBAAkB,CAAC;EAC1C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAM5B,CAAC,GAAG,EAAE4B,kBAAkB,GAAGC,KAAK,CAAC;EACvC;AACJ;AACA;AACA;AACA;EACI,OAAOF,CAAC,GAAGE,KAAK,GAAG7B,CAAC;AACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8B,oBAAoB,GAAGA,CAAA,KAAM;EAC/B,IAAI,CAACvB,GAAG,IAAIA,GAAG,CAACwB,UAAU,IAAI,GAAG,EAAE;IAC/B;EACJ;EACAtB,SAAS,CAACM,QAAQ,CAAC;IAAEO,KAAK,EAAEd,KAAK,CAACwB;EAAK,CAAC,CAAC;AAC7C,CAAC;AACD,MAAMC,uBAAuB,GAAGA,CAACC,YAAY,GAAG1B,KAAK,CAACa,OAAO,KAAK;EAC9D,IAAI,CAACd,GAAG,IAAIA,GAAG,CAACwB,UAAU,IAAI,GAAG,EAAE;IAC/B;EACJ;EACAtB,SAAS,CAACM,QAAQ,CAAC;IAAEO,KAAK,EAAEY;EAAa,CAAC,CAAC;AAC/C,CAAC;AAED,MAAMC,gBAAgB;EAAA,IAAAC,KAAA,GAAAhB,iBAAA,CAAG,WAAOiB,EAAE,EAAEC,SAAS,EAAK;IAC9C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,OAAOD,EAAE,CAACE,UAAU,KAAK,UAAU,EAAE;MACrC;IACJ;IACA;AACJ;AACA;AACA;AACA;IACI,MAAMC,aAAa,SAASH,EAAE,CAACE,UAAU,CAACzB,SAAS,EAAE/B,OAAO,CAAC;IAC7D,IAAI,CAACyD,aAAa,EAAE;MAChB;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIF,SAAS,CAACG,SAAS,CAAC,CAAC,EAAE;MACvBH,SAAS,CAACI,QAAQ,CAAC,MAAM;QACrBL,EAAE,CAAC1C,OAAO,CAACmB,SAAS,EAAE,SAAS,CAAC;MACpC,CAAC,EAAE;QAAE6B,eAAe,EAAE;MAAK,CAAC,CAAC;IACjC,CAAC,MACI;MACDN,EAAE,CAAC1C,OAAO,CAACmB,SAAS,EAAE,SAAS,CAAC;IACpC;EACJ,CAAC;EAAA,gBAvCKqB,gBAAgBA,CAAAS,EAAA,EAAAC,GAAA;IAAA,OAAAT,KAAA,CAAAZ,KAAA,OAAAC,SAAA;EAAA;AAAA,GAuCrB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,mBAAmB,GAAIC,CAAC,IAAK;EAC/B,OAAO,UAAU,GAAG,OAAO,KAAK,CAAC,OAAO,GAAGA,CAAC,CAAC,GAAG,OAAO,GAAG,OAAO,KAAK,CAAC,SAAS,GAAGA,CAAC,CAAC,GAAG,CAAC;AAC7F,CAAC;;AAED;AACA,MAAMC,oBAAoB,GAAG;EACzBC,oBAAoB,EAAE;AAC1B,CAAC;AACD,MAAMC,yBAAyB,GAAGA,CAACb,EAAE,EAAEC,SAAS,EAAEa,cAAc,EAAEC,SAAS,KAAK;EAC5E;AACJ;AACA;AACA;EACI,MAAMC,iBAAiB,GAAG,GAAG;EAC7B,MAAMC,MAAM,GAAGjB,EAAE,CAACkB,YAAY;EAC9B,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,uBAAuB,GAAG,KAAK;EACnC,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIC,QAAQ,GAAG,IAAI;EACnB,MAAMC,iBAAiB,GAAG,GAAG;EAC7B,IAAIC,cAAc,GAAG,IAAI;EACzB,IAAIC,QAAQ,GAAG,CAAC;EAChB,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIL,SAAS,IAAInG,YAAY,CAACmG,SAAS,CAAC,EAAE;MACtC,OAAOA,SAAS,CAACM,OAAO;MACxB;AACZ;AACA;AACA;AACA;IACQ,CAAC,MACI;MACD,OAAO,IAAI;IACf;EACJ,CAAC;EACD,MAAMC,QAAQ,GAAIC,MAAM,IAAK;IACzB,MAAMC,MAAM,GAAGD,MAAM,CAACE,KAAK,CAACD,MAAM;IAClC,IAAIA,MAAM,KAAK,IAAI,IAAI,CAACA,MAAM,CAACE,OAAO,EAAE;MACpC,OAAO,IAAI;IACf;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQX,SAAS,GAAGpG,qBAAqB,CAAC6G,MAAM,CAAC;IACzC,IAAIT,SAAS,EAAE;MACX;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAInG,YAAY,CAACmG,SAAS,CAAC,EAAE;QACzB,MAAMY,IAAI,GAAGnG,cAAc,CAACuF,SAAS,CAAC;QACtCC,QAAQ,GAAGW,IAAI,CAACC,aAAa,CAAC,eAAe,CAAC;MAClD,CAAC,MACI;QACDZ,QAAQ,GAAGD,SAAS;MACxB;MACA,MAAMc,qBAAqB,GAAG,CAAC,CAACd,SAAS,CAACa,aAAa,CAAC,eAAe,CAAC;MACxE,OAAO,CAACC,qBAAqB,IAAIb,QAAQ,CAACc,SAAS,KAAK,CAAC;IAC7D;IACA;AACR;AACA;AACA;IACQ,MAAMC,MAAM,GAAGP,MAAM,CAACE,OAAO,CAAC,YAAY,CAAC;IAC3C,IAAIK,MAAM,KAAK,IAAI,EAAE;MACjB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB,CAAC;EACD,MAAMC,OAAO,GAAIT,MAAM,IAAK;IACxB,MAAM;MAAEU;IAAO,CAAC,GAAGV,MAAM;IACzB;AACR;AACA;AACA;AACA;IACQL,cAAc,GAAGE,UAAU,CAAC,CAAC;IAC7B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQN,uBAAuB,GAAGpB,EAAE,CAACE,UAAU,KAAKzB,SAAS,IAAIuB,EAAE,CAACE,UAAU,KAAK,IAAI;IAC/E;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIqC,MAAM,GAAG,CAAC,IAAIlB,SAAS,EAAE;MACzBlG,qBAAqB,CAACkG,SAAS,CAAC;IACpC;IACApB,SAAS,CAACuC,aAAa,CAAC,IAAI,EAAErB,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;EACjD,CAAC;EACD,MAAMsB,MAAM,GAAIZ,MAAM,IAAK;IACvB,MAAM;MAAEU;IAAO,CAAC,GAAGV,MAAM;IACzB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIU,MAAM,GAAG,CAAC,IAAIlB,SAAS,EAAE;MACzBlG,qBAAqB,CAACkG,SAAS,CAAC;IACpC;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMqB,IAAI,GAAGb,MAAM,CAACU,MAAM,GAAGtB,MAAM;IACnC;AACR;AACA;AACA;AACA;AACA;IACQ,MAAM0B,iCAAiC,GAAGD,IAAI,IAAI,CAAC,IAAItB,uBAAuB;IAC9E;AACR;AACA;AACA;AACA;IACQ,MAAMwB,OAAO,GAAGD,iCAAiC,GAAGpB,iBAAiB,GAAG,MAAM;IAC9E;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMsB,aAAa,GAAGF,iCAAiC,GAAGlC,mBAAmB,CAACiC,IAAI,GAAGE,OAAO,CAAC,GAAGF,IAAI;IACpG,MAAMI,WAAW,GAAG9G,KAAK,CAAC,MAAM,EAAE6G,aAAa,EAAED,OAAO,CAAC;IACzD3C,SAAS,CAAC8C,YAAY,CAACD,WAAW,CAAC;IACnC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIA,WAAW,IAAI9B,iBAAiB,IAAIS,QAAQ,GAAGT,iBAAiB,EAAE;MAClEpB,uBAAuB,CAACkB,cAAc,CAAC;MACvC;AACZ;AACA;AACA;AACA;IACQ,CAAC,MACI,IAAIgC,WAAW,GAAG9B,iBAAiB,IAAIS,QAAQ,IAAIT,iBAAiB,EAAE;MACvEvB,oBAAoB,CAAC,CAAC;IAC1B;IACAgC,QAAQ,GAAGqB,WAAW;EAC1B,CAAC;EACD,MAAME,KAAK,GAAInB,MAAM,IAAK;IACtB,MAAMoB,QAAQ,GAAGpB,MAAM,CAACqB,SAAS;IACjC,MAAMR,IAAI,GAAGb,MAAM,CAACU,MAAM,GAAGtB,MAAM;IACnC,MAAM0B,iCAAiC,GAAGD,IAAI,IAAI,CAAC,IAAItB,uBAAuB;IAC9E,MAAMwB,OAAO,GAAGD,iCAAiC,GAAGpB,iBAAiB,GAAG,MAAM;IAC9E,MAAMsB,aAAa,GAAGF,iCAAiC,GAAGlC,mBAAmB,CAACiC,IAAI,GAAGE,OAAO,CAAC,GAAGF,IAAI;IACpG,MAAMI,WAAW,GAAG9G,KAAK,CAAC,MAAM,EAAE6G,aAAa,EAAED,OAAO,CAAC;IACzD,MAAMO,SAAS,GAAG,CAACtB,MAAM,CAACU,MAAM,GAAGU,QAAQ,GAAG,IAAI,IAAIhC,MAAM;IAC5D;AACR;AACA;AACA;AACA;AACA;IACQ,MAAMmC,cAAc,GAAG,CAACT,iCAAiC,IAAIQ,SAAS,IAAInC,iBAAiB;IAC3F,IAAIqC,YAAY,GAAGD,cAAc,GAAG,CAAC,KAAK,GAAG,KAAK;IAClD,IAAI,CAACA,cAAc,EAAE;MACjBnD,SAAS,CAACqD,MAAM,CAAC,gCAAgC,CAAC;MAClDD,YAAY,IAAIrF,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE8E,WAAW,CAAC,CAAC,CAAC,CAAC;IACjG,CAAC,MACI;MACD7C,SAAS,CAACqD,MAAM,CAAC,gCAAgC,CAAC;MAClDD,YAAY,IAAIrF,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE8E,WAAW,CAAC,CAAC,CAAC,CAAC;IACjG;IACA,MAAMS,QAAQ,GAAGH,cAAc,GACzBI,eAAe,CAACd,IAAI,GAAGzB,MAAM,EAAEgC,QAAQ,CAAC,GACxCO,eAAe,CAAC,CAAC,CAAC,GAAGV,WAAW,IAAI7B,MAAM,EAAEgC,QAAQ,CAAC;IAC3D9B,MAAM,GAAGiC,cAAc;IACvBK,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC;IACrB,IAAIrC,SAAS,EAAE;MACXjG,mBAAmB,CAACiG,SAAS,EAAEG,cAAc,CAAC;IAClD;IACAvB,SAAS,CACJI,QAAQ,CAAC,MAAM;MAChB,IAAI,CAAC+C,cAAc,EAAE;QACjBK,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;MACxB;IACJ,CAAC,CAAC,CACGC,WAAW,CAACP,cAAc,GAAG,CAAC,GAAG,CAAC,EAAEC,YAAY,EAAEE,QAAQ,CAAC;IAChE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIZ,iCAAiC,IAAIG,WAAW,GAAGF,OAAO,GAAG,CAAC,EAAE;MAChE9C,gBAAgB,CAACE,EAAE,EAAEC,SAAS,CAAC;IACnC,CAAC,MACI,IAAImD,cAAc,EAAE;MACrBrC,SAAS,CAAC,CAAC;IACf;EACJ,CAAC;EACD,MAAM0C,OAAO,GAAGxF,aAAa,CAAC;IAC1B+B,EAAE;IACF4D,WAAW,EAAE,mBAAmB;IAChCC,eAAe,EAAEjH,wBAAwB;IACzCkH,SAAS,EAAE,GAAG;IACdX,SAAS,EAAE,EAAE;IACbvB,QAAQ;IACRU,OAAO;IACPG,MAAM;IACNO;EACJ,CAAC,CAAC;EACF,OAAOS,OAAO;AAClB,CAAC;AACD,MAAMD,eAAe,GAAGA,CAACO,SAAS,EAAEd,QAAQ,KAAK;EAC7C,OAAOjH,KAAK,CAAC,GAAG,EAAE+H,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAChB,QAAQ,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC;AAChE,CAAC;AAED,MAAMiB,yBAAyB,GAAIC,IAAI,IAAK;EACxC,MAAM;IAAEC,iBAAiB;IAAE7E;EAAmB,CAAC,GAAG4E,IAAI;EACtD;AACJ;AACA;AACA;AACA;EACI,MAAME,kBAAkB,GAAG9E,kBAAkB,KAAKd,SAAS,IAAIc,kBAAkB,GAAG6E,iBAAiB;EACrG,MAAME,eAAe,GAAGD,kBAAkB,GAAG,kCAAkCD,iBAAiB,GAAG,GAAG,GAAG;EACzG,MAAMG,iBAAiB,GAAGxG,eAAe,CAAC,mBAAmB,CAAC,CAACyG,MAAM,CAAC,SAAS,EAAE,CAAC,EAAEF,eAAe,CAAC;EACpG,IAAID,kBAAkB,EAAE;IACpBE,iBAAiB,CACZE,YAAY,CAAC;MACd,gBAAgB,EAAE;IACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC7C;EACA,MAAMC,gBAAgB,GAAG5G,eAAe,CAAC,kBAAkB,CAAC,CAAC6G,SAAS,CAAC,CACnE;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAmB,CAAC,EACxD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE,cAAc,GAAG,GAAGX,iBAAiB,GAAG,GAAG;EAAK,CAAC,CACxF,CAAC;EACF,OAAO;IAAEO,gBAAgB;IAAEJ;EAAkB,CAAC;AAClD,CAAC;AACD,MAAMS,yBAAyB,GAAIb,IAAI,IAAK;EACxC,MAAM;IAAEC,iBAAiB;IAAE7E;EAAmB,CAAC,GAAG4E,IAAI;EACtD;AACJ;AACA;AACA;AACA;EACI,MAAMc,aAAa,GAAG,kCAAkC5F,wBAAwB,CAAC+E,iBAAiB,EAAE7E,kBAAkB,CAAC,GAAG;EAC1H,MAAM2F,eAAe,GAAG,CACpB;IAAEL,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAEG;EAAc,CAAC,EACrC;IAAEJ,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAE,CAAC,CAC5B;EACD,MAAMK,cAAc,GAAG,CACnB;IAAEN,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAEG;EAAc,CAAC,EACrC;IAAEJ,MAAM,EAAEtF,kBAAkB;IAAEuF,OAAO,EAAE;EAAE,CAAC,EAC1C;IAAED,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAE,CAAC,CAC5B;EACD,MAAMP,iBAAiB,GAAGxG,eAAe,CAAC,mBAAmB,CAAC,CAAC6G,SAAS,CAACrF,kBAAkB,KAAK,CAAC,GAAG4F,cAAc,GAAGD,eAAe,CAAC;EACrI,MAAMP,gBAAgB,GAAG5G,eAAe,CAAC,kBAAkB,CAAC,CAAC6G,SAAS,CAAC,CACnE;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE,cAAc,GAAG,GAAGX,iBAAiB,GAAG,GAAG;EAAK,CAAC,EACrF;IAAES,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAmB,CAAC,CAC3D,CAAC;EACF,OAAO;IAAEJ,gBAAgB;IAAEJ;EAAkB,CAAC;AAClD,CAAC;AAED,MAAMa,sBAAsB,GAAGA,CAAA,KAAM;EACjC,MAAMb,iBAAiB,GAAGxG,eAAe,CAAC,CAAC,CACtCyG,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzC,MAAMC,gBAAgB,GAAG5G,eAAe,CAAC,CAAC,CAACyG,MAAM,CAAC,WAAW,EAAE,mBAAmB,EAAE,iBAAiB,CAAC;EACtG,OAAO;IAAED,iBAAiB;IAAEI;EAAiB,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA,MAAMU,iBAAiB,GAAGA,CAACC,MAAM,EAAEnB,IAAI,KAAK;EACxC,MAAM;IAAEoB,YAAY;IAAEnB;EAAkB,CAAC,GAAGD,IAAI;EAChD,MAAMlC,IAAI,GAAGnG,cAAc,CAACwJ,MAAM,CAAC;EACnC,MAAM;IAAEX,gBAAgB;IAAEJ;EAAkB,CAAC,GAAGH,iBAAiB,KAAK3F,SAAS,GAAGyF,yBAAyB,CAACC,IAAI,CAAC,GAAGiB,sBAAsB,CAAC,CAAC;EAC5Ib,iBAAiB,CAACiB,UAAU,CAACvD,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC,CAAC;EAChEyC,gBAAgB,CAACa,UAAU,CAACvD,IAAI,CAACwD,gBAAgB,CAAC,+BAA+B,CAAC,CAAC,CAAChB,YAAY,CAAC;IAAEK,OAAO,EAAE;EAAE,CAAC,CAAC;EAChH,MAAMY,aAAa,GAAG3H,eAAe,CAAC,eAAe,CAAC,CACjDyH,UAAU,CAACF,MAAM,CAAC,CAClBhC,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAAC,GAAG,CAAC,CACboC,YAAY,CAAChB,gBAAgB,CAAC;EACnC,IAAIY,YAAY,EAAE;IACd,MAAMK,QAAQ,GAAGC,MAAM,CAACnG,UAAU,GAAG,GAAG;IACxC,MAAMoG,YAAY,GAAGP,YAAY,CAACQ,OAAO,KAAK,WAAW,IAAIR,YAAY,CAACS,iBAAiB,KAAKvH,SAAS;IACzG,MAAMwH,gBAAgB,GAAGnK,cAAc,CAACyJ,YAAY,CAAC;IACrD,MAAMW,mBAAmB,GAAGnI,eAAe,CAAC,CAAC,CAAC0G,YAAY,CAAC;MACvDM,SAAS,EAAE,eAAe;MAC1B,kBAAkB,EAAE,YAAY;MAChCoB,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,MAAMC,MAAM,GAAGC,QAAQ,CAACC,IAAI;IAC5B,IAAIV,QAAQ,EAAE;MACV;AACZ;AACA;AACA;AACA;MACY,MAAMW,eAAe,GAAG,CAACC,GAAG,CAACC,QAAQ,CAAC,OAAO,EAAE,eAAe,CAAC,GAAG,MAAM,GAAG,qCAAqC;MAChH,MAAMC,cAAc,GAAGZ,YAAY,GAAG,OAAO,GAAGS,eAAe;MAC/D,MAAMI,iBAAiB,GAAGhG,oBAAoB,CAACC,oBAAoB;MACnE,MAAMgG,cAAc,GAAG,cAAcF,cAAc,WAAWC,iBAAiB,GAAG;MAClFT,mBAAmB,CACdW,WAAW,CAAC;QACb9B,SAAS,EAAE6B;MACf,CAAC,CAAC,CACGE,cAAc,CAAC,MAAMV,MAAM,CAACnH,KAAK,CAAC8H,WAAW,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC,CAC3EvB,UAAU,CAACD,YAAY,CAAC,CACxBX,SAAS,CAAC,CACX;QAAEC,MAAM,EAAE,CAAC;QAAEmC,MAAM,EAAE,aAAa;QAAEjC,SAAS,EAAE,0BAA0B;QAAEkC,YAAY,EAAE;MAAM,CAAC,EAChG;QAAEpC,MAAM,EAAE,CAAC;QAAEmC,MAAM,EAAE,gBAAgB;QAAEjC,SAAS,EAAE6B,cAAc;QAAEK,YAAY,EAAE;MAAgB,CAAC,CACpG,CAAC;MACFvB,aAAa,CAACC,YAAY,CAACO,mBAAmB,CAAC;IACnD,CAAC,MACI;MACDR,aAAa,CAACC,YAAY,CAACpB,iBAAiB,CAAC;MAC7C,IAAI,CAACuB,YAAY,EAAE;QACfnB,gBAAgB,CAACH,MAAM,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC;MAChD,CAAC,MACI;QACD,MAAMmC,iBAAiB,GAAGb,YAAY,GAAGnF,oBAAoB,CAACC,oBAAoB,GAAG,CAAC;QACtF,MAAMgG,cAAc,GAAG,2BAA2BD,iBAAiB,GAAG;QACtET,mBAAmB,CACdW,WAAW,CAAC;UACb9B,SAAS,EAAE6B;QACf,CAAC,CAAC,CACGpB,UAAU,CAACS,gBAAgB,CAAC/D,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAC5D0C,SAAS,CAAC,CACX;UAAEC,MAAM,EAAE,CAAC;UAAEmC,MAAM,EAAE,aAAa;UAAEjC,SAAS,EAAE;QAAyB,CAAC,EACzE;UAAEF,MAAM,EAAE,CAAC;UAAEmC,MAAM,EAAE,gBAAgB;UAAEjC,SAAS,EAAE6B;QAAe,CAAC,CACrE,CAAC;QACF,MAAMM,eAAe,GAAGnJ,eAAe,CAAC,CAAC,CACpC8I,WAAW,CAAC;UACb9B,SAAS,EAAE6B;QACf,CAAC,CAAC,CACGpB,UAAU,CAACS,gBAAgB,CAAC/D,aAAa,CAAC,eAAe,CAAC,CAAC,CAC3D0C,SAAS,CAAC,CACX;UAAEC,MAAM,EAAE,CAAC;UAAEC,OAAO,EAAE,GAAG;UAAEC,SAAS,EAAE;QAAyB,CAAC,EAChE;UAAEF,MAAM,EAAE,CAAC;UAAEC,OAAO,EAAE,GAAG;UAAEC,SAAS,EAAE6B;QAAe,CAAC,CACzD,CAAC;QACFlB,aAAa,CAACC,YAAY,CAAC,CAACO,mBAAmB,EAAEgB,eAAe,CAAC,CAAC;MACtE;IACJ;EACJ,CAAC,MACI;IACDxB,aAAa,CAACC,YAAY,CAACpB,iBAAiB,CAAC;EACjD;EACA,OAAOmB,aAAa;AACxB,CAAC;AAED,MAAMyB,sBAAsB,GAAGA,CAAA,KAAM;EACjC,MAAM5C,iBAAiB,GAAGxG,eAAe,CAAC,CAAC,CAACyG,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAC3F,MAAMG,gBAAgB,GAAG5G,eAAe,CAAC,CAAC,CAACyG,MAAM,CAAC,WAAW,EAAE,iBAAiB,EAAE,mBAAmB,CAAC;EACtG,OAAO;IAAED,iBAAiB;IAAEI;EAAiB,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA,MAAMyC,iBAAiB,GAAGA,CAAC9B,MAAM,EAAEnB,IAAI,EAAEZ,QAAQ,GAAG,GAAG,KAAK;EACxD,MAAM;IAAEgC,YAAY;IAAEnB;EAAkB,CAAC,GAAGD,IAAI;EAChD,MAAMlC,IAAI,GAAGnG,cAAc,CAACwJ,MAAM,CAAC;EACnC,MAAM;IAAEX,gBAAgB;IAAEJ;EAAkB,CAAC,GAAGH,iBAAiB,KAAK3F,SAAS,GAAGuG,yBAAyB,CAACb,IAAI,CAAC,GAAGgD,sBAAsB,CAAC,CAAC;EAC5I5C,iBAAiB,CAACiB,UAAU,CAACvD,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC,CAAC;EAChEyC,gBAAgB,CAACa,UAAU,CAACvD,IAAI,CAACwD,gBAAgB,CAAC,+BAA+B,CAAC,CAAC,CAAChB,YAAY,CAAC;IAAEK,OAAO,EAAE;EAAE,CAAC,CAAC;EAChH,MAAMY,aAAa,GAAG3H,eAAe,CAAC,cAAc,CAAC,CAChDyH,UAAU,CAACF,MAAM,CAAC,CAClBhC,MAAM,CAAC,6BAA6B,CAAC,CACrCC,QAAQ,CAACA,QAAQ,CAAC,CAClBoC,YAAY,CAAChB,gBAAgB,CAAC;EACnC,IAAIY,YAAY,EAAE;IACd,MAAMK,QAAQ,GAAGC,MAAM,CAACnG,UAAU,GAAG,GAAG;IACxC,MAAMoG,YAAY,GAAGP,YAAY,CAACQ,OAAO,KAAK,WAAW,IAAIR,YAAY,CAACS,iBAAiB,KAAKvH,SAAS;IACzG,MAAMwH,gBAAgB,GAAGnK,cAAc,CAACyJ,YAAY,CAAC;IACrD,MAAMW,mBAAmB,GAAGnI,eAAe,CAAC,CAAC,CACxCsJ,iBAAiB,CAAC,CAAC,WAAW,CAAC,CAAC,CAChC3C,gBAAgB,CAAC,CAAC,WAAW,CAAC,CAAC,CAC/BrE,QAAQ,CAAEiH,WAAW,IAAK;MAC3B;MACA,IAAIA,WAAW,KAAK,CAAC,EAAE;QACnB;MACJ;MACA/B,YAAY,CAACtG,KAAK,CAAC8H,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC;MAC9C,MAAMQ,SAAS,GAAGC,KAAK,CAACC,IAAI,CAACrB,MAAM,CAACX,gBAAgB,CAAC,gCAAgC,CAAC,CAAC,CAACuB,MAAM,CAAEU,CAAC,IAAKA,CAAC,CAAC1B,iBAAiB,KAAKvH,SAAS,CAAC,CAACkJ,MAAM;MAC/I,IAAIJ,SAAS,IAAI,CAAC,EAAE;QAChBnB,MAAM,CAACnH,KAAK,CAAC8H,WAAW,CAAC,kBAAkB,EAAE,EAAE,CAAC;MACpD;IACJ,CAAC,CAAC;IACF,MAAMX,MAAM,GAAGC,QAAQ,CAACC,IAAI;IAC5B,IAAIV,QAAQ,EAAE;MACV,MAAMW,eAAe,GAAG,CAACC,GAAG,CAACC,QAAQ,CAAC,OAAO,EAAE,eAAe,CAAC,GAAG,MAAM,GAAG,qCAAqC;MAChH,MAAMC,cAAc,GAAGZ,YAAY,GAAG,OAAO,GAAGS,eAAe;MAC/D,MAAMI,iBAAiB,GAAGhG,oBAAoB,CAACC,oBAAoB;MACnE,MAAMgG,cAAc,GAAG,cAAcF,cAAc,WAAWC,iBAAiB,GAAG;MAClFT,mBAAmB,CAACV,UAAU,CAACD,YAAY,CAAC,CAACX,SAAS,CAAC,CACnD;QAAEC,MAAM,EAAE,CAAC;QAAEmC,MAAM,EAAE,gBAAgB;QAAEjC,SAAS,EAAE6B,cAAc;QAAEK,YAAY,EAAE;MAAgB,CAAC,EACjG;QAAEpC,MAAM,EAAE,CAAC;QAAEmC,MAAM,EAAE,aAAa;QAAEjC,SAAS,EAAE,0BAA0B;QAAEkC,YAAY,EAAE;MAAM,CAAC,CACnG,CAAC;MACFvB,aAAa,CAACC,YAAY,CAACO,mBAAmB,CAAC;IACnD,CAAC,MACI;MACDR,aAAa,CAACC,YAAY,CAACpB,iBAAiB,CAAC;MAC7C,IAAI,CAACuB,YAAY,EAAE;QACfnB,gBAAgB,CAACH,MAAM,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC;MAChD,CAAC,MACI;QACD,MAAMmC,iBAAiB,GAAGb,YAAY,GAAGnF,oBAAoB,CAACC,oBAAoB,GAAG,CAAC;QACtF,MAAMgG,cAAc,GAAG,2BAA2BD,iBAAiB,GAAG;QACtET,mBAAmB,CACdV,UAAU,CAACS,gBAAgB,CAAC/D,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAC5D2E,WAAW,CAAC;UACb9B,SAAS,EAAE;QACf,CAAC,CAAC,CACGH,SAAS,CAAC,CACX;UAAEC,MAAM,EAAE,CAAC;UAAEmC,MAAM,EAAE,gBAAgB;UAAEjC,SAAS,EAAE6B;QAAe,CAAC,EAClE;UAAE/B,MAAM,EAAE,CAAC;UAAEmC,MAAM,EAAE,aAAa;UAAEjC,SAAS,EAAE;QAAyB,CAAC,CAC5E,CAAC;QACF,MAAMmC,eAAe,GAAGnJ,eAAe,CAAC,CAAC,CACpCyH,UAAU,CAACS,gBAAgB,CAAC/D,aAAa,CAAC,eAAe,CAAC,CAAC,CAC3D2E,WAAW,CAAC;UACb9B,SAAS,EAAE;QACf,CAAC,CAAC,CACGH,SAAS,CAAC,CACX;UAAEC,MAAM,EAAE,CAAC;UAAEC,OAAO,EAAE,GAAG;UAAEC,SAAS,EAAE6B;QAAe,CAAC,EACtD;UAAE/B,MAAM,EAAE,CAAC;UAAEC,OAAO,EAAE,GAAG;UAAEC,SAAS,EAAE;QAAyB,CAAC,CACnE,CAAC;QACFW,aAAa,CAACC,YAAY,CAAC,CAACO,mBAAmB,EAAEgB,eAAe,CAAC,CAAC;MACtE;IACJ;EACJ,CAAC,MACI;IACDxB,aAAa,CAACC,YAAY,CAACpB,iBAAiB,CAAC;EACjD;EACA,OAAOmB,aAAa;AACxB,CAAC;AAED,MAAMkC,oBAAoB,GAAGA,CAAA,KAAM;EAC/B,MAAMrD,iBAAiB,GAAGxG,eAAe,CAAC,CAAC,CACtCyG,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzC,MAAMC,gBAAgB,GAAG5G,eAAe,CAAC,CAAC,CAAC6G,SAAS,CAAC,CACjD;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAmB,CAAC,EAC3D;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAkB,CAAC,CAC1D,CAAC;EACF,OAAO;IAAER,iBAAiB;IAAEI;EAAiB,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA,MAAMkD,gBAAgB,GAAGA,CAACvC,MAAM,EAAEnB,IAAI,KAAK;EACvC,MAAM;IAAEC;EAAkB,CAAC,GAAGD,IAAI;EAClC,MAAMlC,IAAI,GAAGnG,cAAc,CAACwJ,MAAM,CAAC;EACnC,MAAM;IAAEX,gBAAgB;IAAEJ;EAAkB,CAAC,GAAGH,iBAAiB,KAAK3F,SAAS,GAAGyF,yBAAyB,CAACC,IAAI,CAAC,GAAGyD,oBAAoB,CAAC,CAAC;EAC1IrD,iBAAiB,CAACiB,UAAU,CAACvD,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC,CAAC;EAChEyC,gBAAgB,CAACa,UAAU,CAACvD,IAAI,CAACC,aAAa,CAAC,gBAAgB,CAAC,CAAC;EACjE,OAAOnE,eAAe,CAAC,CAAC,CACnByH,UAAU,CAACF,MAAM,CAAC,CAClBhC,MAAM,CAAC,gCAAgC,CAAC,CACxCC,QAAQ,CAAC,GAAG,CAAC,CACboC,YAAY,CAAC,CAACpB,iBAAiB,EAAEI,gBAAgB,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMmD,oBAAoB,GAAGA,CAAA,KAAM;EAC/B,MAAMvD,iBAAiB,GAAGxG,eAAe,CAAC,CAAC,CAACyG,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAC3F,MAAMG,gBAAgB,GAAG5G,eAAe,CAAC,CAAC,CAAC6G,SAAS,CAAC,CACjD;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAkB,CAAC,EAC1D;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAmB,CAAC,CAC3D,CAAC;EACF,OAAO;IAAER,iBAAiB;IAAEI;EAAiB,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA,MAAMoD,gBAAgB,GAAGA,CAACzC,MAAM,EAAEnB,IAAI,KAAK;EACvC,MAAM;IAAEC;EAAkB,CAAC,GAAGD,IAAI;EAClC,MAAMlC,IAAI,GAAGnG,cAAc,CAACwJ,MAAM,CAAC;EACnC,MAAM;IAAEX,gBAAgB;IAAEJ;EAAkB,CAAC,GAAGH,iBAAiB,KAAK3F,SAAS,GAAGuG,yBAAyB,CAACb,IAAI,CAAC,GAAG2D,oBAAoB,CAAC,CAAC;EAC1IvD,iBAAiB,CAACiB,UAAU,CAACvD,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC,CAAC;EAChEyC,gBAAgB,CAACa,UAAU,CAACvD,IAAI,CAACC,aAAa,CAAC,gBAAgB,CAAC,CAAC;EACjE,OAAOnE,eAAe,CAAC,CAAC,CACnBuF,MAAM,CAAC,kCAAkC,CAAC,CAC1CC,QAAQ,CAAC,GAAG,CAAC,CACboC,YAAY,CAAC,CAACpB,iBAAiB,EAAEI,gBAAgB,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMqD,kBAAkB,GAAGA,CAAC1C,MAAM,EAAE2C,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,EAAE5I,kBAAkB,EAAEU,SAAS,EAAEmI,WAAW,GAAG,EAAE,EAAEC,oBAAoB,EAAEtH,SAAS,EAAEuH,kBAAkB,KAAK;EACnL;EACA,MAAMpD,eAAe,GAAG,CACpB;IAAEL,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAA0B,CAAC,EACjD;IAAED,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAK,CAAC,CAC/B;EACD,MAAMK,cAAc,GAAG,CACnB;IAAEN,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAA0B,CAAC,EACjD;IAAED,MAAM,EAAE,CAAC,GAAGtF,kBAAkB;IAAEuF,OAAO,EAAE;EAAE,CAAC,EAC9C;IAAED,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAE,CAAC,CAC5B;EACD,MAAMyD,aAAa,GAAG;IAClBC,iBAAiB,EAAE,CACf;MAAE3D,MAAM,EAAE,CAAC;MAAEE,SAAS,EAAE;IAAiB,CAAC,EAC1C;MAAEF,MAAM,EAAE,CAAC;MAAEE,SAAS,EAAE;IAAmB,CAAC,CAC/C;IACD0D,kBAAkB,EAAElJ,kBAAkB,KAAK,CAAC,GAAG4F,cAAc,GAAGD;EACpE,CAAC;EACD,MAAM7D,SAAS,GAAGiE,MAAM,CAACpD,aAAa,CAAC,aAAa,CAAC;EACrD,MAAMjB,MAAM,GAAGiH,SAAS,CAACQ,YAAY;EACrC,IAAItE,iBAAiB,GAAG+D,iBAAiB;EACzC,IAAItD,MAAM,GAAG,CAAC;EACd,IAAIzD,uBAAuB,GAAG,KAAK;EACnC,MAAMG,iBAAiB,GAAG,IAAI;EAC9B,MAAMoD,gBAAgB,GAAG1E,SAAS,CAAC0I,eAAe,CAACC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,EAAE,KAAK,kBAAkB,CAAC;EAC/F,MAAMvE,iBAAiB,GAAGtE,SAAS,CAAC0I,eAAe,CAACC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,EAAE,KAAK,mBAAmB,CAAC;EACjG,MAAMC,aAAa,GAAGX,WAAW,CAACA,WAAW,CAACT,MAAM,GAAG,CAAC,CAAC;EACzD,MAAMqB,aAAa,GAAGZ,WAAW,CAAC,CAAC,CAAC;EACpC,MAAMa,cAAc,GAAGA,CAAA,KAAM;IACzB3D,MAAM,CAACrG,KAAK,CAAC8H,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAClDkB,UAAU,CAAChJ,KAAK,CAAC8H,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC;IACtD;AACR;AACA;AACA;AACA;IACQzB,MAAM,CAAC4D,SAAS,CAACC,MAAM,CAACrM,wBAAwB,CAAC;EACrD,CAAC;EACD,MAAMsM,eAAe,GAAGA,CAAA,KAAM;IAC1B9D,MAAM,CAACrG,KAAK,CAAC8H,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC;IAClDkB,UAAU,CAAChJ,KAAK,CAAC8H,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC;IACtD;AACR;AACA;AACA;AACA;AACA;AACA;IACQzB,MAAM,CAAC4D,SAAS,CAACG,GAAG,CAACvM,wBAAwB,CAAC;EAClD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI6H,gBAAgB,IAAIJ,iBAAiB,EAAE;IACvCI,gBAAgB,CAACC,SAAS,CAAC,CAAC,GAAG2D,aAAa,CAACC,iBAAiB,CAAC,CAAC;IAChEjE,iBAAiB,CAACK,SAAS,CAAC,CAAC,GAAG2D,aAAa,CAACE,kBAAkB,CAAC,CAAC;IAClExI,SAAS,CAACuC,aAAa,CAAC,IAAI,EAAE,CAAC,GAAG4B,iBAAiB,CAAC;IACpD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMkF,oBAAoB,GAAGlF,iBAAiB,GAAG7E,kBAAkB;IACnE,IAAI+J,oBAAoB,EAAE;MACtBL,cAAc,CAAC,CAAC;IACpB,CAAC,MACI;MACDG,eAAe,CAAC,CAAC;IACrB;EACJ;EACA,IAAI/H,SAAS,IAAI+C,iBAAiB,KAAK2E,aAAa,EAAE;IAClD1H,SAAS,CAACM,OAAO,GAAG,KAAK;EAC7B;EACA,MAAMC,QAAQ,GAAIC,MAAM,IAAK;IACzB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMR,SAAS,GAAGpG,qBAAqB,CAAC4G,MAAM,CAACE,KAAK,CAACD,MAAM,CAAC;IAC5DsC,iBAAiB,GAAGiE,oBAAoB,CAAC,CAAC;IAC1C,IAAIjE,iBAAiB,KAAK,CAAC,IAAI/C,SAAS,EAAE;MACtC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,QAAQ,GAAGpG,YAAY,CAACmG,SAAS,CAAC,GAAGvF,cAAc,CAACuF,SAAS,CAAC,CAACa,aAAa,CAAC,eAAe,CAAC,GAAGb,SAAS;MAC/G,MAAMc,qBAAqB,GAAG,CAAC,CAACd,SAAS,CAACa,aAAa,CAAC,eAAe,CAAC;MACxE,OAAO,CAACC,qBAAqB,IAAIb,QAAQ,CAACc,SAAS,KAAK,CAAC;IAC7D;IACA,OAAO,IAAI;EACf,CAAC;EACD,MAAME,OAAO,GAAIT,MAAM,IAAK;IACxB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQT,uBAAuB,GAAGkE,MAAM,CAACpF,UAAU,KAAKzB,SAAS,IAAI6G,MAAM,CAACpF,UAAU,KAAK,IAAI,IAAI8I,aAAa,KAAK,CAAC;IAC9G;AACR;AACA;AACA;IACQ,IAAInH,MAAM,CAACU,MAAM,GAAG,CAAC,IAAIlB,SAAS,EAAE;MAChCA,SAAS,CAACM,OAAO,GAAG,KAAK;IAC7B;IACA1F,GAAG,CAAC,MAAM;MACN;AACZ;AACA;AACA;MACYqJ,MAAM,CAACiE,KAAK,CAAC,CAAC;IAClB,CAAC,CAAC;IACFtJ,SAAS,CAACuC,aAAa,CAAC,IAAI,EAAE,CAAC,GAAG4B,iBAAiB,CAAC;EACxD,CAAC;EACD,MAAM3B,MAAM,GAAIZ,MAAM,IAAK;IACvB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIA,MAAM,CAACU,MAAM,GAAG,CAAC,IAAIlB,SAAS,EAAE;MAChCA,SAAS,CAACM,OAAO,GAAG,KAAK;IAC7B;IACA;AACR;AACA;AACA;AACA;IACQ,MAAM6H,WAAW,GAAG,CAAC,GAAGpF,iBAAiB;IACzC,MAAMqF,sBAAsB,GAAGrB,WAAW,CAACT,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGS,WAAW,CAAC,CAAC,CAAC,GAAG3J,SAAS;IACtF,MAAMiE,IAAI,GAAG8G,WAAW,GAAG3H,MAAM,CAACU,MAAM,GAAGtB,MAAM;IACjD,MAAM0B,iCAAiC,GAAG8G,sBAAsB,KAAKhL,SAAS,IAAIiE,IAAI,IAAI+G,sBAAsB,IAAIrI,uBAAuB;IAC3I;AACR;AACA;AACA;AACA;IACQ,MAAMwB,OAAO,GAAGD,iCAAiC,GAAGpB,iBAAiB,GAAG,MAAM;IAC9E;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMsB,aAAa,GAAGF,iCAAiC,IAAI8G,sBAAsB,KAAKhL,SAAS,GACzFgL,sBAAsB,GACpBhJ,mBAAmB,CAAC,CAACiC,IAAI,GAAG+G,sBAAsB,KAAK7G,OAAO,GAAG6G,sBAAsB,CAAC,CAAC,GAC3F/G,IAAI;IACVmC,MAAM,GAAG7I,KAAK,CAAC,MAAM,EAAE6G,aAAa,EAAED,OAAO,CAAC;IAC9C3C,SAAS,CAAC8C,YAAY,CAAC8B,MAAM,CAAC;EAClC,CAAC;EACD,MAAM7B,KAAK,GAAInB,MAAM,IAAK;IACtB;AACR;AACA;AACA;IACQ,MAAMoB,QAAQ,GAAGpB,MAAM,CAACqB,SAAS;IACjC,MAAMC,SAAS,GAAG,CAACtB,MAAM,CAACU,MAAM,GAAGU,QAAQ,GAAG,GAAG,IAAIhC,MAAM;IAC3D,MAAMyI,IAAI,GAAGtF,iBAAiB,GAAGjB,SAAS;IAC1C,MAAMnB,OAAO,GAAGoG,WAAW,CAACuB,MAAM,CAAC,CAACtO,CAAC,EAAEsC,CAAC,KAAK;MACzC,OAAOqG,IAAI,CAACC,GAAG,CAACtG,CAAC,GAAG+L,IAAI,CAAC,GAAG1F,IAAI,CAACC,GAAG,CAAC5I,CAAC,GAAGqO,IAAI,CAAC,GAAG/L,CAAC,GAAGtC,CAAC;IAC1D,CAAC,CAAC;IACFuO,qBAAqB,CAAC;MAClBC,UAAU,EAAE7H,OAAO;MACnB8H,gBAAgB,EAAEjF,MAAM;MACxB3E,UAAU,EAAEkB,uBAAuB;MACnC;AACZ;AACA;AACA;MACY2I,QAAQ,EAAE;IACd,CAAC,CAAC;EACN,CAAC;EACD,MAAMH,qBAAqB,GAAIjL,OAAO,IAAK;IACvC,MAAM;MAAEkL,UAAU;MAAE3J,UAAU;MAAE4J,gBAAgB;MAAEC;IAAS,CAAC,GAAGpL,OAAO;IACtE;AACR;AACA;AACA;AACA;AACA;IACQ,MAAMqL,oBAAoB,GAAG9J,UAAU,IAAI2J,UAAU,KAAK,CAAC;IAC3D,MAAMI,gBAAgB,GAAGD,oBAAoB,GAAG5F,iBAAiB,GAAGyF,UAAU;IAC9E,MAAMK,gBAAgB,GAAGD,gBAAgB,KAAK,CAAC;IAC/C7F,iBAAiB,GAAG,CAAC;IACrB;AACR;AACA;AACA;IACQ,IAAIO,gBAAgB,IAAIJ,iBAAiB,EAAE;MACvCI,gBAAgB,CAACC,SAAS,CAAC,CACvB;QAAEC,MAAM,EAAE,CAAC;QAAEE,SAAS,EAAE,cAAc+E,gBAAgB,GAAG,GAAG;MAAK,CAAC,EAClE;QAAEjF,MAAM,EAAE,CAAC;QAAEE,SAAS,EAAE,cAAc,CAAC,CAAC,GAAGkF,gBAAgB,IAAI,GAAG;MAAK,CAAC,CAC3E,CAAC;MACF1F,iBAAiB,CAACK,SAAS,CAAC,CACxB;QACIC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,kCAAkCzF,wBAAwB,CAAC,CAAC,GAAGyK,gBAAgB,EAAEvK,kBAAkB,CAAC;MACjH,CAAC,EACD;QACIsF,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,kCAAkCzF,wBAAwB,CAAC4K,gBAAgB,EAAE1K,kBAAkB,CAAC;MAC7G,CAAC,CACJ,CAAC;MACFU,SAAS,CAAC8C,YAAY,CAAC,CAAC,CAAC;IAC7B;IACA;AACR;AACA;AACA;IACQU,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC;IACrB,IAAIsG,oBAAoB,EAAE;MACtBlK,gBAAgB,CAACwF,MAAM,EAAErF,SAAS,CAAC;IACvC,CAAC,MACI,IAAI,CAACiK,gBAAgB,EAAE;MACxBnJ,SAAS,CAAC,CAAC;IACf;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIM,SAAS,IAAI4I,gBAAgB,KAAK7B,WAAW,CAACA,WAAW,CAACT,MAAM,GAAG,CAAC,CAAC,EAAE;MACvEtG,SAAS,CAACM,OAAO,GAAG,IAAI;IAC5B;IACA,OAAO,IAAIwI,OAAO,CAAEC,OAAO,IAAK;MAC5BnK,SAAS,CACJI,QAAQ,CAAC,MAAM;QAChB,IAAI6J,gBAAgB,EAAE;UAClB;AACpB;AACA;AACA;AACA;AACA;AACA;UACoB,IAAIvF,gBAAgB,IAAIJ,iBAAiB,EAAE;YACvCtI,GAAG,CAAC,MAAM;cACN0I,gBAAgB,CAACC,SAAS,CAAC,CAAC,GAAG2D,aAAa,CAACC,iBAAiB,CAAC,CAAC;cAChEjE,iBAAiB,CAACK,SAAS,CAAC,CAAC,GAAG2D,aAAa,CAACE,kBAAkB,CAAC,CAAC;cAClExI,SAAS,CAACuC,aAAa,CAAC,IAAI,EAAE,CAAC,GAAGyH,gBAAgB,CAAC;cACnD7F,iBAAiB,GAAG6F,gBAAgB;cACpC3B,kBAAkB,CAAClE,iBAAiB,CAAC;cACrC;AAC5B;AACA;AACA;cAC4B,MAAMkF,oBAAoB,GAAGlF,iBAAiB,GAAG7E,kBAAkB;cACnE,IAAI+J,oBAAoB,EAAE;gBACtBL,cAAc,CAAC,CAAC;cACpB,CAAC,MACI;gBACDG,eAAe,CAAC,CAAC;cACrB;cACA3F,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;cACpB0G,OAAO,CAAC,CAAC;YACb,CAAC,CAAC;UACN,CAAC,MACI;YACD3G,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;YACpB0G,OAAO,CAAC,CAAC;UACb;QACJ,CAAC,MACI;UACDA,OAAO,CAAC,CAAC;QACb;QACA;AAChB;AACA;AACA;AACA;MACY,CAAC,EAAE;QAAE9J,eAAe,EAAE;MAAK,CAAC,CAAC,CACxBqD,WAAW,CAAC,CAAC,EAAE,CAAC,EAAEoG,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC;IAC9C,CAAC,CAAC;EACN,CAAC;EACD,MAAMtG,OAAO,GAAGxF,aAAa,CAAC;IAC1B+B,EAAE,EAAEkI,SAAS;IACbtE,WAAW,EAAE,YAAY;IACzBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE,GAAG;IACdX,SAAS,EAAE,EAAE;IACbvB,QAAQ;IACRU,OAAO;IACPG,MAAM;IACNO;EACJ,CAAC,CAAC;EACF,OAAO;IACHS,OAAO;IACPmG;EACJ,CAAC;AACL,CAAC;AAED,MAAMS,WAAW,GAAG,0wHAA0wH;AAC9xH,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,0+EAA0+E;AAC7/E,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IACjBpQ,gBAAgB,CAAC,IAAI,EAAEoQ,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAGnQ,WAAW,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACoQ,WAAW,GAAGpQ,WAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACqQ,WAAW,GAAGrQ,WAAW,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAACsQ,UAAU,GAAGtQ,WAAW,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACuQ,sBAAsB,GAAGvQ,WAAW,CAAC,IAAI,EAAE,wBAAwB,EAAE,CAAC,CAAC;IAC5E,IAAI,CAACwQ,mBAAmB,GAAGxQ,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACyQ,oBAAoB,GAAGzQ,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC0Q,oBAAoB,GAAG1Q,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC2Q,mBAAmB,GAAG3Q,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC4Q,QAAQ,GAAG5Q,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC6Q,cAAc,GAAGhP,oBAAoB,CAAC,CAAC;IAC5C,IAAI,CAACiP,iBAAiB,GAAGvO,uBAAuB,CAAC,CAAC;IAClD,IAAI,CAACwO,YAAY,GAAG9P,YAAY,CAAC,CAAC;IAClC,IAAI,CAAC+P,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB;IACA,IAAI,CAACC,0BAA0B,GAAG,KAAK;IACvC,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,MAAM;QAAEC,eAAe;QAAEC;MAAe,CAAC,GAAG,IAAI;MAChD,IAAIA,cAAc,KAAK,OAAO,IAAID,eAAe,KAAKrN,SAAS,EAAE;QAC7D;AAChB;AACA;AACA;AACA;QACgB;MACJ;MACA,IAAI,CAACuN,oBAAoB,CAAC,CAAC;IAC/B,CAAC;IACD,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,MAAM;QAAEH;MAAgB,CAAC,GAAG,IAAI;MAChC,IAAIA,eAAe,KAAKrN,SAAS,EAAE;QAC/B;AAChB;AACA;AACA;AACA;AACA;QACgB;MACJ;MACA,IAAI,CAACnB,OAAO,CAACmB,SAAS,EAAEvB,QAAQ,CAAC;IACrC,CAAC;IACD,IAAI,CAACgP,WAAW,GAAIC,UAAU,IAAK;MAC/B,MAAMnM,EAAE,GAAG,IAAI,CAACoM,YAAY;MAC5B,MAAMC,IAAI,GAAGC,aAAa,CAACH,UAAU,CAACI,IAAI,CAAC;MAC3C,IAAIvM,EAAE,IAAIqM,IAAI,EAAE;QACZ,MAAMG,EAAE,GAAG,IAAIC,WAAW,CAACJ,IAAI,EAAE;UAC7BK,OAAO,EAAE,KAAK;UACdC,UAAU,EAAE,KAAK;UACjB9K,MAAM,EAAEsK,UAAU,CAACtK;QACvB,CAAC,CAAC;QACF7B,EAAE,CAAC4M,aAAa,CAACJ,EAAE,CAAC;MACxB;IACJ,CAAC;IACD,IAAI,CAACK,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,YAAY,GAAGtO,SAAS;IAC7B,IAAI,CAACuO,QAAQ,GAAGvO,SAAS;IACzB,IAAI,CAACwO,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,cAAc,GAAGzO,SAAS;IAC/B,IAAI,CAAC0O,cAAc,GAAG1O,SAAS;IAC/B,IAAI,CAAC2J,WAAW,GAAG3J,SAAS;IAC5B,IAAI,CAAC0J,iBAAiB,GAAG1J,SAAS;IAClC,IAAI,CAACc,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAAC6N,MAAM,GAAG3O,SAAS;IACvB,IAAI,CAACsN,cAAc,GAAG,MAAM;IAC5B,IAAI,CAACsB,SAAS,GAAG5O,SAAS;IAC1B,IAAI,CAAC6O,cAAc,GAAG7O,SAAS;IAC/B,IAAI,CAAC8O,QAAQ,GAAG9O,SAAS;IACzB,IAAI,CAAC+O,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC1D,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC/D,iBAAiB,GAAGvH,SAAS;IAClC,IAAI,CAACiP,cAAc,GAAGjP,SAAS;IAC/B,IAAI,CAAC0C,MAAM,GAAG,KAAK;IACnB,IAAI,CAACwM,OAAO,GAAGlP,SAAS;IACxB,IAAI,CAACmP,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC3N,UAAU,GAAG,IAAI;EAC1B;EACA4N,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC/B,IAAID,QAAQ,KAAK,IAAI,IAAIC,QAAQ,KAAK,KAAK,EAAE;MACzC,IAAI,CAAC3Q,OAAO,CAAC,CAAC;IAClB,CAAC,MACI,IAAI0Q,QAAQ,KAAK,KAAK,IAAIC,QAAQ,KAAK,IAAI,EAAE;MAC9C,IAAI,CAAC1Q,OAAO,CAAC,CAAC;IAClB;EACJ;EACA2Q,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEN,OAAO;MAAE3N,EAAE;MAAEuL;IAAkB,CAAC,GAAG,IAAI;IAC/C,IAAIoC,OAAO,EAAE;MACTpC,iBAAiB,CAAC2C,gBAAgB,CAAClO,EAAE,EAAE2N,OAAO,CAAC;IACnD;EACJ;EACAQ,kBAAkBA,CAAC/F,WAAW,EAAE;IAC5B,IAAIA,WAAW,KAAK3J,SAAS,EAAE;MAC3B,IAAI,CAAC2P,iBAAiB,GAAGhG,WAAW,CAACiG,IAAI,CAAC,CAAChT,CAAC,EAAEsC,CAAC,KAAKtC,CAAC,GAAGsC,CAAC,CAAC;IAC9D;EACJ;EACA2Q,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEtO;IAAG,CAAC,GAAG,IAAI;IACnB7C,cAAc,CAAC6C,EAAE,CAAC;IAClB,IAAI,CAACiO,cAAc,CAAC,CAAC;EACzB;EACAM,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAChD,iBAAiB,CAACiD,mBAAmB,CAAC,CAAC;EAChD;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAIC,EAAE;IACN,MAAM;MAAEtG,WAAW;MAAED,iBAAiB;MAAEnI,EAAE;MAAE0N;IAAe,CAAC,GAAG,IAAI;IACnE,MAAMjC,YAAY,GAAI,IAAI,CAACA,YAAY,GAAGrD,WAAW,KAAK3J,SAAS,IAAI0J,iBAAiB,KAAK1J,SAAU;IACvG,MAAMkQ,mBAAmB,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC;IAClD,IAAI,CAACjD,mBAAmB,GAAGxP,iBAAiB,CAAC8D,EAAE,EAAE2O,mBAAmB,CAAC;IACrE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIjB,cAAc,KAAKjP,SAAS,EAAE;MAC9BkQ,mBAAmB,CAACC,OAAO,CAAEC,SAAS,IAAK;QACvC,MAAMC,cAAc,GAAGpB,cAAc,CAACmB,SAAS,CAAC;QAChD,IAAIC,cAAc,EAAE;UAChB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,IAAI,CAACpD,mBAAmB,GAAGqD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACtD,mBAAmB,CAAC,EAAE;YAAE,CAACmD,SAAS,GAAGnB,cAAc,CAACmB,SAAS;UAAE,CAAC,CAAC;UACjI,OAAOnB,cAAc,CAACmB,SAAS,CAAC;QACpC;MACJ,CAAC,CAAC;IACN;IACA,IAAIpD,YAAY,EAAE;MACd,IAAI,CAACrH,iBAAiB,GAAG,IAAI,CAAC+D,iBAAiB;IACnD;IACA,IAAIC,WAAW,KAAK3J,SAAS,IAAI0J,iBAAiB,KAAK1J,SAAS,IAAI,CAAC2J,WAAW,CAAC6G,QAAQ,CAAC9G,iBAAiB,CAAC,EAAE;MAC1G5L,eAAe,CAAC,kEAAkE,CAAC;IACvF;IACA,IAAI,EAAE,CAACmS,EAAE,GAAG,IAAI,CAAChB,cAAc,MAAM,IAAI,IAAIgB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC5F,EAAE,CAAC,EAAE;MAC1E1L,YAAY,CAAC,IAAI,CAAC4C,EAAE,CAAC;IACzB;EACJ;EACAkP,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAAC/N,MAAM,KAAK,IAAI,EAAE;MACtBlF,GAAG,CAAC,MAAM,IAAI,CAACoB,OAAO,CAAC,CAAC,CAAC;IAC7B;IACA,IAAI,CAAC8Q,kBAAkB,CAAC,IAAI,CAAC/F,WAAW,CAAC;IACzC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC6F,cAAc,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkB,WAAWA,CAACC,KAAK,GAAG,KAAK,EAAE;IACvB,IAAI,IAAI,CAACC,eAAe,IAAI,CAACD,KAAK,EAAE;MAChC,OAAO;QACHpC,QAAQ,EAAE,IAAI,CAACqC,eAAe;QAC9B1D,MAAM,EAAE,IAAI,CAACA;MACjB,CAAC;IACL;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM2D,QAAQ,GAAG,IAAI,CAACtP,EAAE,CAACuP,UAAU;IACnC,MAAM5D,MAAM,GAAI,IAAI,CAACA,MAAM,GAAG2D,QAAQ,KAAK,IAAI,IAAI,CAAC,IAAI,CAACxC,aAAc;IACvE,MAAME,QAAQ,GAAI,IAAI,CAACqC,eAAe,GAAG1D,MAAM,GAAG,IAAI,CAACqB,QAAQ,IAAI,IAAI,CAACxB,YAAY,GAAG,IAAI,CAACwB,QAAS;IACrG,OAAO;MAAErB,MAAM;MAAEqB;IAAS,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACUwC,eAAeA,CAACC,IAAI,EAAEC,IAAI,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAA5Q,iBAAA;MAC9B,MAAM;QAAEmB;MAAW,CAAC,GAAGyP,KAAI;MAC3B,IAAI,OAAOzP,UAAU,KAAK,UAAU,EAAE;QAClC,OAAOA,UAAU,CAACuP,IAAI,EAAEC,IAAI,CAAC;MACjC;MACA,OAAOxP,UAAU;IAAC;EACtB;EACA;AACJ;AACA;EACU7C,OAAOA,CAAA,EAAG;IAAA,IAAAuS,MAAA;IAAA,OAAA7Q,iBAAA;MACZ,MAAM8Q,MAAM,SAASD,MAAI,CAACtE,cAAc,CAACwE,IAAI,CAAC,CAAC;MAC/C,IAAIF,MAAI,CAAC/C,SAAS,EAAE;QAChBgD,MAAM,CAAC,CAAC;QACR;MACJ;MACA,MAAM;QAAE7J,iBAAiB;QAAEhG;MAAG,CAAC,GAAG4P,MAAI;MACtC;AACR;AACA;AACA;MACQA,MAAI,CAACxL,iBAAiB,GAAGwL,MAAI,CAACzH,iBAAiB;MAC/C,MAAM;QAAEwD,MAAM;QAAEqB;MAAS,CAAC,GAAG4C,MAAI,CAACT,WAAW,CAAC,IAAI,CAAC;MACnD;AACR;AACA;AACA;AACA;MACQS,MAAI,CAACvE,QAAQ,CAAC0E,IAAI,CAAC,CAAC;MACpBH,MAAI,CAACxD,YAAY,SAASzQ,eAAe,CAACqR,QAAQ,EAAEhN,EAAE,EAAE4P,MAAI,CAACvC,SAAS,EAAE,CAAC,UAAU,CAAC,EAAEuC,MAAI,CAACtC,cAAc,EAAE3B,MAAM,CAAC;MAClH;AACR;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,IAAIvP,YAAY,CAAC4D,EAAE,CAAC,EAAE;QAClB,MAAMvC,SAAS,CAACmS,MAAI,CAACxD,YAAY,CAAC;QAClC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,CAAC,MACI,IAAI,CAACwD,MAAI,CAAChC,mBAAmB,EAAE;QAChC,MAAMlQ,YAAY,CAAC,CAAC;MACxB;MACA/C,SAAS,CAAC,MAAMiV,MAAI,CAAC5P,EAAE,CAACkJ,SAAS,CAACG,GAAG,CAAC,YAAY,CAAC,CAAC;MACpD,MAAMvD,YAAY,GAAGE,iBAAiB,KAAKvH,SAAS;MACpD;AACR;AACA;AACA;AACA;MACQ,IAAIqH,YAAY,IAAIlI,UAAU,CAACgS,MAAI,CAAC,KAAK,KAAK,EAAE;QAC5C;QACAA,MAAI,CAAC9O,cAAc,SAAS1C,SAAS,CAACS,QAAQ,CAAC,CAAC;QAChDY,oBAAoB,CAAC,CAAC;MAC1B;MACA,MAAMpC,OAAO,CAACuS,MAAI,EAAE,YAAY,EAAEvK,iBAAiB,EAAEwC,gBAAgB,EAAE;QACnEtC,YAAY,EAAES,iBAAiB;QAC/B5B,iBAAiB,EAAEwL,MAAI,CAACzH,iBAAiB;QACzC5I,kBAAkB,EAAEqQ,MAAI,CAACrQ;MAC7B,CAAC,CAAC;MACF;MACA,IAAI,OAAOsG,MAAM,KAAK,WAAW,EAAE;QAC/B;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;QACY+J,MAAI,CAACI,oBAAoB,GAAG,MAAM;UAC9B,IAAIJ,MAAI,CAACnM,OAAO,EAAE;YACd;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;YACoBmM,MAAI,CAACnM,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC;YAC1BzH,GAAG,CAAC,MAAM;cACN,IAAI2T,MAAI,CAACnM,OAAO,EAAE;gBACdmM,MAAI,CAACnM,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;cAC7B;YACJ,CAAC,CAAC;UACN;QACJ,CAAC;QACDmC,MAAM,CAACoK,gBAAgB,CAACnS,iBAAiB,EAAE8R,MAAI,CAACI,oBAAoB,CAAC;MACzE;MACA,IAAIJ,MAAI,CAACnE,YAAY,EAAE;QACnBmE,MAAI,CAACM,gBAAgB,CAAC,CAAC;MAC3B,CAAC,MACI,IAAIpK,YAAY,EAAE;QACnB8J,MAAI,CAACO,gBAAgB,CAAC,CAAC;MAC3B;MACAN,MAAM,CAAC,CAAC;IAAC;EACb;EACAM,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IACf,IAAI1B,EAAE;IACN,IAAI9Q,UAAU,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE;MAC5B;IACJ;IACA,MAAM;MAAEoC;IAAG,CAAC,GAAG,IAAI;IACnB;IACA;IACA;IACA,MAAMqQ,gBAAgB,GAAG,IAAI,CAAClD,cAAc,IAAItP,MAAM,CAACyS,GAAG,CAAC,YAAY,EAAElJ,iBAAiB,CAAC;IAC3F,MAAMyB,GAAG,GAAI,IAAI,CAAC5I,SAAS,GAAGoQ,gBAAgB,CAACrQ,EAAE,EAAE;MAAEuF,YAAY,EAAE,IAAI,CAACS;IAAkB,CAAC,CAAE;IAC7F,MAAM3E,SAAS,GAAG/F,cAAc,CAAC0E,EAAE,CAAC;IACpC,IAAI,CAACqB,SAAS,EAAE;MACZ7F,uBAAuB,CAACwE,EAAE,CAAC;MAC3B;IACJ;IACA,MAAMc,cAAc,GAAG,CAAC4N,EAAE,GAAG,IAAI,CAAC5N,cAAc,MAAM,IAAI,IAAI4N,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGvQ,KAAK,CAACa,OAAO;IAChG,IAAI,CAACyE,OAAO,GAAG5C,yBAAyB,CAACb,EAAE,EAAE6I,GAAG,EAAE/H,cAAc,EAAE,MAAM;MACpE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAAC8K,0BAA0B,GAAG,IAAI;MACtC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACYhM,uBAAuB,CAAC,IAAI,CAACkB,cAAc,CAAC;MAC5C,IAAI,CAACb,SAAS,CAACI,QAAQ,cAAAtB,iBAAA,CAAC,aAAY;QAChC,MAAMqR,MAAI,CAAC9S,OAAO,CAACmB,SAAS,EAAE/B,OAAO,CAAC;QACtC0T,MAAI,CAACxE,0BAA0B,GAAG,KAAK;MAC3C,CAAC,EAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACnI,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;EAC7B;EACAwM,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAEhI,SAAS;MAAEC,iBAAiB;MAAE5I;IAAmB,CAAC,GAAG,IAAI;IACjE,IAAI,CAAC2I,SAAS,IAAIC,iBAAiB,KAAK1J,SAAS,EAAE;MAC/C;IACJ;IACA,MAAM4R,gBAAgB,GAAG,IAAI,CAACnD,cAAc,IAAIrP,MAAM,CAACyS,GAAG,CAAC,YAAY,EAAEjL,iBAAiB,CAAC;IAC3F,MAAMwD,GAAG,GAAI,IAAI,CAAC5I,SAAS,GAAGoQ,gBAAgB,CAAC,IAAI,CAACrQ,EAAE,EAAE;MACpDuF,YAAY,EAAE,IAAI,CAACS,iBAAiB;MACpC5B,iBAAiB,EAAE+D,iBAAiB;MACpC5I;IACJ,CAAC,CAAE;IACHsJ,GAAG,CAACrG,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;IAC1B,MAAM;MAAEiB,OAAO;MAAEmG;IAAsB,CAAC,GAAG5B,kBAAkB,CAAC,IAAI,CAAChI,EAAE,EAAE,IAAI,CAACiI,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,EAAE5I,kBAAkB,EAAEsJ,GAAG,EAAE,IAAI,CAACuF,iBAAiB,EAAE,MAAM;MAAE,IAAIM,EAAE;MAAE,OAAO,CAACA,EAAE,GAAG,IAAI,CAACtK,iBAAiB,MAAM,IAAI,IAAIsK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAAE,CAAC,EAAE,MAAM,IAAI,CAAC6B,cAAc,CAAC,CAAC,EAAG1G,UAAU,IAAK;MAC5S,IAAI,IAAI,CAACzF,iBAAiB,KAAKyF,UAAU,EAAE;QACvC,IAAI,CAACzF,iBAAiB,GAAGyF,UAAU;QACnC,IAAI,CAACmB,sBAAsB,CAAC+E,IAAI,CAAC;UAAElG;QAAW,CAAC,CAAC;MACpD;IACJ,CAAC,CAAC;IACF,IAAI,CAACpG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmG,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACnG,OAAO,CAACC,MAAM,CAAC,IAAI,CAAC;EAC7B;EACA6M,cAAcA,CAAA,EAAG;IAAA,IAAAC,MAAA;IACb;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC5E,0BAA0B,GAAG,IAAI;IACtC,IAAI,CAAC3L,SAAS,CAACI,QAAQ,cAAAtB,iBAAA,CAAC,aAAY;MAChCyR,MAAI,CAACpM,iBAAiB,GAAG,CAAC;MAC1BoM,MAAI,CAACxF,sBAAsB,CAAC+E,IAAI,CAAC;QAAElG,UAAU,EAAE2G,MAAI,CAACpM;MAAkB,CAAC,CAAC;MACxE,MAAMoM,MAAI,CAAClT,OAAO,CAACmB,SAAS,EAAE/B,OAAO,CAAC;MACtC8T,MAAI,CAAC5E,0BAA0B,GAAG,KAAK;IAC3C,CAAC,EAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUtO,OAAOA,CAACmS,IAAI,EAAEC,IAAI,EAAE;IAAA,IAAAe,MAAA;IAAA,OAAA1R,iBAAA;MACtB,IAAI2P,EAAE;MACN,IAAI+B,MAAI,CAAC7E,0BAA0B,IAAI8D,IAAI,KAAKhT,OAAO,EAAE;QACrD,OAAO,KAAK;MAChB;MACA;AACR;AACA;AACA;AACA;MACQ,MAAMmT,MAAM,SAASY,MAAI,CAACnF,cAAc,CAACwE,IAAI,CAAC,CAAC;MAC/C;AACR;AACA;AACA;AACA;MACQ,IAAIJ,IAAI,KAAK,SAAS,IAAI,QAAQe,MAAI,CAACjB,eAAe,CAACC,IAAI,EAAEC,IAAI,CAAC,CAAC,EAAE;QACjEG,MAAM,CAAC,CAAC;QACR,OAAO,KAAK;MAChB;MACA,MAAM;QAAE7J;MAAkB,CAAC,GAAGyK,MAAI;MAClC;AACR;AACA;AACA;AACA;MACQ,MAAM3K,YAAY,GAAGE,iBAAiB,KAAKvH,SAAS;MACpD,IAAIqH,YAAY,IAAIlI,UAAU,CAAC6S,MAAI,CAAC,KAAK,KAAK,EAAE;QAC5C7Q,uBAAuB,CAAC6Q,MAAI,CAAC3P,cAAc,CAAC;MAChD;MACA;MACA,IAAI,OAAO+E,MAAM,KAAK,WAAW,IAAI4K,MAAI,CAACT,oBAAoB,EAAE;QAC5DnK,MAAM,CAAC6K,mBAAmB,CAAC5S,iBAAiB,EAAE2S,MAAI,CAACT,oBAAoB,CAAC;QACxES,MAAI,CAACT,oBAAoB,GAAGvR,SAAS;MACzC;MACA,MAAMkS,SAAS,SAASrT,OAAO,CAACmT,MAAI,EAAEhB,IAAI,EAAEC,IAAI,EAAE,YAAY,EAAEtI,iBAAiB,EAAEW,gBAAgB,EAAE;QACjGxC,YAAY,EAAES,iBAAiB;QAC/B5B,iBAAiB,EAAE,CAACsK,EAAE,GAAG+B,MAAI,CAACrM,iBAAiB,MAAM,IAAI,IAAIsK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG+B,MAAI,CAACtI,iBAAiB;QACxG5I,kBAAkB,EAAEkR,MAAI,CAAClR;MAC7B,CAAC,CAAC;MACF,IAAIoR,SAAS,EAAE;QACX,MAAM;UAAE3D;QAAS,CAAC,GAAGyD,MAAI,CAACtB,WAAW,CAAC,CAAC;QACvC,MAAMvT,eAAe,CAACoR,QAAQ,EAAEyD,MAAI,CAACrE,YAAY,CAAC;QAClDzR,SAAS,CAAC,MAAM8V,MAAI,CAACzQ,EAAE,CAACkJ,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC,CAAC;QACvD,IAAIsH,MAAI,CAACxQ,SAAS,EAAE;UAChBwQ,MAAI,CAACxQ,SAAS,CAAC2Q,OAAO,CAAC,CAAC;QAC5B;QACA,IAAIH,MAAI,CAAChN,OAAO,EAAE;UACdgN,MAAI,CAAChN,OAAO,CAACmN,OAAO,CAAC,CAAC;QAC1B;MACJ;MACAH,MAAI,CAACrM,iBAAiB,GAAG3F,SAAS;MAClCgS,MAAI,CAACxQ,SAAS,GAAGxB,SAAS;MAC1BoR,MAAM,CAAC,CAAC;MACR,OAAOc,SAAS;IAAC;EACrB;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,OAAOtT,WAAW,CAAC,IAAI,CAACyC,EAAE,EAAE,oBAAoB,CAAC;EACrD;EACA;AACJ;AACA;EACI8Q,aAAaA,CAAA,EAAG;IACZ,OAAOvT,WAAW,CAAC,IAAI,CAACyC,EAAE,EAAE,qBAAqB,CAAC;EACtD;EACA;AACJ;AACA;AACA;EACU+Q,oBAAoBA,CAAClH,UAAU,EAAE;IAAA,IAAAmH,MAAA;IAAA,OAAAjS,iBAAA;MACnC,IAAI,CAACiS,MAAI,CAACvF,YAAY,EAAE;QACpBlP,eAAe,CAAC,yDAAyD,CAAC;QAC1E;MACJ;MACA,IAAI,CAACyU,MAAI,CAAC5I,WAAW,CAAC6G,QAAQ,CAACpF,UAAU,CAAC,EAAE;QACxCtN,eAAe,CAAC,6CAA6CsN,UAAU,sFAAsF,CAAC;QAC9J;MACJ;MACA,MAAM;QAAEzF,iBAAiB;QAAEwF,qBAAqB;QAAE1J,UAAU;QAAEkI,WAAW;QAAE2B;MAAS,CAAC,GAAGiH,MAAI;MAC5F,IAAI5M,iBAAiB,KAAKyF,UAAU,EAAE;QAClC;MACJ;MACA,IAAID,qBAAqB,EAAE;QACvBoH,MAAI,CAAClF,eAAe,GAAGlC,qBAAqB,CAAC;UACzCC,UAAU;UACVC,gBAAgB,EAAE,CAAC,GAAG1F,iBAAiB;UACvClE,UAAU,EAAEA,UAAU,KAAKzB,SAAS,IAAIyB,UAAU,KAAK,IAAI,IAAIkI,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC;UACnF2B;QACJ,CAAC,CAAC;QACF,MAAMiH,MAAI,CAAClF,eAAe;QAC1BkF,MAAI,CAAClF,eAAe,GAAGrN,SAAS;MACpC;IAAC;EACL;EACA;AACJ;AACA;EACU4J,oBAAoBA,CAAA,EAAG;IAAA,IAAA4I,MAAA;IAAA,OAAAlS,iBAAA;MACzB,OAAOkS,MAAI,CAAC7M,iBAAiB;IAAC;EAClC;EACM4H,oBAAoBA,CAAA,EAAG;IAAA,IAAAkF,MAAA;IAAA,OAAAnS,iBAAA;MACzB,MAAM;QAAEqJ,WAAW;QAAEhE;MAAkB,CAAC,GAAG8M,MAAI;MAC/C,IAAI,CAAC9I,WAAW,IAAIhE,iBAAiB,IAAI,IAAI,EAAE;QAC3C;AACZ;AACA;AACA;QACY,OAAO,KAAK;MAChB;MACA,MAAM+M,kBAAkB,GAAG/I,WAAW,CAACpB,MAAM,CAAErJ,CAAC,IAAKA,CAAC,KAAK,CAAC,CAAC;MAC7D,MAAMyT,sBAAsB,GAAGD,kBAAkB,CAACE,OAAO,CAACjN,iBAAiB,CAAC;MAC5E,MAAMkN,mBAAmB,GAAG,CAACF,sBAAsB,GAAG,CAAC,IAAID,kBAAkB,CAACxJ,MAAM;MACpF,MAAM4J,cAAc,GAAGJ,kBAAkB,CAACG,mBAAmB,CAAC;MAC9D;AACR;AACA;AACA;AACA;MACQ,MAAMJ,MAAI,CAACH,oBAAoB,CAACQ,cAAc,CAAC;MAC/C,OAAO,IAAI;IAAC;EAChB;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEpE,MAAM;MAAE3B,YAAY;MAAEzF,iBAAiB;MAAE0H,cAAc;MAAE3B,cAAc;MAAEL,mBAAmB;MAAEmC;IAAU,CAAC,GAAG,IAAI;IACxH,MAAM4D,UAAU,GAAGrE,MAAM,KAAK,KAAK,IAAI3B,YAAY;IACnD,MAAMiG,IAAI,GAAG9T,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM+T,WAAW,GAAG3L,iBAAiB,KAAKvH,SAAS,IAAIiT,IAAI,KAAK,KAAK;IACrE,MAAME,aAAa,GAAG7F,cAAc,KAAK,OAAO;IAChD,OAAQnR,CAAC,CAACE,IAAI,EAAEiU,MAAM,CAACC,MAAM,CAAC;MAAE6C,GAAG,EAAE,0CAA0C;MAAE,WAAW,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,EAAEpE,cAAc,EAAE;MAAEzO,KAAK,EAAE;QACvI8S,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,CAAChF,YAAY;MACxC,CAAC;MAAEiF,KAAK,EAAEjD,MAAM,CAACC,MAAM,CAAC;QAAE,CAAC0C,IAAI,GAAG,IAAI;QAAE,CAAC,eAAe,GAAG,CAACC,WAAW,IAAI,CAAClG,YAAY;QAAE,CAAC,YAAY,GAAGkG,WAAW;QAAE,CAAC,aAAa,GAAGlG,YAAY;QAAE,gBAAgB,EAAE,IAAI;QAAE,CAAC3O,wBAAwB,GAAG+Q,SAAS,KAAK;MAAM,CAAC,EAAErQ,WAAW,CAAC,IAAI,CAAC+P,QAAQ,CAAC,CAAC;MAAE0E,gBAAgB,EAAE,IAAI,CAAChG,aAAa;MAAEiG,oBAAoB,EAAE,IAAI,CAAChG,WAAW;MAAEiG,qBAAqB,EAAE,IAAI,CAACjG,WAAW;MAAEkG,qBAAqB,EAAE,IAAI,CAAClG,WAAW;MAAEmG,oBAAoB,EAAE,IAAI,CAACnG;IAAY,CAAC,CAAC,EAAEtR,CAAC,CAAC,cAAc,EAAE;MAAEiX,GAAG,EAAE,0CAA0C;MAAES,GAAG,EAAGtS,EAAE,IAAM,IAAI,CAACiI,UAAU,GAAGjI,EAAG;MAAEuS,OAAO,EAAE,IAAI,CAAC9E,YAAY;MAAE+E,QAAQ,EAAE,IAAI,CAAChF,eAAe;MAAEiF,IAAI,EAAE;IAAW,CAAC,CAAC,EAAEf,IAAI,KAAK,KAAK,IAAI9W,CAAC,CAAC,KAAK,EAAE;MAAEiX,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE;IAAe,CAAC,CAAC,EAAEpX,CAAC,CAAC,KAAK,EAAEmU,MAAM,CAACC,MAAM,CAAC;MAAE6C,GAAG,EAAE,0CAA0C;MACpzB;AACZ;AACA;AACA;AACA;AACA;MACYnC,IAAI,EAAE;IAAS,CAAC,EAAEhE,mBAAmB,EAAE;MAAE,YAAY,EAAE,MAAM;MAAEsG,KAAK,EAAE,mCAAmC;MAAES,IAAI,EAAE,SAAS;MAAEH,GAAG,EAAGtS,EAAE,IAAM,IAAI,CAACkI,SAAS,GAAGlI;IAAI,CAAC,CAAC,EAAEyR,UAAU,IAAK7W,CAAC,CAAC,QAAQ,EAAE;MAAEiX,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE,cAAc;MACtQ;MACAU,QAAQ,EAAE,CAACd,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC;MAAE,YAAY,EAAE,iEAAiE;MAAEe,OAAO,EAAEf,aAAa,GAAG,IAAI,CAAC/F,aAAa,GAAGpN,SAAS;MAAEgU,IAAI,EAAE;IAAS,CAAC,CAAE,EAAE7X,CAAC,CAAC,MAAM,EAAE;MAAEiX,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,CAAC;EACzQ;EACA,IAAI7R,EAAEA,CAAA,EAAG;IAAE,OAAOhF,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW4X,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;MAC5B,SAAS,EAAE,CAAC,gBAAgB;IAChC,CAAC;EAAE;AACP,CAAC;AACD,MAAMtG,aAAa,GAAG;EAClBuG,kBAAkB,EAAE,iBAAiB;EACrCC,mBAAmB,EAAE,kBAAkB;EACvCC,mBAAmB,EAAE,kBAAkB;EACvCC,kBAAkB,EAAE;AACxB,CAAC;AACDvI,KAAK,CAACxL,KAAK,GAAG;EACVgU,GAAG,EAAE3I,iBAAiB;EACtB4I,EAAE,EAAE1I;AACR,CAAC;AAED,SAASC,KAAK,IAAI0I,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}