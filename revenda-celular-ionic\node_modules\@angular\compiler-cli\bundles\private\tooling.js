
      import {createRequire as __cjsCompatRequire} from 'module';
      const require = __cjsCompatRequire(import.meta.url);
    
import {
  GLOBAL_DEFS_FOR_TERSER,
  GLOBAL_DEFS_FOR_TERSER_WITH_AOT,
  constructorParametersDownlevelTransform
} from "../chunk-4C5CBXZ4.js";
import "../chunk-KJL3BXPI.js";
import "../chunk-GEKWXKEG.js";
import "../chunk-RMJDBMCH.js";
import "../chunk-Q2WE7ECN.js";
import "../chunk-37JMVF7H.js";
import "../chunk-KPQ72R34.js";
export {
  GLOBAL_DEFS_FOR_TERSER,
  GLOBAL_DEFS_FOR_TERSER_WITH_AOT,
  constructorParametersDownlevelTransform
};
//# sourceMappingURL=tooling.js.map
