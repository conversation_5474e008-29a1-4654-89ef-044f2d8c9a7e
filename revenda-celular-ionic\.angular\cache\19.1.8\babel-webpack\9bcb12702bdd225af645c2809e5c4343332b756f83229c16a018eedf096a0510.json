{"ast": null, "code": "var _PhonesPageRoutingModule;\nimport { RouterModule } from '@angular/router';\nimport { PhonesPage } from './phones.page';\nimport { PhoneFormComponent } from './phone-form/phone-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: PhonesPage\n}, {\n  path: 'new',\n  component: PhoneFormComponent\n}, {\n  path: 'edit/:id',\n  component: PhoneFormComponent\n}];\nexport class PhonesPageRoutingModule {}\n_PhonesPageRoutingModule = PhonesPageRoutingModule;\n_PhonesPageRoutingModule.ɵfac = function PhonesPageRoutingModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _PhonesPageRoutingModule)();\n};\n_PhonesPageRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _PhonesPageRoutingModule\n});\n_PhonesPageRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [RouterModule.forChild(routes), RouterModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PhonesPageRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "PhonesPage", "PhoneFormComponent", "routes", "path", "component", "PhonesPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\phones\\phones-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { PhonesPage } from './phones.page';\r\nimport { PhoneFormComponent } from './phone-form/phone-form.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: PhonesPage\r\n  },\r\n  {\r\n    path: 'new',\r\n    component: PhoneFormComponent\r\n  },\r\n  {\r\n    path: 'edit/:id',\r\n    component: PhoneFormComponent,\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class PhonesPageRoutingModule {}\r\n"], "mappings": ";AACA,SAAiBA,YAAY,QAAQ,iBAAiB;AAEtD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,kBAAkB,QAAQ,mCAAmC;;;AAEtE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,uBAAuB;2BAAvBA,uBAAuB;;mCAAvBA,wBAAuB;AAAA;;QAAvBA;AAAuB;;YAHxBN,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC,EAC7BH,YAAY;AAAA;;2EAEXM,uBAAuB;IAAAE,OAAA,GAAAC,EAAA,CAAAT,YAAA;IAAAU,OAAA,GAFxBV,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}