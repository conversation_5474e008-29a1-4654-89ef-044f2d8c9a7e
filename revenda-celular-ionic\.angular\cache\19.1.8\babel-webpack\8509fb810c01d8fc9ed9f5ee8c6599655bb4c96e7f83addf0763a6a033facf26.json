{"ast": null, "code": "var _AccessoriesPageModule;\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { AccessoriesPageRoutingModule } from './accessories-routing.module';\nimport { AccessoriesPage } from './accessories.page';\nimport { AccessoryFormComponent } from './accessory-form/accessory-form.component';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { HttpClientModule } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nexport class AccessoriesPageModule {}\n_AccessoriesPageModule = AccessoriesPageModule;\n_AccessoriesPageModule.ɵfac = function AccessoriesPageModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AccessoriesPageModule)();\n};\n_AccessoriesPageModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _AccessoriesPageModule\n});\n_AccessoriesPageModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, AccessoriesPageRoutingModule, HttpClientModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AccessoriesPageModule, {\n    declarations: [AccessoriesPage, AccessoryFormComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, AccessoriesPageRoutingModule, MaskitoDirective, HttpClientModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "IonicModule", "AccessoriesPageRoutingModule", "AccessoriesPage", "AccessoryFormComponent", "MaskitoDirective", "HttpClientModule", "AccessoriesPageModule", "declarations", "imports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\accessories\\accessories.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { AccessoriesPageRoutingModule } from './accessories-routing.module';\r\n\r\nimport { AccessoriesPage } from './accessories.page';\r\nimport { AccessoryFormComponent } from './accessory-form/accessory-form.component';\r\nimport { MaskitoDirective } from '@maskito/angular';\r\nimport { HttpClientModule } from '@angular/common/http';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    IonicModule,\r\n    AccessoriesPageRoutingModule,\r\n    MaskitoDirective,\r\n    HttpClientModule,\r\n  ],\r\n  declarations: [\r\n    AccessoriesPage,\r\n    AccessoryFormComponent,\r\n  ]\r\n})\r\nexport class AccessoriesPageModule { }\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,4BAA4B,QAAQ,8BAA8B;AAE3E,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,gBAAgB,QAAQ,sBAAsB;;AAiBvD,OAAM,MAAOC,qBAAqB;yBAArBA,qBAAqB;;mCAArBA,sBAAqB;AAAA;;QAArBA;AAAqB;;YAb9BT,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW,EACXC,4BAA4B,EAE5BI,gBAAgB;AAAA;;2EAOPC,qBAAqB;IAAAC,YAAA,GAJ9BL,eAAe,EACfC,sBAAsB;IAAAK,OAAA,GAVtBX,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW,EACXC,4BAA4B,EAC5BG,gBAAgB,EAChBC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}