"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAccessoryPhoneCompatibilityTable1703000004000 = void 0;
const typeorm_1 = require("typeorm");
class CreateAccessoryPhoneCompatibilityTable1703000004000 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'accessory_phone_compatibility',
            columns: [
                {
                    name: 'accessory_id',
                    type: 'int',
                    isPrimary: true,
                },
                {
                    name: 'phone_id',
                    type: 'int',
                    isPrimary: true,
                },
            ],
        }), true);
        await queryRunner.createForeignKey('accessory_phone_compatibility', new typeorm_1.TableForeignKey({
            columnNames: ['accessory_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'accessories',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        }));
        await queryRunner.createForeignKey('accessory_phone_compatibility', new typeorm_1.TableForeignKey({
            columnNames: ['phone_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'phones',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('accessory_phone_compatibility');
    }
}
exports.CreateAccessoryPhoneCompatibilityTable1703000004000 = CreateAccessoryPhoneCompatibilityTable1703000004000;
//# sourceMappingURL=1703000004000-CreateAccessoryPhoneCompatibilityTable.js.map