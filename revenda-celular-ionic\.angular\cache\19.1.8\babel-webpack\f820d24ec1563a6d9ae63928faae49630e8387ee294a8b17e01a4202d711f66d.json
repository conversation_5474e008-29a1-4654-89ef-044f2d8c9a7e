{"ast": null, "code": "import { maskitoUpdateElement, MASKITO_DEFAULT_OPTIONS, maskitoTransform } from '@maskito/core';\n\n/**\n * Clamps a value between two inclusive limits\n *\n * @param value\n * @param min lower limit\n * @param max upper limit\n */\nfunction clamp(value, min, max) {\n  const clampedValue = Math.min(Number(max), Math.max(Number(min), Number(value)));\n  return value instanceof Date ? new Date(clampedValue) : clampedValue;\n}\nfunction countDigits(str) {\n  return str.replaceAll(/\\W/g, '').length;\n}\nfunction appendDate(initialDate, {\n  day,\n  month,\n  year\n} = {}) {\n  const date = new Date(initialDate);\n  if (day) {\n    date.setDate(date.getDate() + day);\n  }\n  if (month) {\n    date.setMonth(date.getMonth() + month);\n  }\n  if (year) {\n    date.setFullYear(date.getFullYear() + year);\n  }\n  return date;\n}\nconst getDateSegmentValueLength = dateString => {\n  var _a, _b, _c, _d, _e, _f;\n  return {\n    day: (_b = (_a = dateString.match(/d/g)) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0,\n    month: (_d = (_c = dateString.match(/m/g)) === null || _c === void 0 ? void 0 : _c.length) !== null && _d !== void 0 ? _d : 0,\n    year: (_f = (_e = dateString.match(/y/g)) === null || _e === void 0 ? void 0 : _e.length) !== null && _f !== void 0 ? _f : 0\n  };\n};\nfunction dateToSegments(date) {\n  return {\n    day: String(date.getDate()).padStart(2, '0'),\n    month: String(date.getMonth() + 1).padStart(2, '0'),\n    year: String(date.getFullYear()).padStart(4, '0'),\n    hours: String(date.getHours()).padStart(2, '0'),\n    minutes: String(date.getMinutes()).padStart(2, '0'),\n    seconds: String(date.getSeconds()).padStart(2, '0'),\n    milliseconds: String(date.getMilliseconds()).padStart(3, '0')\n  };\n}\nconst ALL_POSSIBLE_SEGMENTS = ['day', 'month', 'year'];\nfunction getDateSegmentsOrder(template) {\n  return [...ALL_POSSIBLE_SEGMENTS].sort((a, b) => template.indexOf(a[0]) > template.indexOf(b[0]) ? 1 : -1);\n}\nfunction getFirstCompleteDate(dateString, dateModeTemplate) {\n  const digitsInDate = countDigits(dateModeTemplate);\n  const [completeDate = ''] = new RegExp(`(\\\\D*\\\\d){${digitsInDate}}`).exec(dateString) || [];\n  return completeDate;\n}\nfunction isDateStringComplete(dateString, dateModeTemplate) {\n  if (dateString.length < dateModeTemplate.length) {\n    return false;\n  }\n  return dateString.split(/\\D/).every(segment => !/^0+$/.exec(segment));\n}\nfunction parseDateRangeString(dateRange, dateModeTemplate, rangeSeparator) {\n  const digitsInDate = countDigits(dateModeTemplate);\n  return dateRange.replace(rangeSeparator, '').match(new RegExp(`(\\\\D*\\\\d[^\\\\d\\\\s]*){1,${digitsInDate}}`, 'g')) || [];\n}\nfunction parseDateString(dateString, fullMode) {\n  const cleanMode = fullMode.replaceAll(/[^dmy]/g, '');\n  const onlyDigitsDate = dateString.replaceAll(/\\D+/g, '');\n  const dateSegments = {\n    day: onlyDigitsDate.slice(cleanMode.indexOf('d'), cleanMode.lastIndexOf('d') + 1),\n    month: onlyDigitsDate.slice(cleanMode.indexOf('m'), cleanMode.lastIndexOf('m') + 1),\n    year: onlyDigitsDate.slice(cleanMode.indexOf('y'), cleanMode.lastIndexOf('y') + 1)\n  };\n  return Object.fromEntries(Object.entries(dateSegments).filter(([_, value]) => Boolean(value)).sort(([a], [b]) => fullMode.toLowerCase().indexOf(a.slice(0, 1)) > fullMode.toLowerCase().indexOf(b.slice(0, 1)) ? 1 : -1));\n}\nfunction segmentsToDate(parsedDate, parsedTime) {\n  var _a, _b, _c, _d, _e, _f, _g;\n  const year = ((_a = parsedDate.year) === null || _a === void 0 ? void 0 : _a.length) === 2 ? `20${parsedDate.year}` : parsedDate.year;\n  const date = new Date(Number(year !== null && year !== void 0 ? year : '0'), Number((_b = parsedDate.month) !== null && _b !== void 0 ? _b : '1') - 1, Number((_c = parsedDate.day) !== null && _c !== void 0 ? _c : '1'), Number((_d = parsedTime === null || parsedTime === void 0 ? void 0 : parsedTime.hours) !== null && _d !== void 0 ? _d : '0'), Number((_e = parsedTime === null || parsedTime === void 0 ? void 0 : parsedTime.minutes) !== null && _e !== void 0 ? _e : '0'), Number((_f = parsedTime === null || parsedTime === void 0 ? void 0 : parsedTime.seconds) !== null && _f !== void 0 ? _f : '0'), Number((_g = parsedTime === null || parsedTime === void 0 ? void 0 : parsedTime.milliseconds) !== null && _g !== void 0 ? _g : '0'));\n  // needed for years less than 1900\n  date.setFullYear(Number(year !== null && year !== void 0 ? year : '0'));\n  return date;\n}\nconst DATE_TIME_SEPARATOR = ', ';\nfunction toDateString({\n  day,\n  month,\n  year,\n  hours,\n  minutes,\n  seconds,\n  milliseconds\n}, {\n  dateMode,\n  dateTimeSeparator = DATE_TIME_SEPARATOR,\n  timeMode\n}) {\n  var _a;\n  const safeYear = ((_a = dateMode.match(/y/g)) === null || _a === void 0 ? void 0 : _a.length) === 2 ? year === null || year === void 0 ? void 0 : year.slice(-2) : year;\n  const fullMode = dateMode + (timeMode ? dateTimeSeparator + timeMode : '');\n  return fullMode.replaceAll(/d+/g, day !== null && day !== void 0 ? day : '').replaceAll(/m+/g, month !== null && month !== void 0 ? month : '').replaceAll(/y+/g, safeYear !== null && safeYear !== void 0 ? safeYear : '').replaceAll(/H+/g, hours !== null && hours !== void 0 ? hours : '').replaceAll('MSS', milliseconds !== null && milliseconds !== void 0 ? milliseconds : '').replaceAll(/M+/g, minutes !== null && minutes !== void 0 ? minutes : '').replaceAll(/S+/g, seconds !== null && seconds !== void 0 ? seconds : '').replaceAll(/^\\D+/g, '').replaceAll(/\\D+$/g, '');\n}\nconst DATE_SEGMENTS_MAX_VALUES = {\n  day: 31,\n  month: 12,\n  year: 9999\n};\n\n// eslint-disable-next-line i18n/no-russian-character\nconst DEFAULT_DECIMAL_PSEUDO_SEPARATORS = ['.', ',', 'б', 'ю'];\nconst DEFAULT_MIN_DATE = new Date('0001-01-01T00:00');\nconst DEFAULT_MAX_DATE = new Date('9999-12-31T23:59:59.999');\nconst DEFAULT_TIME_SEGMENT_MAX_VALUES = {\n  hours: 23,\n  minutes: 59,\n  seconds: 59,\n  milliseconds: 999\n};\nconst DEFAULT_TIME_SEGMENT_MIN_VALUES = {\n  hours: 0,\n  minutes: 0,\n  seconds: 0,\n  milliseconds: 0\n};\n\n/**\n * {@link https://unicode-table.com/en/00A0/ Non-breaking space}.\n */\nconst CHAR_NO_BREAK_SPACE = '\\u00A0';\n/**\n * {@link https://symbl.cc/en/200B/ Zero width space}.\n */\nconst CHAR_ZERO_WIDTH_SPACE = '\\u200B';\n/**\n * {@link https://unicode-table.com/en/2013/ EN dash}\n * is used to indicate a range of numbers or a span of time.\n * @example 2006–2022\n */\nconst CHAR_EN_DASH = '\\u2013';\n/**\n * {@link https://unicode-table.com/en/2014/ EM dash}\n * is used to mark a break in a sentence.\n * @example Taiga UI — powerful set of open source components for Angular\n * ___\n * Don't confuse with {@link CHAR_EN_DASH} or {@link CHAR_HYPHEN}!\n */\nconst CHAR_EM_DASH = '\\u2014';\n/**\n * {@link https://unicode-table.com/en/002D/ Hyphen (minus sign)}\n * is used to combine words.\n * @example well-behaved\n * ___\n * Don't confuse with {@link CHAR_EN_DASH} or {@link CHAR_EM_DASH}!\n */\nconst CHAR_HYPHEN = '\\u002D';\n/**\n * {@link https://unicode-table.com/en/2212/ Minus}\n * is used as math operator symbol or before negative digits.\n * ---\n * Can be used as `&minus;`. Don't confuse with {@link CHAR_HYPHEN}\n */\nconst CHAR_MINUS = '\\u2212';\n/**\n * {@link https://symbl.cc/en/30FC/ Katakana-Hiragana Prolonged Sound Mark}\n * is used as prolonged sounds in Japanese.\n */\nconst CHAR_JP_HYPHEN = '\\u30FC';\n/**\n * {@link https://symbl.cc/en/003A/ Colon}\n * is a punctuation mark that connects parts of a text logically.\n * ---\n * is also used as separator in time.\n */\nconst CHAR_COLON = '\\u003A';\n/**\n * {@link https://symbl.cc/en/FF1A/ Full-width colon}\n * is a full-width punctuation mark used to separate parts of a text commonly in Japanese.\n */\nconst CHAR_JP_COLON = '\\uFF1A';\nconst ANY_MERIDIEM_CHARACTER_RE = new RegExp(`[${CHAR_NO_BREAK_SPACE}APM]+$`, 'g');\nconst ALL_MERIDIEM_CHARACTERS_RE = new RegExp(`${CHAR_NO_BREAK_SPACE}[AP]M$`, 'g');\nconst TIME_FIXED_CHARACTERS = [':', '.'];\nconst TIME_SEGMENT_VALUE_LENGTHS = {\n  hours: 2,\n  minutes: 2,\n  seconds: 2,\n  milliseconds: 3\n};\nfunction validateDateString({\n  dateString,\n  dateModeTemplate,\n  dateSegmentsSeparator,\n  offset,\n  selection: [from, to]\n}) {\n  var _a, _b;\n  const parsedDate = parseDateString(dateString, dateModeTemplate);\n  const dateSegments = Object.entries(parsedDate);\n  const segmentsOrder = getDateSegmentsOrder(dateModeTemplate);\n  const validatedDateSegments = {};\n  for (let i = 0; i < dateSegments.length; i++) {\n    const [segmentName, segmentValue] = dateSegments[i];\n    const validatedDate = toDateString(validatedDateSegments, {\n      dateMode: dateModeTemplate\n    });\n    const maxSegmentValue = DATE_SEGMENTS_MAX_VALUES[segmentName];\n    const fantomSeparator = validatedDate.length && dateSegmentsSeparator.length;\n    const lastSegmentDigitIndex = offset + validatedDate.length + fantomSeparator + getDateSegmentValueLength(dateModeTemplate)[segmentName];\n    const isLastSegmentDigitAdded = lastSegmentDigitIndex >= from && lastSegmentDigitIndex === to;\n    if (isLastSegmentDigitAdded && Number(segmentValue) > Number(maxSegmentValue)) {\n      const nextSegment = segmentsOrder[segmentsOrder.indexOf(segmentName) + 1];\n      if (!nextSegment || nextSegment === 'year') {\n        // 31.1|0.2010 => Type 9 => 31.1|0.2010\n        return {\n          validatedDateString: '',\n          updatedSelection: [from, to]\n        }; // prevent changes\n      }\n      validatedDateSegments[segmentName] = `0${segmentValue.slice(0, lastSegmentDigitIndex)}`;\n      dateSegments[i + 1] = [nextSegment, segmentValue.slice(-1) + ((_b = (_a = dateSegments[i + 1]) === null || _a === void 0 ? void 0 : _a[1]) !== null && _b !== void 0 ? _b : '').slice(1)];\n      continue;\n    }\n    if (isLastSegmentDigitAdded && Number(segmentValue) < 1) {\n      // 31.0|1.2010 => Type 0 => 31.0|1.2010\n      return {\n        validatedDateString: '',\n        updatedSelection: [from, to]\n      }; // prevent changes\n    }\n    validatedDateSegments[segmentName] = segmentValue;\n  }\n  const validatedDateString = toDateString(validatedDateSegments, {\n    dateMode: dateModeTemplate\n  });\n  const addedDateSegmentSeparators = validatedDateString.length - dateString.length;\n  return {\n    validatedDateString,\n    updatedSelection: [from + addedDateSegmentSeparators, to + addedDateSegmentSeparators]\n  };\n}\nfunction identity(x) {\n  return x;\n}\n// eslint-disable-next-line  @typescript-eslint/no-empty-function\nfunction noop() {}\n\n/**\n * Copy-pasted solution from lodash\n * @see https://lodash.com/docs/4.17.15#escapeRegExp\n */\nconst reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\nconst reHasRegExpChar = new RegExp(reRegExpChar.source);\nfunction escapeRegExp(str) {\n  return str && reHasRegExpChar.test(str) ? str.replaceAll(reRegExpChar, String.raw`\\$&`) : str;\n}\nfunction extractAffixes(value, {\n  prefix,\n  postfix\n}) {\n  var _a, _b;\n  const prefixRegExp = new RegExp(`^${escapeRegExp(prefix)}`);\n  const postfixRegExp = new RegExp(`${escapeRegExp(postfix)}$`);\n  const [extractedPrefix = ''] = (_a = value.match(prefixRegExp)) !== null && _a !== void 0 ? _a : [];\n  const [extractedPostfix = ''] = (_b = value.match(postfixRegExp)) !== null && _b !== void 0 ? _b : [];\n  return {\n    extractedPrefix,\n    extractedPostfix,\n    cleanValue: extractedPrefix || extractedPostfix ? value.slice(extractedPrefix.length, extractedPostfix.length ? -extractedPostfix.length : Infinity) : value\n  };\n}\nfunction findCommonBeginningSubstr(a, b) {\n  let res = '';\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return res;\n    }\n    res += a[i];\n  }\n  return res;\n}\nfunction isEmpty(entity) {\n  return !entity || typeof entity === 'object' && Object.keys(entity).length === 0;\n}\nconst ALL_ZEROES_RE = /^0+$/;\nfunction padWithZeroesUntilValid(segmentValue, paddedMaxValue, prefixedZeroesCount = 0) {\n  const paddedSegmentValue = segmentValue.padEnd(paddedMaxValue.length, '0');\n  if (Number(paddedSegmentValue) <= Number(paddedMaxValue)) {\n    return {\n      validatedSegmentValue: segmentValue,\n      prefixedZeroesCount\n    };\n  }\n  if (paddedSegmentValue.endsWith('0')) {\n    // 00:|00 => Type 9 => 00:09|\n    return padWithZeroesUntilValid(`0${segmentValue.slice(0, paddedMaxValue.length - 1)}`, paddedMaxValue, prefixedZeroesCount + 1);\n  }\n  const valueWithoutLastChar = segmentValue.slice(0, paddedMaxValue.length - 1);\n  if (ALL_ZEROES_RE.exec(valueWithoutLastChar)) {\n    return {\n      validatedSegmentValue: '',\n      prefixedZeroesCount\n    };\n  }\n  // |19:00 => Type 2 => 2|0:00\n  return padWithZeroesUntilValid(`${valueWithoutLastChar}0`, paddedMaxValue, prefixedZeroesCount);\n}\n\n/**\n * Replace fullwidth colon with half width colon\n * @param fullWidthColon full width colon\n * @returns processed half width colon\n */\nfunction toHalfWidthColon(fullWidthColon) {\n  return fullWidthColon.replaceAll(new RegExp(CHAR_JP_COLON, 'g'), CHAR_COLON);\n}\n\n/**\n * Replace fullwidth numbers with half width number\n * @param fullWidthNumber full width number\n * @returns processed half width number\n */\nfunction toHalfWidthNumber(fullWidthNumber) {\n  return fullWidthNumber.replaceAll(/[０-９]/g, s => String.fromCharCode(s.charCodeAt(0) - 0xfee0));\n}\n\n/**\n * Convert full width colon (：) to half width one (:)\n */\nfunction createColonConvertPreprocessor() {\n  return ({\n    elementState,\n    data\n  }) => {\n    const {\n      value,\n      selection\n    } = elementState;\n    return {\n      elementState: {\n        selection,\n        value: toHalfWidthColon(value)\n      },\n      data: toHalfWidthColon(data)\n    };\n  };\n}\nfunction createDateSegmentsZeroPaddingPostprocessor({\n  dateModeTemplate,\n  dateSegmentSeparator,\n  splitFn,\n  uniteFn\n}) {\n  return ({\n    value,\n    selection\n  }) => {\n    var _a;\n    const [from, to] = selection;\n    const {\n      dateStrings,\n      restPart = ''\n    } = splitFn(value);\n    const validatedDateStrings = [];\n    let caretShift = 0;\n    dateStrings.forEach(dateString => {\n      const parsedDate = parseDateString(dateString, dateModeTemplate);\n      const dateSegments = Object.entries(parsedDate);\n      const validatedDateSegments = dateSegments.reduce((acc, [segmentName, segmentValue]) => {\n        const {\n          validatedSegmentValue,\n          prefixedZeroesCount\n        } = padWithZeroesUntilValid(segmentValue, `${DATE_SEGMENTS_MAX_VALUES[segmentName]}`);\n        caretShift += prefixedZeroesCount;\n        return Object.assign(Object.assign({}, acc), {\n          [segmentName]: validatedSegmentValue\n        });\n      }, {});\n      validatedDateStrings.push(toDateString(validatedDateSegments, {\n        dateMode: dateModeTemplate\n      }));\n    });\n    const validatedValue = uniteFn(validatedDateStrings, value) + (((_a = dateStrings[dateStrings.length - 1]) === null || _a === void 0 ? void 0 : _a.endsWith(dateSegmentSeparator)) ? dateSegmentSeparator : '') + restPart;\n    if (caretShift && validatedValue.slice(to + caretShift, to + caretShift + dateSegmentSeparator.length) === dateSegmentSeparator) {\n      /**\n       * If `caretShift` > 0, it means that time segment was padded with zero.\n       * It is only possible if any character insertion happens.\n       * If caret is before `dateSegmentSeparator` => it should be moved after `dateSegmentSeparator`.\n       */\n      caretShift += dateSegmentSeparator.length;\n    }\n    return {\n      selection: [from + caretShift, to + caretShift],\n      value: validatedValue\n    };\n  };\n}\n\n/**\n * It replaces pseudo range separators with valid one.\n * @example '01.01.2000_11.11.2000' -> '01.01.2000 - 01.01.2000'\n * @example '01.01.2000_23:59' -> '01.01.2000, 23:59'\n */\nfunction createFirstDateEndSeparatorPreprocessor({\n  dateModeTemplate,\n  firstDateEndSeparator,\n  dateSegmentSeparator,\n  pseudoFirstDateEndSeparators\n}) {\n  return ({\n    elementState,\n    data\n  }) => {\n    const {\n      value,\n      selection\n    } = elementState;\n    const [from, to] = selection;\n    const firstCompleteDate = getFirstCompleteDate(value, dateModeTemplate);\n    const pseudoSeparators = pseudoFirstDateEndSeparators.filter(x => !firstDateEndSeparator.includes(x) && x !== dateSegmentSeparator);\n    const pseudoSeparatorsRE = new RegExp(`[${pseudoSeparators.join('')}]`, 'gi');\n    const newValue = firstCompleteDate && value.length > firstCompleteDate.length ? firstCompleteDate + value.slice(firstCompleteDate.length).replace(/^[\\D\\s]*/, firstDateEndSeparator) : value;\n    const caretShift = newValue.length - value.length;\n    return {\n      elementState: {\n        selection: [from + caretShift, to + caretShift],\n        value: newValue\n      },\n      data: data.replace(pseudoSeparatorsRE, firstDateEndSeparator)\n    };\n  };\n}\n\n/**\n * Convert full width numbers like １, ２ to half width numbers 1, 2\n */\nfunction createFullWidthToHalfWidthPreprocessor() {\n  return ({\n    elementState,\n    data\n  }) => {\n    const {\n      value,\n      selection\n    } = elementState;\n    return {\n      elementState: {\n        selection,\n        value: toHalfWidthNumber(value)\n      },\n      data: toHalfWidthNumber(data)\n    };\n  };\n}\nfunction createTimeMaskExpression(mode) {\n  return Array.from(mode.replace(' AA', '')).map(char => TIME_FIXED_CHARACTERS.includes(char) ? char : /\\d/).concat(mode.includes('AA') ? [CHAR_NO_BREAK_SPACE, /[AP]/i, /M/i] : []);\n}\nfunction padTimeSegments(timeSegments, pad) {\n  return Object.fromEntries(Object.entries(timeSegments).map(([segmentName, segmentValue]) => [segmentName, pad(String(segmentValue), TIME_SEGMENT_VALUE_LENGTHS[segmentName])]));\n}\nfunction padStartTimeSegments(timeSegments) {\n  return padTimeSegments(timeSegments, (value, length) => value.padStart(length, '0'));\n}\nconst SEGMENT_FULL_NAME = {\n  HH: 'hours',\n  MM: 'minutes',\n  SS: 'seconds',\n  MSS: 'milliseconds'\n};\n/**\n * @param timeString can be with/without fixed characters\n */\nfunction parseTimeString(timeString, timeMode) {\n  const onlyDigits = timeString.replaceAll(/\\D+/g, '');\n  let offset = 0;\n  return Object.fromEntries(timeMode.split(/\\W/).filter(segmentAbbr => SEGMENT_FULL_NAME[segmentAbbr]).map(segmentAbbr => {\n    const segmentValue = onlyDigits.slice(offset, offset + segmentAbbr.length);\n    offset += segmentAbbr.length;\n    return [SEGMENT_FULL_NAME[segmentAbbr], segmentValue];\n  }));\n}\nconst LEADING_NON_DIGITS = /^\\D*/;\nconst TRAILING_NON_DIGITS = /\\D*$/;\nfunction toTimeString({\n  hours = '',\n  minutes = '',\n  seconds = '',\n  milliseconds = ''\n}) {\n  return `${hours}:${minutes}:${seconds}.${milliseconds}`.replace(LEADING_NON_DIGITS, '').replace(TRAILING_NON_DIGITS, '');\n}\nconst TRAILING_TIME_SEGMENT_SEPARATOR_REG = new RegExp(`[${TIME_FIXED_CHARACTERS.map(escapeRegExp).join('')}]$`);\n/**\n * Pads invalid time segment with zero to make it valid.\n * @example 00:|00 => Type 9 (too much for the first digit of minutes) => 00:09|\n * @example |19:00 => Type 2 (29 - invalid value for hours) => 2|0:00\n */\nfunction enrichTimeSegmentsWithZeroes({\n  value,\n  selection\n}, {\n  mode,\n  timeSegmentMaxValues = DEFAULT_TIME_SEGMENT_MAX_VALUES\n}) {\n  const [from, to] = selection;\n  const parsedTime = parseTimeString(value, mode);\n  const possibleTimeSegments = Object.entries(parsedTime);\n  const paddedMaxValues = padStartTimeSegments(timeSegmentMaxValues);\n  const validatedTimeSegments = {};\n  let paddedZeroes = 0;\n  for (const [segmentName, segmentValue] of possibleTimeSegments) {\n    const maxSegmentValue = paddedMaxValues[segmentName];\n    const {\n      validatedSegmentValue,\n      prefixedZeroesCount\n    } = padWithZeroesUntilValid(segmentValue, String(maxSegmentValue));\n    paddedZeroes += prefixedZeroesCount;\n    validatedTimeSegments[segmentName] = validatedSegmentValue;\n  }\n  // trailing segment separators or meridiem characters\n  const [trailingNonDigitCharacters = ''] = value.match(/\\D+$/g) || [];\n  const validatedTimeString = toTimeString(validatedTimeSegments) + trailingNonDigitCharacters;\n  const addedDateSegmentSeparators = Math.max(validatedTimeString.length - value.length, 0);\n  let newFrom = from + paddedZeroes + addedDateSegmentSeparators;\n  let newTo = to + paddedZeroes + addedDateSegmentSeparators;\n  if (newFrom === newTo && paddedZeroes &&\n  // if next character after cursor is time segment separator\n  validatedTimeString.slice(0, newTo + 1).match(TRAILING_TIME_SEGMENT_SEPARATOR_REG)) {\n    newFrom++;\n    newTo++;\n  }\n  return {\n    value: validatedTimeString,\n    selection: [newFrom, newTo]\n  };\n}\nfunction padEndTimeSegments(timeSegments) {\n  return padTimeSegments(timeSegments, (value, length) => value.padEnd(length, '0'));\n}\n\n/**\n * Prevent insertion if any time segment will become invalid\n * (and even zero padding won't help with it).\n * @example 2|0:00 => Type 9 => 2|0:00\n */\nfunction createInvalidTimeSegmentInsertionPreprocessor({\n  timeMode,\n  timeSegmentMinValues = DEFAULT_TIME_SEGMENT_MIN_VALUES,\n  timeSegmentMaxValues = DEFAULT_TIME_SEGMENT_MAX_VALUES,\n  parseValue = x => ({\n    timeString: x\n  })\n}) {\n  const invalidCharsRegExp = new RegExp(`[^\\\\d${TIME_FIXED_CHARACTERS.map(escapeRegExp).join('')}]+`);\n  return ({\n    elementState,\n    data\n  }, actionType) => {\n    if (actionType !== 'insert') {\n      return {\n        elementState,\n        data\n      };\n    }\n    const {\n      value,\n      selection\n    } = elementState;\n    const [from, rawTo] = selection;\n    const newCharacters = data.replace(invalidCharsRegExp, '');\n    const to = rawTo + newCharacters.length; // to be conformed with `overwriteMode: replace`\n    const newPossibleValue = value.slice(0, from) + newCharacters + value.slice(to);\n    const {\n      timeString,\n      restValue = ''\n    } = parseValue(newPossibleValue);\n    const timeSegments = Object.entries(parseTimeString(timeString, timeMode));\n    let offset = restValue.length;\n    for (const [segmentName, stringifiedSegmentValue] of timeSegments) {\n      const minSegmentValue = timeSegmentMinValues[segmentName];\n      const maxSegmentValue = timeSegmentMaxValues[segmentName];\n      const segmentValue = Number(stringifiedSegmentValue);\n      const lastSegmentDigitIndex = offset + TIME_SEGMENT_VALUE_LENGTHS[segmentName];\n      if (lastSegmentDigitIndex >= from && lastSegmentDigitIndex <= to && segmentValue !== clamp(segmentValue, minSegmentValue, maxSegmentValue)) {\n        return {\n          elementState,\n          data: ''\n        }; // prevent insertion\n      }\n      offset += stringifiedSegmentValue.length +\n      // any time segment separator\n      1;\n    }\n    return {\n      elementState,\n      data\n    };\n  };\n}\nfunction createMeridiemPreprocessor(timeMode) {\n  if (!timeMode.includes('AA')) {\n    return identity;\n  }\n  const mainMeridiemCharRE = /^[AP]$/gi;\n  return ({\n    elementState,\n    data\n  }) => {\n    const {\n      value,\n      selection\n    } = elementState;\n    const newValue = value.toUpperCase();\n    const newData = data.toUpperCase();\n    if (newValue.match(ALL_MERIDIEM_CHARACTERS_RE) && newData.match(mainMeridiemCharRE)) {\n      return {\n        elementState: {\n          value: newValue.replaceAll(ALL_MERIDIEM_CHARACTERS_RE, ''),\n          selection\n        },\n        data: `${newData}M`\n      };\n    }\n    return {\n      elementState: {\n        selection,\n        value: newValue\n      },\n      data: newData\n    };\n  };\n}\nfunction createMeridiemPostprocessor(timeMode) {\n  if (!timeMode.includes('AA')) {\n    return identity;\n  }\n  return ({\n    value,\n    selection\n  }, initialElementState) => {\n    if (!value.match(ANY_MERIDIEM_CHARACTER_RE) || value.match(ALL_MERIDIEM_CHARACTERS_RE)) {\n      return {\n        value,\n        selection\n      };\n    }\n    const [from, to] = selection;\n    // any meridiem character was deleted\n    if (initialElementState.value.match(ALL_MERIDIEM_CHARACTERS_RE)) {\n      const newValue = value.replace(ANY_MERIDIEM_CHARACTER_RE, '');\n      return {\n        value: newValue,\n        selection: [Math.min(from, newValue.length), Math.min(to, newValue.length)]\n      };\n    }\n    const fullMeridiem = `${CHAR_NO_BREAK_SPACE}${value.includes('P') ? 'P' : 'A'}M`;\n    const newValue = value.replace(ANY_MERIDIEM_CHARACTER_RE, x => x !== CHAR_NO_BREAK_SPACE ? fullMeridiem : x);\n    return {\n      value: newValue,\n      selection: to >= newValue.indexOf(fullMeridiem) ? [newValue.length, newValue.length] : selection\n    };\n  };\n}\nfunction raiseSegmentValueToMin(segments, fullMode) {\n  const segmentsLength = getDateSegmentValueLength(fullMode);\n  return Object.fromEntries(Object.entries(segments).map(([key, value]) => {\n    const segmentLength = segmentsLength[key];\n    return [key, value.length === segmentLength && /^0+$/.exec(value) ? '1'.padStart(segmentLength, '0') : value];\n  }));\n}\nconst LEAP_YEAR = '1972';\nfunction createMinMaxDatePostprocessor({\n  dateModeTemplate,\n  min = DEFAULT_MIN_DATE,\n  max = DEFAULT_MAX_DATE,\n  rangeSeparator = '',\n  dateSegmentSeparator = '.'\n}) {\n  return ({\n    value,\n    selection\n  }) => {\n    const endsWithRangeSeparator = rangeSeparator && value.endsWith(rangeSeparator);\n    const dateStrings = parseDateRangeString(value, dateModeTemplate, rangeSeparator);\n    let validatedValue = '';\n    for (const dateString of dateStrings) {\n      validatedValue += validatedValue ? rangeSeparator : '';\n      const parsedDate = parseDateString(dateString, dateModeTemplate);\n      if (!isDateStringComplete(dateString, dateModeTemplate)) {\n        const fixedDate = raiseSegmentValueToMin(parsedDate, dateModeTemplate);\n        const fixedValue = toDateString(fixedDate, {\n          dateMode: dateModeTemplate\n        });\n        const tail = dateString.endsWith(dateSegmentSeparator) ? dateSegmentSeparator : '';\n        validatedValue += fixedValue + tail;\n        continue;\n      }\n      const date = segmentsToDate(Object.assign({\n        year: LEAP_YEAR\n      }, parsedDate));\n      const clampedDate = clamp(date, min, max);\n      validatedValue += toDateString(dateToSegments(clampedDate), {\n        dateMode: dateModeTemplate\n      });\n    }\n    return {\n      selection,\n      value: validatedValue + (endsWithRangeSeparator ? rangeSeparator : '')\n    };\n  };\n}\nfunction normalizeDatePreprocessor({\n  dateModeTemplate,\n  dateSegmentsSeparator,\n  rangeSeparator = '',\n  dateTimeSeparator = DATE_TIME_SEPARATOR\n}) {\n  return ({\n    elementState,\n    data\n  }) => {\n    const templateSegments = dateModeTemplate.split(dateSegmentsSeparator);\n    const includesTime = data.includes(dateTimeSeparator);\n    const dateSegments = data.slice(0, includesTime ? data.indexOf(dateTimeSeparator) : Infinity).split(/\\D/).filter(Boolean);\n    if (!dateSegments.length || dateSegments.length % templateSegments.length !== 0) {\n      return {\n        elementState,\n        data\n      };\n    }\n    const dates = dateSegments.reduce((dates, segment, index) => {\n      var _a;\n      const template = (_a = templateSegments[index % templateSegments.length]) !== null && _a !== void 0 ? _a : '';\n      const dateIndex = Math.trunc(index / templateSegments.length);\n      const isLastDateSegment = index % templateSegments.length === templateSegments.length - 1;\n      if (!dates[dateIndex]) {\n        dates[dateIndex] = '';\n      }\n      dates[dateIndex] += isLastDateSegment ? segment : `${segment.padStart(template.length, '0')}${dateSegmentsSeparator}`;\n      return dates;\n    }, []);\n    return {\n      elementState,\n      data: includesTime ? `${dates[0]}${data.slice(data.indexOf(dateTimeSeparator))}` : dates.join(rangeSeparator)\n    };\n  };\n}\nfunction maskitoPostfixPostprocessorGenerator(postfix) {\n  const postfixRE = new RegExp(`${escapeRegExp(postfix)}$`);\n  return postfix ? ({\n    value,\n    selection\n  }, initialElementState) => {\n    if (!value && !initialElementState.value.endsWith(postfix)) {\n      // cases when developer wants input to be empty (programmatically)\n      return {\n        value,\n        selection\n      };\n    }\n    if (!value.endsWith(postfix) && !initialElementState.value.endsWith(postfix)) {\n      return {\n        selection,\n        value: value + postfix\n      };\n    }\n    const initialValueBeforePostfix = initialElementState.value.replace(postfixRE, '');\n    const postfixWasModified = initialElementState.selection[1] > initialValueBeforePostfix.length;\n    const alreadyExistedValueBeforePostfix = findCommonBeginningSubstr(initialValueBeforePostfix, value);\n    return {\n      selection,\n      value: Array.from(postfix).reverse().reduce((newValue, char, index) => {\n        const i = newValue.length - 1 - index;\n        const isInitiallyMirroredChar = alreadyExistedValueBeforePostfix[i] === char && postfixWasModified;\n        return newValue[i] !== char || isInitiallyMirroredChar ? newValue.slice(0, i + 1) + char + newValue.slice(i + 1) : newValue;\n      }, value)\n    };\n  } : identity;\n}\nfunction maskitoPrefixPostprocessorGenerator(prefix) {\n  return prefix ? ({\n    value,\n    selection\n  }, initialElementState) => {\n    if (value.startsWith(prefix) ||\n    // already valid\n    !value && !initialElementState.value.startsWith(prefix) // cases when developer wants input to be empty\n    ) {\n      return {\n        value,\n        selection\n      };\n    }\n    const [from, to] = selection;\n    const prefixedValue = Array.from(prefix).reduce((modifiedValue, char, i) => modifiedValue[i] === char ? modifiedValue : modifiedValue.slice(0, i) + char + modifiedValue.slice(i), value);\n    const addedCharsCount = prefixedValue.length - value.length;\n    return {\n      selection: [from + addedCharsCount, to + addedCharsCount],\n      value: prefixedValue\n    };\n  } : identity;\n}\nfunction createValidDatePreprocessor({\n  dateModeTemplate,\n  dateSegmentsSeparator,\n  rangeSeparator = ''\n}) {\n  return ({\n    elementState,\n    data\n  }) => {\n    const {\n      value,\n      selection\n    } = elementState;\n    if (data === dateSegmentsSeparator) {\n      return {\n        elementState,\n        data: selection[0] === value.length ? data : ''\n      };\n    }\n    const newCharacters = data.replaceAll(new RegExp(`[^\\\\d${escapeRegExp(dateSegmentsSeparator)}${rangeSeparator}]`, 'g'), '');\n    if (!newCharacters) {\n      return {\n        elementState,\n        data: ''\n      };\n    }\n    const [from, rawTo] = selection;\n    let to = rawTo + data.length;\n    const newPossibleValue = value.slice(0, from) + newCharacters + value.slice(to);\n    const dateStrings = parseDateRangeString(newPossibleValue, dateModeTemplate, rangeSeparator);\n    let validatedValue = '';\n    const hasRangeSeparator = Boolean(rangeSeparator) && newPossibleValue.includes(rangeSeparator);\n    for (const dateString of dateStrings) {\n      const {\n        validatedDateString,\n        updatedSelection\n      } = validateDateString({\n        dateString,\n        dateModeTemplate,\n        dateSegmentsSeparator,\n        offset: validatedValue.length,\n        selection: [from, to]\n      });\n      if (dateString && !validatedDateString) {\n        return {\n          elementState,\n          data: ''\n        }; // prevent changes\n      }\n      to = updatedSelection[1];\n      validatedValue += hasRangeSeparator && !validatedValue ? validatedDateString + rangeSeparator : validatedDateString;\n    }\n    const newData = validatedValue.slice(from, to);\n    return {\n      elementState: {\n        selection,\n        value: validatedValue.slice(0, from) + newData.split(dateSegmentsSeparator).map(segment => '0'.repeat(segment.length)).join(dateSegmentsSeparator) + validatedValue.slice(to)\n      },\n      data: newData\n    };\n  };\n}\nfunction maskitoEventHandler(name, handler, eventListenerOptions) {\n  return (element, maskitoOptions) => {\n    const listener = () => handler(element, maskitoOptions);\n    element.addEventListener(name, listener, eventListenerOptions);\n    return () => element.removeEventListener(name, listener, eventListenerOptions);\n  };\n}\nfunction maskitoAddOnFocusPlugin(value) {\n  return maskitoEventHandler('focus', element => {\n    if (!element.value) {\n      maskitoUpdateElement(element, value);\n    }\n  });\n}\nfunction maskitoSelectionChangeHandler(handler) {\n  return (element, options) => {\n    const document = element.ownerDocument;\n    let isPointerDown = 0;\n    const onPointerDown = () => isPointerDown++;\n    const onPointerUp = () => {\n      isPointerDown = Math.max(--isPointerDown, 0);\n    };\n    const listener = () => {\n      if (!element.matches(':focus')) {\n        return;\n      }\n      if (isPointerDown) {\n        return document.addEventListener('mouseup', listener, {\n          once: true,\n          passive: true\n        });\n      }\n      handler(element, options);\n    };\n    document.addEventListener('selectionchange', listener, {\n      passive: true\n    });\n    // Safari does not fire `selectionchange` on focus after programmatic update of textfield value\n    element.addEventListener('focus', listener, {\n      passive: true\n    });\n    element.addEventListener('mousedown', onPointerDown, {\n      passive: true\n    });\n    document.addEventListener('mouseup', onPointerUp, {\n      passive: true\n    });\n    return () => {\n      document.removeEventListener('selectionchange', listener);\n      element.removeEventListener('focus', listener);\n      element.removeEventListener('mousedown', onPointerDown);\n      document.removeEventListener('mouseup', onPointerUp);\n    };\n  };\n}\nfunction maskitoCaretGuard(guard) {\n  return maskitoSelectionChangeHandler(element => {\n    var _a, _b;\n    const start = (_a = element.selectionStart) !== null && _a !== void 0 ? _a : 0;\n    const end = (_b = element.selectionEnd) !== null && _b !== void 0 ? _b : 0;\n    const [fromLimit, toLimit] = guard(element.value, [start, end]);\n    if (fromLimit > start || toLimit < end) {\n      element.setSelectionRange(clamp(start, fromLimit, toLimit), clamp(end, fromLimit, toLimit));\n    }\n  });\n}\nconst maskitoRejectEvent = element => {\n  const listener = () => {\n    const value = element.value;\n    element.addEventListener('beforeinput', event => {\n      if (event.defaultPrevented && value === element.value) {\n        element.dispatchEvent(new CustomEvent('maskitoReject', {\n          bubbles: true\n        }));\n      }\n    }, {\n      once: true\n    });\n  };\n  element.addEventListener('beforeinput', listener, true);\n  return () => element.removeEventListener('beforeinput', listener, true);\n};\nfunction maskitoRemoveOnBlurPlugin(value) {\n  return maskitoEventHandler('blur', element => {\n    if (element.value === value) {\n      maskitoUpdateElement(element, '');\n    }\n  });\n}\nfunction createMeridiemSteppingPlugin(meridiemStartIndex) {\n  if (meridiemStartIndex < 0) {\n    return noop;\n  }\n  return element => {\n    const listener = event => {\n      const caretIndex = Number(element.selectionStart);\n      const value = element.value.toUpperCase();\n      if (event.key !== 'ArrowUp' && event.key !== 'ArrowDown' || caretIndex < meridiemStartIndex) {\n        return;\n      }\n      event.preventDefault();\n      // eslint-disable-next-line no-nested-ternary\n      const meridiemMainCharacter = value.includes('A') ? 'P' : value.includes('P') || event.key === 'ArrowUp' ? 'A' : 'P';\n      const newMeridiem = `${CHAR_NO_BREAK_SPACE}${meridiemMainCharacter}M`;\n      maskitoUpdateElement(element, {\n        value: value.length === meridiemStartIndex ? value + newMeridiem : value.replace(ANY_MERIDIEM_CHARACTER_RE, newMeridiem),\n        selection: [caretIndex, caretIndex]\n      });\n    };\n    element.addEventListener('keydown', listener);\n    return () => element.removeEventListener('keydown', listener);\n  };\n}\nfunction createTimeSegmentsSteppingPlugin({\n  step,\n  fullMode,\n  timeSegmentMaxValues\n}) {\n  const segmentsIndexes = createTimeSegmentsIndexes(fullMode);\n  return step <= 0 ? noop : element => {\n    const listener = event => {\n      var _a;\n      if (event.key !== 'ArrowUp' && event.key !== 'ArrowDown') {\n        return;\n      }\n      event.preventDefault();\n      const selectionStart = (_a = element.selectionStart) !== null && _a !== void 0 ? _a : 0;\n      const activeSegment = getActiveSegment({\n        segmentsIndexes,\n        selectionStart\n      });\n      if (!activeSegment) {\n        return;\n      }\n      const updatedValue = updateSegmentValue({\n        selection: segmentsIndexes.get(activeSegment),\n        value: element.value,\n        toAdd: event.key === 'ArrowUp' ? step : -step,\n        max: timeSegmentMaxValues[activeSegment]\n      });\n      maskitoUpdateElement(element, {\n        value: updatedValue,\n        selection: [selectionStart, selectionStart]\n      });\n    };\n    element.addEventListener('keydown', listener);\n    return () => element.removeEventListener('keydown', listener);\n  };\n}\nfunction createTimeSegmentsIndexes(fullMode) {\n  return new Map([['hours', getSegmentRange(fullMode, 'HH')], ['milliseconds', getSegmentRange(fullMode, 'MSS')], ['minutes', getSegmentRange(fullMode, 'MM')], ['seconds', getSegmentRange(fullMode, 'SS')]]);\n}\nfunction getSegmentRange(mode, segment) {\n  const index = mode.indexOf(segment);\n  return index === -1 ? [-1, -1] : [index, index + segment.length];\n}\nfunction getActiveSegment({\n  segmentsIndexes,\n  selectionStart\n}) {\n  for (const [segmentName, segmentRange] of segmentsIndexes.entries()) {\n    const [from, to] = segmentRange;\n    if (from <= selectionStart && selectionStart <= to) {\n      return segmentName;\n    }\n  }\n  return null;\n}\nfunction updateSegmentValue({\n  selection,\n  value,\n  toAdd,\n  max\n}) {\n  const [from, to] = selection;\n  const segmentValue = Number(value.slice(from, to).padEnd(to - from, '0'));\n  const newSegmentValue = mod(segmentValue + toAdd, max + 1);\n  return value.slice(0, from) + String(newSegmentValue).padStart(to - from, '0') + value.slice(to, value.length);\n}\nfunction mod(value, max) {\n  if (value < 0) {\n    value += Math.floor(Math.abs(value) / max + 1) * max;\n  }\n  return value % max;\n}\nfunction maskitoWithPlaceholder(placeholder, focusedOnly = false) {\n  let lastClearValue = '';\n  let action = 'validation';\n  const removePlaceholder = value => {\n    for (let i = value.length - 1; i >= lastClearValue.length; i--) {\n      if (value[i] !== placeholder[i]) {\n        return value.slice(0, i + 1);\n      }\n    }\n    return value.slice(0, lastClearValue.length);\n  };\n  const plugins = [maskitoCaretGuard(value => [0, removePlaceholder(value).length])];\n  let focused = false;\n  if (focusedOnly) {\n    const focus = maskitoEventHandler('focus', element => {\n      focused = true;\n      maskitoUpdateElement(element, element.value + placeholder.slice(element.value.length));\n    }, {\n      capture: true\n    });\n    const blur = maskitoEventHandler('blur', element => {\n      focused = false;\n      maskitoUpdateElement(element, removePlaceholder(element.value));\n    }, {\n      capture: true\n    });\n    plugins.push(focus, blur);\n  }\n  return {\n    plugins,\n    removePlaceholder,\n    preprocessors: [({\n      elementState,\n      data\n    }, actionType) => {\n      action = actionType;\n      const {\n        value,\n        selection\n      } = elementState;\n      return {\n        elementState: {\n          selection,\n          value: removePlaceholder(value)\n        },\n        data\n      };\n    }],\n    postprocessors: [({\n      value,\n      selection\n    }, initialElementState) => {\n      lastClearValue = value;\n      const justPlaceholderRemoval = value + placeholder.slice(value.length, initialElementState.value.length) === initialElementState.value;\n      if (action === 'validation' && justPlaceholderRemoval) {\n        /**\n         * If `value` still equals to `initialElementState.value`,\n         * then it means that value is patched programmatically (from Maskito's plugin or externally).\n         * In this case, we don't want to mutate value and automatically add/remove placeholder.\n         * ___\n         * For example, developer wants to remove manually placeholder (+ do something else with value) on blur.\n         * Without this condition, placeholder will be unexpectedly added again.\n         */\n        return {\n          selection,\n          value: initialElementState.value\n        };\n      }\n      const newValue = focused || !focusedOnly ? value + placeholder.slice(value.length) : value;\n      if (newValue === initialElementState.value && action === 'deleteBackward') {\n        const [caretIndex] = initialElementState.selection;\n        return {\n          value: newValue,\n          selection: [caretIndex, caretIndex]\n        };\n      }\n      return {\n        value: newValue,\n        selection\n      };\n    }]\n  };\n}\nfunction createZeroPlaceholdersPreprocessor() {\n  return ({\n    elementState\n  }, actionType) => {\n    const {\n      value,\n      selection\n    } = elementState;\n    if (!value || isLastChar(value, selection)) {\n      return {\n        elementState\n      };\n    }\n    const [from, to] = selection;\n    const zeroes = value.slice(from, to).replaceAll(/\\d/g, '0');\n    const newValue = value.slice(0, from) + zeroes + value.slice(to);\n    if (!zeroes.replaceAll(/\\D/g, '')) {\n      return {\n        elementState\n      };\n    }\n    if (actionType === 'validation' || actionType === 'insert' && from === to) {\n      return {\n        elementState: {\n          selection,\n          value: newValue\n        }\n      };\n    }\n    return {\n      elementState: {\n        selection: actionType === 'deleteBackward' || actionType === 'insert' ? [from, from] : [to, to],\n        value: newValue\n      }\n    };\n  };\n}\nfunction isLastChar(value, [_, to]) {\n  return to === value.length;\n}\nfunction maskitoDateOptionsGenerator({\n  mode,\n  separator = '.',\n  max,\n  min\n}) {\n  const dateModeTemplate = mode.split('/').join(separator);\n  return Object.assign(Object.assign({}, MASKITO_DEFAULT_OPTIONS), {\n    mask: Array.from(dateModeTemplate).map(char => separator.includes(char) ? char : /\\d/),\n    overwriteMode: 'replace',\n    preprocessors: [createFullWidthToHalfWidthPreprocessor(), createZeroPlaceholdersPreprocessor(), normalizeDatePreprocessor({\n      dateModeTemplate,\n      dateSegmentsSeparator: separator\n    }), createValidDatePreprocessor({\n      dateModeTemplate,\n      dateSegmentsSeparator: separator\n    })],\n    postprocessors: [createDateSegmentsZeroPaddingPostprocessor({\n      dateModeTemplate,\n      dateSegmentSeparator: separator,\n      splitFn: value => ({\n        dateStrings: [value]\n      }),\n      uniteFn: ([dateString = '']) => dateString\n    }), createMinMaxDatePostprocessor({\n      min,\n      max,\n      dateModeTemplate,\n      dateSegmentSeparator: separator\n    })]\n  });\n}\nfunction maskitoParseDate(value, {\n  mode,\n  min = DEFAULT_MIN_DATE,\n  max = DEFAULT_MAX_DATE\n}) {\n  if (value.length < mode.length) {\n    return null;\n  }\n  const dateSegments = parseDateString(value, mode);\n  const parsedDate = segmentsToDate(dateSegments);\n  return clamp(parsedDate, min, max);\n}\nconst formatter = Intl.DateTimeFormat('en-US', {\n  month: '2-digit',\n  day: '2-digit',\n  year: 'numeric'\n});\nfunction toDateSegments(date) {\n  return formatter.formatToParts(date).reduce((acc, part) => Object.assign(Object.assign({}, acc), {\n    [part.type]: part.value\n  }), {});\n}\nfunction maskitoStringifyDate(date, {\n  mode,\n  separator = '.',\n  min = DEFAULT_MIN_DATE,\n  max = DEFAULT_MAX_DATE\n}) {\n  const validatedDate = clamp(date, min, max);\n  const segments = toDateSegments(validatedDate);\n  return toDateString(segments, {\n    dateMode: mode.replaceAll('/', separator)\n  });\n}\nconst POSSIBLE_DATE_RANGE_SEPARATOR = [CHAR_HYPHEN, CHAR_EN_DASH, CHAR_EM_DASH, CHAR_MINUS, CHAR_JP_HYPHEN];\nfunction createMinMaxRangeLengthPostprocessor({\n  dateModeTemplate,\n  rangeSeparator,\n  minLength,\n  maxLength,\n  max = DEFAULT_MAX_DATE\n}) {\n  if (isEmpty(minLength) && isEmpty(maxLength)) {\n    return identity;\n  }\n  return ({\n    value,\n    selection\n  }) => {\n    const dateStrings = parseDateRangeString(value, dateModeTemplate, rangeSeparator);\n    if (dateStrings.length !== 2 || dateStrings.some(date => !isDateStringComplete(date, dateModeTemplate))) {\n      return {\n        value,\n        selection\n      };\n    }\n    const [fromDate, toDate] = dateStrings.map(dateString => segmentsToDate(parseDateString(dateString, dateModeTemplate)));\n    if (!fromDate || !toDate) {\n      return {\n        value,\n        selection\n      };\n    }\n    const minDistantToDate = appendDate(fromDate, Object.assign(Object.assign({}, minLength), {\n      // 06.02.2023 - 07.02.2023 => {minLength: {day: 3}} => 06.02.2023 - 08.02.2023\n      // \"from\"-day is included in the range\n      day: (minLength === null || minLength === void 0 ? void 0 : minLength.day) && minLength.day - 1\n    }));\n    const maxDistantToDate = !isEmpty(maxLength) ? appendDate(fromDate, Object.assign(Object.assign({}, maxLength), {\n      day: (maxLength === null || maxLength === void 0 ? void 0 : maxLength.day) && maxLength.day - 1\n    })) : max;\n    const minLengthClampedToDate = clamp(toDate, minDistantToDate, max);\n    const minMaxLengthClampedToDate = minLengthClampedToDate > maxDistantToDate ? maxDistantToDate : minLengthClampedToDate;\n    return {\n      selection,\n      value: dateStrings[0] + rangeSeparator + toDateString(dateToSegments(minMaxLengthClampedToDate), {\n        dateMode: dateModeTemplate\n      })\n    };\n  };\n}\nfunction createSwapDatesPostprocessor({\n  dateModeTemplate,\n  rangeSeparator\n}) {\n  return ({\n    value,\n    selection\n  }) => {\n    const dateStrings = parseDateRangeString(value, dateModeTemplate, rangeSeparator);\n    const isDateRangeComplete = dateStrings.length === 2 && dateStrings.every(date => isDateStringComplete(date, dateModeTemplate));\n    const [from, to] = selection;\n    const caretAtTheEnd = from >= value.length;\n    const allValueSelected = from === 0 && to >= value.length; // dropping text inside with a pointer\n    if (!(caretAtTheEnd || allValueSelected) || !isDateRangeComplete) {\n      return {\n        value,\n        selection\n      };\n    }\n    const [fromDate, toDate] = dateStrings.map(dateString => segmentsToDate(parseDateString(dateString, dateModeTemplate)));\n    return {\n      selection,\n      value: fromDate && toDate && fromDate > toDate ? dateStrings.reverse().join(rangeSeparator) : value\n    };\n  };\n}\nfunction maskitoDateRangeOptionsGenerator({\n  mode,\n  min,\n  max,\n  minLength,\n  maxLength,\n  dateSeparator = '.',\n  rangeSeparator = `${CHAR_NO_BREAK_SPACE}${CHAR_EN_DASH}${CHAR_NO_BREAK_SPACE}`\n}) {\n  const dateModeTemplate = mode.split('/').join(dateSeparator);\n  const dateMask = Array.from(dateModeTemplate).map(char => dateSeparator.includes(char) ? char : /\\d/);\n  return Object.assign(Object.assign({}, MASKITO_DEFAULT_OPTIONS), {\n    mask: [...dateMask, ...Array.from(rangeSeparator), ...dateMask],\n    overwriteMode: 'replace',\n    preprocessors: [createFullWidthToHalfWidthPreprocessor(), createFirstDateEndSeparatorPreprocessor({\n      dateModeTemplate,\n      dateSegmentSeparator: dateSeparator,\n      firstDateEndSeparator: rangeSeparator,\n      pseudoFirstDateEndSeparators: POSSIBLE_DATE_RANGE_SEPARATOR\n    }), createZeroPlaceholdersPreprocessor(), normalizeDatePreprocessor({\n      dateModeTemplate,\n      rangeSeparator,\n      dateSegmentsSeparator: dateSeparator\n    }), createValidDatePreprocessor({\n      dateModeTemplate,\n      rangeSeparator,\n      dateSegmentsSeparator: dateSeparator\n    })],\n    postprocessors: [createDateSegmentsZeroPaddingPostprocessor({\n      dateModeTemplate,\n      dateSegmentSeparator: dateSeparator,\n      splitFn: value => ({\n        dateStrings: parseDateRangeString(value, dateModeTemplate, rangeSeparator)\n      }),\n      uniteFn: (validatedDateStrings, initialValue) => validatedDateStrings.reduce((acc, dateString, dateIndex) => acc + dateString + (!dateIndex && initialValue.includes(rangeSeparator) ? rangeSeparator : ''), '')\n    }), createMinMaxDatePostprocessor({\n      min,\n      max,\n      dateModeTemplate,\n      rangeSeparator,\n      dateSegmentSeparator: dateSeparator\n    }), createMinMaxRangeLengthPostprocessor({\n      dateModeTemplate,\n      minLength,\n      maxLength,\n      max,\n      rangeSeparator\n    }), createSwapDatesPostprocessor({\n      dateModeTemplate,\n      rangeSeparator\n    })]\n  });\n}\nfunction isDateTimeStringComplete(dateTimeString, {\n  dateMode,\n  timeMode,\n  dateTimeSeparator = DATE_TIME_SEPARATOR\n}) {\n  var _a;\n  return dateTimeString.length >= dateMode.length + timeMode.length + dateTimeSeparator.length && ((_a = dateTimeString.split(dateTimeSeparator)[0]) !== null && _a !== void 0 ? _a : '').split(/\\D/).every(segment => !/^0+$/.exec(segment));\n}\nfunction maskitoTimeOptionsGenerator({\n  mode,\n  timeSegmentMaxValues = {},\n  timeSegmentMinValues = {},\n  step = 0\n}) {\n  const hasMeridiem = mode.includes('AA');\n  const enrichedTimeSegmentMaxValues = Object.assign(Object.assign(Object.assign({}, DEFAULT_TIME_SEGMENT_MAX_VALUES), hasMeridiem ? {\n    hours: 12\n  } : {}), timeSegmentMaxValues);\n  const enrichedTimeSegmentMinValues = Object.assign(Object.assign(Object.assign({}, DEFAULT_TIME_SEGMENT_MIN_VALUES), hasMeridiem ? {\n    hours: 1\n  } : {}), timeSegmentMinValues);\n  return {\n    mask: createTimeMaskExpression(mode),\n    preprocessors: [createFullWidthToHalfWidthPreprocessor(), createColonConvertPreprocessor(), createZeroPlaceholdersPreprocessor(), createMeridiemPreprocessor(mode), createInvalidTimeSegmentInsertionPreprocessor({\n      timeMode: mode,\n      timeSegmentMinValues: enrichedTimeSegmentMinValues,\n      timeSegmentMaxValues: enrichedTimeSegmentMaxValues\n    })],\n    postprocessors: [createMeridiemPostprocessor(mode), elementState => enrichTimeSegmentsWithZeroes(elementState, {\n      mode,\n      timeSegmentMaxValues: enrichedTimeSegmentMaxValues\n    })],\n    plugins: [createTimeSegmentsSteppingPlugin({\n      fullMode: mode,\n      step,\n      timeSegmentMaxValues: enrichedTimeSegmentMaxValues\n    }), createMeridiemSteppingPlugin(mode.indexOf('AA'))],\n    overwriteMode: 'replace'\n  };\n}\n\n/**\n * Converts a formatted time string to milliseconds based on the given `options.mode`.\n *\n * @param maskedTime a formatted time string by {@link maskitoTimeOptionsGenerator} or {@link maskitoStringifyTime}\n * @param params\n */\nfunction maskitoParseTime(maskedTime, {\n  mode,\n  timeSegmentMaxValues = {}\n}) {\n  var _a, _b, _c, _d;\n  const maxValues = Object.assign(Object.assign({}, DEFAULT_TIME_SEGMENT_MAX_VALUES), timeSegmentMaxValues);\n  const msInSecond = maxValues.milliseconds + 1;\n  const msInMinute = (maxValues.seconds + 1) * msInSecond;\n  const msInHour = (maxValues.minutes + 1) * msInMinute;\n  const parsedTime = padEndTimeSegments(parseTimeString(maskedTime, mode));\n  return Number((_a = parsedTime.hours) !== null && _a !== void 0 ? _a : '') * msInHour + Number((_b = parsedTime.minutes) !== null && _b !== void 0 ? _b : '') * msInMinute + Number((_c = parsedTime.seconds) !== null && _c !== void 0 ? _c : '') * msInSecond + Number((_d = parsedTime.milliseconds) !== null && _d !== void 0 ? _d : '');\n}\n\n/**\n * Converts milliseconds to a formatted time string based on the given `options.mode`.\n *\n * @param milliseconds unsigned integer milliseconds\n * @param params\n */\nfunction maskitoStringifyTime(milliseconds, {\n  mode,\n  timeSegmentMaxValues = {}\n}) {\n  const maxValues = Object.assign(Object.assign({}, DEFAULT_TIME_SEGMENT_MAX_VALUES), timeSegmentMaxValues);\n  const msInSecond = maxValues.milliseconds + 1;\n  const msInMinute = (maxValues.seconds + 1) * msInSecond;\n  const msInHour = (maxValues.minutes + 1) * msInMinute;\n  const hours = Math.trunc(milliseconds / msInHour);\n  milliseconds -= hours * msInHour;\n  const minutes = Math.trunc(milliseconds / msInMinute);\n  milliseconds -= minutes * msInMinute;\n  const seconds = Math.trunc(milliseconds / msInSecond);\n  milliseconds -= seconds * msInSecond;\n  const result = padStartTimeSegments({\n    hours,\n    minutes,\n    seconds,\n    milliseconds\n  });\n  return mode.replaceAll(/H+/g, result.hours).replaceAll('MSS', result.milliseconds).replaceAll(/M+/g, result.minutes).replaceAll(/S+/g, result.seconds);\n}\nfunction maskitoParseDateTime(value, {\n  dateMode,\n  timeMode,\n  min = DEFAULT_MIN_DATE,\n  max = DEFAULT_MAX_DATE,\n  dateTimeSeparator = DATE_TIME_SEPARATOR\n}) {\n  const [dateSegment = '', timeSegment = ''] = value.split(dateTimeSeparator);\n  if (timeSegment.length !== timeMode.length) {\n    return null;\n  }\n  const date = maskitoParseDate(dateSegment, {\n    mode: dateMode\n  });\n  const time = maskitoParseTime(timeSegment, {\n    mode: timeMode\n  });\n  if (!date) {\n    return null;\n  }\n  const dateTime = new Date(Number(date) + time);\n  return clamp(dateTime, min, max);\n}\nconst NON_DIGIT_PLACEHOLDER_RE = /[^dmy]/g;\nconst LEADING_NON_DIGIT_RE = /^\\D*/;\nfunction splitDateTimeString(dateTime, dateModeTemplate) {\n  const dateDigitsCount = dateModeTemplate.replaceAll(NON_DIGIT_PLACEHOLDER_RE, '').length;\n  const [date = ''] = new RegExp(`(\\\\d[^\\\\d]*){0,${dateDigitsCount - 1}}\\\\d?`).exec(dateTime) || [];\n  const [dateTimeSeparator = ''] = LEADING_NON_DIGIT_RE.exec(dateTime.slice(date.length)) || [];\n  return [date, dateTime.slice(date.length + dateTimeSeparator.length)];\n}\nfunction maskitoStringifyDateTime(date, {\n  dateMode,\n  timeMode,\n  dateTimeSeparator = DATE_TIME_SEPARATOR,\n  dateSeparator = '.',\n  min = DEFAULT_MIN_DATE,\n  max = DEFAULT_MAX_DATE\n}) {\n  const validatedDate = clamp(date, min, max);\n  const dateString = maskitoStringifyDate(validatedDate, {\n    mode: dateMode,\n    separator: dateSeparator,\n    min,\n    max\n  });\n  const extractedTime = Number(validatedDate) - Number(new Date(validatedDate.getFullYear(), validatedDate.getMonth(), validatedDate.getDate()));\n  const timeString = maskitoStringifyTime(extractedTime, {\n    mode: timeMode\n  });\n  return dateString + dateTimeSeparator + timeString;\n}\nfunction createMinMaxDateTimePostprocessor({\n  dateModeTemplate,\n  timeMode,\n  min = DEFAULT_MIN_DATE,\n  max = DEFAULT_MAX_DATE,\n  dateTimeSeparator\n}) {\n  return ({\n    value,\n    selection\n  }) => {\n    const [dateString, timeString] = splitDateTimeString(value, dateModeTemplate);\n    const parsedDate = parseDateString(dateString, dateModeTemplate);\n    const parsedTime = parseTimeString(timeString, timeMode);\n    if (!isDateTimeStringComplete(value, {\n      dateMode: dateModeTemplate,\n      timeMode,\n      dateTimeSeparator\n    })) {\n      const fixedDate = raiseSegmentValueToMin(parsedDate, dateModeTemplate);\n      const {\n        year,\n        month,\n        day\n      } = isDateStringComplete(dateString, dateModeTemplate) ? dateToSegments(clamp(segmentsToDate(fixedDate), min, max)) : fixedDate;\n      const fixedValue = toDateString(Object.assign({\n        year,\n        month,\n        day\n      }, parsedTime), {\n        dateMode: dateModeTemplate,\n        dateTimeSeparator,\n        timeMode\n      });\n      const tail = value.slice(fixedValue.length);\n      return {\n        selection,\n        value: fixedValue + tail\n      };\n    }\n    const date = segmentsToDate(parsedDate, parsedTime);\n    const clampedDate = clamp(date, min, max);\n    // trailing segment separators or meridiem characters\n    const [trailingNonDigitCharacters = ''] = value.match(/\\D+$/g) || [];\n    const validatedValue = toDateString(dateToSegments(clampedDate), {\n      dateMode: dateModeTemplate,\n      dateTimeSeparator,\n      timeMode\n    }) + trailingNonDigitCharacters;\n    return {\n      selection,\n      value: validatedValue\n    };\n  };\n}\nfunction createValidDateTimePreprocessor({\n  dateModeTemplate,\n  dateSegmentsSeparator,\n  dateTimeSeparator,\n  timeMode,\n  timeSegmentMaxValues\n}) {\n  return ({\n    elementState,\n    data\n  }) => {\n    const {\n      value,\n      selection\n    } = elementState;\n    if (data === dateSegmentsSeparator) {\n      return {\n        elementState,\n        data: selection[0] === value.length ? data : ''\n      };\n    }\n    const newCharacters = data.replaceAll(/\\D/g, '');\n    if (!newCharacters) {\n      return {\n        elementState,\n        data\n      };\n    }\n    const [from, rawTo] = selection;\n    let to = rawTo + data.length;\n    const newPossibleValue = value.slice(0, from) + newCharacters + value.slice(to);\n    const [dateString, timeString] = splitDateTimeString(newPossibleValue, dateModeTemplate);\n    let validatedValue = '';\n    const hasDateTimeSeparator = newPossibleValue.includes(dateTimeSeparator);\n    const {\n      validatedDateString,\n      updatedSelection\n    } = validateDateString({\n      dateString,\n      dateSegmentsSeparator,\n      dateModeTemplate,\n      offset: 0,\n      selection: [from, to]\n    });\n    if (dateString && !validatedDateString) {\n      return {\n        elementState,\n        data: ''\n      }; // prevent changes\n    }\n    to = updatedSelection[1];\n    validatedValue += validatedDateString;\n    const updatedTimeState = enrichTimeSegmentsWithZeroes({\n      value: timeString,\n      selection: [from, to]\n    }, {\n      mode: timeMode,\n      timeSegmentMaxValues\n    });\n    to = updatedTimeState.selection[1];\n    validatedValue += hasDateTimeSeparator ? dateTimeSeparator + updatedTimeState.value : updatedTimeState.value;\n    const newData = validatedValue.slice(from, to);\n    return {\n      elementState: {\n        selection,\n        value: validatedValue.slice(0, from) + newData.split(dateSegmentsSeparator).map(segment => '0'.repeat(segment.length)).join(dateSegmentsSeparator) + validatedValue.slice(to)\n      },\n      data: newData\n    };\n  };\n}\nfunction maskitoDateTimeOptionsGenerator({\n  dateMode,\n  timeMode,\n  dateSeparator = '.',\n  min,\n  max,\n  dateTimeSeparator = DATE_TIME_SEPARATOR,\n  timeStep = 0\n}) {\n  const hasMeridiem = timeMode.includes('AA');\n  const dateModeTemplate = dateMode.split('/').join(dateSeparator);\n  const timeSegmentMaxValues = Object.assign(Object.assign({}, DEFAULT_TIME_SEGMENT_MAX_VALUES), hasMeridiem ? {\n    hours: 12\n  } : {});\n  const timeSegmentMinValues = Object.assign(Object.assign({}, DEFAULT_TIME_SEGMENT_MIN_VALUES), hasMeridiem ? {\n    hours: 1\n  } : {});\n  const fullMode = `${dateModeTemplate}${dateTimeSeparator}${timeMode}`;\n  return Object.assign(Object.assign({}, MASKITO_DEFAULT_OPTIONS), {\n    mask: [...Array.from(dateModeTemplate).map(char => dateSeparator.includes(char) ? char : /\\d/), ...dateTimeSeparator.split(''), ...createTimeMaskExpression(timeMode)],\n    overwriteMode: 'replace',\n    preprocessors: [createFullWidthToHalfWidthPreprocessor(), createColonConvertPreprocessor(), createFirstDateEndSeparatorPreprocessor({\n      dateModeTemplate,\n      dateSegmentSeparator: dateSeparator,\n      firstDateEndSeparator: dateTimeSeparator,\n      pseudoFirstDateEndSeparators: dateTimeSeparator.split('')\n    }), createZeroPlaceholdersPreprocessor(), createMeridiemPreprocessor(timeMode), normalizeDatePreprocessor({\n      dateModeTemplate,\n      dateSegmentsSeparator: dateSeparator,\n      dateTimeSeparator\n    }), createInvalidTimeSegmentInsertionPreprocessor({\n      timeMode,\n      timeSegmentMinValues,\n      timeSegmentMaxValues,\n      parseValue: x => {\n        const [dateString, timeString] = splitDateTimeString(x, dateModeTemplate);\n        return {\n          timeString,\n          restValue: dateString + dateTimeSeparator\n        };\n      }\n    }), createValidDateTimePreprocessor({\n      dateModeTemplate,\n      dateSegmentsSeparator: dateSeparator,\n      dateTimeSeparator,\n      timeMode,\n      timeSegmentMaxValues\n    })],\n    postprocessors: [createMeridiemPostprocessor(timeMode), createDateSegmentsZeroPaddingPostprocessor({\n      dateModeTemplate,\n      dateSegmentSeparator: dateSeparator,\n      splitFn: value => {\n        const [dateString, timeString] = splitDateTimeString(value, dateModeTemplate);\n        return {\n          dateStrings: [dateString],\n          restPart: timeString\n        };\n      },\n      uniteFn: ([validatedDateString], initialValue) => validatedDateString + (initialValue.includes(dateTimeSeparator) ? dateTimeSeparator : '')\n    }), createMinMaxDateTimePostprocessor({\n      min,\n      max,\n      dateModeTemplate,\n      timeMode,\n      dateTimeSeparator\n    })],\n    plugins: [createTimeSegmentsSteppingPlugin({\n      step: timeStep,\n      fullMode,\n      timeSegmentMaxValues: DEFAULT_TIME_SEGMENT_MAX_VALUES\n    }), createMeridiemSteppingPlugin(fullMode.indexOf('AA'))]\n  });\n}\n\n/**\n * It drops prefix and postfix from data\n * Needed for case, when prefix or postfix contain decimalSeparator, to ignore it in resulting number\n * @example User pastes '{prefix}123.45{postfix}' => 123.45\n */\nfunction createAffixesFilterPreprocessor({\n  prefix,\n  postfix\n}) {\n  return ({\n    elementState,\n    data\n  }) => {\n    const {\n      cleanValue: cleanData\n    } = extractAffixes(data, {\n      prefix,\n      postfix\n    });\n    return {\n      elementState,\n      data: cleanData\n    };\n  };\n}\nfunction generateMaskExpression({\n  decimalPseudoSeparators,\n  decimalSeparator,\n  maximumFractionDigits,\n  min,\n  minusSign,\n  postfix,\n  prefix,\n  pseudoMinuses,\n  thousandSeparator\n}) {\n  const computedPrefix = min < 0 && [minusSign, ...pseudoMinuses].includes(prefix) ? '' : computeAllOptionalCharsRegExp(prefix);\n  const digit = String.raw`\\d`;\n  const optionalMinus = min < 0 ? `[${minusSign}${pseudoMinuses.map(x => `\\\\${x}`).join('')}]?` : '';\n  const integerPart = thousandSeparator ? `[${digit}${escapeRegExp(thousandSeparator).replaceAll(/\\s/g, String.raw`\\s`)}]*` : `[${digit}]*`;\n  const precisionPart = Number.isFinite(maximumFractionDigits) ? maximumFractionDigits : '';\n  const decimalPart = maximumFractionDigits > 0 ? `([${escapeRegExp(decimalSeparator)}${decimalPseudoSeparators.map(escapeRegExp).join('')}]${digit}{0,${precisionPart}})?` : '';\n  const computedPostfix = computeAllOptionalCharsRegExp(postfix);\n  return new RegExp(`^${computedPrefix}${optionalMinus}${integerPart}${decimalPart}${computedPostfix}$`);\n}\nfunction computeAllOptionalCharsRegExp(str) {\n  return str ? `${str.split('').map(char => `${escapeRegExp(char)}?`).join('')}` : '';\n}\nfunction maskitoParseNumber(maskedNumber, decimalSeparator = '.') {\n  const hasNegativeSign = !!new RegExp(`^\\\\D*[${CHAR_MINUS}\\\\${CHAR_HYPHEN}${CHAR_EN_DASH}${CHAR_EM_DASH}${CHAR_JP_HYPHEN}]`).exec(maskedNumber);\n  const escapedDecimalSeparator = escapeRegExp(decimalSeparator);\n  const unmaskedNumber = maskedNumber\n  // drop all decimal separators not followed by a digit\n  .replaceAll(new RegExp(`${escapedDecimalSeparator}(?!\\\\d)`, 'g'), '')\n  // drop all non-digit characters except decimal separator\n  .replaceAll(new RegExp(`[^\\\\d${escapedDecimalSeparator}]`, 'g'), '').replace(decimalSeparator, decimalSeparator && '.');\n  if (unmaskedNumber) {\n    const sign = hasNegativeSign ? CHAR_HYPHEN : '';\n    return Number(`${sign}${unmaskedNumber}`);\n  }\n  return NaN;\n}\nfunction maskitoStringifyNumber(number, params) {\n  if (Number.isNaN(number) || number === null) {\n    return '';\n  }\n  const {\n    min = Number.MIN_SAFE_INTEGER,\n    max = Number.MAX_SAFE_INTEGER,\n    decimalSeparator = '.'\n  } = params;\n  const value = clamp(number, min, max).toString().replace('.', decimalSeparator);\n  return maskitoTransform(value, maskitoNumberOptionsGenerator(params));\n}\n\n/**\n * Convert number to string with replacing exponent part on decimals\n *\n * @param value the number\n * @return string representation of a number\n */\nfunction stringifyNumberWithoutExp(value) {\n  var _a;\n  const valueAsString = String(value);\n  const [numberPart = '', expPart] = valueAsString.split('e-');\n  let valueWithoutExp = valueAsString;\n  if (expPart) {\n    const [, fractionalPart] = numberPart.split('.');\n    const decimalDigits = Number(expPart) + ((_a = fractionalPart === null || fractionalPart === void 0 ? void 0 : fractionalPart.length) !== null && _a !== void 0 ? _a : 0);\n    valueWithoutExp = value.toFixed(decimalDigits);\n  }\n  return valueWithoutExp;\n}\nfunction toNumberParts(value, {\n  decimalSeparator,\n  minusSign\n}) {\n  const [integerWithMinus = '', decimalPart = ''] = decimalSeparator ? value.split(decimalSeparator) : [value];\n  const escapedMinus = escapeRegExp(minusSign);\n  const [, minus = '', integerPart = ''] = new RegExp(`^(?:[^\\\\d${escapedMinus}])?(${escapedMinus})?(.*)`).exec(integerWithMinus) || [];\n  return {\n    minus,\n    integerPart,\n    decimalPart\n  };\n}\nfunction validateDecimalPseudoSeparators({\n  decimalSeparator,\n  thousandSeparator,\n  decimalPseudoSeparators = DEFAULT_DECIMAL_PSEUDO_SEPARATORS\n}) {\n  return decimalPseudoSeparators.filter(char => char !== thousandSeparator && char !== decimalSeparator);\n}\n\n/**\n * If `minimumFractionDigits` is `>0`, it pads decimal part with zeroes\n * (until number of digits after decimalSeparator is equal to the `minimumFractionDigits`).\n * @example 1,42 => (`minimumFractionDigits` is equal to 4) => 1,4200.\n */\nfunction createDecimalZeroPaddingPostprocessor({\n  decimalSeparator,\n  minimumFractionDigits,\n  prefix,\n  postfix\n}) {\n  if (!minimumFractionDigits) {\n    return identity;\n  }\n  return ({\n    value,\n    selection\n  }) => {\n    const {\n      cleanValue,\n      extractedPrefix,\n      extractedPostfix\n    } = extractAffixes(value, {\n      prefix,\n      postfix\n    });\n    if (Number.isNaN(maskitoParseNumber(cleanValue, decimalSeparator))) {\n      return {\n        value,\n        selection\n      };\n    }\n    const [integerPart, decimalPart = ''] = cleanValue.split(decimalSeparator);\n    return {\n      value: extractedPrefix + integerPart + decimalSeparator + decimalPart.padEnd(minimumFractionDigits, '0') + extractedPostfix,\n      selection\n    };\n  };\n}\n\n/**\n * Make textfield empty if there is no integer part and all decimal digits are zeroes.\n * @example 0|,00 => Backspace => Empty.\n * @example -0|,00 => Backspace => -.\n * @example ,42| => Backspace x2 => ,|00 => Backspace => Empty\n */\nfunction emptyPostprocessor({\n  prefix,\n  postfix,\n  decimalSeparator,\n  minusSign\n}) {\n  return ({\n    value,\n    selection\n  }) => {\n    const [caretIndex] = selection;\n    const {\n      cleanValue,\n      extractedPrefix,\n      extractedPostfix\n    } = extractAffixes(value, {\n      prefix,\n      postfix\n    });\n    const {\n      minus,\n      integerPart,\n      decimalPart\n    } = toNumberParts(cleanValue, {\n      decimalSeparator,\n      minusSign\n    });\n    const aloneDecimalSeparator = !integerPart && !decimalPart && Boolean(decimalSeparator) && cleanValue.includes(decimalSeparator);\n    if (!integerPart && !Number(decimalPart) && caretIndex === (minus + extractedPrefix).length || aloneDecimalSeparator) {\n      return {\n        selection,\n        value: extractedPrefix + minus + extractedPostfix\n      };\n    }\n    return {\n      value,\n      selection\n    };\n  };\n}\n\n/**\n * This preprocessor works only once at initialization phase (when `new Maskito(...)` is executed).\n * This preprocessor helps to avoid conflicts during transition from one mask to another (for the same input).\n * For example, the developer changes postfix (or other mask's props) during run-time.\n * ```\n * let maskitoOptions = maskitoNumberOptionsGenerator({postfix: ' year'});\n * // [3 seconds later]\n * maskitoOptions = maskitoNumberOptionsGenerator({postfix: ' years'});\n * ```\n */\nfunction createInitializationOnlyPreprocessor({\n  decimalPseudoSeparators,\n  decimalSeparator,\n  minusSign,\n  postfix,\n  prefix,\n  pseudoMinuses\n}) {\n  let isInitializationPhase = true;\n  const cleanNumberMask = generateMaskExpression({\n    decimalSeparator,\n    decimalPseudoSeparators,\n    pseudoMinuses,\n    prefix: '',\n    postfix: '',\n    thousandSeparator: '',\n    maximumFractionDigits: Infinity,\n    min: Number.MIN_SAFE_INTEGER,\n    minusSign\n  });\n  return ({\n    elementState,\n    data\n  }) => {\n    if (!isInitializationPhase) {\n      return {\n        elementState,\n        data\n      };\n    }\n    isInitializationPhase = false;\n    const {\n      value,\n      selection\n    } = elementState;\n    const [from, to] = selection;\n    const {\n      extractedPrefix,\n      cleanValue,\n      extractedPostfix\n    } = extractAffixes(value, {\n      prefix,\n      postfix\n    });\n    const cleanState = maskitoTransform({\n      selection: [Math.max(from - extractedPrefix.length, 0), clamp(to - extractedPrefix.length, 0, cleanValue.length)],\n      value: cleanValue\n    }, {\n      mask: cleanNumberMask\n    });\n    const [cleanFrom, cleanTo] = cleanState.selection;\n    return {\n      elementState: {\n        selection: [cleanFrom + extractedPrefix.length, cleanTo + extractedPrefix.length],\n        value: extractedPrefix + cleanState.value + extractedPostfix\n      },\n      data\n    };\n  };\n}\n\n/**\n * It removes repeated leading zeroes for integer part.\n * @example 0,|00005 => Backspace => |5\n * @example -0,|00005 => Backspace => -|5\n * @example User types \"000000\" => 0|\n * @example 0| => User types \"5\" => 5|\n */\nfunction createLeadingZeroesValidationPostprocessor({\n  decimalSeparator,\n  thousandSeparator,\n  prefix,\n  postfix\n}) {\n  const trimLeadingZeroes = value => {\n    const escapedThousandSeparator = escapeRegExp(thousandSeparator);\n    return value.replace(\n    // all leading zeroes followed by another zero\n    new RegExp(`^(\\\\D+)?[0${escapedThousandSeparator}]+(?=0)`), '$1').replace(\n    // zero followed by not-zero digit\n    new RegExp(`^(\\\\D+)?[0${escapedThousandSeparator}]+(?=[1-9])`), '$1');\n  };\n  const countTrimmedZeroesBefore = (value, index) => {\n    const valueBefore = value.slice(0, index);\n    const followedByZero = value.slice(index).startsWith('0');\n    return valueBefore.length - trimLeadingZeroes(valueBefore).length + (followedByZero ? 1 : 0);\n  };\n  return ({\n    value,\n    selection\n  }) => {\n    const [from, to] = selection;\n    const {\n      cleanValue,\n      extractedPrefix,\n      extractedPostfix\n    } = extractAffixes(value, {\n      prefix,\n      postfix\n    });\n    const hasDecimalSeparator = Boolean(decimalSeparator) && cleanValue.includes(decimalSeparator);\n    const [integerPart = '', decimalPart = ''] = decimalSeparator ? cleanValue.split(decimalSeparator) : [cleanValue];\n    const zeroTrimmedIntegerPart = trimLeadingZeroes(integerPart);\n    if (integerPart === zeroTrimmedIntegerPart) {\n      return {\n        value,\n        selection\n      };\n    }\n    const newFrom = from - countTrimmedZeroesBefore(value, from);\n    const newTo = to - countTrimmedZeroesBefore(value, to);\n    return {\n      value: extractedPrefix + zeroTrimmedIntegerPart + (hasDecimalSeparator ? decimalSeparator : '') + decimalPart + extractedPostfix,\n      selection: [Math.max(newFrom, 0), Math.max(newTo, 0)]\n    };\n  };\n}\n\n/**\n * This postprocessor is connected with {@link createMinMaxPlugin}:\n * both validate `min`/`max` bounds of entered value (but at the different point of time).\n */\nfunction createMinMaxPostprocessor({\n  min,\n  max,\n  decimalSeparator,\n  minusSign\n}) {\n  return ({\n    value,\n    selection\n  }) => {\n    const parsedNumber = maskitoParseNumber(value, decimalSeparator);\n    const limitedValue =\n    /**\n     * We cannot limit lower bound if user enters positive number.\n     * The same for upper bound and negative number.\n     * ___\n     * @example (min = 5)\n     * Empty input => Without this condition user cannot type 42 (the first digit will be rejected)\n     * ___\n     * @example (max = -10)\n     * Value is -10 => Without this condition user cannot delete 0 to enter another digit\n     */\n    parsedNumber > 0 ? Math.min(parsedNumber, max) : Math.max(parsedNumber, min);\n    if (parsedNumber && limitedValue !== parsedNumber) {\n      const newValue = `${limitedValue}`.replace('.', decimalSeparator).replace(CHAR_HYPHEN, minusSign);\n      return {\n        value: newValue,\n        selection: [newValue.length, newValue.length]\n      };\n    }\n    return {\n      value,\n      selection\n    };\n  };\n}\n\n/**\n * Manage caret-navigation when user \"deletes\" non-removable digits or separators\n * @example 1,|42 => Backspace => 1|,42 (only if `minimumFractionDigits` is `>0`)\n * @example 1|,42 => Delete => 1,|42 (only if `minimumFractionDigits` is `>0`)\n * @example 0,|00 => Delete => 0,0|0 (only if `minimumFractionDigits` is `>0`)\n * @example 1 |000 => Backspace => 1| 000 (always)\n */\nfunction createNonRemovableCharsDeletionPreprocessor({\n  decimalSeparator,\n  thousandSeparator,\n  minimumFractionDigits\n}) {\n  return ({\n    elementState,\n    data\n  }, actionType) => {\n    const {\n      value,\n      selection\n    } = elementState;\n    const [from, to] = selection;\n    const selectedCharacters = value.slice(from, to);\n    const nonRemovableSeparators = minimumFractionDigits ? [decimalSeparator, thousandSeparator] : [thousandSeparator];\n    const areNonRemovableZeroesSelected = Boolean(minimumFractionDigits) && from > value.indexOf(decimalSeparator) && Boolean(selectedCharacters.match(/^0+$/gi));\n    if (actionType !== 'deleteBackward' && actionType !== 'deleteForward' || !nonRemovableSeparators.includes(selectedCharacters) && !areNonRemovableZeroesSelected) {\n      return {\n        elementState,\n        data\n      };\n    }\n    return {\n      elementState: {\n        value,\n        selection: actionType === 'deleteForward' ? [to, to] : [from, from]\n      },\n      data\n    };\n  };\n}\n\n/**\n * It pads integer part with zero if user types decimal separator (for empty input).\n * @example Empty input => User types \",\" (decimal separator) => 0,|\n */\nfunction createNotEmptyIntegerPartPreprocessor({\n  decimalSeparator,\n  maximumFractionDigits,\n  prefix,\n  postfix\n}) {\n  const startWithDecimalSepRegExp = new RegExp(`^\\\\D*${escapeRegExp(decimalSeparator)}`);\n  return ({\n    elementState,\n    data\n  }) => {\n    const {\n      value,\n      selection\n    } = elementState;\n    const {\n      cleanValue,\n      extractedPrefix\n    } = extractAffixes(value, {\n      prefix,\n      postfix\n    });\n    const [from, to] = selection;\n    const cleanFrom = clamp(from - extractedPrefix.length, 0, cleanValue.length);\n    const cleanTo = clamp(to - extractedPrefix.length, 0, cleanValue.length);\n    if (maximumFractionDigits <= 0 || cleanValue.slice(0, cleanFrom).includes(decimalSeparator) || cleanValue.slice(cleanTo).includes(decimalSeparator) || !data.match(startWithDecimalSepRegExp)) {\n      return {\n        elementState,\n        data\n      };\n    }\n    const digitsBeforeCursor = /\\d+/.exec(cleanValue.slice(0, cleanFrom));\n    return {\n      elementState,\n      data: digitsBeforeCursor ? data : `0${data}`\n    };\n  };\n}\n\n/**\n * It replaces pseudo characters with valid one.\n * @example User types '.' (but separator is equal to comma) => dot is replaced with comma.\n * @example User types hyphen / en-dash / em-dash => it is replaced with minus.\n */\nfunction createPseudoCharactersPreprocessor({\n  validCharacter,\n  pseudoCharacters,\n  prefix,\n  postfix\n}) {\n  const pseudoCharactersRegExp = new RegExp(`[${pseudoCharacters.join('')}]`, 'gi');\n  return ({\n    elementState,\n    data\n  }) => {\n    const {\n      value,\n      selection\n    } = elementState;\n    const {\n      cleanValue,\n      extractedPostfix,\n      extractedPrefix\n    } = extractAffixes(value, {\n      prefix,\n      postfix\n    });\n    return {\n      elementState: {\n        selection,\n        value: extractedPrefix + cleanValue.replace(pseudoCharactersRegExp, validCharacter) + extractedPostfix\n      },\n      data: data.replace(pseudoCharactersRegExp, validCharacter)\n    };\n  };\n}\n\n/**\n * It rejects new typed decimal separator if it already exists in text field.\n * Behaviour is similar to native <input type=\"number\"> (Chrome).\n * @example 1|23,45 => Press comma (decimal separator) => 1|23,45 (do nothing).\n */\nfunction createRepeatedDecimalSeparatorPreprocessor({\n  decimalSeparator,\n  prefix,\n  postfix\n}) {\n  if (!decimalSeparator) {\n    return identity;\n  }\n  return ({\n    elementState,\n    data\n  }) => {\n    const {\n      value,\n      selection\n    } = elementState;\n    const [from, to] = selection;\n    const {\n      cleanValue\n    } = extractAffixes(value, {\n      prefix,\n      postfix\n    });\n    return {\n      elementState,\n      data: !cleanValue.includes(decimalSeparator) || value.slice(from, to + 1).includes(decimalSeparator) ? data : data.replaceAll(new RegExp(escapeRegExp(decimalSeparator), 'gi'), '')\n    };\n  };\n}\n\n/**\n * It adds symbol for separating thousands.\n * @example 1000000 => (thousandSeparator is equal to space) => 1 000 000.\n */\nfunction createThousandSeparatorPostprocessor({\n  thousandSeparator,\n  decimalSeparator,\n  prefix,\n  postfix,\n  minusSign\n}) {\n  if (!thousandSeparator) {\n    return identity;\n  }\n  const isAllSpaces = (...chars) => chars.every(x => /\\s/.test(x));\n  return ({\n    value,\n    selection\n  }) => {\n    const [initialFrom, initialTo] = selection;\n    let [from, to] = selection;\n    const {\n      cleanValue,\n      extractedPostfix,\n      extractedPrefix\n    } = extractAffixes(value, {\n      prefix,\n      postfix\n    });\n    const {\n      minus,\n      integerPart,\n      decimalPart\n    } = toNumberParts(cleanValue, {\n      decimalSeparator,\n      minusSign\n    });\n    const hasDecimalSeparator = decimalSeparator && cleanValue.includes(decimalSeparator);\n    const deletedChars = cleanValue.length - (minus + integerPart + (hasDecimalSeparator ? decimalSeparator + decimalPart : '')).length;\n    if (deletedChars > 0 && initialFrom && initialFrom <= deletedChars) {\n      from -= deletedChars;\n    }\n    if (deletedChars > 0 && initialTo && initialTo <= deletedChars) {\n      to -= deletedChars;\n    }\n    const processedIntegerPart = Array.from(integerPart).reduceRight((formattedValuePart, char, i) => {\n      const isLeadingThousandSeparator = !i && char === thousandSeparator;\n      const isPositionForSeparator = !isLeadingThousandSeparator && Boolean(formattedValuePart.length) && (formattedValuePart.length + 1) % 4 === 0;\n      const isSeparator = char === thousandSeparator || isAllSpaces(char, thousandSeparator);\n      if (isPositionForSeparator && isSeparator) {\n        return thousandSeparator + formattedValuePart;\n      }\n      if (!isPositionForSeparator && isSeparator) {\n        if (i && i <= initialFrom) {\n          from--;\n        }\n        if (i && i <= initialTo) {\n          to--;\n        }\n        return formattedValuePart;\n      }\n      if (!isPositionForSeparator) {\n        return char + formattedValuePart;\n      }\n      if (i < initialFrom) {\n        from++;\n      }\n      if (i < initialTo) {\n        to++;\n      }\n      return char + thousandSeparator + formattedValuePart;\n    }, '');\n    return {\n      value: extractedPrefix + minus + processedIntegerPart + (hasDecimalSeparator ? decimalSeparator : '') + decimalPart + extractedPostfix,\n      selection: [from, to]\n    };\n  };\n}\n\n/**\n * It drops decimal part if `maximumFractionDigits` is zero.\n * @example User pastes '123.45' (but `maximumFractionDigits` is zero) => 123\n */\nfunction createZeroPrecisionPreprocessor({\n  maximumFractionDigits,\n  decimalSeparator,\n  prefix,\n  postfix\n}) {\n  if (maximumFractionDigits > 0 || !decimalSeparator // all separators should be treated only as thousand separators\n  ) {\n    return identity;\n  }\n  const decimalPartRegExp = new RegExp(`${escapeRegExp(decimalSeparator)}.*$`, 'g');\n  return ({\n    elementState,\n    data\n  }) => {\n    const {\n      value,\n      selection\n    } = elementState;\n    const {\n      cleanValue,\n      extractedPrefix,\n      extractedPostfix\n    } = extractAffixes(value, {\n      prefix,\n      postfix\n    });\n    const [from, to] = selection;\n    const newValue = extractedPrefix + cleanValue.replace(decimalPartRegExp, '') + extractedPostfix;\n    return {\n      elementState: {\n        selection: [Math.min(from, newValue.length), Math.min(to, newValue.length)],\n        value: newValue\n      },\n      data: data.replace(decimalPartRegExp, '')\n    };\n  };\n}\nconst DUMMY_SELECTION = [0, 0];\n/**\n * It removes repeated leading zeroes for integer part on blur-event.\n * @example 000000 => blur => 0\n * @example 00005 => blur => 5\n */\nfunction createLeadingZeroesValidationPlugin({\n  decimalSeparator,\n  thousandSeparator,\n  prefix,\n  postfix\n}) {\n  const dropRepeatedLeadingZeroes = createLeadingZeroesValidationPostprocessor({\n    decimalSeparator,\n    thousandSeparator,\n    prefix,\n    postfix\n  });\n  return maskitoEventHandler('blur', element => {\n    const newValue = dropRepeatedLeadingZeroes({\n      value: element.value,\n      selection: DUMMY_SELECTION\n    }, {\n      value: '',\n      selection: DUMMY_SELECTION\n    }).value;\n    maskitoUpdateElement(element, newValue);\n  }, {\n    capture: true\n  });\n}\n\n/**\n * This plugin is connected with {@link createMinMaxPostprocessor}:\n * both validate `min`/`max` bounds of entered value (but at the different point of time).\n */\nfunction createMinMaxPlugin({\n  min,\n  max,\n  decimalSeparator\n}) {\n  return maskitoEventHandler('blur', (element, options) => {\n    const parsedNumber = maskitoParseNumber(element.value, decimalSeparator);\n    const clampedNumber = clamp(parsedNumber, min, max);\n    if (!Number.isNaN(parsedNumber) && parsedNumber !== clampedNumber) {\n      maskitoUpdateElement(element, maskitoTransform(stringifyNumberWithoutExp(clampedNumber), options));\n    }\n  }, {\n    capture: true\n  });\n}\n\n/**\n * It pads EMPTY integer part with zero if decimal parts exists.\n * It works on blur event only!\n * @example 1|,23 => Backspace => Blur => 0,23\n */\nfunction createNotEmptyIntegerPlugin({\n  decimalSeparator,\n  prefix,\n  postfix\n}) {\n  if (!decimalSeparator) {\n    return noop;\n  }\n  return maskitoEventHandler('blur', element => {\n    const {\n      cleanValue,\n      extractedPostfix,\n      extractedPrefix\n    } = extractAffixes(element.value, {\n      prefix,\n      postfix\n    });\n    const newValue = extractedPrefix + cleanValue.replace(new RegExp(`^(\\\\D+)?${escapeRegExp(decimalSeparator)}`), `$10${decimalSeparator}`) + extractedPostfix;\n    maskitoUpdateElement(element, newValue);\n  }, {\n    capture: true\n  });\n}\nfunction maskitoNumberOptionsGenerator({\n  max = Number.MAX_SAFE_INTEGER,\n  min = Number.MIN_SAFE_INTEGER,\n  precision = 0,\n  thousandSeparator = CHAR_NO_BREAK_SPACE,\n  decimalSeparator = '.',\n  decimalPseudoSeparators,\n  decimalZeroPadding = false,\n  prefix: unsafePrefix = '',\n  postfix = '',\n  minusSign = CHAR_MINUS,\n  maximumFractionDigits = precision,\n  minimumFractionDigits = decimalZeroPadding ? maximumFractionDigits : 0\n} = {}) {\n  const pseudoMinuses = [CHAR_HYPHEN, CHAR_EN_DASH, CHAR_EM_DASH, CHAR_JP_HYPHEN, CHAR_MINUS].filter(char => char !== thousandSeparator && char !== decimalSeparator && char !== minusSign);\n  const validatedDecimalPseudoSeparators = validateDecimalPseudoSeparators({\n    decimalSeparator,\n    thousandSeparator,\n    decimalPseudoSeparators\n  });\n  const prefix = unsafePrefix.endsWith(decimalSeparator) && maximumFractionDigits > 0 ? `${unsafePrefix}${CHAR_ZERO_WIDTH_SPACE}` : unsafePrefix;\n  const initializationOnlyPreprocessor = createInitializationOnlyPreprocessor({\n    decimalSeparator,\n    decimalPseudoSeparators: validatedDecimalPseudoSeparators,\n    pseudoMinuses,\n    prefix,\n    postfix,\n    minusSign\n  });\n  decimalSeparator = maximumFractionDigits <= 0 && decimalSeparator === thousandSeparator ? '' : decimalSeparator;\n  return Object.assign(Object.assign({}, MASKITO_DEFAULT_OPTIONS), {\n    mask: generateMaskExpression({\n      decimalSeparator,\n      maximumFractionDigits,\n      min,\n      minusSign,\n      postfix,\n      prefix,\n      pseudoMinuses,\n      thousandSeparator,\n      decimalPseudoSeparators: validatedDecimalPseudoSeparators\n    }),\n    preprocessors: [createFullWidthToHalfWidthPreprocessor(), initializationOnlyPreprocessor, createAffixesFilterPreprocessor({\n      prefix,\n      postfix\n    }), createPseudoCharactersPreprocessor({\n      validCharacter: minusSign,\n      pseudoCharacters: pseudoMinuses,\n      prefix,\n      postfix\n    }), createPseudoCharactersPreprocessor({\n      validCharacter: decimalSeparator,\n      pseudoCharacters: validatedDecimalPseudoSeparators,\n      prefix,\n      postfix\n    }), createNotEmptyIntegerPartPreprocessor({\n      decimalSeparator,\n      maximumFractionDigits,\n      prefix,\n      postfix\n    }), createNonRemovableCharsDeletionPreprocessor({\n      decimalSeparator,\n      minimumFractionDigits,\n      thousandSeparator\n    }), createZeroPrecisionPreprocessor({\n      maximumFractionDigits,\n      decimalSeparator,\n      prefix,\n      postfix\n    }), createRepeatedDecimalSeparatorPreprocessor({\n      decimalSeparator,\n      prefix,\n      postfix\n    })],\n    postprocessors: [createMinMaxPostprocessor({\n      decimalSeparator,\n      min,\n      max,\n      minusSign\n    }), maskitoPrefixPostprocessorGenerator(prefix), maskitoPostfixPostprocessorGenerator(postfix), createThousandSeparatorPostprocessor({\n      decimalSeparator,\n      thousandSeparator,\n      prefix,\n      postfix,\n      minusSign\n    }), createDecimalZeroPaddingPostprocessor({\n      decimalSeparator,\n      prefix,\n      postfix,\n      minimumFractionDigits: Math.min(minimumFractionDigits, maximumFractionDigits)\n    }), emptyPostprocessor({\n      prefix,\n      postfix,\n      decimalSeparator,\n      minusSign\n    })],\n    plugins: [createLeadingZeroesValidationPlugin({\n      decimalSeparator,\n      thousandSeparator,\n      prefix,\n      postfix\n    }), createNotEmptyIntegerPlugin({\n      decimalSeparator,\n      prefix,\n      postfix\n    }), createMinMaxPlugin({\n      min,\n      max,\n      decimalSeparator\n    })],\n    overwriteMode: minimumFractionDigits > 0 ? ({\n      value,\n      selection: [from]\n    }) => from <= value.indexOf(decimalSeparator) ? 'shift' : 'replace' : 'shift'\n  });\n}\nexport { maskitoAddOnFocusPlugin, maskitoCaretGuard, maskitoDateOptionsGenerator, maskitoDateRangeOptionsGenerator, maskitoDateTimeOptionsGenerator, maskitoEventHandler, maskitoNumberOptionsGenerator, maskitoParseDate, maskitoParseDateTime, maskitoParseNumber, maskitoParseTime, maskitoPostfixPostprocessorGenerator, maskitoPrefixPostprocessorGenerator, maskitoRejectEvent, maskitoRemoveOnBlurPlugin, maskitoSelectionChangeHandler, maskitoStringifyDate, maskitoStringifyDateTime, maskitoStringifyNumber, maskitoStringifyTime, maskitoTimeOptionsGenerator, maskitoWithPlaceholder };", "map": {"version": 3, "names": ["maskitoUpdateElement", "MASKITO_DEFAULT_OPTIONS", "maskitoTransform", "clamp", "value", "min", "max", "clampedValue", "Math", "Number", "Date", "countDigits", "str", "replaceAll", "length", "appendDate", "initialDate", "day", "month", "year", "date", "setDate", "getDate", "setMonth", "getMonth", "setFullYear", "getFullYear", "getDateSegmentValueLength", "dateString", "_a", "_b", "_c", "_d", "_e", "_f", "match", "dateToSegments", "String", "padStart", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "milliseconds", "getMilliseconds", "ALL_POSSIBLE_SEGMENTS", "getDateSegmentsOrder", "template", "sort", "a", "b", "indexOf", "getFirstCompleteDate", "dateModeTemplate", "digitsInDate", "completeDate", "RegExp", "exec", "isDateStringComplete", "split", "every", "segment", "parseDateRangeString", "date<PERSON><PERSON><PERSON>", "rangeSeparator", "replace", "parseDateString", "fullMode", "cleanMode", "onlyDigitsDate", "dateSegments", "slice", "lastIndexOf", "Object", "fromEntries", "entries", "filter", "_", "Boolean", "toLowerCase", "segmentsToDate", "parsedDate", "parsedTime", "_g", "DATE_TIME_SEPARATOR", "toDateString", "dateMode", "dateTimeSeparator", "timeMode", "safeYear", "DATE_SEGMENTS_MAX_VALUES", "DEFAULT_DECIMAL_PSEUDO_SEPARATORS", "DEFAULT_MIN_DATE", "DEFAULT_MAX_DATE", "DEFAULT_TIME_SEGMENT_MAX_VALUES", "DEFAULT_TIME_SEGMENT_MIN_VALUES", "CHAR_NO_BREAK_SPACE", "CHAR_ZERO_WIDTH_SPACE", "CHAR_EN_DASH", "CHAR_EM_DASH", "CHAR_HYPHEN", "CHAR_MINUS", "CHAR_JP_HYPHEN", "CHAR_COLON", "CHAR_JP_COLON", "ANY_MERIDIEM_CHARACTER_RE", "ALL_MERIDIEM_CHARACTERS_RE", "TIME_FIXED_CHARACTERS", "TIME_SEGMENT_VALUE_LENGTHS", "validateDateString", "dateSegmentsSeparator", "offset", "selection", "from", "to", "segmentsOrder", "validatedDateSegments", "i", "segmentName", "segmentValue", "validatedDate", "maxSegmentValue", "fantomSeparator", "lastSegmentDigitIndex", "isLastSegmentDigitAdded", "nextSegment", "validatedDateString", "updatedSelection", "addedDateSegmentSeparators", "identity", "x", "noop", "reRegExpChar", "reHasRegExpChar", "source", "escapeRegExp", "test", "raw", "extractAffixes", "prefix", "postfix", "prefixRegExp", "postfixRegExp", "extractedPrefix", "extractedPostfix", "cleanValue", "Infinity", "findCommonBeginningSubstr", "res", "isEmpty", "entity", "keys", "ALL_ZEROES_RE", "padWithZeroesUntilValid", "paddedMaxValue", "prefixedZeroesCount", "paddedSegmentValue", "padEnd", "validatedSegmentValue", "endsWith", "valueWithoutLastChar", "toHalfWidthColon", "fullWidthColon", "toHalfWidthNumber", "fullWidthNumber", "s", "fromCharCode", "charCodeAt", "createColonConvertPreprocessor", "elementState", "data", "createDateSegmentsZeroPaddingPostprocessor", "dateSegmentSeparator", "splitFn", "uniteFn", "dateStrings", "restPart", "validatedDateStrings", "caretShift", "for<PERSON>ach", "reduce", "acc", "assign", "push", "validatedV<PERSON>ue", "createFirstDateEndSeparatorPreprocessor", "firstDateEndSeparator", "pseudoFirstDateEndSeparators", "firstCompleteDate", "pseudoSeparators", "includes", "pseudoSeparatorsRE", "join", "newValue", "createFullWidthToHalfWidthPreprocessor", "createTimeMaskExpression", "mode", "Array", "map", "char", "concat", "padTimeSegments", "timeSegments", "pad", "padStartTimeSegments", "SEGMENT_FULL_NAME", "HH", "MM", "SS", "MSS", "parseTimeString", "timeString", "onlyDigits", "segmentAbbr", "LEADING_NON_DIGITS", "TRAILING_NON_DIGITS", "toTimeString", "TRAILING_TIME_SEGMENT_SEPARATOR_REG", "enrichTimeSegmentsWithZeroes", "timeSegmentMaxValues", "possibleTimeSegments", "paddedMaxValues", "validatedTimeSegments", "paddedZeroes", "trailingNonDigitCharacters", "validatedTimeString", "newFrom", "newTo", "padEndTimeSegments", "createInvalidTimeSegmentInsertionPreprocessor", "timeSegmentMinValues", "parseValue", "invalidCharsRegExp", "actionType", "rawTo", "newCharacters", "newPossibleValue", "restValue", "stringifiedSegmentValue", "minSegmentValue", "createMeridiemPreprocessor", "mainMeridiemCharRE", "toUpperCase", "newData", "createMeridiemPostprocessor", "initialElementState", "fullMeridiem", "raiseSegmentValueToMin", "segments", "<PERSON><PERSON><PERSON>th", "key", "segmentLength", "LEAP_YEAR", "createMinMaxDatePostprocessor", "endsWithRangeSeparator", "fixedDate", "fixedValue", "tail", "clampedDate", "normalizeDatePreprocessor", "templateSegments", "includesTime", "dates", "index", "dateIndex", "trunc", "isLastDateSegment", "maskitoPostfixPostprocessorGenerator", "postfixRE", "initialValueBeforePostfix", "postfixWasModified", "alreadyExistedValueBeforePostfix", "reverse", "isInitiallyMirroredChar", "maskitoPrefixPostprocessorGenerator", "startsWith", "prefixedValue", "modifiedValue", "addedCharsCount", "createValidDatePreprocessor", "hasRangeSeparator", "repeat", "maskitoEventHandler", "name", "handler", "eventListenerOptions", "element", "maskitoOptions", "listener", "addEventListener", "removeEventListener", "maskitoAddOnFocusPlugin", "maskitoSelectionChangeHandler", "options", "document", "ownerDocument", "isPointerDown", "onPointerDown", "onPointerUp", "matches", "once", "passive", "maskitoCaretGuard", "guard", "start", "selectionStart", "end", "selectionEnd", "fromLimit", "toLimit", "setSelectionRange", "maskitoRejectEvent", "event", "defaultPrevented", "dispatchEvent", "CustomEvent", "bubbles", "maskitoRemoveOnBlurPlugin", "createMeridiemSteppingPlugin", "meridiemStartIndex", "caretIndex", "preventDefault", "meridiemMainCharacter", "newMeridiem", "createTimeSegmentsSteppingPlugin", "step", "segmentsIndexes", "createTimeSegmentsIndexes", "activeSegment", "getActiveSegment", "updatedValue", "updateSegmentValue", "get", "toAdd", "Map", "getSegmentRange", "segmentRange", "newSegmentValue", "mod", "floor", "abs", "maskitoWithPlaceholder", "placeholder", "focusedOnly", "lastClearValue", "action", "removePlaceholder", "plugins", "focused", "focus", "capture", "blur", "preprocessors", "postprocessors", "justPlaceholderRemoval", "createZeroPlaceholdersPreprocessor", "isLastChar", "zeroes", "maskitoDateOptionsGenerator", "separator", "mask", "overwriteMode", "maskitoParseDate", "formatter", "Intl", "DateTimeFormat", "toDateSegments", "formatToParts", "part", "type", "maskitoStringifyDate", "POSSIBLE_DATE_RANGE_SEPARATOR", "createMinMaxRangeLengthPostprocessor", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "some", "fromDate", "toDate", "minDistantToDate", "maxDistantToDate", "minLengthClampedToDate", "minMaxLengthClampedToDate", "createSwapDatesPostprocessor", "isDateRangeComplete", "caretAtTheEnd", "allValueSelected", "maskitoDateRangeOptionsGenerator", "dateSeparator", "dateMask", "initialValue", "isDateTimeStringComplete", "dateTimeString", "maskitoTimeOptionsGenerator", "hasMeridiem", "enrichedTimeSegmentMaxValues", "enrichedTimeSegmentMinValues", "maskitoParseTime", "maskedTime", "max<PERSON><PERSON><PERSON>", "msInSecond", "msInMinute", "msInHour", "maskitoStringifyTime", "result", "maskitoParseDateTime", "dateSegment", "timeSegment", "time", "dateTime", "NON_DIGIT_PLACEHOLDER_RE", "LEADING_NON_DIGIT_RE", "splitDateTimeString", "dateDigitsCount", "maskitoStringifyDateTime", "extractedTime", "createMinMaxDateTimePostprocessor", "createValidDateTimePreprocessor", "hasDateTimeSeparator", "updatedTimeState", "maskitoDateTimeOptionsGenerator", "timeStep", "createAffixesFilterPreprocessor", "cleanData", "generateMaskExpression", "decimalPseudoSeparators", "decimalSeparator", "maximumFractionDigits", "minusSign", "pseudoMinuses", "thousandSeparator", "computedPrefix", "computeAllOptionalCharsRegExp", "digit", "optionalMinus", "integerPart", "precisionPart", "isFinite", "decimalPart", "computedPostfix", "maskitoParseNumber", "masked<PERSON>umber", "hasNegativeSign", "escapedDecimalSeparator", "unmasked<PERSON><PERSON>ber", "sign", "NaN", "maskitoStringifyNumber", "number", "params", "isNaN", "MIN_SAFE_INTEGER", "MAX_SAFE_INTEGER", "toString", "maskitoNumberOptionsGenerator", "stringifyNumberWithoutExp", "valueAsString", "numberPart", "expPart", "valueWithoutExp", "fractionalPart", "decimalDigits", "toFixed", "toNumberParts", "integerWithMinus", "escapedMinus", "minus", "validateDecimalPseudoSeparators", "createDecimalZeroPaddingPostprocessor", "minimumFractionDigits", "emptyPostprocessor", "aloneDecimalSeparator", "createInitializationOnlyPreprocessor", "isInitializationPhase", "cleanNumberMask", "cleanState", "cleanFrom", "cleanTo", "createLeadingZeroesValidationPostprocessor", "trimLeadingZeroes", "escapedThousandSeparator", "countTrimmedZeroesBefore", "valueBefore", "followedByZero", "hasDecimalSeparator", "zeroTrimmedIntegerPart", "createMinMaxPostprocessor", "parsedNumber", "limitedValue", "createNonRemovableCharsDeletionPreprocessor", "selectedCharacter<PERSON>", "nonRemovableSeparators", "areNonRemovableZeroesSelected", "createNotEmptyIntegerPartPreprocessor", "startWithDecimalSepRegExp", "digitsBeforeCursor", "createPseudoCharactersPreprocessor", "validCharacter", "pseudoCharacters", "pseudoCharactersRegExp", "createRepeatedDecimalSeparatorPreprocessor", "createThousandSeparatorPostprocessor", "isAllSpaces", "chars", "initialFrom", "initialTo", "deletedChars", "processedIntegerPart", "reduceRight", "formattedValuePart", "isLeadingThousandSeparator", "isPositionForSeparator", "isSeparator", "createZeroPrecisionPreprocessor", "decimalPartRegExp", "DUMMY_SELECTION", "createLeadingZeroesValidationPlugin", "dropRepeatedLeadingZeroes", "createMinMaxPlugin", "clampedNumber", "createNotEmptyIntegerPlugin", "precision", "decimalZeroPadding", "unsafePrefix", "validatedDecimalPseudoSeparators", "initializationOnlyPreprocessor"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@maskito/kit/index.esm.js"], "sourcesContent": ["import { maskitoUpdateElement, MASKITO_DEFAULT_OPTIONS, maskitoTransform } from '@maskito/core';\n\n/**\n * Clamps a value between two inclusive limits\n *\n * @param value\n * @param min lower limit\n * @param max upper limit\n */\nfunction clamp(value, min, max) {\n    const clampedValue = Math.min(Number(max), Math.max(Number(min), Number(value)));\n    return (value instanceof Date ? new Date(clampedValue) : clampedValue);\n}\n\nfunction countDigits(str) {\n    return str.replaceAll(/\\W/g, '').length;\n}\n\nfunction appendDate(initialDate, { day, month, year } = {}) {\n    const date = new Date(initialDate);\n    if (day) {\n        date.setDate(date.getDate() + day);\n    }\n    if (month) {\n        date.setMonth(date.getMonth() + month);\n    }\n    if (year) {\n        date.setFullYear(date.getFullYear() + year);\n    }\n    return date;\n}\n\nconst getDateSegmentValueLength = (dateString) => {\n    var _a, _b, _c, _d, _e, _f;\n    return ({\n        day: (_b = (_a = dateString.match(/d/g)) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0,\n        month: (_d = (_c = dateString.match(/m/g)) === null || _c === void 0 ? void 0 : _c.length) !== null && _d !== void 0 ? _d : 0,\n        year: (_f = (_e = dateString.match(/y/g)) === null || _e === void 0 ? void 0 : _e.length) !== null && _f !== void 0 ? _f : 0,\n    });\n};\n\nfunction dateToSegments(date) {\n    return {\n        day: String(date.getDate()).padStart(2, '0'),\n        month: String(date.getMonth() + 1).padStart(2, '0'),\n        year: String(date.getFullYear()).padStart(4, '0'),\n        hours: String(date.getHours()).padStart(2, '0'),\n        minutes: String(date.getMinutes()).padStart(2, '0'),\n        seconds: String(date.getSeconds()).padStart(2, '0'),\n        milliseconds: String(date.getMilliseconds()).padStart(3, '0'),\n    };\n}\n\nconst ALL_POSSIBLE_SEGMENTS = [\n    'day',\n    'month',\n    'year',\n];\nfunction getDateSegmentsOrder(template) {\n    return [...ALL_POSSIBLE_SEGMENTS].sort((a, b) => template.indexOf(a[0]) > template.indexOf(b[0]) ? 1 : -1);\n}\n\nfunction getFirstCompleteDate(dateString, dateModeTemplate) {\n    const digitsInDate = countDigits(dateModeTemplate);\n    const [completeDate = ''] = new RegExp(`(\\\\D*\\\\d){${digitsInDate}}`).exec(dateString) || [];\n    return completeDate;\n}\n\nfunction isDateStringComplete(dateString, dateModeTemplate) {\n    if (dateString.length < dateModeTemplate.length) {\n        return false;\n    }\n    return dateString.split(/\\D/).every((segment) => !/^0+$/.exec(segment));\n}\n\nfunction parseDateRangeString(dateRange, dateModeTemplate, rangeSeparator) {\n    const digitsInDate = countDigits(dateModeTemplate);\n    return (dateRange\n        .replace(rangeSeparator, '')\n        .match(new RegExp(`(\\\\D*\\\\d[^\\\\d\\\\s]*){1,${digitsInDate}}`, 'g')) || []);\n}\n\nfunction parseDateString(dateString, fullMode) {\n    const cleanMode = fullMode.replaceAll(/[^dmy]/g, '');\n    const onlyDigitsDate = dateString.replaceAll(/\\D+/g, '');\n    const dateSegments = {\n        day: onlyDigitsDate.slice(cleanMode.indexOf('d'), cleanMode.lastIndexOf('d') + 1),\n        month: onlyDigitsDate.slice(cleanMode.indexOf('m'), cleanMode.lastIndexOf('m') + 1),\n        year: onlyDigitsDate.slice(cleanMode.indexOf('y'), cleanMode.lastIndexOf('y') + 1),\n    };\n    return Object.fromEntries(Object.entries(dateSegments)\n        .filter(([_, value]) => Boolean(value))\n        .sort(([a], [b]) => fullMode.toLowerCase().indexOf(a.slice(0, 1)) >\n        fullMode.toLowerCase().indexOf(b.slice(0, 1))\n        ? 1\n        : -1));\n}\n\nfunction segmentsToDate(parsedDate, parsedTime) {\n    var _a, _b, _c, _d, _e, _f, _g;\n    const year = ((_a = parsedDate.year) === null || _a === void 0 ? void 0 : _a.length) === 2 ? `20${parsedDate.year}` : parsedDate.year;\n    const date = new Date(Number(year !== null && year !== void 0 ? year : '0'), Number((_b = parsedDate.month) !== null && _b !== void 0 ? _b : '1') - 1, Number((_c = parsedDate.day) !== null && _c !== void 0 ? _c : '1'), Number((_d = parsedTime === null || parsedTime === void 0 ? void 0 : parsedTime.hours) !== null && _d !== void 0 ? _d : '0'), Number((_e = parsedTime === null || parsedTime === void 0 ? void 0 : parsedTime.minutes) !== null && _e !== void 0 ? _e : '0'), Number((_f = parsedTime === null || parsedTime === void 0 ? void 0 : parsedTime.seconds) !== null && _f !== void 0 ? _f : '0'), Number((_g = parsedTime === null || parsedTime === void 0 ? void 0 : parsedTime.milliseconds) !== null && _g !== void 0 ? _g : '0'));\n    // needed for years less than 1900\n    date.setFullYear(Number(year !== null && year !== void 0 ? year : '0'));\n    return date;\n}\n\nconst DATE_TIME_SEPARATOR = ', ';\n\nfunction toDateString({ day, month, year, hours, minutes, seconds, milliseconds, }, { dateMode, dateTimeSeparator = DATE_TIME_SEPARATOR, timeMode, }) {\n    var _a;\n    const safeYear = ((_a = dateMode.match(/y/g)) === null || _a === void 0 ? void 0 : _a.length) === 2 ? year === null || year === void 0 ? void 0 : year.slice(-2) : year;\n    const fullMode = dateMode + (timeMode ? dateTimeSeparator + timeMode : '');\n    return fullMode\n        .replaceAll(/d+/g, day !== null && day !== void 0 ? day : '')\n        .replaceAll(/m+/g, month !== null && month !== void 0 ? month : '')\n        .replaceAll(/y+/g, safeYear !== null && safeYear !== void 0 ? safeYear : '')\n        .replaceAll(/H+/g, hours !== null && hours !== void 0 ? hours : '')\n        .replaceAll('MSS', milliseconds !== null && milliseconds !== void 0 ? milliseconds : '')\n        .replaceAll(/M+/g, minutes !== null && minutes !== void 0 ? minutes : '')\n        .replaceAll(/S+/g, seconds !== null && seconds !== void 0 ? seconds : '')\n        .replaceAll(/^\\D+/g, '')\n        .replaceAll(/\\D+$/g, '');\n}\n\nconst DATE_SEGMENTS_MAX_VALUES = {\n    day: 31,\n    month: 12,\n    year: 9999,\n};\n\n// eslint-disable-next-line i18n/no-russian-character\nconst DEFAULT_DECIMAL_PSEUDO_SEPARATORS = ['.', ',', 'б', 'ю'];\n\nconst DEFAULT_MIN_DATE = new Date('0001-01-01T00:00');\nconst DEFAULT_MAX_DATE = new Date('9999-12-31T23:59:59.999');\n\nconst DEFAULT_TIME_SEGMENT_MAX_VALUES = {\n    hours: 23,\n    minutes: 59,\n    seconds: 59,\n    milliseconds: 999,\n};\nconst DEFAULT_TIME_SEGMENT_MIN_VALUES = {\n    hours: 0,\n    minutes: 0,\n    seconds: 0,\n    milliseconds: 0,\n};\n\n/**\n * {@link https://unicode-table.com/en/00A0/ Non-breaking space}.\n */\nconst CHAR_NO_BREAK_SPACE = '\\u00A0';\n/**\n * {@link https://symbl.cc/en/200B/ Zero width space}.\n */\nconst CHAR_ZERO_WIDTH_SPACE = '\\u200B';\n/**\n * {@link https://unicode-table.com/en/2013/ EN dash}\n * is used to indicate a range of numbers or a span of time.\n * @example 2006–2022\n */\nconst CHAR_EN_DASH = '\\u2013';\n/**\n * {@link https://unicode-table.com/en/2014/ EM dash}\n * is used to mark a break in a sentence.\n * @example Taiga UI — powerful set of open source components for Angular\n * ___\n * Don't confuse with {@link CHAR_EN_DASH} or {@link CHAR_HYPHEN}!\n */\nconst CHAR_EM_DASH = '\\u2014';\n/**\n * {@link https://unicode-table.com/en/002D/ Hyphen (minus sign)}\n * is used to combine words.\n * @example well-behaved\n * ___\n * Don't confuse with {@link CHAR_EN_DASH} or {@link CHAR_EM_DASH}!\n */\nconst CHAR_HYPHEN = '\\u002D';\n/**\n * {@link https://unicode-table.com/en/2212/ Minus}\n * is used as math operator symbol or before negative digits.\n * ---\n * Can be used as `&minus;`. Don't confuse with {@link CHAR_HYPHEN}\n */\nconst CHAR_MINUS = '\\u2212';\n/**\n * {@link https://symbl.cc/en/30FC/ Katakana-Hiragana Prolonged Sound Mark}\n * is used as prolonged sounds in Japanese.\n */\nconst CHAR_JP_HYPHEN = '\\u30FC';\n/**\n * {@link https://symbl.cc/en/003A/ Colon}\n * is a punctuation mark that connects parts of a text logically.\n * ---\n * is also used as separator in time.\n */\nconst CHAR_COLON = '\\u003A';\n/**\n * {@link https://symbl.cc/en/FF1A/ Full-width colon}\n * is a full-width punctuation mark used to separate parts of a text commonly in Japanese.\n */\nconst CHAR_JP_COLON = '\\uFF1A';\n\nconst ANY_MERIDIEM_CHARACTER_RE = new RegExp(`[${CHAR_NO_BREAK_SPACE}APM]+$`, 'g');\nconst ALL_MERIDIEM_CHARACTERS_RE = new RegExp(`${CHAR_NO_BREAK_SPACE}[AP]M$`, 'g');\n\nconst TIME_FIXED_CHARACTERS = [':', '.'];\n\nconst TIME_SEGMENT_VALUE_LENGTHS = {\n    hours: 2,\n    minutes: 2,\n    seconds: 2,\n    milliseconds: 3,\n};\n\nfunction validateDateString({ dateString, dateModeTemplate, dateSegmentsSeparator, offset, selection: [from, to], }) {\n    var _a, _b;\n    const parsedDate = parseDateString(dateString, dateModeTemplate);\n    const dateSegments = Object.entries(parsedDate);\n    const segmentsOrder = getDateSegmentsOrder(dateModeTemplate);\n    const validatedDateSegments = {};\n    for (let i = 0; i < dateSegments.length; i++) {\n        const [segmentName, segmentValue] = dateSegments[i];\n        const validatedDate = toDateString(validatedDateSegments, {\n            dateMode: dateModeTemplate,\n        });\n        const maxSegmentValue = DATE_SEGMENTS_MAX_VALUES[segmentName];\n        const fantomSeparator = validatedDate.length && dateSegmentsSeparator.length;\n        const lastSegmentDigitIndex = offset +\n            validatedDate.length +\n            fantomSeparator +\n            getDateSegmentValueLength(dateModeTemplate)[segmentName];\n        const isLastSegmentDigitAdded = lastSegmentDigitIndex >= from && lastSegmentDigitIndex === to;\n        if (isLastSegmentDigitAdded && Number(segmentValue) > Number(maxSegmentValue)) {\n            const nextSegment = segmentsOrder[segmentsOrder.indexOf(segmentName) + 1];\n            if (!nextSegment || nextSegment === 'year') {\n                // 31.1|0.2010 => Type 9 => 31.1|0.2010\n                return { validatedDateString: '', updatedSelection: [from, to] }; // prevent changes\n            }\n            validatedDateSegments[segmentName] =\n                `0${segmentValue.slice(0, lastSegmentDigitIndex)}`;\n            dateSegments[i + 1] = [\n                nextSegment,\n                segmentValue.slice(-1) + ((_b = (_a = dateSegments[i + 1]) === null || _a === void 0 ? void 0 : _a[1]) !== null && _b !== void 0 ? _b : '').slice(1),\n            ];\n            continue;\n        }\n        if (isLastSegmentDigitAdded && Number(segmentValue) < 1) {\n            // 31.0|1.2010 => Type 0 => 31.0|1.2010\n            return { validatedDateString: '', updatedSelection: [from, to] }; // prevent changes\n        }\n        validatedDateSegments[segmentName] = segmentValue;\n    }\n    const validatedDateString = toDateString(validatedDateSegments, {\n        dateMode: dateModeTemplate,\n    });\n    const addedDateSegmentSeparators = validatedDateString.length - dateString.length;\n    return {\n        validatedDateString,\n        updatedSelection: [\n            from + addedDateSegmentSeparators,\n            to + addedDateSegmentSeparators,\n        ],\n    };\n}\n\nfunction identity(x) {\n    return x;\n}\n// eslint-disable-next-line  @typescript-eslint/no-empty-function\nfunction noop() { }\n\n/**\n * Copy-pasted solution from lodash\n * @see https://lodash.com/docs/4.17.15#escapeRegExp\n */\nconst reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g;\nconst reHasRegExpChar = new RegExp(reRegExpChar.source);\nfunction escapeRegExp(str) {\n    return str && reHasRegExpChar.test(str)\n        ? str.replaceAll(reRegExpChar, String.raw `\\$&`)\n        : str;\n}\n\nfunction extractAffixes(value, { prefix, postfix }) {\n    var _a, _b;\n    const prefixRegExp = new RegExp(`^${escapeRegExp(prefix)}`);\n    const postfixRegExp = new RegExp(`${escapeRegExp(postfix)}$`);\n    const [extractedPrefix = ''] = (_a = value.match(prefixRegExp)) !== null && _a !== void 0 ? _a : [];\n    const [extractedPostfix = ''] = (_b = value.match(postfixRegExp)) !== null && _b !== void 0 ? _b : [];\n    return {\n        extractedPrefix,\n        extractedPostfix,\n        cleanValue: extractedPrefix || extractedPostfix\n            ? value.slice(extractedPrefix.length, extractedPostfix.length ? -extractedPostfix.length : Infinity)\n            : value,\n    };\n}\n\nfunction findCommonBeginningSubstr(a, b) {\n    let res = '';\n    for (let i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) {\n            return res;\n        }\n        res += a[i];\n    }\n    return res;\n}\n\nfunction isEmpty(entity) {\n    return !entity || (typeof entity === 'object' && Object.keys(entity).length === 0);\n}\n\nconst ALL_ZEROES_RE = /^0+$/;\nfunction padWithZeroesUntilValid(segmentValue, paddedMaxValue, prefixedZeroesCount = 0) {\n    const paddedSegmentValue = segmentValue.padEnd(paddedMaxValue.length, '0');\n    if (Number(paddedSegmentValue) <= Number(paddedMaxValue)) {\n        return { validatedSegmentValue: segmentValue, prefixedZeroesCount };\n    }\n    if (paddedSegmentValue.endsWith('0')) {\n        // 00:|00 => Type 9 => 00:09|\n        return padWithZeroesUntilValid(`0${segmentValue.slice(0, paddedMaxValue.length - 1)}`, paddedMaxValue, prefixedZeroesCount + 1);\n    }\n    const valueWithoutLastChar = segmentValue.slice(0, paddedMaxValue.length - 1);\n    if (ALL_ZEROES_RE.exec(valueWithoutLastChar)) {\n        return { validatedSegmentValue: '', prefixedZeroesCount };\n    }\n    // |19:00 => Type 2 => 2|0:00\n    return padWithZeroesUntilValid(`${valueWithoutLastChar}0`, paddedMaxValue, prefixedZeroesCount);\n}\n\n/**\n * Replace fullwidth colon with half width colon\n * @param fullWidthColon full width colon\n * @returns processed half width colon\n */\nfunction toHalfWidthColon(fullWidthColon) {\n    return fullWidthColon.replaceAll(new RegExp(CHAR_JP_COLON, 'g'), CHAR_COLON);\n}\n\n/**\n * Replace fullwidth numbers with half width number\n * @param fullWidthNumber full width number\n * @returns processed half width number\n */\nfunction toHalfWidthNumber(fullWidthNumber) {\n    return fullWidthNumber.replaceAll(/[０-９]/g, (s) => String.fromCharCode(s.charCodeAt(0) - 0xfee0));\n}\n\n/**\n * Convert full width colon (：) to half width one (:)\n */\nfunction createColonConvertPreprocessor() {\n    return ({ elementState, data }) => {\n        const { value, selection } = elementState;\n        return {\n            elementState: {\n                selection,\n                value: toHalfWidthColon(value),\n            },\n            data: toHalfWidthColon(data),\n        };\n    };\n}\n\nfunction createDateSegmentsZeroPaddingPostprocessor({ dateModeTemplate, dateSegmentSeparator, splitFn, uniteFn, }) {\n    return ({ value, selection }) => {\n        var _a;\n        const [from, to] = selection;\n        const { dateStrings, restPart = '' } = splitFn(value);\n        const validatedDateStrings = [];\n        let caretShift = 0;\n        dateStrings.forEach((dateString) => {\n            const parsedDate = parseDateString(dateString, dateModeTemplate);\n            const dateSegments = Object.entries(parsedDate);\n            const validatedDateSegments = dateSegments.reduce((acc, [segmentName, segmentValue]) => {\n                const { validatedSegmentValue, prefixedZeroesCount } = padWithZeroesUntilValid(segmentValue, `${DATE_SEGMENTS_MAX_VALUES[segmentName]}`);\n                caretShift += prefixedZeroesCount;\n                return Object.assign(Object.assign({}, acc), { [segmentName]: validatedSegmentValue });\n            }, {});\n            validatedDateStrings.push(toDateString(validatedDateSegments, { dateMode: dateModeTemplate }));\n        });\n        const validatedValue = uniteFn(validatedDateStrings, value) +\n            (((_a = dateStrings[dateStrings.length - 1]) === null || _a === void 0 ? void 0 : _a.endsWith(dateSegmentSeparator))\n                ? dateSegmentSeparator\n                : '') +\n            restPart;\n        if (caretShift &&\n            validatedValue.slice(to + caretShift, to + caretShift + dateSegmentSeparator.length) === dateSegmentSeparator) {\n            /**\n             * If `caretShift` > 0, it means that time segment was padded with zero.\n             * It is only possible if any character insertion happens.\n             * If caret is before `dateSegmentSeparator` => it should be moved after `dateSegmentSeparator`.\n             */\n            caretShift += dateSegmentSeparator.length;\n        }\n        return {\n            selection: [from + caretShift, to + caretShift],\n            value: validatedValue,\n        };\n    };\n}\n\n/**\n * It replaces pseudo range separators with valid one.\n * @example '01.01.2000_11.11.2000' -> '01.01.2000 - 01.01.2000'\n * @example '01.01.2000_23:59' -> '01.01.2000, 23:59'\n */\nfunction createFirstDateEndSeparatorPreprocessor({ dateModeTemplate, firstDateEndSeparator, dateSegmentSeparator, pseudoFirstDateEndSeparators, }) {\n    return ({ elementState, data }) => {\n        const { value, selection } = elementState;\n        const [from, to] = selection;\n        const firstCompleteDate = getFirstCompleteDate(value, dateModeTemplate);\n        const pseudoSeparators = pseudoFirstDateEndSeparators.filter((x) => !firstDateEndSeparator.includes(x) && x !== dateSegmentSeparator);\n        const pseudoSeparatorsRE = new RegExp(`[${pseudoSeparators.join('')}]`, 'gi');\n        const newValue = firstCompleteDate && value.length > firstCompleteDate.length\n            ? firstCompleteDate +\n                value\n                    .slice(firstCompleteDate.length)\n                    .replace(/^[\\D\\s]*/, firstDateEndSeparator)\n            : value;\n        const caretShift = newValue.length - value.length;\n        return {\n            elementState: {\n                selection: [from + caretShift, to + caretShift],\n                value: newValue,\n            },\n            data: data.replace(pseudoSeparatorsRE, firstDateEndSeparator),\n        };\n    };\n}\n\n/**\n * Convert full width numbers like １, ２ to half width numbers 1, 2\n */\nfunction createFullWidthToHalfWidthPreprocessor() {\n    return ({ elementState, data }) => {\n        const { value, selection } = elementState;\n        return {\n            elementState: {\n                selection,\n                value: toHalfWidthNumber(value),\n            },\n            data: toHalfWidthNumber(data),\n        };\n    };\n}\n\nfunction createTimeMaskExpression(mode) {\n    return Array.from(mode.replace(' AA', ''))\n        .map((char) => (TIME_FIXED_CHARACTERS.includes(char) ? char : /\\d/))\n        .concat(mode.includes('AA') ? [CHAR_NO_BREAK_SPACE, /[AP]/i, /M/i] : []);\n}\n\nfunction padTimeSegments(timeSegments, pad) {\n    return Object.fromEntries(Object.entries(timeSegments).map(([segmentName, segmentValue]) => [\n        segmentName,\n        pad(String(segmentValue), TIME_SEGMENT_VALUE_LENGTHS[segmentName]),\n    ]));\n}\n\nfunction padStartTimeSegments(timeSegments) {\n    return padTimeSegments(timeSegments, (value, length) => value.padStart(length, '0'));\n}\n\nconst SEGMENT_FULL_NAME = {\n    HH: 'hours',\n    MM: 'minutes',\n    SS: 'seconds',\n    MSS: 'milliseconds',\n};\n/**\n * @param timeString can be with/without fixed characters\n */\nfunction parseTimeString(timeString, timeMode) {\n    const onlyDigits = timeString.replaceAll(/\\D+/g, '');\n    let offset = 0;\n    return Object.fromEntries(timeMode\n        .split(/\\W/)\n        .filter((segmentAbbr) => SEGMENT_FULL_NAME[segmentAbbr])\n        .map((segmentAbbr) => {\n        const segmentValue = onlyDigits.slice(offset, offset + segmentAbbr.length);\n        offset += segmentAbbr.length;\n        return [SEGMENT_FULL_NAME[segmentAbbr], segmentValue];\n    }));\n}\n\nconst LEADING_NON_DIGITS = /^\\D*/;\nconst TRAILING_NON_DIGITS = /\\D*$/;\nfunction toTimeString({ hours = '', minutes = '', seconds = '', milliseconds = '', }) {\n    return `${hours}:${minutes}:${seconds}.${milliseconds}`\n        .replace(LEADING_NON_DIGITS, '')\n        .replace(TRAILING_NON_DIGITS, '');\n}\n\nconst TRAILING_TIME_SEGMENT_SEPARATOR_REG = new RegExp(`[${TIME_FIXED_CHARACTERS.map(escapeRegExp).join('')}]$`);\n/**\n * Pads invalid time segment with zero to make it valid.\n * @example 00:|00 => Type 9 (too much for the first digit of minutes) => 00:09|\n * @example |19:00 => Type 2 (29 - invalid value for hours) => 2|0:00\n */\nfunction enrichTimeSegmentsWithZeroes({ value, selection }, { mode, timeSegmentMaxValues = DEFAULT_TIME_SEGMENT_MAX_VALUES, }) {\n    const [from, to] = selection;\n    const parsedTime = parseTimeString(value, mode);\n    const possibleTimeSegments = Object.entries(parsedTime);\n    const paddedMaxValues = padStartTimeSegments(timeSegmentMaxValues);\n    const validatedTimeSegments = {};\n    let paddedZeroes = 0;\n    for (const [segmentName, segmentValue] of possibleTimeSegments) {\n        const maxSegmentValue = paddedMaxValues[segmentName];\n        const { validatedSegmentValue, prefixedZeroesCount } = padWithZeroesUntilValid(segmentValue, String(maxSegmentValue));\n        paddedZeroes += prefixedZeroesCount;\n        validatedTimeSegments[segmentName] = validatedSegmentValue;\n    }\n    // trailing segment separators or meridiem characters\n    const [trailingNonDigitCharacters = ''] = value.match(/\\D+$/g) || [];\n    const validatedTimeString = toTimeString(validatedTimeSegments) + trailingNonDigitCharacters;\n    const addedDateSegmentSeparators = Math.max(validatedTimeString.length - value.length, 0);\n    let newFrom = from + paddedZeroes + addedDateSegmentSeparators;\n    let newTo = to + paddedZeroes + addedDateSegmentSeparators;\n    if (newFrom === newTo &&\n        paddedZeroes &&\n        // if next character after cursor is time segment separator\n        validatedTimeString.slice(0, newTo + 1).match(TRAILING_TIME_SEGMENT_SEPARATOR_REG)) {\n        newFrom++;\n        newTo++;\n    }\n    return {\n        value: validatedTimeString,\n        selection: [newFrom, newTo],\n    };\n}\n\nfunction padEndTimeSegments(timeSegments) {\n    return padTimeSegments(timeSegments, (value, length) => value.padEnd(length, '0'));\n}\n\n/**\n * Prevent insertion if any time segment will become invalid\n * (and even zero padding won't help with it).\n * @example 2|0:00 => Type 9 => 2|0:00\n */\nfunction createInvalidTimeSegmentInsertionPreprocessor({ timeMode, timeSegmentMinValues = DEFAULT_TIME_SEGMENT_MIN_VALUES, timeSegmentMaxValues = DEFAULT_TIME_SEGMENT_MAX_VALUES, parseValue = (x) => ({ timeString: x }), }) {\n    const invalidCharsRegExp = new RegExp(`[^\\\\d${TIME_FIXED_CHARACTERS.map(escapeRegExp).join('')}]+`);\n    return ({ elementState, data }, actionType) => {\n        if (actionType !== 'insert') {\n            return { elementState, data };\n        }\n        const { value, selection } = elementState;\n        const [from, rawTo] = selection;\n        const newCharacters = data.replace(invalidCharsRegExp, '');\n        const to = rawTo + newCharacters.length; // to be conformed with `overwriteMode: replace`\n        const newPossibleValue = value.slice(0, from) + newCharacters + value.slice(to);\n        const { timeString, restValue = '' } = parseValue(newPossibleValue);\n        const timeSegments = Object.entries(parseTimeString(timeString, timeMode));\n        let offset = restValue.length;\n        for (const [segmentName, stringifiedSegmentValue] of timeSegments) {\n            const minSegmentValue = timeSegmentMinValues[segmentName];\n            const maxSegmentValue = timeSegmentMaxValues[segmentName];\n            const segmentValue = Number(stringifiedSegmentValue);\n            const lastSegmentDigitIndex = offset + TIME_SEGMENT_VALUE_LENGTHS[segmentName];\n            if (lastSegmentDigitIndex >= from &&\n                lastSegmentDigitIndex <= to &&\n                segmentValue !== clamp(segmentValue, minSegmentValue, maxSegmentValue)) {\n                return { elementState, data: '' }; // prevent insertion\n            }\n            offset +=\n                stringifiedSegmentValue.length +\n                    // any time segment separator\n                    1;\n        }\n        return { elementState, data };\n    };\n}\n\nfunction createMeridiemPreprocessor(timeMode) {\n    if (!timeMode.includes('AA')) {\n        return identity;\n    }\n    const mainMeridiemCharRE = /^[AP]$/gi;\n    return ({ elementState, data }) => {\n        const { value, selection } = elementState;\n        const newValue = value.toUpperCase();\n        const newData = data.toUpperCase();\n        if (newValue.match(ALL_MERIDIEM_CHARACTERS_RE) &&\n            newData.match(mainMeridiemCharRE)) {\n            return {\n                elementState: {\n                    value: newValue.replaceAll(ALL_MERIDIEM_CHARACTERS_RE, ''),\n                    selection,\n                },\n                data: `${newData}M`,\n            };\n        }\n        return { elementState: { selection, value: newValue }, data: newData };\n    };\n}\nfunction createMeridiemPostprocessor(timeMode) {\n    if (!timeMode.includes('AA')) {\n        return identity;\n    }\n    return ({ value, selection }, initialElementState) => {\n        if (!value.match(ANY_MERIDIEM_CHARACTER_RE) ||\n            value.match(ALL_MERIDIEM_CHARACTERS_RE)) {\n            return { value, selection };\n        }\n        const [from, to] = selection;\n        // any meridiem character was deleted\n        if (initialElementState.value.match(ALL_MERIDIEM_CHARACTERS_RE)) {\n            const newValue = value.replace(ANY_MERIDIEM_CHARACTER_RE, '');\n            return {\n                value: newValue,\n                selection: [\n                    Math.min(from, newValue.length),\n                    Math.min(to, newValue.length),\n                ],\n            };\n        }\n        const fullMeridiem = `${CHAR_NO_BREAK_SPACE}${value.includes('P') ? 'P' : 'A'}M`;\n        const newValue = value.replace(ANY_MERIDIEM_CHARACTER_RE, (x) => x !== CHAR_NO_BREAK_SPACE ? fullMeridiem : x);\n        return {\n            value: newValue,\n            selection: to >= newValue.indexOf(fullMeridiem)\n                ? [newValue.length, newValue.length]\n                : selection,\n        };\n    };\n}\n\nfunction raiseSegmentValueToMin(segments, fullMode) {\n    const segmentsLength = getDateSegmentValueLength(fullMode);\n    return Object.fromEntries(Object.entries(segments).map(([key, value]) => {\n        const segmentLength = segmentsLength[key];\n        return [\n            key,\n            value.length === segmentLength && /^0+$/.exec(value)\n                ? '1'.padStart(segmentLength, '0')\n                : value,\n        ];\n    }));\n}\n\nconst LEAP_YEAR = '1972';\nfunction createMinMaxDatePostprocessor({ dateModeTemplate, min = DEFAULT_MIN_DATE, max = DEFAULT_MAX_DATE, rangeSeparator = '', dateSegmentSeparator = '.', }) {\n    return ({ value, selection }) => {\n        const endsWithRangeSeparator = rangeSeparator && value.endsWith(rangeSeparator);\n        const dateStrings = parseDateRangeString(value, dateModeTemplate, rangeSeparator);\n        let validatedValue = '';\n        for (const dateString of dateStrings) {\n            validatedValue += validatedValue ? rangeSeparator : '';\n            const parsedDate = parseDateString(dateString, dateModeTemplate);\n            if (!isDateStringComplete(dateString, dateModeTemplate)) {\n                const fixedDate = raiseSegmentValueToMin(parsedDate, dateModeTemplate);\n                const fixedValue = toDateString(fixedDate, { dateMode: dateModeTemplate });\n                const tail = dateString.endsWith(dateSegmentSeparator)\n                    ? dateSegmentSeparator\n                    : '';\n                validatedValue += fixedValue + tail;\n                continue;\n            }\n            const date = segmentsToDate(Object.assign({ year: LEAP_YEAR }, parsedDate));\n            const clampedDate = clamp(date, min, max);\n            validatedValue += toDateString(dateToSegments(clampedDate), {\n                dateMode: dateModeTemplate,\n            });\n        }\n        return {\n            selection,\n            value: validatedValue + (endsWithRangeSeparator ? rangeSeparator : ''),\n        };\n    };\n}\n\nfunction normalizeDatePreprocessor({ dateModeTemplate, dateSegmentsSeparator, rangeSeparator = '', dateTimeSeparator = DATE_TIME_SEPARATOR, }) {\n    return ({ elementState, data }) => {\n        const templateSegments = dateModeTemplate.split(dateSegmentsSeparator);\n        const includesTime = data.includes(dateTimeSeparator);\n        const dateSegments = data\n            .slice(0, includesTime ? data.indexOf(dateTimeSeparator) : Infinity)\n            .split(/\\D/)\n            .filter(Boolean);\n        if (!dateSegments.length || dateSegments.length % templateSegments.length !== 0) {\n            return { elementState, data };\n        }\n        const dates = dateSegments.reduce((dates, segment, index) => {\n            var _a;\n            const template = (_a = templateSegments[index % templateSegments.length]) !== null && _a !== void 0 ? _a : '';\n            const dateIndex = Math.trunc(index / templateSegments.length);\n            const isLastDateSegment = index % templateSegments.length === templateSegments.length - 1;\n            if (!dates[dateIndex]) {\n                dates[dateIndex] = '';\n            }\n            dates[dateIndex] += isLastDateSegment\n                ? segment\n                : `${segment.padStart(template.length, '0')}${dateSegmentsSeparator}`;\n            return dates;\n        }, []);\n        return {\n            elementState,\n            data: includesTime\n                ? `${dates[0]}${data.slice(data.indexOf(dateTimeSeparator))}`\n                : dates.join(rangeSeparator),\n        };\n    };\n}\n\nfunction maskitoPostfixPostprocessorGenerator(postfix) {\n    const postfixRE = new RegExp(`${escapeRegExp(postfix)}$`);\n    return postfix\n        ? ({ value, selection }, initialElementState) => {\n            if (!value && !initialElementState.value.endsWith(postfix)) {\n                // cases when developer wants input to be empty (programmatically)\n                return { value, selection };\n            }\n            if (!value.endsWith(postfix) &&\n                !initialElementState.value.endsWith(postfix)) {\n                return { selection, value: value + postfix };\n            }\n            const initialValueBeforePostfix = initialElementState.value.replace(postfixRE, '');\n            const postfixWasModified = initialElementState.selection[1] > initialValueBeforePostfix.length;\n            const alreadyExistedValueBeforePostfix = findCommonBeginningSubstr(initialValueBeforePostfix, value);\n            return {\n                selection,\n                value: Array.from(postfix)\n                    .reverse()\n                    .reduce((newValue, char, index) => {\n                    const i = newValue.length - 1 - index;\n                    const isInitiallyMirroredChar = alreadyExistedValueBeforePostfix[i] === char &&\n                        postfixWasModified;\n                    return newValue[i] !== char || isInitiallyMirroredChar\n                        ? newValue.slice(0, i + 1) + char + newValue.slice(i + 1)\n                        : newValue;\n                }, value),\n            };\n        }\n        : identity;\n}\n\nfunction maskitoPrefixPostprocessorGenerator(prefix) {\n    return prefix\n        ? ({ value, selection }, initialElementState) => {\n            if (value.startsWith(prefix) || // already valid\n                (!value && !initialElementState.value.startsWith(prefix)) // cases when developer wants input to be empty\n            ) {\n                return { value, selection };\n            }\n            const [from, to] = selection;\n            const prefixedValue = Array.from(prefix).reduce((modifiedValue, char, i) => modifiedValue[i] === char\n                ? modifiedValue\n                : modifiedValue.slice(0, i) + char + modifiedValue.slice(i), value);\n            const addedCharsCount = prefixedValue.length - value.length;\n            return {\n                selection: [from + addedCharsCount, to + addedCharsCount],\n                value: prefixedValue,\n            };\n        }\n        : identity;\n}\n\nfunction createValidDatePreprocessor({ dateModeTemplate, dateSegmentsSeparator, rangeSeparator = '', }) {\n    return ({ elementState, data }) => {\n        const { value, selection } = elementState;\n        if (data === dateSegmentsSeparator) {\n            return {\n                elementState,\n                data: selection[0] === value.length ? data : '',\n            };\n        }\n        const newCharacters = data.replaceAll(new RegExp(`[^\\\\d${escapeRegExp(dateSegmentsSeparator)}${rangeSeparator}]`, 'g'), '');\n        if (!newCharacters) {\n            return { elementState, data: '' };\n        }\n        const [from, rawTo] = selection;\n        let to = rawTo + data.length;\n        const newPossibleValue = value.slice(0, from) + newCharacters + value.slice(to);\n        const dateStrings = parseDateRangeString(newPossibleValue, dateModeTemplate, rangeSeparator);\n        let validatedValue = '';\n        const hasRangeSeparator = Boolean(rangeSeparator) && newPossibleValue.includes(rangeSeparator);\n        for (const dateString of dateStrings) {\n            const { validatedDateString, updatedSelection } = validateDateString({\n                dateString,\n                dateModeTemplate,\n                dateSegmentsSeparator,\n                offset: validatedValue.length,\n                selection: [from, to],\n            });\n            if (dateString && !validatedDateString) {\n                return { elementState, data: '' }; // prevent changes\n            }\n            to = updatedSelection[1];\n            validatedValue +=\n                hasRangeSeparator && !validatedValue\n                    ? validatedDateString + rangeSeparator\n                    : validatedDateString;\n        }\n        const newData = validatedValue.slice(from, to);\n        return {\n            elementState: {\n                selection,\n                value: validatedValue.slice(0, from) +\n                    newData\n                        .split(dateSegmentsSeparator)\n                        .map((segment) => '0'.repeat(segment.length))\n                        .join(dateSegmentsSeparator) +\n                    validatedValue.slice(to),\n            },\n            data: newData,\n        };\n    };\n}\n\nfunction maskitoEventHandler(name, handler, eventListenerOptions) {\n    return (element, maskitoOptions) => {\n        const listener = () => handler(element, maskitoOptions);\n        element.addEventListener(name, listener, eventListenerOptions);\n        return () => element.removeEventListener(name, listener, eventListenerOptions);\n    };\n}\n\nfunction maskitoAddOnFocusPlugin(value) {\n    return maskitoEventHandler('focus', (element) => {\n        if (!element.value) {\n            maskitoUpdateElement(element, value);\n        }\n    });\n}\n\nfunction maskitoSelectionChangeHandler(handler) {\n    return (element, options) => {\n        const document = element.ownerDocument;\n        let isPointerDown = 0;\n        const onPointerDown = () => isPointerDown++;\n        const onPointerUp = () => {\n            isPointerDown = Math.max(--isPointerDown, 0);\n        };\n        const listener = () => {\n            if (!element.matches(':focus')) {\n                return;\n            }\n            if (isPointerDown) {\n                return document.addEventListener('mouseup', listener, {\n                    once: true,\n                    passive: true,\n                });\n            }\n            handler(element, options);\n        };\n        document.addEventListener('selectionchange', listener, { passive: true });\n        // Safari does not fire `selectionchange` on focus after programmatic update of textfield value\n        element.addEventListener('focus', listener, { passive: true });\n        element.addEventListener('mousedown', onPointerDown, { passive: true });\n        document.addEventListener('mouseup', onPointerUp, { passive: true });\n        return () => {\n            document.removeEventListener('selectionchange', listener);\n            element.removeEventListener('focus', listener);\n            element.removeEventListener('mousedown', onPointerDown);\n            document.removeEventListener('mouseup', onPointerUp);\n        };\n    };\n}\n\nfunction maskitoCaretGuard(guard) {\n    return maskitoSelectionChangeHandler((element) => {\n        var _a, _b;\n        const start = (_a = element.selectionStart) !== null && _a !== void 0 ? _a : 0;\n        const end = (_b = element.selectionEnd) !== null && _b !== void 0 ? _b : 0;\n        const [fromLimit, toLimit] = guard(element.value, [start, end]);\n        if (fromLimit > start || toLimit < end) {\n            element.setSelectionRange(clamp(start, fromLimit, toLimit), clamp(end, fromLimit, toLimit));\n        }\n    });\n}\n\nconst maskitoRejectEvent = (element) => {\n    const listener = () => {\n        const value = element.value;\n        element.addEventListener('beforeinput', (event) => {\n            if (event.defaultPrevented && value === element.value) {\n                element.dispatchEvent(new CustomEvent('maskitoReject', { bubbles: true }));\n            }\n        }, { once: true });\n    };\n    element.addEventListener('beforeinput', listener, true);\n    return () => element.removeEventListener('beforeinput', listener, true);\n};\n\nfunction maskitoRemoveOnBlurPlugin(value) {\n    return maskitoEventHandler('blur', (element) => {\n        if (element.value === value) {\n            maskitoUpdateElement(element, '');\n        }\n    });\n}\n\nfunction createMeridiemSteppingPlugin(meridiemStartIndex) {\n    if (meridiemStartIndex < 0) {\n        return noop;\n    }\n    return (element) => {\n        const listener = (event) => {\n            const caretIndex = Number(element.selectionStart);\n            const value = element.value.toUpperCase();\n            if ((event.key !== 'ArrowUp' && event.key !== 'ArrowDown') ||\n                caretIndex < meridiemStartIndex) {\n                return;\n            }\n            event.preventDefault();\n            // eslint-disable-next-line no-nested-ternary\n            const meridiemMainCharacter = value.includes('A')\n                ? 'P'\n                : value.includes('P') || event.key === 'ArrowUp'\n                    ? 'A'\n                    : 'P';\n            const newMeridiem = `${CHAR_NO_BREAK_SPACE}${meridiemMainCharacter}M`;\n            maskitoUpdateElement(element, {\n                value: value.length === meridiemStartIndex\n                    ? value + newMeridiem\n                    : value.replace(ANY_MERIDIEM_CHARACTER_RE, newMeridiem),\n                selection: [caretIndex, caretIndex],\n            });\n        };\n        element.addEventListener('keydown', listener);\n        return () => element.removeEventListener('keydown', listener);\n    };\n}\n\nfunction createTimeSegmentsSteppingPlugin({ step, fullMode, timeSegmentMaxValues, }) {\n    const segmentsIndexes = createTimeSegmentsIndexes(fullMode);\n    return step <= 0\n        ? noop\n        : (element) => {\n            const listener = (event) => {\n                var _a;\n                if (event.key !== 'ArrowUp' && event.key !== 'ArrowDown') {\n                    return;\n                }\n                event.preventDefault();\n                const selectionStart = (_a = element.selectionStart) !== null && _a !== void 0 ? _a : 0;\n                const activeSegment = getActiveSegment({\n                    segmentsIndexes,\n                    selectionStart,\n                });\n                if (!activeSegment) {\n                    return;\n                }\n                const updatedValue = updateSegmentValue({\n                    selection: segmentsIndexes.get(activeSegment),\n                    value: element.value,\n                    toAdd: event.key === 'ArrowUp' ? step : -step,\n                    max: timeSegmentMaxValues[activeSegment],\n                });\n                maskitoUpdateElement(element, {\n                    value: updatedValue,\n                    selection: [selectionStart, selectionStart],\n                });\n            };\n            element.addEventListener('keydown', listener);\n            return () => element.removeEventListener('keydown', listener);\n        };\n}\nfunction createTimeSegmentsIndexes(fullMode) {\n    return new Map([\n        ['hours', getSegmentRange(fullMode, 'HH')],\n        ['milliseconds', getSegmentRange(fullMode, 'MSS')],\n        ['minutes', getSegmentRange(fullMode, 'MM')],\n        ['seconds', getSegmentRange(fullMode, 'SS')],\n    ]);\n}\nfunction getSegmentRange(mode, segment) {\n    const index = mode.indexOf(segment);\n    return index === -1 ? [-1, -1] : [index, index + segment.length];\n}\nfunction getActiveSegment({ segmentsIndexes, selectionStart, }) {\n    for (const [segmentName, segmentRange] of segmentsIndexes.entries()) {\n        const [from, to] = segmentRange;\n        if (from <= selectionStart && selectionStart <= to) {\n            return segmentName;\n        }\n    }\n    return null;\n}\nfunction updateSegmentValue({ selection, value, toAdd, max, }) {\n    const [from, to] = selection;\n    const segmentValue = Number(value.slice(from, to).padEnd(to - from, '0'));\n    const newSegmentValue = mod(segmentValue + toAdd, max + 1);\n    return (value.slice(0, from) +\n        String(newSegmentValue).padStart(to - from, '0') +\n        value.slice(to, value.length));\n}\nfunction mod(value, max) {\n    if (value < 0) {\n        value += Math.floor(Math.abs(value) / max + 1) * max;\n    }\n    return value % max;\n}\n\nfunction maskitoWithPlaceholder(placeholder, focusedOnly = false) {\n    let lastClearValue = '';\n    let action = 'validation';\n    const removePlaceholder = (value) => {\n        for (let i = value.length - 1; i >= lastClearValue.length; i--) {\n            if (value[i] !== placeholder[i]) {\n                return value.slice(0, i + 1);\n            }\n        }\n        return value.slice(0, lastClearValue.length);\n    };\n    const plugins = [maskitoCaretGuard((value) => [0, removePlaceholder(value).length])];\n    let focused = false;\n    if (focusedOnly) {\n        const focus = maskitoEventHandler('focus', (element) => {\n            focused = true;\n            maskitoUpdateElement(element, element.value + placeholder.slice(element.value.length));\n        }, { capture: true });\n        const blur = maskitoEventHandler('blur', (element) => {\n            focused = false;\n            maskitoUpdateElement(element, removePlaceholder(element.value));\n        }, { capture: true });\n        plugins.push(focus, blur);\n    }\n    return {\n        plugins,\n        removePlaceholder,\n        preprocessors: [\n            ({ elementState, data }, actionType) => {\n                action = actionType;\n                const { value, selection } = elementState;\n                return {\n                    elementState: {\n                        selection,\n                        value: removePlaceholder(value),\n                    },\n                    data,\n                };\n            },\n        ],\n        postprocessors: [\n            ({ value, selection }, initialElementState) => {\n                lastClearValue = value;\n                const justPlaceholderRemoval = value +\n                    placeholder.slice(value.length, initialElementState.value.length) ===\n                    initialElementState.value;\n                if (action === 'validation' && justPlaceholderRemoval) {\n                    /**\n                     * If `value` still equals to `initialElementState.value`,\n                     * then it means that value is patched programmatically (from Maskito's plugin or externally).\n                     * In this case, we don't want to mutate value and automatically add/remove placeholder.\n                     * ___\n                     * For example, developer wants to remove manually placeholder (+ do something else with value) on blur.\n                     * Without this condition, placeholder will be unexpectedly added again.\n                     */\n                    return { selection, value: initialElementState.value };\n                }\n                const newValue = focused || !focusedOnly\n                    ? value + placeholder.slice(value.length)\n                    : value;\n                if (newValue === initialElementState.value &&\n                    action === 'deleteBackward') {\n                    const [caretIndex] = initialElementState.selection;\n                    return {\n                        value: newValue,\n                        selection: [caretIndex, caretIndex],\n                    };\n                }\n                return { value: newValue, selection };\n            },\n        ],\n    };\n}\n\nfunction createZeroPlaceholdersPreprocessor() {\n    return ({ elementState }, actionType) => {\n        const { value, selection } = elementState;\n        if (!value || isLastChar(value, selection)) {\n            return { elementState };\n        }\n        const [from, to] = selection;\n        const zeroes = value.slice(from, to).replaceAll(/\\d/g, '0');\n        const newValue = value.slice(0, from) + zeroes + value.slice(to);\n        if (!zeroes.replaceAll(/\\D/g, '')) {\n            return { elementState };\n        }\n        if (actionType === 'validation' || (actionType === 'insert' && from === to)) {\n            return {\n                elementState: { selection, value: newValue },\n            };\n        }\n        return {\n            elementState: {\n                selection: actionType === 'deleteBackward' || actionType === 'insert'\n                    ? [from, from]\n                    : [to, to],\n                value: newValue,\n            },\n        };\n    };\n}\nfunction isLastChar(value, [_, to]) {\n    return to === value.length;\n}\n\nfunction maskitoDateOptionsGenerator({ mode, separator = '.', max, min, }) {\n    const dateModeTemplate = mode.split('/').join(separator);\n    return Object.assign(Object.assign({}, MASKITO_DEFAULT_OPTIONS), { mask: Array.from(dateModeTemplate).map((char) => separator.includes(char) ? char : /\\d/), overwriteMode: 'replace', preprocessors: [\n            createFullWidthToHalfWidthPreprocessor(),\n            createZeroPlaceholdersPreprocessor(),\n            normalizeDatePreprocessor({\n                dateModeTemplate,\n                dateSegmentsSeparator: separator,\n            }),\n            createValidDatePreprocessor({\n                dateModeTemplate,\n                dateSegmentsSeparator: separator,\n            }),\n        ], postprocessors: [\n            createDateSegmentsZeroPaddingPostprocessor({\n                dateModeTemplate,\n                dateSegmentSeparator: separator,\n                splitFn: (value) => ({ dateStrings: [value] }),\n                uniteFn: ([dateString = '']) => dateString,\n            }),\n            createMinMaxDatePostprocessor({\n                min,\n                max,\n                dateModeTemplate,\n                dateSegmentSeparator: separator,\n            }),\n        ] });\n}\n\nfunction maskitoParseDate(value, { mode, min = DEFAULT_MIN_DATE, max = DEFAULT_MAX_DATE }) {\n    if (value.length < mode.length) {\n        return null;\n    }\n    const dateSegments = parseDateString(value, mode);\n    const parsedDate = segmentsToDate(dateSegments);\n    return clamp(parsedDate, min, max);\n}\n\nconst formatter = Intl.DateTimeFormat('en-US', {\n    month: '2-digit',\n    day: '2-digit',\n    year: 'numeric',\n});\nfunction toDateSegments(date) {\n    return formatter\n        .formatToParts(date)\n        .reduce((acc, part) => (Object.assign(Object.assign({}, acc), { [part.type]: part.value })), {});\n}\n\nfunction maskitoStringifyDate(date, { mode, separator = '.', min = DEFAULT_MIN_DATE, max = DEFAULT_MAX_DATE, }) {\n    const validatedDate = clamp(date, min, max);\n    const segments = toDateSegments(validatedDate);\n    return toDateString(segments, {\n        dateMode: mode.replaceAll('/', separator),\n    });\n}\n\nconst POSSIBLE_DATE_RANGE_SEPARATOR = [\n    CHAR_HYPHEN,\n    CHAR_EN_DASH,\n    CHAR_EM_DASH,\n    CHAR_MINUS,\n    CHAR_JP_HYPHEN,\n];\n\nfunction createMinMaxRangeLengthPostprocessor({ dateModeTemplate, rangeSeparator, minLength, maxLength, max = DEFAULT_MAX_DATE, }) {\n    if (isEmpty(minLength) && isEmpty(maxLength)) {\n        return identity;\n    }\n    return ({ value, selection }) => {\n        const dateStrings = parseDateRangeString(value, dateModeTemplate, rangeSeparator);\n        if (dateStrings.length !== 2 ||\n            dateStrings.some((date) => !isDateStringComplete(date, dateModeTemplate))) {\n            return { value, selection };\n        }\n        const [fromDate, toDate] = dateStrings.map((dateString) => segmentsToDate(parseDateString(dateString, dateModeTemplate)));\n        if (!fromDate || !toDate) {\n            return { value, selection };\n        }\n        const minDistantToDate = appendDate(fromDate, Object.assign(Object.assign({}, minLength), { \n            // 06.02.2023 - 07.02.2023 => {minLength: {day: 3}} => 06.02.2023 - 08.02.2023\n            // \"from\"-day is included in the range\n            day: (minLength === null || minLength === void 0 ? void 0 : minLength.day) && minLength.day - 1 }));\n        const maxDistantToDate = !isEmpty(maxLength)\n            ? appendDate(fromDate, Object.assign(Object.assign({}, maxLength), { day: (maxLength === null || maxLength === void 0 ? void 0 : maxLength.day) && maxLength.day - 1 }))\n            : max;\n        const minLengthClampedToDate = clamp(toDate, minDistantToDate, max);\n        const minMaxLengthClampedToDate = minLengthClampedToDate > maxDistantToDate\n            ? maxDistantToDate\n            : minLengthClampedToDate;\n        return {\n            selection,\n            value: dateStrings[0] +\n                rangeSeparator +\n                toDateString(dateToSegments(minMaxLengthClampedToDate), {\n                    dateMode: dateModeTemplate,\n                }),\n        };\n    };\n}\n\nfunction createSwapDatesPostprocessor({ dateModeTemplate, rangeSeparator, }) {\n    return ({ value, selection }) => {\n        const dateStrings = parseDateRangeString(value, dateModeTemplate, rangeSeparator);\n        const isDateRangeComplete = dateStrings.length === 2 &&\n            dateStrings.every((date) => isDateStringComplete(date, dateModeTemplate));\n        const [from, to] = selection;\n        const caretAtTheEnd = from >= value.length;\n        const allValueSelected = from === 0 && to >= value.length; // dropping text inside with a pointer\n        if (!(caretAtTheEnd || allValueSelected) || !isDateRangeComplete) {\n            return { value, selection };\n        }\n        const [fromDate, toDate] = dateStrings.map((dateString) => segmentsToDate(parseDateString(dateString, dateModeTemplate)));\n        return {\n            selection,\n            value: fromDate && toDate && fromDate > toDate\n                ? dateStrings.reverse().join(rangeSeparator)\n                : value,\n        };\n    };\n}\n\nfunction maskitoDateRangeOptionsGenerator({ mode, min, max, minLength, maxLength, dateSeparator = '.', rangeSeparator = `${CHAR_NO_BREAK_SPACE}${CHAR_EN_DASH}${CHAR_NO_BREAK_SPACE}`, }) {\n    const dateModeTemplate = mode.split('/').join(dateSeparator);\n    const dateMask = Array.from(dateModeTemplate).map((char) => dateSeparator.includes(char) ? char : /\\d/);\n    return Object.assign(Object.assign({}, MASKITO_DEFAULT_OPTIONS), { mask: [...dateMask, ...Array.from(rangeSeparator), ...dateMask], overwriteMode: 'replace', preprocessors: [\n            createFullWidthToHalfWidthPreprocessor(),\n            createFirstDateEndSeparatorPreprocessor({\n                dateModeTemplate,\n                dateSegmentSeparator: dateSeparator,\n                firstDateEndSeparator: rangeSeparator,\n                pseudoFirstDateEndSeparators: POSSIBLE_DATE_RANGE_SEPARATOR,\n            }),\n            createZeroPlaceholdersPreprocessor(),\n            normalizeDatePreprocessor({\n                dateModeTemplate,\n                rangeSeparator,\n                dateSegmentsSeparator: dateSeparator,\n            }),\n            createValidDatePreprocessor({\n                dateModeTemplate,\n                rangeSeparator,\n                dateSegmentsSeparator: dateSeparator,\n            }),\n        ], postprocessors: [\n            createDateSegmentsZeroPaddingPostprocessor({\n                dateModeTemplate,\n                dateSegmentSeparator: dateSeparator,\n                splitFn: (value) => ({\n                    dateStrings: parseDateRangeString(value, dateModeTemplate, rangeSeparator),\n                }),\n                uniteFn: (validatedDateStrings, initialValue) => validatedDateStrings.reduce((acc, dateString, dateIndex) => acc +\n                    dateString +\n                    (!dateIndex && initialValue.includes(rangeSeparator)\n                        ? rangeSeparator\n                        : ''), ''),\n            }),\n            createMinMaxDatePostprocessor({\n                min,\n                max,\n                dateModeTemplate,\n                rangeSeparator,\n                dateSegmentSeparator: dateSeparator,\n            }),\n            createMinMaxRangeLengthPostprocessor({\n                dateModeTemplate,\n                minLength,\n                maxLength,\n                max,\n                rangeSeparator,\n            }),\n            createSwapDatesPostprocessor({\n                dateModeTemplate,\n                rangeSeparator,\n            }),\n        ] });\n}\n\nfunction isDateTimeStringComplete(dateTimeString, { dateMode, timeMode, dateTimeSeparator = DATE_TIME_SEPARATOR, }) {\n    var _a;\n    return (dateTimeString.length >=\n        dateMode.length + timeMode.length + dateTimeSeparator.length &&\n        ((_a = dateTimeString.split(dateTimeSeparator)[0]) !== null && _a !== void 0 ? _a : '')\n            .split(/\\D/)\n            .every((segment) => !/^0+$/.exec(segment)));\n}\n\nfunction maskitoTimeOptionsGenerator({ mode, timeSegmentMaxValues = {}, timeSegmentMinValues = {}, step = 0, }) {\n    const hasMeridiem = mode.includes('AA');\n    const enrichedTimeSegmentMaxValues = Object.assign(Object.assign(Object.assign({}, DEFAULT_TIME_SEGMENT_MAX_VALUES), (hasMeridiem ? { hours: 12 } : {})), timeSegmentMaxValues);\n    const enrichedTimeSegmentMinValues = Object.assign(Object.assign(Object.assign({}, DEFAULT_TIME_SEGMENT_MIN_VALUES), (hasMeridiem ? { hours: 1 } : {})), timeSegmentMinValues);\n    return {\n        mask: createTimeMaskExpression(mode),\n        preprocessors: [\n            createFullWidthToHalfWidthPreprocessor(),\n            createColonConvertPreprocessor(),\n            createZeroPlaceholdersPreprocessor(),\n            createMeridiemPreprocessor(mode),\n            createInvalidTimeSegmentInsertionPreprocessor({\n                timeMode: mode,\n                timeSegmentMinValues: enrichedTimeSegmentMinValues,\n                timeSegmentMaxValues: enrichedTimeSegmentMaxValues,\n            }),\n        ],\n        postprocessors: [\n            createMeridiemPostprocessor(mode),\n            (elementState) => enrichTimeSegmentsWithZeroes(elementState, {\n                mode,\n                timeSegmentMaxValues: enrichedTimeSegmentMaxValues,\n            }),\n        ],\n        plugins: [\n            createTimeSegmentsSteppingPlugin({\n                fullMode: mode,\n                step,\n                timeSegmentMaxValues: enrichedTimeSegmentMaxValues,\n            }),\n            createMeridiemSteppingPlugin(mode.indexOf('AA')),\n        ],\n        overwriteMode: 'replace',\n    };\n}\n\n/**\n * Converts a formatted time string to milliseconds based on the given `options.mode`.\n *\n * @param maskedTime a formatted time string by {@link maskitoTimeOptionsGenerator} or {@link maskitoStringifyTime}\n * @param params\n */\nfunction maskitoParseTime(maskedTime, { mode, timeSegmentMaxValues = {} }) {\n    var _a, _b, _c, _d;\n    const maxValues = Object.assign(Object.assign({}, DEFAULT_TIME_SEGMENT_MAX_VALUES), timeSegmentMaxValues);\n    const msInSecond = maxValues.milliseconds + 1;\n    const msInMinute = (maxValues.seconds + 1) * msInSecond;\n    const msInHour = (maxValues.minutes + 1) * msInMinute;\n    const parsedTime = padEndTimeSegments(parseTimeString(maskedTime, mode));\n    return (Number((_a = parsedTime.hours) !== null && _a !== void 0 ? _a : '') * msInHour +\n        Number((_b = parsedTime.minutes) !== null && _b !== void 0 ? _b : '') * msInMinute +\n        Number((_c = parsedTime.seconds) !== null && _c !== void 0 ? _c : '') * msInSecond +\n        Number((_d = parsedTime.milliseconds) !== null && _d !== void 0 ? _d : ''));\n}\n\n/**\n * Converts milliseconds to a formatted time string based on the given `options.mode`.\n *\n * @param milliseconds unsigned integer milliseconds\n * @param params\n */\nfunction maskitoStringifyTime(milliseconds, { mode, timeSegmentMaxValues = {} }) {\n    const maxValues = Object.assign(Object.assign({}, DEFAULT_TIME_SEGMENT_MAX_VALUES), timeSegmentMaxValues);\n    const msInSecond = maxValues.milliseconds + 1;\n    const msInMinute = (maxValues.seconds + 1) * msInSecond;\n    const msInHour = (maxValues.minutes + 1) * msInMinute;\n    const hours = Math.trunc(milliseconds / msInHour);\n    milliseconds -= hours * msInHour;\n    const minutes = Math.trunc(milliseconds / msInMinute);\n    milliseconds -= minutes * msInMinute;\n    const seconds = Math.trunc(milliseconds / msInSecond);\n    milliseconds -= seconds * msInSecond;\n    const result = padStartTimeSegments({ hours, minutes, seconds, milliseconds });\n    return mode\n        .replaceAll(/H+/g, result.hours)\n        .replaceAll('MSS', result.milliseconds)\n        .replaceAll(/M+/g, result.minutes)\n        .replaceAll(/S+/g, result.seconds);\n}\n\nfunction maskitoParseDateTime(value, { dateMode, timeMode, min = DEFAULT_MIN_DATE, max = DEFAULT_MAX_DATE, dateTimeSeparator = DATE_TIME_SEPARATOR, }) {\n    const [dateSegment = '', timeSegment = ''] = value.split(dateTimeSeparator);\n    if (timeSegment.length !== timeMode.length) {\n        return null;\n    }\n    const date = maskitoParseDate(dateSegment, { mode: dateMode });\n    const time = maskitoParseTime(timeSegment, { mode: timeMode });\n    if (!date) {\n        return null;\n    }\n    const dateTime = new Date(Number(date) + time);\n    return clamp(dateTime, min, max);\n}\n\nconst NON_DIGIT_PLACEHOLDER_RE = /[^dmy]/g;\nconst LEADING_NON_DIGIT_RE = /^\\D*/;\nfunction splitDateTimeString(dateTime, dateModeTemplate) {\n    const dateDigitsCount = dateModeTemplate.replaceAll(NON_DIGIT_PLACEHOLDER_RE, '').length;\n    const [date = ''] = new RegExp(`(\\\\d[^\\\\d]*){0,${dateDigitsCount - 1}}\\\\d?`).exec(dateTime) || [];\n    const [dateTimeSeparator = ''] = LEADING_NON_DIGIT_RE.exec(dateTime.slice(date.length)) || [];\n    return [date, dateTime.slice(date.length + dateTimeSeparator.length)];\n}\n\nfunction maskitoStringifyDateTime(date, { dateMode, timeMode, dateTimeSeparator = DATE_TIME_SEPARATOR, dateSeparator = '.', min = DEFAULT_MIN_DATE, max = DEFAULT_MAX_DATE, }) {\n    const validatedDate = clamp(date, min, max);\n    const dateString = maskitoStringifyDate(validatedDate, {\n        mode: dateMode,\n        separator: dateSeparator,\n        min,\n        max,\n    });\n    const extractedTime = Number(validatedDate) -\n        Number(new Date(validatedDate.getFullYear(), validatedDate.getMonth(), validatedDate.getDate()));\n    const timeString = maskitoStringifyTime(extractedTime, { mode: timeMode });\n    return dateString + dateTimeSeparator + timeString;\n}\n\nfunction createMinMaxDateTimePostprocessor({ dateModeTemplate, timeMode, min = DEFAULT_MIN_DATE, max = DEFAULT_MAX_DATE, dateTimeSeparator, }) {\n    return ({ value, selection }) => {\n        const [dateString, timeString] = splitDateTimeString(value, dateModeTemplate);\n        const parsedDate = parseDateString(dateString, dateModeTemplate);\n        const parsedTime = parseTimeString(timeString, timeMode);\n        if (!isDateTimeStringComplete(value, {\n            dateMode: dateModeTemplate,\n            timeMode,\n            dateTimeSeparator,\n        })) {\n            const fixedDate = raiseSegmentValueToMin(parsedDate, dateModeTemplate);\n            const { year, month, day } = isDateStringComplete(dateString, dateModeTemplate)\n                ? dateToSegments(clamp(segmentsToDate(fixedDate), min, max))\n                : fixedDate;\n            const fixedValue = toDateString(Object.assign({ year,\n                month,\n                day }, parsedTime), { dateMode: dateModeTemplate, dateTimeSeparator, timeMode });\n            const tail = value.slice(fixedValue.length);\n            return {\n                selection,\n                value: fixedValue + tail,\n            };\n        }\n        const date = segmentsToDate(parsedDate, parsedTime);\n        const clampedDate = clamp(date, min, max);\n        // trailing segment separators or meridiem characters\n        const [trailingNonDigitCharacters = ''] = value.match(/\\D+$/g) || [];\n        const validatedValue = toDateString(dateToSegments(clampedDate), {\n            dateMode: dateModeTemplate,\n            dateTimeSeparator,\n            timeMode,\n        }) + trailingNonDigitCharacters;\n        return {\n            selection,\n            value: validatedValue,\n        };\n    };\n}\n\nfunction createValidDateTimePreprocessor({ dateModeTemplate, dateSegmentsSeparator, dateTimeSeparator, timeMode, timeSegmentMaxValues, }) {\n    return ({ elementState, data }) => {\n        const { value, selection } = elementState;\n        if (data === dateSegmentsSeparator) {\n            return {\n                elementState,\n                data: selection[0] === value.length ? data : '',\n            };\n        }\n        const newCharacters = data.replaceAll(/\\D/g, '');\n        if (!newCharacters) {\n            return { elementState, data };\n        }\n        const [from, rawTo] = selection;\n        let to = rawTo + data.length;\n        const newPossibleValue = value.slice(0, from) + newCharacters + value.slice(to);\n        const [dateString, timeString] = splitDateTimeString(newPossibleValue, dateModeTemplate);\n        let validatedValue = '';\n        const hasDateTimeSeparator = newPossibleValue.includes(dateTimeSeparator);\n        const { validatedDateString, updatedSelection } = validateDateString({\n            dateString,\n            dateSegmentsSeparator,\n            dateModeTemplate,\n            offset: 0,\n            selection: [from, to],\n        });\n        if (dateString && !validatedDateString) {\n            return { elementState, data: '' }; // prevent changes\n        }\n        to = updatedSelection[1];\n        validatedValue += validatedDateString;\n        const updatedTimeState = enrichTimeSegmentsWithZeroes({ value: timeString, selection: [from, to] }, { mode: timeMode, timeSegmentMaxValues });\n        to = updatedTimeState.selection[1];\n        validatedValue += hasDateTimeSeparator\n            ? dateTimeSeparator + updatedTimeState.value\n            : updatedTimeState.value;\n        const newData = validatedValue.slice(from, to);\n        return {\n            elementState: {\n                selection,\n                value: validatedValue.slice(0, from) +\n                    newData\n                        .split(dateSegmentsSeparator)\n                        .map((segment) => '0'.repeat(segment.length))\n                        .join(dateSegmentsSeparator) +\n                    validatedValue.slice(to),\n            },\n            data: newData,\n        };\n    };\n}\n\nfunction maskitoDateTimeOptionsGenerator({ dateMode, timeMode, dateSeparator = '.', min, max, dateTimeSeparator = DATE_TIME_SEPARATOR, timeStep = 0, }) {\n    const hasMeridiem = timeMode.includes('AA');\n    const dateModeTemplate = dateMode.split('/').join(dateSeparator);\n    const timeSegmentMaxValues = Object.assign(Object.assign({}, DEFAULT_TIME_SEGMENT_MAX_VALUES), (hasMeridiem ? { hours: 12 } : {}));\n    const timeSegmentMinValues = Object.assign(Object.assign({}, DEFAULT_TIME_SEGMENT_MIN_VALUES), (hasMeridiem ? { hours: 1 } : {}));\n    const fullMode = `${dateModeTemplate}${dateTimeSeparator}${timeMode}`;\n    return Object.assign(Object.assign({}, MASKITO_DEFAULT_OPTIONS), { mask: [\n            ...Array.from(dateModeTemplate).map((char) => dateSeparator.includes(char) ? char : /\\d/),\n            ...dateTimeSeparator.split(''),\n            ...createTimeMaskExpression(timeMode),\n        ], overwriteMode: 'replace', preprocessors: [\n            createFullWidthToHalfWidthPreprocessor(),\n            createColonConvertPreprocessor(),\n            createFirstDateEndSeparatorPreprocessor({\n                dateModeTemplate,\n                dateSegmentSeparator: dateSeparator,\n                firstDateEndSeparator: dateTimeSeparator,\n                pseudoFirstDateEndSeparators: dateTimeSeparator.split(''),\n            }),\n            createZeroPlaceholdersPreprocessor(),\n            createMeridiemPreprocessor(timeMode),\n            normalizeDatePreprocessor({\n                dateModeTemplate,\n                dateSegmentsSeparator: dateSeparator,\n                dateTimeSeparator,\n            }),\n            createInvalidTimeSegmentInsertionPreprocessor({\n                timeMode,\n                timeSegmentMinValues,\n                timeSegmentMaxValues,\n                parseValue: (x) => {\n                    const [dateString, timeString] = splitDateTimeString(x, dateModeTemplate);\n                    return { timeString, restValue: dateString + dateTimeSeparator };\n                },\n            }),\n            createValidDateTimePreprocessor({\n                dateModeTemplate,\n                dateSegmentsSeparator: dateSeparator,\n                dateTimeSeparator,\n                timeMode,\n                timeSegmentMaxValues,\n            }),\n        ], postprocessors: [\n            createMeridiemPostprocessor(timeMode),\n            createDateSegmentsZeroPaddingPostprocessor({\n                dateModeTemplate,\n                dateSegmentSeparator: dateSeparator,\n                splitFn: (value) => {\n                    const [dateString, timeString] = splitDateTimeString(value, dateModeTemplate);\n                    return { dateStrings: [dateString], restPart: timeString };\n                },\n                uniteFn: ([validatedDateString], initialValue) => validatedDateString +\n                    (initialValue.includes(dateTimeSeparator) ? dateTimeSeparator : ''),\n            }),\n            createMinMaxDateTimePostprocessor({\n                min,\n                max,\n                dateModeTemplate,\n                timeMode,\n                dateTimeSeparator,\n            }),\n        ], plugins: [\n            createTimeSegmentsSteppingPlugin({\n                step: timeStep,\n                fullMode,\n                timeSegmentMaxValues: DEFAULT_TIME_SEGMENT_MAX_VALUES,\n            }),\n            createMeridiemSteppingPlugin(fullMode.indexOf('AA')),\n        ] });\n}\n\n/**\n * It drops prefix and postfix from data\n * Needed for case, when prefix or postfix contain decimalSeparator, to ignore it in resulting number\n * @example User pastes '{prefix}123.45{postfix}' => 123.45\n */\nfunction createAffixesFilterPreprocessor({ prefix, postfix, }) {\n    return ({ elementState, data }) => {\n        const { cleanValue: cleanData } = extractAffixes(data, {\n            prefix,\n            postfix,\n        });\n        return {\n            elementState,\n            data: cleanData,\n        };\n    };\n}\n\nfunction generateMaskExpression({ decimalPseudoSeparators, decimalSeparator, maximumFractionDigits, min, minusSign, postfix, prefix, pseudoMinuses, thousandSeparator, }) {\n    const computedPrefix = min < 0 && [minusSign, ...pseudoMinuses].includes(prefix)\n        ? ''\n        : computeAllOptionalCharsRegExp(prefix);\n    const digit = String.raw `\\d`;\n    const optionalMinus = min < 0 ? `[${minusSign}${pseudoMinuses.map((x) => `\\\\${x}`).join('')}]?` : '';\n    const integerPart = thousandSeparator\n        ? `[${digit}${escapeRegExp(thousandSeparator).replaceAll(/\\s/g, String.raw `\\s`)}]*`\n        : `[${digit}]*`;\n    const precisionPart = Number.isFinite(maximumFractionDigits)\n        ? maximumFractionDigits\n        : '';\n    const decimalPart = maximumFractionDigits > 0\n        ? `([${escapeRegExp(decimalSeparator)}${decimalPseudoSeparators\n            .map(escapeRegExp)\n            .join('')}]${digit}{0,${precisionPart}})?`\n        : '';\n    const computedPostfix = computeAllOptionalCharsRegExp(postfix);\n    return new RegExp(`^${computedPrefix}${optionalMinus}${integerPart}${decimalPart}${computedPostfix}$`);\n}\nfunction computeAllOptionalCharsRegExp(str) {\n    return str\n        ? `${str\n            .split('')\n            .map((char) => `${escapeRegExp(char)}?`)\n            .join('')}`\n        : '';\n}\n\nfunction maskitoParseNumber(maskedNumber, decimalSeparator = '.') {\n    const hasNegativeSign = !!new RegExp(`^\\\\D*[${CHAR_MINUS}\\\\${CHAR_HYPHEN}${CHAR_EN_DASH}${CHAR_EM_DASH}${CHAR_JP_HYPHEN}]`).exec(maskedNumber);\n    const escapedDecimalSeparator = escapeRegExp(decimalSeparator);\n    const unmaskedNumber = maskedNumber\n        // drop all decimal separators not followed by a digit\n        .replaceAll(new RegExp(`${escapedDecimalSeparator}(?!\\\\d)`, 'g'), '')\n        // drop all non-digit characters except decimal separator\n        .replaceAll(new RegExp(`[^\\\\d${escapedDecimalSeparator}]`, 'g'), '')\n        .replace(decimalSeparator, decimalSeparator && '.');\n    if (unmaskedNumber) {\n        const sign = hasNegativeSign ? CHAR_HYPHEN : '';\n        return Number(`${sign}${unmaskedNumber}`);\n    }\n    return NaN;\n}\n\nfunction maskitoStringifyNumber(number, params) {\n    if (Number.isNaN(number) || number === null) {\n        return '';\n    }\n    const { min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER, decimalSeparator = '.', } = params;\n    const value = clamp(number, min, max).toString().replace('.', decimalSeparator);\n    return maskitoTransform(value, maskitoNumberOptionsGenerator(params));\n}\n\n/**\n * Convert number to string with replacing exponent part on decimals\n *\n * @param value the number\n * @return string representation of a number\n */\nfunction stringifyNumberWithoutExp(value) {\n    var _a;\n    const valueAsString = String(value);\n    const [numberPart = '', expPart] = valueAsString.split('e-');\n    let valueWithoutExp = valueAsString;\n    if (expPart) {\n        const [, fractionalPart] = numberPart.split('.');\n        const decimalDigits = Number(expPart) + ((_a = fractionalPart === null || fractionalPart === void 0 ? void 0 : fractionalPart.length) !== null && _a !== void 0 ? _a : 0);\n        valueWithoutExp = value.toFixed(decimalDigits);\n    }\n    return valueWithoutExp;\n}\n\nfunction toNumberParts(value, { decimalSeparator, minusSign }) {\n    const [integerWithMinus = '', decimalPart = ''] = decimalSeparator\n        ? value.split(decimalSeparator)\n        : [value];\n    const escapedMinus = escapeRegExp(minusSign);\n    const [, minus = '', integerPart = ''] = new RegExp(`^(?:[^\\\\d${escapedMinus}])?(${escapedMinus})?(.*)`).exec(integerWithMinus) || [];\n    return { minus, integerPart, decimalPart };\n}\n\nfunction validateDecimalPseudoSeparators({ decimalSeparator, thousandSeparator, decimalPseudoSeparators = DEFAULT_DECIMAL_PSEUDO_SEPARATORS, }) {\n    return decimalPseudoSeparators.filter((char) => char !== thousandSeparator && char !== decimalSeparator);\n}\n\n/**\n * If `minimumFractionDigits` is `>0`, it pads decimal part with zeroes\n * (until number of digits after decimalSeparator is equal to the `minimumFractionDigits`).\n * @example 1,42 => (`minimumFractionDigits` is equal to 4) => 1,4200.\n */\nfunction createDecimalZeroPaddingPostprocessor({ decimalSeparator, minimumFractionDigits, prefix, postfix, }) {\n    if (!minimumFractionDigits) {\n        return identity;\n    }\n    return ({ value, selection }) => {\n        const { cleanValue, extractedPrefix, extractedPostfix } = extractAffixes(value, {\n            prefix,\n            postfix,\n        });\n        if (Number.isNaN(maskitoParseNumber(cleanValue, decimalSeparator))) {\n            return { value, selection };\n        }\n        const [integerPart, decimalPart = ''] = cleanValue.split(decimalSeparator);\n        return {\n            value: extractedPrefix +\n                integerPart +\n                decimalSeparator +\n                decimalPart.padEnd(minimumFractionDigits, '0') +\n                extractedPostfix,\n            selection,\n        };\n    };\n}\n\n/**\n * Make textfield empty if there is no integer part and all decimal digits are zeroes.\n * @example 0|,00 => Backspace => Empty.\n * @example -0|,00 => Backspace => -.\n * @example ,42| => Backspace x2 => ,|00 => Backspace => Empty\n */\nfunction emptyPostprocessor({ prefix, postfix, decimalSeparator, minusSign, }) {\n    return ({ value, selection }) => {\n        const [caretIndex] = selection;\n        const { cleanValue, extractedPrefix, extractedPostfix } = extractAffixes(value, {\n            prefix,\n            postfix,\n        });\n        const { minus, integerPart, decimalPart } = toNumberParts(cleanValue, {\n            decimalSeparator,\n            minusSign,\n        });\n        const aloneDecimalSeparator = !integerPart &&\n            !decimalPart &&\n            Boolean(decimalSeparator) &&\n            cleanValue.includes(decimalSeparator);\n        if ((!integerPart &&\n            !Number(decimalPart) &&\n            caretIndex === (minus + extractedPrefix).length) ||\n            aloneDecimalSeparator) {\n            return {\n                selection,\n                value: extractedPrefix + minus + extractedPostfix,\n            };\n        }\n        return { value, selection };\n    };\n}\n\n/**\n * This preprocessor works only once at initialization phase (when `new Maskito(...)` is executed).\n * This preprocessor helps to avoid conflicts during transition from one mask to another (for the same input).\n * For example, the developer changes postfix (or other mask's props) during run-time.\n * ```\n * let maskitoOptions = maskitoNumberOptionsGenerator({postfix: ' year'});\n * // [3 seconds later]\n * maskitoOptions = maskitoNumberOptionsGenerator({postfix: ' years'});\n * ```\n */\nfunction createInitializationOnlyPreprocessor({ decimalPseudoSeparators, decimalSeparator, minusSign, postfix, prefix, pseudoMinuses, }) {\n    let isInitializationPhase = true;\n    const cleanNumberMask = generateMaskExpression({\n        decimalSeparator,\n        decimalPseudoSeparators,\n        pseudoMinuses,\n        prefix: '',\n        postfix: '',\n        thousandSeparator: '',\n        maximumFractionDigits: Infinity,\n        min: Number.MIN_SAFE_INTEGER,\n        minusSign,\n    });\n    return ({ elementState, data }) => {\n        if (!isInitializationPhase) {\n            return { elementState, data };\n        }\n        isInitializationPhase = false;\n        const { value, selection } = elementState;\n        const [from, to] = selection;\n        const { extractedPrefix, cleanValue, extractedPostfix } = extractAffixes(value, {\n            prefix,\n            postfix,\n        });\n        const cleanState = maskitoTransform({\n            selection: [\n                Math.max(from - extractedPrefix.length, 0),\n                clamp(to - extractedPrefix.length, 0, cleanValue.length),\n            ],\n            value: cleanValue,\n        }, {\n            mask: cleanNumberMask,\n        });\n        const [cleanFrom, cleanTo] = cleanState.selection;\n        return {\n            elementState: {\n                selection: [\n                    cleanFrom + extractedPrefix.length,\n                    cleanTo + extractedPrefix.length,\n                ],\n                value: extractedPrefix + cleanState.value + extractedPostfix,\n            },\n            data,\n        };\n    };\n}\n\n/**\n * It removes repeated leading zeroes for integer part.\n * @example 0,|00005 => Backspace => |5\n * @example -0,|00005 => Backspace => -|5\n * @example User types \"000000\" => 0|\n * @example 0| => User types \"5\" => 5|\n */\nfunction createLeadingZeroesValidationPostprocessor({ decimalSeparator, thousandSeparator, prefix, postfix, }) {\n    const trimLeadingZeroes = (value) => {\n        const escapedThousandSeparator = escapeRegExp(thousandSeparator);\n        return value\n            .replace(\n        // all leading zeroes followed by another zero\n        new RegExp(`^(\\\\D+)?[0${escapedThousandSeparator}]+(?=0)`), '$1')\n            .replace(\n        // zero followed by not-zero digit\n        new RegExp(`^(\\\\D+)?[0${escapedThousandSeparator}]+(?=[1-9])`), '$1');\n    };\n    const countTrimmedZeroesBefore = (value, index) => {\n        const valueBefore = value.slice(0, index);\n        const followedByZero = value.slice(index).startsWith('0');\n        return (valueBefore.length -\n            trimLeadingZeroes(valueBefore).length +\n            (followedByZero ? 1 : 0));\n    };\n    return ({ value, selection }) => {\n        const [from, to] = selection;\n        const { cleanValue, extractedPrefix, extractedPostfix } = extractAffixes(value, {\n            prefix,\n            postfix,\n        });\n        const hasDecimalSeparator = Boolean(decimalSeparator) && cleanValue.includes(decimalSeparator);\n        const [integerPart = '', decimalPart = ''] = decimalSeparator\n            ? cleanValue.split(decimalSeparator)\n            : [cleanValue];\n        const zeroTrimmedIntegerPart = trimLeadingZeroes(integerPart);\n        if (integerPart === zeroTrimmedIntegerPart) {\n            return { value, selection };\n        }\n        const newFrom = from - countTrimmedZeroesBefore(value, from);\n        const newTo = to - countTrimmedZeroesBefore(value, to);\n        return {\n            value: extractedPrefix +\n                zeroTrimmedIntegerPart +\n                (hasDecimalSeparator ? decimalSeparator : '') +\n                decimalPart +\n                extractedPostfix,\n            selection: [Math.max(newFrom, 0), Math.max(newTo, 0)],\n        };\n    };\n}\n\n/**\n * This postprocessor is connected with {@link createMinMaxPlugin}:\n * both validate `min`/`max` bounds of entered value (but at the different point of time).\n */\nfunction createMinMaxPostprocessor({ min, max, decimalSeparator, minusSign, }) {\n    return ({ value, selection }) => {\n        const parsedNumber = maskitoParseNumber(value, decimalSeparator);\n        const limitedValue = \n        /**\n         * We cannot limit lower bound if user enters positive number.\n         * The same for upper bound and negative number.\n         * ___\n         * @example (min = 5)\n         * Empty input => Without this condition user cannot type 42 (the first digit will be rejected)\n         * ___\n         * @example (max = -10)\n         * Value is -10 => Without this condition user cannot delete 0 to enter another digit\n         */\n        parsedNumber > 0 ? Math.min(parsedNumber, max) : Math.max(parsedNumber, min);\n        if (parsedNumber && limitedValue !== parsedNumber) {\n            const newValue = `${limitedValue}`\n                .replace('.', decimalSeparator)\n                .replace(CHAR_HYPHEN, minusSign);\n            return {\n                value: newValue,\n                selection: [newValue.length, newValue.length],\n            };\n        }\n        return {\n            value,\n            selection,\n        };\n    };\n}\n\n/**\n * Manage caret-navigation when user \"deletes\" non-removable digits or separators\n * @example 1,|42 => Backspace => 1|,42 (only if `minimumFractionDigits` is `>0`)\n * @example 1|,42 => Delete => 1,|42 (only if `minimumFractionDigits` is `>0`)\n * @example 0,|00 => Delete => 0,0|0 (only if `minimumFractionDigits` is `>0`)\n * @example 1 |000 => Backspace => 1| 000 (always)\n */\nfunction createNonRemovableCharsDeletionPreprocessor({ decimalSeparator, thousandSeparator, minimumFractionDigits, }) {\n    return ({ elementState, data }, actionType) => {\n        const { value, selection } = elementState;\n        const [from, to] = selection;\n        const selectedCharacters = value.slice(from, to);\n        const nonRemovableSeparators = minimumFractionDigits\n            ? [decimalSeparator, thousandSeparator]\n            : [thousandSeparator];\n        const areNonRemovableZeroesSelected = Boolean(minimumFractionDigits) &&\n            from > value.indexOf(decimalSeparator) &&\n            Boolean(selectedCharacters.match(/^0+$/gi));\n        if ((actionType !== 'deleteBackward' && actionType !== 'deleteForward') ||\n            (!nonRemovableSeparators.includes(selectedCharacters) &&\n                !areNonRemovableZeroesSelected)) {\n            return {\n                elementState,\n                data,\n            };\n        }\n        return {\n            elementState: {\n                value,\n                selection: actionType === 'deleteForward' ? [to, to] : [from, from],\n            },\n            data,\n        };\n    };\n}\n\n/**\n * It pads integer part with zero if user types decimal separator (for empty input).\n * @example Empty input => User types \",\" (decimal separator) => 0,|\n */\nfunction createNotEmptyIntegerPartPreprocessor({ decimalSeparator, maximumFractionDigits, prefix, postfix, }) {\n    const startWithDecimalSepRegExp = new RegExp(`^\\\\D*${escapeRegExp(decimalSeparator)}`);\n    return ({ elementState, data }) => {\n        const { value, selection } = elementState;\n        const { cleanValue, extractedPrefix } = extractAffixes(value, {\n            prefix,\n            postfix,\n        });\n        const [from, to] = selection;\n        const cleanFrom = clamp(from - extractedPrefix.length, 0, cleanValue.length);\n        const cleanTo = clamp(to - extractedPrefix.length, 0, cleanValue.length);\n        if (maximumFractionDigits <= 0 ||\n            cleanValue.slice(0, cleanFrom).includes(decimalSeparator) ||\n            cleanValue.slice(cleanTo).includes(decimalSeparator) ||\n            !data.match(startWithDecimalSepRegExp)) {\n            return { elementState, data };\n        }\n        const digitsBeforeCursor = /\\d+/.exec(cleanValue.slice(0, cleanFrom));\n        return {\n            elementState,\n            data: digitsBeforeCursor ? data : `0${data}`,\n        };\n    };\n}\n\n/**\n * It replaces pseudo characters with valid one.\n * @example User types '.' (but separator is equal to comma) => dot is replaced with comma.\n * @example User types hyphen / en-dash / em-dash => it is replaced with minus.\n */\nfunction createPseudoCharactersPreprocessor({ validCharacter, pseudoCharacters, prefix, postfix, }) {\n    const pseudoCharactersRegExp = new RegExp(`[${pseudoCharacters.join('')}]`, 'gi');\n    return ({ elementState, data }) => {\n        const { value, selection } = elementState;\n        const { cleanValue, extractedPostfix, extractedPrefix } = extractAffixes(value, {\n            prefix,\n            postfix,\n        });\n        return {\n            elementState: {\n                selection,\n                value: extractedPrefix +\n                    cleanValue.replace(pseudoCharactersRegExp, validCharacter) +\n                    extractedPostfix,\n            },\n            data: data.replace(pseudoCharactersRegExp, validCharacter),\n        };\n    };\n}\n\n/**\n * It rejects new typed decimal separator if it already exists in text field.\n * Behaviour is similar to native <input type=\"number\"> (Chrome).\n * @example 1|23,45 => Press comma (decimal separator) => 1|23,45 (do nothing).\n */\nfunction createRepeatedDecimalSeparatorPreprocessor({ decimalSeparator, prefix, postfix, }) {\n    if (!decimalSeparator) {\n        return identity;\n    }\n    return ({ elementState, data }) => {\n        const { value, selection } = elementState;\n        const [from, to] = selection;\n        const { cleanValue } = extractAffixes(value, { prefix, postfix });\n        return {\n            elementState,\n            data: !cleanValue.includes(decimalSeparator) ||\n                value.slice(from, to + 1).includes(decimalSeparator)\n                ? data\n                : data.replaceAll(new RegExp(escapeRegExp(decimalSeparator), 'gi'), ''),\n        };\n    };\n}\n\n/**\n * It adds symbol for separating thousands.\n * @example 1000000 => (thousandSeparator is equal to space) => 1 000 000.\n */\nfunction createThousandSeparatorPostprocessor({ thousandSeparator, decimalSeparator, prefix, postfix, minusSign, }) {\n    if (!thousandSeparator) {\n        return identity;\n    }\n    const isAllSpaces = (...chars) => chars.every((x) => /\\s/.test(x));\n    return ({ value, selection }) => {\n        const [initialFrom, initialTo] = selection;\n        let [from, to] = selection;\n        const { cleanValue, extractedPostfix, extractedPrefix } = extractAffixes(value, {\n            prefix,\n            postfix,\n        });\n        const { minus, integerPart, decimalPart } = toNumberParts(cleanValue, {\n            decimalSeparator,\n            minusSign,\n        });\n        const hasDecimalSeparator = decimalSeparator && cleanValue.includes(decimalSeparator);\n        const deletedChars = cleanValue.length -\n            (minus +\n                integerPart +\n                (hasDecimalSeparator ? decimalSeparator + decimalPart : '')).length;\n        if (deletedChars > 0 && initialFrom && initialFrom <= deletedChars) {\n            from -= deletedChars;\n        }\n        if (deletedChars > 0 && initialTo && initialTo <= deletedChars) {\n            to -= deletedChars;\n        }\n        const processedIntegerPart = Array.from(integerPart).reduceRight((formattedValuePart, char, i) => {\n            const isLeadingThousandSeparator = !i && char === thousandSeparator;\n            const isPositionForSeparator = !isLeadingThousandSeparator &&\n                Boolean(formattedValuePart.length) &&\n                (formattedValuePart.length + 1) % 4 === 0;\n            const isSeparator = char === thousandSeparator || isAllSpaces(char, thousandSeparator);\n            if (isPositionForSeparator && isSeparator) {\n                return thousandSeparator + formattedValuePart;\n            }\n            if (!isPositionForSeparator && isSeparator) {\n                if (i && i <= initialFrom) {\n                    from--;\n                }\n                if (i && i <= initialTo) {\n                    to--;\n                }\n                return formattedValuePart;\n            }\n            if (!isPositionForSeparator) {\n                return char + formattedValuePart;\n            }\n            if (i < initialFrom) {\n                from++;\n            }\n            if (i < initialTo) {\n                to++;\n            }\n            return char + thousandSeparator + formattedValuePart;\n        }, '');\n        return {\n            value: extractedPrefix +\n                minus +\n                processedIntegerPart +\n                (hasDecimalSeparator ? decimalSeparator : '') +\n                decimalPart +\n                extractedPostfix,\n            selection: [from, to],\n        };\n    };\n}\n\n/**\n * It drops decimal part if `maximumFractionDigits` is zero.\n * @example User pastes '123.45' (but `maximumFractionDigits` is zero) => 123\n */\nfunction createZeroPrecisionPreprocessor({ maximumFractionDigits, decimalSeparator, prefix, postfix, }) {\n    if (maximumFractionDigits > 0 ||\n        !decimalSeparator // all separators should be treated only as thousand separators\n    ) {\n        return identity;\n    }\n    const decimalPartRegExp = new RegExp(`${escapeRegExp(decimalSeparator)}.*$`, 'g');\n    return ({ elementState, data }) => {\n        const { value, selection } = elementState;\n        const { cleanValue, extractedPrefix, extractedPostfix } = extractAffixes(value, {\n            prefix,\n            postfix,\n        });\n        const [from, to] = selection;\n        const newValue = extractedPrefix +\n            cleanValue.replace(decimalPartRegExp, '') +\n            extractedPostfix;\n        return {\n            elementState: {\n                selection: [\n                    Math.min(from, newValue.length),\n                    Math.min(to, newValue.length),\n                ],\n                value: newValue,\n            },\n            data: data.replace(decimalPartRegExp, ''),\n        };\n    };\n}\n\nconst DUMMY_SELECTION = [0, 0];\n/**\n * It removes repeated leading zeroes for integer part on blur-event.\n * @example 000000 => blur => 0\n * @example 00005 => blur => 5\n */\nfunction createLeadingZeroesValidationPlugin({ decimalSeparator, thousandSeparator, prefix, postfix, }) {\n    const dropRepeatedLeadingZeroes = createLeadingZeroesValidationPostprocessor({\n        decimalSeparator,\n        thousandSeparator,\n        prefix,\n        postfix,\n    });\n    return maskitoEventHandler('blur', (element) => {\n        const newValue = dropRepeatedLeadingZeroes({\n            value: element.value,\n            selection: DUMMY_SELECTION,\n        }, { value: '', selection: DUMMY_SELECTION }).value;\n        maskitoUpdateElement(element, newValue);\n    }, { capture: true });\n}\n\n/**\n * This plugin is connected with {@link createMinMaxPostprocessor}:\n * both validate `min`/`max` bounds of entered value (but at the different point of time).\n */\nfunction createMinMaxPlugin({ min, max, decimalSeparator, }) {\n    return maskitoEventHandler('blur', (element, options) => {\n        const parsedNumber = maskitoParseNumber(element.value, decimalSeparator);\n        const clampedNumber = clamp(parsedNumber, min, max);\n        if (!Number.isNaN(parsedNumber) && parsedNumber !== clampedNumber) {\n            maskitoUpdateElement(element, maskitoTransform(stringifyNumberWithoutExp(clampedNumber), options));\n        }\n    }, { capture: true });\n}\n\n/**\n * It pads EMPTY integer part with zero if decimal parts exists.\n * It works on blur event only!\n * @example 1|,23 => Backspace => Blur => 0,23\n */\nfunction createNotEmptyIntegerPlugin({ decimalSeparator, prefix, postfix, }) {\n    if (!decimalSeparator) {\n        return noop;\n    }\n    return maskitoEventHandler('blur', (element) => {\n        const { cleanValue, extractedPostfix, extractedPrefix } = extractAffixes(element.value, { prefix, postfix });\n        const newValue = extractedPrefix +\n            cleanValue.replace(new RegExp(`^(\\\\D+)?${escapeRegExp(decimalSeparator)}`), `$10${decimalSeparator}`) +\n            extractedPostfix;\n        maskitoUpdateElement(element, newValue);\n    }, { capture: true });\n}\n\nfunction maskitoNumberOptionsGenerator({ max = Number.MAX_SAFE_INTEGER, min = Number.MIN_SAFE_INTEGER, precision = 0, thousandSeparator = CHAR_NO_BREAK_SPACE, decimalSeparator = '.', decimalPseudoSeparators, decimalZeroPadding = false, prefix: unsafePrefix = '', postfix = '', minusSign = CHAR_MINUS, maximumFractionDigits = precision, minimumFractionDigits = decimalZeroPadding ? maximumFractionDigits : 0, } = {}) {\n    const pseudoMinuses = [\n        CHAR_HYPHEN,\n        CHAR_EN_DASH,\n        CHAR_EM_DASH,\n        CHAR_JP_HYPHEN,\n        CHAR_MINUS,\n    ].filter((char) => char !== thousandSeparator && char !== decimalSeparator && char !== minusSign);\n    const validatedDecimalPseudoSeparators = validateDecimalPseudoSeparators({\n        decimalSeparator,\n        thousandSeparator,\n        decimalPseudoSeparators,\n    });\n    const prefix = unsafePrefix.endsWith(decimalSeparator) && maximumFractionDigits > 0\n        ? `${unsafePrefix}${CHAR_ZERO_WIDTH_SPACE}`\n        : unsafePrefix;\n    const initializationOnlyPreprocessor = createInitializationOnlyPreprocessor({\n        decimalSeparator,\n        decimalPseudoSeparators: validatedDecimalPseudoSeparators,\n        pseudoMinuses,\n        prefix,\n        postfix,\n        minusSign,\n    });\n    decimalSeparator =\n        maximumFractionDigits <= 0 && decimalSeparator === thousandSeparator\n            ? ''\n            : decimalSeparator;\n    return Object.assign(Object.assign({}, MASKITO_DEFAULT_OPTIONS), { mask: generateMaskExpression({\n            decimalSeparator,\n            maximumFractionDigits,\n            min,\n            minusSign,\n            postfix,\n            prefix,\n            pseudoMinuses,\n            thousandSeparator,\n            decimalPseudoSeparators: validatedDecimalPseudoSeparators,\n        }), preprocessors: [\n            createFullWidthToHalfWidthPreprocessor(),\n            initializationOnlyPreprocessor,\n            createAffixesFilterPreprocessor({ prefix, postfix }),\n            createPseudoCharactersPreprocessor({\n                validCharacter: minusSign,\n                pseudoCharacters: pseudoMinuses,\n                prefix,\n                postfix,\n            }),\n            createPseudoCharactersPreprocessor({\n                validCharacter: decimalSeparator,\n                pseudoCharacters: validatedDecimalPseudoSeparators,\n                prefix,\n                postfix,\n            }),\n            createNotEmptyIntegerPartPreprocessor({\n                decimalSeparator,\n                maximumFractionDigits,\n                prefix,\n                postfix,\n            }),\n            createNonRemovableCharsDeletionPreprocessor({\n                decimalSeparator,\n                minimumFractionDigits,\n                thousandSeparator,\n            }),\n            createZeroPrecisionPreprocessor({\n                maximumFractionDigits,\n                decimalSeparator,\n                prefix,\n                postfix,\n            }),\n            createRepeatedDecimalSeparatorPreprocessor({\n                decimalSeparator,\n                prefix,\n                postfix,\n            }),\n        ], postprocessors: [\n            createMinMaxPostprocessor({ decimalSeparator, min, max, minusSign }),\n            maskitoPrefixPostprocessorGenerator(prefix),\n            maskitoPostfixPostprocessorGenerator(postfix),\n            createThousandSeparatorPostprocessor({\n                decimalSeparator,\n                thousandSeparator,\n                prefix,\n                postfix,\n                minusSign,\n            }),\n            createDecimalZeroPaddingPostprocessor({\n                decimalSeparator,\n                prefix,\n                postfix,\n                minimumFractionDigits: Math.min(minimumFractionDigits, maximumFractionDigits),\n            }),\n            emptyPostprocessor({\n                prefix,\n                postfix,\n                decimalSeparator,\n                minusSign,\n            }),\n        ], plugins: [\n            createLeadingZeroesValidationPlugin({\n                decimalSeparator,\n                thousandSeparator,\n                prefix,\n                postfix,\n            }),\n            createNotEmptyIntegerPlugin({\n                decimalSeparator,\n                prefix,\n                postfix,\n            }),\n            createMinMaxPlugin({ min, max, decimalSeparator }),\n        ], overwriteMode: minimumFractionDigits > 0\n            ? ({ value, selection: [from] }) => from <= value.indexOf(decimalSeparator) ? 'shift' : 'replace'\n            : 'shift' });\n}\n\nexport { maskitoAddOnFocusPlugin, maskitoCaretGuard, maskitoDateOptionsGenerator, maskitoDateRangeOptionsGenerator, maskitoDateTimeOptionsGenerator, maskitoEventHandler, maskitoNumberOptionsGenerator, maskitoParseDate, maskitoParseDateTime, maskitoParseNumber, maskitoParseTime, maskitoPostfixPostprocessorGenerator, maskitoPrefixPostprocessorGenerator, maskitoRejectEvent, maskitoRemoveOnBlurPlugin, maskitoSelectionChangeHandler, maskitoStringifyDate, maskitoStringifyDateTime, maskitoStringifyNumber, maskitoStringifyTime, maskitoTimeOptionsGenerator, maskitoWithPlaceholder };\n"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,uBAAuB,EAAEC,gBAAgB,QAAQ,eAAe;;AAE/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC5B,MAAMC,YAAY,GAAGC,IAAI,CAACH,GAAG,CAACI,MAAM,CAACH,GAAG,CAAC,EAAEE,IAAI,CAACF,GAAG,CAACG,MAAM,CAACJ,GAAG,CAAC,EAAEI,MAAM,CAACL,KAAK,CAAC,CAAC,CAAC;EAChF,OAAQA,KAAK,YAAYM,IAAI,GAAG,IAAIA,IAAI,CAACH,YAAY,CAAC,GAAGA,YAAY;AACzE;AAEA,SAASI,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAOA,GAAG,CAACC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,CAACC,MAAM;AAC3C;AAEA,SAASC,UAAUA,CAACC,WAAW,EAAE;EAAEC,GAAG;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG,CAAC,CAAC,EAAE;EACxD,MAAMC,IAAI,GAAG,IAAIV,IAAI,CAACM,WAAW,CAAC;EAClC,IAAIC,GAAG,EAAE;IACLG,IAAI,CAACC,OAAO,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,GAAGL,GAAG,CAAC;EACtC;EACA,IAAIC,KAAK,EAAE;IACPE,IAAI,CAACG,QAAQ,CAACH,IAAI,CAACI,QAAQ,CAAC,CAAC,GAAGN,KAAK,CAAC;EAC1C;EACA,IAAIC,IAAI,EAAE;IACNC,IAAI,CAACK,WAAW,CAACL,IAAI,CAACM,WAAW,CAAC,CAAC,GAAGP,IAAI,CAAC;EAC/C;EACA,OAAOC,IAAI;AACf;AAEA,MAAMO,yBAAyB,GAAIC,UAAU,IAAK;EAC9C,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAC1B,OAAQ;IACJjB,GAAG,EAAE,CAACa,EAAE,GAAG,CAACD,EAAE,GAAGD,UAAU,CAACO,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACf,MAAM,MAAM,IAAI,IAAIgB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAC3HZ,KAAK,EAAE,CAACc,EAAE,GAAG,CAACD,EAAE,GAAGH,UAAU,CAACO,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjB,MAAM,MAAM,IAAI,IAAIkB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAC7Hb,IAAI,EAAE,CAACe,EAAE,GAAG,CAACD,EAAE,GAAGL,UAAU,CAACO,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACnB,MAAM,MAAM,IAAI,IAAIoB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;EAC/H,CAAC;AACL,CAAC;AAED,SAASE,cAAcA,CAAChB,IAAI,EAAE;EAC1B,OAAO;IACHH,GAAG,EAAEoB,MAAM,CAACjB,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,CAACgB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC5CpB,KAAK,EAAEmB,MAAM,CAACjB,IAAI,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACc,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnDnB,IAAI,EAAEkB,MAAM,CAACjB,IAAI,CAACM,WAAW,CAAC,CAAC,CAAC,CAACY,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACjDC,KAAK,EAAEF,MAAM,CAACjB,IAAI,CAACoB,QAAQ,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC/CG,OAAO,EAAEJ,MAAM,CAACjB,IAAI,CAACsB,UAAU,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnDK,OAAO,EAAEN,MAAM,CAACjB,IAAI,CAACwB,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACnDO,YAAY,EAAER,MAAM,CAACjB,IAAI,CAAC0B,eAAe,CAAC,CAAC,CAAC,CAACR,QAAQ,CAAC,CAAC,EAAE,GAAG;EAChE,CAAC;AACL;AAEA,MAAMS,qBAAqB,GAAG,CAC1B,KAAK,EACL,OAAO,EACP,MAAM,CACT;AACD,SAASC,oBAAoBA,CAACC,QAAQ,EAAE;EACpC,OAAO,CAAC,GAAGF,qBAAqB,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKH,QAAQ,CAACI,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGF,QAAQ,CAACI,OAAO,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9G;AAEA,SAASE,oBAAoBA,CAAC1B,UAAU,EAAE2B,gBAAgB,EAAE;EACxD,MAAMC,YAAY,GAAG7C,WAAW,CAAC4C,gBAAgB,CAAC;EAClD,MAAM,CAACE,YAAY,GAAG,EAAE,CAAC,GAAG,IAAIC,MAAM,CAAC,aAAaF,YAAY,GAAG,CAAC,CAACG,IAAI,CAAC/B,UAAU,CAAC,IAAI,EAAE;EAC3F,OAAO6B,YAAY;AACvB;AAEA,SAASG,oBAAoBA,CAAChC,UAAU,EAAE2B,gBAAgB,EAAE;EACxD,IAAI3B,UAAU,CAACd,MAAM,GAAGyC,gBAAgB,CAACzC,MAAM,EAAE;IAC7C,OAAO,KAAK;EAChB;EACA,OAAOc,UAAU,CAACiC,KAAK,CAAC,IAAI,CAAC,CAACC,KAAK,CAAEC,OAAO,IAAK,CAAC,MAAM,CAACJ,IAAI,CAACI,OAAO,CAAC,CAAC;AAC3E;AAEA,SAASC,oBAAoBA,CAACC,SAAS,EAAEV,gBAAgB,EAAEW,cAAc,EAAE;EACvE,MAAMV,YAAY,GAAG7C,WAAW,CAAC4C,gBAAgB,CAAC;EAClD,OAAQU,SAAS,CACZE,OAAO,CAACD,cAAc,EAAE,EAAE,CAAC,CAC3B/B,KAAK,CAAC,IAAIuB,MAAM,CAAC,yBAAyBF,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE;AAC/E;AAEA,SAASY,eAAeA,CAACxC,UAAU,EAAEyC,QAAQ,EAAE;EAC3C,MAAMC,SAAS,GAAGD,QAAQ,CAACxD,UAAU,CAAC,SAAS,EAAE,EAAE,CAAC;EACpD,MAAM0D,cAAc,GAAG3C,UAAU,CAACf,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;EACxD,MAAM2D,YAAY,GAAG;IACjBvD,GAAG,EAAEsD,cAAc,CAACE,KAAK,CAACH,SAAS,CAACjB,OAAO,CAAC,GAAG,CAAC,EAAEiB,SAAS,CAACI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACjFxD,KAAK,EAAEqD,cAAc,CAACE,KAAK,CAACH,SAAS,CAACjB,OAAO,CAAC,GAAG,CAAC,EAAEiB,SAAS,CAACI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACnFvD,IAAI,EAAEoD,cAAc,CAACE,KAAK,CAACH,SAAS,CAACjB,OAAO,CAAC,GAAG,CAAC,EAAEiB,SAAS,CAACI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC;EACrF,CAAC;EACD,OAAOC,MAAM,CAACC,WAAW,CAACD,MAAM,CAACE,OAAO,CAACL,YAAY,CAAC,CACjDM,MAAM,CAAC,CAAC,CAACC,CAAC,EAAE3E,KAAK,CAAC,KAAK4E,OAAO,CAAC5E,KAAK,CAAC,CAAC,CACtC8C,IAAI,CAAC,CAAC,CAACC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKiB,QAAQ,CAACY,WAAW,CAAC,CAAC,CAAC5B,OAAO,CAACF,CAAC,CAACsB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GACjEJ,QAAQ,CAACY,WAAW,CAAC,CAAC,CAAC5B,OAAO,CAACD,CAAC,CAACqB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAC3C,CAAC,GACD,CAAC,CAAC,CAAC,CAAC;AACd;AAEA,SAASS,cAAcA,CAACC,UAAU,EAAEC,UAAU,EAAE;EAC5C,IAAIvD,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEmD,EAAE;EAC9B,MAAMlE,IAAI,GAAG,CAAC,CAACU,EAAE,GAAGsD,UAAU,CAAChE,IAAI,MAAM,IAAI,IAAIU,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACf,MAAM,MAAM,CAAC,GAAG,KAAKqE,UAAU,CAAChE,IAAI,EAAE,GAAGgE,UAAU,CAAChE,IAAI;EACrI,MAAMC,IAAI,GAAG,IAAIV,IAAI,CAACD,MAAM,CAACU,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,GAAG,CAAC,EAAEV,MAAM,CAAC,CAACqB,EAAE,GAAGqD,UAAU,CAACjE,KAAK,MAAM,IAAI,IAAIY,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,EAAErB,MAAM,CAAC,CAACsB,EAAE,GAAGoD,UAAU,CAAClE,GAAG,MAAM,IAAI,IAAIc,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG,CAAC,EAAEtB,MAAM,CAAC,CAACuB,EAAE,GAAGoD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC7C,KAAK,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG,CAAC,EAAEvB,MAAM,CAAC,CAACwB,EAAE,GAAGmD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC3C,OAAO,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG,CAAC,EAAExB,MAAM,CAAC,CAACyB,EAAE,GAAGkD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACzC,OAAO,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG,CAAC,EAAEzB,MAAM,CAAC,CAAC4E,EAAE,GAAGD,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACvC,YAAY,MAAM,IAAI,IAAIwC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG,CAAC,CAAC;EAC7tB;EACAjE,IAAI,CAACK,WAAW,CAAChB,MAAM,CAACU,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,GAAG,CAAC,CAAC;EACvE,OAAOC,IAAI;AACf;AAEA,MAAMkE,mBAAmB,GAAG,IAAI;AAEhC,SAASC,YAAYA,CAAC;EAAEtE,GAAG;EAAEC,KAAK;EAAEC,IAAI;EAAEoB,KAAK;EAAEE,OAAO;EAAEE,OAAO;EAAEE;AAAc,CAAC,EAAE;EAAE2C,QAAQ;EAAEC,iBAAiB,GAAGH,mBAAmB;EAAEI;AAAU,CAAC,EAAE;EAClJ,IAAI7D,EAAE;EACN,MAAM8D,QAAQ,GAAG,CAAC,CAAC9D,EAAE,GAAG2D,QAAQ,CAACrD,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACf,MAAM,MAAM,CAAC,GAAGK,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACsD,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGtD,IAAI;EACvK,MAAMkD,QAAQ,GAAGmB,QAAQ,IAAIE,QAAQ,GAAGD,iBAAiB,GAAGC,QAAQ,GAAG,EAAE,CAAC;EAC1E,OAAOrB,QAAQ,CACVxD,UAAU,CAAC,KAAK,EAAEI,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAG,EAAE,CAAC,CAC5DJ,UAAU,CAAC,KAAK,EAAEK,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE,CAAC,CAClEL,UAAU,CAAC,KAAK,EAAE8E,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAG,EAAE,CAAC,CAC3E9E,UAAU,CAAC,KAAK,EAAE0B,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE,CAAC,CAClE1B,UAAU,CAAC,KAAK,EAAEgC,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,EAAE,CAAC,CACvFhC,UAAU,CAAC,KAAK,EAAE4B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,EAAE,CAAC,CACxE5B,UAAU,CAAC,KAAK,EAAE8B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,EAAE,CAAC,CACxE9B,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CACvBA,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;AAChC;AAEA,MAAM+E,wBAAwB,GAAG;EAC7B3E,GAAG,EAAE,EAAE;EACPC,KAAK,EAAE,EAAE;EACTC,IAAI,EAAE;AACV,CAAC;;AAED;AACA,MAAM0E,iCAAiC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAE9D,MAAMC,gBAAgB,GAAG,IAAIpF,IAAI,CAAC,kBAAkB,CAAC;AACrD,MAAMqF,gBAAgB,GAAG,IAAIrF,IAAI,CAAC,yBAAyB,CAAC;AAE5D,MAAMsF,+BAA+B,GAAG;EACpCzD,KAAK,EAAE,EAAE;EACTE,OAAO,EAAE,EAAE;EACXE,OAAO,EAAE,EAAE;EACXE,YAAY,EAAE;AAClB,CAAC;AACD,MAAMoD,+BAA+B,GAAG;EACpC1D,KAAK,EAAE,CAAC;EACRE,OAAO,EAAE,CAAC;EACVE,OAAO,EAAE,CAAC;EACVE,YAAY,EAAE;AAClB,CAAC;;AAED;AACA;AACA;AACA,MAAMqD,mBAAmB,GAAG,QAAQ;AACpC;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,QAAQ;AACtC;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,QAAQ;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,QAAQ;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,QAAQ;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,QAAQ;AAC3B;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG,QAAQ;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,QAAQ;AAC3B;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,QAAQ;AAE9B,MAAMC,yBAAyB,GAAG,IAAIjD,MAAM,CAAC,IAAIwC,mBAAmB,QAAQ,EAAE,GAAG,CAAC;AAClF,MAAMU,0BAA0B,GAAG,IAAIlD,MAAM,CAAC,GAAGwC,mBAAmB,QAAQ,EAAE,GAAG,CAAC;AAElF,MAAMW,qBAAqB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AAExC,MAAMC,0BAA0B,GAAG;EAC/BvE,KAAK,EAAE,CAAC;EACRE,OAAO,EAAE,CAAC;EACVE,OAAO,EAAE,CAAC;EACVE,YAAY,EAAE;AAClB,CAAC;AAED,SAASkE,kBAAkBA,CAAC;EAAEnF,UAAU;EAAE2B,gBAAgB;EAAEyD,qBAAqB;EAAEC,MAAM;EAAEC,SAAS,EAAE,CAACC,IAAI,EAAEC,EAAE;AAAG,CAAC,EAAE;EACjH,IAAIvF,EAAE,EAAEC,EAAE;EACV,MAAMqD,UAAU,GAAGf,eAAe,CAACxC,UAAU,EAAE2B,gBAAgB,CAAC;EAChE,MAAMiB,YAAY,GAAGG,MAAM,CAACE,OAAO,CAACM,UAAU,CAAC;EAC/C,MAAMkC,aAAa,GAAGrE,oBAAoB,CAACO,gBAAgB,CAAC;EAC5D,MAAM+D,qBAAqB,GAAG,CAAC,CAAC;EAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/C,YAAY,CAAC1D,MAAM,EAAEyG,CAAC,EAAE,EAAE;IAC1C,MAAM,CAACC,WAAW,EAAEC,YAAY,CAAC,GAAGjD,YAAY,CAAC+C,CAAC,CAAC;IACnD,MAAMG,aAAa,GAAGnC,YAAY,CAAC+B,qBAAqB,EAAE;MACtD9B,QAAQ,EAAEjC;IACd,CAAC,CAAC;IACF,MAAMoE,eAAe,GAAG/B,wBAAwB,CAAC4B,WAAW,CAAC;IAC7D,MAAMI,eAAe,GAAGF,aAAa,CAAC5G,MAAM,IAAIkG,qBAAqB,CAAClG,MAAM;IAC5E,MAAM+G,qBAAqB,GAAGZ,MAAM,GAChCS,aAAa,CAAC5G,MAAM,GACpB8G,eAAe,GACfjG,yBAAyB,CAAC4B,gBAAgB,CAAC,CAACiE,WAAW,CAAC;IAC5D,MAAMM,uBAAuB,GAAGD,qBAAqB,IAAIV,IAAI,IAAIU,qBAAqB,KAAKT,EAAE;IAC7F,IAAIU,uBAAuB,IAAIrH,MAAM,CAACgH,YAAY,CAAC,GAAGhH,MAAM,CAACkH,eAAe,CAAC,EAAE;MAC3E,MAAMI,WAAW,GAAGV,aAAa,CAACA,aAAa,CAAChE,OAAO,CAACmE,WAAW,CAAC,GAAG,CAAC,CAAC;MACzE,IAAI,CAACO,WAAW,IAAIA,WAAW,KAAK,MAAM,EAAE;QACxC;QACA,OAAO;UAAEC,mBAAmB,EAAE,EAAE;UAAEC,gBAAgB,EAAE,CAACd,IAAI,EAAEC,EAAE;QAAE,CAAC,CAAC,CAAC;MACtE;MACAE,qBAAqB,CAACE,WAAW,CAAC,GAC9B,IAAIC,YAAY,CAAChD,KAAK,CAAC,CAAC,EAAEoD,qBAAqB,CAAC,EAAE;MACtDrD,YAAY,CAAC+C,CAAC,GAAG,CAAC,CAAC,GAAG,CAClBQ,WAAW,EACXN,YAAY,CAAChD,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC3C,EAAE,GAAG,CAACD,EAAE,GAAG2C,YAAY,CAAC+C,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,IAAI1F,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,EAAE2C,KAAK,CAAC,CAAC,CAAC,CACvJ;MACD;IACJ;IACA,IAAIqD,uBAAuB,IAAIrH,MAAM,CAACgH,YAAY,CAAC,GAAG,CAAC,EAAE;MACrD;MACA,OAAO;QAAEO,mBAAmB,EAAE,EAAE;QAAEC,gBAAgB,EAAE,CAACd,IAAI,EAAEC,EAAE;MAAE,CAAC,CAAC,CAAC;IACtE;IACAE,qBAAqB,CAACE,WAAW,CAAC,GAAGC,YAAY;EACrD;EACA,MAAMO,mBAAmB,GAAGzC,YAAY,CAAC+B,qBAAqB,EAAE;IAC5D9B,QAAQ,EAAEjC;EACd,CAAC,CAAC;EACF,MAAM2E,0BAA0B,GAAGF,mBAAmB,CAAClH,MAAM,GAAGc,UAAU,CAACd,MAAM;EACjF,OAAO;IACHkH,mBAAmB;IACnBC,gBAAgB,EAAE,CACdd,IAAI,GAAGe,0BAA0B,EACjCd,EAAE,GAAGc,0BAA0B;EAEvC,CAAC;AACL;AAEA,SAASC,QAAQA,CAACC,CAAC,EAAE;EACjB,OAAOA,CAAC;AACZ;AACA;AACA,SAASC,IAAIA,CAAA,EAAG,CAAE;;AAElB;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,qBAAqB;AAC1C,MAAMC,eAAe,GAAG,IAAI7E,MAAM,CAAC4E,YAAY,CAACE,MAAM,CAAC;AACvD,SAASC,YAAYA,CAAC7H,GAAG,EAAE;EACvB,OAAOA,GAAG,IAAI2H,eAAe,CAACG,IAAI,CAAC9H,GAAG,CAAC,GACjCA,GAAG,CAACC,UAAU,CAACyH,YAAY,EAAEjG,MAAM,CAACsG,GAAI,KAAK,CAAC,GAC9C/H,GAAG;AACb;AAEA,SAASgI,cAAcA,CAACxI,KAAK,EAAE;EAAEyI,MAAM;EAAEC;AAAQ,CAAC,EAAE;EAChD,IAAIjH,EAAE,EAAEC,EAAE;EACV,MAAMiH,YAAY,GAAG,IAAIrF,MAAM,CAAC,IAAI+E,YAAY,CAACI,MAAM,CAAC,EAAE,CAAC;EAC3D,MAAMG,aAAa,GAAG,IAAItF,MAAM,CAAC,GAAG+E,YAAY,CAACK,OAAO,CAAC,GAAG,CAAC;EAC7D,MAAM,CAACG,eAAe,GAAG,EAAE,CAAC,GAAG,CAACpH,EAAE,GAAGzB,KAAK,CAAC+B,KAAK,CAAC4G,YAAY,CAAC,MAAM,IAAI,IAAIlH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;EACnG,MAAM,CAACqH,gBAAgB,GAAG,EAAE,CAAC,GAAG,CAACpH,EAAE,GAAG1B,KAAK,CAAC+B,KAAK,CAAC6G,aAAa,CAAC,MAAM,IAAI,IAAIlH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;EACrG,OAAO;IACHmH,eAAe;IACfC,gBAAgB;IAChBC,UAAU,EAAEF,eAAe,IAAIC,gBAAgB,GACzC9I,KAAK,CAACqE,KAAK,CAACwE,eAAe,CAACnI,MAAM,EAAEoI,gBAAgB,CAACpI,MAAM,GAAG,CAACoI,gBAAgB,CAACpI,MAAM,GAAGsI,QAAQ,CAAC,GAClGhJ;EACV,CAAC;AACL;AAEA,SAASiJ,yBAAyBA,CAAClG,CAAC,EAAEC,CAAC,EAAE;EACrC,IAAIkG,GAAG,GAAG,EAAE;EACZ,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpE,CAAC,CAACrC,MAAM,EAAEyG,CAAC,EAAE,EAAE;IAC/B,IAAIpE,CAAC,CAACoE,CAAC,CAAC,KAAKnE,CAAC,CAACmE,CAAC,CAAC,EAAE;MACf,OAAO+B,GAAG;IACd;IACAA,GAAG,IAAInG,CAAC,CAACoE,CAAC,CAAC;EACf;EACA,OAAO+B,GAAG;AACd;AAEA,SAASC,OAAOA,CAACC,MAAM,EAAE;EACrB,OAAO,CAACA,MAAM,IAAK,OAAOA,MAAM,KAAK,QAAQ,IAAI7E,MAAM,CAAC8E,IAAI,CAACD,MAAM,CAAC,CAAC1I,MAAM,KAAK,CAAE;AACtF;AAEA,MAAM4I,aAAa,GAAG,MAAM;AAC5B,SAASC,uBAAuBA,CAAClC,YAAY,EAAEmC,cAAc,EAAEC,mBAAmB,GAAG,CAAC,EAAE;EACpF,MAAMC,kBAAkB,GAAGrC,YAAY,CAACsC,MAAM,CAACH,cAAc,CAAC9I,MAAM,EAAE,GAAG,CAAC;EAC1E,IAAIL,MAAM,CAACqJ,kBAAkB,CAAC,IAAIrJ,MAAM,CAACmJ,cAAc,CAAC,EAAE;IACtD,OAAO;MAAEI,qBAAqB,EAAEvC,YAAY;MAAEoC;IAAoB,CAAC;EACvE;EACA,IAAIC,kBAAkB,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;IAClC;IACA,OAAON,uBAAuB,CAAC,IAAIlC,YAAY,CAAChD,KAAK,CAAC,CAAC,EAAEmF,cAAc,CAAC9I,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE8I,cAAc,EAAEC,mBAAmB,GAAG,CAAC,CAAC;EACnI;EACA,MAAMK,oBAAoB,GAAGzC,YAAY,CAAChD,KAAK,CAAC,CAAC,EAAEmF,cAAc,CAAC9I,MAAM,GAAG,CAAC,CAAC;EAC7E,IAAI4I,aAAa,CAAC/F,IAAI,CAACuG,oBAAoB,CAAC,EAAE;IAC1C,OAAO;MAAEF,qBAAqB,EAAE,EAAE;MAAEH;IAAoB,CAAC;EAC7D;EACA;EACA,OAAOF,uBAAuB,CAAC,GAAGO,oBAAoB,GAAG,EAAEN,cAAc,EAAEC,mBAAmB,CAAC;AACnG;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASM,gBAAgBA,CAACC,cAAc,EAAE;EACtC,OAAOA,cAAc,CAACvJ,UAAU,CAAC,IAAI6C,MAAM,CAACgD,aAAa,EAAE,GAAG,CAAC,EAAED,UAAU,CAAC;AAChF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS4D,iBAAiBA,CAACC,eAAe,EAAE;EACxC,OAAOA,eAAe,CAACzJ,UAAU,CAAC,QAAQ,EAAG0J,CAAC,IAAKlI,MAAM,CAACmI,YAAY,CAACD,CAAC,CAACE,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AACrG;;AAEA;AACA;AACA;AACA,SAASC,8BAA8BA,CAAA,EAAG;EACtC,OAAO,CAAC;IAAEC,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,MAAM;MAAExK,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,OAAO;MACHA,YAAY,EAAE;QACVzD,SAAS;QACT9G,KAAK,EAAE+J,gBAAgB,CAAC/J,KAAK;MACjC,CAAC;MACDwK,IAAI,EAAET,gBAAgB,CAACS,IAAI;IAC/B,CAAC;EACL,CAAC;AACL;AAEA,SAASC,0CAA0CA,CAAC;EAAEtH,gBAAgB;EAAEuH,oBAAoB;EAAEC,OAAO;EAAEC;AAAS,CAAC,EAAE;EAC/G,OAAO,CAAC;IAAE5K,KAAK;IAAE8G;EAAU,CAAC,KAAK;IAC7B,IAAIrF,EAAE;IACN,MAAM,CAACsF,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC5B,MAAM;MAAE+D,WAAW;MAAEC,QAAQ,GAAG;IAAG,CAAC,GAAGH,OAAO,CAAC3K,KAAK,CAAC;IACrD,MAAM+K,oBAAoB,GAAG,EAAE;IAC/B,IAAIC,UAAU,GAAG,CAAC;IAClBH,WAAW,CAACI,OAAO,CAAEzJ,UAAU,IAAK;MAChC,MAAMuD,UAAU,GAAGf,eAAe,CAACxC,UAAU,EAAE2B,gBAAgB,CAAC;MAChE,MAAMiB,YAAY,GAAGG,MAAM,CAACE,OAAO,CAACM,UAAU,CAAC;MAC/C,MAAMmC,qBAAqB,GAAG9C,YAAY,CAAC8G,MAAM,CAAC,CAACC,GAAG,EAAE,CAAC/D,WAAW,EAAEC,YAAY,CAAC,KAAK;QACpF,MAAM;UAAEuC,qBAAqB;UAAEH;QAAoB,CAAC,GAAGF,uBAAuB,CAAClC,YAAY,EAAE,GAAG7B,wBAAwB,CAAC4B,WAAW,CAAC,EAAE,CAAC;QACxI4D,UAAU,IAAIvB,mBAAmB;QACjC,OAAOlF,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC,EAAE;UAAE,CAAC/D,WAAW,GAAGwC;QAAsB,CAAC,CAAC;MAC1F,CAAC,EAAE,CAAC,CAAC,CAAC;MACNmB,oBAAoB,CAACM,IAAI,CAAClG,YAAY,CAAC+B,qBAAqB,EAAE;QAAE9B,QAAQ,EAAEjC;MAAiB,CAAC,CAAC,CAAC;IAClG,CAAC,CAAC;IACF,MAAMmI,cAAc,GAAGV,OAAO,CAACG,oBAAoB,EAAE/K,KAAK,CAAC,IACtD,CAAC,CAACyB,EAAE,GAAGoJ,WAAW,CAACA,WAAW,CAACnK,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIe,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoI,QAAQ,CAACa,oBAAoB,CAAC,IAC7GA,oBAAoB,GACpB,EAAE,CAAC,GACTI,QAAQ;IACZ,IAAIE,UAAU,IACVM,cAAc,CAACjH,KAAK,CAAC2C,EAAE,GAAGgE,UAAU,EAAEhE,EAAE,GAAGgE,UAAU,GAAGN,oBAAoB,CAAChK,MAAM,CAAC,KAAKgK,oBAAoB,EAAE;MAC/G;AACZ;AACA;AACA;AACA;MACYM,UAAU,IAAIN,oBAAoB,CAAChK,MAAM;IAC7C;IACA,OAAO;MACHoG,SAAS,EAAE,CAACC,IAAI,GAAGiE,UAAU,EAAEhE,EAAE,GAAGgE,UAAU,CAAC;MAC/ChL,KAAK,EAAEsL;IACX,CAAC;EACL,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,uCAAuCA,CAAC;EAAEpI,gBAAgB;EAAEqI,qBAAqB;EAAEd,oBAAoB;EAAEe;AAA8B,CAAC,EAAE;EAC/I,OAAO,CAAC;IAAElB,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,MAAM;MAAExK,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,MAAM,CAACxD,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC5B,MAAM4E,iBAAiB,GAAGxI,oBAAoB,CAAClD,KAAK,EAAEmD,gBAAgB,CAAC;IACvE,MAAMwI,gBAAgB,GAAGF,4BAA4B,CAAC/G,MAAM,CAAEsD,CAAC,IAAK,CAACwD,qBAAqB,CAACI,QAAQ,CAAC5D,CAAC,CAAC,IAAIA,CAAC,KAAK0C,oBAAoB,CAAC;IACrI,MAAMmB,kBAAkB,GAAG,IAAIvI,MAAM,CAAC,IAAIqI,gBAAgB,CAACG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC;IAC7E,MAAMC,QAAQ,GAAGL,iBAAiB,IAAI1L,KAAK,CAACU,MAAM,GAAGgL,iBAAiB,CAAChL,MAAM,GACvEgL,iBAAiB,GACf1L,KAAK,CACAqE,KAAK,CAACqH,iBAAiB,CAAChL,MAAM,CAAC,CAC/BqD,OAAO,CAAC,UAAU,EAAEyH,qBAAqB,CAAC,GACjDxL,KAAK;IACX,MAAMgL,UAAU,GAAGe,QAAQ,CAACrL,MAAM,GAAGV,KAAK,CAACU,MAAM;IACjD,OAAO;MACH6J,YAAY,EAAE;QACVzD,SAAS,EAAE,CAACC,IAAI,GAAGiE,UAAU,EAAEhE,EAAE,GAAGgE,UAAU,CAAC;QAC/ChL,KAAK,EAAE+L;MACX,CAAC;MACDvB,IAAI,EAAEA,IAAI,CAACzG,OAAO,CAAC8H,kBAAkB,EAAEL,qBAAqB;IAChE,CAAC;EACL,CAAC;AACL;;AAEA;AACA;AACA;AACA,SAASQ,sCAAsCA,CAAA,EAAG;EAC9C,OAAO,CAAC;IAAEzB,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,MAAM;MAAExK,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,OAAO;MACHA,YAAY,EAAE;QACVzD,SAAS;QACT9G,KAAK,EAAEiK,iBAAiB,CAACjK,KAAK;MAClC,CAAC;MACDwK,IAAI,EAAEP,iBAAiB,CAACO,IAAI;IAChC,CAAC;EACL,CAAC;AACL;AAEA,SAASyB,wBAAwBA,CAACC,IAAI,EAAE;EACpC,OAAOC,KAAK,CAACpF,IAAI,CAACmF,IAAI,CAACnI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CACrCqI,GAAG,CAAEC,IAAI,IAAM5F,qBAAqB,CAACmF,QAAQ,CAACS,IAAI,CAAC,GAAGA,IAAI,GAAG,IAAK,CAAC,CACnEC,MAAM,CAACJ,IAAI,CAACN,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC9F,mBAAmB,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;AAChF;AAEA,SAASyG,eAAeA,CAACC,YAAY,EAAEC,GAAG,EAAE;EACxC,OAAOlI,MAAM,CAACC,WAAW,CAACD,MAAM,CAACE,OAAO,CAAC+H,YAAY,CAAC,CAACJ,GAAG,CAAC,CAAC,CAAChF,WAAW,EAAEC,YAAY,CAAC,KAAK,CACxFD,WAAW,EACXqF,GAAG,CAACxK,MAAM,CAACoF,YAAY,CAAC,EAAEX,0BAA0B,CAACU,WAAW,CAAC,CAAC,CACrE,CAAC,CAAC;AACP;AAEA,SAASsF,oBAAoBA,CAACF,YAAY,EAAE;EACxC,OAAOD,eAAe,CAACC,YAAY,EAAE,CAACxM,KAAK,EAAEU,MAAM,KAAKV,KAAK,CAACkC,QAAQ,CAACxB,MAAM,EAAE,GAAG,CAAC,CAAC;AACxF;AAEA,MAAMiM,iBAAiB,GAAG;EACtBC,EAAE,EAAE,OAAO;EACXC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE,SAAS;EACbC,GAAG,EAAE;AACT,CAAC;AACD;AACA;AACA;AACA,SAASC,eAAeA,CAACC,UAAU,EAAE3H,QAAQ,EAAE;EAC3C,MAAM4H,UAAU,GAAGD,UAAU,CAACxM,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;EACpD,IAAIoG,MAAM,GAAG,CAAC;EACd,OAAOtC,MAAM,CAACC,WAAW,CAACc,QAAQ,CAC7B7B,KAAK,CAAC,IAAI,CAAC,CACXiB,MAAM,CAAEyI,WAAW,IAAKR,iBAAiB,CAACQ,WAAW,CAAC,CAAC,CACvDf,GAAG,CAAEe,WAAW,IAAK;IACtB,MAAM9F,YAAY,GAAG6F,UAAU,CAAC7I,KAAK,CAACwC,MAAM,EAAEA,MAAM,GAAGsG,WAAW,CAACzM,MAAM,CAAC;IAC1EmG,MAAM,IAAIsG,WAAW,CAACzM,MAAM;IAC5B,OAAO,CAACiM,iBAAiB,CAACQ,WAAW,CAAC,EAAE9F,YAAY,CAAC;EACzD,CAAC,CAAC,CAAC;AACP;AAEA,MAAM+F,kBAAkB,GAAG,MAAM;AACjC,MAAMC,mBAAmB,GAAG,MAAM;AAClC,SAASC,YAAYA,CAAC;EAAEnL,KAAK,GAAG,EAAE;EAAEE,OAAO,GAAG,EAAE;EAAEE,OAAO,GAAG,EAAE;EAAEE,YAAY,GAAG;AAAI,CAAC,EAAE;EAClF,OAAO,GAAGN,KAAK,IAAIE,OAAO,IAAIE,OAAO,IAAIE,YAAY,EAAE,CAClDsB,OAAO,CAACqJ,kBAAkB,EAAE,EAAE,CAAC,CAC/BrJ,OAAO,CAACsJ,mBAAmB,EAAE,EAAE,CAAC;AACzC;AAEA,MAAME,mCAAmC,GAAG,IAAIjK,MAAM,CAAC,IAAImD,qBAAqB,CAAC2F,GAAG,CAAC/D,YAAY,CAAC,CAACyD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;AAChH;AACA;AACA;AACA;AACA;AACA,SAAS0B,4BAA4BA,CAAC;EAAExN,KAAK;EAAE8G;AAAU,CAAC,EAAE;EAAEoF,IAAI;EAAEuB,oBAAoB,GAAG7H;AAAiC,CAAC,EAAE;EAC3H,MAAM,CAACmB,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;EAC5B,MAAM9B,UAAU,GAAGgI,eAAe,CAAChN,KAAK,EAAEkM,IAAI,CAAC;EAC/C,MAAMwB,oBAAoB,GAAGnJ,MAAM,CAACE,OAAO,CAACO,UAAU,CAAC;EACvD,MAAM2I,eAAe,GAAGjB,oBAAoB,CAACe,oBAAoB,CAAC;EAClE,MAAMG,qBAAqB,GAAG,CAAC,CAAC;EAChC,IAAIC,YAAY,GAAG,CAAC;EACpB,KAAK,MAAM,CAACzG,WAAW,EAAEC,YAAY,CAAC,IAAIqG,oBAAoB,EAAE;IAC5D,MAAMnG,eAAe,GAAGoG,eAAe,CAACvG,WAAW,CAAC;IACpD,MAAM;MAAEwC,qBAAqB;MAAEH;IAAoB,CAAC,GAAGF,uBAAuB,CAAClC,YAAY,EAAEpF,MAAM,CAACsF,eAAe,CAAC,CAAC;IACrHsG,YAAY,IAAIpE,mBAAmB;IACnCmE,qBAAqB,CAACxG,WAAW,CAAC,GAAGwC,qBAAqB;EAC9D;EACA;EACA,MAAM,CAACkE,0BAA0B,GAAG,EAAE,CAAC,GAAG9N,KAAK,CAAC+B,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE;EACpE,MAAMgM,mBAAmB,GAAGT,YAAY,CAACM,qBAAqB,CAAC,GAAGE,0BAA0B;EAC5F,MAAMhG,0BAA0B,GAAG1H,IAAI,CAACF,GAAG,CAAC6N,mBAAmB,CAACrN,MAAM,GAAGV,KAAK,CAACU,MAAM,EAAE,CAAC,CAAC;EACzF,IAAIsN,OAAO,GAAGjH,IAAI,GAAG8G,YAAY,GAAG/F,0BAA0B;EAC9D,IAAImG,KAAK,GAAGjH,EAAE,GAAG6G,YAAY,GAAG/F,0BAA0B;EAC1D,IAAIkG,OAAO,KAAKC,KAAK,IACjBJ,YAAY;EACZ;EACAE,mBAAmB,CAAC1J,KAAK,CAAC,CAAC,EAAE4J,KAAK,GAAG,CAAC,CAAC,CAAClM,KAAK,CAACwL,mCAAmC,CAAC,EAAE;IACpFS,OAAO,EAAE;IACTC,KAAK,EAAE;EACX;EACA,OAAO;IACHjO,KAAK,EAAE+N,mBAAmB;IAC1BjH,SAAS,EAAE,CAACkH,OAAO,EAAEC,KAAK;EAC9B,CAAC;AACL;AAEA,SAASC,kBAAkBA,CAAC1B,YAAY,EAAE;EACtC,OAAOD,eAAe,CAACC,YAAY,EAAE,CAACxM,KAAK,EAAEU,MAAM,KAAKV,KAAK,CAAC2J,MAAM,CAACjJ,MAAM,EAAE,GAAG,CAAC,CAAC;AACtF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASyN,6CAA6CA,CAAC;EAAE7I,QAAQ;EAAE8I,oBAAoB,GAAGvI,+BAA+B;EAAE4H,oBAAoB,GAAG7H,+BAA+B;EAAEyI,UAAU,GAAIrG,CAAC,KAAM;IAAEiF,UAAU,EAAEjF;EAAE,CAAC;AAAG,CAAC,EAAE;EAC3N,MAAMsG,kBAAkB,GAAG,IAAIhL,MAAM,CAAC,QAAQmD,qBAAqB,CAAC2F,GAAG,CAAC/D,YAAY,CAAC,CAACyD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;EACnG,OAAO,CAAC;IAAEvB,YAAY;IAAEC;EAAK,CAAC,EAAE+D,UAAU,KAAK;IAC3C,IAAIA,UAAU,KAAK,QAAQ,EAAE;MACzB,OAAO;QAAEhE,YAAY;QAAEC;MAAK,CAAC;IACjC;IACA,MAAM;MAAExK,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,MAAM,CAACxD,IAAI,EAAEyH,KAAK,CAAC,GAAG1H,SAAS;IAC/B,MAAM2H,aAAa,GAAGjE,IAAI,CAACzG,OAAO,CAACuK,kBAAkB,EAAE,EAAE,CAAC;IAC1D,MAAMtH,EAAE,GAAGwH,KAAK,GAAGC,aAAa,CAAC/N,MAAM,CAAC,CAAC;IACzC,MAAMgO,gBAAgB,GAAG1O,KAAK,CAACqE,KAAK,CAAC,CAAC,EAAE0C,IAAI,CAAC,GAAG0H,aAAa,GAAGzO,KAAK,CAACqE,KAAK,CAAC2C,EAAE,CAAC;IAC/E,MAAM;MAAEiG,UAAU;MAAE0B,SAAS,GAAG;IAAG,CAAC,GAAGN,UAAU,CAACK,gBAAgB,CAAC;IACnE,MAAMlC,YAAY,GAAGjI,MAAM,CAACE,OAAO,CAACuI,eAAe,CAACC,UAAU,EAAE3H,QAAQ,CAAC,CAAC;IAC1E,IAAIuB,MAAM,GAAG8H,SAAS,CAACjO,MAAM;IAC7B,KAAK,MAAM,CAAC0G,WAAW,EAAEwH,uBAAuB,CAAC,IAAIpC,YAAY,EAAE;MAC/D,MAAMqC,eAAe,GAAGT,oBAAoB,CAAChH,WAAW,CAAC;MACzD,MAAMG,eAAe,GAAGkG,oBAAoB,CAACrG,WAAW,CAAC;MACzD,MAAMC,YAAY,GAAGhH,MAAM,CAACuO,uBAAuB,CAAC;MACpD,MAAMnH,qBAAqB,GAAGZ,MAAM,GAAGH,0BAA0B,CAACU,WAAW,CAAC;MAC9E,IAAIK,qBAAqB,IAAIV,IAAI,IAC7BU,qBAAqB,IAAIT,EAAE,IAC3BK,YAAY,KAAKtH,KAAK,CAACsH,YAAY,EAAEwH,eAAe,EAAEtH,eAAe,CAAC,EAAE;QACxE,OAAO;UAAEgD,YAAY;UAAEC,IAAI,EAAE;QAAG,CAAC,CAAC,CAAC;MACvC;MACA3D,MAAM,IACF+H,uBAAuB,CAAClO,MAAM;MAC1B;MACA,CAAC;IACb;IACA,OAAO;MAAE6J,YAAY;MAAEC;IAAK,CAAC;EACjC,CAAC;AACL;AAEA,SAASsE,0BAA0BA,CAACxJ,QAAQ,EAAE;EAC1C,IAAI,CAACA,QAAQ,CAACsG,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC1B,OAAO7D,QAAQ;EACnB;EACA,MAAMgH,kBAAkB,GAAG,UAAU;EACrC,OAAO,CAAC;IAAExE,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,MAAM;MAAExK,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,MAAMwB,QAAQ,GAAG/L,KAAK,CAACgP,WAAW,CAAC,CAAC;IACpC,MAAMC,OAAO,GAAGzE,IAAI,CAACwE,WAAW,CAAC,CAAC;IAClC,IAAIjD,QAAQ,CAAChK,KAAK,CAACyE,0BAA0B,CAAC,IAC1CyI,OAAO,CAAClN,KAAK,CAACgN,kBAAkB,CAAC,EAAE;MACnC,OAAO;QACHxE,YAAY,EAAE;UACVvK,KAAK,EAAE+L,QAAQ,CAACtL,UAAU,CAAC+F,0BAA0B,EAAE,EAAE,CAAC;UAC1DM;QACJ,CAAC;QACD0D,IAAI,EAAE,GAAGyE,OAAO;MACpB,CAAC;IACL;IACA,OAAO;MAAE1E,YAAY,EAAE;QAAEzD,SAAS;QAAE9G,KAAK,EAAE+L;MAAS,CAAC;MAAEvB,IAAI,EAAEyE;IAAQ,CAAC;EAC1E,CAAC;AACL;AACA,SAASC,2BAA2BA,CAAC5J,QAAQ,EAAE;EAC3C,IAAI,CAACA,QAAQ,CAACsG,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC1B,OAAO7D,QAAQ;EACnB;EACA,OAAO,CAAC;IAAE/H,KAAK;IAAE8G;EAAU,CAAC,EAAEqI,mBAAmB,KAAK;IAClD,IAAI,CAACnP,KAAK,CAAC+B,KAAK,CAACwE,yBAAyB,CAAC,IACvCvG,KAAK,CAAC+B,KAAK,CAACyE,0BAA0B,CAAC,EAAE;MACzC,OAAO;QAAExG,KAAK;QAAE8G;MAAU,CAAC;IAC/B;IACA,MAAM,CAACC,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC5B;IACA,IAAIqI,mBAAmB,CAACnP,KAAK,CAAC+B,KAAK,CAACyE,0BAA0B,CAAC,EAAE;MAC7D,MAAMuF,QAAQ,GAAG/L,KAAK,CAAC+D,OAAO,CAACwC,yBAAyB,EAAE,EAAE,CAAC;MAC7D,OAAO;QACHvG,KAAK,EAAE+L,QAAQ;QACfjF,SAAS,EAAE,CACP1G,IAAI,CAACH,GAAG,CAAC8G,IAAI,EAAEgF,QAAQ,CAACrL,MAAM,CAAC,EAC/BN,IAAI,CAACH,GAAG,CAAC+G,EAAE,EAAE+E,QAAQ,CAACrL,MAAM,CAAC;MAErC,CAAC;IACL;IACA,MAAM0O,YAAY,GAAG,GAAGtJ,mBAAmB,GAAG9F,KAAK,CAAC4L,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG;IAChF,MAAMG,QAAQ,GAAG/L,KAAK,CAAC+D,OAAO,CAACwC,yBAAyB,EAAGyB,CAAC,IAAKA,CAAC,KAAKlC,mBAAmB,GAAGsJ,YAAY,GAAGpH,CAAC,CAAC;IAC9G,OAAO;MACHhI,KAAK,EAAE+L,QAAQ;MACfjF,SAAS,EAAEE,EAAE,IAAI+E,QAAQ,CAAC9I,OAAO,CAACmM,YAAY,CAAC,GACzC,CAACrD,QAAQ,CAACrL,MAAM,EAAEqL,QAAQ,CAACrL,MAAM,CAAC,GAClCoG;IACV,CAAC;EACL,CAAC;AACL;AAEA,SAASuI,sBAAsBA,CAACC,QAAQ,EAAErL,QAAQ,EAAE;EAChD,MAAMsL,cAAc,GAAGhO,yBAAyB,CAAC0C,QAAQ,CAAC;EAC1D,OAAOM,MAAM,CAACC,WAAW,CAACD,MAAM,CAACE,OAAO,CAAC6K,QAAQ,CAAC,CAAClD,GAAG,CAAC,CAAC,CAACoD,GAAG,EAAExP,KAAK,CAAC,KAAK;IACrE,MAAMyP,aAAa,GAAGF,cAAc,CAACC,GAAG,CAAC;IACzC,OAAO,CACHA,GAAG,EACHxP,KAAK,CAACU,MAAM,KAAK+O,aAAa,IAAI,MAAM,CAAClM,IAAI,CAACvD,KAAK,CAAC,GAC9C,GAAG,CAACkC,QAAQ,CAACuN,aAAa,EAAE,GAAG,CAAC,GAChCzP,KAAK,CACd;EACL,CAAC,CAAC,CAAC;AACP;AAEA,MAAM0P,SAAS,GAAG,MAAM;AACxB,SAASC,6BAA6BA,CAAC;EAAExM,gBAAgB;EAAElD,GAAG,GAAGyF,gBAAgB;EAAExF,GAAG,GAAGyF,gBAAgB;EAAE7B,cAAc,GAAG,EAAE;EAAE4G,oBAAoB,GAAG;AAAK,CAAC,EAAE;EAC3J,OAAO,CAAC;IAAE1K,KAAK;IAAE8G;EAAU,CAAC,KAAK;IAC7B,MAAM8I,sBAAsB,GAAG9L,cAAc,IAAI9D,KAAK,CAAC6J,QAAQ,CAAC/F,cAAc,CAAC;IAC/E,MAAM+G,WAAW,GAAGjH,oBAAoB,CAAC5D,KAAK,EAAEmD,gBAAgB,EAAEW,cAAc,CAAC;IACjF,IAAIwH,cAAc,GAAG,EAAE;IACvB,KAAK,MAAM9J,UAAU,IAAIqJ,WAAW,EAAE;MAClCS,cAAc,IAAIA,cAAc,GAAGxH,cAAc,GAAG,EAAE;MACtD,MAAMiB,UAAU,GAAGf,eAAe,CAACxC,UAAU,EAAE2B,gBAAgB,CAAC;MAChE,IAAI,CAACK,oBAAoB,CAAChC,UAAU,EAAE2B,gBAAgB,CAAC,EAAE;QACrD,MAAM0M,SAAS,GAAGR,sBAAsB,CAACtK,UAAU,EAAE5B,gBAAgB,CAAC;QACtE,MAAM2M,UAAU,GAAG3K,YAAY,CAAC0K,SAAS,EAAE;UAAEzK,QAAQ,EAAEjC;QAAiB,CAAC,CAAC;QAC1E,MAAM4M,IAAI,GAAGvO,UAAU,CAACqI,QAAQ,CAACa,oBAAoB,CAAC,GAChDA,oBAAoB,GACpB,EAAE;QACRY,cAAc,IAAIwE,UAAU,GAAGC,IAAI;QACnC;MACJ;MACA,MAAM/O,IAAI,GAAG8D,cAAc,CAACP,MAAM,CAAC6G,MAAM,CAAC;QAAErK,IAAI,EAAE2O;MAAU,CAAC,EAAE3K,UAAU,CAAC,CAAC;MAC3E,MAAMiL,WAAW,GAAGjQ,KAAK,CAACiB,IAAI,EAAEf,GAAG,EAAEC,GAAG,CAAC;MACzCoL,cAAc,IAAInG,YAAY,CAACnD,cAAc,CAACgO,WAAW,CAAC,EAAE;QACxD5K,QAAQ,EAAEjC;MACd,CAAC,CAAC;IACN;IACA,OAAO;MACH2D,SAAS;MACT9G,KAAK,EAAEsL,cAAc,IAAIsE,sBAAsB,GAAG9L,cAAc,GAAG,EAAE;IACzE,CAAC;EACL,CAAC;AACL;AAEA,SAASmM,yBAAyBA,CAAC;EAAE9M,gBAAgB;EAAEyD,qBAAqB;EAAE9C,cAAc,GAAG,EAAE;EAAEuB,iBAAiB,GAAGH;AAAqB,CAAC,EAAE;EAC3I,OAAO,CAAC;IAAEqF,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,MAAM0F,gBAAgB,GAAG/M,gBAAgB,CAACM,KAAK,CAACmD,qBAAqB,CAAC;IACtE,MAAMuJ,YAAY,GAAG3F,IAAI,CAACoB,QAAQ,CAACvG,iBAAiB,CAAC;IACrD,MAAMjB,YAAY,GAAGoG,IAAI,CACpBnG,KAAK,CAAC,CAAC,EAAE8L,YAAY,GAAG3F,IAAI,CAACvH,OAAO,CAACoC,iBAAiB,CAAC,GAAG2D,QAAQ,CAAC,CACnEvF,KAAK,CAAC,IAAI,CAAC,CACXiB,MAAM,CAACE,OAAO,CAAC;IACpB,IAAI,CAACR,YAAY,CAAC1D,MAAM,IAAI0D,YAAY,CAAC1D,MAAM,GAAGwP,gBAAgB,CAACxP,MAAM,KAAK,CAAC,EAAE;MAC7E,OAAO;QAAE6J,YAAY;QAAEC;MAAK,CAAC;IACjC;IACA,MAAM4F,KAAK,GAAGhM,YAAY,CAAC8G,MAAM,CAAC,CAACkF,KAAK,EAAEzM,OAAO,EAAE0M,KAAK,KAAK;MACzD,IAAI5O,EAAE;MACN,MAAMoB,QAAQ,GAAG,CAACpB,EAAE,GAAGyO,gBAAgB,CAACG,KAAK,GAAGH,gBAAgB,CAACxP,MAAM,CAAC,MAAM,IAAI,IAAIe,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE;MAC7G,MAAM6O,SAAS,GAAGlQ,IAAI,CAACmQ,KAAK,CAACF,KAAK,GAAGH,gBAAgB,CAACxP,MAAM,CAAC;MAC7D,MAAM8P,iBAAiB,GAAGH,KAAK,GAAGH,gBAAgB,CAACxP,MAAM,KAAKwP,gBAAgB,CAACxP,MAAM,GAAG,CAAC;MACzF,IAAI,CAAC0P,KAAK,CAACE,SAAS,CAAC,EAAE;QACnBF,KAAK,CAACE,SAAS,CAAC,GAAG,EAAE;MACzB;MACAF,KAAK,CAACE,SAAS,CAAC,IAAIE,iBAAiB,GAC/B7M,OAAO,GACP,GAAGA,OAAO,CAACzB,QAAQ,CAACW,QAAQ,CAACnC,MAAM,EAAE,GAAG,CAAC,GAAGkG,qBAAqB,EAAE;MACzE,OAAOwJ,KAAK;IAChB,CAAC,EAAE,EAAE,CAAC;IACN,OAAO;MACH7F,YAAY;MACZC,IAAI,EAAE2F,YAAY,GACZ,GAAGC,KAAK,CAAC,CAAC,CAAC,GAAG5F,IAAI,CAACnG,KAAK,CAACmG,IAAI,CAACvH,OAAO,CAACoC,iBAAiB,CAAC,CAAC,EAAE,GAC3D+K,KAAK,CAACtE,IAAI,CAAChI,cAAc;IACnC,CAAC;EACL,CAAC;AACL;AAEA,SAAS2M,oCAAoCA,CAAC/H,OAAO,EAAE;EACnD,MAAMgI,SAAS,GAAG,IAAIpN,MAAM,CAAC,GAAG+E,YAAY,CAACK,OAAO,CAAC,GAAG,CAAC;EACzD,OAAOA,OAAO,GACR,CAAC;IAAE1I,KAAK;IAAE8G;EAAU,CAAC,EAAEqI,mBAAmB,KAAK;IAC7C,IAAI,CAACnP,KAAK,IAAI,CAACmP,mBAAmB,CAACnP,KAAK,CAAC6J,QAAQ,CAACnB,OAAO,CAAC,EAAE;MACxD;MACA,OAAO;QAAE1I,KAAK;QAAE8G;MAAU,CAAC;IAC/B;IACA,IAAI,CAAC9G,KAAK,CAAC6J,QAAQ,CAACnB,OAAO,CAAC,IACxB,CAACyG,mBAAmB,CAACnP,KAAK,CAAC6J,QAAQ,CAACnB,OAAO,CAAC,EAAE;MAC9C,OAAO;QAAE5B,SAAS;QAAE9G,KAAK,EAAEA,KAAK,GAAG0I;MAAQ,CAAC;IAChD;IACA,MAAMiI,yBAAyB,GAAGxB,mBAAmB,CAACnP,KAAK,CAAC+D,OAAO,CAAC2M,SAAS,EAAE,EAAE,CAAC;IAClF,MAAME,kBAAkB,GAAGzB,mBAAmB,CAACrI,SAAS,CAAC,CAAC,CAAC,GAAG6J,yBAAyB,CAACjQ,MAAM;IAC9F,MAAMmQ,gCAAgC,GAAG5H,yBAAyB,CAAC0H,yBAAyB,EAAE3Q,KAAK,CAAC;IACpG,OAAO;MACH8G,SAAS;MACT9G,KAAK,EAAEmM,KAAK,CAACpF,IAAI,CAAC2B,OAAO,CAAC,CACrBoI,OAAO,CAAC,CAAC,CACT5F,MAAM,CAAC,CAACa,QAAQ,EAAEM,IAAI,EAAEgE,KAAK,KAAK;QACnC,MAAMlJ,CAAC,GAAG4E,QAAQ,CAACrL,MAAM,GAAG,CAAC,GAAG2P,KAAK;QACrC,MAAMU,uBAAuB,GAAGF,gCAAgC,CAAC1J,CAAC,CAAC,KAAKkF,IAAI,IACxEuE,kBAAkB;QACtB,OAAO7E,QAAQ,CAAC5E,CAAC,CAAC,KAAKkF,IAAI,IAAI0E,uBAAuB,GAChDhF,QAAQ,CAAC1H,KAAK,CAAC,CAAC,EAAE8C,CAAC,GAAG,CAAC,CAAC,GAAGkF,IAAI,GAAGN,QAAQ,CAAC1H,KAAK,CAAC8C,CAAC,GAAG,CAAC,CAAC,GACvD4E,QAAQ;MAClB,CAAC,EAAE/L,KAAK;IACZ,CAAC;EACL,CAAC,GACC+H,QAAQ;AAClB;AAEA,SAASiJ,mCAAmCA,CAACvI,MAAM,EAAE;EACjD,OAAOA,MAAM,GACP,CAAC;IAAEzI,KAAK;IAAE8G;EAAU,CAAC,EAAEqI,mBAAmB,KAAK;IAC7C,IAAInP,KAAK,CAACiR,UAAU,CAACxI,MAAM,CAAC;IAAI;IAC3B,CAACzI,KAAK,IAAI,CAACmP,mBAAmB,CAACnP,KAAK,CAACiR,UAAU,CAACxI,MAAM,CAAE,CAAC;IAAA,EAC5D;MACE,OAAO;QAAEzI,KAAK;QAAE8G;MAAU,CAAC;IAC/B;IACA,MAAM,CAACC,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC5B,MAAMoK,aAAa,GAAG/E,KAAK,CAACpF,IAAI,CAAC0B,MAAM,CAAC,CAACyC,MAAM,CAAC,CAACiG,aAAa,EAAE9E,IAAI,EAAElF,CAAC,KAAKgK,aAAa,CAAChK,CAAC,CAAC,KAAKkF,IAAI,GAC/F8E,aAAa,GACbA,aAAa,CAAC9M,KAAK,CAAC,CAAC,EAAE8C,CAAC,CAAC,GAAGkF,IAAI,GAAG8E,aAAa,CAAC9M,KAAK,CAAC8C,CAAC,CAAC,EAAEnH,KAAK,CAAC;IACvE,MAAMoR,eAAe,GAAGF,aAAa,CAACxQ,MAAM,GAAGV,KAAK,CAACU,MAAM;IAC3D,OAAO;MACHoG,SAAS,EAAE,CAACC,IAAI,GAAGqK,eAAe,EAAEpK,EAAE,GAAGoK,eAAe,CAAC;MACzDpR,KAAK,EAAEkR;IACX,CAAC;EACL,CAAC,GACCnJ,QAAQ;AAClB;AAEA,SAASsJ,2BAA2BA,CAAC;EAAElO,gBAAgB;EAAEyD,qBAAqB;EAAE9C,cAAc,GAAG;AAAI,CAAC,EAAE;EACpG,OAAO,CAAC;IAAEyG,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,MAAM;MAAExK,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,IAAIC,IAAI,KAAK5D,qBAAqB,EAAE;MAChC,OAAO;QACH2D,YAAY;QACZC,IAAI,EAAE1D,SAAS,CAAC,CAAC,CAAC,KAAK9G,KAAK,CAACU,MAAM,GAAG8J,IAAI,GAAG;MACjD,CAAC;IACL;IACA,MAAMiE,aAAa,GAAGjE,IAAI,CAAC/J,UAAU,CAAC,IAAI6C,MAAM,CAAC,QAAQ+E,YAAY,CAACzB,qBAAqB,CAAC,GAAG9C,cAAc,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC;IAC3H,IAAI,CAAC2K,aAAa,EAAE;MAChB,OAAO;QAAElE,YAAY;QAAEC,IAAI,EAAE;MAAG,CAAC;IACrC;IACA,MAAM,CAACzD,IAAI,EAAEyH,KAAK,CAAC,GAAG1H,SAAS;IAC/B,IAAIE,EAAE,GAAGwH,KAAK,GAAGhE,IAAI,CAAC9J,MAAM;IAC5B,MAAMgO,gBAAgB,GAAG1O,KAAK,CAACqE,KAAK,CAAC,CAAC,EAAE0C,IAAI,CAAC,GAAG0H,aAAa,GAAGzO,KAAK,CAACqE,KAAK,CAAC2C,EAAE,CAAC;IAC/E,MAAM6D,WAAW,GAAGjH,oBAAoB,CAAC8K,gBAAgB,EAAEvL,gBAAgB,EAAEW,cAAc,CAAC;IAC5F,IAAIwH,cAAc,GAAG,EAAE;IACvB,MAAMgG,iBAAiB,GAAG1M,OAAO,CAACd,cAAc,CAAC,IAAI4K,gBAAgB,CAAC9C,QAAQ,CAAC9H,cAAc,CAAC;IAC9F,KAAK,MAAMtC,UAAU,IAAIqJ,WAAW,EAAE;MAClC,MAAM;QAAEjD,mBAAmB;QAAEC;MAAiB,CAAC,GAAGlB,kBAAkB,CAAC;QACjEnF,UAAU;QACV2B,gBAAgB;QAChByD,qBAAqB;QACrBC,MAAM,EAAEyE,cAAc,CAAC5K,MAAM;QAC7BoG,SAAS,EAAE,CAACC,IAAI,EAAEC,EAAE;MACxB,CAAC,CAAC;MACF,IAAIxF,UAAU,IAAI,CAACoG,mBAAmB,EAAE;QACpC,OAAO;UAAE2C,YAAY;UAAEC,IAAI,EAAE;QAAG,CAAC,CAAC,CAAC;MACvC;MACAxD,EAAE,GAAGa,gBAAgB,CAAC,CAAC,CAAC;MACxByD,cAAc,IACVgG,iBAAiB,IAAI,CAAChG,cAAc,GAC9B1D,mBAAmB,GAAG9D,cAAc,GACpC8D,mBAAmB;IACjC;IACA,MAAMqH,OAAO,GAAG3D,cAAc,CAACjH,KAAK,CAAC0C,IAAI,EAAEC,EAAE,CAAC;IAC9C,OAAO;MACHuD,YAAY,EAAE;QACVzD,SAAS;QACT9G,KAAK,EAAEsL,cAAc,CAACjH,KAAK,CAAC,CAAC,EAAE0C,IAAI,CAAC,GAChCkI,OAAO,CACFxL,KAAK,CAACmD,qBAAqB,CAAC,CAC5BwF,GAAG,CAAEzI,OAAO,IAAK,GAAG,CAAC4N,MAAM,CAAC5N,OAAO,CAACjD,MAAM,CAAC,CAAC,CAC5CoL,IAAI,CAAClF,qBAAqB,CAAC,GAChC0E,cAAc,CAACjH,KAAK,CAAC2C,EAAE;MAC/B,CAAC;MACDwD,IAAI,EAAEyE;IACV,CAAC;EACL,CAAC;AACL;AAEA,SAASuC,mBAAmBA,CAACC,IAAI,EAAEC,OAAO,EAAEC,oBAAoB,EAAE;EAC9D,OAAO,CAACC,OAAO,EAAEC,cAAc,KAAK;IAChC,MAAMC,QAAQ,GAAGA,CAAA,KAAMJ,OAAO,CAACE,OAAO,EAAEC,cAAc,CAAC;IACvDD,OAAO,CAACG,gBAAgB,CAACN,IAAI,EAAEK,QAAQ,EAAEH,oBAAoB,CAAC;IAC9D,OAAO,MAAMC,OAAO,CAACI,mBAAmB,CAACP,IAAI,EAAEK,QAAQ,EAAEH,oBAAoB,CAAC;EAClF,CAAC;AACL;AAEA,SAASM,uBAAuBA,CAACjS,KAAK,EAAE;EACpC,OAAOwR,mBAAmB,CAAC,OAAO,EAAGI,OAAO,IAAK;IAC7C,IAAI,CAACA,OAAO,CAAC5R,KAAK,EAAE;MAChBJ,oBAAoB,CAACgS,OAAO,EAAE5R,KAAK,CAAC;IACxC;EACJ,CAAC,CAAC;AACN;AAEA,SAASkS,6BAA6BA,CAACR,OAAO,EAAE;EAC5C,OAAO,CAACE,OAAO,EAAEO,OAAO,KAAK;IACzB,MAAMC,QAAQ,GAAGR,OAAO,CAACS,aAAa;IACtC,IAAIC,aAAa,GAAG,CAAC;IACrB,MAAMC,aAAa,GAAGA,CAAA,KAAMD,aAAa,EAAE;IAC3C,MAAME,WAAW,GAAGA,CAAA,KAAM;MACtBF,aAAa,GAAGlS,IAAI,CAACF,GAAG,CAAC,EAAEoS,aAAa,EAAE,CAAC,CAAC;IAChD,CAAC;IACD,MAAMR,QAAQ,GAAGA,CAAA,KAAM;MACnB,IAAI,CAACF,OAAO,CAACa,OAAO,CAAC,QAAQ,CAAC,EAAE;QAC5B;MACJ;MACA,IAAIH,aAAa,EAAE;QACf,OAAOF,QAAQ,CAACL,gBAAgB,CAAC,SAAS,EAAED,QAAQ,EAAE;UAClDY,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE;QACb,CAAC,CAAC;MACN;MACAjB,OAAO,CAACE,OAAO,EAAEO,OAAO,CAAC;IAC7B,CAAC;IACDC,QAAQ,CAACL,gBAAgB,CAAC,iBAAiB,EAAED,QAAQ,EAAE;MAAEa,OAAO,EAAE;IAAK,CAAC,CAAC;IACzE;IACAf,OAAO,CAACG,gBAAgB,CAAC,OAAO,EAAED,QAAQ,EAAE;MAAEa,OAAO,EAAE;IAAK,CAAC,CAAC;IAC9Df,OAAO,CAACG,gBAAgB,CAAC,WAAW,EAAEQ,aAAa,EAAE;MAAEI,OAAO,EAAE;IAAK,CAAC,CAAC;IACvEP,QAAQ,CAACL,gBAAgB,CAAC,SAAS,EAAES,WAAW,EAAE;MAAEG,OAAO,EAAE;IAAK,CAAC,CAAC;IACpE,OAAO,MAAM;MACTP,QAAQ,CAACJ,mBAAmB,CAAC,iBAAiB,EAAEF,QAAQ,CAAC;MACzDF,OAAO,CAACI,mBAAmB,CAAC,OAAO,EAAEF,QAAQ,CAAC;MAC9CF,OAAO,CAACI,mBAAmB,CAAC,WAAW,EAAEO,aAAa,CAAC;MACvDH,QAAQ,CAACJ,mBAAmB,CAAC,SAAS,EAAEQ,WAAW,CAAC;IACxD,CAAC;EACL,CAAC;AACL;AAEA,SAASI,iBAAiBA,CAACC,KAAK,EAAE;EAC9B,OAAOX,6BAA6B,CAAEN,OAAO,IAAK;IAC9C,IAAInQ,EAAE,EAAEC,EAAE;IACV,MAAMoR,KAAK,GAAG,CAACrR,EAAE,GAAGmQ,OAAO,CAACmB,cAAc,MAAM,IAAI,IAAItR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAC9E,MAAMuR,GAAG,GAAG,CAACtR,EAAE,GAAGkQ,OAAO,CAACqB,YAAY,MAAM,IAAI,IAAIvR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAC1E,MAAM,CAACwR,SAAS,EAAEC,OAAO,CAAC,GAAGN,KAAK,CAACjB,OAAO,CAAC5R,KAAK,EAAE,CAAC8S,KAAK,EAAEE,GAAG,CAAC,CAAC;IAC/D,IAAIE,SAAS,GAAGJ,KAAK,IAAIK,OAAO,GAAGH,GAAG,EAAE;MACpCpB,OAAO,CAACwB,iBAAiB,CAACrT,KAAK,CAAC+S,KAAK,EAAEI,SAAS,EAAEC,OAAO,CAAC,EAAEpT,KAAK,CAACiT,GAAG,EAAEE,SAAS,EAAEC,OAAO,CAAC,CAAC;IAC/F;EACJ,CAAC,CAAC;AACN;AAEA,MAAME,kBAAkB,GAAIzB,OAAO,IAAK;EACpC,MAAME,QAAQ,GAAGA,CAAA,KAAM;IACnB,MAAM9R,KAAK,GAAG4R,OAAO,CAAC5R,KAAK;IAC3B4R,OAAO,CAACG,gBAAgB,CAAC,aAAa,EAAGuB,KAAK,IAAK;MAC/C,IAAIA,KAAK,CAACC,gBAAgB,IAAIvT,KAAK,KAAK4R,OAAO,CAAC5R,KAAK,EAAE;QACnD4R,OAAO,CAAC4B,aAAa,CAAC,IAAIC,WAAW,CAAC,eAAe,EAAE;UAAEC,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC;MAC9E;IACJ,CAAC,EAAE;MAAEhB,IAAI,EAAE;IAAK,CAAC,CAAC;EACtB,CAAC;EACDd,OAAO,CAACG,gBAAgB,CAAC,aAAa,EAAED,QAAQ,EAAE,IAAI,CAAC;EACvD,OAAO,MAAMF,OAAO,CAACI,mBAAmB,CAAC,aAAa,EAAEF,QAAQ,EAAE,IAAI,CAAC;AAC3E,CAAC;AAED,SAAS6B,yBAAyBA,CAAC3T,KAAK,EAAE;EACtC,OAAOwR,mBAAmB,CAAC,MAAM,EAAGI,OAAO,IAAK;IAC5C,IAAIA,OAAO,CAAC5R,KAAK,KAAKA,KAAK,EAAE;MACzBJ,oBAAoB,CAACgS,OAAO,EAAE,EAAE,CAAC;IACrC;EACJ,CAAC,CAAC;AACN;AAEA,SAASgC,4BAA4BA,CAACC,kBAAkB,EAAE;EACtD,IAAIA,kBAAkB,GAAG,CAAC,EAAE;IACxB,OAAO5L,IAAI;EACf;EACA,OAAQ2J,OAAO,IAAK;IAChB,MAAME,QAAQ,GAAIwB,KAAK,IAAK;MACxB,MAAMQ,UAAU,GAAGzT,MAAM,CAACuR,OAAO,CAACmB,cAAc,CAAC;MACjD,MAAM/S,KAAK,GAAG4R,OAAO,CAAC5R,KAAK,CAACgP,WAAW,CAAC,CAAC;MACzC,IAAKsE,KAAK,CAAC9D,GAAG,KAAK,SAAS,IAAI8D,KAAK,CAAC9D,GAAG,KAAK,WAAW,IACrDsE,UAAU,GAAGD,kBAAkB,EAAE;QACjC;MACJ;MACAP,KAAK,CAACS,cAAc,CAAC,CAAC;MACtB;MACA,MAAMC,qBAAqB,GAAGhU,KAAK,CAAC4L,QAAQ,CAAC,GAAG,CAAC,GAC3C,GAAG,GACH5L,KAAK,CAAC4L,QAAQ,CAAC,GAAG,CAAC,IAAI0H,KAAK,CAAC9D,GAAG,KAAK,SAAS,GAC1C,GAAG,GACH,GAAG;MACb,MAAMyE,WAAW,GAAG,GAAGnO,mBAAmB,GAAGkO,qBAAqB,GAAG;MACrEpU,oBAAoB,CAACgS,OAAO,EAAE;QAC1B5R,KAAK,EAAEA,KAAK,CAACU,MAAM,KAAKmT,kBAAkB,GACpC7T,KAAK,GAAGiU,WAAW,GACnBjU,KAAK,CAAC+D,OAAO,CAACwC,yBAAyB,EAAE0N,WAAW,CAAC;QAC3DnN,SAAS,EAAE,CAACgN,UAAU,EAAEA,UAAU;MACtC,CAAC,CAAC;IACN,CAAC;IACDlC,OAAO,CAACG,gBAAgB,CAAC,SAAS,EAAED,QAAQ,CAAC;IAC7C,OAAO,MAAMF,OAAO,CAACI,mBAAmB,CAAC,SAAS,EAAEF,QAAQ,CAAC;EACjE,CAAC;AACL;AAEA,SAASoC,gCAAgCA,CAAC;EAAEC,IAAI;EAAElQ,QAAQ;EAAEwJ;AAAsB,CAAC,EAAE;EACjF,MAAM2G,eAAe,GAAGC,yBAAyB,CAACpQ,QAAQ,CAAC;EAC3D,OAAOkQ,IAAI,IAAI,CAAC,GACVlM,IAAI,GACH2J,OAAO,IAAK;IACX,MAAME,QAAQ,GAAIwB,KAAK,IAAK;MACxB,IAAI7R,EAAE;MACN,IAAI6R,KAAK,CAAC9D,GAAG,KAAK,SAAS,IAAI8D,KAAK,CAAC9D,GAAG,KAAK,WAAW,EAAE;QACtD;MACJ;MACA8D,KAAK,CAACS,cAAc,CAAC,CAAC;MACtB,MAAMhB,cAAc,GAAG,CAACtR,EAAE,GAAGmQ,OAAO,CAACmB,cAAc,MAAM,IAAI,IAAItR,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;MACvF,MAAM6S,aAAa,GAAGC,gBAAgB,CAAC;QACnCH,eAAe;QACfrB;MACJ,CAAC,CAAC;MACF,IAAI,CAACuB,aAAa,EAAE;QAChB;MACJ;MACA,MAAME,YAAY,GAAGC,kBAAkB,CAAC;QACpC3N,SAAS,EAAEsN,eAAe,CAACM,GAAG,CAACJ,aAAa,CAAC;QAC7CtU,KAAK,EAAE4R,OAAO,CAAC5R,KAAK;QACpB2U,KAAK,EAAErB,KAAK,CAAC9D,GAAG,KAAK,SAAS,GAAG2E,IAAI,GAAG,CAACA,IAAI;QAC7CjU,GAAG,EAAEuN,oBAAoB,CAAC6G,aAAa;MAC3C,CAAC,CAAC;MACF1U,oBAAoB,CAACgS,OAAO,EAAE;QAC1B5R,KAAK,EAAEwU,YAAY;QACnB1N,SAAS,EAAE,CAACiM,cAAc,EAAEA,cAAc;MAC9C,CAAC,CAAC;IACN,CAAC;IACDnB,OAAO,CAACG,gBAAgB,CAAC,SAAS,EAAED,QAAQ,CAAC;IAC7C,OAAO,MAAMF,OAAO,CAACI,mBAAmB,CAAC,SAAS,EAAEF,QAAQ,CAAC;EACjE,CAAC;AACT;AACA,SAASuC,yBAAyBA,CAACpQ,QAAQ,EAAE;EACzC,OAAO,IAAI2Q,GAAG,CAAC,CACX,CAAC,OAAO,EAAEC,eAAe,CAAC5Q,QAAQ,EAAE,IAAI,CAAC,CAAC,EAC1C,CAAC,cAAc,EAAE4Q,eAAe,CAAC5Q,QAAQ,EAAE,KAAK,CAAC,CAAC,EAClD,CAAC,SAAS,EAAE4Q,eAAe,CAAC5Q,QAAQ,EAAE,IAAI,CAAC,CAAC,EAC5C,CAAC,SAAS,EAAE4Q,eAAe,CAAC5Q,QAAQ,EAAE,IAAI,CAAC,CAAC,CAC/C,CAAC;AACN;AACA,SAAS4Q,eAAeA,CAAC3I,IAAI,EAAEvI,OAAO,EAAE;EACpC,MAAM0M,KAAK,GAAGnE,IAAI,CAACjJ,OAAO,CAACU,OAAO,CAAC;EACnC,OAAO0M,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAACA,KAAK,EAAEA,KAAK,GAAG1M,OAAO,CAACjD,MAAM,CAAC;AACpE;AACA,SAAS6T,gBAAgBA,CAAC;EAAEH,eAAe;EAAErB;AAAgB,CAAC,EAAE;EAC5D,KAAK,MAAM,CAAC3L,WAAW,EAAE0N,YAAY,CAAC,IAAIV,eAAe,CAAC3P,OAAO,CAAC,CAAC,EAAE;IACjE,MAAM,CAACsC,IAAI,EAAEC,EAAE,CAAC,GAAG8N,YAAY;IAC/B,IAAI/N,IAAI,IAAIgM,cAAc,IAAIA,cAAc,IAAI/L,EAAE,EAAE;MAChD,OAAOI,WAAW;IACtB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASqN,kBAAkBA,CAAC;EAAE3N,SAAS;EAAE9G,KAAK;EAAE2U,KAAK;EAAEzU;AAAK,CAAC,EAAE;EAC3D,MAAM,CAAC6G,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;EAC5B,MAAMO,YAAY,GAAGhH,MAAM,CAACL,KAAK,CAACqE,KAAK,CAAC0C,IAAI,EAAEC,EAAE,CAAC,CAAC2C,MAAM,CAAC3C,EAAE,GAAGD,IAAI,EAAE,GAAG,CAAC,CAAC;EACzE,MAAMgO,eAAe,GAAGC,GAAG,CAAC3N,YAAY,GAAGsN,KAAK,EAAEzU,GAAG,GAAG,CAAC,CAAC;EAC1D,OAAQF,KAAK,CAACqE,KAAK,CAAC,CAAC,EAAE0C,IAAI,CAAC,GACxB9E,MAAM,CAAC8S,eAAe,CAAC,CAAC7S,QAAQ,CAAC8E,EAAE,GAAGD,IAAI,EAAE,GAAG,CAAC,GAChD/G,KAAK,CAACqE,KAAK,CAAC2C,EAAE,EAAEhH,KAAK,CAACU,MAAM,CAAC;AACrC;AACA,SAASsU,GAAGA,CAAChV,KAAK,EAAEE,GAAG,EAAE;EACrB,IAAIF,KAAK,GAAG,CAAC,EAAE;IACXA,KAAK,IAAII,IAAI,CAAC6U,KAAK,CAAC7U,IAAI,CAAC8U,GAAG,CAAClV,KAAK,CAAC,GAAGE,GAAG,GAAG,CAAC,CAAC,GAAGA,GAAG;EACxD;EACA,OAAOF,KAAK,GAAGE,GAAG;AACtB;AAEA,SAASiV,sBAAsBA,CAACC,WAAW,EAAEC,WAAW,GAAG,KAAK,EAAE;EAC9D,IAAIC,cAAc,GAAG,EAAE;EACvB,IAAIC,MAAM,GAAG,YAAY;EACzB,MAAMC,iBAAiB,GAAIxV,KAAK,IAAK;IACjC,KAAK,IAAImH,CAAC,GAAGnH,KAAK,CAACU,MAAM,GAAG,CAAC,EAAEyG,CAAC,IAAImO,cAAc,CAAC5U,MAAM,EAAEyG,CAAC,EAAE,EAAE;MAC5D,IAAInH,KAAK,CAACmH,CAAC,CAAC,KAAKiO,WAAW,CAACjO,CAAC,CAAC,EAAE;QAC7B,OAAOnH,KAAK,CAACqE,KAAK,CAAC,CAAC,EAAE8C,CAAC,GAAG,CAAC,CAAC;MAChC;IACJ;IACA,OAAOnH,KAAK,CAACqE,KAAK,CAAC,CAAC,EAAEiR,cAAc,CAAC5U,MAAM,CAAC;EAChD,CAAC;EACD,MAAM+U,OAAO,GAAG,CAAC7C,iBAAiB,CAAE5S,KAAK,IAAK,CAAC,CAAC,EAAEwV,iBAAiB,CAACxV,KAAK,CAAC,CAACU,MAAM,CAAC,CAAC,CAAC;EACpF,IAAIgV,OAAO,GAAG,KAAK;EACnB,IAAIL,WAAW,EAAE;IACb,MAAMM,KAAK,GAAGnE,mBAAmB,CAAC,OAAO,EAAGI,OAAO,IAAK;MACpD8D,OAAO,GAAG,IAAI;MACd9V,oBAAoB,CAACgS,OAAO,EAAEA,OAAO,CAAC5R,KAAK,GAAGoV,WAAW,CAAC/Q,KAAK,CAACuN,OAAO,CAAC5R,KAAK,CAACU,MAAM,CAAC,CAAC;IAC1F,CAAC,EAAE;MAAEkV,OAAO,EAAE;IAAK,CAAC,CAAC;IACrB,MAAMC,IAAI,GAAGrE,mBAAmB,CAAC,MAAM,EAAGI,OAAO,IAAK;MAClD8D,OAAO,GAAG,KAAK;MACf9V,oBAAoB,CAACgS,OAAO,EAAE4D,iBAAiB,CAAC5D,OAAO,CAAC5R,KAAK,CAAC,CAAC;IACnE,CAAC,EAAE;MAAE4V,OAAO,EAAE;IAAK,CAAC,CAAC;IACrBH,OAAO,CAACpK,IAAI,CAACsK,KAAK,EAAEE,IAAI,CAAC;EAC7B;EACA,OAAO;IACHJ,OAAO;IACPD,iBAAiB;IACjBM,aAAa,EAAE,CACX,CAAC;MAAEvL,YAAY;MAAEC;IAAK,CAAC,EAAE+D,UAAU,KAAK;MACpCgH,MAAM,GAAGhH,UAAU;MACnB,MAAM;QAAEvO,KAAK;QAAE8G;MAAU,CAAC,GAAGyD,YAAY;MACzC,OAAO;QACHA,YAAY,EAAE;UACVzD,SAAS;UACT9G,KAAK,EAAEwV,iBAAiB,CAACxV,KAAK;QAClC,CAAC;QACDwK;MACJ,CAAC;IACL,CAAC,CACJ;IACDuL,cAAc,EAAE,CACZ,CAAC;MAAE/V,KAAK;MAAE8G;IAAU,CAAC,EAAEqI,mBAAmB,KAAK;MAC3CmG,cAAc,GAAGtV,KAAK;MACtB,MAAMgW,sBAAsB,GAAGhW,KAAK,GAChCoV,WAAW,CAAC/Q,KAAK,CAACrE,KAAK,CAACU,MAAM,EAAEyO,mBAAmB,CAACnP,KAAK,CAACU,MAAM,CAAC,KACjEyO,mBAAmB,CAACnP,KAAK;MAC7B,IAAIuV,MAAM,KAAK,YAAY,IAAIS,sBAAsB,EAAE;QACnD;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;QACoB,OAAO;UAAElP,SAAS;UAAE9G,KAAK,EAAEmP,mBAAmB,CAACnP;QAAM,CAAC;MAC1D;MACA,MAAM+L,QAAQ,GAAG2J,OAAO,IAAI,CAACL,WAAW,GAClCrV,KAAK,GAAGoV,WAAW,CAAC/Q,KAAK,CAACrE,KAAK,CAACU,MAAM,CAAC,GACvCV,KAAK;MACX,IAAI+L,QAAQ,KAAKoD,mBAAmB,CAACnP,KAAK,IACtCuV,MAAM,KAAK,gBAAgB,EAAE;QAC7B,MAAM,CAACzB,UAAU,CAAC,GAAG3E,mBAAmB,CAACrI,SAAS;QAClD,OAAO;UACH9G,KAAK,EAAE+L,QAAQ;UACfjF,SAAS,EAAE,CAACgN,UAAU,EAAEA,UAAU;QACtC,CAAC;MACL;MACA,OAAO;QAAE9T,KAAK,EAAE+L,QAAQ;QAAEjF;MAAU,CAAC;IACzC,CAAC;EAET,CAAC;AACL;AAEA,SAASmP,kCAAkCA,CAAA,EAAG;EAC1C,OAAO,CAAC;IAAE1L;EAAa,CAAC,EAAEgE,UAAU,KAAK;IACrC,MAAM;MAAEvO,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,IAAI,CAACvK,KAAK,IAAIkW,UAAU,CAAClW,KAAK,EAAE8G,SAAS,CAAC,EAAE;MACxC,OAAO;QAAEyD;MAAa,CAAC;IAC3B;IACA,MAAM,CAACxD,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC5B,MAAMqP,MAAM,GAAGnW,KAAK,CAACqE,KAAK,CAAC0C,IAAI,EAAEC,EAAE,CAAC,CAACvG,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC;IAC3D,MAAMsL,QAAQ,GAAG/L,KAAK,CAACqE,KAAK,CAAC,CAAC,EAAE0C,IAAI,CAAC,GAAGoP,MAAM,GAAGnW,KAAK,CAACqE,KAAK,CAAC2C,EAAE,CAAC;IAChE,IAAI,CAACmP,MAAM,CAAC1V,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE;MAC/B,OAAO;QAAE8J;MAAa,CAAC;IAC3B;IACA,IAAIgE,UAAU,KAAK,YAAY,IAAKA,UAAU,KAAK,QAAQ,IAAIxH,IAAI,KAAKC,EAAG,EAAE;MACzE,OAAO;QACHuD,YAAY,EAAE;UAAEzD,SAAS;UAAE9G,KAAK,EAAE+L;QAAS;MAC/C,CAAC;IACL;IACA,OAAO;MACHxB,YAAY,EAAE;QACVzD,SAAS,EAAEyH,UAAU,KAAK,gBAAgB,IAAIA,UAAU,KAAK,QAAQ,GAC/D,CAACxH,IAAI,EAAEA,IAAI,CAAC,GACZ,CAACC,EAAE,EAAEA,EAAE,CAAC;QACdhH,KAAK,EAAE+L;MACX;IACJ,CAAC;EACL,CAAC;AACL;AACA,SAASmK,UAAUA,CAAClW,KAAK,EAAE,CAAC2E,CAAC,EAAEqC,EAAE,CAAC,EAAE;EAChC,OAAOA,EAAE,KAAKhH,KAAK,CAACU,MAAM;AAC9B;AAEA,SAAS0V,2BAA2BA,CAAC;EAAElK,IAAI;EAAEmK,SAAS,GAAG,GAAG;EAAEnW,GAAG;EAAED;AAAK,CAAC,EAAE;EACvE,MAAMkD,gBAAgB,GAAG+I,IAAI,CAACzI,KAAK,CAAC,GAAG,CAAC,CAACqI,IAAI,CAACuK,SAAS,CAAC;EACxD,OAAO9R,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAEvL,uBAAuB,CAAC,EAAE;IAAEyW,IAAI,EAAEnK,KAAK,CAACpF,IAAI,CAAC5D,gBAAgB,CAAC,CAACiJ,GAAG,CAAEC,IAAI,IAAKgK,SAAS,CAACzK,QAAQ,CAACS,IAAI,CAAC,GAAGA,IAAI,GAAG,IAAI,CAAC;IAAEkK,aAAa,EAAE,SAAS;IAAET,aAAa,EAAE,CAC9L9J,sCAAsC,CAAC,CAAC,EACxCiK,kCAAkC,CAAC,CAAC,EACpChG,yBAAyB,CAAC;MACtB9M,gBAAgB;MAChByD,qBAAqB,EAAEyP;IAC3B,CAAC,CAAC,EACFhF,2BAA2B,CAAC;MACxBlO,gBAAgB;MAChByD,qBAAqB,EAAEyP;IAC3B,CAAC,CAAC,CACL;IAAEN,cAAc,EAAE,CACftL,0CAA0C,CAAC;MACvCtH,gBAAgB;MAChBuH,oBAAoB,EAAE2L,SAAS;MAC/B1L,OAAO,EAAG3K,KAAK,KAAM;QAAE6K,WAAW,EAAE,CAAC7K,KAAK;MAAE,CAAC,CAAC;MAC9C4K,OAAO,EAAEA,CAAC,CAACpJ,UAAU,GAAG,EAAE,CAAC,KAAKA;IACpC,CAAC,CAAC,EACFmO,6BAA6B,CAAC;MAC1B1P,GAAG;MACHC,GAAG;MACHiD,gBAAgB;MAChBuH,oBAAoB,EAAE2L;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;AACZ;AAEA,SAASG,gBAAgBA,CAACxW,KAAK,EAAE;EAAEkM,IAAI;EAAEjM,GAAG,GAAGyF,gBAAgB;EAAExF,GAAG,GAAGyF;AAAiB,CAAC,EAAE;EACvF,IAAI3F,KAAK,CAACU,MAAM,GAAGwL,IAAI,CAACxL,MAAM,EAAE;IAC5B,OAAO,IAAI;EACf;EACA,MAAM0D,YAAY,GAAGJ,eAAe,CAAChE,KAAK,EAAEkM,IAAI,CAAC;EACjD,MAAMnH,UAAU,GAAGD,cAAc,CAACV,YAAY,CAAC;EAC/C,OAAOrE,KAAK,CAACgF,UAAU,EAAE9E,GAAG,EAAEC,GAAG,CAAC;AACtC;AAEA,MAAMuW,SAAS,GAAGC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;EAC3C7V,KAAK,EAAE,SAAS;EAChBD,GAAG,EAAE,SAAS;EACdE,IAAI,EAAE;AACV,CAAC,CAAC;AACF,SAAS6V,cAAcA,CAAC5V,IAAI,EAAE;EAC1B,OAAOyV,SAAS,CACXI,aAAa,CAAC7V,IAAI,CAAC,CACnBkK,MAAM,CAAC,CAACC,GAAG,EAAE2L,IAAI,KAAMvS,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAED,GAAG,CAAC,EAAE;IAAE,CAAC2L,IAAI,CAACC,IAAI,GAAGD,IAAI,CAAC9W;EAAM,CAAC,CAAE,EAAE,CAAC,CAAC,CAAC;AACxG;AAEA,SAASgX,oBAAoBA,CAAChW,IAAI,EAAE;EAAEkL,IAAI;EAAEmK,SAAS,GAAG,GAAG;EAAEpW,GAAG,GAAGyF,gBAAgB;EAAExF,GAAG,GAAGyF;AAAkB,CAAC,EAAE;EAC5G,MAAM2B,aAAa,GAAGvH,KAAK,CAACiB,IAAI,EAAEf,GAAG,EAAEC,GAAG,CAAC;EAC3C,MAAMoP,QAAQ,GAAGsH,cAAc,CAACtP,aAAa,CAAC;EAC9C,OAAOnC,YAAY,CAACmK,QAAQ,EAAE;IAC1BlK,QAAQ,EAAE8G,IAAI,CAACzL,UAAU,CAAC,GAAG,EAAE4V,SAAS;EAC5C,CAAC,CAAC;AACN;AAEA,MAAMY,6BAA6B,GAAG,CAClC/Q,WAAW,EACXF,YAAY,EACZC,YAAY,EACZE,UAAU,EACVC,cAAc,CACjB;AAED,SAAS8Q,oCAAoCA,CAAC;EAAE/T,gBAAgB;EAAEW,cAAc;EAAEqT,SAAS;EAAEC,SAAS;EAAElX,GAAG,GAAGyF;AAAkB,CAAC,EAAE;EAC/H,IAAIwD,OAAO,CAACgO,SAAS,CAAC,IAAIhO,OAAO,CAACiO,SAAS,CAAC,EAAE;IAC1C,OAAOrP,QAAQ;EACnB;EACA,OAAO,CAAC;IAAE/H,KAAK;IAAE8G;EAAU,CAAC,KAAK;IAC7B,MAAM+D,WAAW,GAAGjH,oBAAoB,CAAC5D,KAAK,EAAEmD,gBAAgB,EAAEW,cAAc,CAAC;IACjF,IAAI+G,WAAW,CAACnK,MAAM,KAAK,CAAC,IACxBmK,WAAW,CAACwM,IAAI,CAAErW,IAAI,IAAK,CAACwC,oBAAoB,CAACxC,IAAI,EAAEmC,gBAAgB,CAAC,CAAC,EAAE;MAC3E,OAAO;QAAEnD,KAAK;QAAE8G;MAAU,CAAC;IAC/B;IACA,MAAM,CAACwQ,QAAQ,EAAEC,MAAM,CAAC,GAAG1M,WAAW,CAACuB,GAAG,CAAE5K,UAAU,IAAKsD,cAAc,CAACd,eAAe,CAACxC,UAAU,EAAE2B,gBAAgB,CAAC,CAAC,CAAC;IACzH,IAAI,CAACmU,QAAQ,IAAI,CAACC,MAAM,EAAE;MACtB,OAAO;QAAEvX,KAAK;QAAE8G;MAAU,CAAC;IAC/B;IACA,MAAM0Q,gBAAgB,GAAG7W,UAAU,CAAC2W,QAAQ,EAAE/S,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAE+L,SAAS,CAAC,EAAE;MACtF;MACA;MACAtW,GAAG,EAAE,CAACsW,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACtW,GAAG,KAAKsW,SAAS,CAACtW,GAAG,GAAG;IAAE,CAAC,CAAC,CAAC;IACvG,MAAM4W,gBAAgB,GAAG,CAACtO,OAAO,CAACiO,SAAS,CAAC,GACtCzW,UAAU,CAAC2W,QAAQ,EAAE/S,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAEgM,SAAS,CAAC,EAAE;MAAEvW,GAAG,EAAE,CAACuW,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACvW,GAAG,KAAKuW,SAAS,CAACvW,GAAG,GAAG;IAAE,CAAC,CAAC,CAAC,GACtKX,GAAG;IACT,MAAMwX,sBAAsB,GAAG3X,KAAK,CAACwX,MAAM,EAAEC,gBAAgB,EAAEtX,GAAG,CAAC;IACnE,MAAMyX,yBAAyB,GAAGD,sBAAsB,GAAGD,gBAAgB,GACrEA,gBAAgB,GAChBC,sBAAsB;IAC5B,OAAO;MACH5Q,SAAS;MACT9G,KAAK,EAAE6K,WAAW,CAAC,CAAC,CAAC,GACjB/G,cAAc,GACdqB,YAAY,CAACnD,cAAc,CAAC2V,yBAAyB,CAAC,EAAE;QACpDvS,QAAQ,EAAEjC;MACd,CAAC;IACT,CAAC;EACL,CAAC;AACL;AAEA,SAASyU,4BAA4BA,CAAC;EAAEzU,gBAAgB;EAAEW;AAAgB,CAAC,EAAE;EACzE,OAAO,CAAC;IAAE9D,KAAK;IAAE8G;EAAU,CAAC,KAAK;IAC7B,MAAM+D,WAAW,GAAGjH,oBAAoB,CAAC5D,KAAK,EAAEmD,gBAAgB,EAAEW,cAAc,CAAC;IACjF,MAAM+T,mBAAmB,GAAGhN,WAAW,CAACnK,MAAM,KAAK,CAAC,IAChDmK,WAAW,CAACnH,KAAK,CAAE1C,IAAI,IAAKwC,oBAAoB,CAACxC,IAAI,EAAEmC,gBAAgB,CAAC,CAAC;IAC7E,MAAM,CAAC4D,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC5B,MAAMgR,aAAa,GAAG/Q,IAAI,IAAI/G,KAAK,CAACU,MAAM;IAC1C,MAAMqX,gBAAgB,GAAGhR,IAAI,KAAK,CAAC,IAAIC,EAAE,IAAIhH,KAAK,CAACU,MAAM,CAAC,CAAC;IAC3D,IAAI,EAAEoX,aAAa,IAAIC,gBAAgB,CAAC,IAAI,CAACF,mBAAmB,EAAE;MAC9D,OAAO;QAAE7X,KAAK;QAAE8G;MAAU,CAAC;IAC/B;IACA,MAAM,CAACwQ,QAAQ,EAAEC,MAAM,CAAC,GAAG1M,WAAW,CAACuB,GAAG,CAAE5K,UAAU,IAAKsD,cAAc,CAACd,eAAe,CAACxC,UAAU,EAAE2B,gBAAgB,CAAC,CAAC,CAAC;IACzH,OAAO;MACH2D,SAAS;MACT9G,KAAK,EAAEsX,QAAQ,IAAIC,MAAM,IAAID,QAAQ,GAAGC,MAAM,GACxC1M,WAAW,CAACiG,OAAO,CAAC,CAAC,CAAChF,IAAI,CAAChI,cAAc,CAAC,GAC1C9D;IACV,CAAC;EACL,CAAC;AACL;AAEA,SAASgY,gCAAgCA,CAAC;EAAE9L,IAAI;EAAEjM,GAAG;EAAEC,GAAG;EAAEiX,SAAS;EAAEC,SAAS;EAAEa,aAAa,GAAG,GAAG;EAAEnU,cAAc,GAAG,GAAGgC,mBAAmB,GAAGE,YAAY,GAAGF,mBAAmB;AAAI,CAAC,EAAE;EACtL,MAAM3C,gBAAgB,GAAG+I,IAAI,CAACzI,KAAK,CAAC,GAAG,CAAC,CAACqI,IAAI,CAACmM,aAAa,CAAC;EAC5D,MAAMC,QAAQ,GAAG/L,KAAK,CAACpF,IAAI,CAAC5D,gBAAgB,CAAC,CAACiJ,GAAG,CAAEC,IAAI,IAAK4L,aAAa,CAACrM,QAAQ,CAACS,IAAI,CAAC,GAAGA,IAAI,GAAG,IAAI,CAAC;EACvG,OAAO9H,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAEvL,uBAAuB,CAAC,EAAE;IAAEyW,IAAI,EAAE,CAAC,GAAG4B,QAAQ,EAAE,GAAG/L,KAAK,CAACpF,IAAI,CAACjD,cAAc,CAAC,EAAE,GAAGoU,QAAQ,CAAC;IAAE3B,aAAa,EAAE,SAAS;IAAET,aAAa,EAAE,CACrK9J,sCAAsC,CAAC,CAAC,EACxCT,uCAAuC,CAAC;MACpCpI,gBAAgB;MAChBuH,oBAAoB,EAAEuN,aAAa;MACnCzM,qBAAqB,EAAE1H,cAAc;MACrC2H,4BAA4B,EAAEwL;IAClC,CAAC,CAAC,EACFhB,kCAAkC,CAAC,CAAC,EACpChG,yBAAyB,CAAC;MACtB9M,gBAAgB;MAChBW,cAAc;MACd8C,qBAAqB,EAAEqR;IAC3B,CAAC,CAAC,EACF5G,2BAA2B,CAAC;MACxBlO,gBAAgB;MAChBW,cAAc;MACd8C,qBAAqB,EAAEqR;IAC3B,CAAC,CAAC,CACL;IAAElC,cAAc,EAAE,CACftL,0CAA0C,CAAC;MACvCtH,gBAAgB;MAChBuH,oBAAoB,EAAEuN,aAAa;MACnCtN,OAAO,EAAG3K,KAAK,KAAM;QACjB6K,WAAW,EAAEjH,oBAAoB,CAAC5D,KAAK,EAAEmD,gBAAgB,EAAEW,cAAc;MAC7E,CAAC,CAAC;MACF8G,OAAO,EAAEA,CAACG,oBAAoB,EAAEoN,YAAY,KAAKpN,oBAAoB,CAACG,MAAM,CAAC,CAACC,GAAG,EAAE3J,UAAU,EAAE8O,SAAS,KAAKnF,GAAG,GAC5G3J,UAAU,IACT,CAAC8O,SAAS,IAAI6H,YAAY,CAACvM,QAAQ,CAAC9H,cAAc,CAAC,GAC9CA,cAAc,GACd,EAAE,CAAC,EAAE,EAAE;IACrB,CAAC,CAAC,EACF6L,6BAA6B,CAAC;MAC1B1P,GAAG;MACHC,GAAG;MACHiD,gBAAgB;MAChBW,cAAc;MACd4G,oBAAoB,EAAEuN;IAC1B,CAAC,CAAC,EACFf,oCAAoC,CAAC;MACjC/T,gBAAgB;MAChBgU,SAAS;MACTC,SAAS;MACTlX,GAAG;MACH4D;IACJ,CAAC,CAAC,EACF8T,4BAA4B,CAAC;MACzBzU,gBAAgB;MAChBW;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACZ;AAEA,SAASsU,wBAAwBA,CAACC,cAAc,EAAE;EAAEjT,QAAQ;EAAEE,QAAQ;EAAED,iBAAiB,GAAGH;AAAqB,CAAC,EAAE;EAChH,IAAIzD,EAAE;EACN,OAAQ4W,cAAc,CAAC3X,MAAM,IACzB0E,QAAQ,CAAC1E,MAAM,GAAG4E,QAAQ,CAAC5E,MAAM,GAAG2E,iBAAiB,CAAC3E,MAAM,IAC5D,CAAC,CAACe,EAAE,GAAG4W,cAAc,CAAC5U,KAAK,CAAC4B,iBAAiB,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI5D,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,EACjFgC,KAAK,CAAC,IAAI,CAAC,CACXC,KAAK,CAAEC,OAAO,IAAK,CAAC,MAAM,CAACJ,IAAI,CAACI,OAAO,CAAC,CAAC;AACtD;AAEA,SAAS2U,2BAA2BA,CAAC;EAAEpM,IAAI;EAAEuB,oBAAoB,GAAG,CAAC,CAAC;EAAEW,oBAAoB,GAAG,CAAC,CAAC;EAAE+F,IAAI,GAAG;AAAG,CAAC,EAAE;EAC5G,MAAMoE,WAAW,GAAGrM,IAAI,CAACN,QAAQ,CAAC,IAAI,CAAC;EACvC,MAAM4M,4BAA4B,GAAGjU,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAExF,+BAA+B,CAAC,EAAG2S,WAAW,GAAG;IAAEpW,KAAK,EAAE;EAAG,CAAC,GAAG,CAAC,CAAE,CAAC,EAAEsL,oBAAoB,CAAC;EAC/K,MAAMgL,4BAA4B,GAAGlU,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAEvF,+BAA+B,CAAC,EAAG0S,WAAW,GAAG;IAAEpW,KAAK,EAAE;EAAE,CAAC,GAAG,CAAC,CAAE,CAAC,EAAEiM,oBAAoB,CAAC;EAC9K,OAAO;IACHkI,IAAI,EAAErK,wBAAwB,CAACC,IAAI,CAAC;IACpC4J,aAAa,EAAE,CACX9J,sCAAsC,CAAC,CAAC,EACxC1B,8BAA8B,CAAC,CAAC,EAChC2L,kCAAkC,CAAC,CAAC,EACpCnH,0BAA0B,CAAC5C,IAAI,CAAC,EAChCiC,6CAA6C,CAAC;MAC1C7I,QAAQ,EAAE4G,IAAI;MACdkC,oBAAoB,EAAEqK,4BAA4B;MAClDhL,oBAAoB,EAAE+K;IAC1B,CAAC,CAAC,CACL;IACDzC,cAAc,EAAE,CACZ7G,2BAA2B,CAAChD,IAAI,CAAC,EAChC3B,YAAY,IAAKiD,4BAA4B,CAACjD,YAAY,EAAE;MACzD2B,IAAI;MACJuB,oBAAoB,EAAE+K;IAC1B,CAAC,CAAC,CACL;IACD/C,OAAO,EAAE,CACLvB,gCAAgC,CAAC;MAC7BjQ,QAAQ,EAAEiI,IAAI;MACdiI,IAAI;MACJ1G,oBAAoB,EAAE+K;IAC1B,CAAC,CAAC,EACF5E,4BAA4B,CAAC1H,IAAI,CAACjJ,OAAO,CAAC,IAAI,CAAC,CAAC,CACnD;IACDsT,aAAa,EAAE;EACnB,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmC,gBAAgBA,CAACC,UAAU,EAAE;EAAEzM,IAAI;EAAEuB,oBAAoB,GAAG,CAAC;AAAE,CAAC,EAAE;EACvE,IAAIhM,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,MAAMgX,SAAS,GAAGrU,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAExF,+BAA+B,CAAC,EAAE6H,oBAAoB,CAAC;EACzG,MAAMoL,UAAU,GAAGD,SAAS,CAACnW,YAAY,GAAG,CAAC;EAC7C,MAAMqW,UAAU,GAAG,CAACF,SAAS,CAACrW,OAAO,GAAG,CAAC,IAAIsW,UAAU;EACvD,MAAME,QAAQ,GAAG,CAACH,SAAS,CAACvW,OAAO,GAAG,CAAC,IAAIyW,UAAU;EACrD,MAAM9T,UAAU,GAAGkJ,kBAAkB,CAAClB,eAAe,CAAC2L,UAAU,EAAEzM,IAAI,CAAC,CAAC;EACxE,OAAQ7L,MAAM,CAAC,CAACoB,EAAE,GAAGuD,UAAU,CAAC7C,KAAK,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC,GAAGsX,QAAQ,GAClF1Y,MAAM,CAAC,CAACqB,EAAE,GAAGsD,UAAU,CAAC3C,OAAO,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC,GAAGoX,UAAU,GAClFzY,MAAM,CAAC,CAACsB,EAAE,GAAGqD,UAAU,CAACzC,OAAO,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC,GAAGkX,UAAU,GAClFxY,MAAM,CAAC,CAACuB,EAAE,GAAGoD,UAAU,CAACvC,YAAY,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC;AAClF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoX,oBAAoBA,CAACvW,YAAY,EAAE;EAAEyJ,IAAI;EAAEuB,oBAAoB,GAAG,CAAC;AAAE,CAAC,EAAE;EAC7E,MAAMmL,SAAS,GAAGrU,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAExF,+BAA+B,CAAC,EAAE6H,oBAAoB,CAAC;EACzG,MAAMoL,UAAU,GAAGD,SAAS,CAACnW,YAAY,GAAG,CAAC;EAC7C,MAAMqW,UAAU,GAAG,CAACF,SAAS,CAACrW,OAAO,GAAG,CAAC,IAAIsW,UAAU;EACvD,MAAME,QAAQ,GAAG,CAACH,SAAS,CAACvW,OAAO,GAAG,CAAC,IAAIyW,UAAU;EACrD,MAAM3W,KAAK,GAAG/B,IAAI,CAACmQ,KAAK,CAAC9N,YAAY,GAAGsW,QAAQ,CAAC;EACjDtW,YAAY,IAAIN,KAAK,GAAG4W,QAAQ;EAChC,MAAM1W,OAAO,GAAGjC,IAAI,CAACmQ,KAAK,CAAC9N,YAAY,GAAGqW,UAAU,CAAC;EACrDrW,YAAY,IAAIJ,OAAO,GAAGyW,UAAU;EACpC,MAAMvW,OAAO,GAAGnC,IAAI,CAACmQ,KAAK,CAAC9N,YAAY,GAAGoW,UAAU,CAAC;EACrDpW,YAAY,IAAIF,OAAO,GAAGsW,UAAU;EACpC,MAAMI,MAAM,GAAGvM,oBAAoB,CAAC;IAAEvK,KAAK;IAAEE,OAAO;IAAEE,OAAO;IAAEE;EAAa,CAAC,CAAC;EAC9E,OAAOyJ,IAAI,CACNzL,UAAU,CAAC,KAAK,EAAEwY,MAAM,CAAC9W,KAAK,CAAC,CAC/B1B,UAAU,CAAC,KAAK,EAAEwY,MAAM,CAACxW,YAAY,CAAC,CACtChC,UAAU,CAAC,KAAK,EAAEwY,MAAM,CAAC5W,OAAO,CAAC,CACjC5B,UAAU,CAAC,KAAK,EAAEwY,MAAM,CAAC1W,OAAO,CAAC;AAC1C;AAEA,SAAS2W,oBAAoBA,CAAClZ,KAAK,EAAE;EAAEoF,QAAQ;EAAEE,QAAQ;EAAErF,GAAG,GAAGyF,gBAAgB;EAAExF,GAAG,GAAGyF,gBAAgB;EAAEN,iBAAiB,GAAGH;AAAqB,CAAC,EAAE;EACnJ,MAAM,CAACiU,WAAW,GAAG,EAAE,EAAEC,WAAW,GAAG,EAAE,CAAC,GAAGpZ,KAAK,CAACyD,KAAK,CAAC4B,iBAAiB,CAAC;EAC3E,IAAI+T,WAAW,CAAC1Y,MAAM,KAAK4E,QAAQ,CAAC5E,MAAM,EAAE;IACxC,OAAO,IAAI;EACf;EACA,MAAMM,IAAI,GAAGwV,gBAAgB,CAAC2C,WAAW,EAAE;IAAEjN,IAAI,EAAE9G;EAAS,CAAC,CAAC;EAC9D,MAAMiU,IAAI,GAAGX,gBAAgB,CAACU,WAAW,EAAE;IAAElN,IAAI,EAAE5G;EAAS,CAAC,CAAC;EAC9D,IAAI,CAACtE,IAAI,EAAE;IACP,OAAO,IAAI;EACf;EACA,MAAMsY,QAAQ,GAAG,IAAIhZ,IAAI,CAACD,MAAM,CAACW,IAAI,CAAC,GAAGqY,IAAI,CAAC;EAC9C,OAAOtZ,KAAK,CAACuZ,QAAQ,EAAErZ,GAAG,EAAEC,GAAG,CAAC;AACpC;AAEA,MAAMqZ,wBAAwB,GAAG,SAAS;AAC1C,MAAMC,oBAAoB,GAAG,MAAM;AACnC,SAASC,mBAAmBA,CAACH,QAAQ,EAAEnW,gBAAgB,EAAE;EACrD,MAAMuW,eAAe,GAAGvW,gBAAgB,CAAC1C,UAAU,CAAC8Y,wBAAwB,EAAE,EAAE,CAAC,CAAC7Y,MAAM;EACxF,MAAM,CAACM,IAAI,GAAG,EAAE,CAAC,GAAG,IAAIsC,MAAM,CAAC,kBAAkBoW,eAAe,GAAG,CAAC,OAAO,CAAC,CAACnW,IAAI,CAAC+V,QAAQ,CAAC,IAAI,EAAE;EACjG,MAAM,CAACjU,iBAAiB,GAAG,EAAE,CAAC,GAAGmU,oBAAoB,CAACjW,IAAI,CAAC+V,QAAQ,CAACjV,KAAK,CAACrD,IAAI,CAACN,MAAM,CAAC,CAAC,IAAI,EAAE;EAC7F,OAAO,CAACM,IAAI,EAAEsY,QAAQ,CAACjV,KAAK,CAACrD,IAAI,CAACN,MAAM,GAAG2E,iBAAiB,CAAC3E,MAAM,CAAC,CAAC;AACzE;AAEA,SAASiZ,wBAAwBA,CAAC3Y,IAAI,EAAE;EAAEoE,QAAQ;EAAEE,QAAQ;EAAED,iBAAiB,GAAGH,mBAAmB;EAAE+S,aAAa,GAAG,GAAG;EAAEhY,GAAG,GAAGyF,gBAAgB;EAAExF,GAAG,GAAGyF;AAAkB,CAAC,EAAE;EAC3K,MAAM2B,aAAa,GAAGvH,KAAK,CAACiB,IAAI,EAAEf,GAAG,EAAEC,GAAG,CAAC;EAC3C,MAAMsB,UAAU,GAAGwV,oBAAoB,CAAC1P,aAAa,EAAE;IACnD4E,IAAI,EAAE9G,QAAQ;IACdiR,SAAS,EAAE4B,aAAa;IACxBhY,GAAG;IACHC;EACJ,CAAC,CAAC;EACF,MAAM0Z,aAAa,GAAGvZ,MAAM,CAACiH,aAAa,CAAC,GACvCjH,MAAM,CAAC,IAAIC,IAAI,CAACgH,aAAa,CAAChG,WAAW,CAAC,CAAC,EAAEgG,aAAa,CAAClG,QAAQ,CAAC,CAAC,EAAEkG,aAAa,CAACpG,OAAO,CAAC,CAAC,CAAC,CAAC;EACpG,MAAM+L,UAAU,GAAG+L,oBAAoB,CAACY,aAAa,EAAE;IAAE1N,IAAI,EAAE5G;EAAS,CAAC,CAAC;EAC1E,OAAO9D,UAAU,GAAG6D,iBAAiB,GAAG4H,UAAU;AACtD;AAEA,SAAS4M,iCAAiCA,CAAC;EAAE1W,gBAAgB;EAAEmC,QAAQ;EAAErF,GAAG,GAAGyF,gBAAgB;EAAExF,GAAG,GAAGyF,gBAAgB;EAAEN;AAAmB,CAAC,EAAE;EAC3I,OAAO,CAAC;IAAErF,KAAK;IAAE8G;EAAU,CAAC,KAAK;IAC7B,MAAM,CAACtF,UAAU,EAAEyL,UAAU,CAAC,GAAGwM,mBAAmB,CAACzZ,KAAK,EAAEmD,gBAAgB,CAAC;IAC7E,MAAM4B,UAAU,GAAGf,eAAe,CAACxC,UAAU,EAAE2B,gBAAgB,CAAC;IAChE,MAAM6B,UAAU,GAAGgI,eAAe,CAACC,UAAU,EAAE3H,QAAQ,CAAC;IACxD,IAAI,CAAC8S,wBAAwB,CAACpY,KAAK,EAAE;MACjCoF,QAAQ,EAAEjC,gBAAgB;MAC1BmC,QAAQ;MACRD;IACJ,CAAC,CAAC,EAAE;MACA,MAAMwK,SAAS,GAAGR,sBAAsB,CAACtK,UAAU,EAAE5B,gBAAgB,CAAC;MACtE,MAAM;QAAEpC,IAAI;QAAED,KAAK;QAAED;MAAI,CAAC,GAAG2C,oBAAoB,CAAChC,UAAU,EAAE2B,gBAAgB,CAAC,GACzEnB,cAAc,CAACjC,KAAK,CAAC+E,cAAc,CAAC+K,SAAS,CAAC,EAAE5P,GAAG,EAAEC,GAAG,CAAC,CAAC,GAC1D2P,SAAS;MACf,MAAMC,UAAU,GAAG3K,YAAY,CAACZ,MAAM,CAAC6G,MAAM,CAAC;QAAErK,IAAI;QAChDD,KAAK;QACLD;MAAI,CAAC,EAAEmE,UAAU,CAAC,EAAE;QAAEI,QAAQ,EAAEjC,gBAAgB;QAAEkC,iBAAiB;QAAEC;MAAS,CAAC,CAAC;MACpF,MAAMyK,IAAI,GAAG/P,KAAK,CAACqE,KAAK,CAACyL,UAAU,CAACpP,MAAM,CAAC;MAC3C,OAAO;QACHoG,SAAS;QACT9G,KAAK,EAAE8P,UAAU,GAAGC;MACxB,CAAC;IACL;IACA,MAAM/O,IAAI,GAAG8D,cAAc,CAACC,UAAU,EAAEC,UAAU,CAAC;IACnD,MAAMgL,WAAW,GAAGjQ,KAAK,CAACiB,IAAI,EAAEf,GAAG,EAAEC,GAAG,CAAC;IACzC;IACA,MAAM,CAAC4N,0BAA0B,GAAG,EAAE,CAAC,GAAG9N,KAAK,CAAC+B,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE;IACpE,MAAMuJ,cAAc,GAAGnG,YAAY,CAACnD,cAAc,CAACgO,WAAW,CAAC,EAAE;MAC7D5K,QAAQ,EAAEjC,gBAAgB;MAC1BkC,iBAAiB;MACjBC;IACJ,CAAC,CAAC,GAAGwI,0BAA0B;IAC/B,OAAO;MACHhH,SAAS;MACT9G,KAAK,EAAEsL;IACX,CAAC;EACL,CAAC;AACL;AAEA,SAASwO,+BAA+BA,CAAC;EAAE3W,gBAAgB;EAAEyD,qBAAqB;EAAEvB,iBAAiB;EAAEC,QAAQ;EAAEmI;AAAsB,CAAC,EAAE;EACtI,OAAO,CAAC;IAAElD,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,MAAM;MAAExK,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,IAAIC,IAAI,KAAK5D,qBAAqB,EAAE;MAChC,OAAO;QACH2D,YAAY;QACZC,IAAI,EAAE1D,SAAS,CAAC,CAAC,CAAC,KAAK9G,KAAK,CAACU,MAAM,GAAG8J,IAAI,GAAG;MACjD,CAAC;IACL;IACA,MAAMiE,aAAa,GAAGjE,IAAI,CAAC/J,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC;IAChD,IAAI,CAACgO,aAAa,EAAE;MAChB,OAAO;QAAElE,YAAY;QAAEC;MAAK,CAAC;IACjC;IACA,MAAM,CAACzD,IAAI,EAAEyH,KAAK,CAAC,GAAG1H,SAAS;IAC/B,IAAIE,EAAE,GAAGwH,KAAK,GAAGhE,IAAI,CAAC9J,MAAM;IAC5B,MAAMgO,gBAAgB,GAAG1O,KAAK,CAACqE,KAAK,CAAC,CAAC,EAAE0C,IAAI,CAAC,GAAG0H,aAAa,GAAGzO,KAAK,CAACqE,KAAK,CAAC2C,EAAE,CAAC;IAC/E,MAAM,CAACxF,UAAU,EAAEyL,UAAU,CAAC,GAAGwM,mBAAmB,CAAC/K,gBAAgB,EAAEvL,gBAAgB,CAAC;IACxF,IAAImI,cAAc,GAAG,EAAE;IACvB,MAAMyO,oBAAoB,GAAGrL,gBAAgB,CAAC9C,QAAQ,CAACvG,iBAAiB,CAAC;IACzE,MAAM;MAAEuC,mBAAmB;MAAEC;IAAiB,CAAC,GAAGlB,kBAAkB,CAAC;MACjEnF,UAAU;MACVoF,qBAAqB;MACrBzD,gBAAgB;MAChB0D,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE,CAACC,IAAI,EAAEC,EAAE;IACxB,CAAC,CAAC;IACF,IAAIxF,UAAU,IAAI,CAACoG,mBAAmB,EAAE;MACpC,OAAO;QAAE2C,YAAY;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC,CAAC;IACvC;IACAxD,EAAE,GAAGa,gBAAgB,CAAC,CAAC,CAAC;IACxByD,cAAc,IAAI1D,mBAAmB;IACrC,MAAMoS,gBAAgB,GAAGxM,4BAA4B,CAAC;MAAExN,KAAK,EAAEiN,UAAU;MAAEnG,SAAS,EAAE,CAACC,IAAI,EAAEC,EAAE;IAAE,CAAC,EAAE;MAAEkF,IAAI,EAAE5G,QAAQ;MAAEmI;IAAqB,CAAC,CAAC;IAC7IzG,EAAE,GAAGgT,gBAAgB,CAAClT,SAAS,CAAC,CAAC,CAAC;IAClCwE,cAAc,IAAIyO,oBAAoB,GAChC1U,iBAAiB,GAAG2U,gBAAgB,CAACha,KAAK,GAC1Cga,gBAAgB,CAACha,KAAK;IAC5B,MAAMiP,OAAO,GAAG3D,cAAc,CAACjH,KAAK,CAAC0C,IAAI,EAAEC,EAAE,CAAC;IAC9C,OAAO;MACHuD,YAAY,EAAE;QACVzD,SAAS;QACT9G,KAAK,EAAEsL,cAAc,CAACjH,KAAK,CAAC,CAAC,EAAE0C,IAAI,CAAC,GAChCkI,OAAO,CACFxL,KAAK,CAACmD,qBAAqB,CAAC,CAC5BwF,GAAG,CAAEzI,OAAO,IAAK,GAAG,CAAC4N,MAAM,CAAC5N,OAAO,CAACjD,MAAM,CAAC,CAAC,CAC5CoL,IAAI,CAAClF,qBAAqB,CAAC,GAChC0E,cAAc,CAACjH,KAAK,CAAC2C,EAAE;MAC/B,CAAC;MACDwD,IAAI,EAAEyE;IACV,CAAC;EACL,CAAC;AACL;AAEA,SAASgL,+BAA+BA,CAAC;EAAE7U,QAAQ;EAAEE,QAAQ;EAAE2S,aAAa,GAAG,GAAG;EAAEhY,GAAG;EAAEC,GAAG;EAAEmF,iBAAiB,GAAGH,mBAAmB;EAAEgV,QAAQ,GAAG;AAAG,CAAC,EAAE;EACpJ,MAAM3B,WAAW,GAAGjT,QAAQ,CAACsG,QAAQ,CAAC,IAAI,CAAC;EAC3C,MAAMzI,gBAAgB,GAAGiC,QAAQ,CAAC3B,KAAK,CAAC,GAAG,CAAC,CAACqI,IAAI,CAACmM,aAAa,CAAC;EAChE,MAAMxK,oBAAoB,GAAGlJ,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAExF,+BAA+B,CAAC,EAAG2S,WAAW,GAAG;IAAEpW,KAAK,EAAE;EAAG,CAAC,GAAG,CAAC,CAAE,CAAC;EAClI,MAAMiM,oBAAoB,GAAG7J,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAEvF,+BAA+B,CAAC,EAAG0S,WAAW,GAAG;IAAEpW,KAAK,EAAE;EAAE,CAAC,GAAG,CAAC,CAAE,CAAC;EACjI,MAAM8B,QAAQ,GAAG,GAAGd,gBAAgB,GAAGkC,iBAAiB,GAAGC,QAAQ,EAAE;EACrE,OAAOf,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAEvL,uBAAuB,CAAC,EAAE;IAAEyW,IAAI,EAAE,CACjE,GAAGnK,KAAK,CAACpF,IAAI,CAAC5D,gBAAgB,CAAC,CAACiJ,GAAG,CAAEC,IAAI,IAAK4L,aAAa,CAACrM,QAAQ,CAACS,IAAI,CAAC,GAAGA,IAAI,GAAG,IAAI,CAAC,EACzF,GAAGhH,iBAAiB,CAAC5B,KAAK,CAAC,EAAE,CAAC,EAC9B,GAAGwI,wBAAwB,CAAC3G,QAAQ,CAAC,CACxC;IAAEiR,aAAa,EAAE,SAAS;IAAET,aAAa,EAAE,CACxC9J,sCAAsC,CAAC,CAAC,EACxC1B,8BAA8B,CAAC,CAAC,EAChCiB,uCAAuC,CAAC;MACpCpI,gBAAgB;MAChBuH,oBAAoB,EAAEuN,aAAa;MACnCzM,qBAAqB,EAAEnG,iBAAiB;MACxCoG,4BAA4B,EAAEpG,iBAAiB,CAAC5B,KAAK,CAAC,EAAE;IAC5D,CAAC,CAAC,EACFwS,kCAAkC,CAAC,CAAC,EACpCnH,0BAA0B,CAACxJ,QAAQ,CAAC,EACpC2K,yBAAyB,CAAC;MACtB9M,gBAAgB;MAChByD,qBAAqB,EAAEqR,aAAa;MACpC5S;IACJ,CAAC,CAAC,EACF8I,6CAA6C,CAAC;MAC1C7I,QAAQ;MACR8I,oBAAoB;MACpBX,oBAAoB;MACpBY,UAAU,EAAGrG,CAAC,IAAK;QACf,MAAM,CAACxG,UAAU,EAAEyL,UAAU,CAAC,GAAGwM,mBAAmB,CAACzR,CAAC,EAAE7E,gBAAgB,CAAC;QACzE,OAAO;UAAE8J,UAAU;UAAE0B,SAAS,EAAEnN,UAAU,GAAG6D;QAAkB,CAAC;MACpE;IACJ,CAAC,CAAC,EACFyU,+BAA+B,CAAC;MAC5B3W,gBAAgB;MAChByD,qBAAqB,EAAEqR,aAAa;MACpC5S,iBAAiB;MACjBC,QAAQ;MACRmI;IACJ,CAAC,CAAC,CACL;IAAEsI,cAAc,EAAE,CACf7G,2BAA2B,CAAC5J,QAAQ,CAAC,EACrCmF,0CAA0C,CAAC;MACvCtH,gBAAgB;MAChBuH,oBAAoB,EAAEuN,aAAa;MACnCtN,OAAO,EAAG3K,KAAK,IAAK;QAChB,MAAM,CAACwB,UAAU,EAAEyL,UAAU,CAAC,GAAGwM,mBAAmB,CAACzZ,KAAK,EAAEmD,gBAAgB,CAAC;QAC7E,OAAO;UAAE0H,WAAW,EAAE,CAACrJ,UAAU,CAAC;UAAEsJ,QAAQ,EAAEmC;QAAW,CAAC;MAC9D,CAAC;MACDrC,OAAO,EAAEA,CAAC,CAAChD,mBAAmB,CAAC,EAAEuQ,YAAY,KAAKvQ,mBAAmB,IAChEuQ,YAAY,CAACvM,QAAQ,CAACvG,iBAAiB,CAAC,GAAGA,iBAAiB,GAAG,EAAE;IAC1E,CAAC,CAAC,EACFwU,iCAAiC,CAAC;MAC9B5Z,GAAG;MACHC,GAAG;MACHiD,gBAAgB;MAChBmC,QAAQ;MACRD;IACJ,CAAC,CAAC,CACL;IAAEoQ,OAAO,EAAE,CACRvB,gCAAgC,CAAC;MAC7BC,IAAI,EAAE+F,QAAQ;MACdjW,QAAQ;MACRwJ,oBAAoB,EAAE7H;IAC1B,CAAC,CAAC,EACFgO,4BAA4B,CAAC3P,QAAQ,CAAChB,OAAO,CAAC,IAAI,CAAC,CAAC;EACtD,CAAC,CAAC;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASkX,+BAA+BA,CAAC;EAAE1R,MAAM;EAAEC;AAAS,CAAC,EAAE;EAC3D,OAAO,CAAC;IAAE6B,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,MAAM;MAAEzB,UAAU,EAAEqR;IAAU,CAAC,GAAG5R,cAAc,CAACgC,IAAI,EAAE;MACnD/B,MAAM;MACNC;IACJ,CAAC,CAAC;IACF,OAAO;MACH6B,YAAY;MACZC,IAAI,EAAE4P;IACV,CAAC;EACL,CAAC;AACL;AAEA,SAASC,sBAAsBA,CAAC;EAAEC,uBAAuB;EAAEC,gBAAgB;EAAEC,qBAAqB;EAAEva,GAAG;EAAEwa,SAAS;EAAE/R,OAAO;EAAED,MAAM;EAAEiS,aAAa;EAAEC;AAAmB,CAAC,EAAE;EACtK,MAAMC,cAAc,GAAG3a,GAAG,GAAG,CAAC,IAAI,CAACwa,SAAS,EAAE,GAAGC,aAAa,CAAC,CAAC9O,QAAQ,CAACnD,MAAM,CAAC,GAC1E,EAAE,GACFoS,6BAA6B,CAACpS,MAAM,CAAC;EAC3C,MAAMqS,KAAK,GAAG7Y,MAAM,CAACsG,GAAI,IAAI;EAC7B,MAAMwS,aAAa,GAAG9a,GAAG,GAAG,CAAC,GAAG,IAAIwa,SAAS,GAAGC,aAAa,CAACtO,GAAG,CAAEpE,CAAC,IAAK,KAAKA,CAAC,EAAE,CAAC,CAAC8D,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE;EACpG,MAAMkP,WAAW,GAAGL,iBAAiB,GAC/B,IAAIG,KAAK,GAAGzS,YAAY,CAACsS,iBAAiB,CAAC,CAACla,UAAU,CAAC,KAAK,EAAEwB,MAAM,CAACsG,GAAI,IAAI,CAAC,IAAI,GAClF,IAAIuS,KAAK,IAAI;EACnB,MAAMG,aAAa,GAAG5a,MAAM,CAAC6a,QAAQ,CAACV,qBAAqB,CAAC,GACtDA,qBAAqB,GACrB,EAAE;EACR,MAAMW,WAAW,GAAGX,qBAAqB,GAAG,CAAC,GACvC,KAAKnS,YAAY,CAACkS,gBAAgB,CAAC,GAAGD,uBAAuB,CAC1DlO,GAAG,CAAC/D,YAAY,CAAC,CACjByD,IAAI,CAAC,EAAE,CAAC,IAAIgP,KAAK,MAAMG,aAAa,KAAK,GAC5C,EAAE;EACR,MAAMG,eAAe,GAAGP,6BAA6B,CAACnS,OAAO,CAAC;EAC9D,OAAO,IAAIpF,MAAM,CAAC,IAAIsX,cAAc,GAAGG,aAAa,GAAGC,WAAW,GAAGG,WAAW,GAAGC,eAAe,GAAG,CAAC;AAC1G;AACA,SAASP,6BAA6BA,CAACra,GAAG,EAAE;EACxC,OAAOA,GAAG,GACJ,GAAGA,GAAG,CACHiD,KAAK,CAAC,EAAE,CAAC,CACT2I,GAAG,CAAEC,IAAI,IAAK,GAAGhE,YAAY,CAACgE,IAAI,CAAC,GAAG,CAAC,CACvCP,IAAI,CAAC,EAAE,CAAC,EAAE,GACb,EAAE;AACZ;AAEA,SAASuP,kBAAkBA,CAACC,YAAY,EAAEf,gBAAgB,GAAG,GAAG,EAAE;EAC9D,MAAMgB,eAAe,GAAG,CAAC,CAAC,IAAIjY,MAAM,CAAC,SAAS6C,UAAU,KAAKD,WAAW,GAAGF,YAAY,GAAGC,YAAY,GAAGG,cAAc,GAAG,CAAC,CAAC7C,IAAI,CAAC+X,YAAY,CAAC;EAC9I,MAAME,uBAAuB,GAAGnT,YAAY,CAACkS,gBAAgB,CAAC;EAC9D,MAAMkB,cAAc,GAAGH;EACnB;EAAA,CACC7a,UAAU,CAAC,IAAI6C,MAAM,CAAC,GAAGkY,uBAAuB,SAAS,EAAE,GAAG,CAAC,EAAE,EAAE;EACpE;EAAA,CACC/a,UAAU,CAAC,IAAI6C,MAAM,CAAC,QAAQkY,uBAAuB,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CACnEzX,OAAO,CAACwW,gBAAgB,EAAEA,gBAAgB,IAAI,GAAG,CAAC;EACvD,IAAIkB,cAAc,EAAE;IAChB,MAAMC,IAAI,GAAGH,eAAe,GAAGrV,WAAW,GAAG,EAAE;IAC/C,OAAO7F,MAAM,CAAC,GAAGqb,IAAI,GAAGD,cAAc,EAAE,CAAC;EAC7C;EACA,OAAOE,GAAG;AACd;AAEA,SAASC,sBAAsBA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC5C,IAAIzb,MAAM,CAAC0b,KAAK,CAACF,MAAM,CAAC,IAAIA,MAAM,KAAK,IAAI,EAAE;IACzC,OAAO,EAAE;EACb;EACA,MAAM;IAAE5b,GAAG,GAAGI,MAAM,CAAC2b,gBAAgB;IAAE9b,GAAG,GAAGG,MAAM,CAAC4b,gBAAgB;IAAE1B,gBAAgB,GAAG;EAAK,CAAC,GAAGuB,MAAM;EACxG,MAAM9b,KAAK,GAAGD,KAAK,CAAC8b,MAAM,EAAE5b,GAAG,EAAEC,GAAG,CAAC,CAACgc,QAAQ,CAAC,CAAC,CAACnY,OAAO,CAAC,GAAG,EAAEwW,gBAAgB,CAAC;EAC/E,OAAOza,gBAAgB,CAACE,KAAK,EAAEmc,6BAA6B,CAACL,MAAM,CAAC,CAAC;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,yBAAyBA,CAACpc,KAAK,EAAE;EACtC,IAAIyB,EAAE;EACN,MAAM4a,aAAa,GAAGpa,MAAM,CAACjC,KAAK,CAAC;EACnC,MAAM,CAACsc,UAAU,GAAG,EAAE,EAAEC,OAAO,CAAC,GAAGF,aAAa,CAAC5Y,KAAK,CAAC,IAAI,CAAC;EAC5D,IAAI+Y,eAAe,GAAGH,aAAa;EACnC,IAAIE,OAAO,EAAE;IACT,MAAM,GAAGE,cAAc,CAAC,GAAGH,UAAU,CAAC7Y,KAAK,CAAC,GAAG,CAAC;IAChD,MAAMiZ,aAAa,GAAGrc,MAAM,CAACkc,OAAO,CAAC,IAAI,CAAC9a,EAAE,GAAGgb,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC/b,MAAM,MAAM,IAAI,IAAIe,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IACzK+a,eAAe,GAAGxc,KAAK,CAAC2c,OAAO,CAACD,aAAa,CAAC;EAClD;EACA,OAAOF,eAAe;AAC1B;AAEA,SAASI,aAAaA,CAAC5c,KAAK,EAAE;EAAEua,gBAAgB;EAAEE;AAAU,CAAC,EAAE;EAC3D,MAAM,CAACoC,gBAAgB,GAAG,EAAE,EAAE1B,WAAW,GAAG,EAAE,CAAC,GAAGZ,gBAAgB,GAC5Dva,KAAK,CAACyD,KAAK,CAAC8W,gBAAgB,CAAC,GAC7B,CAACva,KAAK,CAAC;EACb,MAAM8c,YAAY,GAAGzU,YAAY,CAACoS,SAAS,CAAC;EAC5C,MAAM,GAAGsC,KAAK,GAAG,EAAE,EAAE/B,WAAW,GAAG,EAAE,CAAC,GAAG,IAAI1X,MAAM,CAAC,YAAYwZ,YAAY,OAAOA,YAAY,QAAQ,CAAC,CAACvZ,IAAI,CAACsZ,gBAAgB,CAAC,IAAI,EAAE;EACrI,OAAO;IAAEE,KAAK;IAAE/B,WAAW;IAAEG;EAAY,CAAC;AAC9C;AAEA,SAAS6B,+BAA+BA,CAAC;EAAEzC,gBAAgB;EAAEI,iBAAiB;EAAEL,uBAAuB,GAAG7U;AAAmC,CAAC,EAAE;EAC5I,OAAO6U,uBAAuB,CAAC5V,MAAM,CAAE2H,IAAI,IAAKA,IAAI,KAAKsO,iBAAiB,IAAItO,IAAI,KAAKkO,gBAAgB,CAAC;AAC5G;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS0C,qCAAqCA,CAAC;EAAE1C,gBAAgB;EAAE2C,qBAAqB;EAAEzU,MAAM;EAAEC;AAAS,CAAC,EAAE;EAC1G,IAAI,CAACwU,qBAAqB,EAAE;IACxB,OAAOnV,QAAQ;EACnB;EACA,OAAO,CAAC;IAAE/H,KAAK;IAAE8G;EAAU,CAAC,KAAK;IAC7B,MAAM;MAAEiC,UAAU;MAAEF,eAAe;MAAEC;IAAiB,CAAC,GAAGN,cAAc,CAACxI,KAAK,EAAE;MAC5EyI,MAAM;MACNC;IACJ,CAAC,CAAC;IACF,IAAIrI,MAAM,CAAC0b,KAAK,CAACV,kBAAkB,CAACtS,UAAU,EAAEwR,gBAAgB,CAAC,CAAC,EAAE;MAChE,OAAO;QAAEva,KAAK;QAAE8G;MAAU,CAAC;IAC/B;IACA,MAAM,CAACkU,WAAW,EAAEG,WAAW,GAAG,EAAE,CAAC,GAAGpS,UAAU,CAACtF,KAAK,CAAC8W,gBAAgB,CAAC;IAC1E,OAAO;MACHva,KAAK,EAAE6I,eAAe,GAClBmS,WAAW,GACXT,gBAAgB,GAChBY,WAAW,CAACxR,MAAM,CAACuT,qBAAqB,EAAE,GAAG,CAAC,GAC9CpU,gBAAgB;MACpBhC;IACJ,CAAC;EACL,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqW,kBAAkBA,CAAC;EAAE1U,MAAM;EAAEC,OAAO;EAAE6R,gBAAgB;EAAEE;AAAW,CAAC,EAAE;EAC3E,OAAO,CAAC;IAAEza,KAAK;IAAE8G;EAAU,CAAC,KAAK;IAC7B,MAAM,CAACgN,UAAU,CAAC,GAAGhN,SAAS;IAC9B,MAAM;MAAEiC,UAAU;MAAEF,eAAe;MAAEC;IAAiB,CAAC,GAAGN,cAAc,CAACxI,KAAK,EAAE;MAC5EyI,MAAM;MACNC;IACJ,CAAC,CAAC;IACF,MAAM;MAAEqU,KAAK;MAAE/B,WAAW;MAAEG;IAAY,CAAC,GAAGyB,aAAa,CAAC7T,UAAU,EAAE;MAClEwR,gBAAgB;MAChBE;IACJ,CAAC,CAAC;IACF,MAAM2C,qBAAqB,GAAG,CAACpC,WAAW,IACtC,CAACG,WAAW,IACZvW,OAAO,CAAC2V,gBAAgB,CAAC,IACzBxR,UAAU,CAAC6C,QAAQ,CAAC2O,gBAAgB,CAAC;IACzC,IAAK,CAACS,WAAW,IACb,CAAC3a,MAAM,CAAC8a,WAAW,CAAC,IACpBrH,UAAU,KAAK,CAACiJ,KAAK,GAAGlU,eAAe,EAAEnI,MAAM,IAC/C0c,qBAAqB,EAAE;MACvB,OAAO;QACHtW,SAAS;QACT9G,KAAK,EAAE6I,eAAe,GAAGkU,KAAK,GAAGjU;MACrC,CAAC;IACL;IACA,OAAO;MAAE9I,KAAK;MAAE8G;IAAU,CAAC;EAC/B,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuW,oCAAoCA,CAAC;EAAE/C,uBAAuB;EAAEC,gBAAgB;EAAEE,SAAS;EAAE/R,OAAO;EAAED,MAAM;EAAEiS;AAAe,CAAC,EAAE;EACrI,IAAI4C,qBAAqB,GAAG,IAAI;EAChC,MAAMC,eAAe,GAAGlD,sBAAsB,CAAC;IAC3CE,gBAAgB;IAChBD,uBAAuB;IACvBI,aAAa;IACbjS,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE,EAAE;IACXiS,iBAAiB,EAAE,EAAE;IACrBH,qBAAqB,EAAExR,QAAQ;IAC/B/I,GAAG,EAAEI,MAAM,CAAC2b,gBAAgB;IAC5BvB;EACJ,CAAC,CAAC;EACF,OAAO,CAAC;IAAElQ,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,IAAI,CAAC8S,qBAAqB,EAAE;MACxB,OAAO;QAAE/S,YAAY;QAAEC;MAAK,CAAC;IACjC;IACA8S,qBAAqB,GAAG,KAAK;IAC7B,MAAM;MAAEtd,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,MAAM,CAACxD,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC5B,MAAM;MAAE+B,eAAe;MAAEE,UAAU;MAAED;IAAiB,CAAC,GAAGN,cAAc,CAACxI,KAAK,EAAE;MAC5EyI,MAAM;MACNC;IACJ,CAAC,CAAC;IACF,MAAM8U,UAAU,GAAG1d,gBAAgB,CAAC;MAChCgH,SAAS,EAAE,CACP1G,IAAI,CAACF,GAAG,CAAC6G,IAAI,GAAG8B,eAAe,CAACnI,MAAM,EAAE,CAAC,CAAC,EAC1CX,KAAK,CAACiH,EAAE,GAAG6B,eAAe,CAACnI,MAAM,EAAE,CAAC,EAAEqI,UAAU,CAACrI,MAAM,CAAC,CAC3D;MACDV,KAAK,EAAE+I;IACX,CAAC,EAAE;MACCuN,IAAI,EAAEiH;IACV,CAAC,CAAC;IACF,MAAM,CAACE,SAAS,EAAEC,OAAO,CAAC,GAAGF,UAAU,CAAC1W,SAAS;IACjD,OAAO;MACHyD,YAAY,EAAE;QACVzD,SAAS,EAAE,CACP2W,SAAS,GAAG5U,eAAe,CAACnI,MAAM,EAClCgd,OAAO,GAAG7U,eAAe,CAACnI,MAAM,CACnC;QACDV,KAAK,EAAE6I,eAAe,GAAG2U,UAAU,CAACxd,KAAK,GAAG8I;MAChD,CAAC;MACD0B;IACJ,CAAC;EACL,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmT,0CAA0CA,CAAC;EAAEpD,gBAAgB;EAAEI,iBAAiB;EAAElS,MAAM;EAAEC;AAAS,CAAC,EAAE;EAC3G,MAAMkV,iBAAiB,GAAI5d,KAAK,IAAK;IACjC,MAAM6d,wBAAwB,GAAGxV,YAAY,CAACsS,iBAAiB,CAAC;IAChE,OAAO3a,KAAK,CACP+D,OAAO;IACZ;IACA,IAAIT,MAAM,CAAC,aAAaua,wBAAwB,SAAS,CAAC,EAAE,IAAI,CAAC,CAC5D9Z,OAAO;IACZ;IACA,IAAIT,MAAM,CAAC,aAAaua,wBAAwB,aAAa,CAAC,EAAE,IAAI,CAAC;EACzE,CAAC;EACD,MAAMC,wBAAwB,GAAGA,CAAC9d,KAAK,EAAEqQ,KAAK,KAAK;IAC/C,MAAM0N,WAAW,GAAG/d,KAAK,CAACqE,KAAK,CAAC,CAAC,EAAEgM,KAAK,CAAC;IACzC,MAAM2N,cAAc,GAAGhe,KAAK,CAACqE,KAAK,CAACgM,KAAK,CAAC,CAACY,UAAU,CAAC,GAAG,CAAC;IACzD,OAAQ8M,WAAW,CAACrd,MAAM,GACtBkd,iBAAiB,CAACG,WAAW,CAAC,CAACrd,MAAM,IACpCsd,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;EAChC,CAAC;EACD,OAAO,CAAC;IAAEhe,KAAK;IAAE8G;EAAU,CAAC,KAAK;IAC7B,MAAM,CAACC,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC5B,MAAM;MAAEiC,UAAU;MAAEF,eAAe;MAAEC;IAAiB,CAAC,GAAGN,cAAc,CAACxI,KAAK,EAAE;MAC5EyI,MAAM;MACNC;IACJ,CAAC,CAAC;IACF,MAAMuV,mBAAmB,GAAGrZ,OAAO,CAAC2V,gBAAgB,CAAC,IAAIxR,UAAU,CAAC6C,QAAQ,CAAC2O,gBAAgB,CAAC;IAC9F,MAAM,CAACS,WAAW,GAAG,EAAE,EAAEG,WAAW,GAAG,EAAE,CAAC,GAAGZ,gBAAgB,GACvDxR,UAAU,CAACtF,KAAK,CAAC8W,gBAAgB,CAAC,GAClC,CAACxR,UAAU,CAAC;IAClB,MAAMmV,sBAAsB,GAAGN,iBAAiB,CAAC5C,WAAW,CAAC;IAC7D,IAAIA,WAAW,KAAKkD,sBAAsB,EAAE;MACxC,OAAO;QAAEle,KAAK;QAAE8G;MAAU,CAAC;IAC/B;IACA,MAAMkH,OAAO,GAAGjH,IAAI,GAAG+W,wBAAwB,CAAC9d,KAAK,EAAE+G,IAAI,CAAC;IAC5D,MAAMkH,KAAK,GAAGjH,EAAE,GAAG8W,wBAAwB,CAAC9d,KAAK,EAAEgH,EAAE,CAAC;IACtD,OAAO;MACHhH,KAAK,EAAE6I,eAAe,GAClBqV,sBAAsB,IACrBD,mBAAmB,GAAG1D,gBAAgB,GAAG,EAAE,CAAC,GAC7CY,WAAW,GACXrS,gBAAgB;MACpBhC,SAAS,EAAE,CAAC1G,IAAI,CAACF,GAAG,CAAC8N,OAAO,EAAE,CAAC,CAAC,EAAE5N,IAAI,CAACF,GAAG,CAAC+N,KAAK,EAAE,CAAC,CAAC;IACxD,CAAC;EACL,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,SAASkQ,yBAAyBA,CAAC;EAAEle,GAAG;EAAEC,GAAG;EAAEqa,gBAAgB;EAAEE;AAAW,CAAC,EAAE;EAC3E,OAAO,CAAC;IAAEza,KAAK;IAAE8G;EAAU,CAAC,KAAK;IAC7B,MAAMsX,YAAY,GAAG/C,kBAAkB,CAACrb,KAAK,EAAEua,gBAAgB,CAAC;IAChE,MAAM8D,YAAY;IAClB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQD,YAAY,GAAG,CAAC,GAAGhe,IAAI,CAACH,GAAG,CAACme,YAAY,EAAEle,GAAG,CAAC,GAAGE,IAAI,CAACF,GAAG,CAACke,YAAY,EAAEne,GAAG,CAAC;IAC5E,IAAIme,YAAY,IAAIC,YAAY,KAAKD,YAAY,EAAE;MAC/C,MAAMrS,QAAQ,GAAG,GAAGsS,YAAY,EAAE,CAC7Bta,OAAO,CAAC,GAAG,EAAEwW,gBAAgB,CAAC,CAC9BxW,OAAO,CAACmC,WAAW,EAAEuU,SAAS,CAAC;MACpC,OAAO;QACHza,KAAK,EAAE+L,QAAQ;QACfjF,SAAS,EAAE,CAACiF,QAAQ,CAACrL,MAAM,EAAEqL,QAAQ,CAACrL,MAAM;MAChD,CAAC;IACL;IACA,OAAO;MACHV,KAAK;MACL8G;IACJ,CAAC;EACL,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwX,2CAA2CA,CAAC;EAAE/D,gBAAgB;EAAEI,iBAAiB;EAAEuC;AAAuB,CAAC,EAAE;EAClH,OAAO,CAAC;IAAE3S,YAAY;IAAEC;EAAK,CAAC,EAAE+D,UAAU,KAAK;IAC3C,MAAM;MAAEvO,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,MAAM,CAACxD,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC5B,MAAMyX,kBAAkB,GAAGve,KAAK,CAACqE,KAAK,CAAC0C,IAAI,EAAEC,EAAE,CAAC;IAChD,MAAMwX,sBAAsB,GAAGtB,qBAAqB,GAC9C,CAAC3C,gBAAgB,EAAEI,iBAAiB,CAAC,GACrC,CAACA,iBAAiB,CAAC;IACzB,MAAM8D,6BAA6B,GAAG7Z,OAAO,CAACsY,qBAAqB,CAAC,IAChEnW,IAAI,GAAG/G,KAAK,CAACiD,OAAO,CAACsX,gBAAgB,CAAC,IACtC3V,OAAO,CAAC2Z,kBAAkB,CAACxc,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC/C,IAAKwM,UAAU,KAAK,gBAAgB,IAAIA,UAAU,KAAK,eAAe,IACjE,CAACiQ,sBAAsB,CAAC5S,QAAQ,CAAC2S,kBAAkB,CAAC,IACjD,CAACE,6BAA8B,EAAE;MACrC,OAAO;QACHlU,YAAY;QACZC;MACJ,CAAC;IACL;IACA,OAAO;MACHD,YAAY,EAAE;QACVvK,KAAK;QACL8G,SAAS,EAAEyH,UAAU,KAAK,eAAe,GAAG,CAACvH,EAAE,EAAEA,EAAE,CAAC,GAAG,CAACD,IAAI,EAAEA,IAAI;MACtE,CAAC;MACDyD;IACJ,CAAC;EACL,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,SAASkU,qCAAqCA,CAAC;EAAEnE,gBAAgB;EAAEC,qBAAqB;EAAE/R,MAAM;EAAEC;AAAS,CAAC,EAAE;EAC1G,MAAMiW,yBAAyB,GAAG,IAAIrb,MAAM,CAAC,QAAQ+E,YAAY,CAACkS,gBAAgB,CAAC,EAAE,CAAC;EACtF,OAAO,CAAC;IAAEhQ,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,MAAM;MAAExK,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,MAAM;MAAExB,UAAU;MAAEF;IAAgB,CAAC,GAAGL,cAAc,CAACxI,KAAK,EAAE;MAC1DyI,MAAM;MACNC;IACJ,CAAC,CAAC;IACF,MAAM,CAAC3B,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC5B,MAAM2W,SAAS,GAAG1d,KAAK,CAACgH,IAAI,GAAG8B,eAAe,CAACnI,MAAM,EAAE,CAAC,EAAEqI,UAAU,CAACrI,MAAM,CAAC;IAC5E,MAAMgd,OAAO,GAAG3d,KAAK,CAACiH,EAAE,GAAG6B,eAAe,CAACnI,MAAM,EAAE,CAAC,EAAEqI,UAAU,CAACrI,MAAM,CAAC;IACxE,IAAI8Z,qBAAqB,IAAI,CAAC,IAC1BzR,UAAU,CAAC1E,KAAK,CAAC,CAAC,EAAEoZ,SAAS,CAAC,CAAC7R,QAAQ,CAAC2O,gBAAgB,CAAC,IACzDxR,UAAU,CAAC1E,KAAK,CAACqZ,OAAO,CAAC,CAAC9R,QAAQ,CAAC2O,gBAAgB,CAAC,IACpD,CAAC/P,IAAI,CAACzI,KAAK,CAAC4c,yBAAyB,CAAC,EAAE;MACxC,OAAO;QAAEpU,YAAY;QAAEC;MAAK,CAAC;IACjC;IACA,MAAMoU,kBAAkB,GAAG,KAAK,CAACrb,IAAI,CAACwF,UAAU,CAAC1E,KAAK,CAAC,CAAC,EAAEoZ,SAAS,CAAC,CAAC;IACrE,OAAO;MACHlT,YAAY;MACZC,IAAI,EAAEoU,kBAAkB,GAAGpU,IAAI,GAAG,IAAIA,IAAI;IAC9C,CAAC;EACL,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASqU,kCAAkCA,CAAC;EAAEC,cAAc;EAAEC,gBAAgB;EAAEtW,MAAM;EAAEC;AAAS,CAAC,EAAE;EAChG,MAAMsW,sBAAsB,GAAG,IAAI1b,MAAM,CAAC,IAAIyb,gBAAgB,CAACjT,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC;EACjF,OAAO,CAAC;IAAEvB,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,MAAM;MAAExK,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,MAAM;MAAExB,UAAU;MAAED,gBAAgB;MAAED;IAAgB,CAAC,GAAGL,cAAc,CAACxI,KAAK,EAAE;MAC5EyI,MAAM;MACNC;IACJ,CAAC,CAAC;IACF,OAAO;MACH6B,YAAY,EAAE;QACVzD,SAAS;QACT9G,KAAK,EAAE6I,eAAe,GAClBE,UAAU,CAAChF,OAAO,CAACib,sBAAsB,EAAEF,cAAc,CAAC,GAC1DhW;MACR,CAAC;MACD0B,IAAI,EAAEA,IAAI,CAACzG,OAAO,CAACib,sBAAsB,EAAEF,cAAc;IAC7D,CAAC;EACL,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASG,0CAA0CA,CAAC;EAAE1E,gBAAgB;EAAE9R,MAAM;EAAEC;AAAS,CAAC,EAAE;EACxF,IAAI,CAAC6R,gBAAgB,EAAE;IACnB,OAAOxS,QAAQ;EACnB;EACA,OAAO,CAAC;IAAEwC,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,MAAM;MAAExK,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,MAAM,CAACxD,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC5B,MAAM;MAAEiC;IAAW,CAAC,GAAGP,cAAc,CAACxI,KAAK,EAAE;MAAEyI,MAAM;MAAEC;IAAQ,CAAC,CAAC;IACjE,OAAO;MACH6B,YAAY;MACZC,IAAI,EAAE,CAACzB,UAAU,CAAC6C,QAAQ,CAAC2O,gBAAgB,CAAC,IACxCva,KAAK,CAACqE,KAAK,CAAC0C,IAAI,EAAEC,EAAE,GAAG,CAAC,CAAC,CAAC4E,QAAQ,CAAC2O,gBAAgB,CAAC,GAClD/P,IAAI,GACJA,IAAI,CAAC/J,UAAU,CAAC,IAAI6C,MAAM,CAAC+E,YAAY,CAACkS,gBAAgB,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE;IAC9E,CAAC;EACL,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,SAAS2E,oCAAoCA,CAAC;EAAEvE,iBAAiB;EAAEJ,gBAAgB;EAAE9R,MAAM;EAAEC,OAAO;EAAE+R;AAAW,CAAC,EAAE;EAChH,IAAI,CAACE,iBAAiB,EAAE;IACpB,OAAO5S,QAAQ;EACnB;EACA,MAAMoX,WAAW,GAAGA,CAAC,GAAGC,KAAK,KAAKA,KAAK,CAAC1b,KAAK,CAAEsE,CAAC,IAAK,IAAI,CAACM,IAAI,CAACN,CAAC,CAAC,CAAC;EAClE,OAAO,CAAC;IAAEhI,KAAK;IAAE8G;EAAU,CAAC,KAAK;IAC7B,MAAM,CAACuY,WAAW,EAAEC,SAAS,CAAC,GAAGxY,SAAS;IAC1C,IAAI,CAACC,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC1B,MAAM;MAAEiC,UAAU;MAAED,gBAAgB;MAAED;IAAgB,CAAC,GAAGL,cAAc,CAACxI,KAAK,EAAE;MAC5EyI,MAAM;MACNC;IACJ,CAAC,CAAC;IACF,MAAM;MAAEqU,KAAK;MAAE/B,WAAW;MAAEG;IAAY,CAAC,GAAGyB,aAAa,CAAC7T,UAAU,EAAE;MAClEwR,gBAAgB;MAChBE;IACJ,CAAC,CAAC;IACF,MAAMwD,mBAAmB,GAAG1D,gBAAgB,IAAIxR,UAAU,CAAC6C,QAAQ,CAAC2O,gBAAgB,CAAC;IACrF,MAAMgF,YAAY,GAAGxW,UAAU,CAACrI,MAAM,GAClC,CAACqc,KAAK,GACF/B,WAAW,IACViD,mBAAmB,GAAG1D,gBAAgB,GAAGY,WAAW,GAAG,EAAE,CAAC,EAAEza,MAAM;IAC3E,IAAI6e,YAAY,GAAG,CAAC,IAAIF,WAAW,IAAIA,WAAW,IAAIE,YAAY,EAAE;MAChExY,IAAI,IAAIwY,YAAY;IACxB;IACA,IAAIA,YAAY,GAAG,CAAC,IAAID,SAAS,IAAIA,SAAS,IAAIC,YAAY,EAAE;MAC5DvY,EAAE,IAAIuY,YAAY;IACtB;IACA,MAAMC,oBAAoB,GAAGrT,KAAK,CAACpF,IAAI,CAACiU,WAAW,CAAC,CAACyE,WAAW,CAAC,CAACC,kBAAkB,EAAErT,IAAI,EAAElF,CAAC,KAAK;MAC9F,MAAMwY,0BAA0B,GAAG,CAACxY,CAAC,IAAIkF,IAAI,KAAKsO,iBAAiB;MACnE,MAAMiF,sBAAsB,GAAG,CAACD,0BAA0B,IACtD/a,OAAO,CAAC8a,kBAAkB,CAAChf,MAAM,CAAC,IAClC,CAACgf,kBAAkB,CAAChf,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;MAC7C,MAAMmf,WAAW,GAAGxT,IAAI,KAAKsO,iBAAiB,IAAIwE,WAAW,CAAC9S,IAAI,EAAEsO,iBAAiB,CAAC;MACtF,IAAIiF,sBAAsB,IAAIC,WAAW,EAAE;QACvC,OAAOlF,iBAAiB,GAAG+E,kBAAkB;MACjD;MACA,IAAI,CAACE,sBAAsB,IAAIC,WAAW,EAAE;QACxC,IAAI1Y,CAAC,IAAIA,CAAC,IAAIkY,WAAW,EAAE;UACvBtY,IAAI,EAAE;QACV;QACA,IAAII,CAAC,IAAIA,CAAC,IAAImY,SAAS,EAAE;UACrBtY,EAAE,EAAE;QACR;QACA,OAAO0Y,kBAAkB;MAC7B;MACA,IAAI,CAACE,sBAAsB,EAAE;QACzB,OAAOvT,IAAI,GAAGqT,kBAAkB;MACpC;MACA,IAAIvY,CAAC,GAAGkY,WAAW,EAAE;QACjBtY,IAAI,EAAE;MACV;MACA,IAAII,CAAC,GAAGmY,SAAS,EAAE;QACftY,EAAE,EAAE;MACR;MACA,OAAOqF,IAAI,GAAGsO,iBAAiB,GAAG+E,kBAAkB;IACxD,CAAC,EAAE,EAAE,CAAC;IACN,OAAO;MACH1f,KAAK,EAAE6I,eAAe,GAClBkU,KAAK,GACLyC,oBAAoB,IACnBvB,mBAAmB,GAAG1D,gBAAgB,GAAG,EAAE,CAAC,GAC7CY,WAAW,GACXrS,gBAAgB;MACpBhC,SAAS,EAAE,CAACC,IAAI,EAAEC,EAAE;IACxB,CAAC;EACL,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,SAAS8Y,+BAA+BA,CAAC;EAAEtF,qBAAqB;EAAED,gBAAgB;EAAE9R,MAAM;EAAEC;AAAS,CAAC,EAAE;EACpG,IAAI8R,qBAAqB,GAAG,CAAC,IACzB,CAACD,gBAAgB,CAAC;EAAA,EACpB;IACE,OAAOxS,QAAQ;EACnB;EACA,MAAMgY,iBAAiB,GAAG,IAAIzc,MAAM,CAAC,GAAG+E,YAAY,CAACkS,gBAAgB,CAAC,KAAK,EAAE,GAAG,CAAC;EACjF,OAAO,CAAC;IAAEhQ,YAAY;IAAEC;EAAK,CAAC,KAAK;IAC/B,MAAM;MAAExK,KAAK;MAAE8G;IAAU,CAAC,GAAGyD,YAAY;IACzC,MAAM;MAAExB,UAAU;MAAEF,eAAe;MAAEC;IAAiB,CAAC,GAAGN,cAAc,CAACxI,KAAK,EAAE;MAC5EyI,MAAM;MACNC;IACJ,CAAC,CAAC;IACF,MAAM,CAAC3B,IAAI,EAAEC,EAAE,CAAC,GAAGF,SAAS;IAC5B,MAAMiF,QAAQ,GAAGlD,eAAe,GAC5BE,UAAU,CAAChF,OAAO,CAACgc,iBAAiB,EAAE,EAAE,CAAC,GACzCjX,gBAAgB;IACpB,OAAO;MACHyB,YAAY,EAAE;QACVzD,SAAS,EAAE,CACP1G,IAAI,CAACH,GAAG,CAAC8G,IAAI,EAAEgF,QAAQ,CAACrL,MAAM,CAAC,EAC/BN,IAAI,CAACH,GAAG,CAAC+G,EAAE,EAAE+E,QAAQ,CAACrL,MAAM,CAAC,CAChC;QACDV,KAAK,EAAE+L;MACX,CAAC;MACDvB,IAAI,EAAEA,IAAI,CAACzG,OAAO,CAACgc,iBAAiB,EAAE,EAAE;IAC5C,CAAC;EACL,CAAC;AACL;AAEA,MAAMC,eAAe,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA,SAASC,mCAAmCA,CAAC;EAAE1F,gBAAgB;EAAEI,iBAAiB;EAAElS,MAAM;EAAEC;AAAS,CAAC,EAAE;EACpG,MAAMwX,yBAAyB,GAAGvC,0CAA0C,CAAC;IACzEpD,gBAAgB;IAChBI,iBAAiB;IACjBlS,MAAM;IACNC;EACJ,CAAC,CAAC;EACF,OAAO8I,mBAAmB,CAAC,MAAM,EAAGI,OAAO,IAAK;IAC5C,MAAM7F,QAAQ,GAAGmU,yBAAyB,CAAC;MACvClgB,KAAK,EAAE4R,OAAO,CAAC5R,KAAK;MACpB8G,SAAS,EAAEkZ;IACf,CAAC,EAAE;MAAEhgB,KAAK,EAAE,EAAE;MAAE8G,SAAS,EAAEkZ;IAAgB,CAAC,CAAC,CAAChgB,KAAK;IACnDJ,oBAAoB,CAACgS,OAAO,EAAE7F,QAAQ,CAAC;EAC3C,CAAC,EAAE;IAAE6J,OAAO,EAAE;EAAK,CAAC,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA,SAASuK,kBAAkBA,CAAC;EAAElgB,GAAG;EAAEC,GAAG;EAAEqa;AAAkB,CAAC,EAAE;EACzD,OAAO/I,mBAAmB,CAAC,MAAM,EAAE,CAACI,OAAO,EAAEO,OAAO,KAAK;IACrD,MAAMiM,YAAY,GAAG/C,kBAAkB,CAACzJ,OAAO,CAAC5R,KAAK,EAAEua,gBAAgB,CAAC;IACxE,MAAM6F,aAAa,GAAGrgB,KAAK,CAACqe,YAAY,EAAEne,GAAG,EAAEC,GAAG,CAAC;IACnD,IAAI,CAACG,MAAM,CAAC0b,KAAK,CAACqC,YAAY,CAAC,IAAIA,YAAY,KAAKgC,aAAa,EAAE;MAC/DxgB,oBAAoB,CAACgS,OAAO,EAAE9R,gBAAgB,CAACsc,yBAAyB,CAACgE,aAAa,CAAC,EAAEjO,OAAO,CAAC,CAAC;IACtG;EACJ,CAAC,EAAE;IAAEyD,OAAO,EAAE;EAAK,CAAC,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASyK,2BAA2BA,CAAC;EAAE9F,gBAAgB;EAAE9R,MAAM;EAAEC;AAAS,CAAC,EAAE;EACzE,IAAI,CAAC6R,gBAAgB,EAAE;IACnB,OAAOtS,IAAI;EACf;EACA,OAAOuJ,mBAAmB,CAAC,MAAM,EAAGI,OAAO,IAAK;IAC5C,MAAM;MAAE7I,UAAU;MAAED,gBAAgB;MAAED;IAAgB,CAAC,GAAGL,cAAc,CAACoJ,OAAO,CAAC5R,KAAK,EAAE;MAAEyI,MAAM;MAAEC;IAAQ,CAAC,CAAC;IAC5G,MAAMqD,QAAQ,GAAGlD,eAAe,GAC5BE,UAAU,CAAChF,OAAO,CAAC,IAAIT,MAAM,CAAC,WAAW+E,YAAY,CAACkS,gBAAgB,CAAC,EAAE,CAAC,EAAE,MAAMA,gBAAgB,EAAE,CAAC,GACrGzR,gBAAgB;IACpBlJ,oBAAoB,CAACgS,OAAO,EAAE7F,QAAQ,CAAC;EAC3C,CAAC,EAAE;IAAE6J,OAAO,EAAE;EAAK,CAAC,CAAC;AACzB;AAEA,SAASuG,6BAA6BA,CAAC;EAAEjc,GAAG,GAAGG,MAAM,CAAC4b,gBAAgB;EAAEhc,GAAG,GAAGI,MAAM,CAAC2b,gBAAgB;EAAEsE,SAAS,GAAG,CAAC;EAAE3F,iBAAiB,GAAG7U,mBAAmB;EAAEyU,gBAAgB,GAAG,GAAG;EAAED,uBAAuB;EAAEiG,kBAAkB,GAAG,KAAK;EAAE9X,MAAM,EAAE+X,YAAY,GAAG,EAAE;EAAE9X,OAAO,GAAG,EAAE;EAAE+R,SAAS,GAAGtU,UAAU;EAAEqU,qBAAqB,GAAG8F,SAAS;EAAEpD,qBAAqB,GAAGqD,kBAAkB,GAAG/F,qBAAqB,GAAG;AAAG,CAAC,GAAG,CAAC,CAAC,EAAE;EAC5Z,MAAME,aAAa,GAAG,CAClBxU,WAAW,EACXF,YAAY,EACZC,YAAY,EACZG,cAAc,EACdD,UAAU,CACb,CAACzB,MAAM,CAAE2H,IAAI,IAAKA,IAAI,KAAKsO,iBAAiB,IAAItO,IAAI,KAAKkO,gBAAgB,IAAIlO,IAAI,KAAKoO,SAAS,CAAC;EACjG,MAAMgG,gCAAgC,GAAGzD,+BAA+B,CAAC;IACrEzC,gBAAgB;IAChBI,iBAAiB;IACjBL;EACJ,CAAC,CAAC;EACF,MAAM7R,MAAM,GAAG+X,YAAY,CAAC3W,QAAQ,CAAC0Q,gBAAgB,CAAC,IAAIC,qBAAqB,GAAG,CAAC,GAC7E,GAAGgG,YAAY,GAAGza,qBAAqB,EAAE,GACzCya,YAAY;EAClB,MAAME,8BAA8B,GAAGrD,oCAAoC,CAAC;IACxE9C,gBAAgB;IAChBD,uBAAuB,EAAEmG,gCAAgC;IACzD/F,aAAa;IACbjS,MAAM;IACNC,OAAO;IACP+R;EACJ,CAAC,CAAC;EACFF,gBAAgB,GACZC,qBAAqB,IAAI,CAAC,IAAID,gBAAgB,KAAKI,iBAAiB,GAC9D,EAAE,GACFJ,gBAAgB;EAC1B,OAAOhW,MAAM,CAAC6G,MAAM,CAAC7G,MAAM,CAAC6G,MAAM,CAAC,CAAC,CAAC,EAAEvL,uBAAuB,CAAC,EAAE;IAAEyW,IAAI,EAAE+D,sBAAsB,CAAC;MACxFE,gBAAgB;MAChBC,qBAAqB;MACrBva,GAAG;MACHwa,SAAS;MACT/R,OAAO;MACPD,MAAM;MACNiS,aAAa;MACbC,iBAAiB;MACjBL,uBAAuB,EAAEmG;IAC7B,CAAC,CAAC;IAAE3K,aAAa,EAAE,CACf9J,sCAAsC,CAAC,CAAC,EACxC0U,8BAA8B,EAC9BvG,+BAA+B,CAAC;MAAE1R,MAAM;MAAEC;IAAQ,CAAC,CAAC,EACpDmW,kCAAkC,CAAC;MAC/BC,cAAc,EAAErE,SAAS;MACzBsE,gBAAgB,EAAErE,aAAa;MAC/BjS,MAAM;MACNC;IACJ,CAAC,CAAC,EACFmW,kCAAkC,CAAC;MAC/BC,cAAc,EAAEvE,gBAAgB;MAChCwE,gBAAgB,EAAE0B,gCAAgC;MAClDhY,MAAM;MACNC;IACJ,CAAC,CAAC,EACFgW,qCAAqC,CAAC;MAClCnE,gBAAgB;MAChBC,qBAAqB;MACrB/R,MAAM;MACNC;IACJ,CAAC,CAAC,EACF4V,2CAA2C,CAAC;MACxC/D,gBAAgB;MAChB2C,qBAAqB;MACrBvC;IACJ,CAAC,CAAC,EACFmF,+BAA+B,CAAC;MAC5BtF,qBAAqB;MACrBD,gBAAgB;MAChB9R,MAAM;MACNC;IACJ,CAAC,CAAC,EACFuW,0CAA0C,CAAC;MACvC1E,gBAAgB;MAChB9R,MAAM;MACNC;IACJ,CAAC,CAAC,CACL;IAAEqN,cAAc,EAAE,CACfoI,yBAAyB,CAAC;MAAE5D,gBAAgB;MAAEta,GAAG;MAAEC,GAAG;MAAEua;IAAU,CAAC,CAAC,EACpEzJ,mCAAmC,CAACvI,MAAM,CAAC,EAC3CgI,oCAAoC,CAAC/H,OAAO,CAAC,EAC7CwW,oCAAoC,CAAC;MACjC3E,gBAAgB;MAChBI,iBAAiB;MACjBlS,MAAM;MACNC,OAAO;MACP+R;IACJ,CAAC,CAAC,EACFwC,qCAAqC,CAAC;MAClC1C,gBAAgB;MAChB9R,MAAM;MACNC,OAAO;MACPwU,qBAAqB,EAAE9c,IAAI,CAACH,GAAG,CAACid,qBAAqB,EAAE1C,qBAAqB;IAChF,CAAC,CAAC,EACF2C,kBAAkB,CAAC;MACf1U,MAAM;MACNC,OAAO;MACP6R,gBAAgB;MAChBE;IACJ,CAAC,CAAC,CACL;IAAEhF,OAAO,EAAE,CACRwK,mCAAmC,CAAC;MAChC1F,gBAAgB;MAChBI,iBAAiB;MACjBlS,MAAM;MACNC;IACJ,CAAC,CAAC,EACF2X,2BAA2B,CAAC;MACxB9F,gBAAgB;MAChB9R,MAAM;MACNC;IACJ,CAAC,CAAC,EACFyX,kBAAkB,CAAC;MAAElgB,GAAG;MAAEC,GAAG;MAAEqa;IAAiB,CAAC,CAAC,CACrD;IAAEhE,aAAa,EAAE2G,qBAAqB,GAAG,CAAC,GACrC,CAAC;MAAEld,KAAK;MAAE8G,SAAS,EAAE,CAACC,IAAI;IAAE,CAAC,KAAKA,IAAI,IAAI/G,KAAK,CAACiD,OAAO,CAACsX,gBAAgB,CAAC,GAAG,OAAO,GAAG,SAAS,GAC/F;EAAQ,CAAC,CAAC;AACxB;AAEA,SAAStI,uBAAuB,EAAEW,iBAAiB,EAAEwD,2BAA2B,EAAE4B,gCAAgC,EAAEiC,+BAA+B,EAAEzI,mBAAmB,EAAE2K,6BAA6B,EAAE3F,gBAAgB,EAAE0C,oBAAoB,EAAEmC,kBAAkB,EAAE3C,gBAAgB,EAAEjI,oCAAoC,EAAEO,mCAAmC,EAAEqC,kBAAkB,EAAEM,yBAAyB,EAAEzB,6BAA6B,EAAE8E,oBAAoB,EAAE2C,wBAAwB,EAAEiC,sBAAsB,EAAE5C,oBAAoB,EAAEV,2BAA2B,EAAEnD,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}