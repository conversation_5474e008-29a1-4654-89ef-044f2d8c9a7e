{"ast": null, "code": "export class ApplicationValidators {\n  static urlValidator(control) {\n    const value = control.value;\n    if (value.startsWith('http://') || value.startsWith('https://')) {\n      return null;\n    }\n    return {\n      invalidUrl: true\n    };\n  }\n  // ========================================\n  // VALIDATORS PARA CLIENTES\n  // ========================================\n  static emailDomainValidator(control) {\n    if (!control.value) return null;\n    const email = control.value.toLowerCase();\n    const allowedDomains = ['gmail.com', 'hotmail.com', 'outlook.com', 'yahoo.com'];\n    const domain = email.split('@')[1];\n    if (domain && !allowedDomains.includes(domain)) {\n      return {\n        invalidDomain: true\n      };\n    }\n    return null;\n  }\n  static phoneValidator(control) {\n    if (!control.value) return null;\n    const phone = control.value.replace(/\\D/g, '');\n    if (phone.length !== 11) {\n      return {\n        invalidPhone: true\n      };\n    }\n    return null;\n  }\n  static ageValidator(control) {\n    if (!control.value) return null;\n    const birthDate = new Date(control.value);\n    const today = new Date();\n    const age = today.getFullYear() - birthDate.getFullYear();\n    const monthDiff = today.getMonth() - birthDate.getMonth();\n    const actualAge = monthDiff < 0 || monthDiff === 0 && today.getDate() < birthDate.getDate() ? age - 1 : age;\n    if (actualAge < 16) {\n      return {\n        tooYoung: true\n      };\n    }\n    if (actualAge > 120) {\n      return {\n        tooOld: true\n      };\n    }\n    return null;\n  }\n  static nameValidator(control) {\n    if (!control.value) return null;\n    const namePattern = /^[a-zA-ZÀ-ÿ\\s]+$/;\n    if (!namePattern.test(control.value)) {\n      return {\n        invalidName: true\n      };\n    }\n    return null;\n  }\n  static addressValidator(control) {\n    if (!control.value) return null;\n    if (control.value.length < 10) {\n      return {\n        addressTooShort: true\n      };\n    }\n    return null;\n  }\n  // ========================================\n  // VALIDATORS PARA LOJAS\n  // ========================================\n  static storeNameValidator(control) {\n    if (!control.value) return null;\n    const name = control.value.toLowerCase();\n    const requiredWords = ['loja', 'store', 'shop'];\n    if (!requiredWords.some(word => name.includes(word))) {\n      return {\n        invalidStoreName: true\n      };\n    }\n    return null;\n  }\n  static storePhoneValidator(control) {\n    if (!control.value) return null;\n    const phone = control.value.replace(/\\D/g, '');\n    if (phone.length !== 10 && phone.length !== 11) {\n      return {\n        invalidStorePhone: true\n      };\n    }\n    return null;\n  }\n  static cityValidator(control) {\n    if (!control.value) return null;\n    const cityPattern = /^[a-zA-ZÀ-ÿ\\s]+$/;\n    if (!cityPattern.test(control.value)) {\n      return {\n        invalidCity: true\n      };\n    }\n    return null;\n  }\n  static managerValidator(control) {\n    if (!control.value) return null;\n    const managerPattern = /^[a-zA-ZÀ-ÿ\\s]+$/;\n    if (!managerPattern.test(control.value)) {\n      return {\n        invalidManager: true\n      };\n    }\n    return null;\n  }\n  // ========================================\n  // VALIDATORS PARA CELULARES\n  // ========================================\n  static phoneModelValidator(control) {\n    if (!control.value) return null;\n    const model = control.value.toLowerCase();\n    const forbiddenWords = ['teste', 'test', 'exemplo'];\n    if (forbiddenWords.some(word => model.includes(word))) {\n      return {\n        invalidModel: true\n      };\n    }\n    return null;\n  }\n  static releaseDateValidator(control) {\n    if (!control.value) return null;\n    const releaseDate = new Date(control.value);\n    const currentDate = new Date();\n    const minDate = new Date('2000-01-01');\n    if (releaseDate > currentDate) {\n      return {\n        futureDate: true\n      };\n    }\n    if (releaseDate < minDate) {\n      return {\n        tooOld: true\n      };\n    }\n    return null;\n  }\n  static phonePriceValidator(control) {\n    if (!control.value) return null;\n    const price = +control.value;\n    if (price > 50000) {\n      return {\n        tooExpensive: true\n      };\n    }\n    return null;\n  }\n  static phoneStockValidator(control) {\n    if (!control.value && control.value !== 0) return null;\n    const stock = +control.value;\n    if (stock > 10000) {\n      return {\n        excessiveStock: true\n      };\n    }\n    return null;\n  }\n  // ========================================\n  // VALIDATORS PARA ACESSÓRIOS\n  // ========================================\n  static accessoryNameValidator(control) {\n    if (!control.value) return null;\n    const name = control.value.toLowerCase();\n    const requiredWords = ['case', 'capa', 'carregador', 'fone', 'película', 'suporte'];\n    if (!requiredWords.some(word => name.includes(word))) {\n      return {\n        invalidAccessoryName: true\n      };\n    }\n    return null;\n  }\n  static descriptionValidator(control) {\n    if (!control.value) return null;\n    const description = control.value.toLowerCase();\n    const forbiddenWords = ['ruim', 'péssimo', 'horrível'];\n    if (forbiddenWords.some(word => description.includes(word))) {\n      return {\n        negativeDescription: true\n      };\n    }\n    return null;\n  }\n  static accessoryPriceValidator(control) {\n    if (!control.value) return null;\n    const price = +control.value;\n    if (price > 5000) {\n      return {\n        tooExpensive: true\n      };\n    }\n    return null;\n  }\n  static accessoryStockValidator(control) {\n    if (!control.value && control.value !== 0) return null;\n    const stock = +control.value;\n    if (stock > 10000) {\n      return {\n        excessiveStock: true\n      };\n    }\n    return null;\n  }\n  // ========================================\n  // VALIDATORS PARA VENDAS\n  // ========================================\n  static stockValidator(control) {\n    var _control$parent$get;\n    if (!control.value || !control.parent) return null;\n    const quantity = +control.value;\n    const product = (_control$parent$get = control.parent.get('product')) === null || _control$parent$get === void 0 ? void 0 : _control$parent$get.value;\n    if (!product) {\n      return null; // Se não há produto selecionado, deixa outros validators tratarem\n    }\n    // Se o produto não tem campo stock definido, considera como sem estoque limitado\n    if (product.stock === undefined || product.stock === null) {\n      return null; // Permite venda se estoque não está definido\n    }\n    // Se estoque é 0, não permite venda\n    if (product.stock === 0) {\n      return {\n        noStock: true\n      };\n    }\n    // Se quantidade excede estoque disponível\n    if (quantity > product.stock) {\n      return {\n        stockExceeded: true\n      };\n    }\n    return null;\n  }\n}", "map": {"version": 3, "names": ["ApplicationValidators", "urlValidator", "control", "value", "startsWith", "invalidUrl", "emailDomainValidator", "email", "toLowerCase", "allowedDomains", "domain", "split", "includes", "invalidDomain", "phoneValidator", "phone", "replace", "length", "invalidPhone", "ageValida<PERSON>", "birthDate", "Date", "today", "age", "getFullYear", "monthDiff", "getMonth", "actualAge", "getDate", "too<PERSON><PERSON>ng", "tooOld", "nameValidator", "namePattern", "test", "invalid<PERSON><PERSON>", "addressValidator", "addressTooShort", "storeNameValidator", "name", "requiredWords", "some", "word", "invalidStoreName", "storePhoneValidator", "invalidStorePhone", "cityValidator", "cityPattern", "invalidCity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "manager<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "phoneModelValidator", "model", "forbidden<PERSON><PERSON><PERSON>", "invalidModel", "releaseDateValidator", "releaseDate", "currentDate", "minDate", "futureDate", "phonePriceValidator", "price", "tooExpensive", "phoneStockValidator", "stock", "excessiveStock", "accessoryNameValidator", "invalidAccessoryName", "descriptionValidator", "description", "negativeDescription", "accessoryPriceValidator", "accessoryStockValidator", "stockValidator", "_control$parent$get", "parent", "quantity", "product", "get", "undefined", "noStock", "stockExceeded"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\core\\validators\\url.validator.ts"], "sourcesContent": ["import { AbstractControl, ValidationErrors, ValidatorFn } from \"@angular/forms\";\r\n\r\nexport class ApplicationValidators {\r\n\r\n  static urlValidator(control: AbstractControl): ValidationErrors | null {\r\n    const value = control.value;\r\n    if(value.startsWith('http://') || value.startsWith('https://')) {\r\n      return null;\r\n    }\r\n    return { invalidUrl: true }\r\n  }\r\n\r\n  // ========================================\r\n  // VALIDATORS PARA CLIENTES\r\n  // ========================================\r\n\r\n  static emailDomainValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const email = control.value.toLowerCase();\r\n    const allowedDomains = ['gmail.com', 'hotmail.com', 'outlook.com', 'yahoo.com'];\r\n    const domain = email.split('@')[1];\r\n\r\n    if (domain && !allowedDomains.includes(domain)) {\r\n      return { invalidDomain: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static phoneValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const phone = control.value.replace(/\\D/g, '');\r\n    if (phone.length !== 11) {\r\n      return { invalidPhone: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static ageValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const birthDate = new Date(control.value);\r\n    const today = new Date();\r\n    const age = today.getFullYear() - birthDate.getFullYear();\r\n    const monthDiff = today.getMonth() - birthDate.getMonth();\r\n\r\n    const actualAge = monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())\r\n      ? age - 1 : age;\r\n\r\n    if (actualAge < 16) {\r\n      return { tooYoung: true };\r\n    }\r\n    if (actualAge > 120) {\r\n      return { tooOld: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static nameValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const namePattern = /^[a-zA-ZÀ-ÿ\\s]+$/;\r\n    if (!namePattern.test(control.value)) {\r\n      return { invalidName: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static addressValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    if (control.value.length < 10) {\r\n      return { addressTooShort: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // ========================================\r\n  // VALIDATORS PARA LOJAS\r\n  // ========================================\r\n\r\n  static storeNameValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const name = control.value.toLowerCase();\r\n    const requiredWords = ['loja', 'store', 'shop'];\r\n\r\n    if (!requiredWords.some(word => name.includes(word))) {\r\n      return { invalidStoreName: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static storePhoneValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const phone = control.value.replace(/\\D/g, '');\r\n    if (phone.length !== 10 && phone.length !== 11) {\r\n      return { invalidStorePhone: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static cityValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const cityPattern = /^[a-zA-ZÀ-ÿ\\s]+$/;\r\n    if (!cityPattern.test(control.value)) {\r\n      return { invalidCity: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static managerValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const managerPattern = /^[a-zA-ZÀ-ÿ\\s]+$/;\r\n    if (!managerPattern.test(control.value)) {\r\n      return { invalidManager: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // ========================================\r\n  // VALIDATORS PARA CELULARES\r\n  // ========================================\r\n\r\n  static phoneModelValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const model = control.value.toLowerCase();\r\n    const forbiddenWords = ['teste', 'test', 'exemplo'];\r\n\r\n    if (forbiddenWords.some(word => model.includes(word))) {\r\n      return { invalidModel: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static releaseDateValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const releaseDate = new Date(control.value);\r\n    const currentDate = new Date();\r\n    const minDate = new Date('2000-01-01');\r\n\r\n    if (releaseDate > currentDate) {\r\n      return { futureDate: true };\r\n    }\r\n    if (releaseDate < minDate) {\r\n      return { tooOld: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static phonePriceValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const price = +control.value;\r\n    if (price > 50000) {\r\n      return { tooExpensive: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static phoneStockValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value && control.value !== 0) return null;\r\n\r\n    const stock = +control.value;\r\n    if (stock > 10000) {\r\n      return { excessiveStock: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // ========================================\r\n  // VALIDATORS PARA ACESSÓRIOS\r\n  // ========================================\r\n\r\n  static accessoryNameValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const name = control.value.toLowerCase();\r\n    const requiredWords = ['case', 'capa', 'carregador', 'fone', 'película', 'suporte'];\r\n\r\n    if (!requiredWords.some(word => name.includes(word))) {\r\n      return { invalidAccessoryName: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static descriptionValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const description = control.value.toLowerCase();\r\n    const forbiddenWords = ['ruim', 'péssimo', 'horrível'];\r\n\r\n    if (forbiddenWords.some(word => description.includes(word))) {\r\n      return { negativeDescription: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static accessoryPriceValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const price = +control.value;\r\n    if (price > 5000) {\r\n      return { tooExpensive: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  static accessoryStockValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value && control.value !== 0) return null;\r\n\r\n    const stock = +control.value;\r\n    if (stock > 10000) {\r\n      return { excessiveStock: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // ========================================\r\n  // VALIDATORS PARA VENDAS\r\n  // ========================================\r\n\r\n  static stockValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value || !control.parent) return null;\r\n\r\n    const quantity = +control.value;\r\n    const product = control.parent.get('product')?.value;\r\n\r\n    if (!product) {\r\n      return null; // Se não há produto selecionado, deixa outros validators tratarem\r\n    }\r\n\r\n    // Se o produto não tem campo stock definido, considera como sem estoque limitado\r\n    if (product.stock === undefined || product.stock === null) {\r\n      return null; // Permite venda se estoque não está definido\r\n    }\r\n\r\n    // Se estoque é 0, não permite venda\r\n    if (product.stock === 0) {\r\n      return { noStock: true };\r\n    }\r\n\r\n    // Se quantidade excede estoque disponível\r\n    if (quantity > product.stock) {\r\n      return { stockExceeded: true };\r\n    }\r\n\r\n    return null;\r\n  }\r\n}\r\n"], "mappings": "AAEA,OAAM,MAAOA,qBAAqB;EAEhC,OAAOC,YAAYA,CAACC,OAAwB;IAC1C,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;IAC3B,IAAGA,KAAK,CAACC,UAAU,CAAC,SAAS,CAAC,IAAID,KAAK,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;MAC9D,OAAO,IAAI;IACb;IACA,OAAO;MAAEC,UAAU,EAAE;IAAI,CAAE;EAC7B;EAEA;EACA;EACA;EAEA,OAAOC,oBAAoBA,CAACJ,OAAwB;IAClD,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAMI,KAAK,GAAGL,OAAO,CAACC,KAAK,CAACK,WAAW,EAAE;IACzC,MAAMC,cAAc,GAAG,CAAC,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,CAAC;IAC/E,MAAMC,MAAM,GAAGH,KAAK,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAElC,IAAID,MAAM,IAAI,CAACD,cAAc,CAACG,QAAQ,CAACF,MAAM,CAAC,EAAE;MAC9C,OAAO;QAAEG,aAAa,EAAE;MAAI,CAAE;IAChC;IACA,OAAO,IAAI;EACb;EAEA,OAAOC,cAAcA,CAACZ,OAAwB;IAC5C,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAMY,KAAK,GAAGb,OAAO,CAACC,KAAK,CAACa,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC9C,IAAID,KAAK,CAACE,MAAM,KAAK,EAAE,EAAE;MACvB,OAAO;QAAEC,YAAY,EAAE;MAAI,CAAE;IAC/B;IACA,OAAO,IAAI;EACb;EAEA,OAAOC,YAAYA,CAACjB,OAAwB;IAC1C,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAMiB,SAAS,GAAG,IAAIC,IAAI,CAACnB,OAAO,CAACC,KAAK,CAAC;IACzC,MAAMmB,KAAK,GAAG,IAAID,IAAI,EAAE;IACxB,MAAME,GAAG,GAAGD,KAAK,CAACE,WAAW,EAAE,GAAGJ,SAAS,CAACI,WAAW,EAAE;IACzD,MAAMC,SAAS,GAAGH,KAAK,CAACI,QAAQ,EAAE,GAAGN,SAAS,CAACM,QAAQ,EAAE;IAEzD,MAAMC,SAAS,GAAGF,SAAS,GAAG,CAAC,IAAKA,SAAS,KAAK,CAAC,IAAIH,KAAK,CAACM,OAAO,EAAE,GAAGR,SAAS,CAACQ,OAAO,EAAG,GACzFL,GAAG,GAAG,CAAC,GAAGA,GAAG;IAEjB,IAAII,SAAS,GAAG,EAAE,EAAE;MAClB,OAAO;QAAEE,QAAQ,EAAE;MAAI,CAAE;IAC3B;IACA,IAAIF,SAAS,GAAG,GAAG,EAAE;MACnB,OAAO;QAAEG,MAAM,EAAE;MAAI,CAAE;IACzB;IACA,OAAO,IAAI;EACb;EAEA,OAAOC,aAAaA,CAAC7B,OAAwB;IAC3C,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAM6B,WAAW,GAAG,kBAAkB;IACtC,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC/B,OAAO,CAACC,KAAK,CAAC,EAAE;MACpC,OAAO;QAAE+B,WAAW,EAAE;MAAI,CAAE;IAC9B;IACA,OAAO,IAAI;EACb;EAEA,OAAOC,gBAAgBA,CAACjC,OAAwB;IAC9C,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,IAAID,OAAO,CAACC,KAAK,CAACc,MAAM,GAAG,EAAE,EAAE;MAC7B,OAAO;QAAEmB,eAAe,EAAE;MAAI,CAAE;IAClC;IACA,OAAO,IAAI;EACb;EAEA;EACA;EACA;EAEA,OAAOC,kBAAkBA,CAACnC,OAAwB;IAChD,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAMmC,IAAI,GAAGpC,OAAO,CAACC,KAAK,CAACK,WAAW,EAAE;IACxC,MAAM+B,aAAa,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;IAE/C,IAAI,CAACA,aAAa,CAACC,IAAI,CAACC,IAAI,IAAIH,IAAI,CAAC1B,QAAQ,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACpD,OAAO;QAAEC,gBAAgB,EAAE;MAAI,CAAE;IACnC;IACA,OAAO,IAAI;EACb;EAEA,OAAOC,mBAAmBA,CAACzC,OAAwB;IACjD,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAMY,KAAK,GAAGb,OAAO,CAACC,KAAK,CAACa,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC9C,IAAID,KAAK,CAACE,MAAM,KAAK,EAAE,IAAIF,KAAK,CAACE,MAAM,KAAK,EAAE,EAAE;MAC9C,OAAO;QAAE2B,iBAAiB,EAAE;MAAI,CAAE;IACpC;IACA,OAAO,IAAI;EACb;EAEA,OAAOC,aAAaA,CAAC3C,OAAwB;IAC3C,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAM2C,WAAW,GAAG,kBAAkB;IACtC,IAAI,CAACA,WAAW,CAACb,IAAI,CAAC/B,OAAO,CAACC,KAAK,CAAC,EAAE;MACpC,OAAO;QAAE4C,WAAW,EAAE;MAAI,CAAE;IAC9B;IACA,OAAO,IAAI;EACb;EAEA,OAAOC,gBAAgBA,CAAC9C,OAAwB;IAC9C,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAM8C,cAAc,GAAG,kBAAkB;IACzC,IAAI,CAACA,cAAc,CAAChB,IAAI,CAAC/B,OAAO,CAACC,KAAK,CAAC,EAAE;MACvC,OAAO;QAAE+C,cAAc,EAAE;MAAI,CAAE;IACjC;IACA,OAAO,IAAI;EACb;EAEA;EACA;EACA;EAEA,OAAOC,mBAAmBA,CAACjD,OAAwB;IACjD,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAMiD,KAAK,GAAGlD,OAAO,CAACC,KAAK,CAACK,WAAW,EAAE;IACzC,MAAM6C,cAAc,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;IAEnD,IAAIA,cAAc,CAACb,IAAI,CAACC,IAAI,IAAIW,KAAK,CAACxC,QAAQ,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACrD,OAAO;QAAEa,YAAY,EAAE;MAAI,CAAE;IAC/B;IACA,OAAO,IAAI;EACb;EAEA,OAAOC,oBAAoBA,CAACrD,OAAwB;IAClD,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAMqD,WAAW,GAAG,IAAInC,IAAI,CAACnB,OAAO,CAACC,KAAK,CAAC;IAC3C,MAAMsD,WAAW,GAAG,IAAIpC,IAAI,EAAE;IAC9B,MAAMqC,OAAO,GAAG,IAAIrC,IAAI,CAAC,YAAY,CAAC;IAEtC,IAAImC,WAAW,GAAGC,WAAW,EAAE;MAC7B,OAAO;QAAEE,UAAU,EAAE;MAAI,CAAE;IAC7B;IACA,IAAIH,WAAW,GAAGE,OAAO,EAAE;MACzB,OAAO;QAAE5B,MAAM,EAAE;MAAI,CAAE;IACzB;IACA,OAAO,IAAI;EACb;EAEA,OAAO8B,mBAAmBA,CAAC1D,OAAwB;IACjD,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAM0D,KAAK,GAAG,CAAC3D,OAAO,CAACC,KAAK;IAC5B,IAAI0D,KAAK,GAAG,KAAK,EAAE;MACjB,OAAO;QAAEC,YAAY,EAAE;MAAI,CAAE;IAC/B;IACA,OAAO,IAAI;EACb;EAEA,OAAOC,mBAAmBA,CAAC7D,OAAwB;IACjD,IAAI,CAACA,OAAO,CAACC,KAAK,IAAID,OAAO,CAACC,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAEtD,MAAM6D,KAAK,GAAG,CAAC9D,OAAO,CAACC,KAAK;IAC5B,IAAI6D,KAAK,GAAG,KAAK,EAAE;MACjB,OAAO;QAAEC,cAAc,EAAE;MAAI,CAAE;IACjC;IACA,OAAO,IAAI;EACb;EAEA;EACA;EACA;EAEA,OAAOC,sBAAsBA,CAAChE,OAAwB;IACpD,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAMmC,IAAI,GAAGpC,OAAO,CAACC,KAAK,CAACK,WAAW,EAAE;IACxC,MAAM+B,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC;IAEnF,IAAI,CAACA,aAAa,CAACC,IAAI,CAACC,IAAI,IAAIH,IAAI,CAAC1B,QAAQ,CAAC6B,IAAI,CAAC,CAAC,EAAE;MACpD,OAAO;QAAE0B,oBAAoB,EAAE;MAAI,CAAE;IACvC;IACA,OAAO,IAAI;EACb;EAEA,OAAOC,oBAAoBA,CAAClE,OAAwB;IAClD,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAMkE,WAAW,GAAGnE,OAAO,CAACC,KAAK,CAACK,WAAW,EAAE;IAC/C,MAAM6C,cAAc,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC;IAEtD,IAAIA,cAAc,CAACb,IAAI,CAACC,IAAI,IAAI4B,WAAW,CAACzD,QAAQ,CAAC6B,IAAI,CAAC,CAAC,EAAE;MAC3D,OAAO;QAAE6B,mBAAmB,EAAE;MAAI,CAAE;IACtC;IACA,OAAO,IAAI;EACb;EAEA,OAAOC,uBAAuBA,CAACrE,OAAwB;IACrD,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAM0D,KAAK,GAAG,CAAC3D,OAAO,CAACC,KAAK;IAC5B,IAAI0D,KAAK,GAAG,IAAI,EAAE;MAChB,OAAO;QAAEC,YAAY,EAAE;MAAI,CAAE;IAC/B;IACA,OAAO,IAAI;EACb;EAEA,OAAOU,uBAAuBA,CAACtE,OAAwB;IACrD,IAAI,CAACA,OAAO,CAACC,KAAK,IAAID,OAAO,CAACC,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAEtD,MAAM6D,KAAK,GAAG,CAAC9D,OAAO,CAACC,KAAK;IAC5B,IAAI6D,KAAK,GAAG,KAAK,EAAE;MACjB,OAAO;QAAEC,cAAc,EAAE;MAAI,CAAE;IACjC;IACA,OAAO,IAAI;EACb;EAEA;EACA;EACA;EAEA,OAAOQ,cAAcA,CAACvE,OAAwB;IAAA,IAAAwE,mBAAA;IAC5C,IAAI,CAACxE,OAAO,CAACC,KAAK,IAAI,CAACD,OAAO,CAACyE,MAAM,EAAE,OAAO,IAAI;IAElD,MAAMC,QAAQ,GAAG,CAAC1E,OAAO,CAACC,KAAK;IAC/B,MAAM0E,OAAO,IAAAH,mBAAA,GAAGxE,OAAO,CAACyE,MAAM,CAACG,GAAG,CAAC,SAAS,CAAC,cAAAJ,mBAAA,uBAA7BA,mBAAA,CAA+BvE,KAAK;IAEpD,IAAI,CAAC0E,OAAO,EAAE;MACZ,OAAO,IAAI,CAAC,CAAC;IACf;IAEA;IACA,IAAIA,OAAO,CAACb,KAAK,KAAKe,SAAS,IAAIF,OAAO,CAACb,KAAK,KAAK,IAAI,EAAE;MACzD,OAAO,IAAI,CAAC,CAAC;IACf;IAEA;IACA,IAAIa,OAAO,CAACb,KAAK,KAAK,CAAC,EAAE;MACvB,OAAO;QAAEgB,OAAO,EAAE;MAAI,CAAE;IAC1B;IAEA;IACA,IAAIJ,QAAQ,GAAGC,OAAO,CAACb,KAAK,EAAE;MAC5B,OAAO;QAAEiB,aAAa,EAAE;MAAI,CAAE;IAChC;IAEA,OAAO,IAAI;EACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}