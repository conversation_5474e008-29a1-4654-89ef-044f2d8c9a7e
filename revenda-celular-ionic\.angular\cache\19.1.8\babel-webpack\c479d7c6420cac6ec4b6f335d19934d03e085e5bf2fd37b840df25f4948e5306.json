{"ast": null, "code": "var _StoresPageModule;\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { StoresPageRoutingModule } from './stores-routing.module';\nimport { StoresPage } from './stores.page';\nimport { StoreFormComponent } from './store-form/store-form.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { MaskitoDirective } from '@maskito/angular';\nimport * as i0 from \"@angular/core\";\nexport class StoresPageModule {}\n_StoresPageModule = StoresPageModule;\n_StoresPageModule.ɵfac = function StoresPageModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _StoresPageModule)();\n};\n_StoresPageModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _StoresPageModule\n});\n_StoresPageModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [CommonModule, FormsModule, IonicModule, StoresPageRoutingModule, ReactiveFormsModule, HttpClientModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StoresPageModule, {\n    declarations: [StoresPage, StoreFormComponent],\n    imports: [CommonModule, FormsModule, IonicModule, StoresPageRoutingModule, ReactiveFormsModule, HttpClientModule, MaskitoDirective]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "IonicModule", "StoresPageRoutingModule", "StoresPage", "StoreFormComponent", "HttpClientModule", "MaskitoDirective", "StoresPageModule", "declarations", "imports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\stores\\stores.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { StoresPageRoutingModule } from './stores-routing.module';\r\n\r\nimport { StoresPage } from './stores.page';\r\nimport { StoreFormComponent } from './store-form/store-form.component';\r\nimport { HttpClientModule } from '@angular/common/http';\r\nimport { MaskitoDirective } from '@maskito/angular';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    StoresPageRoutingModule,\r\n    ReactiveFormsModule,\r\n    HttpClientModule,\r\n    MaskitoDirective,\r\n  ],\r\n  declarations: [\r\n    StoresPage,\r\n    StoreFormComponent,\r\n  ]\r\n})\r\nexport class StoresPageModule {}\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,uBAAuB,QAAQ,yBAAyB;AAEjE,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,gBAAgB,QAAQ,kBAAkB;;AAiBnD,OAAM,MAAOC,gBAAgB;oBAAhBA,gBAAgB;;mCAAhBA,iBAAgB;AAAA;;QAAhBA;AAAgB;;YAbzBT,YAAY,EACZC,WAAW,EACXE,WAAW,EACXC,uBAAuB,EACvBF,mBAAmB,EACnBK,gBAAgB;AAAA;;2EAQPE,gBAAgB;IAAAC,YAAA,GAJzBL,UAAU,EACVC,kBAAkB;IAAAK,OAAA,GAVlBX,YAAY,EACZC,WAAW,EACXE,WAAW,EACXC,uBAAuB,EACvBF,mBAAmB,EACnBK,gBAAgB,EAChBC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}