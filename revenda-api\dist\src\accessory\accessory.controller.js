"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccessoryController = void 0;
const common_1 = require("@nestjs/common");
const accessory_service_1 = require("./accessory.service");
const create_accessory_dto_1 = require("./dto/create-accessory.dto");
const update_accessory_dto_1 = require("./dto/update-accessory.dto");
let AccessoryController = class AccessoryController {
    accessoryService;
    constructor(accessoryService) {
        this.accessoryService = accessoryService;
    }
    create(createAccessoryDto) {
        return this.accessoryService.create(createAccessoryDto);
    }
    findAll() {
        return this.accessoryService.findAll();
    }
    findOne(id) {
        return this.accessoryService.findOne(+id);
    }
    findByCategory(category) {
        return this.accessoryService.findByCategory(category);
    }
    findInStock() {
        return this.accessoryService.findInStock();
    }
    findAllInStock() {
        return this.accessoryService.findAllInStock();
    }
    update(id, updateAccessoryDto) {
        return this.accessoryService.update(+id, updateAccessoryDto);
    }
    updateStock(id, quantity) {
        return this.accessoryService.updateStock(+id, quantity);
    }
    remove(id) {
        return this.accessoryService.remove(+id);
    }
};
exports.AccessoryController = AccessoryController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_accessory_dto_1.CreateAccessoryDto]),
    __metadata("design:returntype", void 0)
], AccessoryController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AccessoryController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AccessoryController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('category/:category'),
    __param(0, (0, common_1.Param)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AccessoryController.prototype, "findByCategory", null);
__decorate([
    (0, common_1.Get)('stock/available'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AccessoryController.prototype, "findInStock", null);
__decorate([
    (0, common_1.Get)('stock/only-available'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AccessoryController.prototype, "findAllInStock", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_accessory_dto_1.UpdateAccessoryDto]),
    __metadata("design:returntype", void 0)
], AccessoryController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/stock'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('quantity')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", void 0)
], AccessoryController.prototype, "updateStock", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AccessoryController.prototype, "remove", null);
exports.AccessoryController = AccessoryController = __decorate([
    (0, common_1.Controller)('accessories'),
    __metadata("design:paramtypes", [accessory_service_1.AccessoryService])
], AccessoryController);
//# sourceMappingURL=accessory.controller.js.map