{"ast": null, "code": "var _SalesPageModule;\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { SalesPageRoutingModule } from './sales-routing.module';\nimport { SalesPage } from './sales.page';\nimport { SaleFormComponent } from './sale-form/sale-form.component';\nimport { SaleDetailsComponent } from './sale-details/sale-details.component';\nimport { MaskitoDirective } from '@maskito/angular';\nimport * as i0 from \"@angular/core\";\nexport class SalesPageModule {}\n_SalesPageModule = SalesPageModule;\n_SalesPageModule.ɵfac = function SalesPageModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SalesPageModule)();\n};\n_SalesPageModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _SalesPageModule\n});\n_SalesPageModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, SalesPageRoutingModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SalesPageModule, {\n    declarations: [SalesPage, SaleFormComponent, SaleDetailsComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, SalesPageRoutingModule, MaskitoDirective]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "IonicModule", "SalesPageRoutingModule", "SalesPage", "SaleFormComponent", "SaleDetailsComponent", "MaskitoDirective", "SalesPageModule", "declarations", "imports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\sales\\sales.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { SalesPageRoutingModule } from './sales-routing.module';\r\n\r\nimport { SalesPage } from './sales.page';\r\nimport { SaleFormComponent } from './sale-form/sale-form.component';\r\nimport { SaleDetailsComponent } from './sale-details/sale-details.component';\r\nimport { MaskitoDirective } from '@maskito/angular';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    IonicModule,\r\n    SalesPageRoutingModule,\r\n    MaskitoDirective\r\n  ],\r\n  declarations: [\r\n    SalesPage,\r\n    SaleFormComponent,\r\n    SaleDetailsComponent\r\n  ]\r\n})\r\nexport class SalesPageModule {}\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,sBAAsB,QAAQ,wBAAwB;AAE/D,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,gBAAgB,QAAQ,kBAAkB;;AAiBnD,OAAM,MAAOC,eAAe;mBAAfA,eAAe;;mCAAfA,gBAAe;AAAA;;QAAfA;AAAe;;YAbxBT,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW,EACXC,sBAAsB;AAAA;;2EASbK,eAAe;IAAAC,YAAA,GALxBL,SAAS,EACTC,iBAAiB,EACjBC,oBAAoB;IAAAI,OAAA,GAVpBX,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW,EACXC,sBAAsB,EACtBI,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}