{"ast": null, "code": "export class ApplicationValidators {\n  static urlValidator(control) {\n    const value = control.value;\n    if (value.startsWith('http://') || value.startsWith('https://')) {\n      return null;\n    }\n    return {\n      invalidUrl: true\n    };\n  }\n}", "map": {"version": 3, "names": ["ApplicationValidators", "urlValidator", "control", "value", "startsWith", "invalidUrl"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\core\\validators\\url.validator.ts"], "sourcesContent": ["import { AbstractControl, ValidationErrors, ValidatorFn } from \"@angular/forms\";\r\n\r\nexport class ApplicationValidators {\r\n\r\n  static urlValidator(control: AbstractControl): ValidationErrors | null {\r\n    const value = control.value;\r\n    if(value.startsWith('http://') || value.startsWith('https://')) {\r\n      return null;\r\n    }\r\n    return { invalidUrl: true }\r\n  }\r\n}\r\n"], "mappings": "AAEA,OAAM,MAAOA,qBAAqB;EAEhC,OAAOC,YAAYA,CAACC,OAAwB;IAC1C,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;IAC3B,IAAGA,KAAK,CAACC,UAAU,CAAC,SAAS,CAAC,IAAID,KAAK,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;MAC9D,OAAO,IAAI;IACb;IACA,OAAO;MAAEC,UAAU,EAAE;IAAI,CAAE;EAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}