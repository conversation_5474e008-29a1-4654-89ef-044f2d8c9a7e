// Estilos padronizados para lista
ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 100px;
  --border-radius: 12px;
  --border-width: 1px;
  --border-style: solid;
  --border-color: var(--ion-color-light-shade);
  margin: 8px 16px;
  --background: var(--ion-color-light);
}

ion-avatar {
  width: 50px;
  height: 50px;
  margin-right: 16px;

  .customer-avatar {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--ion-color-medium-tint);
    border-radius: 8px;
    background: transparent;

    ion-icon {
      font-size: 24px;
      color: var(--ion-color-medium);
    }
  }
}

ion-label {
  h2 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 4px;
    color: var(--ion-color-primary);
  }

  p {
    font-size: 13px;
    margin: 2px 0;
    color: var(--ion-color-dark);

    strong {
      color: var(--ion-color-primary);
    }
  }

  .badges {
    margin-top: 8px;

    ion-badge {
      margin-right: 8px;
      margin-bottom: 4px;
    }
  }
}

ion-button {
  margin-left: 4px;

  &[color="danger"] {
    --color: var(--ion-color-danger);
  }
}

// Manter estilos antigos para compatibilidade
.customer-info {
  width: 100%;

  h2 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 4px;
  }

  h3, h4 {
    font-size: 14px;
    margin: 4px 0;
    font-weight: normal;
  }

  ion-badge {
    margin-right: 8px;
  }

  ion-button {
    margin-top: 8px;
    margin-right: 8px;
  }
}












