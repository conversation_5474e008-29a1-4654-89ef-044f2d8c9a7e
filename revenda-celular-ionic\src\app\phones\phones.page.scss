h2, h3, h4 {
  display: block;
  margin: 0.25rem;
}

.brands {
  list-style: none;
  padding-inline-start: 0;
  margin: 0;
}

h2 {
  padding-top: 1rem;
}

// Estilos para lista com imagens
ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 80px;
}

ion-avatar {
  width: 60px;
  height: 60px;
  margin-right: 16px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }
}

ion-label {
  h2 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 4px;
    color: var(--ion-color-primary);
  }

  h3 {
    font-size: 14px;
    font-weight: 600;
    margin: 2px 0;
    color: var(--ion-color-medium);
  }

  p {
    font-size: 13px;
    margin: 2px 0;
    color: var(--ion-color-dark);

    strong {
      color: var(--ion-color-primary);
    }
  }
}

ion-button {
  margin-left: 4px;

  &[color="danger"] {
    --color: var(--ion-color-danger);
  }
}

// Manter estilos antigos para compatibilidade
.phone-info {
  width: 100%;

  h2 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 4px;
  }

  h3, h4 {
    font-size: 14px;
    margin: 4px 0;
    font-weight: normal;
  }

  ion-badge {
    margin-right: 8px;
  }

  ion-button {
    margin-top: 8px;
    margin-right: 8px;
  }
}
