<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Cadastro de Lojas</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="form-container">
    <form [formGroup]="storeForm">
      <ion-list>
        <ion-item>
          <ion-input formControlName="name" labelPlacement="floating" label="Nome" type="text"></ion-input>
          <p>
          @if(hasError('name', 'required')) {
            O campo é obrigatório
          }
          @if(hasError('name', 'minlength')) {
            O campo deve ter no mínimo 3 caracteres
          }
          @if(hasError('name', 'maxlength')) {
            O campo deve ter no máximo 100 caracteres
          }
          @if(hasError('name', 'invalidStoreName')) {
            O nome deve conter uma palavra identificadora (loja, store, shop)
          }
          </p>
        </ion-item>
        
        <ion-item>
          <ion-input formControlName="address" labelPlacement="floating" label="Endereço" type="text"></ion-input>
          <p>
          @if(hasError('address', 'required')) {
            O campo é obrigatório
          }
          @if(hasError('address', 'addressTooShort')) {
            Endereço deve ter pelo menos 10 caracteres
          }
          </p>
        </ion-item>
        
        <ion-item>
          <ion-input formControlName="city" labelPlacement="floating" label="Cidade" type="text"></ion-input>
          <p>
          @if(hasError('city', 'required')) {
            O campo é obrigatório
          }
          @if(hasError('city', 'invalidCity')) {
            Cidade deve conter apenas letras e espaços
          }
          </p>
        </ion-item>
        
        <ion-item>
          <ion-select formControlName="state" labelPlacement="floating" label="Estado">
            @for(state of stateOptions; track state) {
              <ion-select-option [value]="state">{{ state }}</ion-select-option>
            }
          </ion-select>
          <p>
          @if(hasError('state', 'required')) {
            O campo é obrigatório
          }
          </p>
        </ion-item>
        
        <ion-item>
          <ion-input formControlName="phone" labelPlacement="floating" label="Telefone" type="tel"></ion-input>
          <p>
          @if(hasError('phone', 'required')) {
            O campo é obrigatório
          }
          @if(hasError('phone', 'invalidStorePhone')) {
            Telefone deve ter 10 ou 11 dígitos
          }
          </p>
        </ion-item>
        
        <ion-item>
          <ion-input formControlName="manager" labelPlacement="floating" label="Gerente" type="text"></ion-input>
          <p>
          @if(hasError('manager', 'required')) {
            O campo é obrigatório
          }
          @if(hasError('manager', 'invalidManager')) {
            Nome do gerente deve conter apenas letras e espaços
          }
          </p>
        </ion-item>
        
        <ion-item>
          <ion-label>Matriz:</ion-label>
          <ion-toggle formControlName="isHeadquarters" slot="end"></ion-toggle>
        </ion-item>
        
        <ion-item>
          <ion-select formControlName="status" labelPlacement="floating" label="Status">
            @for(option of statusOptions; track option.value) {
              <ion-select-option [value]="option.value">{{ option.label }}</ion-select-option>
            }
          </ion-select>
          <p>
          @if(hasError('status', 'required')) {
            O campo é obrigatório
          }
          </p>
        </ion-item>
      </ion-list>
      
      <ion-fab vertical="bottom" horizontal="end" slot="fixed">
        <ion-fab-button [disabled]="storeForm.invalid" (click)="save()">
          <ion-icon name="checkmark"></ion-icon>
        </ion-fab-button>
      </ion-fab>
    </form>
  </div>
</ion-content>
