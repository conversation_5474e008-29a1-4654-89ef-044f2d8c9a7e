{"ast": null, "code": "var _SaleService;\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class SaleService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.baseUrl}/sales`;\n  }\n  getAll() {\n    return this.http.get(this.apiUrl);\n  }\n  getById(id) {\n    return this.http.get(`${this.apiUrl}/${id}`);\n  }\n  save(sale) {\n    if (sale.id) {\n      return this.update(sale);\n    }\n    return this.add(sale);\n  }\n  add(sale) {\n    return this.http.post(this.apiUrl, sale);\n  }\n  update(sale) {\n    return this.http.put(`${this.apiUrl}/${sale.id}`, sale);\n  }\n  delete(id) {\n    return this.http.delete(`${this.apiUrl}/${id}`);\n  }\n  filterSales(filters) {\n    let queryParams = '';\n    if (filters) {\n      const params = [];\n      if (filters.storeId) {\n        params.push(`store.id=${filters.storeId}`);\n      }\n      if (filters.status) {\n        params.push(`status=${filters.status}`);\n      }\n      if (params.length > 0) {\n        queryParams = '?' + params.join('&');\n      }\n    }\n    return this.http.get(`${this.apiUrl}${queryParams}`);\n  }\n}\n_SaleService = SaleService;\n_SaleService.ɵfac = function SaleService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SaleService)(i0.ɵɵinject(i1.HttpClient));\n};\n_SaleService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _SaleService,\n  factory: _SaleService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["environment", "SaleService", "constructor", "http", "apiUrl", "baseUrl", "getAll", "get", "getById", "id", "save", "sale", "update", "add", "post", "put", "delete", "filterSales", "filters", "queryParams", "params", "storeId", "push", "status", "length", "join", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\sales\\services\\sale.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { Sale } from '../models/sale.type';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SaleService {\r\n  private apiUrl = `${environment.baseUrl}/sales`;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getAll(): Observable<Sale[]> {\r\n    return this.http.get<Sale[]>(this.apiUrl);\r\n  }\r\n\r\n  getById(id: string | number): Observable<Sale> {\r\n    return this.http.get<Sale>(`${this.apiUrl}/${id}`);\r\n  }\r\n\r\n  save(sale: Sale): Observable<Sale> {\r\n    if (sale.id) {\r\n      return this.update(sale);\r\n    }\r\n    return this.add(sale);\r\n  }\r\n\r\n  private add(sale: Sale): Observable<Sale> {\r\n    return this.http.post<Sale>(this.apiUrl, sale);\r\n  }\r\n\r\n  private update(sale: Sale): Observable<Sale> {\r\n    return this.http.put<Sale>(`${this.apiUrl}/${sale.id}`, sale);\r\n  }\r\n\r\n  delete(id: string | number): Observable<void> {\r\n    return this.http.delete<void>(`${this.apiUrl}/${id}`);\r\n  }\r\n\r\n  filterSales(filters: any): Observable<Sale[]> {\r\n    let queryParams = '';\r\n    \r\n    if (filters) {\r\n      const params = [];\r\n      \r\n      if (filters.storeId) {\r\n        params.push(`store.id=${filters.storeId}`);\r\n      }\r\n      \r\n      if (filters.status) {\r\n        params.push(`status=${filters.status}`);\r\n      }\r\n      \r\n      if (params.length > 0) {\r\n        queryParams = '?' + params.join('&');\r\n      }\r\n    }\r\n    \r\n    return this.http.get<Sale[]>(`${this.apiUrl}${queryParams}`);\r\n  }\r\n}\r\n"], "mappings": ";AAIA,SAASA,WAAW,QAAQ,8BAA8B;;;AAK1D,OAAM,MAAOC,WAAW;EAGtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,OAAO,QAAQ;EAEP;EAExCC,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAS,IAAI,CAACH,MAAM,CAAC;EAC3C;EAEAI,OAAOA,CAACC,EAAmB;IACzB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAO,GAAG,IAAI,CAACH,MAAM,IAAIK,EAAE,EAAE,CAAC;EACpD;EAEAC,IAAIA,CAACC,IAAU;IACb,IAAIA,IAAI,CAACF,EAAE,EAAE;MACX,OAAO,IAAI,CAACG,MAAM,CAACD,IAAI,CAAC;IAC1B;IACA,OAAO,IAAI,CAACE,GAAG,CAACF,IAAI,CAAC;EACvB;EAEQE,GAAGA,CAACF,IAAU;IACpB,OAAO,IAAI,CAACR,IAAI,CAACW,IAAI,CAAO,IAAI,CAACV,MAAM,EAAEO,IAAI,CAAC;EAChD;EAEQC,MAAMA,CAACD,IAAU;IACvB,OAAO,IAAI,CAACR,IAAI,CAACY,GAAG,CAAO,GAAG,IAAI,CAACX,MAAM,IAAIO,IAAI,CAACF,EAAE,EAAE,EAAEE,IAAI,CAAC;EAC/D;EAEAK,MAAMA,CAACP,EAAmB;IACxB,OAAO,IAAI,CAACN,IAAI,CAACa,MAAM,CAAO,GAAG,IAAI,CAACZ,MAAM,IAAIK,EAAE,EAAE,CAAC;EACvD;EAEAQ,WAAWA,CAACC,OAAY;IACtB,IAAIC,WAAW,GAAG,EAAE;IAEpB,IAAID,OAAO,EAAE;MACX,MAAME,MAAM,GAAG,EAAE;MAEjB,IAAIF,OAAO,CAACG,OAAO,EAAE;QACnBD,MAAM,CAACE,IAAI,CAAC,YAAYJ,OAAO,CAACG,OAAO,EAAE,CAAC;MAC5C;MAEA,IAAIH,OAAO,CAACK,MAAM,EAAE;QAClBH,MAAM,CAACE,IAAI,CAAC,UAAUJ,OAAO,CAACK,MAAM,EAAE,CAAC;MACzC;MAEA,IAAIH,MAAM,CAACI,MAAM,GAAG,CAAC,EAAE;QACrBL,WAAW,GAAG,GAAG,GAAGC,MAAM,CAACK,IAAI,CAAC,GAAG,CAAC;MACtC;IACF;IAEA,OAAO,IAAI,CAACtB,IAAI,CAACI,GAAG,CAAS,GAAG,IAAI,CAACH,MAAM,GAAGe,WAAW,EAAE,CAAC;EAC9D;;eApDWlB,WAAW;;mCAAXA,YAAW,EAAAyB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAX5B,YAAW;EAAA6B,OAAA,EAAX7B,YAAW,CAAA8B,IAAA;EAAAC,UAAA,EAFV;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}