{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-28849c61.js';\nconst segmentViewIosCss = \":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}\";\nconst IonSegmentViewIosStyle0 = segmentViewIosCss;\nconst segmentViewMdCss = \":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}\";\nconst IonSegmentViewMdStyle0 = segmentViewMdCss;\nconst SegmentView = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionSegmentViewScroll = createEvent(this, \"ionSegmentViewScroll\", 7);\n    this.scrollEndTimeout = null;\n    this.isTouching = false;\n    this.disabled = false;\n    this.isManualScroll = undefined;\n  }\n  handleScroll(ev) {\n    var _a;\n    const {\n      scrollLeft,\n      scrollWidth,\n      clientWidth\n    } = ev.target;\n    const scrollRatio = scrollLeft / (scrollWidth - clientWidth);\n    this.ionSegmentViewScroll.emit({\n      scrollRatio,\n      isManualScroll: (_a = this.isManualScroll) !== null && _a !== void 0 ? _a : true\n    });\n    // Reset the timeout to check for scroll end\n    this.resetScrollEndTimeout();\n  }\n  /**\n   * Handle touch start event to know when the user is actively dragging the segment view.\n   */\n  handleScrollStart() {\n    if (this.scrollEndTimeout) {\n      clearTimeout(this.scrollEndTimeout);\n      this.scrollEndTimeout = null;\n    }\n    this.isTouching = true;\n  }\n  /**\n   * Handle touch end event to know when the user is no longer dragging the segment view.\n   */\n  handleTouchEnd() {\n    this.isTouching = false;\n  }\n  /**\n   * Reset the scroll end detection timer. This is called on every scroll event.\n   */\n  resetScrollEndTimeout() {\n    if (this.scrollEndTimeout) {\n      clearTimeout(this.scrollEndTimeout);\n      this.scrollEndTimeout = null;\n    }\n    this.scrollEndTimeout = setTimeout(() => {\n      this.checkForScrollEnd();\n    },\n    // Setting this to a lower value may result in inconsistencies in behavior\n    // across browsers (particularly Firefox).\n    // Ideally, all of this logic is removed once the scroll end event is\n    // supported on all browsers (https://caniuse.com/?search=scrollend)\n    100);\n  }\n  /**\n   * Check if the scroll has ended and the user is not actively touching.\n   * If the conditions are met (active content is enabled and no active touch),\n   * reset the scroll position and emit the scroll end event.\n   */\n  checkForScrollEnd() {\n    // Only emit scroll end event if the active content is not disabled and\n    // the user is not touching the segment view\n    if (!this.isTouching) {\n      this.isManualScroll = undefined;\n    }\n  }\n  /**\n   * @internal\n   *\n   * This method is used to programmatically set the displayed segment content\n   * in the segment view. Calling this method will update the `value` of the\n   * corresponding segment button.\n   *\n   * @param id: The id of the segment content to display.\n   * @param smoothScroll: Whether to animate the scroll transition.\n   */\n  setContent(id, smoothScroll = true) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const contents = _this.getSegmentContents();\n      const index = contents.findIndex(content => content.id === id);\n      if (index === -1) return;\n      _this.isManualScroll = false;\n      _this.resetScrollEndTimeout();\n      const contentWidth = _this.el.offsetWidth;\n      _this.el.scrollTo({\n        top: 0,\n        left: index * contentWidth,\n        behavior: smoothScroll ? 'smooth' : 'instant'\n      });\n    })();\n  }\n  getSegmentContents() {\n    return Array.from(this.el.querySelectorAll('ion-segment-content'));\n  }\n  render() {\n    const {\n      disabled,\n      isManualScroll\n    } = this;\n    return h(Host, {\n      key: '9f4f11d31c4db776f077e59ae895b35dd4454717',\n      class: {\n        'segment-view-disabled': disabled,\n        'segment-view-scroll-disabled': isManualScroll === false\n      }\n    }, h(\"slot\", {\n      key: 'ea58b21f031cee2ab2b70580f336deaefa364538'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nSegmentView.style = {\n  ios: IonSegmentViewIosStyle0,\n  md: IonSegmentViewMdStyle0\n};\nexport { SegmentView as ion_segment_view };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "segmentViewIosCss", "IonSegmentViewIosStyle0", "segmentViewMdCss", "IonSegmentViewMdStyle0", "SegmentView", "constructor", "hostRef", "ionSegmentViewScroll", "scrollEndTimeout", "isTouching", "disabled", "isManualScroll", "undefined", "handleScroll", "ev", "_a", "scrollLeft", "scrollWidth", "clientWidth", "target", "scrollRatio", "emit", "resetScrollEndTimeout", "handleScrollStart", "clearTimeout", "handleTouchEnd", "setTimeout", "checkForScrollEnd", "<PERSON><PERSON><PERSON><PERSON>", "id", "smoothScroll", "_this", "_asyncToGenerator", "contents", "getSegmentContents", "index", "findIndex", "content", "contentWidth", "el", "offsetWidth", "scrollTo", "top", "left", "behavior", "Array", "from", "querySelectorAll", "render", "key", "class", "style", "ios", "md", "ion_segment_view"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/ion-segment-view.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-28849c61.js';\n\nconst segmentViewIosCss = \":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}\";\nconst IonSegmentViewIosStyle0 = segmentViewIosCss;\n\nconst segmentViewMdCss = \":host{display:-ms-flexbox;display:flex;height:100%;overflow-x:scroll;-webkit-scroll-snap-type:x mandatory;-ms-scroll-snap-type:x mandatory;scroll-snap-type:x mandatory;scrollbar-width:none;-ms-overflow-style:none}:host::-webkit-scrollbar{display:none}:host(.segment-view-disabled){-ms-touch-action:none;touch-action:none;overflow-x:hidden}:host(.segment-view-scroll-disabled){pointer-events:none}:host(.segment-view-disabled){opacity:0.3}\";\nconst IonSegmentViewMdStyle0 = segmentViewMdCss;\n\nconst SegmentView = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionSegmentViewScroll = createEvent(this, \"ionSegmentViewScroll\", 7);\n        this.scrollEndTimeout = null;\n        this.isTouching = false;\n        this.disabled = false;\n        this.isManualScroll = undefined;\n    }\n    handleScroll(ev) {\n        var _a;\n        const { scrollLeft, scrollWidth, clientWidth } = ev.target;\n        const scrollRatio = scrollLeft / (scrollWidth - clientWidth);\n        this.ionSegmentViewScroll.emit({\n            scrollRatio,\n            isManualScroll: (_a = this.isManualScroll) !== null && _a !== void 0 ? _a : true,\n        });\n        // Reset the timeout to check for scroll end\n        this.resetScrollEndTimeout();\n    }\n    /**\n     * Handle touch start event to know when the user is actively dragging the segment view.\n     */\n    handleScrollStart() {\n        if (this.scrollEndTimeout) {\n            clearTimeout(this.scrollEndTimeout);\n            this.scrollEndTimeout = null;\n        }\n        this.isTouching = true;\n    }\n    /**\n     * Handle touch end event to know when the user is no longer dragging the segment view.\n     */\n    handleTouchEnd() {\n        this.isTouching = false;\n    }\n    /**\n     * Reset the scroll end detection timer. This is called on every scroll event.\n     */\n    resetScrollEndTimeout() {\n        if (this.scrollEndTimeout) {\n            clearTimeout(this.scrollEndTimeout);\n            this.scrollEndTimeout = null;\n        }\n        this.scrollEndTimeout = setTimeout(() => {\n            this.checkForScrollEnd();\n        }, \n        // Setting this to a lower value may result in inconsistencies in behavior\n        // across browsers (particularly Firefox).\n        // Ideally, all of this logic is removed once the scroll end event is\n        // supported on all browsers (https://caniuse.com/?search=scrollend)\n        100);\n    }\n    /**\n     * Check if the scroll has ended and the user is not actively touching.\n     * If the conditions are met (active content is enabled and no active touch),\n     * reset the scroll position and emit the scroll end event.\n     */\n    checkForScrollEnd() {\n        // Only emit scroll end event if the active content is not disabled and\n        // the user is not touching the segment view\n        if (!this.isTouching) {\n            this.isManualScroll = undefined;\n        }\n    }\n    /**\n     * @internal\n     *\n     * This method is used to programmatically set the displayed segment content\n     * in the segment view. Calling this method will update the `value` of the\n     * corresponding segment button.\n     *\n     * @param id: The id of the segment content to display.\n     * @param smoothScroll: Whether to animate the scroll transition.\n     */\n    async setContent(id, smoothScroll = true) {\n        const contents = this.getSegmentContents();\n        const index = contents.findIndex((content) => content.id === id);\n        if (index === -1)\n            return;\n        this.isManualScroll = false;\n        this.resetScrollEndTimeout();\n        const contentWidth = this.el.offsetWidth;\n        this.el.scrollTo({\n            top: 0,\n            left: index * contentWidth,\n            behavior: smoothScroll ? 'smooth' : 'instant',\n        });\n    }\n    getSegmentContents() {\n        return Array.from(this.el.querySelectorAll('ion-segment-content'));\n    }\n    render() {\n        const { disabled, isManualScroll } = this;\n        return (h(Host, { key: '9f4f11d31c4db776f077e59ae895b35dd4454717', class: {\n                'segment-view-disabled': disabled,\n                'segment-view-scroll-disabled': isManualScroll === false,\n            } }, h(\"slot\", { key: 'ea58b21f031cee2ab2b70580f336deaefa364538' })));\n    }\n    get el() { return getElement(this); }\n};\nSegmentView.style = {\n    ios: IonSegmentViewIosStyle0,\n    md: IonSegmentViewMdStyle0\n};\n\nexport { SegmentView as ion_segment_view };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAE5G,MAAMC,iBAAiB,GAAG,wbAAwb;AACld,MAAMC,uBAAuB,GAAGD,iBAAiB;AAEjD,MAAME,gBAAgB,GAAG,wbAAwb;AACjd,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,WAAW,GAAG,MAAM;EACtBC,WAAWA,CAACC,OAAO,EAAE;IACjBd,gBAAgB,CAAC,IAAI,EAAEc,OAAO,CAAC;IAC/B,IAAI,CAACC,oBAAoB,GAAGb,WAAW,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC;IACxE,IAAI,CAACc,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,cAAc,GAAGC,SAAS;EACnC;EACAC,YAAYA,CAACC,EAAE,EAAE;IACb,IAAIC,EAAE;IACN,MAAM;MAAEC,UAAU;MAAEC,WAAW;MAAEC;IAAY,CAAC,GAAGJ,EAAE,CAACK,MAAM;IAC1D,MAAMC,WAAW,GAAGJ,UAAU,IAAIC,WAAW,GAAGC,WAAW,CAAC;IAC5D,IAAI,CAACX,oBAAoB,CAACc,IAAI,CAAC;MAC3BD,WAAW;MACXT,cAAc,EAAE,CAACI,EAAE,GAAG,IAAI,CAACJ,cAAc,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;IAChF,CAAC,CAAC;IACF;IACA,IAAI,CAACO,qBAAqB,CAAC,CAAC;EAChC;EACA;AACJ;AACA;EACIC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACf,gBAAgB,EAAE;MACvBgB,YAAY,CAAC,IAAI,CAAChB,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAG,IAAI;IAChC;IACA,IAAI,CAACC,UAAU,GAAG,IAAI;EAC1B;EACA;AACJ;AACA;EACIgB,cAAcA,CAAA,EAAG;IACb,IAAI,CAAChB,UAAU,GAAG,KAAK;EAC3B;EACA;AACJ;AACA;EACIa,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAACd,gBAAgB,EAAE;MACvBgB,YAAY,CAAC,IAAI,CAAChB,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAG,IAAI;IAChC;IACA,IAAI,CAACA,gBAAgB,GAAGkB,UAAU,CAAC,MAAM;MACrC,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC5B,CAAC;IACD;IACA;IACA;IACA;IACA,GAAG,CAAC;EACR;EACA;AACJ;AACA;AACA;AACA;EACIA,iBAAiBA,CAAA,EAAG;IAChB;IACA;IACA,IAAI,CAAC,IAAI,CAAClB,UAAU,EAAE;MAClB,IAAI,CAACE,cAAc,GAAGC,SAAS;IACnC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUgB,UAAUA,CAACC,EAAE,EAAEC,YAAY,GAAG,IAAI,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtC,MAAMC,QAAQ,GAAGF,KAAI,CAACG,kBAAkB,CAAC,CAAC;MAC1C,MAAMC,KAAK,GAAGF,QAAQ,CAACG,SAAS,CAAEC,OAAO,IAAKA,OAAO,CAACR,EAAE,KAAKA,EAAE,CAAC;MAChE,IAAIM,KAAK,KAAK,CAAC,CAAC,EACZ;MACJJ,KAAI,CAACpB,cAAc,GAAG,KAAK;MAC3BoB,KAAI,CAACT,qBAAqB,CAAC,CAAC;MAC5B,MAAMgB,YAAY,GAAGP,KAAI,CAACQ,EAAE,CAACC,WAAW;MACxCT,KAAI,CAACQ,EAAE,CAACE,QAAQ,CAAC;QACbC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAER,KAAK,GAAGG,YAAY;QAC1BM,QAAQ,EAAEd,YAAY,GAAG,QAAQ,GAAG;MACxC,CAAC,CAAC;IAAC;EACP;EACAI,kBAAkBA,CAAA,EAAG;IACjB,OAAOW,KAAK,CAACC,IAAI,CAAC,IAAI,CAACP,EAAE,CAACQ,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;EACtE;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEtC,QAAQ;MAAEC;IAAe,CAAC,GAAG,IAAI;IACzC,OAAQhB,CAAC,CAACE,IAAI,EAAE;MAAEoD,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,uBAAuB,EAAExC,QAAQ;QACjC,8BAA8B,EAAEC,cAAc,KAAK;MACvD;IAAE,CAAC,EAAEhB,CAAC,CAAC,MAAM,EAAE;MAAEsD,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;EACA,IAAIV,EAAEA,CAAA,EAAG;IAAE,OAAOxC,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDK,WAAW,CAAC+C,KAAK,GAAG;EAChBC,GAAG,EAAEnD,uBAAuB;EAC5BoD,EAAE,EAAElD;AACR,CAAC;AAED,SAASC,WAAW,IAAIkD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}