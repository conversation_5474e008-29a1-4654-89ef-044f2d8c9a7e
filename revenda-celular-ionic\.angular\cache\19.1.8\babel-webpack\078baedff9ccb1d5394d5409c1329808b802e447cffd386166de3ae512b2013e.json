{"ast": null, "code": "var _HomePageModule;\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { RouterModule } from '@angular/router';\nimport { HomePageRoutingModule } from './home-routing.module';\nimport { HomePage } from './home.page';\nimport * as i0 from \"@angular/core\";\nexport class HomePageModule {}\n_HomePageModule = HomePageModule;\n_HomePageModule.ɵfac = function HomePageModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HomePageModule)();\n};\n_HomePageModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _HomePageModule\n});\n_HomePageModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [CommonModule, FormsModule, IonicModule, HomePageRoutingModule, RouterModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HomePageModule, {\n    declarations: [HomePage],\n    imports: [CommonModule, FormsModule, IonicModule, HomePageRoutingModule, RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "IonicModule", "RouterModule", "HomePageRoutingModule", "HomePage", "HomePageModule", "declarations", "imports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\home\\home.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { RouterModule } from '@angular/router';\r\n\r\nimport { HomePageRoutingModule } from './home-routing.module';\r\nimport { HomePage } from './home.page';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    HomePageRoutingModule,\r\n    RouterModule\r\n  ],\r\n  declarations: [HomePage]\r\n})\r\nexport class HomePageModule {}\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,QAAQ,QAAQ,aAAa;;AAYtC,OAAM,MAAOC,cAAc;kBAAdA,cAAc;;mCAAdA,eAAc;AAAA;;QAAdA;AAAc;;YARvBN,YAAY,EACZC,WAAW,EACXC,WAAW,EACXE,qBAAqB,EACrBD,YAAY;AAAA;;2EAIHG,cAAc;IAAAC,YAAA,GAFVF,QAAQ;IAAAG,OAAA,GANrBR,YAAY,EACZC,WAAW,EACXC,WAAW,EACXE,qBAAqB,EACrBD,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}