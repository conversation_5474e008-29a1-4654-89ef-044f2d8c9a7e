{"ast": null, "code": "var _AccessoryService;\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AccessoryService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.baseUrl}/accessories`;\n  }\n  getById(accessoryId) {\n    return this.http.get(`${this.apiUrl}/${accessoryId}`);\n  }\n  getList() {\n    return this.http.get(this.apiUrl);\n  }\n  getByCategory(category) {\n    return this.http.get(`${this.apiUrl}/category/${category}`);\n  }\n  getAvailableStock() {\n    return this.http.get(`${this.apiUrl}/stock/available`);\n  }\n  add(accessory) {\n    return this.http.post(this.apiUrl, accessory);\n  }\n  update(id, accessory) {\n    return this.http.patch(`${this.apiUrl}/${id}`, accessory);\n  }\n  updateStock(id, quantity) {\n    return this.http.patch(`${this.apiUrl}/${id}/stock`, {\n      quantity\n    });\n  }\n  save(accessory) {\n    if (accessory.id) {\n      var _accessory$compatible;\n      const updateData = {\n        name: accessory.name,\n        description: accessory.description,\n        price: typeof accessory.price === 'string' ? parseFloat(accessory.price) : accessory.price,\n        category: accessory.category,\n        image: accessory.image,\n        stock: accessory.stock,\n        compatiblePhoneIds: ((_accessory$compatible = accessory.compatiblePhones) === null || _accessory$compatible === void 0 ? void 0 : _accessory$compatible.map(phone => phone.id).filter(id => id !== undefined)) || []\n      };\n      return this.update(accessory.id, updateData);\n    } else {\n      var _accessory$compatible2;\n      const createData = {\n        name: accessory.name,\n        description: accessory.description,\n        price: typeof accessory.price === 'string' ? parseFloat(accessory.price) : accessory.price,\n        category: accessory.category,\n        image: accessory.image,\n        stock: accessory.stock,\n        compatiblePhoneIds: ((_accessory$compatible2 = accessory.compatiblePhones) === null || _accessory$compatible2 === void 0 ? void 0 : _accessory$compatible2.map(phone => phone.id)) || []\n      };\n      return this.add(createData);\n    }\n  }\n  remove(accessory) {\n    return this.http.delete(`${this.apiUrl}/${accessory.id}`);\n  }\n}\n_AccessoryService = AccessoryService;\n_AccessoryService.ɵfac = function AccessoryService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AccessoryService)(i0.ɵɵinject(i1.HttpClient));\n};\n_AccessoryService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _AccessoryService,\n  factory: _AccessoryService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["environment", "AccessoryService", "constructor", "http", "apiUrl", "baseUrl", "getById", "accessoryId", "get", "getList", "getByCategory", "category", "getAvailableStock", "add", "accessory", "post", "update", "id", "patch", "updateStock", "quantity", "save", "_accessory$compatible", "updateData", "name", "description", "price", "parseFloat", "image", "stock", "compatiblePhoneIds", "compatiblePhones", "map", "phone", "filter", "undefined", "_accessory$compatible2", "createData", "remove", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\accessories\\services\\accessory.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { Accessory, CreateAccessoryDto, UpdateAccessoryDto } from '../models/accessory.type';\r\nimport { environment } from '../../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AccessoryService {\r\n  private readonly apiUrl = `${environment.baseUrl}/accessories`;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getById(accessoryId: number): Observable<Accessory> {\r\n    return this.http.get<Accessory>(`${this.apiUrl}/${accessoryId}`);\r\n  }\r\n\r\n  getList(): Observable<Accessory[]> {\r\n    return this.http.get<Accessory[]>(this.apiUrl);\r\n  }\r\n\r\n  getByCategory(category: string): Observable<Accessory[]> {\r\n    return this.http.get<Accessory[]>(`${this.apiUrl}/category/${category}`);\r\n  }\r\n\r\n  getAvailableStock(): Observable<Accessory[]> {\r\n    return this.http.get<Accessory[]>(`${this.apiUrl}/stock/available`);\r\n  }\r\n\r\n  private add(accessory: CreateAccessoryDto): Observable<Accessory> {\r\n    return this.http.post<Accessory>(this.apiUrl, accessory);\r\n  }\r\n\r\n  private update(id: number, accessory: UpdateAccessoryDto): Observable<any> {\r\n    return this.http.patch(`${this.apiUrl}/${id}`, accessory);\r\n  }\r\n\r\n  updateStock(id: number, quantity: number): Observable<any> {\r\n    return this.http.patch(`${this.apiUrl}/${id}/stock`, { quantity });\r\n  }\r\n\r\n  save(accessory: Accessory): Observable<any> {\r\n    if (accessory.id) {\r\n      const updateData: UpdateAccessoryDto = {\r\n        name: accessory.name,\r\n        description: accessory.description,\r\n        price: typeof accessory.price === 'string' ? parseFloat(accessory.price) : accessory.price,\r\n        category: accessory.category,\r\n        image: accessory.image,\r\n        stock: accessory.stock,\r\n        compatiblePhoneIds: accessory.compatiblePhones?.map(phone => phone.id).filter((id): id is number => id !== undefined) || []\r\n      };\r\n      return this.update(accessory.id, updateData);\r\n    } else {\r\n      const createData: CreateAccessoryDto = {\r\n        name: accessory.name,\r\n        description: accessory.description,\r\n        price: typeof accessory.price === 'string' ? parseFloat(accessory.price) : accessory.price,\r\n        category: accessory.category,\r\n        image: accessory.image,\r\n        stock: accessory.stock,\r\n        compatiblePhoneIds: accessory.compatiblePhones?.map(phone => phone.id) || []\r\n      };\r\n      return this.add(createData);\r\n    }\r\n  }\r\n\r\n  remove(accessory: Accessory): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/${accessory.id}`);\r\n  }\r\n}\r\n"], "mappings": ";AAIA,SAASA,WAAW,QAAQ,mCAAmC;;;AAK/D,OAAM,MAAOC,gBAAgB;EAG3BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,OAAO,cAAc;EAEtB;EAExCC,OAAOA,CAACC,WAAmB;IACzB,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAY,GAAG,IAAI,CAACJ,MAAM,IAAIG,WAAW,EAAE,CAAC;EAClE;EAEAE,OAAOA,CAAA;IACL,OAAO,IAAI,CAACN,IAAI,CAACK,GAAG,CAAc,IAAI,CAACJ,MAAM,CAAC;EAChD;EAEAM,aAAaA,CAACC,QAAgB;IAC5B,OAAO,IAAI,CAACR,IAAI,CAACK,GAAG,CAAc,GAAG,IAAI,CAACJ,MAAM,aAAaO,QAAQ,EAAE,CAAC;EAC1E;EAEAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACT,IAAI,CAACK,GAAG,CAAc,GAAG,IAAI,CAACJ,MAAM,kBAAkB,CAAC;EACrE;EAEQS,GAAGA,CAACC,SAA6B;IACvC,OAAO,IAAI,CAACX,IAAI,CAACY,IAAI,CAAY,IAAI,CAACX,MAAM,EAAEU,SAAS,CAAC;EAC1D;EAEQE,MAAMA,CAACC,EAAU,EAAEH,SAA6B;IACtD,OAAO,IAAI,CAACX,IAAI,CAACe,KAAK,CAAC,GAAG,IAAI,CAACd,MAAM,IAAIa,EAAE,EAAE,EAAEH,SAAS,CAAC;EAC3D;EAEAK,WAAWA,CAACF,EAAU,EAAEG,QAAgB;IACtC,OAAO,IAAI,CAACjB,IAAI,CAACe,KAAK,CAAC,GAAG,IAAI,CAACd,MAAM,IAAIa,EAAE,QAAQ,EAAE;MAAEG;IAAQ,CAAE,CAAC;EACpE;EAEAC,IAAIA,CAACP,SAAoB;IACvB,IAAIA,SAAS,CAACG,EAAE,EAAE;MAAA,IAAAK,qBAAA;MAChB,MAAMC,UAAU,GAAuB;QACrCC,IAAI,EAAEV,SAAS,CAACU,IAAI;QACpBC,WAAW,EAAEX,SAAS,CAACW,WAAW;QAClCC,KAAK,EAAE,OAAOZ,SAAS,CAACY,KAAK,KAAK,QAAQ,GAAGC,UAAU,CAACb,SAAS,CAACY,KAAK,CAAC,GAAGZ,SAAS,CAACY,KAAK;QAC1Ff,QAAQ,EAAEG,SAAS,CAACH,QAAQ;QAC5BiB,KAAK,EAAEd,SAAS,CAACc,KAAK;QACtBC,KAAK,EAAEf,SAAS,CAACe,KAAK;QACtBC,kBAAkB,EAAE,EAAAR,qBAAA,GAAAR,SAAS,CAACiB,gBAAgB,cAAAT,qBAAA,uBAA1BA,qBAAA,CAA4BU,GAAG,CAACC,KAAK,IAAIA,KAAK,CAAChB,EAAE,CAAC,CAACiB,MAAM,CAAEjB,EAAE,IAAmBA,EAAE,KAAKkB,SAAS,CAAC,KAAI;OAC1H;MACD,OAAO,IAAI,CAACnB,MAAM,CAACF,SAAS,CAACG,EAAE,EAAEM,UAAU,CAAC;IAC9C,CAAC,MAAM;MAAA,IAAAa,sBAAA;MACL,MAAMC,UAAU,GAAuB;QACrCb,IAAI,EAAEV,SAAS,CAACU,IAAI;QACpBC,WAAW,EAAEX,SAAS,CAACW,WAAW;QAClCC,KAAK,EAAE,OAAOZ,SAAS,CAACY,KAAK,KAAK,QAAQ,GAAGC,UAAU,CAACb,SAAS,CAACY,KAAK,CAAC,GAAGZ,SAAS,CAACY,KAAK;QAC1Ff,QAAQ,EAAEG,SAAS,CAACH,QAAQ;QAC5BiB,KAAK,EAAEd,SAAS,CAACc,KAAK;QACtBC,KAAK,EAAEf,SAAS,CAACe,KAAK;QACtBC,kBAAkB,EAAE,EAAAM,sBAAA,GAAAtB,SAAS,CAACiB,gBAAgB,cAAAK,sBAAA,uBAA1BA,sBAAA,CAA4BJ,GAAG,CAACC,KAAK,IAAIA,KAAK,CAAChB,EAAE,CAAC,KAAI;OAC3E;MACD,OAAO,IAAI,CAACJ,GAAG,CAACwB,UAAU,CAAC;IAC7B;EACF;EAEAC,MAAMA,CAACxB,SAAoB;IACzB,OAAO,IAAI,CAACX,IAAI,CAACoC,MAAM,CAAC,GAAG,IAAI,CAACnC,MAAM,IAAIU,SAAS,CAACG,EAAE,EAAE,CAAC;EAC3D;;oBA7DWhB,gBAAgB;;mCAAhBA,iBAAgB,EAAAuC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAhB1C,iBAAgB;EAAA2C,OAAA,EAAhB3C,iBAAgB,CAAA4C,IAAA;EAAAC,UAAA,EAFf;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}