{"ast": null, "code": "var _SaleFormComponent;\nimport { Validators } from '@angular/forms';\nimport { PaymentMethods, SaleStatus } from '../models/sale.type';\nimport { priceMask, maskitoElement, parseNumberMask, formatNumberMask } from 'src/app/core/constants/mask.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../services/sale.service\";\nimport * as i3 from \"src/app/customers/services/customer.service\";\nimport * as i4 from \"src/app/stores/services/store.service\";\nimport * as i5 from \"src/app/phones/services/phone.service\";\nimport * as i6 from \"src/app/accessories/services/accessory.service\";\nimport * as i7 from \"@angular/router\";\nimport * as i8 from \"@ionic/angular\";\nimport * as i9 from \"@maskito/angular\";\nimport * as i10 from \"@angular/common\";\nconst _forTrack0 = ($index, $item) => $item.id;\nconst _forTrack1 = ($index, $item) => $item.value;\nfunction SaleFormComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" A data da venda \\u00E9 obrigat\\u00F3ria \");\n  }\n}\nfunction SaleFormComponent_For_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const customer_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", customer_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(customer_r1.name);\n  }\n}\nfunction SaleFormComponent_Conditional_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O cliente \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction SaleFormComponent_For_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const store_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", store_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(store_r2.name);\n  }\n}\nfunction SaleFormComponent_Conditional_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" A loja \\u00E9 obrigat\\u00F3ria \");\n  }\n}\nfunction SaleFormComponent_For_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const method_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", method_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(method_r3.label);\n  }\n}\nfunction SaleFormComponent_Conditional_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O m\\u00E9todo de pagamento \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction SaleFormComponent_For_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r4.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(status_r4.label);\n  }\n}\nfunction SaleFormComponent_Conditional_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O status \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction SaleFormComponent_Conditional_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O vendedor \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction SaleFormComponent_For_50_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O tipo de produto \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction SaleFormComponent_For_50_Conditional_11_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"currency\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const phone_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", phone_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", phone_r6.model, \" - \", i0.ɵɵpipeBind2(2, 3, phone_r6.price, \"BRL\"), \"\");\n  }\n}\nfunction SaleFormComponent_For_50_Conditional_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, SaleFormComponent_For_50_Conditional_11_For_1_Template, 3, 6, \"ion-select-option\", 9, _forTrack0);\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵrepeater(ctx_r6.phonesList);\n  }\n}\nfunction SaleFormComponent_For_50_Conditional_12_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"currency\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const accessory_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", accessory_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", accessory_r8.name, \" - \", i0.ɵɵpipeBind2(2, 3, accessory_r8.price, \"BRL\"), \"\");\n  }\n}\nfunction SaleFormComponent_For_50_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, SaleFormComponent_For_50_Conditional_12_For_1_Template, 3, 6, \"ion-select-option\", 9, _forTrack0);\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵrepeater(ctx_r6.accessoriesList);\n  }\n}\nfunction SaleFormComponent_For_50_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O produto \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction SaleFormComponent_For_50_Conditional_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" A quantidade \\u00E9 obrigat\\u00F3ria \");\n  }\n}\nfunction SaleFormComponent_For_50_Conditional_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" A quantidade deve ser pelo menos 1 \");\n  }\n}\nfunction SaleFormComponent_For_50_Conditional_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O pre\\u00E7o unit\\u00E1rio \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction SaleFormComponent_For_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"ion-item\")(2, \"ion-select\", 22)(3, \"ion-select-option\", 23);\n    i0.ɵɵtext(4, \"Celular\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"ion-select-option\", 24);\n    i0.ɵɵtext(6, \"Acess\\u00F3rio\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtemplate(8, SaleFormComponent_For_50_Conditional_8_Template, 1, 0);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"ion-item\")(10, \"ion-select\", 25);\n    i0.ɵɵtemplate(11, SaleFormComponent_For_50_Conditional_11_Template, 2, 0)(12, SaleFormComponent_For_50_Conditional_12_Template, 2, 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\");\n    i0.ɵɵtemplate(14, SaleFormComponent_For_50_Conditional_14_Template, 1, 0);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"ion-item\");\n    i0.ɵɵelement(16, \"ion-input\", 26);\n    i0.ɵɵelementStart(17, \"p\");\n    i0.ɵɵtemplate(18, SaleFormComponent_For_50_Conditional_18_Template, 1, 0)(19, SaleFormComponent_For_50_Conditional_19_Template, 1, 0);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"ion-item\");\n    i0.ɵɵelement(21, \"ion-input\", 27);\n    i0.ɵɵelementStart(22, \"p\");\n    i0.ɵɵtemplate(23, SaleFormComponent_For_50_Conditional_23_Template, 1, 0);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"ion-item\");\n    i0.ɵɵelement(25, \"ion-input\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 29)(27, \"ion-button\", 30);\n    i0.ɵɵlistener(\"click\", function SaleFormComponent_For_50_Template_ion_button_click_27_listener() {\n      const ɵ$index_96_r9 = i0.ɵɵrestoreView(_r5).$index;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.removeItem(ɵ$index_96_r9));\n    });\n    i0.ɵɵelement(28, \"ion-icon\", 31);\n    i0.ɵɵtext(29, \" Remover \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ɵ$index_96_r9 = ctx.$index;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroupName\", ɵ$index_96_r9);\n    i0.ɵɵadvance(8);\n    i0.ɵɵconditional(ctx_r6.hasItemError(ɵ$index_96_r9, \"productType\", \"required\") ? 8 : -1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"compareWith\", ctx_r6.compareWith);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r6.getProductType(ɵ$index_96_r9) === \"phone\" ? 11 : 12);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(ctx_r6.hasItemError(ɵ$index_96_r9, \"product\", \"required\") ? 14 : -1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵconditional(ctx_r6.hasItemError(ɵ$index_96_r9, \"quantity\", \"required\") ? 18 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r6.hasItemError(ɵ$index_96_r9, \"quantity\", \"min\") ? 19 : -1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"maskito\", ctx_r6.priceMask)(\"maskitoElement\", ctx_r6.maskitoElement);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r6.hasItemError(ɵ$index_96_r9, \"unitPrice\", \"required\") ? 23 : -1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"maskito\", ctx_r6.priceMask)(\"maskitoElement\", ctx_r6.maskitoElement);\n  }\n}\nfunction SaleFormComponent_ForEmpty_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\")(1, \"ion-label\", 32);\n    i0.ɵɵtext(2, \"Nenhum item adicionado\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class SaleFormComponent {\n  constructor(fb, saleService, customerService, storeService, phoneService, accessoryService, route, router, toastController) {\n    this.fb = fb;\n    this.saleService = saleService;\n    this.customerService = customerService;\n    this.storeService = storeService;\n    this.phoneService = phoneService;\n    this.accessoryService = accessoryService;\n    this.route = route;\n    this.router = router;\n    this.toastController = toastController;\n    this.saleId = null;\n    this.customersList = [];\n    this.storesList = [];\n    this.phonesList = [];\n    this.accessoriesList = [];\n    this.paymentMethods = PaymentMethods;\n    this.saleStatus = SaleStatus;\n    this.priceMask = priceMask;\n    this.maskitoElement = maskitoElement;\n    this.saleForm = this.createForm();\n    this.maxDate = new Date().toISOString().split('T')[0];\n  }\n  ngOnInit() {\n    this.loadCustomers();\n    this.loadStores();\n    this.loadPhones();\n    this.loadAccessories();\n    this.route.paramMap.subscribe(params => {\n      const id = params.get('id');\n      if (id) {\n        this.saleId = id;\n        this.loadSale(this.saleId);\n      } else {\n        var _this$saleForm$get;\n        (_this$saleForm$get = this.saleForm.get('date')) === null || _this$saleForm$get === void 0 || _this$saleForm$get.setValue(new Date().toISOString().split('T')[0]);\n      }\n    });\n    this.itemsFormArray.valueChanges.subscribe(() => {\n      this.updateTotals();\n    });\n  }\n  createForm() {\n    return this.fb.group({\n      date: ['', Validators.required],\n      customer: ['', Validators.required],\n      store: ['', Validators.required],\n      paymentMethod: ['', Validators.required],\n      status: ['pending', Validators.required],\n      seller: ['', Validators.required],\n      items: this.fb.array([]),\n      totalValue: [0]\n    });\n  }\n  get itemsFormArray() {\n    return this.saleForm.get('items');\n  }\n  createItemForm() {\n    var _itemForm$get, _itemForm$get3, _itemForm$get4;\n    const itemForm = this.fb.group({\n      productType: ['phone', Validators.required],\n      product: ['', Validators.required],\n      quantity: [1, [Validators.required, Validators.min(1)]],\n      unitPrice: ['0', Validators.required],\n      subtotal: ['0']\n    });\n    (_itemForm$get = itemForm.get('product')) === null || _itemForm$get === void 0 || _itemForm$get.valueChanges.subscribe(product => {\n      if (product && typeof product === 'object' && 'price' in product) {\n        var _itemForm$get2;\n        const price = product.price ? formatNumberMask(parseFloat(product.price)) : '0';\n        (_itemForm$get2 = itemForm.get('unitPrice')) === null || _itemForm$get2 === void 0 || _itemForm$get2.setValue(price);\n        this.updateItemSubtotal(itemForm);\n      }\n    });\n    (_itemForm$get3 = itemForm.get('quantity')) === null || _itemForm$get3 === void 0 || _itemForm$get3.valueChanges.subscribe(() => {\n      this.updateItemSubtotal(itemForm);\n    });\n    (_itemForm$get4 = itemForm.get('unitPrice')) === null || _itemForm$get4 === void 0 || _itemForm$get4.valueChanges.subscribe(() => {\n      this.updateItemSubtotal(itemForm);\n    });\n    return itemForm;\n  }\n  addItem() {\n    this.itemsFormArray.push(this.createItemForm());\n  }\n  removeItem(index) {\n    this.itemsFormArray.removeAt(index);\n  }\n  getProductType(index) {\n    var _this$itemsFormArray$;\n    return ((_this$itemsFormArray$ = this.itemsFormArray.at(index).get('productType')) === null || _this$itemsFormArray$ === void 0 ? void 0 : _this$itemsFormArray$.value) || 'phone';\n  }\n  loadSale(id) {\n    this.saleService.getById(+id).subscribe({\n      next: sale => {\n        Promise.all([this.customerService.getAll().toPromise(), this.storeService.getList().toPromise(), this.phoneService.getList().toPromise(), this.accessoryService.getList().toPromise()]).then(() => {\n          while (this.itemsFormArray.length) {\n            this.itemsFormArray.removeAt(0);\n          }\n          sale.items.forEach(item => {\n            const itemForm = this.createItemForm();\n            itemForm.patchValue({\n              productType: item.productType,\n              product: item.product,\n              quantity: item.quantity,\n              unitPrice: formatNumberMask(item.unitPrice),\n              subtotal: formatNumberMask(item.subtotal)\n            });\n            this.itemsFormArray.push(itemForm);\n          });\n          this.saleForm.patchValue({\n            date: new Date(sale.date).toISOString().split('T')[0],\n            customer: sale.customer,\n            store: sale.store,\n            paymentMethod: sale.paymentMethod,\n            status: sale.status,\n            seller: sale.seller,\n            totalValue: formatNumberMask(sale.totalValue)\n          });\n        });\n      },\n      error: error => {\n        console.error('Erro ao carregar venda', error);\n      }\n    });\n  }\n  loadCustomers() {\n    this.customerService.getAll().subscribe({\n      next: customers => {\n        this.customersList = customers;\n      },\n      error: error => {\n        console.error('Erro ao carregar clientes', error);\n      }\n    });\n  }\n  loadStores() {\n    this.storeService.getList().subscribe({\n      next: stores => {\n        this.storesList = stores;\n      },\n      error: error => {\n        console.error('Erro ao carregar lojas', error);\n      }\n    });\n  }\n  loadPhones() {\n    this.phoneService.getList().subscribe({\n      next: phones => {\n        this.phonesList = phones;\n      },\n      error: error => {\n        console.error('Erro ao carregar celulares', error);\n      }\n    });\n  }\n  loadAccessories() {\n    this.accessoryService.getList().subscribe({\n      next: accessories => {\n        this.accessoriesList = accessories;\n      },\n      error: error => {\n        console.error('Erro ao carregar acessórios', error);\n      }\n    });\n  }\n  updateItemSubtotal(itemForm) {\n    var _itemForm$get5, _itemForm$get6, _itemForm$get7;\n    const quantity = +((_itemForm$get5 = itemForm.get('quantity')) === null || _itemForm$get5 === void 0 ? void 0 : _itemForm$get5.value) || 0;\n    const unitPrice = parseNumberMask((_itemForm$get6 = itemForm.get('unitPrice')) === null || _itemForm$get6 === void 0 ? void 0 : _itemForm$get6.value) || 0;\n    const subtotal = quantity * unitPrice;\n    (_itemForm$get7 = itemForm.get('subtotal')) === null || _itemForm$get7 === void 0 || _itemForm$get7.setValue(formatNumberMask(subtotal), {\n      emitEvent: false\n    });\n    this.updateTotals();\n  }\n  updateTotals() {\n    var _this$saleForm$get2;\n    let total = 0;\n    for (let i = 0; i < this.itemsFormArray.length; i++) {\n      var _itemForm$get8;\n      const itemForm = this.itemsFormArray.at(i);\n      const subtotalValue = parseNumberMask((_itemForm$get8 = itemForm.get('subtotal')) === null || _itemForm$get8 === void 0 ? void 0 : _itemForm$get8.value) || 0;\n      total += subtotalValue;\n    }\n    (_this$saleForm$get2 = this.saleForm.get('totalValue')) === null || _this$saleForm$get2 === void 0 || _this$saleForm$get2.setValue(formatNumberMask(total));\n  }\n  compareWith(o1, o2) {\n    return o1 && o2 ? o1.id === o2.id : o1 === o2;\n  }\n  save() {\n    var _formValue$customer, _formValue$store;\n    if (this.saleForm.invalid || this.itemsFormArray.length === 0) {\n      console.log('Form invalid or no items:', {\n        invalid: this.saleForm.invalid,\n        errors: this.saleForm.errors,\n        itemsLength: this.itemsFormArray.length\n      });\n      return;\n    }\n    const formValue = this.saleForm.value;\n    console.log('Form value:', formValue);\n    const items = formValue.items.map(item => {\n      var _item$product;\n      return {\n        productId: ((_item$product = item.product) === null || _item$product === void 0 ? void 0 : _item$product.id) || 0,\n        productType: item.productType,\n        quantity: +item.quantity,\n        unitPrice: parseNumberMask(item.unitPrice),\n        subtotal: parseNumberMask(item.subtotal)\n      };\n    });\n    console.log('Mapped items:', items);\n    const sale = {\n      ...(this.saleId ? {\n        id: +this.saleId\n      } : {}),\n      date: formValue.date,\n      customerId: ((_formValue$customer = formValue.customer) === null || _formValue$customer === void 0 ? void 0 : _formValue$customer.id) || 0,\n      storeId: ((_formValue$store = formValue.store) === null || _formValue$store === void 0 ? void 0 : _formValue$store.id) || 0,\n      customer: formValue.customer,\n      store: formValue.store,\n      paymentMethod: formValue.paymentMethod,\n      status: formValue.status,\n      seller: formValue.seller,\n      items: items,\n      totalValue: parseNumberMask(formValue.totalValue)\n    };\n    console.log('Sale object to save:', sale);\n    this.saleService.save(sale).subscribe({\n      next: () => {\n        this.toastController.create({\n          message: 'Venda salva com sucesso!',\n          duration: 3000,\n          color: 'success'\n        }).then(toast => toast.present());\n        this.router.navigate(['/sales']);\n      },\n      error: error => {\n        console.error('Erro ao salvar venda', error);\n        this.toastController.create({\n          message: 'Erro ao salvar venda',\n          duration: 3000,\n          color: 'danger'\n        }).then(toast => toast.present());\n      }\n    });\n  }\n  hasError(field, error) {\n    var _formControl$errors;\n    const formControl = this.saleForm.get(field);\n    return !!(formControl !== null && formControl !== void 0 && formControl.touched) && !!(formControl !== null && formControl !== void 0 && (_formControl$errors = formControl.errors) !== null && _formControl$errors !== void 0 && _formControl$errors[error]);\n  }\n  hasItemError(index, field, error) {\n    var _formControl$errors2;\n    const itemFormGroup = this.itemsFormArray.at(index);\n    const formControl = itemFormGroup.get(field);\n    return !!(formControl !== null && formControl !== void 0 && formControl.touched) && !!(formControl !== null && formControl !== void 0 && (_formControl$errors2 = formControl.errors) !== null && _formControl$errors2 !== void 0 && _formControl$errors2[error]);\n  }\n}\n_SaleFormComponent = SaleFormComponent;\n_SaleFormComponent.ɵfac = function SaleFormComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SaleFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.SaleService), i0.ɵɵdirectiveInject(i3.CustomerService), i0.ɵɵdirectiveInject(i4.StoreService), i0.ɵɵdirectiveInject(i5.PhoneService), i0.ɵɵdirectiveInject(i6.AccessoryService), i0.ɵɵdirectiveInject(i7.ActivatedRoute), i0.ɵɵdirectiveInject(i7.Router), i0.ɵɵdirectiveInject(i8.ToastController));\n};\n_SaleFormComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _SaleFormComponent,\n  selectors: [[\"app-sale-form\"]],\n  standalone: false,\n  decls: 59,\n  vars: 17,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [\"defaultHref\", \"/sales\"], [3, \"fullscreen\"], [1, \"form-container\"], [3, \"ngSubmit\", \"formGroup\"], [\"type\", \"date\", \"formControlName\", \"date\", \"labelPlacement\", \"floating\", \"label\", \"Data da Venda\", 3, \"max\"], [\"formControlName\", \"customer\", \"labelPlacement\", \"floating\", \"label\", \"Cliente\", 3, \"compareWith\"], [3, \"value\"], [\"formControlName\", \"store\", \"labelPlacement\", \"floating\", \"label\", \"Loja\", 3, \"compareWith\"], [\"formControlName\", \"paymentMethod\", \"labelPlacement\", \"floating\", \"label\", \"M\\u00E9todo de Pagamento\"], [\"formControlName\", \"status\", \"labelPlacement\", \"floating\", \"label\", \"Status\"], [\"formControlName\", \"seller\", \"labelPlacement\", \"floating\", \"label\", \"Vendedor\", \"type\", \"text\"], [\"slot\", \"end\", \"size\", \"small\", \"fill\", \"outline\", 1, \"add-item-button\", 3, \"click\"], [\"name\", \"add\"], [\"formArrayName\", \"items\"], [1, \"item-form\", 3, \"formGroupName\"], [\"formControlName\", \"totalValue\", \"labelPlacement\", \"floating\", \"label\", \"Valor Total\", \"type\", \"text\", \"readonly\", \"\", 3, \"maskito\", \"maskitoElement\"], [1, \"form-buttons\"], [\"expand\", \"block\", \"type\", \"submit\", 3, \"disabled\"], [\"expand\", \"block\", \"fill\", \"outline\", \"routerLink\", \"/sales\"], [\"formControlName\", \"productType\", \"labelPlacement\", \"floating\", \"label\", \"Tipo de Produto\"], [\"value\", \"phone\"], [\"value\", \"accessory\"], [\"formControlName\", \"product\", \"labelPlacement\", \"floating\", \"label\", \"Produto\", 3, \"compareWith\"], [\"formControlName\", \"quantity\", \"labelPlacement\", \"floating\", \"label\", \"Quantidade\", \"type\", \"number\", \"min\", \"1\"], [\"formControlName\", \"unitPrice\", \"labelPlacement\", \"floating\", \"label\", \"Pre\\u00E7o Unit\\u00E1rio\", \"type\", \"text\", 3, \"maskito\", \"maskitoElement\"], [\"formControlName\", \"subtotal\", \"labelPlacement\", \"floating\", \"label\", \"Subtotal\", \"type\", \"text\", \"readonly\", \"\", 3, \"maskito\", \"maskitoElement\"], [1, \"item-actions\"], [\"fill\", \"clear\", \"color\", \"danger\", 3, \"click\"], [\"name\", \"trash\"], [\"color\", \"medium\"]],\n  template: function SaleFormComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-back-button\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 4)(7, \"div\", 5)(8, \"form\", 6);\n      i0.ɵɵlistener(\"ngSubmit\", function SaleFormComponent_Template_form_ngSubmit_8_listener() {\n        return ctx.save();\n      });\n      i0.ɵɵelementStart(9, \"ion-list\")(10, \"ion-item\");\n      i0.ɵɵelement(11, \"ion-input\", 7);\n      i0.ɵɵelementStart(12, \"p\");\n      i0.ɵɵtemplate(13, SaleFormComponent_Conditional_13_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(14, \"ion-item\")(15, \"ion-select\", 8);\n      i0.ɵɵrepeaterCreate(16, SaleFormComponent_For_17_Template, 2, 2, \"ion-select-option\", 9, _forTrack0);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"p\");\n      i0.ɵɵtemplate(19, SaleFormComponent_Conditional_19_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(20, \"ion-item\")(21, \"ion-select\", 10);\n      i0.ɵɵrepeaterCreate(22, SaleFormComponent_For_23_Template, 2, 2, \"ion-select-option\", 9, _forTrack0);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"p\");\n      i0.ɵɵtemplate(25, SaleFormComponent_Conditional_25_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(26, \"ion-item\")(27, \"ion-select\", 11);\n      i0.ɵɵrepeaterCreate(28, SaleFormComponent_For_29_Template, 2, 2, \"ion-select-option\", 9, _forTrack1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"p\");\n      i0.ɵɵtemplate(31, SaleFormComponent_Conditional_31_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(32, \"ion-item\")(33, \"ion-select\", 12);\n      i0.ɵɵrepeaterCreate(34, SaleFormComponent_For_35_Template, 2, 2, \"ion-select-option\", 9, _forTrack1);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(36, \"p\");\n      i0.ɵɵtemplate(37, SaleFormComponent_Conditional_37_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(38, \"ion-item\");\n      i0.ɵɵelement(39, \"ion-input\", 13);\n      i0.ɵɵelementStart(40, \"p\");\n      i0.ɵɵtemplate(41, SaleFormComponent_Conditional_41_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(42, \"ion-item-divider\")(43, \"ion-label\");\n      i0.ɵɵtext(44, \"Itens da Venda\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(45, \"ion-button\", 14);\n      i0.ɵɵlistener(\"click\", function SaleFormComponent_Template_ion_button_click_45_listener() {\n        return ctx.addItem();\n      });\n      i0.ɵɵelement(46, \"ion-icon\", 15);\n      i0.ɵɵtext(47, \" Adicionar Item \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(48, \"div\", 16);\n      i0.ɵɵrepeaterCreate(49, SaleFormComponent_For_50_Template, 30, 12, \"div\", 17, i0.ɵɵrepeaterTrackByIndex, false, SaleFormComponent_ForEmpty_51_Template, 3, 0, \"ion-item\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(52, \"ion-item\");\n      i0.ɵɵelement(53, \"ion-input\", 18);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(54, \"div\", 19)(55, \"ion-button\", 20);\n      i0.ɵɵtext(56, \" Salvar \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(57, \"ion-button\", 21);\n      i0.ɵɵtext(58, \" Cancelar \");\n      i0.ɵɵelementEnd()()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵtextInterpolate(ctx.saleId ? \"Editar Venda\" : \"Nova Venda\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.saleForm);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"max\", ctx.maxDate);\n      i0.ɵɵadvance(2);\n      i0.ɵɵconditional(ctx.hasError(\"date\", \"required\") ? 13 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"compareWith\", ctx.compareWith);\n      i0.ɵɵadvance();\n      i0.ɵɵrepeater(ctx.customersList);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"customer\", \"required\") ? 19 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"compareWith\", ctx.compareWith);\n      i0.ɵɵadvance();\n      i0.ɵɵrepeater(ctx.storesList);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"store\", \"required\") ? 25 : -1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵrepeater(ctx.paymentMethods);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"paymentMethod\", \"required\") ? 31 : -1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵrepeater(ctx.saleStatus);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"status\", \"required\") ? 37 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"seller\", \"required\") ? 41 : -1);\n      i0.ɵɵadvance(8);\n      i0.ɵɵrepeater(ctx.itemsFormArray.controls);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"maskito\", ctx.priceMask)(\"maskitoElement\", ctx.maskitoElement);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"disabled\", ctx.saleForm.invalid || ctx.itemsFormArray.length === 0);\n    }\n  },\n  dependencies: [i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i8.IonButton, i8.IonButtons, i8.IonContent, i8.IonHeader, i8.IonIcon, i8.IonInput, i8.IonItem, i8.IonItemDivider, i8.IonLabel, i8.IonList, i8.IonSelect, i8.IonSelectOption, i8.IonTitle, i8.IonToolbar, i8.NumericValueAccessor, i8.SelectValueAccessor, i8.TextValueAccessor, i8.IonBackButton, i8.RouterLinkDelegate, i8.IonMinValidator, i7.RouterLink, i9.MaskitoDirective, i10.CurrencyPipe],\n  styles: [\".form-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n.form-buttons[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 10px;\\n  justify-content: flex-end;\\n}\\n\\n.item-form[_ngcontent-%COMP%] {\\n  border: 1px solid var(--ion-color-light);\\n  border-radius: 12px;\\n  padding: 16px;\\n  margin-bottom: 20px;\\n  background-color: rgba(var(--ion-color-light-rgb), 0.5);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n\\n.item-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  margin-top: 16px;\\n  gap: 10px;\\n}\\n\\nion-item-divider[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n  margin-bottom: 16px;\\n  --background: var(--ion-color-secondary);\\n  --color: var(--ion-color-secondary-contrast);\\n  border-radius: 8px;\\n  padding: 8px 16px;\\n}\\nion-item-divider[_ngcontent-%COMP%]   .add-item-button[_ngcontent-%COMP%] {\\n  --background: transparent;\\n  --color: var(--ion-color-secondary-contrast);\\n  --border-color: var(--ion-color-secondary-contrast);\\n  --border-width: 1px;\\n  --border-style: solid;\\n  --border-radius: 6px;\\n  --padding-start: 12px;\\n  --padding-end: 12px;\\n}\\n\\nion-item[_ngcontent-%COMP%] {\\n  --padding-start: 0;\\n  margin-bottom: 12px;\\n  --background: transparent;\\n}\\nion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--ion-color-danger);\\n  padding-left: 16px;\\n  margin: 4px 0;\\n}\\n\\nion-input[_ngcontent-%COMP%], ion-select[_ngcontent-%COMP%] {\\n  --background: var(--ion-background-color);\\n  --padding-start: 16px;\\n  --padding-end: 16px;\\n  --padding-top: 12px;\\n  --padding-bottom: 12px;\\n  --border-radius: 8px;\\n  --border-width: 1px;\\n  --border-color: var(--ion-color-light-shade);\\n  margin-top: 4px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["Validators", "PaymentMethods", "SaleStatus", "priceMask", "maskitoElement", "parseNumberMask", "formatNumberMask", "i0", "ɵɵtext", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵproperty", "customer_r1", "ɵɵadvance", "ɵɵtextInterpolate", "name", "store_r2", "method_r3", "value", "label", "status_r4", "phone_r6", "ɵɵtextInterpolate2", "model", "ɵɵpipeBind2", "price", "ɵɵrepeaterCreate", "SaleFormComponent_For_50_Conditional_11_For_1_Template", "_forTrack0", "ɵɵrepeater", "ctx_r6", "phonesList", "accessory_r8", "SaleFormComponent_For_50_Conditional_12_For_1_Template", "accessoriesList", "ɵɵtemplate", "SaleFormComponent_For_50_Conditional_8_Template", "SaleFormComponent_For_50_Conditional_11_Template", "SaleFormComponent_For_50_Conditional_12_Template", "SaleFormComponent_For_50_Conditional_14_Template", "ɵɵelement", "SaleFormComponent_For_50_Conditional_18_Template", "SaleFormComponent_For_50_Conditional_19_Template", "SaleFormComponent_For_50_Conditional_23_Template", "ɵɵlistener", "SaleFormComponent_For_50_Template_ion_button_click_27_listener", "ɵ$index_96_r9", "ɵɵrestoreView", "_r5", "$index", "ɵɵnextContext", "ɵɵresetView", "removeItem", "ɵɵconditional", "hasItemError", "compareWith", "getProductType", "SaleFormComponent", "constructor", "fb", "saleService", "customerService", "storeService", "phoneService", "accessoryService", "route", "router", "toastController", "saleId", "customersList", "storesList", "paymentMethods", "saleStatus", "saleForm", "createForm", "maxDate", "Date", "toISOString", "split", "ngOnInit", "loadCustomers", "loadStores", "loadPhones", "loadAccessories", "paramMap", "subscribe", "params", "id", "get", "loadSale", "_this$saleForm$get", "setValue", "itemsFormArray", "valueChanges", "updateTotals", "group", "date", "required", "customer", "store", "paymentMethod", "status", "seller", "items", "array", "totalValue", "createItemForm", "_itemForm$get", "_itemForm$get3", "_itemForm$get4", "itemForm", "productType", "product", "quantity", "min", "unitPrice", "subtotal", "_itemForm$get2", "parseFloat", "updateItemSubtotal", "addItem", "push", "index", "removeAt", "_this$itemsFormArray$", "at", "getById", "next", "sale", "Promise", "all", "getAll", "to<PERSON>romise", "getList", "then", "length", "for<PERSON>ach", "item", "patchValue", "error", "console", "customers", "stores", "phones", "accessories", "_itemForm$get5", "_itemForm$get6", "_itemForm$get7", "emitEvent", "_this$saleForm$get2", "total", "i", "_itemForm$get8", "subtotalValue", "o1", "o2", "save", "_formValue$customer", "_formValue$store", "invalid", "log", "errors", "itemsLength", "formValue", "map", "_item$product", "productId", "customerId", "storeId", "create", "message", "duration", "color", "toast", "present", "navigate", "<PERSON><PERSON><PERSON><PERSON>", "field", "_formControl$errors", "formControl", "touched", "_formControl$errors2", "itemFormGroup", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "SaleService", "i3", "CustomerService", "i4", "StoreService", "i5", "PhoneService", "i6", "AccessoryService", "i7", "ActivatedRoute", "Router", "i8", "ToastController", "selectors", "standalone", "decls", "vars", "consts", "template", "SaleFormComponent_Template", "rf", "ctx", "SaleFormComponent_Template_form_ngSubmit_8_listener", "SaleFormComponent_Conditional_13_Template", "SaleFormComponent_For_17_Template", "SaleFormComponent_Conditional_19_Template", "SaleFormComponent_For_23_Template", "SaleFormComponent_Conditional_25_Template", "SaleFormComponent_For_29_Template", "_forTrack1", "SaleFormComponent_Conditional_31_Template", "SaleFormComponent_For_35_Template", "SaleFormComponent_Conditional_37_Template", "SaleFormComponent_Conditional_41_Template", "SaleFormComponent_Template_ion_button_click_45_listener", "SaleFormComponent_For_50_Template", "ɵɵrepeaterTrackByIndex", "SaleFormComponent_ForEmpty_51_Template", "controls"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\sales\\sale-form\\sale-form.component.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\sales\\sale-form\\sale-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ToastController } from '@ionic/angular';\r\nimport { SaleService } from '../services/sale.service';\r\nimport { CustomerService } from 'src/app/customers/services/customer.service';\r\nimport { StoreService } from 'src/app/stores/services/store.service';\r\nimport { PhoneService } from 'src/app/phones/services/phone.service';\r\nimport { AccessoryService } from 'src/app/accessories/services/accessory.service';\r\nimport { Customer } from 'src/app/customers/models/customer.type';\r\nimport { Store } from 'src/app/stores/models/store.type';\r\nimport { Phone } from 'src/app/phones/models/phone.type';\r\nimport { Accessory } from 'src/app/accessories/models/accessory.type';\r\nimport { PaymentMethods, SaleStatus, Sale } from '../models/sale.type';\r\nimport { priceMask, maskitoElement, parseNumberMask, formatNumberMask } from 'src/app/core/constants/mask.constants';\r\n\r\n@Component({\r\n  selector: 'app-sale-form',\r\n  templateUrl: './sale-form.component.html',\r\n  styleUrls: ['./sale-form.component.scss'],\r\n  standalone: false,\r\n})\r\nexport class SaleFormComponent implements OnInit {\r\n  saleForm: FormGroup;\r\n  saleId: string | number | null = null;\r\n  customersList: Customer[] = [];\r\n  storesList: Store[] = [];\r\n  phonesList: Phone[] = [];\r\n  accessoriesList: Accessory[] = [];\r\n  paymentMethods = PaymentMethods;\r\n  saleStatus = SaleStatus;\r\n  priceMask = priceMask;\r\n  maskitoElement = maskitoElement;\r\n  maxDate: string;\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private saleService: SaleService,\r\n    private customerService: CustomerService,\r\n    private storeService: StoreService,\r\n    private phoneService: PhoneService,\r\n    private accessoryService: AccessoryService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private toastController: ToastController\r\n  ) {\r\n    this.saleForm = this.createForm();\r\n    this.maxDate = new Date().toISOString().split('T')[0];\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.loadCustomers();\r\n    this.loadStores();\r\n    this.loadPhones();\r\n    this.loadAccessories();\r\n    \r\n    this.route.paramMap.subscribe(params => {\r\n      const id = params.get('id');\r\n      if (id) {\r\n        this.saleId = id;\r\n        this.loadSale(this.saleId);\r\n      } else {\r\n        this.saleForm.get('date')?.setValue(new Date().toISOString().split('T')[0]);\r\n      }\r\n    });\r\n\r\n    this.itemsFormArray.valueChanges.subscribe(() => {\r\n      this.updateTotals();\r\n    });\r\n  }\r\n\r\n  createForm(): FormGroup {\r\n    return this.fb.group({\r\n      date: ['', Validators.required],\r\n      customer: ['', Validators.required],\r\n      store: ['', Validators.required],\r\n      paymentMethod: ['', Validators.required],\r\n      status: ['pending', Validators.required],\r\n      seller: ['', Validators.required],\r\n      items: this.fb.array([]),\r\n      totalValue: [0]\r\n    });\r\n  }\r\n\r\n  get itemsFormArray(): FormArray {\r\n    return this.saleForm.get('items') as FormArray;\r\n  }\r\n\r\n  createItemForm(): FormGroup {\r\n    const itemForm = this.fb.group({\r\n      productType: ['phone', Validators.required],\r\n      product: ['', Validators.required],\r\n      quantity: [1, [Validators.required, Validators.min(1)]],\r\n      unitPrice: ['0', Validators.required], \r\n      subtotal: ['0'] \r\n    });\r\n\r\n    \r\n    itemForm.get('product')?.valueChanges.subscribe((product: Phone | Accessory | any) => {\r\n      if (product && typeof product === 'object' && 'price' in product) {\r\n        const price = product.price ? formatNumberMask(parseFloat(product.price)) : '0';\r\n        itemForm.get('unitPrice')?.setValue(price);\r\n        \r\n        this.updateItemSubtotal(itemForm);\r\n      }\r\n    });\r\n\r\n    itemForm.get('quantity')?.valueChanges.subscribe(() => {\r\n      this.updateItemSubtotal(itemForm);\r\n    });\r\n    \r\n    itemForm.get('unitPrice')?.valueChanges.subscribe(() => {\r\n      this.updateItemSubtotal(itemForm);\r\n    });\r\n\r\n    return itemForm;\r\n  }\r\n\r\n  addItem() {\r\n    this.itemsFormArray.push(this.createItemForm());\r\n  }\r\n\r\n  removeItem(index: number) {\r\n    this.itemsFormArray.removeAt(index);\r\n  }\r\n\r\n  getProductType(index: number): string {\r\n    return this.itemsFormArray.at(index).get('productType')?.value || 'phone';\r\n  }\r\n\r\n  loadSale(id: string | number) {\r\n    this.saleService.getById(+id).subscribe({\r\n      next: (sale) => {\r\n        Promise.all([\r\n          this.customerService.getAll().toPromise(),\r\n          this.storeService.getList().toPromise(),\r\n          this.phoneService.getList().toPromise(),\r\n          this.accessoryService.getList().toPromise()\r\n        ]).then(() => {\r\n         \r\n          while (this.itemsFormArray.length) {\r\n            this.itemsFormArray.removeAt(0);\r\n          }\r\n          \r\n          sale.items.forEach(item => {\r\n            const itemForm = this.createItemForm();\r\n            itemForm.patchValue({\r\n              productType: item.productType,\r\n              product: item.product,\r\n              quantity: item.quantity,\r\n              unitPrice: formatNumberMask(item.unitPrice),\r\n              subtotal: formatNumberMask(item.subtotal)\r\n            });\r\n            this.itemsFormArray.push(itemForm);\r\n          });\r\n          \r\n          this.saleForm.patchValue({\r\n            date: new Date(sale.date).toISOString().split('T')[0],\r\n            customer: sale.customer,\r\n            store: sale.store,\r\n            paymentMethod: sale.paymentMethod,\r\n            status: sale.status,\r\n            seller: sale.seller,\r\n            totalValue: formatNumberMask(sale.totalValue)\r\n          });\r\n        });\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar venda', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadCustomers() {\r\n    this.customerService.getAll().subscribe({\r\n      next: (customers) => {\r\n        this.customersList = customers;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar clientes', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadStores() {\r\n    this.storeService.getList().subscribe({\r\n      next: (stores) => {\r\n        this.storesList = stores;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar lojas', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadPhones() {\r\n    this.phoneService.getList().subscribe({\r\n      next: (phones) => {\r\n        this.phonesList = phones;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar celulares', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadAccessories() {\r\n    this.accessoryService.getList().subscribe({\r\n      next: (accessories) => {\r\n        this.accessoriesList = accessories;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar acessórios', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  updateItemSubtotal(itemForm: FormGroup): void {\r\n    const quantity = +itemForm.get('quantity')?.value || 0;\r\n    const unitPrice = parseNumberMask(itemForm.get('unitPrice')?.value) || 0;\r\n    const subtotal = quantity * unitPrice;\r\n    \r\n    itemForm.get('subtotal')?.setValue(formatNumberMask(subtotal), { emitEvent: false });\r\n    this.updateTotals();\r\n  }\r\n\r\n  updateTotals() {\r\n    let total = 0;\r\n    \r\n    for (let i = 0; i < this.itemsFormArray.length; i++) {\r\n      const itemForm = this.itemsFormArray.at(i);\r\n      const subtotalValue = parseNumberMask(itemForm.get('subtotal')?.value) || 0;\r\n      total += subtotalValue;\r\n    }\r\n    \r\n    this.saleForm.get('totalValue')?.setValue(formatNumberMask(total));\r\n  }\r\n\r\n  compareWith(o1: any, o2: any): boolean {\r\n    return o1 && o2 ? o1.id === o2.id : o1 === o2;\r\n  }\r\n\r\n  save() {\r\n    if (this.saleForm.invalid || this.itemsFormArray.length === 0) {\r\n      console.log('Form invalid or no items:', {\r\n        invalid: this.saleForm.invalid,\r\n        errors: this.saleForm.errors,\r\n        itemsLength: this.itemsFormArray.length\r\n      });\r\n      return;\r\n    }\r\n\r\n    const formValue = this.saleForm.value;\r\n    console.log('Form value:', formValue);\r\n\r\n    const items = formValue.items.map((item: any) => ({\r\n      productId: item.product?.id || 0,\r\n      productType: item.productType,\r\n      quantity: +item.quantity,\r\n      unitPrice: parseNumberMask(item.unitPrice),\r\n      subtotal: parseNumberMask(item.subtotal)\r\n    }));\r\n\r\n    console.log('Mapped items:', items);\r\n\r\n    const sale: Sale = {\r\n      ...(this.saleId ? { id: +this.saleId } : {}),\r\n      date: formValue.date,\r\n      customerId: formValue.customer?.id || 0,\r\n      storeId: formValue.store?.id || 0,\r\n      customer: formValue.customer,\r\n      store: formValue.store,\r\n      paymentMethod: formValue.paymentMethod,\r\n      status: formValue.status,\r\n      seller: formValue.seller,\r\n      items: items,\r\n      totalValue: parseNumberMask(formValue.totalValue)\r\n    };\r\n\r\n    console.log('Sale object to save:', sale);\r\n    \r\n    this.saleService.save(sale).subscribe({\r\n      next: () => {\r\n        this.toastController.create({\r\n          message: 'Venda salva com sucesso!',\r\n          duration: 3000,\r\n          color: 'success'\r\n        }).then(toast => toast.present());\r\n        \r\n        this.router.navigate(['/sales']);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao salvar venda', error);\r\n        this.toastController.create({\r\n          message: 'Erro ao salvar venda',\r\n          duration: 3000,\r\n          color: 'danger'\r\n        }).then(toast => toast.present());\r\n      }\r\n    });\r\n  }\r\n\r\n  hasError(field: string, error: string): boolean {\r\n    const formControl = this.saleForm.get(field);\r\n    return !!formControl?.touched && !!formControl?.errors?.[error];\r\n  }\r\n\r\n  hasItemError(index: number, field: string, error: string): boolean {\r\n    const itemFormGroup = this.itemsFormArray.at(index);\r\n    const formControl = itemFormGroup.get(field);\r\n    return !!formControl?.touched && !!formControl?.errors?.[error];\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-back-button defaultHref=\"/sales\"></ion-back-button>\r\n    </ion-buttons>\r\n    <ion-title>{{ saleId ? 'Editar Venda' : 'Nova Venda' }}</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div class=\"form-container\">\r\n    <form [formGroup]=\"saleForm\" (ngSubmit)=\"save()\">\r\n      <ion-list>\r\n        <ion-item>\r\n          <ion-input type=\"date\" formControlName=\"date\" labelPlacement=\"floating\" label=\"Data da Venda\" [max]=\"maxDate\"></ion-input>\r\n          <p>\r\n            @if(hasError('date', 'required')) {\r\n              A data da venda é obrigatória\r\n            }\r\n          </p>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-select formControlName=\"customer\" labelPlacement=\"floating\" label=\"Cliente\" [compareWith]=\"compareWith\">\r\n            @for(customer of customersList; track customer.id) {\r\n              <ion-select-option [value]=\"customer\">{{ customer.name }}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n            @if(hasError('customer', 'required')) {\r\n              O cliente é obrigatório\r\n            }\r\n          </p>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-select formControlName=\"store\" labelPlacement=\"floating\" label=\"Loja\" [compareWith]=\"compareWith\">\r\n            @for(store of storesList; track store.id) {\r\n              <ion-select-option [value]=\"store\">{{ store.name }}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n            @if(hasError('store', 'required')) {\r\n              A loja é obrigatória\r\n            }\r\n          </p>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-select formControlName=\"paymentMethod\" labelPlacement=\"floating\" label=\"Método de Pagamento\">\r\n            @for(method of paymentMethods; track method.value) {\r\n              <ion-select-option [value]=\"method.value\">{{ method.label }}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n            @if(hasError('paymentMethod', 'required')) {\r\n              O método de pagamento é obrigatório\r\n            }\r\n          </p>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-select formControlName=\"status\" labelPlacement=\"floating\" label=\"Status\">\r\n            @for(status of saleStatus; track status.value) {\r\n              <ion-select-option [value]=\"status.value\">{{ status.label }}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n            @if(hasError('status', 'required')) {\r\n              O status é obrigatório\r\n            }\r\n          </p>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-input formControlName=\"seller\" labelPlacement=\"floating\" label=\"Vendedor\" type=\"text\"></ion-input>\r\n          <p>\r\n            @if(hasError('seller', 'required')) {\r\n              O vendedor é obrigatório\r\n            }\r\n          </p>\r\n        </ion-item>\r\n\r\n        <ion-item-divider>\r\n          <ion-label>Itens da Venda</ion-label>\r\n          <ion-button slot=\"end\" size=\"small\" class=\"add-item-button\" fill=\"outline\" (click)=\"addItem()\">\r\n            <ion-icon name=\"add\"></ion-icon>\r\n            Adicionar Item\r\n          </ion-button>\r\n        </ion-item-divider>\r\n\r\n        <div formArrayName=\"items\">\r\n          @for(item of itemsFormArray.controls; track $index; let i = $index) {\r\n            <div [formGroupName]=\"i\" class=\"item-form\">\r\n              <ion-item>\r\n                <ion-select formControlName=\"productType\" labelPlacement=\"floating\" label=\"Tipo de Produto\">\r\n                  <ion-select-option value=\"phone\">Celular</ion-select-option>\r\n                  <ion-select-option value=\"accessory\">Acessório</ion-select-option>\r\n                </ion-select>\r\n                <p>\r\n                  @if(hasItemError(i, 'productType', 'required')) {\r\n                    O tipo de produto é obrigatório\r\n                  }\r\n                </p>\r\n              </ion-item>\r\n\r\n              <ion-item>\r\n                <ion-select formControlName=\"product\" labelPlacement=\"floating\" label=\"Produto\" [compareWith]=\"compareWith\">\r\n                  @if(getProductType(i) === 'phone') {\r\n                    @for(phone of phonesList; track phone.id) {\r\n                      <ion-select-option [value]=\"phone\">{{ phone.model }} - {{ phone.price | currency: 'BRL' }}</ion-select-option>\r\n                    }\r\n                  } @else {\r\n                    @for(accessory of accessoriesList; track accessory.id) {\r\n                      <ion-select-option [value]=\"accessory\">{{ accessory.name }} - {{ accessory.price | currency: 'BRL' }}</ion-select-option>\r\n                    }\r\n                  }\r\n                </ion-select>\r\n                <p>\r\n                  @if(hasItemError(i, 'product', 'required')) {\r\n                    O produto é obrigatório\r\n                  }\r\n                </p>\r\n              </ion-item>\r\n\r\n              <ion-item>\r\n                <ion-input formControlName=\"quantity\" labelPlacement=\"floating\" label=\"Quantidade\" type=\"number\" min=\"1\"></ion-input>\r\n                <p>\r\n                  @if(hasItemError(i, 'quantity', 'required')) {\r\n                    A quantidade é obrigatória\r\n                  }\r\n                  @if(hasItemError(i, 'quantity', 'min')) {\r\n                    A quantidade deve ser pelo menos 1\r\n                  }\r\n                </p>\r\n              </ion-item>\r\n\r\n              <ion-item>\r\n                <ion-input formControlName=\"unitPrice\" labelPlacement=\"floating\" label=\"Preço Unitário\" type=\"text\" \r\n                  [maskito]=\"priceMask\" [maskitoElement]=\"maskitoElement\"></ion-input>\r\n                <p>\r\n                  @if(hasItemError(i, 'unitPrice', 'required')) {\r\n                    O preço unitário é obrigatório\r\n                  }\r\n                </p>\r\n              </ion-item>\r\n\r\n              <ion-item>\r\n                <ion-input formControlName=\"subtotal\" labelPlacement=\"floating\" label=\"Subtotal\" type=\"text\" readonly\r\n                  [maskito]=\"priceMask\" [maskitoElement]=\"maskitoElement\"></ion-input>\r\n              </ion-item>\r\n\r\n              <div class=\"item-actions\">\r\n                <ion-button fill=\"clear\" color=\"danger\" (click)=\"removeItem(i)\">\r\n                  <ion-icon name=\"trash\"></ion-icon>\r\n                  Remover\r\n                </ion-button>\r\n              </div>\r\n            </div>\r\n          }\r\n          @empty {\r\n            <ion-item>\r\n              <ion-label color=\"medium\">Nenhum item adicionado</ion-label>\r\n            </ion-item>\r\n          }\r\n        </div>\r\n\r\n        <ion-item>\r\n          <ion-input formControlName=\"totalValue\" labelPlacement=\"floating\" label=\"Valor Total\" type=\"text\" readonly\r\n            [maskito]=\"priceMask\" [maskitoElement]=\"maskitoElement\"></ion-input>\r\n        </ion-item>\r\n      </ion-list>\r\n\r\n      <div class=\"form-buttons\">\r\n        <ion-button expand=\"block\" type=\"submit\" [disabled]=\"saleForm.invalid || itemsFormArray.length === 0\">\r\n          Salvar\r\n        </ion-button>\r\n        <ion-button expand=\"block\" fill=\"outline\" routerLink=\"/sales\">\r\n          Cancelar\r\n        </ion-button>\r\n      </div>\r\n    </form>\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";AACA,SAA4CA,UAAU,QAAQ,gBAAgB;AAY9E,SAASC,cAAc,EAAEC,UAAU,QAAc,qBAAqB;AACtE,SAASC,SAAS,EAAEC,cAAc,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,uCAAuC;;;;;;;;;;;;;;;;ICGtGC,EAAA,CAAAC,MAAA,gDACF;;;;;IAOED,EAAA,CAAAE,cAAA,2BAAsC;IAAAF,EAAA,CAAAC,MAAA,GAAmB;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAA1DH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAkB;IAACL,EAAA,CAAAM,SAAA,EAAmB;IAAnBN,EAAA,CAAAO,iBAAA,CAAAF,WAAA,CAAAG,IAAA,CAAmB;;;;;IAKzDR,EAAA,CAAAC,MAAA,0CACF;;;;;IAOED,EAAA,CAAAE,cAAA,2BAAmC;IAAAF,EAAA,CAAAC,MAAA,GAAgB;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAApDH,EAAA,CAAAI,UAAA,UAAAK,QAAA,CAAe;IAACT,EAAA,CAAAM,SAAA,EAAgB;IAAhBN,EAAA,CAAAO,iBAAA,CAAAE,QAAA,CAAAD,IAAA,CAAgB;;;;;IAKnDR,EAAA,CAAAC,MAAA,uCACF;;;;;IAOED,EAAA,CAAAE,cAAA,2BAA0C;IAAAF,EAAA,CAAAC,MAAA,GAAkB;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAA7DH,EAAA,CAAAI,UAAA,UAAAM,SAAA,CAAAC,KAAA,CAAsB;IAACX,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAO,iBAAA,CAAAG,SAAA,CAAAE,KAAA,CAAkB;;;;;IAK5DZ,EAAA,CAAAC,MAAA,2DACF;;;;;IAOED,EAAA,CAAAE,cAAA,2BAA0C;IAAAF,EAAA,CAAAC,MAAA,GAAkB;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAA7DH,EAAA,CAAAI,UAAA,UAAAS,SAAA,CAAAF,KAAA,CAAsB;IAACX,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAO,iBAAA,CAAAM,SAAA,CAAAD,KAAA,CAAkB;;;;;IAK5DZ,EAAA,CAAAC,MAAA,yCACF;;;;;IAQED,EAAA,CAAAC,MAAA,2CACF;;;;;IAsBQD,EAAA,CAAAC,MAAA,kDACF;;;;;IAQID,EAAA,CAAAE,cAAA,2BAAmC;IAAAF,EAAA,CAAAC,MAAA,GAAuD;;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAA3FH,EAAA,CAAAI,UAAA,UAAAU,QAAA,CAAe;IAACd,EAAA,CAAAM,SAAA,EAAuD;IAAvDN,EAAA,CAAAe,kBAAA,KAAAD,QAAA,CAAAE,KAAA,SAAAhB,EAAA,CAAAiB,WAAA,OAAAH,QAAA,CAAAI,KAAA,aAAuD;;;;;IAD5FlB,EAAA,CAAAmB,gBAAA,IAAAC,sDAAA,gCAAAC,UAAA,CAEC;;;;IAFDrB,EAAA,CAAAsB,UAAA,CAAAC,MAAA,CAAAC,UAAA,CAEC;;;;;IAGCxB,EAAA,CAAAE,cAAA,2BAAuC;IAAAF,EAAA,CAAAC,MAAA,GAA8D;;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAAtGH,EAAA,CAAAI,UAAA,UAAAqB,YAAA,CAAmB;IAACzB,EAAA,CAAAM,SAAA,EAA8D;IAA9DN,EAAA,CAAAe,kBAAA,KAAAU,YAAA,CAAAjB,IAAA,SAAAR,EAAA,CAAAiB,WAAA,OAAAQ,YAAA,CAAAP,KAAA,aAA8D;;;;;IADvGlB,EAAA,CAAAmB,gBAAA,IAAAO,sDAAA,gCAAAL,UAAA,CAEC;;;;IAFDrB,EAAA,CAAAsB,UAAA,CAAAC,MAAA,CAAAI,eAAA,CAEC;;;;;IAKD3B,EAAA,CAAAC,MAAA,0CACF;;;;;IAQED,EAAA,CAAAC,MAAA,6CACF;;;;;IAEED,EAAA,CAAAC,MAAA,2CACF;;;;;IASED,EAAA,CAAAC,MAAA,2DACF;;;;;;IA/CAD,EAHN,CAAAE,cAAA,cAA2C,eAC/B,qBACoF,4BACzD;IAAAF,EAAA,CAAAC,MAAA,cAAO;IAAAD,EAAA,CAAAG,YAAA,EAAoB;IAC5DH,EAAA,CAAAE,cAAA,4BAAqC;IAAAF,EAAA,CAAAC,MAAA,qBAAS;IAChDD,EADgD,CAAAG,YAAA,EAAoB,EACvD;IACbH,EAAA,CAAAE,cAAA,QAAG;IACDF,EAAA,CAAA4B,UAAA,IAAAC,+CAAA,OAAiD;IAIrD7B,EADE,CAAAG,YAAA,EAAI,EACK;IAGTH,EADF,CAAAE,cAAA,eAAU,sBACoG;IAKxGF,EAJF,CAAA4B,UAAA,KAAAE,gDAAA,OAAoC,KAAAC,gDAAA,OAI3B;IAKX/B,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAE,cAAA,SAAG;IACDF,EAAA,CAAA4B,UAAA,KAAAI,gDAAA,OAA6C;IAIjDhC,EADE,CAAAG,YAAA,EAAI,EACK;IAEXH,EAAA,CAAAE,cAAA,gBAAU;IACRF,EAAA,CAAAiC,SAAA,qBAAqH;IACrHjC,EAAA,CAAAE,cAAA,SAAG;IAIDF,EAHA,CAAA4B,UAAA,KAAAM,gDAAA,OAA8C,KAAAC,gDAAA,OAGL;IAI7CnC,EADE,CAAAG,YAAA,EAAI,EACK;IAEXH,EAAA,CAAAE,cAAA,gBAAU;IACRF,EAAA,CAAAiC,SAAA,qBACsE;IACtEjC,EAAA,CAAAE,cAAA,SAAG;IACDF,EAAA,CAAA4B,UAAA,KAAAQ,gDAAA,OAA+C;IAInDpC,EADE,CAAAG,YAAA,EAAI,EACK;IAEXH,EAAA,CAAAE,cAAA,gBAAU;IACRF,EAAA,CAAAiC,SAAA,qBACsE;IACxEjC,EAAA,CAAAG,YAAA,EAAW;IAGTH,EADF,CAAAE,cAAA,eAA0B,sBACwC;IAAxBF,EAAA,CAAAqC,UAAA,mBAAAC,+DAAA;MAAA,MAAAC,aAAA,GAAAvC,EAAA,CAAAwC,aAAA,CAAAC,GAAA,EAAAC,MAAA;MAAA,MAAAnB,MAAA,GAAAvB,EAAA,CAAA2C,aAAA;MAAA,OAAA3C,EAAA,CAAA4C,WAAA,CAASrB,MAAA,CAAAsB,UAAA,CAAAN,aAAA,CAAa;IAAA,EAAC;IAC7DvC,EAAA,CAAAiC,SAAA,oBAAkC;IAClCjC,EAAA,CAAAC,MAAA,iBACF;IAEJD,EAFI,CAAAG,YAAA,EAAa,EACT,EACF;;;;;IAjEDH,EAAA,CAAAI,UAAA,kBAAAmC,aAAA,CAAmB;IAOlBvC,EAAA,CAAAM,SAAA,GAEC;IAFDN,EAAA,CAAA8C,aAAA,CAAAvB,MAAA,CAAAwB,YAAA,CAAAR,aAAA,sCAEC;IAK6EvC,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAI,UAAA,gBAAAmB,MAAA,CAAAyB,WAAA,CAA2B;IACzGhD,EAAA,CAAAM,SAAA,EAQC;IARDN,EAAA,CAAA8C,aAAA,CAAAvB,MAAA,CAAA0B,cAAA,CAAAV,aAAA,wBAQC;IAGDvC,EAAA,CAAAM,SAAA,GAEC;IAFDN,EAAA,CAAA8C,aAAA,CAAAvB,MAAA,CAAAwB,YAAA,CAAAR,aAAA,mCAEC;IAODvC,EAAA,CAAAM,SAAA,GAEC;IAFDN,EAAA,CAAA8C,aAAA,CAAAvB,MAAA,CAAAwB,YAAA,CAAAR,aAAA,oCAEC;IACDvC,EAAA,CAAAM,SAAA,EAEC;IAFDN,EAAA,CAAA8C,aAAA,CAAAvB,MAAA,CAAAwB,YAAA,CAAAR,aAAA,+BAEC;IAMDvC,EAAA,CAAAM,SAAA,GAAqB;IAACN,EAAtB,CAAAI,UAAA,YAAAmB,MAAA,CAAA3B,SAAA,CAAqB,mBAAA2B,MAAA,CAAA1B,cAAA,CAAkC;IAEvDG,EAAA,CAAAM,SAAA,GAEC;IAFDN,EAAA,CAAA8C,aAAA,CAAAvB,MAAA,CAAAwB,YAAA,CAAAR,aAAA,qCAEC;IAMDvC,EAAA,CAAAM,SAAA,GAAqB;IAACN,EAAtB,CAAAI,UAAA,YAAAmB,MAAA,CAAA3B,SAAA,CAAqB,mBAAA2B,MAAA,CAAA1B,cAAA,CAAkC;;;;;IAa3DG,EADF,CAAAE,cAAA,eAAU,oBACkB;IAAAF,EAAA,CAAAC,MAAA,6BAAsB;IAClDD,EADkD,CAAAG,YAAA,EAAY,EACnD;;;AD7IvB,OAAM,MAAO+C,iBAAiB;EAa5BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,eAAgC,EAChCC,YAA0B,EAC1BC,YAA0B,EAC1BC,gBAAkC,EAClCC,KAAqB,EACrBC,MAAc,EACdC,eAAgC;IARhC,KAAAR,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IApBzB,KAAAC,MAAM,GAA2B,IAAI;IACrC,KAAAC,aAAa,GAAe,EAAE;IAC9B,KAAAC,UAAU,GAAY,EAAE;IACxB,KAAAvC,UAAU,GAAY,EAAE;IACxB,KAAAG,eAAe,GAAgB,EAAE;IACjC,KAAAqC,cAAc,GAAGtE,cAAc;IAC/B,KAAAuE,UAAU,GAAGtE,UAAU;IACvB,KAAAC,SAAS,GAAGA,SAAS;IACrB,KAAAC,cAAc,GAAGA,cAAc;IAc7B,IAAI,CAACqE,QAAQ,GAAG,IAAI,CAACC,UAAU,EAAE;IACjC,IAAI,CAACC,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACvD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,eAAe,EAAE;IAEtB,IAAI,CAAClB,KAAK,CAACmB,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACrC,MAAMC,EAAE,GAAGD,MAAM,CAACE,GAAG,CAAC,IAAI,CAAC;MAC3B,IAAID,EAAE,EAAE;QACN,IAAI,CAACnB,MAAM,GAAGmB,EAAE;QAChB,IAAI,CAACE,QAAQ,CAAC,IAAI,CAACrB,MAAM,CAAC;MAC5B,CAAC,MAAM;QAAA,IAAAsB,kBAAA;QACL,CAAAA,kBAAA,OAAI,CAACjB,QAAQ,CAACe,GAAG,CAAC,MAAM,CAAC,cAAAE,kBAAA,eAAzBA,kBAAA,CAA2BC,QAAQ,CAAC,IAAIf,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7E;IACF,CAAC,CAAC;IAEF,IAAI,CAACc,cAAc,CAACC,YAAY,CAACR,SAAS,CAAC,MAAK;MAC9C,IAAI,CAACS,YAAY,EAAE;IACrB,CAAC,CAAC;EACJ;EAEApB,UAAUA,CAAA;IACR,OAAO,IAAI,CAACf,EAAE,CAACoC,KAAK,CAAC;MACnBC,IAAI,EAAE,CAAC,EAAE,EAAEhG,UAAU,CAACiG,QAAQ,CAAC;MAC/BC,QAAQ,EAAE,CAAC,EAAE,EAAElG,UAAU,CAACiG,QAAQ,CAAC;MACnCE,KAAK,EAAE,CAAC,EAAE,EAAEnG,UAAU,CAACiG,QAAQ,CAAC;MAChCG,aAAa,EAAE,CAAC,EAAE,EAAEpG,UAAU,CAACiG,QAAQ,CAAC;MACxCI,MAAM,EAAE,CAAC,SAAS,EAAErG,UAAU,CAACiG,QAAQ,CAAC;MACxCK,MAAM,EAAE,CAAC,EAAE,EAAEtG,UAAU,CAACiG,QAAQ,CAAC;MACjCM,KAAK,EAAE,IAAI,CAAC5C,EAAE,CAAC6C,KAAK,CAAC,EAAE,CAAC;MACxBC,UAAU,EAAE,CAAC,CAAC;KACf,CAAC;EACJ;EAEA,IAAIb,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACnB,QAAQ,CAACe,GAAG,CAAC,OAAO,CAAc;EAChD;EAEAkB,cAAcA,CAAA;IAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA;IACZ,MAAMC,QAAQ,GAAG,IAAI,CAACnD,EAAE,CAACoC,KAAK,CAAC;MAC7BgB,WAAW,EAAE,CAAC,OAAO,EAAE/G,UAAU,CAACiG,QAAQ,CAAC;MAC3Ce,OAAO,EAAE,CAAC,EAAE,EAAEhH,UAAU,CAACiG,QAAQ,CAAC;MAClCgB,QAAQ,EAAE,CAAC,CAAC,EAAE,CAACjH,UAAU,CAACiG,QAAQ,EAAEjG,UAAU,CAACkH,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvDC,SAAS,EAAE,CAAC,GAAG,EAAEnH,UAAU,CAACiG,QAAQ,CAAC;MACrCmB,QAAQ,EAAE,CAAC,GAAG;KACf,CAAC;IAGF,CAAAT,aAAA,GAAAG,QAAQ,CAACtB,GAAG,CAAC,SAAS,CAAC,cAAAmB,aAAA,eAAvBA,aAAA,CAAyBd,YAAY,CAACR,SAAS,CAAE2B,OAAgC,IAAI;MACnF,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,OAAO,IAAIA,OAAO,EAAE;QAAA,IAAAK,cAAA;QAChE,MAAM5F,KAAK,GAAGuF,OAAO,CAACvF,KAAK,GAAGnB,gBAAgB,CAACgH,UAAU,CAACN,OAAO,CAACvF,KAAK,CAAC,CAAC,GAAG,GAAG;QAC/E,CAAA4F,cAAA,GAAAP,QAAQ,CAACtB,GAAG,CAAC,WAAW,CAAC,cAAA6B,cAAA,eAAzBA,cAAA,CAA2B1B,QAAQ,CAAClE,KAAK,CAAC;QAE1C,IAAI,CAAC8F,kBAAkB,CAACT,QAAQ,CAAC;MACnC;IACF,CAAC,CAAC;IAEF,CAAAF,cAAA,GAAAE,QAAQ,CAACtB,GAAG,CAAC,UAAU,CAAC,cAAAoB,cAAA,eAAxBA,cAAA,CAA0Bf,YAAY,CAACR,SAAS,CAAC,MAAK;MACpD,IAAI,CAACkC,kBAAkB,CAACT,QAAQ,CAAC;IACnC,CAAC,CAAC;IAEF,CAAAD,cAAA,GAAAC,QAAQ,CAACtB,GAAG,CAAC,WAAW,CAAC,cAAAqB,cAAA,eAAzBA,cAAA,CAA2BhB,YAAY,CAACR,SAAS,CAAC,MAAK;MACrD,IAAI,CAACkC,kBAAkB,CAACT,QAAQ,CAAC;IACnC,CAAC,CAAC;IAEF,OAAOA,QAAQ;EACjB;EAEAU,OAAOA,CAAA;IACL,IAAI,CAAC5B,cAAc,CAAC6B,IAAI,CAAC,IAAI,CAACf,cAAc,EAAE,CAAC;EACjD;EAEAtD,UAAUA,CAACsE,KAAa;IACtB,IAAI,CAAC9B,cAAc,CAAC+B,QAAQ,CAACD,KAAK,CAAC;EACrC;EAEAlE,cAAcA,CAACkE,KAAa;IAAA,IAAAE,qBAAA;IAC1B,OAAO,EAAAA,qBAAA,OAAI,CAAChC,cAAc,CAACiC,EAAE,CAACH,KAAK,CAAC,CAAClC,GAAG,CAAC,aAAa,CAAC,cAAAoC,qBAAA,uBAAhDA,qBAAA,CAAkD1G,KAAK,KAAI,OAAO;EAC3E;EAEAuE,QAAQA,CAACF,EAAmB;IAC1B,IAAI,CAAC3B,WAAW,CAACkE,OAAO,CAAC,CAACvC,EAAE,CAAC,CAACF,SAAS,CAAC;MACtC0C,IAAI,EAAGC,IAAI,IAAI;QACbC,OAAO,CAACC,GAAG,CAAC,CACV,IAAI,CAACrE,eAAe,CAACsE,MAAM,EAAE,CAACC,SAAS,EAAE,EACzC,IAAI,CAACtE,YAAY,CAACuE,OAAO,EAAE,CAACD,SAAS,EAAE,EACvC,IAAI,CAACrE,YAAY,CAACsE,OAAO,EAAE,CAACD,SAAS,EAAE,EACvC,IAAI,CAACpE,gBAAgB,CAACqE,OAAO,EAAE,CAACD,SAAS,EAAE,CAC5C,CAAC,CAACE,IAAI,CAAC,MAAK;UAEX,OAAO,IAAI,CAAC1C,cAAc,CAAC2C,MAAM,EAAE;YACjC,IAAI,CAAC3C,cAAc,CAAC+B,QAAQ,CAAC,CAAC,CAAC;UACjC;UAEAK,IAAI,CAACzB,KAAK,CAACiC,OAAO,CAACC,IAAI,IAAG;YACxB,MAAM3B,QAAQ,GAAG,IAAI,CAACJ,cAAc,EAAE;YACtCI,QAAQ,CAAC4B,UAAU,CAAC;cAClB3B,WAAW,EAAE0B,IAAI,CAAC1B,WAAW;cAC7BC,OAAO,EAAEyB,IAAI,CAACzB,OAAO;cACrBC,QAAQ,EAAEwB,IAAI,CAACxB,QAAQ;cACvBE,SAAS,EAAE7G,gBAAgB,CAACmI,IAAI,CAACtB,SAAS,CAAC;cAC3CC,QAAQ,EAAE9G,gBAAgB,CAACmI,IAAI,CAACrB,QAAQ;aACzC,CAAC;YACF,IAAI,CAACxB,cAAc,CAAC6B,IAAI,CAACX,QAAQ,CAAC;UACpC,CAAC,CAAC;UAEF,IAAI,CAACrC,QAAQ,CAACiE,UAAU,CAAC;YACvB1C,IAAI,EAAE,IAAIpB,IAAI,CAACoD,IAAI,CAAChC,IAAI,CAAC,CAACnB,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACrDoB,QAAQ,EAAE8B,IAAI,CAAC9B,QAAQ;YACvBC,KAAK,EAAE6B,IAAI,CAAC7B,KAAK;YACjBC,aAAa,EAAE4B,IAAI,CAAC5B,aAAa;YACjCC,MAAM,EAAE2B,IAAI,CAAC3B,MAAM;YACnBC,MAAM,EAAE0B,IAAI,CAAC1B,MAAM;YACnBG,UAAU,EAAEnG,gBAAgB,CAAC0H,IAAI,CAACvB,UAAU;WAC7C,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MACDkC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEA3D,aAAaA,CAAA;IACX,IAAI,CAACnB,eAAe,CAACsE,MAAM,EAAE,CAAC9C,SAAS,CAAC;MACtC0C,IAAI,EAAGc,SAAS,IAAI;QAClB,IAAI,CAACxE,aAAa,GAAGwE,SAAS;MAChC,CAAC;MACDF,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;EAEA1D,UAAUA,CAAA;IACR,IAAI,CAACnB,YAAY,CAACuE,OAAO,EAAE,CAAChD,SAAS,CAAC;MACpC0C,IAAI,EAAGe,MAAM,IAAI;QACf,IAAI,CAACxE,UAAU,GAAGwE,MAAM;MAC1B,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAzD,UAAUA,CAAA;IACR,IAAI,CAACnB,YAAY,CAACsE,OAAO,EAAE,CAAChD,SAAS,CAAC;MACpC0C,IAAI,EAAGgB,MAAM,IAAI;QACf,IAAI,CAAChH,UAAU,GAAGgH,MAAM;MAC1B,CAAC;MACDJ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;KACD,CAAC;EACJ;EAEAxD,eAAeA,CAAA;IACb,IAAI,CAACnB,gBAAgB,CAACqE,OAAO,EAAE,CAAChD,SAAS,CAAC;MACxC0C,IAAI,EAAGiB,WAAW,IAAI;QACpB,IAAI,CAAC9G,eAAe,GAAG8G,WAAW;MACpC,CAAC;MACDL,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACrD;KACD,CAAC;EACJ;EAEApB,kBAAkBA,CAACT,QAAmB;IAAA,IAAAmC,cAAA,EAAAC,cAAA,EAAAC,cAAA;IACpC,MAAMlC,QAAQ,GAAG,GAAAgC,cAAA,GAACnC,QAAQ,CAACtB,GAAG,CAAC,UAAU,CAAC,cAAAyD,cAAA,uBAAxBA,cAAA,CAA0B/H,KAAK,KAAI,CAAC;IACtD,MAAMiG,SAAS,GAAG9G,eAAe,EAAA6I,cAAA,GAACpC,QAAQ,CAACtB,GAAG,CAAC,WAAW,CAAC,cAAA0D,cAAA,uBAAzBA,cAAA,CAA2BhI,KAAK,CAAC,IAAI,CAAC;IACxE,MAAMkG,QAAQ,GAAGH,QAAQ,GAAGE,SAAS;IAErC,CAAAgC,cAAA,GAAArC,QAAQ,CAACtB,GAAG,CAAC,UAAU,CAAC,cAAA2D,cAAA,eAAxBA,cAAA,CAA0BxD,QAAQ,CAACrF,gBAAgB,CAAC8G,QAAQ,CAAC,EAAE;MAAEgC,SAAS,EAAE;IAAK,CAAE,CAAC;IACpF,IAAI,CAACtD,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IAAA,IAAAuD,mBAAA;IACV,IAAIC,KAAK,GAAG,CAAC;IAEb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3D,cAAc,CAAC2C,MAAM,EAAEgB,CAAC,EAAE,EAAE;MAAA,IAAAC,cAAA;MACnD,MAAM1C,QAAQ,GAAG,IAAI,CAAClB,cAAc,CAACiC,EAAE,CAAC0B,CAAC,CAAC;MAC1C,MAAME,aAAa,GAAGpJ,eAAe,EAAAmJ,cAAA,GAAC1C,QAAQ,CAACtB,GAAG,CAAC,UAAU,CAAC,cAAAgE,cAAA,uBAAxBA,cAAA,CAA0BtI,KAAK,CAAC,IAAI,CAAC;MAC3EoI,KAAK,IAAIG,aAAa;IACxB;IAEA,CAAAJ,mBAAA,OAAI,CAAC5E,QAAQ,CAACe,GAAG,CAAC,YAAY,CAAC,cAAA6D,mBAAA,eAA/BA,mBAAA,CAAiC1D,QAAQ,CAACrF,gBAAgB,CAACgJ,KAAK,CAAC,CAAC;EACpE;EAEA/F,WAAWA,CAACmG,EAAO,EAAEC,EAAO;IAC1B,OAAOD,EAAE,IAAIC,EAAE,GAAGD,EAAE,CAACnE,EAAE,KAAKoE,EAAE,CAACpE,EAAE,GAAGmE,EAAE,KAAKC,EAAE;EAC/C;EAEAC,IAAIA,CAAA;IAAA,IAAAC,mBAAA,EAAAC,gBAAA;IACF,IAAI,IAAI,CAACrF,QAAQ,CAACsF,OAAO,IAAI,IAAI,CAACnE,cAAc,CAAC2C,MAAM,KAAK,CAAC,EAAE;MAC7DK,OAAO,CAACoB,GAAG,CAAC,2BAA2B,EAAE;QACvCD,OAAO,EAAE,IAAI,CAACtF,QAAQ,CAACsF,OAAO;QAC9BE,MAAM,EAAE,IAAI,CAACxF,QAAQ,CAACwF,MAAM;QAC5BC,WAAW,EAAE,IAAI,CAACtE,cAAc,CAAC2C;OAClC,CAAC;MACF;IACF;IAEA,MAAM4B,SAAS,GAAG,IAAI,CAAC1F,QAAQ,CAACvD,KAAK;IACrC0H,OAAO,CAACoB,GAAG,CAAC,aAAa,EAAEG,SAAS,CAAC;IAErC,MAAM5D,KAAK,GAAG4D,SAAS,CAAC5D,KAAK,CAAC6D,GAAG,CAAE3B,IAAS;MAAA,IAAA4B,aAAA;MAAA,OAAM;QAChDC,SAAS,EAAE,EAAAD,aAAA,GAAA5B,IAAI,CAACzB,OAAO,cAAAqD,aAAA,uBAAZA,aAAA,CAAc9E,EAAE,KAAI,CAAC;QAChCwB,WAAW,EAAE0B,IAAI,CAAC1B,WAAW;QAC7BE,QAAQ,EAAE,CAACwB,IAAI,CAACxB,QAAQ;QACxBE,SAAS,EAAE9G,eAAe,CAACoI,IAAI,CAACtB,SAAS,CAAC;QAC1CC,QAAQ,EAAE/G,eAAe,CAACoI,IAAI,CAACrB,QAAQ;OACxC;IAAA,CAAC,CAAC;IAEHwB,OAAO,CAACoB,GAAG,CAAC,eAAe,EAAEzD,KAAK,CAAC;IAEnC,MAAMyB,IAAI,GAAS;MACjB,IAAI,IAAI,CAAC5D,MAAM,GAAG;QAAEmB,EAAE,EAAE,CAAC,IAAI,CAACnB;MAAM,CAAE,GAAG,EAAE,CAAC;MAC5C4B,IAAI,EAAEmE,SAAS,CAACnE,IAAI;MACpBuE,UAAU,EAAE,EAAAV,mBAAA,GAAAM,SAAS,CAACjE,QAAQ,cAAA2D,mBAAA,uBAAlBA,mBAAA,CAAoBtE,EAAE,KAAI,CAAC;MACvCiF,OAAO,EAAE,EAAAV,gBAAA,GAAAK,SAAS,CAAChE,KAAK,cAAA2D,gBAAA,uBAAfA,gBAAA,CAAiBvE,EAAE,KAAI,CAAC;MACjCW,QAAQ,EAAEiE,SAAS,CAACjE,QAAQ;MAC5BC,KAAK,EAAEgE,SAAS,CAAChE,KAAK;MACtBC,aAAa,EAAE+D,SAAS,CAAC/D,aAAa;MACtCC,MAAM,EAAE8D,SAAS,CAAC9D,MAAM;MACxBC,MAAM,EAAE6D,SAAS,CAAC7D,MAAM;MACxBC,KAAK,EAAEA,KAAK;MACZE,UAAU,EAAEpG,eAAe,CAAC8J,SAAS,CAAC1D,UAAU;KACjD;IAEDmC,OAAO,CAACoB,GAAG,CAAC,sBAAsB,EAAEhC,IAAI,CAAC;IAEzC,IAAI,CAACpE,WAAW,CAACgG,IAAI,CAAC5B,IAAI,CAAC,CAAC3C,SAAS,CAAC;MACpC0C,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC5D,eAAe,CAACsG,MAAM,CAAC;UAC1BC,OAAO,EAAE,0BAA0B;UACnCC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;SACR,CAAC,CAACtC,IAAI,CAACuC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;QAEjC,IAAI,CAAC5G,MAAM,CAAC6G,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC,CAAC;MACDpC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACxE,eAAe,CAACsG,MAAM,CAAC;UAC1BC,OAAO,EAAE,sBAAsB;UAC/BC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;SACR,CAAC,CAACtC,IAAI,CAACuC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;MACnC;KACD,CAAC;EACJ;EAEAE,QAAQA,CAACC,KAAa,EAAEtC,KAAa;IAAA,IAAAuC,mBAAA;IACnC,MAAMC,WAAW,GAAG,IAAI,CAAC1G,QAAQ,CAACe,GAAG,CAACyF,KAAK,CAAC;IAC5C,OAAO,CAAC,EAACE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEC,OAAO,KAAI,CAAC,EAACD,WAAW,aAAXA,WAAW,gBAAAD,mBAAA,GAAXC,WAAW,CAAElB,MAAM,cAAAiB,mBAAA,eAAnBA,mBAAA,CAAsBvC,KAAK,CAAC;EACjE;EAEArF,YAAYA,CAACoE,KAAa,EAAEuD,KAAa,EAAEtC,KAAa;IAAA,IAAA0C,oBAAA;IACtD,MAAMC,aAAa,GAAG,IAAI,CAAC1F,cAAc,CAACiC,EAAE,CAACH,KAAK,CAAC;IACnD,MAAMyD,WAAW,GAAGG,aAAa,CAAC9F,GAAG,CAACyF,KAAK,CAAC;IAC5C,OAAO,CAAC,EAACE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEC,OAAO,KAAI,CAAC,EAACD,WAAW,aAAXA,WAAW,gBAAAE,oBAAA,GAAXF,WAAW,CAAElB,MAAM,cAAAoB,oBAAA,eAAnBA,oBAAA,CAAsB1C,KAAK,CAAC;EACjE;;qBAjSWlF,iBAAiB;;mCAAjBA,kBAAiB,EAAAlD,EAAA,CAAAgL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlL,EAAA,CAAAgL,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAApL,EAAA,CAAAgL,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAtL,EAAA,CAAAgL,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAAxL,EAAA,CAAAgL,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAA1L,EAAA,CAAAgL,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA5L,EAAA,CAAAgL,iBAAA,CAAAa,EAAA,CAAAC,cAAA,GAAA9L,EAAA,CAAAgL,iBAAA,CAAAa,EAAA,CAAAE,MAAA,GAAA/L,EAAA,CAAAgL,iBAAA,CAAAgB,EAAA,CAAAC,eAAA;AAAA;;QAAjB/I,kBAAiB;EAAAgJ,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCpB1BzM,EAFJ,CAAAE,cAAA,oBAAiC,qBACA,qBACH;MACxBF,EAAA,CAAAiC,SAAA,yBAAwD;MAC1DjC,EAAA,CAAAG,YAAA,EAAc;MACdH,EAAA,CAAAE,cAAA,gBAAW;MAAAF,EAAA,CAAAC,MAAA,GAA4C;MAE3DD,EAF2D,CAAAG,YAAA,EAAY,EACvD,EACH;MAITH,EAFJ,CAAAE,cAAA,qBAAiC,aACH,cACuB;MAApBF,EAAA,CAAAqC,UAAA,sBAAAsK,oDAAA;QAAA,OAAYD,GAAA,CAAArD,IAAA,EAAM;MAAA,EAAC;MAE5CrJ,EADF,CAAAE,cAAA,eAAU,gBACE;MACRF,EAAA,CAAAiC,SAAA,oBAA0H;MAC1HjC,EAAA,CAAAE,cAAA,SAAG;MACDF,EAAA,CAAA4B,UAAA,KAAAgL,yCAAA,OAAmC;MAIvC5M,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,qBACqG;MAC3GF,EAAA,CAAAmB,gBAAA,KAAA0L,iCAAA,gCAAAxL,UAAA,CAEC;MACHrB,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACDF,EAAA,CAAA4B,UAAA,KAAAkL,yCAAA,OAAuC;MAI3C9M,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,sBAC+F;MACrGF,EAAA,CAAAmB,gBAAA,KAAA4L,iCAAA,gCAAA1L,UAAA,CAEC;MACHrB,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACDF,EAAA,CAAA4B,UAAA,KAAAoL,yCAAA,OAAoC;MAIxChN,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,sBAC0F;MAChGF,EAAA,CAAAmB,gBAAA,KAAA8L,iCAAA,gCAAAC,UAAA,CAEC;MACHlN,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACDF,EAAA,CAAA4B,UAAA,KAAAuL,yCAAA,OAA4C;MAIhDnN,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,sBACsE;MAC5EF,EAAA,CAAAmB,gBAAA,KAAAiM,iCAAA,gCAAAF,UAAA,CAEC;MACHlN,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACDF,EAAA,CAAA4B,UAAA,KAAAyL,yCAAA,OAAqC;MAIzCrN,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAAiC,SAAA,qBAAuG;MACvGjC,EAAA,CAAAE,cAAA,SAAG;MACDF,EAAA,CAAA4B,UAAA,KAAA0L,yCAAA,OAAqC;MAIzCtN,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,wBAAkB,iBACL;MAAAF,EAAA,CAAAC,MAAA,sBAAc;MAAAD,EAAA,CAAAG,YAAA,EAAY;MACrCH,EAAA,CAAAE,cAAA,sBAA+F;MAApBF,EAAA,CAAAqC,UAAA,mBAAAkL,wDAAA;QAAA,OAASb,GAAA,CAAAzF,OAAA,EAAS;MAAA,EAAC;MAC5FjH,EAAA,CAAAiC,SAAA,oBAAgC;MAChCjC,EAAA,CAAAC,MAAA,wBACF;MACFD,EADE,CAAAG,YAAA,EAAa,EACI;MAEnBH,EAAA,CAAAE,cAAA,eAA2B;MACzBF,EAAA,CAAAmB,gBAAA,KAAAqM,iCAAA,qBAAAxN,EAAA,CAAAyN,sBAAA,SAAAC,sCAAA,mBAwEC;MACH1N,EAAA,CAAAG,YAAA,EAAM;MAENH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAAiC,SAAA,qBACsE;MAE1EjC,EADE,CAAAG,YAAA,EAAW,EACF;MAGTH,EADF,CAAAE,cAAA,eAA0B,sBAC8E;MACpGF,EAAA,CAAAC,MAAA,gBACF;MAAAD,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,sBAA8D;MAC5DF,EAAA,CAAAC,MAAA,kBACF;MAIRD,EAJQ,CAAAG,YAAA,EAAa,EACT,EACD,EACH,EACM;;;MAvLFH,EAAA,CAAAI,UAAA,qBAAoB;MAKjBJ,EAAA,CAAAM,SAAA,GAA4C;MAA5CN,EAAA,CAAAO,iBAAA,CAAAmM,GAAA,CAAA7I,MAAA,iCAA4C;MAI9C7D,EAAA,CAAAM,SAAA,EAAmB;MAAnBN,EAAA,CAAAI,UAAA,oBAAmB;MAEtBJ,EAAA,CAAAM,SAAA,GAAsB;MAAtBN,EAAA,CAAAI,UAAA,cAAAsM,GAAA,CAAAxI,QAAA,CAAsB;MAGwElE,EAAA,CAAAM,SAAA,GAAe;MAAfN,EAAA,CAAAI,UAAA,QAAAsM,GAAA,CAAAtI,OAAA,CAAe;MAE3GpE,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8C,aAAA,CAAA4J,GAAA,CAAAjC,QAAA,+BAEC;MAK8EzK,EAAA,CAAAM,SAAA,GAA2B;MAA3BN,EAAA,CAAAI,UAAA,gBAAAsM,GAAA,CAAA1J,WAAA,CAA2B;MAC1GhD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAsB,UAAA,CAAAoL,GAAA,CAAA5I,aAAA,CAEC;MAGD9D,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8C,aAAA,CAAA4J,GAAA,CAAAjC,QAAA,mCAEC;MAKwEzK,EAAA,CAAAM,SAAA,GAA2B;MAA3BN,EAAA,CAAAI,UAAA,gBAAAsM,GAAA,CAAA1J,WAAA,CAA2B;MACpGhD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAsB,UAAA,CAAAoL,GAAA,CAAA3I,UAAA,CAEC;MAGD/D,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8C,aAAA,CAAA4J,GAAA,CAAAjC,QAAA,gCAEC;MAMDzK,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAsB,UAAA,CAAAoL,GAAA,CAAA1I,cAAA,CAEC;MAGDhE,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8C,aAAA,CAAA4J,GAAA,CAAAjC,QAAA,wCAEC;MAMDzK,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAsB,UAAA,CAAAoL,GAAA,CAAAzI,UAAA,CAEC;MAGDjE,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8C,aAAA,CAAA4J,GAAA,CAAAjC,QAAA,iCAEC;MAODzK,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA8C,aAAA,CAAA4J,GAAA,CAAAjC,QAAA,iCAEC;MAaHzK,EAAA,CAAAM,SAAA,GAwEC;MAxEDN,EAAA,CAAAsB,UAAA,CAAAoL,GAAA,CAAArH,cAAA,CAAAsI,QAAA,CAwEC;MAKC3N,EAAA,CAAAM,SAAA,GAAqB;MAACN,EAAtB,CAAAI,UAAA,YAAAsM,GAAA,CAAA9M,SAAA,CAAqB,mBAAA8M,GAAA,CAAA7M,cAAA,CAAkC;MAKlBG,EAAA,CAAAM,SAAA,GAA4D;MAA5DN,EAAA,CAAAI,UAAA,aAAAsM,GAAA,CAAAxI,QAAA,CAAAsF,OAAA,IAAAkD,GAAA,CAAArH,cAAA,CAAA2C,MAAA,OAA4D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}