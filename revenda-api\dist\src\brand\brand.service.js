"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BrandService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const brand_entity_1 = require("./brand.entity");
let BrandService = class BrandService {
    brandRepository;
    constructor(brandRepository) {
        this.brandRepository = brandRepository;
    }
    async create(createBrandDto) {
        const existingBrand = await this.brandRepository.findOne({
            where: { name: createBrandDto.name }
        });
        if (existingBrand) {
            throw new common_1.ConflictException(`Marca '${createBrandDto.name}' já existe no sistema`);
        }
        const brand = this.brandRepository.create(createBrandDto);
        return this.brandRepository.save(brand);
    }
    findAll() {
        return this.brandRepository.find({
            relations: ['phones'],
        });
    }
    findOne(id) {
        return this.brandRepository.findOne({
            where: { id },
            relations: ['phones'],
        });
    }
    async update(id, updateBrandDto) {
        if (updateBrandDto.name) {
            const existingBrand = await this.brandRepository.findOne({
                where: { name: updateBrandDto.name }
            });
            if (existingBrand && existingBrand.id !== id) {
                throw new common_1.ConflictException(`Marca '${updateBrandDto.name}' já existe no sistema`);
            }
        }
        return this.brandRepository.update(id, updateBrandDto);
    }
    async remove(id) {
        const brand = await this.brandRepository.findOne({
            where: { id },
            relations: ['phones'],
        });
        if (!brand) {
            throw new common_1.BadRequestException(`Marca com ID ${id} não encontrada`);
        }
        if (brand.phones && brand.phones.length > 0) {
            throw new common_1.BadRequestException(`Não é possível deletar a marca '${brand.name}' pois existem ${brand.phones.length} celular(es) associado(s) a ela`);
        }
        return this.brandRepository.delete(id);
    }
};
exports.BrandService = BrandService;
exports.BrandService = BrandService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(brand_entity_1.Brand)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], BrandService);
//# sourceMappingURL=brand.service.js.map