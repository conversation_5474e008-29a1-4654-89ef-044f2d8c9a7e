.dashboard-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

// Resumo Financeiro
.summary-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  ion-card-header {
    background-color: var(--ion-color-secondary);
    color: white;
    border-radius: 12px 12px 0 0;
    
    ion-card-title {
      font-size: 1.3rem;
      font-weight: 600;
      color: white;
    }
  }
}

.financial-summary {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 10px 0;
  
  .summary-item {
    text-align: center;
    padding: 10px;
    min-width: 120px;
    
    h2 {
      font-size: 1.8rem;
      font-weight: 700;
      margin: 0;
      color: var(--ion-color-secondary);
    }
    
    p {
      margin: 5px 0 0;
      color: var(--ion-color-medium);
      font-size: 0.9rem;
    }
  }
}

// Estatísticas
.stats-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-bottom: 20px;
  
  @media (min-width: 768px) {
    grid-template-columns: 1fr 1fr;
  }
}

.stats-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  height: 100%;
  
  ion-card-header {
    background-color: var(--ion-color-light);
    
    ion-card-title {
      font-size: 1.2rem;
      font-weight: 600;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 15px;
  padding: 10px 0;
  
  .stats-item {
    text-align: center;
    padding: 10px;
    
    ion-icon {
      font-size: 2rem;
      margin-bottom: 8px;
    }
    
    h3 {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0;
      color: var(--ion-color-dark);
    }
    
    p {
      margin: 5px 0 0;
      color: var(--ion-color-medium);
      font-size: 0.9rem;
    }
  }
}

// Últimas Vendas
.recent-sales-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  ion-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    ion-card-title {
      font-size: 1.2rem;
      font-weight: 600;
    }
  }
  
  ion-item {
    --padding-start: 16px;
    --padding-end: 16px;
    --padding-top: 12px;
    --padding-bottom: 12px;
    
    h2 {
      font-weight: 600;
      margin-bottom: 4px;
    }
  }
}

.sale-info {
  text-align: right;
  
  ion-badge {
    margin-bottom: 8px;
    padding: 6px 8px;
    border-radius: 4px;
  }
  
  h3 {
    font-weight: 700;
    margin: 8px 0 0;
  }
}