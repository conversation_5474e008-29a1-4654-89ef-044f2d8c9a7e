{"version": 3, "file": "1703000002000-CreatePhonesTable.js", "sourceRoot": "", "sources": ["../../../../src/db/migrations/1703000002000-CreatePhonesTable.ts"], "names": [], "mappings": ";;;AAAA,qCAAkF;AAElF,MAAa,8BAA8B;IAClC,KAAK,CAAC,EAAE,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,WAAW,CAC3B,IAAI,eAAK,CAAC;YACR,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,KAAK;oBACX,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,IAAI;oBACjB,kBAAkB,EAAE,WAAW;iBAChC;gBACD;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;iBACd;gBACD;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;iBACd;gBACD;oBACE,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,MAAM;iBACb;gBACD;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,EAAE;oBACb,KAAK,EAAE,CAAC;iBACT;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;iBACd;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,KAAK;iBACZ;aACF;SACF,CAAC,EACF,IAAI,CACL,CAAC;QAEF,MAAM,WAAW,CAAC,gBAAgB,CAChC,QAAQ,EACR,IAAI,yBAAe,CAAC;YAClB,WAAW,EAAE,CAAC,UAAU,CAAC;YACzB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,QAAQ;YAC7B,QAAQ,EAAE,SAAS;YACnB,QAAQ,EAAE,SAAS;SACpB,CAAC,CACH,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;CACF;AA9DD,wEA8DC"}