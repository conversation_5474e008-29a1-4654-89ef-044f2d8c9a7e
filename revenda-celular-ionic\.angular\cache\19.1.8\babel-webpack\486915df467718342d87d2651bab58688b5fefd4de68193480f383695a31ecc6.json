{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n  return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n  return typeof color === 'string' && color.length > 0 ? Object.assign({\n    'ion-color': true,\n    [`ion-color-${color}`]: true\n  }, cssClassMap) : cssClassMap;\n};\nconst getClassList = classes => {\n  if (classes !== undefined) {\n    const array = Array.isArray(classes) ? classes : classes.split(' ');\n    return array.filter(c => c != null).map(c => c.trim()).filter(c => c !== '');\n  }\n  return [];\n};\nconst getClassMap = classes => {\n  const map = {};\n  getClassList(classes).forEach(c => map[c] = true);\n  return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (url, ev, direction, animation) {\n    if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n      const router = document.querySelector('ion-router');\n      if (router) {\n        if (ev != null) {\n          ev.preventDefault();\n        }\n        return router.push(url, direction, animation);\n      }\n    }\n    return false;\n  });\n  return function openURL(_x, _x2, _x3, _x4) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };", "map": {"version": 3, "names": ["hostContext", "selector", "el", "closest", "createColorClasses", "color", "cssClassMap", "length", "Object", "assign", "getClassList", "classes", "undefined", "array", "Array", "isArray", "split", "filter", "c", "map", "trim", "getClassMap", "for<PERSON>ach", "SCHEME", "openURL", "_ref", "_asyncToGenerator", "url", "ev", "direction", "animation", "test", "router", "document", "querySelector", "preventDefault", "push", "_x", "_x2", "_x3", "_x4", "apply", "arguments", "g", "h", "o"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/theme-01f3f29c.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst hostContext = (selector, el) => {\n    return el.closest(selector) !== null;\n};\n/**\n * Create the mode and color classes for the component based on the classes passed in\n */\nconst createColorClasses = (color, cssClassMap) => {\n    return typeof color === 'string' && color.length > 0\n        ? Object.assign({ 'ion-color': true, [`ion-color-${color}`]: true }, cssClassMap) : cssClassMap;\n};\nconst getClassList = (classes) => {\n    if (classes !== undefined) {\n        const array = Array.isArray(classes) ? classes : classes.split(' ');\n        return array\n            .filter((c) => c != null)\n            .map((c) => c.trim())\n            .filter((c) => c !== '');\n    }\n    return [];\n};\nconst getClassMap = (classes) => {\n    const map = {};\n    getClassList(classes).forEach((c) => (map[c] = true));\n    return map;\n};\nconst SCHEME = /^[a-z][a-z0-9+\\-.]*:/;\nconst openURL = async (url, ev, direction, animation) => {\n    if (url != null && url[0] !== '#' && !SCHEME.test(url)) {\n        const router = document.querySelector('ion-router');\n        if (router) {\n            if (ev != null) {\n                ev.preventDefault();\n            }\n            return router.push(url, direction, animation);\n        }\n    }\n    return false;\n};\n\nexport { createColorClasses as c, getClassMap as g, hostContext as h, openURL as o };\n"], "mappings": ";AAAA;AACA;AACA;AACA,MAAMA,WAAW,GAAGA,CAACC,QAAQ,EAAEC,EAAE,KAAK;EAClC,OAAOA,EAAE,CAACC,OAAO,CAACF,QAAQ,CAAC,KAAK,IAAI;AACxC,CAAC;AACD;AACA;AACA;AACA,MAAMG,kBAAkB,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;EAC/C,OAAO,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,MAAM,GAAG,CAAC,GAC9CC,MAAM,CAACC,MAAM,CAAC;IAAE,WAAW,EAAE,IAAI;IAAE,CAAC,aAAaJ,KAAK,EAAE,GAAG;EAAK,CAAC,EAAEC,WAAW,CAAC,GAAGA,WAAW;AACvG,CAAC;AACD,MAAMI,YAAY,GAAIC,OAAO,IAAK;EAC9B,IAAIA,OAAO,KAAKC,SAAS,EAAE;IACvB,MAAMC,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACJ,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACK,KAAK,CAAC,GAAG,CAAC;IACnE,OAAOH,KAAK,CACPI,MAAM,CAAEC,CAAC,IAAKA,CAAC,IAAI,IAAI,CAAC,CACxBC,GAAG,CAAED,CAAC,IAAKA,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CACpBH,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;EAChC;EACA,OAAO,EAAE;AACb,CAAC;AACD,MAAMG,WAAW,GAAIV,OAAO,IAAK;EAC7B,MAAMQ,GAAG,GAAG,CAAC,CAAC;EACdT,YAAY,CAACC,OAAO,CAAC,CAACW,OAAO,CAAEJ,CAAC,IAAMC,GAAG,CAACD,CAAC,CAAC,GAAG,IAAK,CAAC;EACrD,OAAOC,GAAG;AACd,CAAC;AACD,MAAMI,MAAM,GAAG,sBAAsB;AACrC,MAAMC,OAAO;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,GAAG,EAAEC,EAAE,EAAEC,SAAS,EAAEC,SAAS,EAAK;IACrD,IAAIH,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACQ,IAAI,CAACJ,GAAG,CAAC,EAAE;MACpD,MAAMK,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC;MACnD,IAAIF,MAAM,EAAE;QACR,IAAIJ,EAAE,IAAI,IAAI,EAAE;UACZA,EAAE,CAACO,cAAc,CAAC,CAAC;QACvB;QACA,OAAOH,MAAM,CAACI,IAAI,CAACT,GAAG,EAAEE,SAAS,EAAEC,SAAS,CAAC;MACjD;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EAAA,gBAXKN,OAAOA,CAAAa,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAf,IAAA,CAAAgB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWZ;AAED,SAAStC,kBAAkB,IAAIc,CAAC,EAAEG,WAAW,IAAIsB,CAAC,EAAE3C,WAAW,IAAI4C,CAAC,EAAEpB,OAAO,IAAIqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}