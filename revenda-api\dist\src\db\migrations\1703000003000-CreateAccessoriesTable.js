"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAccessoriesTable1703000003000 = void 0;
const typeorm_1 = require("typeorm");
class CreateAccessoriesTable1703000003000 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'accessories',
            columns: [
                {
                    name: 'id',
                    type: 'int',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '200',
                },
                {
                    name: 'description',
                    type: 'text',
                },
                {
                    name: 'price',
                    type: 'decimal',
                    precision: 10,
                    scale: 2,
                },
                {
                    name: 'category',
                    type: 'varchar',
                    length: '100',
                },
                {
                    name: 'image',
                    type: 'varchar',
                    length: '500',
                },
                {
                    name: 'stock',
                    type: 'int',
                    default: 0,
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('accessories');
    }
}
exports.CreateAccessoriesTable1703000003000 = CreateAccessoriesTable1703000003000;
//# sourceMappingURL=1703000003000-CreateAccessoriesTable.js.map