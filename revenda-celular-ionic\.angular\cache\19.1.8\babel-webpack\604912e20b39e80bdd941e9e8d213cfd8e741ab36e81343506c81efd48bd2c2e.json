{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*\n Stencil Client Platform v4.20.0 | MIT Licensed | https://stenciljs.com\n */\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\n\n// src/client/client-build.ts\nimport { BUILD } from \"@stencil/core/internal/app-data\";\nvar Build = {\n  isDev: BUILD.isDev ? true : false,\n  isBrowser: true,\n  isServer: false,\n  isTesting: BUILD.isTesting ? true : false\n};\n\n// src/client/client-host-ref.ts\nimport { BUILD as BUILD2 } from \"@stencil/core/internal/app-data\";\nvar hostRefs = BUILD2.hotModuleReplacement ? window.__STENCIL_HOSTREFS__ || (window.__STENCIL_HOSTREFS__ = /* @__PURE__ */new WeakMap()) : /* @__PURE__ */new WeakMap();\nvar getHostRef = ref => hostRefs.get(ref);\nvar registerInstance = (lazyInstance, hostRef) => hostRefs.set(hostRef.$lazyInstance$ = lazyInstance, hostRef);\nvar registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: /* @__PURE__ */new Map()\n  };\n  if (BUILD2.isDev) {\n    hostRef.$renderCount$ = 0;\n  }\n  if (BUILD2.method && BUILD2.lazyLoad) {\n    hostRef.$onInstancePromise$ = new Promise(r => hostRef.$onInstanceResolve$ = r);\n  }\n  if (BUILD2.asyncLoading) {\n    hostRef.$onReadyPromise$ = new Promise(r => hostRef.$onReadyResolve$ = r);\n    hostElement[\"s-p\"] = [];\n    hostElement[\"s-rc\"] = [];\n  }\n  return hostRefs.set(hostElement, hostRef);\n};\nvar isMemberInElement = (elm, memberName) => memberName in elm;\n\n// src/client/client-load-module.ts\nimport { BUILD as BUILD4 } from \"@stencil/core/internal/app-data\";\n\n// src/client/client-log.ts\nimport { BUILD as BUILD3 } from \"@stencil/core/internal/app-data\";\nvar customError;\nvar consoleError = (e, el) => (customError || console.error)(e, el);\nvar STENCIL_DEV_MODE = BUILD3.isTesting ? [\"STENCIL:\"] : [\"%cstencil\", \"color: white;background:#4c47ff;font-weight: bold; font-size:10px; padding:2px 6px; border-radius: 5px\"];\nvar consoleDevError = (...m) => console.error(...STENCIL_DEV_MODE, ...m);\nvar consoleDevWarn = (...m) => console.warn(...STENCIL_DEV_MODE, ...m);\nvar consoleDevInfo = (...m) => console.info(...STENCIL_DEV_MODE, ...m);\nvar setErrorHandler = handler => customError = handler;\n\n// src/client/client-load-module.ts\nvar cmpModules = /* @__PURE__ */new Map();\nvar MODULE_IMPORT_PREFIX = \"./\";\nvar loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  const exportName = cmpMeta.$tagName$.replace(/-/g, \"_\");\n  const bundleId = cmpMeta.$lazyBundleId$;\n  if (BUILD4.isDev && typeof bundleId !== \"string\") {\n    consoleDevError(`Trying to lazily load component <${cmpMeta.$tagName$}> with style mode \"${hostRef.$modeName$}\", but it does not exist.`);\n    return void 0;\n  } else if (!bundleId) {\n    return void 0;\n  }\n  const module = !BUILD4.hotModuleReplacement ? cmpModules.get(bundleId) : false;\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(/* @vite-ignore */\n  /* webpackInclude: /\\.entry\\.js$/ */\n  /* webpackExclude: /\\.system\\.entry\\.js$/ */\n  /* webpackMode: \"lazy\" */\n  `./${bundleId}.entry.js${BUILD4.hotModuleReplacement && hmrVersionId ? \"?s-hmr=\" + hmrVersionId : \"\"}`).then(importedModule => {\n    if (!BUILD4.hotModuleReplacement) {\n      cmpModules.set(bundleId, importedModule);\n    }\n    return importedModule[exportName];\n  }, consoleError);\n};\n\n// src/client/client-style.ts\nvar styles = /* @__PURE__ */new Map();\nvar modeResolutionChain = [];\n\n// src/client/client-task-queue.ts\nimport { BUILD as BUILD6 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/runtime-constants.ts\nvar CONTENT_REF_ID = \"r\";\nvar ORG_LOCATION_ID = \"o\";\nvar SLOT_NODE_ID = \"s\";\nvar TEXT_NODE_ID = \"t\";\nvar HYDRATE_ID = \"s-id\";\nvar HYDRATED_STYLE_ID = \"sty-id\";\nvar HYDRATE_CHILD_ID = \"c-id\";\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar SLOT_FB_CSS = \"slot-fb{display:contents}slot-fb[hidden]{display:none}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS = [\"formAssociatedCallback\", \"formResetCallback\", \"formDisabledCallback\", \"formStateRestoreCallback\"];\n\n// src/client/client-window.ts\nimport { BUILD as BUILD5 } from \"@stencil/core/internal/app-data\";\nvar win = typeof window !== \"undefined\" ? window : {};\nvar doc = win.document || {\n  head: {}\n};\nvar H = win.HTMLElement || class {};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: h2 => h2(),\n  raf: h2 => requestAnimationFrame(h2),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nvar setPlatformHelpers = helpers => {\n  Object.assign(plt, helpers);\n};\nvar supportsShadow = BUILD5.shadowDom;\nvar supportsListenerOptions = /* @__PURE__ */(() => {\n  let supportsListenerOptions2 = false;\n  try {\n    doc.addEventListener(\"e\", null, Object.defineProperty({}, \"passive\", {\n      get() {\n        supportsListenerOptions2 = true;\n      }\n    }));\n  } catch (e) {}\n  return supportsListenerOptions2;\n})();\nvar promiseResolve = v => Promise.resolve(v);\nvar supportsConstructableStylesheets = BUILD5.constructableCSS ? /* @__PURE__ */(() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {}\n  return false;\n})() : false;\n\n// src/client/client-task-queue.ts\nvar queueCongestion = 0;\nvar queuePending = false;\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueDomWritesLow = [];\nvar queueTask = (queue, write) => cb => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nvar consume = queue => {\n  for (let i2 = 0; i2 < queue.length; i2++) {\n    try {\n      queue[i2](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nvar consumeTimeout = (queue, timeout) => {\n  let i2 = 0;\n  let ts = 0;\n  while (i2 < queue.length && (ts = performance.now()) < timeout) {\n    try {\n      queue[i2++](ts);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  if (i2 === queue.length) {\n    queue.length = 0;\n  } else if (i2 !== 0) {\n    queue.splice(0, i2);\n  }\n};\nvar flush = () => {\n  if (BUILD6.asyncQueue) {\n    queueCongestion++;\n  }\n  consume(queueDomReads);\n  if (BUILD6.asyncQueue) {\n    const timeout = (plt.$flags$ & 6 /* queueMask */) === 2 /* appLoaded */ ? performance.now() + 14 * Math.ceil(queueCongestion * (1 / 10)) : Infinity;\n    consumeTimeout(queueDomWrites, timeout);\n    consumeTimeout(queueDomWritesLow, timeout);\n    if (queueDomWrites.length > 0) {\n      queueDomWritesLow.push(...queueDomWrites);\n      queueDomWrites.length = 0;\n    }\n    if (queuePending = queueDomReads.length + queueDomWrites.length + queueDomWritesLow.length > 0) {\n      plt.raf(flush);\n    } else {\n      queueCongestion = 0;\n    }\n  } else {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = cb => promiseResolve().then(cb);\nvar readTask = /* @__PURE__ */queueTask(queueDomReads, false);\nvar writeTask = /* @__PURE__ */queueTask(queueDomWrites, true);\n\n// src/client/index.ts\nimport { BUILD as BUILD27, Env, NAMESPACE as NAMESPACE2 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/asset-path.ts\nvar getAssetPath = path => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\nvar setAssetPath = path => plt.$resourcesUrl$ = path;\n\n// src/runtime/bootstrap-custom-element.ts\nimport { BUILD as BUILD24 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/constants.ts\nvar EMPTY_OBJ = {};\nvar SVG_NS = \"http://www.w3.org/2000/svg\";\nvar HTML_NS = \"http://www.w3.org/1999/xhtml\";\n\n// src/utils/helpers.ts\nvar isDef = v => v != null;\nvar isComplexType = o => {\n  o = typeof o;\n  return o === \"object\" || o === \"function\";\n};\n\n// src/utils/query-nonce-meta-tag-content.ts\nfunction queryNonceMetaTagContent(doc2) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc2.head) == null ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) == null ? void 0 : _b.getAttribute(\"content\")) != null ? _c : void 0;\n}\n\n// src/utils/result.ts\nvar result_exports = {};\n__export(result_exports, {\n  err: () => err,\n  map: () => map,\n  ok: () => ok,\n  unwrap: () => unwrap,\n  unwrapErr: () => unwrapErr\n});\nvar ok = value => ({\n  isOk: true,\n  isErr: false,\n  value\n});\nvar err = value => ({\n  isOk: false,\n  isErr: true,\n  value\n});\nfunction map(result, fn) {\n  if (result.isOk) {\n    const val = fn(result.value);\n    if (val instanceof Promise) {\n      return val.then(newVal => ok(newVal));\n    } else {\n      return ok(val);\n    }\n  }\n  if (result.isErr) {\n    const value = result.value;\n    return err(value);\n  }\n  throw \"should never get here\";\n}\nvar unwrap = result => {\n  if (result.isOk) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar unwrapErr = result => {\n  if (result.isErr) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\n\n// src/runtime/connected-callback.ts\nimport { BUILD as BUILD21 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/client-hydrate.ts\nimport { BUILD as BUILD9 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/profile.ts\nimport { BUILD as BUILD7 } from \"@stencil/core/internal/app-data\";\nvar i = 0;\nvar createTime = (fnName, tagName = \"\") => {\n  if (BUILD7.profile && performance.mark) {\n    const key = `st:${fnName}:${tagName}:${i++}`;\n    performance.mark(key);\n    return () => performance.measure(`[Stencil] ${fnName}() <${tagName}>`, key);\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar uniqueTime = (key, measureText) => {\n  if (BUILD7.profile && performance.mark) {\n    if (performance.getEntriesByName(key, \"mark\").length === 0) {\n      performance.mark(key);\n    }\n    return () => {\n      if (performance.getEntriesByName(measureText, \"measure\").length === 0) {\n        performance.measure(measureText, key);\n      }\n    };\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar inspect = ref => {\n  const hostRef = getHostRef(ref);\n  if (!hostRef) {\n    return void 0;\n  }\n  const flags = hostRef.$flags$;\n  const hostElement = hostRef.$hostElement$;\n  return {\n    renderCount: hostRef.$renderCount$,\n    flags: {\n      hasRendered: !!(flags & 2 /* hasRendered */),\n      hasConnected: !!(flags & 1 /* hasConnected */),\n      isWaitingForChildren: !!(flags & 4 /* isWaitingForChildren */),\n      isConstructingInstance: !!(flags & 8 /* isConstructingInstance */),\n      isQueuedForUpdate: !!(flags & 16 /* isQueuedForUpdate */),\n      hasInitializedComponent: !!(flags & 32 /* hasInitializedComponent */),\n      hasLoadedComponent: !!(flags & 64 /* hasLoadedComponent */),\n      isWatchReady: !!(flags & 128 /* isWatchReady */),\n      isListenReady: !!(flags & 256 /* isListenReady */),\n      needsRerender: !!(flags & 512 /* needsRerender */)\n    },\n    instanceValues: hostRef.$instanceValues$,\n    ancestorComponent: hostRef.$ancestorComponent$,\n    hostElement,\n    lazyInstance: hostRef.$lazyInstance$,\n    vnode: hostRef.$vnode$,\n    modeName: hostRef.$modeName$,\n    onReadyPromise: hostRef.$onReadyPromise$,\n    onReadyResolve: hostRef.$onReadyResolve$,\n    onInstancePromise: hostRef.$onInstancePromise$,\n    onInstanceResolve: hostRef.$onInstanceResolve$,\n    onRenderResolve: hostRef.$onRenderResolve$,\n    queuedListeners: hostRef.$queuedListeners$,\n    rmListeners: hostRef.$rmListeners$,\n    [\"s-id\"]: hostElement[\"s-id\"],\n    [\"s-cr\"]: hostElement[\"s-cr\"],\n    [\"s-lr\"]: hostElement[\"s-lr\"],\n    [\"s-p\"]: hostElement[\"s-p\"],\n    [\"s-rc\"]: hostElement[\"s-rc\"],\n    [\"s-sc\"]: hostElement[\"s-sc\"]\n  };\n};\nvar installDevTools = () => {\n  if (BUILD7.devTools) {\n    const stencil = win.stencil = win.stencil || {};\n    const originalInspect = stencil.inspect;\n    stencil.inspect = ref => {\n      let result = inspect(ref);\n      if (!result && typeof originalInspect === \"function\") {\n        result = originalInspect(ref);\n      }\n      return result;\n    };\n  }\n};\n\n// src/runtime/vdom/h.ts\nimport { BUILD as BUILD8 } from \"@stencil/core/internal/app-data\";\nvar h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = c => {\n    for (let i2 = 0; i2 < c.length; i2++) {\n      child = c[i2];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== \"boolean\") {\n        if (simple = typeof nodeName !== \"function\" && !isComplexType(child)) {\n          child = String(child);\n        } else if (BUILD8.isDev && typeof nodeName !== \"function\" && child.$flags$ === void 0) {\n          consoleDevError(`vNode passed as children has unexpected type.\nMake sure it's using the correct h() function.\nEmpty objects can also be the cause, look for JSX comments that became objects.`);\n        }\n        if (simple && lastSimple) {\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (BUILD8.isDev && nodeName === \"input\") {\n      validateInputProperties(vnodeData);\n    }\n    if (BUILD8.vdomKey && vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (BUILD8.slotRelocation && vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    if (BUILD8.vdomClass) {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== \"object\" ? classData : Object.keys(classData).filter(k => classData[k]).join(\" \");\n      }\n    }\n  }\n  if (BUILD8.isDev && vNodeChildren.some(isHost)) {\n    consoleDevError(`The <Host> must be the single root component. Make sure:\n- You are NOT using hostData() and <Host> in the same component.\n- <Host> is used once, and it's the single root component of the render() function.`);\n  }\n  if (BUILD8.vdomFunctional && typeof nodeName === \"function\") {\n    return nodeName(vnodeData === null ? {} : vnodeData, vNodeChildren, vdomFnUtils);\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  if (BUILD8.vdomKey) {\n    vnode.$key$ = key;\n  }\n  if (BUILD8.slotRelocation) {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\nvar newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  if (BUILD8.vdomAttribute) {\n    vnode.$attrs$ = null;\n  }\n  if (BUILD8.vdomKey) {\n    vnode.$key$ = null;\n  }\n  if (BUILD8.slotRelocation) {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nvar Host = {};\nvar isHost = node => node && node.$tag$ === Host;\nvar vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\nvar convertToPublic = node => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\nvar convertToPrivate = node => {\n  if (typeof node.vtag === \"function\") {\n    const vnodeData = {\n      ...node.vattrs\n    };\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...(node.vchildren || []));\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\nvar validateInputProperties = inputElm => {\n  const props = Object.keys(inputElm);\n  const value = props.indexOf(\"value\");\n  if (value === -1) {\n    return;\n  }\n  const typeIndex = props.indexOf(\"type\");\n  const minIndex = props.indexOf(\"min\");\n  const maxIndex = props.indexOf(\"max\");\n  const stepIndex = props.indexOf(\"step\");\n  if (value < typeIndex || value < minIndex || value < maxIndex || value < stepIndex) {\n    consoleDevWarn(`The \"value\" prop of <input> should be set after \"min\", \"max\", \"type\" and \"step\"`);\n  }\n};\n\n// src/runtime/client-hydrate.ts\nvar initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  const endHydrate = createTime(\"hydrateClient\", tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const shadowRootNodes = BUILD9.shadowDom && shadowRoot ? [] : null;\n  const vnode = hostRef.$vnode$ = newVNode(tagName, null);\n  if (!plt.$orgLocNodes$) {\n    initializeDocumentHydrate(doc.body, plt.$orgLocNodes$ = /* @__PURE__ */new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId);\n  childRenderNodes.map(c => {\n    const orgLocationId = c.$hostId$ + \".\" + c.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = c.$elm$;\n    if (orgLocationNode && supportsShadow && orgLocationNode[\"s-en\"] === \"\") {\n      orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n    }\n    if (!shadowRoot) {\n      node[\"s-hn\"] = tagName;\n      if (orgLocationNode) {\n        node[\"s-ol\"] = orgLocationNode;\n        node[\"s-ol\"][\"s-nr\"] = node;\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  });\n  if (BUILD9.shadowDom && shadowRoot) {\n    shadowRootNodes.map(shadowRootNode => {\n      if (shadowRootNode) {\n        shadowRoot.appendChild(shadowRootNode);\n      }\n    });\n  }\n  endHydrate();\n};\nvar clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i2;\n  if (node.nodeType === 1 /* ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      childIdSplt = childNodeType.split(\".\");\n      if (childIdSplt[0] === hostId || childIdSplt[0] === \"0\") {\n        childVNode = {\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          $attrs$: null,\n          $children$: null,\n          $key$: null,\n          $name$: null,\n          $text$: null\n        };\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        parentVNode.$children$[childVNode.$index$] = childVNode;\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    if (node.shadowRoot) {\n      for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {\n        clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.shadowRoot.childNodes[i2], hostId);\n      }\n    }\n    for (i2 = node.childNodes.length - 1; i2 >= 0; i2--) {\n      clientHydrate(parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node.childNodes[i2], hostId);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[1] === hostId || childIdSplt[1] === \"0\") {\n      childNodeType = childIdSplt[0];\n      childVNode = {\n        $flags$: 0,\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4],\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      };\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = node.nextSibling;\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          node.remove();\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n          if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        if (childNodeType === SLOT_NODE_ID) {\n          childVNode.$tag$ = \"slot\";\n          if (childIdSplt[5]) {\n            node[\"s-sn\"] = childVNode.$name$ = childIdSplt[5];\n          } else {\n            node[\"s-sn\"] = \"\";\n          }\n          node[\"s-sr\"] = true;\n          if (BUILD9.shadowDom && shadowRootNodes) {\n            childVNode.$elm$ = doc.createElement(childVNode.$tag$);\n            if (childVNode.$name$) {\n              childVNode.$elm$.setAttribute(\"name\", childVNode.$name$);\n            }\n            node.parentNode.insertBefore(childVNode.$elm$, node);\n            node.remove();\n            if (childVNode.$depth$ === \"0\") {\n              shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n            }\n          }\n          slotNodes.push(childVNode);\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        } else if (childNodeType === CONTENT_REF_ID) {\n          if (BUILD9.shadowDom && shadowRootNodes) {\n            node.remove();\n          } else if (BUILD9.slotRelocation) {\n            hostElm[\"s-cr\"] = node;\n            node[\"s-cn\"] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === \"style\") {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = \"0\";\n    parentVNode.$children$ = [vnode];\n  }\n};\nvar initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* ElementNode */) {\n    let i2 = 0;\n    if (node.shadowRoot) {\n      for (; i2 < node.shadowRoot.childNodes.length; i2++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);\n      }\n    }\n    for (i2 = 0; i2 < node.childNodes.length; i2++) {\n      initializeDocumentHydrate(node.childNodes[i2], orgLocNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    const childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + \".\" + childIdSplt[2], node);\n      node.nodeValue = \"\";\n      node[\"s-en\"] = childIdSplt[3];\n    }\n  }\n};\n\n// src/runtime/initialize-component.ts\nimport { BUILD as BUILD20 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/mode.ts\nvar computeMode = elm => modeResolutionChain.map(h2 => h2(elm)).find(m => !!m);\nvar setMode = handler => modeResolutionChain.push(handler);\nvar getMode = ref => getHostRef(ref).$modeName$;\n\n// src/runtime/proxy-component.ts\nimport { BUILD as BUILD19 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/set-value.ts\nimport { BUILD as BUILD18 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/parse-property-value.ts\nimport { BUILD as BUILD10 } from \"@stencil/core/internal/app-data\";\nvar parsePropertyValue = (propValue, propType) => {\n  if (propValue != null && !isComplexType(propValue)) {\n    if (BUILD10.propBoolean && propType & 4 /* Boolean */) {\n      return propValue === \"false\" ? false : propValue === \"\" || !!propValue;\n    }\n    if (BUILD10.propNumber && propType & 2 /* Number */) {\n      return parseFloat(propValue);\n    }\n    if (BUILD10.propString && propType & 1 /* String */) {\n      return String(propValue);\n    }\n    return propValue;\n  }\n  return propValue;\n};\n\n// src/runtime/update-component.ts\nimport { BUILD as BUILD17, NAMESPACE } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/event-emitter.ts\nimport { BUILD as BUILD12 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/element.ts\nimport { BUILD as BUILD11 } from \"@stencil/core/internal/app-data\";\nvar getElement = ref => BUILD11.lazyLoad ? getHostRef(ref).$hostElement$ : ref;\n\n// src/runtime/event-emitter.ts\nvar createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: detail => {\n      if (BUILD12.isDev && !elm.isConnected) {\n        consoleDevWarn(`The \"${name}\" event was emitted, but the dispatcher node is no longer connected to the dom.`);\n      }\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* Bubbles */),\n        composed: !!(flags & 2 /* Composed */),\n        cancelable: !!(flags & 1 /* Cancellable */),\n        detail\n      });\n    }\n  };\n};\nvar emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\n\n// src/runtime/styles.ts\nimport { BUILD as BUILD13 } from \"@stencil/core/internal/app-data\";\nvar rootAppliedStyles = /* @__PURE__ */new WeakMap();\nvar registerStyle = (scopeId2, cssText, allowCS) => {\n  let style = styles.get(scopeId2);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === \"string\") {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId2, style);\n};\nvar addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId2 = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId2);\n  if (!BUILD13.attachStyles) {\n    return scopeId2;\n  }\n  styleContainerNode = styleContainerNode.nodeType === 11 /* DocumentFragment */ ? styleContainerNode : doc;\n  if (style) {\n    if (typeof style === \"string\") {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */new Set());\n      }\n      if (!appliedStyles.has(scopeId2)) {\n        if (BUILD13.hydrateClientSide && styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`))) {\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = doc.createElement(\"style\");\n          styleElm.innerHTML = style;\n          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n          if (nonce != null) {\n            styleElm.setAttribute(\"nonce\", nonce);\n          }\n          if ((BUILD13.hydrateServerSide || BUILD13.hotModuleReplacement) && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n            styleElm.setAttribute(HYDRATED_STYLE_ID, scopeId2);\n          }\n          const injectStyle =\n          /**\n           * we render a scoped component\n           */\n          !(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) ||\n          /**\n          * we are using shadow dom and render the style tag within the shadowRoot\n          */\n          cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */ && styleContainerNode.nodeName !== \"HEAD\";\n          if (injectStyle) {\n            styleContainerNode.insertBefore(styleElm, styleContainerNode.querySelector(\"link\"));\n          }\n        }\n        if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId2);\n        }\n      }\n    } else if (BUILD13.constructableCSS && !styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId2;\n};\nvar attachStyles = hostRef => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime(\"attachStyles\", cmpMeta.$tagName$);\n  const scopeId2 = addStyle(BUILD13.shadowDom && supportsShadow && elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(), cmpMeta, hostRef.$modeName$);\n  if ((BUILD13.shadowDom || BUILD13.scoped) && BUILD13.cssAnnotations && flags & 10 /* needsScopedEncapsulation */ && flags & 2 /* scopedCssEncapsulation */) {\n    elm[\"s-sc\"] = scopeId2;\n    elm.classList.add(scopeId2 + \"-h\");\n    if (BUILD13.scoped && flags & 2 /* scopedCssEncapsulation */) {\n      elm.classList.add(scopeId2 + \"-s\");\n    }\n  }\n  endAttachStyles();\n};\nvar getScopeId = (cmp, mode) => \"sc-\" + (BUILD13.mode && mode && cmp.$flags$ & 32 /* hasMode */ ? cmp.$tagName$ + \"-\" + mode : cmp.$tagName$);\n\n// src/runtime/vdom/vdom-render.ts\nimport { BUILD as BUILD16 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/update-element.ts\nimport { BUILD as BUILD15 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/set-accessor.ts\nimport { BUILD as BUILD14 } from \"@stencil/core/internal/app-data\";\nvar setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {\n  if (oldValue !== newValue) {\n    let isProp = isMemberInElement(elm, memberName);\n    let ln = memberName.toLowerCase();\n    if (BUILD14.vdomClass && memberName === \"class\") {\n      const classList = elm.classList;\n      const oldClasses = parseClassList(oldValue);\n      const newClasses = parseClassList(newValue);\n      classList.remove(...oldClasses.filter(c => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter(c => c && !oldClasses.includes(c)));\n    } else if (BUILD14.vdomStyle && memberName === \"style\") {\n      if (BUILD14.updatable) {\n        for (const prop in oldValue) {\n          if (!newValue || newValue[prop] == null) {\n            if (!BUILD14.hydrateServerSide && prop.includes(\"-\")) {\n              elm.style.removeProperty(prop);\n            } else {\n              elm.style[prop] = \"\";\n            }\n          }\n        }\n      }\n      for (const prop in newValue) {\n        if (!oldValue || newValue[prop] !== oldValue[prop]) {\n          if (!BUILD14.hydrateServerSide && prop.includes(\"-\")) {\n            elm.style.setProperty(prop, newValue[prop]);\n          } else {\n            elm.style[prop] = newValue[prop];\n          }\n        }\n      }\n    } else if (BUILD14.vdomKey && memberName === \"key\") {} else if (BUILD14.vdomRef && memberName === \"ref\") {\n      if (newValue) {\n        newValue(elm);\n      }\n    } else if (BUILD14.vdomListener && (BUILD14.lazyLoad ? !isProp : !elm.__lookupSetter__(memberName)) && memberName[0] === \"o\" && memberName[1] === \"n\") {\n      if (memberName[2] === \"-\") {\n        memberName = memberName.slice(3);\n      } else if (isMemberInElement(win, ln)) {\n        memberName = ln.slice(2);\n      } else {\n        memberName = ln[2] + memberName.slice(3);\n      }\n      if (oldValue || newValue) {\n        const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n        memberName = memberName.replace(CAPTURE_EVENT_REGEX, \"\");\n        if (oldValue) {\n          plt.rel(elm, memberName, oldValue, capture);\n        }\n        if (newValue) {\n          plt.ael(elm, memberName, newValue, capture);\n        }\n      }\n    } else if (BUILD14.vdomPropOrAttr) {\n      const isComplex = isComplexType(newValue);\n      if ((isProp || isComplex && newValue !== null) && !isSvg) {\n        try {\n          if (!elm.tagName.includes(\"-\")) {\n            const n = newValue == null ? \"\" : newValue;\n            if (memberName === \"list\") {\n              isProp = false;\n            } else if (oldValue == null || elm[memberName] != n) {\n              elm[memberName] = n;\n            }\n          } else {\n            elm[memberName] = newValue;\n          }\n        } catch (e) {}\n      }\n      let xlink = false;\n      if (BUILD14.vdomXlink) {\n        if (ln !== (ln = ln.replace(/^xlink\\:?/, \"\"))) {\n          memberName = ln;\n          xlink = true;\n        }\n      }\n      if (newValue == null || newValue === false) {\n        if (newValue !== false || elm.getAttribute(memberName) === \"\") {\n          if (BUILD14.vdomXlink && xlink) {\n            elm.removeAttributeNS(XLINK_NS, memberName);\n          } else {\n            elm.removeAttribute(memberName);\n          }\n        }\n      } else if ((!isProp || flags & 4 /* isHost */ || isSvg) && !isComplex) {\n        newValue = newValue === true ? \"\" : newValue;\n        if (BUILD14.vdomXlink && xlink) {\n          elm.setAttributeNS(XLINK_NS, memberName, newValue);\n        } else {\n          elm.setAttribute(memberName, newValue);\n        }\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = value => !value ? [] : value.split(parseClassListRegex);\nvar CAPTURE_EVENT_SUFFIX = \"Capture\";\nvar CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + \"$\");\n\n// src/runtime/vdom/update-element.ts\nvar updateElement = (oldVnode, newVnode, isSvgMode2) => {\n  const elm = newVnode.$elm$.nodeType === 11 /* DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || EMPTY_OBJ;\n  const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;\n  if (BUILD15.updatable) {\n    for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], void 0, isSvgMode2, newVnode.$flags$);\n      }\n    }\n  }\n  for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode2, newVnode.$flags$);\n  }\n};\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes(\"ref\") ?\n  // we need to sort these to ensure that `'ref'` is the last attr\n  [...attrNames.filter(attr => attr !== \"ref\"), \"ref\"] :\n  // no need to sort, return the original array\n  attrNames;\n}\n\n// src/runtime/vdom/vdom-render.ts\nvar scopeId;\nvar contentRef;\nvar hostTagName;\nvar useNativeShadowDom = false;\nvar checkSlotFallbackVisibility = false;\nvar checkSlotRelocate = false;\nvar isSvgMode = false;\nvar createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {\n  var _a;\n  const newVNode2 = newParentVNode.$children$[childIndex];\n  let i2 = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (BUILD16.slotRelocation && !useNativeShadowDom) {\n    checkSlotRelocate = true;\n    if (newVNode2.$tag$ === \"slot\") {\n      if (scopeId) {\n        parentElm.classList.add(scopeId + \"-s\");\n      }\n      newVNode2.$flags$ |= newVNode2.$children$ ?\n      // slot element has fallback content\n      // still create an element that \"mocks\" the slot element\n      2 /* isSlotFallback */ :\n      // slot element does not have fallback content\n      // create an html comment we'll use to always reference\n      // where actual slot content should sit next to\n      1 /* isSlotReference */;\n    }\n  }\n  if (BUILD16.isDev && newVNode2.$elm$) {\n    consoleDevError(`The JSX ${newVNode2.$text$ !== null ? `\"${newVNode2.$text$}\" text` : `\"${newVNode2.$tag$}\" element`} node should not be shared within the same renderer. The renderer caches element lookups in order to improve performance. However, a side effect from this is that the exact same JSX node should not be reused. For more information please see https://stenciljs.com/docs/templating-jsx#avoid-shared-jsx-nodes`);\n  }\n  if (BUILD16.vdomText && newVNode2.$text$ !== null) {\n    elm = newVNode2.$elm$ = doc.createTextNode(newVNode2.$text$);\n  } else if (BUILD16.slotRelocation && newVNode2.$flags$ & 1 /* isSlotReference */) {\n    elm = newVNode2.$elm$ = BUILD16.isDebug || BUILD16.hydrateServerSide ? slotReferenceDebugNode(newVNode2) : doc.createTextNode(\"\");\n  } else {\n    if (BUILD16.svg && !isSvgMode) {\n      isSvgMode = newVNode2.$tag$ === \"svg\";\n    }\n    elm = newVNode2.$elm$ = BUILD16.svg ? doc.createElementNS(isSvgMode ? SVG_NS : HTML_NS, !useNativeShadowDom && BUILD16.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$) : doc.createElement(!useNativeShadowDom && BUILD16.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$);\n    if (BUILD16.svg && isSvgMode && newVNode2.$tag$ === \"foreignObject\") {\n      isSvgMode = false;\n    }\n    if (BUILD16.vdomAttribute) {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n    const rootNode = elm.getRootNode();\n    const isElementWithinShadowRoot = !rootNode.querySelector(\"body\");\n    if (!isElementWithinShadowRoot && BUILD16.scoped && isDef(scopeId) && elm[\"s-si\"] !== scopeId) {\n      elm.classList.add(elm[\"s-si\"] = scopeId);\n    }\n    if (BUILD16.scoped) {\n      updateElementScopeIds(elm, parentElm);\n    }\n    if (newVNode2.$children$) {\n      for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {\n        childNode = createElm(oldParentVNode, newVNode2, i2, elm);\n        if (childNode) {\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    if (BUILD16.svg) {\n      if (newVNode2.$tag$ === \"svg\") {\n        isSvgMode = false;\n      } else if (elm.tagName === \"foreignObject\") {\n        isSvgMode = true;\n      }\n    }\n  }\n  elm[\"s-hn\"] = hostTagName;\n  if (BUILD16.slotRelocation) {\n    if (newVNode2.$flags$ & (2 /* isSlotFallback */ | 1 /* isSlotReference */)) {\n      elm[\"s-sr\"] = true;\n      elm[\"s-cr\"] = contentRef;\n      elm[\"s-sn\"] = newVNode2.$name$ || \"\";\n      elm[\"s-rf\"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {\n        if (BUILD16.experimentalSlotFixes) {\n          relocateToHostRoot(oldParentVNode.$elm$);\n        } else {\n          putBackInOriginalLocation(oldParentVNode.$elm$, false);\n        }\n      }\n    }\n  }\n  return elm;\n};\nvar relocateToHostRoot = parentElm => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const host = parentElm.closest(hostTagName.toLowerCase());\n  if (host != null) {\n    const contentRefNode = Array.from(host.childNodes).find(ref => ref[\"s-cr\"]);\n    const childNodeArray = Array.from(parentElm.childNodes);\n    for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {\n      if (childNode[\"s-sh\"] != null) {\n        insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);\n        childNode[\"s-sh\"] = void 0;\n        checkSlotRelocate = true;\n      }\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.childNodes);\n  if (parentElm[\"s-sr\"] && BUILD16.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node[\"s-sn\"] === parentElm[\"s-sn\"] && node[\"s-sh\"] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {\n    const childNode = oldSlotChildNodes[i2];\n    if (childNode[\"s-hn\"] !== hostTagName && childNode[\"s-ol\"]) {\n      insertBefore(parentReferenceNode(childNode), childNode, referenceNode(childNode));\n      childNode[\"s-ol\"].remove();\n      childNode[\"s-ol\"] = void 0;\n      childNode[\"s-sh\"] = void 0;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = BUILD16.slotRelocation && parentElm[\"s-cr\"] && parentElm[\"s-cr\"].parentNode || parentElm;\n  let childNode;\n  if (BUILD16.shadowDom && containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx, parentElm);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        insertBefore(containerElm, childNode, BUILD16.slotRelocation ? referenceNode(before) : before);\n      }\n    }\n  }\n};\nvar removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        if (BUILD16.slotRelocation) {\n          checkSlotFallbackVisibility = true;\n          if (elm[\"s-ol\"]) {\n            elm[\"s-ol\"].remove();\n          } else {\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        elm.remove();\n      }\n    }\n  }\n};\nvar updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i2 = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      if (BUILD16.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      if (BUILD16.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      idxInOld = -1;\n      if (BUILD16.vdomKey) {\n        for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {\n          if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {\n            idxInOld = i2;\n            break;\n          }\n        }\n      }\n      if (BUILD16.vdomKey && idxInOld >= 0) {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld, parentElm);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          oldCh[idxInOld] = void 0;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx, parentElm);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        if (BUILD16.slotRelocation) {\n          insertBefore(parentReferenceNode(oldStartVnode.$elm$), node, referenceNode(oldStartVnode.$elm$));\n        } else {\n          insertBefore(oldStartVnode.$elm$.parentNode, node, oldStartVnode.$elm$);\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    addVnodes(parentElm, newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$, newVNode2, newCh, newStartIdx, newEndIdx);\n  } else if (BUILD16.updatable && newStartIdx > newEndIdx) {\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\nvar isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (BUILD16.slotRelocation && leftVNode.$tag$ === \"slot\") {\n      if (\n      // The component gets hydrated and no VDOM has been initialized.\n      // Here the comparison can't happen as $name$ property is not set for `leftNode`.\n      \"$nodeId$\" in leftVNode && isInitialRender &&\n      // `leftNode` is not from type HTMLComment which would cause many\n      // hydration comments to be removed\n      leftVNode.$elm$.nodeType !== 8) {\n        return false;\n      }\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    if (BUILD16.vdomKey && !isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nvar referenceNode = node => {\n  return node && node[\"s-ol\"] || node;\n};\nvar parentReferenceNode = node => (node[\"s-ol\"] ? node[\"s-ol\"] : node).parentNode;\nvar patch = (oldVNode, newVNode2, isInitialRender = false) => {\n  const elm = newVNode2.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode2.$children$;\n  const tag = newVNode2.$tag$;\n  const text = newVNode2.$text$;\n  let defaultHolder;\n  if (!BUILD16.vdomText || text === null) {\n    if (BUILD16.svg) {\n      isSvgMode = tag === \"svg\" ? true : tag === \"foreignObject\" ? false : isSvgMode;\n    }\n    if (BUILD16.vdomAttribute || BUILD16.reflect) {\n      if (BUILD16.slot && tag === \"slot\" && !useNativeShadowDom) {\n        if (BUILD16.experimentalSlotFixes && oldVNode.$name$ !== newVNode2.$name$) {\n          newVNode2.$elm$[\"s-sn\"] = newVNode2.$name$ || \"\";\n          relocateToHostRoot(newVNode2.$elm$.parentElement);\n        }\n      } else {\n        updateElement(oldVNode, newVNode2, isSvgMode);\n      }\n    }\n    if (BUILD16.updatable && oldChildren !== null && newChildren !== null) {\n      updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      if (BUILD16.updatable && BUILD16.vdomText && oldVNode.$text$ !== null) {\n        elm.textContent = \"\";\n      }\n      addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);\n    } else if (\n    // don't do this on initial render as it can cause non-hydrated content to be removed\n    !isInitialRender && BUILD16.updatable && oldChildren !== null) {\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (BUILD16.svg && isSvgMode && tag === \"svg\") {\n      isSvgMode = false;\n    }\n  } else if (BUILD16.vdomText && BUILD16.slotRelocation && (defaultHolder = elm[\"s-cr\"])) {\n    defaultHolder.parentNode.textContent = text;\n  } else if (BUILD16.vdomText && oldVNode.$text$ !== text) {\n    elm.data = text;\n  }\n};\nvar updateFallbackSlotVisibility = elm => {\n  const childNodes = elm.childNodes;\n  for (const childNode of childNodes) {\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      if (childNode[\"s-sr\"]) {\n        const slotName = childNode[\"s-sn\"];\n        childNode.hidden = false;\n        for (const siblingNode of childNodes) {\n          if (siblingNode !== childNode) {\n            if (siblingNode[\"s-hn\"] !== childNode[\"s-hn\"] || slotName !== \"\") {\n              if (siblingNode.nodeType === 1 /* ElementNode */ && (slotName === siblingNode.getAttribute(\"slot\") || slotName === siblingNode[\"s-sn\"]) || siblingNode.nodeType === 3 /* TextNode */ && slotName === siblingNode[\"s-sn\"]) {\n                childNode.hidden = true;\n                break;\n              }\n            } else {\n              if (siblingNode.nodeType === 1 /* ElementNode */ || siblingNode.nodeType === 3 /* TextNode */ && siblingNode.textContent.trim() !== \"\") {\n                childNode.hidden = true;\n                break;\n              }\n            }\n          }\n        }\n      }\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\nvar relocateNodes = [];\nvar markSlotContentForRelocation = elm => {\n  let node;\n  let hostContentNodes;\n  let j;\n  for (const childNode of elm.childNodes) {\n    if (childNode[\"s-sr\"] && (node = childNode[\"s-cr\"]) && node.parentNode) {\n      hostContentNodes = node.parentNode.childNodes;\n      const slotName = childNode[\"s-sn\"];\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        if (!node[\"s-cn\"] && !node[\"s-nr\"] && node[\"s-hn\"] !== childNode[\"s-hn\"] && (!BUILD16.experimentalSlotFixes || !node[\"s-sh\"] || node[\"s-sh\"] !== childNode[\"s-hn\"])) {\n          if (isNodeLocatedInSlot(node, slotName)) {\n            let relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n            checkSlotFallbackVisibility = true;\n            node[\"s-sn\"] = node[\"s-sn\"] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node[\"s-sr\"]) {\n              relocateNodes.map(relocateNode => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node[\"s-sn\"])) {\n                  relocateNodeData = relocateNodes.find(r => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some(r => r.$nodeToRelocate$ === node)) {\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\nvar isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n    if (nodeToRelocate.getAttribute(\"slot\") === null && slotName === \"\") {\n      return true;\n    }\n    if (nodeToRelocate.getAttribute(\"slot\") === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate[\"s-sn\"] === slotName) {\n    return true;\n  }\n  return slotName === \"\";\n};\nvar nullifyVNodeRefs = vNode => {\n  if (BUILD16.vdomRef) {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar insertBefore = (parent, newNode, reference) => {\n  const inserted = parent == null ? void 0 : parent.insertBefore(newNode, reference);\n  if (BUILD16.scoped) {\n    updateElementScopeIds(newNode, parent);\n  }\n  return inserted;\n};\nvar findScopeIds = element => {\n  const scopeIds = [];\n  if (element) {\n    scopeIds.push(...(element[\"s-scs\"] || []), element[\"s-si\"], element[\"s-sc\"], ...findScopeIds(element.parentElement));\n  }\n  return scopeIds;\n};\nvar updateElementScopeIds = (element, parent, iterateChildNodes = false) => {\n  var _a;\n  if (element && parent && element.nodeType === 1 /* ElementNode */) {\n    const scopeIds = new Set(findScopeIds(parent).filter(Boolean));\n    if (scopeIds.size) {\n      (_a = element.classList) == null ? void 0 : _a.add(...(element[\"s-scs\"] = [...scopeIds]));\n      if (element[\"s-ol\"] || iterateChildNodes) {\n        for (const childNode of Array.from(element.childNodes)) {\n          updateElementScopeIds(childNode, element, true);\n        }\n      }\n    }\n  }\n};\nvar renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d, _e;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (BUILD16.isDev && Array.isArray(renderFnResults) && renderFnResults.some(isHost)) {\n    throw new Error(`The <Host> must be the single root component.\nLooks like the render() function of \"${hostTagName.toLowerCase()}\" is returning an array that contains the <Host>.\n\nThe render() function should look like this instead:\n\nrender() {\n  // Do not return an array\n  return (\n    <Host>{content}</Host>\n  );\n}\n  `);\n  }\n  if (BUILD16.reflect && cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]);\n  }\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      if (hostElm.hasAttribute(key) && ![\"key\", \"ref\", \"style\", \"class\"].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = BUILD16.shadowDom ? hostElm.shadowRoot || hostElm : hostElm;\n  if (BUILD16.scoped || BUILD16.shadowDom) {\n    scopeId = hostElm[\"s-sc\"];\n  }\n  useNativeShadowDom = supportsShadow && (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) !== 0;\n  if (BUILD16.slotRelocation) {\n    contentRef = hostElm[\"s-cr\"];\n    checkSlotFallbackVisibility = false;\n  }\n  patch(oldVNode, rootVnode, isInitialLoad);\n  if (BUILD16.slotRelocation) {\n    plt.$flags$ |= 1 /* isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate[\"s-ol\"]) {\n          const orgLocationNode = BUILD16.isDebug || BUILD16.hydrateServerSide ? originalLocationDebugNode(nodeToRelocate) : doc.createTextNode(\"\");\n          orgLocationNode[\"s-nr\"] = nodeToRelocate;\n          insertBefore(nodeToRelocate.parentNode, nodeToRelocate[\"s-ol\"] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          let insertBeforeNode = slotRefNode.nextSibling;\n          if (!BUILD16.experimentalSlotFixes || insertBeforeNode && insertBeforeNode.nodeType === 1 /* ElementNode */) {\n            let orgLocationNode = (_a = nodeToRelocate[\"s-ol\"]) == null ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode[\"s-nr\"]) != null ? _b : null;\n              if (refNode && refNode[\"s-sn\"] === nodeToRelocate[\"s-sn\"] && parentNodeRef === refNode.parentNode) {\n                refNode = refNode.nextSibling;\n                while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode[\"s-sr\"])) {\n                  refNode = refNode == null ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode[\"s-nr\"]) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          if (!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode || nodeToRelocate.nextSibling !== insertBeforeNode) {\n            if (nodeToRelocate !== insertBeforeNode) {\n              if (!BUILD16.experimentalSlotFixes && !nodeToRelocate[\"s-hn\"] && nodeToRelocate[\"s-ol\"]) {\n                nodeToRelocate[\"s-hn\"] = nodeToRelocate[\"s-ol\"].parentNode.nodeName;\n              }\n              insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);\n              if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n                nodeToRelocate.hidden = (_c = nodeToRelocate[\"s-ih\"]) != null ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode[\"s-rf\"] === \"function\" && slotRefNode[\"s-rf\"](nodeToRelocate);\n        } else {\n          if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n            if (isInitialLoad) {\n              nodeToRelocate[\"s-ih\"] = (_d = nodeToRelocate.hidden) != null ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    plt.$flags$ &= ~1 /* isTmpDisconnected */;\n    relocateNodes.length = 0;\n  }\n  if (BUILD16.experimentalScopedSlotChanges && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n    for (const childNode of rootVnode.$elm$.childNodes) {\n      if (childNode[\"s-hn\"] !== hostTagName && !childNode[\"s-sh\"]) {\n        if (isInitialLoad && childNode[\"s-ih\"] == null) {\n          childNode[\"s-ih\"] = (_e = childNode.hidden) != null ? _e : false;\n        }\n        childNode.hidden = true;\n      }\n    }\n  }\n  contentRef = void 0;\n};\nvar slotReferenceDebugNode = slotVNode => doc.createComment(`<slot${slotVNode.$name$ ? ' name=\"' + slotVNode.$name$ + '\"' : \"\"}> (host=${hostTagName.toLowerCase()})`);\nvar originalLocationDebugNode = nodeToRelocate => doc.createComment(`org-location for ` + (nodeToRelocate.localName ? `<${nodeToRelocate.localName}> (host=${nodeToRelocate[\"s-hn\"]})` : `[${nodeToRelocate.textContent}]`));\n\n// src/runtime/update-component.ts\nvar attachToAncestor = (hostRef, ancestorComponent) => {\n  if (BUILD17.asyncLoading && ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent[\"s-p\"]) {\n    ancestorComponent[\"s-p\"].push(new Promise(r => hostRef.$onRenderResolve$ = r));\n  }\n};\nvar scheduleUpdate = (hostRef, isInitialLoad) => {\n  if (BUILD17.taskQueue && BUILD17.updatable) {\n    hostRef.$flags$ |= 16 /* isQueuedForUpdate */;\n  }\n  if (BUILD17.asyncLoading && hostRef.$flags$ & 4 /* isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return BUILD17.taskQueue ? writeTask(dispatch) : dispatch();\n};\nvar dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime(\"scheduleUpdate\", hostRef.$cmpMeta$.$tagName$);\n  const instance = BUILD17.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  if (!instance) {\n    throw new Error(`Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \\`externalRuntime: true\\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);\n  }\n  let maybePromise;\n  if (isInitialLoad) {\n    if (BUILD17.lazyLoad && BUILD17.hostListener) {\n      hostRef.$flags$ |= 256 /* isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));\n        hostRef.$queuedListeners$ = void 0;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentWillLoad\");\n    if (BUILD17.cmpWillLoad) {\n      maybePromise = safeCall(instance, \"componentWillLoad\");\n    }\n  } else {\n    emitLifecycleEvent(elm, \"componentWillUpdate\");\n    if (BUILD17.cmpWillUpdate) {\n      maybePromise = safeCall(instance, \"componentWillUpdate\");\n    }\n  }\n  emitLifecycleEvent(elm, \"componentWillRender\");\n  if (BUILD17.cmpWillRender) {\n    maybePromise = enqueue(maybePromise, () => safeCall(instance, \"componentWillRender\"));\n  }\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\nvar enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch(err2 => {\n  console.error(err2);\n  fn();\n}) : fn();\nvar isPromisey = maybePromise => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === \"function\";\nvar updateComponent = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (hostRef, instance, isInitialLoad) {\n    var _a;\n    const elm = hostRef.$hostElement$;\n    const endUpdate = createTime(\"update\", hostRef.$cmpMeta$.$tagName$);\n    const rc = elm[\"s-rc\"];\n    if (BUILD17.style && isInitialLoad) {\n      attachStyles(hostRef);\n    }\n    const endRender = createTime(\"render\", hostRef.$cmpMeta$.$tagName$);\n    if (BUILD17.isDev) {\n      hostRef.$flags$ |= 1024 /* devOnRender */;\n    }\n    if (BUILD17.hydrateServerSide) {\n      yield callRender(hostRef, instance, elm, isInitialLoad);\n    } else {\n      callRender(hostRef, instance, elm, isInitialLoad);\n    }\n    if (BUILD17.isDev) {\n      hostRef.$renderCount$ = hostRef.$renderCount$ === void 0 ? 1 : hostRef.$renderCount$ + 1;\n      hostRef.$flags$ &= ~1024 /* devOnRender */;\n    }\n    if (BUILD17.hydrateServerSide) {\n      try {\n        serverSideConnected(elm);\n        if (isInitialLoad) {\n          if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n            elm[\"s-en\"] = \"\";\n          } else if (hostRef.$cmpMeta$.$flags$ & 2 /* scopedCssEncapsulation */) {\n            elm[\"s-en\"] = \"c\";\n          }\n        }\n      } catch (e) {\n        consoleError(e, elm);\n      }\n    }\n    if (BUILD17.asyncLoading && rc) {\n      rc.map(cb => cb());\n      elm[\"s-rc\"] = void 0;\n    }\n    endRender();\n    endUpdate();\n    if (BUILD17.asyncLoading) {\n      const childrenPromises = (_a = elm[\"s-p\"]) != null ? _a : [];\n      const postUpdate = () => postUpdateComponent(hostRef);\n      if (childrenPromises.length === 0) {\n        postUpdate();\n      } else {\n        Promise.all(childrenPromises).then(postUpdate);\n        hostRef.$flags$ |= 4 /* isWaitingForChildren */;\n        childrenPromises.length = 0;\n      }\n    } else {\n      postUpdateComponent(hostRef);\n    }\n  });\n  return function updateComponent(_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}();\nvar renderingRef = null;\nvar callRender = (hostRef, instance, elm, isInitialLoad) => {\n  const allRenderFn = BUILD17.allRenderFn ? true : false;\n  const lazyLoad = BUILD17.lazyLoad ? true : false;\n  const taskQueue = BUILD17.taskQueue ? true : false;\n  const updatable = BUILD17.updatable ? true : false;\n  try {\n    renderingRef = instance;\n    instance = allRenderFn ? instance.render() : instance.render && instance.render();\n    if (updatable && taskQueue) {\n      hostRef.$flags$ &= ~16 /* isQueuedForUpdate */;\n    }\n    if (updatable || lazyLoad) {\n      hostRef.$flags$ |= 2 /* hasRendered */;\n    }\n    if (BUILD17.hasRenderFn || BUILD17.reflect) {\n      if (BUILD17.vdomRender || BUILD17.reflect) {\n        if (BUILD17.hydrateServerSide) {\n          return Promise.resolve(instance).then(value => renderVdom(hostRef, value, isInitialLoad));\n        } else {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      } else {\n        const shadowRoot = elm.shadowRoot;\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          shadowRoot.textContent = instance;\n        } else {\n          elm.textContent = instance;\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  renderingRef = null;\n  return null;\n};\nvar getRenderingRef = () => renderingRef;\nvar postUpdateComponent = hostRef => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime(\"postUpdate\", tagName);\n  const instance = BUILD17.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  if (BUILD17.cmpDidRender) {\n    if (BUILD17.isDev) {\n      hostRef.$flags$ |= 1024 /* devOnRender */;\n    }\n    safeCall(instance, \"componentDidRender\");\n    if (BUILD17.isDev) {\n      hostRef.$flags$ &= ~1024 /* devOnRender */;\n    }\n  }\n  emitLifecycleEvent(elm, \"componentDidRender\");\n  if (!(hostRef.$flags$ & 64 /* hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* hasLoadedComponent */;\n    if (BUILD17.asyncLoading && BUILD17.cssAnnotations) {\n      addHydratedFlag(elm);\n    }\n    if (BUILD17.cmpDidLoad) {\n      if (BUILD17.isDev) {\n        hostRef.$flags$ |= 2048 /* devOnDidLoad */;\n      }\n      safeCall(instance, \"componentDidLoad\");\n      if (BUILD17.isDev) {\n        hostRef.$flags$ &= ~2048 /* devOnDidLoad */;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentDidLoad\");\n    endPostUpdate();\n    if (BUILD17.asyncLoading) {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad(tagName);\n      }\n    }\n  } else {\n    if (BUILD17.cmpDidUpdate) {\n      if (BUILD17.isDev) {\n        hostRef.$flags$ |= 1024 /* devOnRender */;\n      }\n      safeCall(instance, \"componentDidUpdate\");\n      if (BUILD17.isDev) {\n        hostRef.$flags$ &= ~1024 /* devOnRender */;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentDidUpdate\");\n    endPostUpdate();\n  }\n  if (BUILD17.method && BUILD17.lazyLoad) {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  if (BUILD17.asyncLoading) {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = void 0;\n    }\n    if (hostRef.$flags$ & 512 /* needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* isWaitingForChildren */ | 512 /* needsRerender */);\n  }\n};\nvar forceUpdate = ref => {\n  if (BUILD17.updatable && (Build.isBrowser || Build.isTesting)) {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    return isConnected;\n  }\n  return false;\n};\nvar appDidLoad = who => {\n  if (BUILD17.cssAnnotations) {\n    addHydratedFlag(doc.documentElement);\n  }\n  if (BUILD17.asyncQueue) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  nextTick(() => emitEvent(win, \"appload\", {\n    detail: {\n      namespace: NAMESPACE\n    }\n  }));\n  if (BUILD17.profile && performance.measure) {\n    performance.measure(`[Stencil] ${NAMESPACE} initial load (by ${who})`, \"st:app:start\");\n  }\n};\nvar safeCall = (instance, method, arg) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  return void 0;\n};\nvar emitLifecycleEvent = (elm, lifecycleName) => {\n  if (BUILD17.lifecycleDOMEvents) {\n    emitEvent(elm, \"stencil_\" + lifecycleName, {\n      bubbles: true,\n      composed: true,\n      detail: {\n        namespace: NAMESPACE\n      }\n    });\n  }\n};\nvar addHydratedFlag = elm => {\n  var _a, _b;\n  return BUILD17.hydratedClass ? elm.classList.add((_a = BUILD17.hydratedSelectorName) != null ? _a : \"hydrated\") : BUILD17.hydratedAttribute ? elm.setAttribute((_b = BUILD17.hydratedSelectorName) != null ? _b : \"hydrated\", \"\") : void 0;\n};\nvar serverSideConnected = elm => {\n  const children = elm.children;\n  if (children != null) {\n    for (let i2 = 0, ii = children.length; i2 < ii; i2++) {\n      const childElm = children[i2];\n      if (typeof childElm.connectedCallback === \"function\") {\n        childElm.connectedCallback();\n      }\n      serverSideConnected(childElm);\n    }\n  }\n};\n\n// src/runtime/set-value.ts\nvar getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nvar setValue = (ref, propName, newVal, cmpMeta) => {\n  const hostRef = getHostRef(ref);\n  if (BUILD18.lazyLoad && !hostRef) {\n    throw new Error(`Couldn't find host element for \"${cmpMeta.$tagName$}\" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/ionic-team/stencil/issues/5457).`);\n  }\n  const elm = BUILD18.lazyLoad ? hostRef.$hostElement$ : ref;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = BUILD18.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!BUILD18.lazyLoad || !(flags & 8 /* isConstructingInstance */) || oldVal === void 0) && didValueChange) {\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (BUILD18.isDev) {\n      if (hostRef.$flags$ & 1024 /* devOnRender */) {\n        consoleDevWarn(`The state/prop \"${propName}\" changed during rendering. This can potentially lead to infinite-loops and other bugs.`, \"\\nElement\", elm, \"\\nNew value\", newVal, \"\\nOld value\", oldVal);\n      } else if (hostRef.$flags$ & 2048 /* devOnDidLoad */) {\n        consoleDevWarn(`The state/prop \"${propName}\" changed during \"componentDidLoad()\", this triggers extra re-renders, try to setup on \"componentWillLoad()\"`, \"\\nElement\", elm, \"\\nNew value\", newVal, \"\\nOld value\", oldVal);\n      }\n    }\n    if (!BUILD18.lazyLoad || instance) {\n      if (BUILD18.watchCallback && cmpMeta.$watchers$ && flags & 128 /* isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          watchMethods.map(watchMethodName => {\n            try {\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if (BUILD18.updatable && (flags & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n        if (BUILD18.cmpShouldUpdate && instance.componentShouldUpdate) {\n          if (instance.componentShouldUpdate(newVal, oldVal, propName) === false) {\n            return;\n          }\n        }\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n\n// src/runtime/proxy-component.ts\nvar proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a, _b;\n  const prototype = Cstr.prototype;\n  if (BUILD19.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */ && flags & 1 /* isElementConstructor */) {\n    FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS.forEach(cbName => Object.defineProperty(prototype, cbName, {\n      value(...args) {\n        const hostRef = getHostRef(this);\n        const elm = BUILD19.lazyLoad ? hostRef.$hostElement$ : this;\n        const instance = BUILD19.lazyLoad ? hostRef.$lazyInstance$ : elm;\n        if (!instance) {\n          hostRef.$onReadyPromise$.then(instance2 => {\n            const cb = instance2[cbName];\n            typeof cb === \"function\" && cb.call(instance2, ...args);\n          });\n        } else {\n          const cb = instance[cbName];\n          typeof cb === \"function\" && cb.call(instance, ...args);\n        }\n      }\n    }));\n  }\n  if (BUILD19.member && cmpMeta.$members$ || BUILD19.watchCallback && (cmpMeta.$watchers$ || Cstr.watchers)) {\n    if (BUILD19.watchCallback && Cstr.watchers && !cmpMeta.$watchers$) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n    members.map(([memberName, [memberFlags]]) => {\n      if ((BUILD19.prop || BUILD19.state) && (memberFlags & 31 /* Prop */ || (!BUILD19.lazyLoad || flags & 2 /* proxyState */) && memberFlags & 32 /* State */)) {\n        Object.defineProperty(prototype, memberName, {\n          get() {\n            return getValue(this, memberName);\n          },\n          set(newValue) {\n            if (BUILD19.isDev) {\n              const ref = getHostRef(this);\n              if (\n              // we are proxying the instance (not element)\n              (flags & 1 /* isElementConstructor */) === 0 &&\n              // the element is not constructing\n              (ref && ref.$flags$ & 8 /* isConstructingInstance */) === 0 &&\n              // the member is a prop\n              (memberFlags & 31 /* Prop */) !== 0 &&\n              // the member is not mutable\n              (memberFlags & 1024 /* Mutable */) === 0) {\n                consoleDevWarn(`@Prop() \"${memberName}\" on <${cmpMeta.$tagName$}> is immutable but was modified from within the component.\nMore information: https://stenciljs.com/docs/properties#prop-mutability`);\n              }\n            }\n            setValue(this, memberName, newValue, cmpMeta);\n          },\n          configurable: true,\n          enumerable: true\n        });\n      } else if (BUILD19.lazyLoad && BUILD19.method && flags & 1 /* isElementConstructor */ && memberFlags & 64 /* Method */) {\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a2;\n            const ref = getHostRef(this);\n            return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {\n              var _a3;\n              return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if (BUILD19.observeAttribute && (!BUILD19.lazyLoad || flags & 1 /* isElementConstructor */)) {\n      const attrNameToPropName = /* @__PURE__ */new Map();\n      prototype.attributeChangedCallback = function (attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a2;\n          const propName = attrNameToPropName.get(attrName);\n          if (this.hasOwnProperty(propName)) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === \"number\" &&\n          // cast type to number to avoid TS compiler issues\n          this[propName] == newValue) {\n            return;\n          } else if (propName == null) {\n            const hostRef = getHostRef(this);\n            const flags2 = hostRef == null ? void 0 : hostRef.$flags$;\n            if (flags2 && !(flags2 & 8 /* isConstructingInstance */) && flags2 & 128 /* isWatchReady */ && newValue !== oldValue) {\n              const elm = BUILD19.lazyLoad ? hostRef.$hostElement$ : this;\n              const instance = BUILD19.lazyLoad ? hostRef.$lazyInstance$ : elm;\n              const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];\n              entry == null ? void 0 : entry.forEach(callbackName => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          this[propName] = newValue === null && typeof this[propName] === \"boolean\" ? false : newValue;\n        });\n      };\n      Cstr.observedAttributes = Array.from(/* @__PURE__ */new Set([...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}), ...members.filter(([_, m]) => m[0] & 15 /* HasAttribute */).map(([propName, m]) => {\n        var _a2;\n        const attrName = m[1] || propName;\n        attrNameToPropName.set(attrName, propName);\n        if (BUILD19.reflect && m[0] & 512 /* ReflectAttr */) {\n          (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);\n        }\n        return attrName;\n      })]));\n    }\n  }\n  return Cstr;\n};\n\n// src/runtime/initialize-component.ts\nvar initializeComponent = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (elm, hostRef, cmpMeta, hmrVersionId) {\n    let Cstr;\n    if ((hostRef.$flags$ & 32 /* hasInitializedComponent */) === 0) {\n      hostRef.$flags$ |= 32 /* hasInitializedComponent */;\n      const bundleId = cmpMeta.$lazyBundleId$;\n      if ((BUILD20.lazyLoad || BUILD20.hydrateClientSide) && bundleId) {\n        const CstrImport = loadModule(cmpMeta, hostRef, hmrVersionId);\n        if (CstrImport && \"then\" in CstrImport) {\n          const endLoad = uniqueTime(`st:load:${cmpMeta.$tagName$}:${hostRef.$modeName$}`, `[Stencil] Load module for <${cmpMeta.$tagName$}>`);\n          Cstr = yield CstrImport;\n          endLoad();\n        } else {\n          Cstr = CstrImport;\n        }\n        if (!Cstr) {\n          throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n        }\n        if (BUILD20.member && !Cstr.isProxied) {\n          if (BUILD20.watchCallback) {\n            cmpMeta.$watchers$ = Cstr.watchers;\n          }\n          proxyComponent(Cstr, cmpMeta, 2 /* proxyState */);\n          Cstr.isProxied = true;\n        }\n        const endNewInstance = createTime(\"createInstance\", cmpMeta.$tagName$);\n        if (BUILD20.member) {\n          hostRef.$flags$ |= 8 /* isConstructingInstance */;\n        }\n        try {\n          new Cstr(hostRef);\n        } catch (e) {\n          consoleError(e);\n        }\n        if (BUILD20.member) {\n          hostRef.$flags$ &= ~8 /* isConstructingInstance */;\n        }\n        if (BUILD20.watchCallback) {\n          hostRef.$flags$ |= 128 /* isWatchReady */;\n        }\n        endNewInstance();\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else {\n        Cstr = elm.constructor;\n        const cmpTag = elm.localName;\n        customElements.whenDefined(cmpTag).then(() => hostRef.$flags$ |= 128 /* isWatchReady */);\n      }\n      if (BUILD20.style && Cstr && Cstr.style) {\n        let style;\n        if (typeof Cstr.style === \"string\") {\n          style = Cstr.style;\n        } else if (BUILD20.mode && typeof Cstr.style !== \"string\") {\n          hostRef.$modeName$ = computeMode(elm);\n          if (hostRef.$modeName$) {\n            style = Cstr.style[hostRef.$modeName$];\n          }\n          if (BUILD20.hydrateServerSide && hostRef.$modeName$) {\n            elm.setAttribute(\"s-mode\", hostRef.$modeName$);\n          }\n        }\n        const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);\n        if (!styles.has(scopeId2)) {\n          const endRegisterStyles = createTime(\"registerStyles\", cmpMeta.$tagName$);\n          if (!BUILD20.hydrateServerSide && BUILD20.shadowDom &&\n          // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n          BUILD20.shadowDomShim && cmpMeta.$flags$ & 8 /* needsShadowDomShim */) {\n            style = yield import(\"./shadow-css.js\").then(m => m.scopeCss(style, scopeId2));\n          }\n          registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */));\n          endRegisterStyles();\n        }\n      }\n    }\n    const ancestorComponent = hostRef.$ancestorComponent$;\n    const schedule = () => scheduleUpdate(hostRef, true);\n    if (BUILD20.asyncLoading && ancestorComponent && ancestorComponent[\"s-rc\"]) {\n      ancestorComponent[\"s-rc\"].push(schedule);\n    } else {\n      schedule();\n    }\n  });\n  return function initializeComponent(_x4, _x5, _x6, _x7) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nvar fireConnectedCallback = instance => {\n  if (BUILD20.lazyLoad && BUILD20.connectedCallback) {\n    safeCall(instance, \"connectedCallback\");\n  }\n};\n\n// src/runtime/connected-callback.ts\nvar connectedCallback = elm => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime(\"connectedCallback\", cmpMeta.$tagName$);\n    if (BUILD21.hostListenerTargetParent) {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, true);\n    }\n    if (!(hostRef.$flags$ & 1 /* hasConnected */)) {\n      hostRef.$flags$ |= 1 /* hasConnected */;\n      let hostId;\n      if (BUILD21.hydrateClientSide) {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (BUILD21.shadowDom && supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            const scopeId2 = BUILD21.mode ? addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute(\"s-mode\")) : addStyle(elm.shadowRoot, cmpMeta);\n            elm.classList.remove(scopeId2 + \"-h\", scopeId2 + \"-s\");\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (BUILD21.slotRelocation && !hostId) {\n        if (BUILD21.hydrateServerSide || (BUILD21.slot || BUILD21.shadowDom) &&\n        // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* hasSlotRelocation */ | 8 /* needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      if (BUILD21.asyncLoading) {\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          if (BUILD21.hydrateClientSide && ancestorComponent.nodeType === 1 /* ElementNode */ && ancestorComponent.hasAttribute(\"s-id\") && ancestorComponent[\"s-p\"] || ancestorComponent[\"s-p\"]) {\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      if (BUILD21.prop && !BUILD21.hydrateServerSide && cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      if (BUILD21.initializeNextTick) {\n        nextTick(() => initializeComponent(elm, hostRef, cmpMeta));\n      } else {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, false);\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));\n      }\n    }\n    endConnected();\n  }\n};\nvar setContentReference = elm => {\n  const contentRefElm = elm[\"s-cr\"] = doc.createComment(BUILD21.isDebug ? `content-ref (host=${elm.localName})` : \"\");\n  contentRefElm[\"s-cn\"] = true;\n  insertBefore(elm, contentRefElm, elm.firstChild);\n};\n\n// src/runtime/disconnected-callback.ts\nimport { BUILD as BUILD22 } from \"@stencil/core/internal/app-data\";\nvar disconnectInstance = instance => {\n  if (BUILD22.lazyLoad && BUILD22.disconnectedCallback) {\n    safeCall(instance, \"disconnectedCallback\");\n  }\n  if (BUILD22.cmpDidUnload) {\n    safeCall(instance, \"componentDidUnload\");\n  }\n};\nvar disconnectedCallback = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* (elm) {\n    if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n      const hostRef = getHostRef(elm);\n      if (BUILD22.hostListener) {\n        if (hostRef.$rmListeners$) {\n          hostRef.$rmListeners$.map(rmListener => rmListener());\n          hostRef.$rmListeners$ = void 0;\n        }\n      }\n      if (!BUILD22.lazyLoad) {\n        disconnectInstance(elm);\n      } else if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        disconnectInstance(hostRef.$lazyInstance$);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));\n      }\n    }\n  });\n  return function disconnectedCallback(_x8) {\n    return _ref3.apply(this, arguments);\n  };\n}();\n\n// src/runtime/dom-extras.ts\nimport { BUILD as BUILD23 } from \"@stencil/core/internal/app-data\";\nvar patchPseudoShadowDom = (hostElementPrototype, descriptorPrototype) => {\n  patchCloneNode(hostElementPrototype);\n  patchSlotAppendChild(hostElementPrototype);\n  patchSlotAppend(hostElementPrototype);\n  patchSlotPrepend(hostElementPrototype);\n  patchSlotInsertAdjacentElement(hostElementPrototype);\n  patchSlotInsertAdjacentHTML(hostElementPrototype);\n  patchSlotInsertAdjacentText(hostElementPrototype);\n  patchTextContent(hostElementPrototype);\n  patchChildSlotNodes(hostElementPrototype, descriptorPrototype);\n  patchSlotRemoveChild(hostElementPrototype);\n};\nvar patchCloneNode = HostElementPrototype => {\n  const orgCloneNode = HostElementPrototype.cloneNode;\n  HostElementPrototype.cloneNode = function (deep) {\n    const srcNode = this;\n    const isShadowDom = BUILD23.shadowDom ? srcNode.shadowRoot && supportsShadow : false;\n    const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n    if (BUILD23.slot && !isShadowDom && deep) {\n      let i2 = 0;\n      let slotted, nonStencilNode;\n      const stencilPrivates = [\"s-id\", \"s-cr\", \"s-lr\", \"s-rc\", \"s-sc\", \"s-p\", \"s-cn\", \"s-sr\", \"s-sn\", \"s-hn\", \"s-ol\", \"s-nr\", \"s-si\", \"s-rf\", \"s-scs\"];\n      for (; i2 < srcNode.childNodes.length; i2++) {\n        slotted = srcNode.childNodes[i2][\"s-nr\"];\n        nonStencilNode = stencilPrivates.every(privateField => !srcNode.childNodes[i2][privateField]);\n        if (slotted) {\n          if (BUILD23.appendChildSlotFix && clonedNode.__appendChild) {\n            clonedNode.__appendChild(slotted.cloneNode(true));\n          } else {\n            clonedNode.appendChild(slotted.cloneNode(true));\n          }\n        }\n        if (nonStencilNode) {\n          clonedNode.appendChild(srcNode.childNodes[i2].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nvar patchSlotAppendChild = HostElementPrototype => {\n  HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n  HostElementPrototype.appendChild = function (newChild) {\n    const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n    const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n    if (slotNode) {\n      const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n      const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n      const insertedNode = insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      updateFallbackSlotVisibility(this);\n      return insertedNode;\n    }\n    return this.__appendChild(newChild);\n  };\n};\nvar patchSlotRemoveChild = ElementPrototype => {\n  ElementPrototype.__removeChild = ElementPrototype.removeChild;\n  ElementPrototype.removeChild = function (toRemove) {\n    if (toRemove && typeof toRemove[\"s-sn\"] !== \"undefined\") {\n      const slotNode = getHostSlotNode(this.childNodes, toRemove[\"s-sn\"], this.tagName);\n      if (slotNode) {\n        const slotChildNodes = getHostSlotChildNodes(slotNode, toRemove[\"s-sn\"]);\n        const existingNode = slotChildNodes.find(n => n === toRemove);\n        if (existingNode) {\n          existingNode.remove();\n          updateFallbackSlotVisibility(this);\n          return;\n        }\n      }\n    }\n    return this.__removeChild(toRemove);\n  };\n};\nvar patchSlotPrepend = HostElementPrototype => {\n  const originalPrepend = HostElementPrototype.prepend;\n  HostElementPrototype.prepend = function (...newChildren) {\n    newChildren.forEach(newChild => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n      const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n      if (slotNode) {\n        const slotPlaceholder = document.createTextNode(\"\");\n        slotPlaceholder[\"s-nr\"] = newChild;\n        slotNode[\"s-cr\"].parentNode.__appendChild(slotPlaceholder);\n        newChild[\"s-ol\"] = slotPlaceholder;\n        const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n        const appendAfter = slotChildNodes[0];\n        return insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      }\n      if (newChild.nodeType === 1 && !!newChild.getAttribute(\"slot\")) {\n        newChild.hidden = true;\n      }\n      return originalPrepend.call(this, newChild);\n    });\n  };\n};\nvar patchSlotAppend = HostElementPrototype => {\n  HostElementPrototype.append = function (...newChildren) {\n    newChildren.forEach(newChild => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      this.appendChild(newChild);\n    });\n  };\n};\nvar patchSlotInsertAdjacentHTML = HostElementPrototype => {\n  const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n  HostElementPrototype.insertAdjacentHTML = function (position, text) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentHtml.call(this, position, text);\n    }\n    const container = this.ownerDocument.createElement(\"_\");\n    let node;\n    container.innerHTML = text;\n    if (position === \"afterbegin\") {\n      while (node = container.firstChild) {\n        this.prepend(node);\n      }\n    } else if (position === \"beforeend\") {\n      while (node = container.firstChild) {\n        this.append(node);\n      }\n    }\n  };\n};\nvar patchSlotInsertAdjacentText = HostElementPrototype => {\n  HostElementPrototype.insertAdjacentText = function (position, text) {\n    this.insertAdjacentHTML(position, text);\n  };\n};\nvar patchSlotInsertAdjacentElement = HostElementPrototype => {\n  const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n  HostElementPrototype.insertAdjacentElement = function (position, element) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentElement.call(this, position, element);\n    }\n    if (position === \"afterbegin\") {\n      this.prepend(element);\n      return element;\n    } else if (position === \"beforeend\") {\n      this.append(element);\n      return element;\n    }\n    return element;\n  };\n};\nvar patchTextContent = hostElementPrototype => {\n  const descriptor = Object.getOwnPropertyDescriptor(Node.prototype, \"textContent\");\n  Object.defineProperty(hostElementPrototype, \"__textContent\", descriptor);\n  if (BUILD23.experimentalScopedSlotChanges) {\n    Object.defineProperty(hostElementPrototype, \"textContent\", {\n      // To mimic shadow root behavior, we need to return the text content of all\n      // nodes in a slot reference node\n      get() {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        const textContent = slotRefNodes.map(node => {\n          var _a, _b;\n          const text = [];\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            if (slotContent.nodeType === 3 /* TEXT_NODE */ || slotContent.nodeType === 1 /* ELEMENT_NODE */) {\n              text.push((_b = (_a = slotContent.textContent) == null ? void 0 : _a.trim()) != null ? _b : \"\");\n            }\n            slotContent = slotContent.nextSibling;\n          }\n          return text.filter(ref => ref !== \"\").join(\" \");\n        }).filter(text => text !== \"\").join(\" \");\n        return \" \" + textContent + \" \";\n      },\n      // To mimic shadow root behavior, we need to overwrite all nodes in a slot\n      // reference node. If a default slot reference node exists, the text content will be\n      // placed there. Otherwise, the new text node will be hidden\n      set(value) {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        slotRefNodes.forEach(node => {\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            const tmp = slotContent;\n            slotContent = slotContent.nextSibling;\n            tmp.remove();\n          }\n          if (node[\"s-sn\"] === \"\") {\n            const textNode = this.ownerDocument.createTextNode(value);\n            textNode[\"s-sn\"] = \"\";\n            insertBefore(node.parentElement, textNode, node.nextSibling);\n          } else {\n            node.remove();\n          }\n        });\n      }\n    });\n  } else {\n    Object.defineProperty(hostElementPrototype, \"textContent\", {\n      get() {\n        var _a;\n        const slotNode = getHostSlotNode(this.childNodes, \"\", this.tagName);\n        if (((_a = slotNode == null ? void 0 : slotNode.nextSibling) == null ? void 0 : _a.nodeType) === 3 /* TEXT_NODE */) {\n          return slotNode.nextSibling.textContent;\n        } else if (slotNode) {\n          return slotNode.textContent;\n        } else {\n          return this.__textContent;\n        }\n      },\n      set(value) {\n        var _a;\n        const slotNode = getHostSlotNode(this.childNodes, \"\", this.tagName);\n        if (((_a = slotNode == null ? void 0 : slotNode.nextSibling) == null ? void 0 : _a.nodeType) === 3 /* TEXT_NODE */) {\n          slotNode.nextSibling.textContent = value;\n        } else if (slotNode) {\n          slotNode.textContent = value;\n        } else {\n          this.__textContent = value;\n          const contentRefElm = this[\"s-cr\"];\n          if (contentRefElm) {\n            insertBefore(this, contentRefElm, this.firstChild);\n          }\n        }\n      }\n    });\n  }\n};\nvar patchChildSlotNodes = (elm, cmpMeta) => {\n  class FakeNodeList extends Array {\n    item(n) {\n      return this[n];\n    }\n  }\n  if (cmpMeta.$flags$ & 8 /* needsShadowDomShim */) {\n    const childNodesFn = elm.__lookupGetter__(\"childNodes\");\n    Object.defineProperty(elm, \"children\", {\n      get() {\n        return this.childNodes.map(n => n.nodeType === 1);\n      }\n    });\n    Object.defineProperty(elm, \"childElementCount\", {\n      get() {\n        return elm.children.length;\n      }\n    });\n    Object.defineProperty(elm, \"childNodes\", {\n      get() {\n        const childNodes = childNodesFn.call(this);\n        if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0 && getHostRef(this).$flags$ & 2 /* hasRendered */) {\n          const result = new FakeNodeList();\n          for (let i2 = 0; i2 < childNodes.length; i2++) {\n            const slot = childNodes[i2][\"s-nr\"];\n            if (slot) {\n              result.push(slot);\n            }\n          }\n          return result;\n        }\n        return FakeNodeList.from(childNodes);\n      }\n    });\n  }\n};\nvar getAllChildSlotNodes = childNodes => {\n  const slotRefNodes = [];\n  for (const childNode of Array.from(childNodes)) {\n    if (childNode[\"s-sr\"]) {\n      slotRefNodes.push(childNode);\n    }\n    slotRefNodes.push(...getAllChildSlotNodes(childNode.childNodes));\n  }\n  return slotRefNodes;\n};\nvar getSlotName = node => node[\"s-sn\"] || node.nodeType === 1 && node.getAttribute(\"slot\") || \"\";\nvar getHostSlotNode = (childNodes, slotName, hostName) => {\n  let i2 = 0;\n  let childNode;\n  for (; i2 < childNodes.length; i2++) {\n    childNode = childNodes[i2];\n    if (childNode[\"s-sr\"] && childNode[\"s-sn\"] === slotName && childNode[\"s-hn\"] === hostName) {\n      return childNode;\n    }\n    childNode = getHostSlotNode(childNode.childNodes, slotName, hostName);\n    if (childNode) {\n      return childNode;\n    }\n  }\n  return null;\n};\nvar getHostSlotChildNodes = (n, slotName) => {\n  const childNodes = [n];\n  while ((n = n.nextSibling) && n[\"s-sn\"] === slotName) {\n    childNodes.push(n);\n  }\n  return childNodes;\n};\n\n// src/runtime/bootstrap-custom-element.ts\nvar defineCustomElement = (Cstr, compactMeta) => {\n  customElements.define(compactMeta[1], proxyCustomElement(Cstr, compactMeta));\n};\nvar proxyCustomElement = (Cstr, compactMeta) => {\n  const cmpMeta = {\n    $flags$: compactMeta[0],\n    $tagName$: compactMeta[1]\n  };\n  if (BUILD24.member) {\n    cmpMeta.$members$ = compactMeta[2];\n  }\n  if (BUILD24.hostListener) {\n    cmpMeta.$listeners$ = compactMeta[3];\n  }\n  if (BUILD24.watchCallback) {\n    cmpMeta.$watchers$ = Cstr.$watchers$;\n  }\n  if (BUILD24.reflect) {\n    cmpMeta.$attrsToReflect$ = [];\n  }\n  if (BUILD24.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n    cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n  }\n  if (BUILD24.experimentalSlotFixes) {\n    if (BUILD24.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchPseudoShadowDom(Cstr.prototype, cmpMeta);\n    }\n  } else {\n    if (BUILD24.slotChildNodesFix) {\n      patchChildSlotNodes(Cstr.prototype, cmpMeta);\n    }\n    if (BUILD24.cloneNodeFix) {\n      patchCloneNode(Cstr.prototype);\n    }\n    if (BUILD24.appendChildSlotFix) {\n      patchSlotAppendChild(Cstr.prototype);\n    }\n    if (BUILD24.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchTextContent(Cstr.prototype);\n    }\n  }\n  const originalConnectedCallback = Cstr.prototype.connectedCallback;\n  const originalDisconnectedCallback = Cstr.prototype.disconnectedCallback;\n  Object.assign(Cstr.prototype, {\n    __registerHost() {\n      registerHost(this, cmpMeta);\n    },\n    connectedCallback() {\n      const hostRef = getHostRef(this);\n      addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n      connectedCallback(this);\n      if (BUILD24.connectedCallback && originalConnectedCallback) {\n        originalConnectedCallback.call(this);\n      }\n    },\n    disconnectedCallback() {\n      disconnectedCallback(this);\n      if (BUILD24.disconnectedCallback && originalDisconnectedCallback) {\n        originalDisconnectedCallback.call(this);\n      }\n    },\n    __attachShadow() {\n      if (supportsShadow) {\n        if (!this.shadowRoot) {\n          if (BUILD24.shadowDelegatesFocus) {\n            this.attachShadow({\n              mode: \"open\",\n              delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n            });\n          } else {\n            this.attachShadow({\n              mode: \"open\"\n            });\n          }\n        } else {\n          if (this.shadowRoot.mode !== \"open\") {\n            throw new Error(`Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${this.shadowRoot.mode} but Stencil only supports open shadow roots.`);\n          }\n        }\n      } else {\n        this.shadowRoot = this;\n      }\n    }\n  });\n  Cstr.is = cmpMeta.$tagName$;\n  return proxyComponent(Cstr, cmpMeta, 1 /* isElementConstructor */ | 2 /* proxyState */);\n};\nvar forceModeUpdate = elm => {\n  if (BUILD24.style && BUILD24.mode && !BUILD24.lazyLoad) {\n    const mode = computeMode(elm);\n    const hostRef = getHostRef(elm);\n    if (hostRef.$modeName$ !== mode) {\n      const cmpMeta = hostRef.$cmpMeta$;\n      const oldScopeId = elm[\"s-sc\"];\n      const scopeId2 = getScopeId(cmpMeta, mode);\n      const style = elm.constructor.style[mode];\n      const flags = cmpMeta.$flags$;\n      if (style) {\n        if (!styles.has(scopeId2)) {\n          registerStyle(scopeId2, style, !!(flags & 1 /* shadowDomEncapsulation */));\n        }\n        hostRef.$modeName$ = mode;\n        elm.classList.remove(oldScopeId + \"-h\", oldScopeId + \"-s\");\n        attachStyles(hostRef);\n        forceUpdate(elm);\n      }\n    }\n  }\n};\n\n// src/runtime/bootstrap-lazy.ts\nimport { BUILD as BUILD25 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/hmr-component.ts\nvar hmrStart = (hostElement, cmpMeta, hmrVersionId) => {\n  const hostRef = getHostRef(hostElement);\n  hostRef.$flags$ = 1 /* hasConnected */;\n  initializeComponent(hostElement, hostRef, cmpMeta, hmrVersionId);\n};\n\n// src/runtime/bootstrap-lazy.ts\nvar bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  if (BUILD25.profile && performance.mark) {\n    performance.mark(\"st:app:start\");\n  }\n  installDevTools();\n  const endBootstrap = createTime(\"bootstrapLazy\");\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements2 = win.customElements;\n  const head = doc.head;\n  const metaCharset = /* @__PURE__ */head.querySelector(\"meta[charset]\");\n  const dataStyles = /* @__PURE__ */doc.createElement(\"style\");\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || \"./\", doc.baseURI).href;\n  if (BUILD25.asyncQueue) {\n    if (options.syncQueue) {\n      plt.$flags$ |= 4 /* queueSync */;\n    }\n  }\n  if (BUILD25.hydrateClientSide) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map(lazyBundle => {\n    lazyBundle[1].map(compactMeta => {\n      var _a2;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      if (BUILD25.member) {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      if (BUILD25.hostListener) {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      if (BUILD25.reflect) {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      if (BUILD25.watchCallback) {\n        cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};\n      }\n      if (BUILD25.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n        cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n      }\n      const tagName = BUILD25.transformTagName && options.transformTagName ? options.transformTagName(cmpMeta.$tagName$) : cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          super(self);\n          this.hasRegisteredEventListeners = false;\n          self = this;\n          registerHost(self, cmpMeta);\n          if (BUILD25.shadowDom && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            if (supportsShadow) {\n              if (!self.shadowRoot) {\n                if (BUILD25.shadowDelegatesFocus) {\n                  self.attachShadow({\n                    mode: \"open\",\n                    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n                  });\n                } else {\n                  self.attachShadow({\n                    mode: \"open\"\n                  });\n                }\n              } else {\n                if (self.shadowRoot.mode !== \"open\") {\n                  throw new Error(`Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`);\n                }\n              }\n            } else if (!BUILD25.hydrateServerSide && !(\"shadowRoot\" in self)) {\n              self.shadowRoot = self;\n            }\n          }\n        }\n        connectedCallback() {\n          const hostRef = getHostRef(this);\n          if (!this.hasRegisteredEventListeners) {\n            this.hasRegisteredEventListeners = true;\n            addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n          }\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      if (BUILD25.experimentalSlotFixes) {\n        if (BUILD25.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchPseudoShadowDom(HostElement.prototype, cmpMeta);\n        }\n      } else {\n        if (BUILD25.slotChildNodesFix) {\n          patchChildSlotNodes(HostElement.prototype, cmpMeta);\n        }\n        if (BUILD25.cloneNodeFix) {\n          patchCloneNode(HostElement.prototype);\n        }\n        if (BUILD25.appendChildSlotFix) {\n          patchSlotAppendChild(HostElement.prototype);\n        }\n        if (BUILD25.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchTextContent(HostElement.prototype);\n        }\n      }\n      if (BUILD25.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */) {\n        HostElement.formAssociated = true;\n      }\n      if (BUILD25.hotModuleReplacement) {\n        HostElement.prototype[\"s-hmr\"] = function (hmrVersionId) {\n          hmrStart(this, cmpMeta, hmrVersionId);\n        };\n      }\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements2.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements2.define(tagName, proxyComponent(HostElement, cmpMeta, 1 /* isElementConstructor */));\n      }\n    });\n  });\n  if (cmpTags.length > 0) {\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    if (BUILD25.invisiblePrehydration && (BUILD25.hydratedClass || BUILD25.hydratedAttribute)) {\n      dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;\n    }\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute(\"data-styles\", \"\");\n      const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n      if (nonce != null) {\n        dataStyles.setAttribute(\"nonce\", nonce);\n      }\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map(host => host.connectedCallback());\n  } else {\n    if (BUILD25.profile) {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30, \"timeout\"));\n    } else {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  endBootstrap();\n};\n\n// src/runtime/fragment.ts\nvar Fragment = (_, children) => children;\n\n// src/runtime/host-listener.ts\nimport { BUILD as BUILD26 } from \"@stencil/core/internal/app-data\";\nvar addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (BUILD26.hostListener && listeners) {\n    if (BUILD26.hostListenerTargetParent) {\n      if (attachParentListeners) {\n        listeners = listeners.filter(([flags]) => flags & 32 /* TargetParent */);\n      } else {\n        listeners = listeners.filter(([flags]) => !(flags & 32 /* TargetParent */));\n      }\n    }\n    listeners.map(([flags, name, method]) => {\n      const target = BUILD26.hostListenerTarget ? getHostListenerTarget(elm, flags) : elm;\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nvar hostListenerProxy = (hostRef, methodName) => ev => {\n  var _a;\n  try {\n    if (BUILD26.lazyLoad) {\n      if (hostRef.$flags$ & 256 /* isListenReady */) {\n        (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    } else {\n      hostRef.$hostElement$[methodName](ev);\n    }\n  } catch (e) {\n    consoleError(e);\n  }\n};\nvar getHostListenerTarget = (elm, flags) => {\n  if (BUILD26.hostListenerTargetDocument && flags & 4 /* TargetDocument */) return doc;\n  if (BUILD26.hostListenerTargetWindow && flags & 8 /* TargetWindow */) return win;\n  if (BUILD26.hostListenerTargetBody && flags & 16 /* TargetBody */) return doc.body;\n  if (BUILD26.hostListenerTargetParent && flags & 32 /* TargetParent */ && elm.parentElement) return elm.parentElement;\n  return elm;\n};\nvar hostListenerOpts = flags => supportsListenerOptions ? {\n  passive: (flags & 1 /* Passive */) !== 0,\n  capture: (flags & 2 /* Capture */) !== 0\n} : (flags & 2 /* Capture */) !== 0;\n\n// src/runtime/nonce.ts\nvar setNonce = nonce => plt.$nonce$ = nonce;\n\n// src/runtime/platform-options.ts\nvar setPlatformOptions = opts => Object.assign(plt, opts);\n\n// src/runtime/vdom/vdom-annotations.ts\nvar insertVdomAnnotations = (doc2, staticComponents) => {\n  if (doc2 != null) {\n    const docData = {\n      hostIds: 0,\n      rootLevelIds: 0,\n      staticComponents: new Set(staticComponents)\n    };\n    const orgLocationNodes = [];\n    parseVNodeAnnotations(doc2, doc2.body, docData, orgLocationNodes);\n    orgLocationNodes.forEach(orgLocationNode => {\n      var _a;\n      if (orgLocationNode != null && orgLocationNode[\"s-nr\"]) {\n        const nodeRef = orgLocationNode[\"s-nr\"];\n        let hostId = nodeRef[\"s-host-id\"];\n        let nodeId = nodeRef[\"s-node-id\"];\n        let childId = `${hostId}.${nodeId}`;\n        if (hostId == null) {\n          hostId = 0;\n          docData.rootLevelIds++;\n          nodeId = docData.rootLevelIds;\n          childId = `${hostId}.${nodeId}`;\n          if (nodeRef.nodeType === 1 /* ElementNode */) {\n            nodeRef.setAttribute(HYDRATE_CHILD_ID, childId);\n          } else if (nodeRef.nodeType === 3 /* TextNode */) {\n            if (hostId === 0) {\n              const textContent = (_a = nodeRef.nodeValue) == null ? void 0 : _a.trim();\n              if (textContent === \"\") {\n                orgLocationNode.remove();\n                return;\n              }\n            }\n            const commentBeforeTextNode = doc2.createComment(childId);\n            commentBeforeTextNode.nodeValue = `${TEXT_NODE_ID}.${childId}`;\n            insertBefore(nodeRef.parentNode, commentBeforeTextNode, nodeRef);\n          }\n        }\n        let orgLocationNodeId = `${ORG_LOCATION_ID}.${childId}`;\n        const orgLocationParentNode = orgLocationNode.parentElement;\n        if (orgLocationParentNode) {\n          if (orgLocationParentNode[\"s-en\"] === \"\") {\n            orgLocationNodeId += `.`;\n          } else if (orgLocationParentNode[\"s-en\"] === \"c\") {\n            orgLocationNodeId += `.c`;\n          }\n        }\n        orgLocationNode.nodeValue = orgLocationNodeId;\n      }\n    });\n  }\n};\nvar parseVNodeAnnotations = (doc2, node, docData, orgLocationNodes) => {\n  var _a;\n  if (node == null) {\n    return;\n  }\n  if (node[\"s-nr\"] != null) {\n    orgLocationNodes.push(node);\n  }\n  if (node.nodeType === 1 /* ElementNode */) {\n    const childNodes = [...Array.from(node.childNodes), ...Array.from(((_a = node.shadowRoot) == null ? void 0 : _a.childNodes) || [])];\n    childNodes.forEach(childNode => {\n      const hostRef = getHostRef(childNode);\n      if (hostRef != null && !docData.staticComponents.has(childNode.nodeName.toLowerCase())) {\n        const cmpData = {\n          nodeIds: 0\n        };\n        insertVNodeAnnotations(doc2, childNode, hostRef.$vnode$, docData, cmpData);\n      }\n      parseVNodeAnnotations(doc2, childNode, docData, orgLocationNodes);\n    });\n  }\n};\nvar insertVNodeAnnotations = (doc2, hostElm, vnode, docData, cmpData) => {\n  if (vnode != null) {\n    const hostId = ++docData.hostIds;\n    hostElm.setAttribute(HYDRATE_ID, hostId);\n    if (hostElm[\"s-cr\"] != null) {\n      hostElm[\"s-cr\"].nodeValue = `${CONTENT_REF_ID}.${hostId}`;\n    }\n    if (vnode.$children$ != null) {\n      const depth = 0;\n      vnode.$children$.forEach((vnodeChild, index) => {\n        insertChildVNodeAnnotations(doc2, vnodeChild, cmpData, hostId, depth, index);\n      });\n    }\n    if (hostElm && vnode && vnode.$elm$ && !hostElm.hasAttribute(HYDRATE_CHILD_ID)) {\n      const parent = hostElm.parentElement;\n      if (parent && parent.childNodes) {\n        const parentChildNodes = Array.from(parent.childNodes);\n        const comment = parentChildNodes.find(node => node.nodeType === 8 /* CommentNode */ && node[\"s-sr\"]);\n        if (comment) {\n          const index = parentChildNodes.indexOf(hostElm) - 1;\n          vnode.$elm$.setAttribute(HYDRATE_CHILD_ID, `${comment[\"s-host-id\"]}.${comment[\"s-node-id\"]}.0.${index}`);\n        }\n      }\n    }\n  }\n};\nvar insertChildVNodeAnnotations = (doc2, vnodeChild, cmpData, hostId, depth, index) => {\n  const childElm = vnodeChild.$elm$;\n  if (childElm == null) {\n    return;\n  }\n  const nodeId = cmpData.nodeIds++;\n  const childId = `${hostId}.${nodeId}.${depth}.${index}`;\n  childElm[\"s-host-id\"] = hostId;\n  childElm[\"s-node-id\"] = nodeId;\n  if (childElm.nodeType === 1 /* ElementNode */) {\n    childElm.setAttribute(HYDRATE_CHILD_ID, childId);\n  } else if (childElm.nodeType === 3 /* TextNode */) {\n    const parentNode = childElm.parentNode;\n    const nodeName = parentNode == null ? void 0 : parentNode.nodeName;\n    if (nodeName !== \"STYLE\" && nodeName !== \"SCRIPT\") {\n      const textNodeId = `${TEXT_NODE_ID}.${childId}`;\n      const commentBeforeTextNode = doc2.createComment(textNodeId);\n      insertBefore(parentNode, commentBeforeTextNode, childElm);\n    }\n  } else if (childElm.nodeType === 8 /* CommentNode */) {\n    if (childElm[\"s-sr\"]) {\n      const slotName = childElm[\"s-sn\"] || \"\";\n      const slotNodeId = `${SLOT_NODE_ID}.${childId}.${slotName}`;\n      childElm.nodeValue = slotNodeId;\n    }\n  }\n  if (vnodeChild.$children$ != null) {\n    const childDepth = depth + 1;\n    vnodeChild.$children$.forEach((vnode, index2) => {\n      insertChildVNodeAnnotations(doc2, vnode, cmpData, hostId, childDepth, index2);\n    });\n  }\n};\nexport { BUILD27 as BUILD, Build, Env, Fragment, H, H as HTMLElement, Host, NAMESPACE2 as NAMESPACE, STENCIL_DEV_MODE, addHostEventListeners, bootstrapLazy, cmpModules, connectedCallback, consoleDevError, consoleDevInfo, consoleDevWarn, consoleError, createEvent, defineCustomElement, disconnectedCallback, doc, forceModeUpdate, forceUpdate, getAssetPath, getElement, getHostRef, getMode, getRenderingRef, getValue, h, insertVdomAnnotations, isMemberInElement, loadModule, modeResolutionChain, nextTick, parsePropertyValue, plt, postUpdateComponent, promiseResolve, proxyComponent, proxyCustomElement, readTask, registerHost, registerInstance, renderVdom, setAssetPath, setErrorHandler, setMode, setNonce, setPlatformHelpers, setPlatformOptions, setValue, styles, supportsConstructableStylesheets, supportsListenerOptions, supportsShadow, win, writeTask };", "map": {"version": 3, "names": ["__defProp", "Object", "defineProperty", "__export", "target", "all", "name", "get", "enumerable", "BUILD", "Build", "isDev", "<PERSON><PERSON><PERSON><PERSON>", "isServer", "isTesting", "BUILD2", "hostRefs", "hotModuleReplacement", "window", "__STENCIL_HOSTREFS__", "WeakMap", "getHostRef", "ref", "registerInstance", "lazyInstance", "hostRef", "set", "$lazyInstance$", "registerHost", "hostElement", "cmpMeta", "$flags$", "$hostElement$", "$cmpMeta$", "$instanceValues$", "Map", "$renderCount$", "method", "lazyLoad", "$onInstancePromise$", "Promise", "r", "$onInstanceResolve$", "asyncLoading", "$onReadyPromise$", "$onReadyResolve$", "isMemberInElement", "elm", "memberName", "BUILD4", "BUILD3", "customError", "consoleError", "e", "el", "console", "error", "STENCIL_DEV_MODE", "consoleDevError", "m", "consoleDevWarn", "warn", "consoleDevInfo", "info", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handler", "cmpModules", "MODULE_IMPORT_PREFIX", "loadModule", "hmrVersionId", "exportName", "$tagName$", "replace", "bundleId", "$lazyBundleId$", "$modeName$", "module", "then", "importedModule", "styles", "modeResolutionChain", "BUILD6", "CONTENT_REF_ID", "ORG_LOCATION_ID", "SLOT_NODE_ID", "TEXT_NODE_ID", "HYDRATE_ID", "HYDRATED_STYLE_ID", "HYDRATE_CHILD_ID", "HYDRATED_CSS", "SLOT_FB_CSS", "XLINK_NS", "FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS", "BUILD5", "win", "doc", "document", "head", "H", "HTMLElement", "plt", "$resourcesUrl$", "jmp", "h2", "raf", "requestAnimationFrame", "ael", "eventName", "listener", "opts", "addEventListener", "rel", "removeEventListener", "ce", "CustomEvent", "setPlatformHelpers", "helpers", "assign", "supportsShadow", "shadowDom", "supportsListenerOptions", "supportsListenerOptions2", "promiseResolve", "v", "resolve", "supportsConstructableStylesheets", "constructableCSS", "CSSStyleSheet", "replaceSync", "queueCongestion", "queuePending", "queueDomReads", "queueDomWrites", "queueDomWritesLow", "queueTask", "queue", "write", "cb", "push", "nextTick", "flush", "consume", "i2", "length", "performance", "now", "consumeTimeout", "timeout", "ts", "splice", "asyncQueue", "Math", "ceil", "Infinity", "readTask", "writeTask", "BUILD27", "Env", "NAMESPACE", "NAMESPACE2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path", "assetUrl", "URL", "origin", "location", "href", "pathname", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BUILD24", "EMPTY_OBJ", "SVG_NS", "HTML_NS", "isDef", "isComplexType", "o", "queryNonceMetaTagContent", "doc2", "_a", "_b", "_c", "querySelector", "getAttribute", "result_exports", "err", "map", "ok", "unwrap", "unwrapErr", "value", "isOk", "isErr", "result", "fn", "val", "newVal", "BUILD21", "BUILD9", "BUILD7", "i", "createTime", "fnName", "tagName", "profile", "mark", "key", "measure", "uniqueTime", "measureText", "getEntriesByName", "inspect", "flags", "renderCount", "hasRendered", "hasConnected", "isWaitingFor<PERSON><PERSON><PERSON>n", "isConstructingInstance", "isQueuedForUpdate", "hasInitializedComponent", "hasLoadedComponent", "isWatchReady", "isListenReady", "<PERSON><PERSON><PERSON><PERSON>", "instanceV<PERSON>ues", "ancestorComponent", "$ancestorComponent$", "vnode", "$vnode$", "modeName", "onReadyPromise", "onReadyResolve", "onInstancePromise", "onInstanceResolve", "onRenderResolve", "$onRenderResolve$", "queuedListeners", "$queuedListeners$", "rmListeners", "$rmListeners$", "installDevTools", "devTools", "stencil", "originalInspect", "BUILD8", "h", "nodeName", "vnodeData", "children", "child", "slotName", "simple", "lastSimple", "vNodeChildren", "walk", "c", "Array", "isArray", "String", "$text$", "newVNode", "validateInputProperties", "vdomKey", "slotRelocation", "vdomClass", "classData", "className", "class", "keys", "filter", "k", "join", "some", "isHost", "vdomFunctional", "vdomFnUtils", "$attrs$", "$children$", "$key$", "$name$", "tag", "text", "$tag$", "$elm$", "vdomAttribute", "Host", "node", "for<PERSON>ach", "convertToPublic", "convertToPrivate", "vattrs", "vchildren", "vkey", "vname", "vtag", "vtext", "inputElm", "props", "indexOf", "typeIndex", "minIndex", "maxIndex", "stepIndex", "initializeClientHydrate", "hostElm", "hostId", "endHydrate", "shadowRoot", "childRenderNodes", "slotNodes", "shadowRootNodes", "$orgLocNodes$", "initializeDocumentHydrate", "body", "removeAttribute", "clientHydrate", "orgLocationId", "$hostId$", "$nodeId$", "orgLocationNode", "parentNode", "insertBefore", "nextS<PERSON>ling", "delete", "shadowRootNode", "append<PERSON><PERSON><PERSON>", "parentVNode", "childNodeType", "childIdSplt", "childVNode", "nodeType", "split", "$depth$", "$index$", "toLowerCase", "childNodes", "nodeValue", "textContent", "remove", "createElement", "setAttribute", "orgLocNodes", "BUILD20", "computeMode", "find", "setMode", "getMode", "BUILD19", "BUILD18", "BUILD10", "parsePropertyValue", "propValue", "propType", "propBoolean", "propNumber", "parseFloat", "propString", "BUILD17", "BUILD12", "BUILD11", "getElement", "createEvent", "emit", "detail", "isConnected", "emitEvent", "bubbles", "composed", "cancelable", "ev", "dispatchEvent", "BUILD13", "rootAppliedStyles", "registerStyle", "scopeId2", "cssText", "allowCS", "style", "addStyle", "styleContainerNode", "mode", "getScopeId", "attachStyles", "appliedStyles", "styleElm", "Set", "has", "hydrateClientSide", "host", "innerHTML", "nonce", "$nonce$", "hydrateServerSide", "injectStyle", "add", "adoptedStyleSheets", "includes", "endAttachStyles", "getRootNode", "scoped", "cssAnnotations", "classList", "cmp", "BUILD16", "BUILD15", "BUILD14", "setAccessor", "oldValue", "newValue", "isSvg", "isProp", "ln", "oldClasses", "parseClassList", "newClasses", "vdomStyle", "updatable", "prop", "removeProperty", "setProperty", "vdomRef", "vdomListener", "__lookupSetter__", "slice", "capture", "endsWith", "CAPTURE_EVENT_SUFFIX", "CAPTURE_EVENT_REGEX", "vdomPropOrAttr", "isComplex", "n", "xlink", "vdomXlink", "removeAttributeNS", "setAttributeNS", "parseClassListRegex", "RegExp", "updateElement", "oldVnode", "newVnode", "isSvgMode2", "oldVnodeAttrs", "newVnodeAttrs", "sortedAttrNames", "attrNames", "attr", "scopeId", "contentRef", "hostTagName", "useNativeShadowDom", "checkSlotFallbackVisibility", "checkSlotRelocate", "isSvgMode", "createElm", "oldParentVNode", "newParentVNode", "childIndex", "parentElm", "newVNode2", "childNode", "oldVNode", "vdomText", "createTextNode", "isDebug", "slotReferenceDebugNode", "svg", "createElementNS", "rootNode", "isElementWithinShadowRoot", "updateElementScopeIds", "experimentalSlotFixes", "relocateToHostRoot", "putBackInOriginalLocation", "closest", "contentRefNode", "from", "child<PERSON>odeA<PERSON>y", "reverse", "recursive", "oldSlotChildNodes", "parentReferenceNode", "referenceNode", "addVnodes", "before", "vnodes", "startIdx", "endIdx", "containerElm", "removeVnodes", "index", "nullifyVNodeRefs", "update<PERSON><PERSON><PERSON>n", "oldCh", "newCh", "isInitialRender", "oldStartIdx", "newStartIdx", "idxInOld", "oldEndIdx", "oldStartVnode", "oldEndVnode", "newEndIdx", "newStartVnode", "newEndVnode", "elmToMove", "isSameVnode", "patch", "leftVNode", "rightVNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultHolder", "reflect", "slot", "parentElement", "data", "updateFallbackSlotVisibility", "hidden", "siblingNode", "trim", "relocateNodes", "markSlotContentForRelocation", "hostContentNodes", "j", "isNodeLocatedInSlot", "relocateNodeData", "$nodeToRelocate$", "$slotRefNode$", "relocateNode", "nodeToRelocate", "vNode", "parent", "newNode", "reference", "inserted", "findScopeIds", "element", "scopeIds", "iterateChildNodes", "Boolean", "size", "renderVdom", "renderFnResults", "isInitialLoad", "_d", "_e", "rootVnode", "Error", "$attrsToReflect$", "propName", "attribute", "hasAttribute", "relocateData", "originalLocationDebugNode", "slotRefNode", "parentNodeRef", "insertBeforeNode", "previousSibling", "refNode", "experimentalScopedSlotChanges", "slotVNode", "createComment", "localName", "attachToAncestor", "scheduleUpdate", "taskQueue", "dispatch", "dispatchHooks", "endSchedule", "instance", "<PERSON><PERSON><PERSON><PERSON>", "hostListener", "methodName", "event", "safeCall", "emitLifecycleEvent", "cmpWillLoad", "cmpWillUpdate", "cmpWillRender", "enqueue", "updateComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "catch", "err2", "_ref", "_asyncToGenerator", "endUpdate", "rc", "endRender", "callRender", "serverSideConnected", "childrenPromises", "postUpdate", "postUpdateComponent", "_x", "_x2", "_x3", "apply", "arguments", "renderingRef", "allRenderFn", "render", "hasRenderFn", "v<PERSON><PERSON><PERSON>", "getRenderingRef", "endPostUpdate", "cmpDidRender", "addHydratedFlag", "cmpDidLoad", "appDidLoad", "cmpDidUpdate", "forceUpdate", "who", "documentElement", "namespace", "arg", "lifecycleName", "lifecycleDOMEvents", "hydratedClass", "hydratedSelectorName", "hydratedAttribute", "ii", "<PERSON><PERSON><PERSON>", "connectedCallback", "getValue", "setValue", "oldVal", "$members$", "areBothNaN", "Number", "isNaN", "didValueChange", "watchCallback", "$watchers$", "watchMethods", "watchMethodName", "cmpShouldUpdate", "componentShouldUpdate", "proxyComponent", "Cstr", "prototype", "formAssociated", "cbName", "args", "instance2", "call", "member", "watchers", "members", "entries", "memberFlags", "state", "configurable", "_a2", "_a3", "observeAttribute", "attrNameToPropName", "attributeChangedCallback", "attrName", "hasOwnProperty", "flags2", "entry", "callback<PERSON><PERSON>", "observedAttributes", "_", "initializeComponent", "_ref2", "CstrImport", "endLoad", "isProxied", "endNewInstance", "fireConnectedCallback", "constructor", "cmpTag", "customElements", "whenDefined", "endRegisterStyles", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scopeCss", "schedule", "_x4", "_x5", "_x6", "_x7", "endConnected", "hostListenerTargetParent", "addHostEventListeners", "$listeners$", "setContentReference", "initializeNextTick", "contentRefElm", "<PERSON><PERSON><PERSON><PERSON>", "BUILD22", "disconnectInstance", "disconnectedCallback", "cmpDidUnload", "_ref3", "rmListener", "_x8", "BUILD23", "patchPseudoShadowDom", "hostElementPrototype", "descriptorPrototype", "patchCloneNode", "patchSlotAppendChild", "patchSlotAppend", "patchSlotPrepend", "patchSlotInsertAdjacentElement", "patchSlotInsertAdjacentHTML", "patchSlotInsertAdjacentText", "patchTextContent", "patchChildSlotNodes", "patchSlotRemoveChild", "HostElementPrototype", "orgCloneNode", "cloneNode", "deep", "srcNode", "isShadowDom", "clonedNode", "slotted", "nonStencilNode", "stencilPrivates", "every", "privateField", "appendChildSlotFix", "__append<PERSON><PERSON>d", "<PERSON><PERSON><PERSON><PERSON>", "getSlotName", "slotNode", "getHostSlotNode", "slotChildNodes", "getHostSlotChildNodes", "appendAfter", "insertedNode", "ElementPrototype", "__remove<PERSON><PERSON>d", "<PERSON><PERSON><PERSON><PERSON>", "toRemove", "existingNode", "originalPrepend", "prepend", "ownerDocument", "slotPlaceholder", "append", "originalInsertAdjacentHtml", "insertAdjacentHTML", "position", "container", "insertAdjacentText", "originalInsertAdjacentElement", "insertAdjacentElement", "descriptor", "getOwnPropertyDescriptor", "Node", "slotRefNodes", "getAllChildSlotNodes", "slotContent", "tmp", "textNode", "__textContent", "FakeNodeList", "item", "childNodesFn", "__lookupGetter__", "hostName", "defineCustomElement", "compactMeta", "define", "proxyCustomElement", "slotChildNodesFix", "cloneNodeFix", "scopedSlotTextContentFix", "originalConnectedCallback", "originalDisconnectedCallback", "__registerHost", "__attachShadow", "shadowDelegatesFocus", "attachShadow", "delegatesFocus", "is", "forceModeUpdate", "oldScopeId", "BUILD25", "hmrStart", "bootstrapLazy", "lazyB<PERSON>les", "options", "endBootstrap", "cmpTags", "exclude", "customElements2", "metaCharset", "dataStyles", "deferredConnectedCallbacks", "appLoadFallback", "isBootstrapping", "resourcesUrl", "baseURI", "syncQueue", "hasSlotRelocation", "lazyBundle", "transformTagName", "HostElement", "self", "hasRegisteredEventListeners", "clearTimeout", "componentOnReady", "invisiblePrehydration", "sort", "setTimeout", "Fragment", "BUILD26", "listeners", "attachParentListeners", "hostListenerTarget", "getHostListenerTarget", "hostListenerProxy", "hostListenerOpts", "hostListenerTargetDocument", "hostListenerTargetWindow", "hostListenerTargetBody", "passive", "setNonce", "setPlatformOptions", "insertVdomAnnotations", "staticComponents", "docData", "hostIds", "rootLevelIds", "orgLocationNodes", "parseVNodeAnnotations", "nodeRef", "nodeId", "childId", "commentBeforeTextNode", "orgLocationNodeId", "orgLocationParentNode", "cmpData", "nodeIds", "insertVNodeAnnotations", "depth", "vnodeChild", "insertChildVNodeAnnotations", "parentChildNodes", "comment", "textNodeId", "slotNodeId", "<PERSON><PERSON><PERSON><PERSON>", "index2"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@stencil/core/internal/client/index.js"], "sourcesContent": ["/*\n Stencil Client Platform v4.20.0 | MIT Licensed | https://stenciljs.com\n */\nvar __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// src/client/client-build.ts\nimport { BUILD } from \"@stencil/core/internal/app-data\";\nvar Build = {\n  isDev: BUILD.isDev ? true : false,\n  isBrowser: true,\n  isServer: false,\n  isTesting: BUILD.isTesting ? true : false\n};\n\n// src/client/client-host-ref.ts\nimport { BUILD as BUILD2 } from \"@stencil/core/internal/app-data\";\nvar hostRefs = BUILD2.hotModuleReplacement ? window.__STENCIL_HOSTREFS__ || (window.__STENCIL_HOSTREFS__ = /* @__PURE__ */ new WeakMap()) : /* @__PURE__ */ new WeakMap();\nvar getHostRef = (ref) => hostRefs.get(ref);\nvar registerInstance = (lazyInstance, hostRef) => hostRefs.set(hostRef.$lazyInstance$ = lazyInstance, hostRef);\nvar registerHost = (hostElement, cmpMeta) => {\n  const hostRef = {\n    $flags$: 0,\n    $hostElement$: hostElement,\n    $cmpMeta$: cmpMeta,\n    $instanceValues$: /* @__PURE__ */ new Map()\n  };\n  if (BUILD2.isDev) {\n    hostRef.$renderCount$ = 0;\n  }\n  if (BUILD2.method && BUILD2.lazyLoad) {\n    hostRef.$onInstancePromise$ = new Promise((r) => hostRef.$onInstanceResolve$ = r);\n  }\n  if (BUILD2.asyncLoading) {\n    hostRef.$onReadyPromise$ = new Promise((r) => hostRef.$onReadyResolve$ = r);\n    hostElement[\"s-p\"] = [];\n    hostElement[\"s-rc\"] = [];\n  }\n  return hostRefs.set(hostElement, hostRef);\n};\nvar isMemberInElement = (elm, memberName) => memberName in elm;\n\n// src/client/client-load-module.ts\nimport { BUILD as BUILD4 } from \"@stencil/core/internal/app-data\";\n\n// src/client/client-log.ts\nimport { BUILD as BUILD3 } from \"@stencil/core/internal/app-data\";\nvar customError;\nvar consoleError = (e, el) => (customError || console.error)(e, el);\nvar STENCIL_DEV_MODE = BUILD3.isTesting ? [\"STENCIL:\"] : [\n  \"%cstencil\",\n  \"color: white;background:#4c47ff;font-weight: bold; font-size:10px; padding:2px 6px; border-radius: 5px\"\n];\nvar consoleDevError = (...m) => console.error(...STENCIL_DEV_MODE, ...m);\nvar consoleDevWarn = (...m) => console.warn(...STENCIL_DEV_MODE, ...m);\nvar consoleDevInfo = (...m) => console.info(...STENCIL_DEV_MODE, ...m);\nvar setErrorHandler = (handler) => customError = handler;\n\n// src/client/client-load-module.ts\nvar cmpModules = /* @__PURE__ */ new Map();\nvar MODULE_IMPORT_PREFIX = \"./\";\nvar loadModule = (cmpMeta, hostRef, hmrVersionId) => {\n  const exportName = cmpMeta.$tagName$.replace(/-/g, \"_\");\n  const bundleId = cmpMeta.$lazyBundleId$;\n  if (BUILD4.isDev && typeof bundleId !== \"string\") {\n    consoleDevError(\n      `Trying to lazily load component <${cmpMeta.$tagName$}> with style mode \"${hostRef.$modeName$}\", but it does not exist.`\n    );\n    return void 0;\n  } else if (!bundleId) {\n    return void 0;\n  }\n  const module = !BUILD4.hotModuleReplacement ? cmpModules.get(bundleId) : false;\n  if (module) {\n    return module[exportName];\n  }\n  /*!__STENCIL_STATIC_IMPORT_SWITCH__*/\n  return import(\n    /* @vite-ignore */\n    /* webpackInclude: /\\.entry\\.js$/ */\n    /* webpackExclude: /\\.system\\.entry\\.js$/ */\n    /* webpackMode: \"lazy\" */\n    `./${bundleId}.entry.js${BUILD4.hotModuleReplacement && hmrVersionId ? \"?s-hmr=\" + hmrVersionId : \"\"}`\n  ).then((importedModule) => {\n    if (!BUILD4.hotModuleReplacement) {\n      cmpModules.set(bundleId, importedModule);\n    }\n    return importedModule[exportName];\n  }, consoleError);\n};\n\n// src/client/client-style.ts\nvar styles = /* @__PURE__ */ new Map();\nvar modeResolutionChain = [];\n\n// src/client/client-task-queue.ts\nimport { BUILD as BUILD6 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/runtime-constants.ts\nvar CONTENT_REF_ID = \"r\";\nvar ORG_LOCATION_ID = \"o\";\nvar SLOT_NODE_ID = \"s\";\nvar TEXT_NODE_ID = \"t\";\nvar HYDRATE_ID = \"s-id\";\nvar HYDRATED_STYLE_ID = \"sty-id\";\nvar HYDRATE_CHILD_ID = \"c-id\";\nvar HYDRATED_CSS = \"{visibility:hidden}.hydrated{visibility:inherit}\";\nvar SLOT_FB_CSS = \"slot-fb{display:contents}slot-fb[hidden]{display:none}\";\nvar XLINK_NS = \"http://www.w3.org/1999/xlink\";\nvar FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS = [\n  \"formAssociatedCallback\",\n  \"formResetCallback\",\n  \"formDisabledCallback\",\n  \"formStateRestoreCallback\"\n];\n\n// src/client/client-window.ts\nimport { BUILD as BUILD5 } from \"@stencil/core/internal/app-data\";\nvar win = typeof window !== \"undefined\" ? window : {};\nvar doc = win.document || { head: {} };\nvar H = win.HTMLElement || class {\n};\nvar plt = {\n  $flags$: 0,\n  $resourcesUrl$: \"\",\n  jmp: (h2) => h2(),\n  raf: (h2) => requestAnimationFrame(h2),\n  ael: (el, eventName, listener, opts) => el.addEventListener(eventName, listener, opts),\n  rel: (el, eventName, listener, opts) => el.removeEventListener(eventName, listener, opts),\n  ce: (eventName, opts) => new CustomEvent(eventName, opts)\n};\nvar setPlatformHelpers = (helpers) => {\n  Object.assign(plt, helpers);\n};\nvar supportsShadow = BUILD5.shadowDom;\nvar supportsListenerOptions = /* @__PURE__ */ (() => {\n  let supportsListenerOptions2 = false;\n  try {\n    doc.addEventListener(\n      \"e\",\n      null,\n      Object.defineProperty({}, \"passive\", {\n        get() {\n          supportsListenerOptions2 = true;\n        }\n      })\n    );\n  } catch (e) {\n  }\n  return supportsListenerOptions2;\n})();\nvar promiseResolve = (v) => Promise.resolve(v);\nvar supportsConstructableStylesheets = BUILD5.constructableCSS ? /* @__PURE__ */ (() => {\n  try {\n    new CSSStyleSheet();\n    return typeof new CSSStyleSheet().replaceSync === \"function\";\n  } catch (e) {\n  }\n  return false;\n})() : false;\n\n// src/client/client-task-queue.ts\nvar queueCongestion = 0;\nvar queuePending = false;\nvar queueDomReads = [];\nvar queueDomWrites = [];\nvar queueDomWritesLow = [];\nvar queueTask = (queue, write) => (cb) => {\n  queue.push(cb);\n  if (!queuePending) {\n    queuePending = true;\n    if (write && plt.$flags$ & 4 /* queueSync */) {\n      nextTick(flush);\n    } else {\n      plt.raf(flush);\n    }\n  }\n};\nvar consume = (queue) => {\n  for (let i2 = 0; i2 < queue.length; i2++) {\n    try {\n      queue[i2](performance.now());\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  queue.length = 0;\n};\nvar consumeTimeout = (queue, timeout) => {\n  let i2 = 0;\n  let ts = 0;\n  while (i2 < queue.length && (ts = performance.now()) < timeout) {\n    try {\n      queue[i2++](ts);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  if (i2 === queue.length) {\n    queue.length = 0;\n  } else if (i2 !== 0) {\n    queue.splice(0, i2);\n  }\n};\nvar flush = () => {\n  if (BUILD6.asyncQueue) {\n    queueCongestion++;\n  }\n  consume(queueDomReads);\n  if (BUILD6.asyncQueue) {\n    const timeout = (plt.$flags$ & 6 /* queueMask */) === 2 /* appLoaded */ ? performance.now() + 14 * Math.ceil(queueCongestion * (1 / 10)) : Infinity;\n    consumeTimeout(queueDomWrites, timeout);\n    consumeTimeout(queueDomWritesLow, timeout);\n    if (queueDomWrites.length > 0) {\n      queueDomWritesLow.push(...queueDomWrites);\n      queueDomWrites.length = 0;\n    }\n    if (queuePending = queueDomReads.length + queueDomWrites.length + queueDomWritesLow.length > 0) {\n      plt.raf(flush);\n    } else {\n      queueCongestion = 0;\n    }\n  } else {\n    consume(queueDomWrites);\n    if (queuePending = queueDomReads.length > 0) {\n      plt.raf(flush);\n    }\n  }\n};\nvar nextTick = (cb) => promiseResolve().then(cb);\nvar readTask = /* @__PURE__ */ queueTask(queueDomReads, false);\nvar writeTask = /* @__PURE__ */ queueTask(queueDomWrites, true);\n\n// src/client/index.ts\nimport { BUILD as BUILD27, Env, NAMESPACE as NAMESPACE2 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/asset-path.ts\nvar getAssetPath = (path) => {\n  const assetUrl = new URL(path, plt.$resourcesUrl$);\n  return assetUrl.origin !== win.location.origin ? assetUrl.href : assetUrl.pathname;\n};\nvar setAssetPath = (path) => plt.$resourcesUrl$ = path;\n\n// src/runtime/bootstrap-custom-element.ts\nimport { BUILD as BUILD24 } from \"@stencil/core/internal/app-data\";\n\n// src/utils/constants.ts\nvar EMPTY_OBJ = {};\nvar SVG_NS = \"http://www.w3.org/2000/svg\";\nvar HTML_NS = \"http://www.w3.org/1999/xhtml\";\n\n// src/utils/helpers.ts\nvar isDef = (v) => v != null;\nvar isComplexType = (o) => {\n  o = typeof o;\n  return o === \"object\" || o === \"function\";\n};\n\n// src/utils/query-nonce-meta-tag-content.ts\nfunction queryNonceMetaTagContent(doc2) {\n  var _a, _b, _c;\n  return (_c = (_b = (_a = doc2.head) == null ? void 0 : _a.querySelector('meta[name=\"csp-nonce\"]')) == null ? void 0 : _b.getAttribute(\"content\")) != null ? _c : void 0;\n}\n\n// src/utils/result.ts\nvar result_exports = {};\n__export(result_exports, {\n  err: () => err,\n  map: () => map,\n  ok: () => ok,\n  unwrap: () => unwrap,\n  unwrapErr: () => unwrapErr\n});\nvar ok = (value) => ({\n  isOk: true,\n  isErr: false,\n  value\n});\nvar err = (value) => ({\n  isOk: false,\n  isErr: true,\n  value\n});\nfunction map(result, fn) {\n  if (result.isOk) {\n    const val = fn(result.value);\n    if (val instanceof Promise) {\n      return val.then((newVal) => ok(newVal));\n    } else {\n      return ok(val);\n    }\n  }\n  if (result.isErr) {\n    const value = result.value;\n    return err(value);\n  }\n  throw \"should never get here\";\n}\nvar unwrap = (result) => {\n  if (result.isOk) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\nvar unwrapErr = (result) => {\n  if (result.isErr) {\n    return result.value;\n  } else {\n    throw result.value;\n  }\n};\n\n// src/runtime/connected-callback.ts\nimport { BUILD as BUILD21 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/client-hydrate.ts\nimport { BUILD as BUILD9 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/profile.ts\nimport { BUILD as BUILD7 } from \"@stencil/core/internal/app-data\";\nvar i = 0;\nvar createTime = (fnName, tagName = \"\") => {\n  if (BUILD7.profile && performance.mark) {\n    const key = `st:${fnName}:${tagName}:${i++}`;\n    performance.mark(key);\n    return () => performance.measure(`[Stencil] ${fnName}() <${tagName}>`, key);\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar uniqueTime = (key, measureText) => {\n  if (BUILD7.profile && performance.mark) {\n    if (performance.getEntriesByName(key, \"mark\").length === 0) {\n      performance.mark(key);\n    }\n    return () => {\n      if (performance.getEntriesByName(measureText, \"measure\").length === 0) {\n        performance.measure(measureText, key);\n      }\n    };\n  } else {\n    return () => {\n      return;\n    };\n  }\n};\nvar inspect = (ref) => {\n  const hostRef = getHostRef(ref);\n  if (!hostRef) {\n    return void 0;\n  }\n  const flags = hostRef.$flags$;\n  const hostElement = hostRef.$hostElement$;\n  return {\n    renderCount: hostRef.$renderCount$,\n    flags: {\n      hasRendered: !!(flags & 2 /* hasRendered */),\n      hasConnected: !!(flags & 1 /* hasConnected */),\n      isWaitingForChildren: !!(flags & 4 /* isWaitingForChildren */),\n      isConstructingInstance: !!(flags & 8 /* isConstructingInstance */),\n      isQueuedForUpdate: !!(flags & 16 /* isQueuedForUpdate */),\n      hasInitializedComponent: !!(flags & 32 /* hasInitializedComponent */),\n      hasLoadedComponent: !!(flags & 64 /* hasLoadedComponent */),\n      isWatchReady: !!(flags & 128 /* isWatchReady */),\n      isListenReady: !!(flags & 256 /* isListenReady */),\n      needsRerender: !!(flags & 512 /* needsRerender */)\n    },\n    instanceValues: hostRef.$instanceValues$,\n    ancestorComponent: hostRef.$ancestorComponent$,\n    hostElement,\n    lazyInstance: hostRef.$lazyInstance$,\n    vnode: hostRef.$vnode$,\n    modeName: hostRef.$modeName$,\n    onReadyPromise: hostRef.$onReadyPromise$,\n    onReadyResolve: hostRef.$onReadyResolve$,\n    onInstancePromise: hostRef.$onInstancePromise$,\n    onInstanceResolve: hostRef.$onInstanceResolve$,\n    onRenderResolve: hostRef.$onRenderResolve$,\n    queuedListeners: hostRef.$queuedListeners$,\n    rmListeners: hostRef.$rmListeners$,\n    [\"s-id\"]: hostElement[\"s-id\"],\n    [\"s-cr\"]: hostElement[\"s-cr\"],\n    [\"s-lr\"]: hostElement[\"s-lr\"],\n    [\"s-p\"]: hostElement[\"s-p\"],\n    [\"s-rc\"]: hostElement[\"s-rc\"],\n    [\"s-sc\"]: hostElement[\"s-sc\"]\n  };\n};\nvar installDevTools = () => {\n  if (BUILD7.devTools) {\n    const stencil = win.stencil = win.stencil || {};\n    const originalInspect = stencil.inspect;\n    stencil.inspect = (ref) => {\n      let result = inspect(ref);\n      if (!result && typeof originalInspect === \"function\") {\n        result = originalInspect(ref);\n      }\n      return result;\n    };\n  }\n};\n\n// src/runtime/vdom/h.ts\nimport { BUILD as BUILD8 } from \"@stencil/core/internal/app-data\";\nvar h = (nodeName, vnodeData, ...children) => {\n  let child = null;\n  let key = null;\n  let slotName = null;\n  let simple = false;\n  let lastSimple = false;\n  const vNodeChildren = [];\n  const walk = (c) => {\n    for (let i2 = 0; i2 < c.length; i2++) {\n      child = c[i2];\n      if (Array.isArray(child)) {\n        walk(child);\n      } else if (child != null && typeof child !== \"boolean\") {\n        if (simple = typeof nodeName !== \"function\" && !isComplexType(child)) {\n          child = String(child);\n        } else if (BUILD8.isDev && typeof nodeName !== \"function\" && child.$flags$ === void 0) {\n          consoleDevError(`vNode passed as children has unexpected type.\nMake sure it's using the correct h() function.\nEmpty objects can also be the cause, look for JSX comments that became objects.`);\n        }\n        if (simple && lastSimple) {\n          vNodeChildren[vNodeChildren.length - 1].$text$ += child;\n        } else {\n          vNodeChildren.push(simple ? newVNode(null, child) : child);\n        }\n        lastSimple = simple;\n      }\n    }\n  };\n  walk(children);\n  if (vnodeData) {\n    if (BUILD8.isDev && nodeName === \"input\") {\n      validateInputProperties(vnodeData);\n    }\n    if (BUILD8.vdomKey && vnodeData.key) {\n      key = vnodeData.key;\n    }\n    if (BUILD8.slotRelocation && vnodeData.name) {\n      slotName = vnodeData.name;\n    }\n    if (BUILD8.vdomClass) {\n      const classData = vnodeData.className || vnodeData.class;\n      if (classData) {\n        vnodeData.class = typeof classData !== \"object\" ? classData : Object.keys(classData).filter((k) => classData[k]).join(\" \");\n      }\n    }\n  }\n  if (BUILD8.isDev && vNodeChildren.some(isHost)) {\n    consoleDevError(`The <Host> must be the single root component. Make sure:\n- You are NOT using hostData() and <Host> in the same component.\n- <Host> is used once, and it's the single root component of the render() function.`);\n  }\n  if (BUILD8.vdomFunctional && typeof nodeName === \"function\") {\n    return nodeName(\n      vnodeData === null ? {} : vnodeData,\n      vNodeChildren,\n      vdomFnUtils\n    );\n  }\n  const vnode = newVNode(nodeName, null);\n  vnode.$attrs$ = vnodeData;\n  if (vNodeChildren.length > 0) {\n    vnode.$children$ = vNodeChildren;\n  }\n  if (BUILD8.vdomKey) {\n    vnode.$key$ = key;\n  }\n  if (BUILD8.slotRelocation) {\n    vnode.$name$ = slotName;\n  }\n  return vnode;\n};\nvar newVNode = (tag, text) => {\n  const vnode = {\n    $flags$: 0,\n    $tag$: tag,\n    $text$: text,\n    $elm$: null,\n    $children$: null\n  };\n  if (BUILD8.vdomAttribute) {\n    vnode.$attrs$ = null;\n  }\n  if (BUILD8.vdomKey) {\n    vnode.$key$ = null;\n  }\n  if (BUILD8.slotRelocation) {\n    vnode.$name$ = null;\n  }\n  return vnode;\n};\nvar Host = {};\nvar isHost = (node) => node && node.$tag$ === Host;\nvar vdomFnUtils = {\n  forEach: (children, cb) => children.map(convertToPublic).forEach(cb),\n  map: (children, cb) => children.map(convertToPublic).map(cb).map(convertToPrivate)\n};\nvar convertToPublic = (node) => ({\n  vattrs: node.$attrs$,\n  vchildren: node.$children$,\n  vkey: node.$key$,\n  vname: node.$name$,\n  vtag: node.$tag$,\n  vtext: node.$text$\n});\nvar convertToPrivate = (node) => {\n  if (typeof node.vtag === \"function\") {\n    const vnodeData = { ...node.vattrs };\n    if (node.vkey) {\n      vnodeData.key = node.vkey;\n    }\n    if (node.vname) {\n      vnodeData.name = node.vname;\n    }\n    return h(node.vtag, vnodeData, ...node.vchildren || []);\n  }\n  const vnode = newVNode(node.vtag, node.vtext);\n  vnode.$attrs$ = node.vattrs;\n  vnode.$children$ = node.vchildren;\n  vnode.$key$ = node.vkey;\n  vnode.$name$ = node.vname;\n  return vnode;\n};\nvar validateInputProperties = (inputElm) => {\n  const props = Object.keys(inputElm);\n  const value = props.indexOf(\"value\");\n  if (value === -1) {\n    return;\n  }\n  const typeIndex = props.indexOf(\"type\");\n  const minIndex = props.indexOf(\"min\");\n  const maxIndex = props.indexOf(\"max\");\n  const stepIndex = props.indexOf(\"step\");\n  if (value < typeIndex || value < minIndex || value < maxIndex || value < stepIndex) {\n    consoleDevWarn(`The \"value\" prop of <input> should be set after \"min\", \"max\", \"type\" and \"step\"`);\n  }\n};\n\n// src/runtime/client-hydrate.ts\nvar initializeClientHydrate = (hostElm, tagName, hostId, hostRef) => {\n  const endHydrate = createTime(\"hydrateClient\", tagName);\n  const shadowRoot = hostElm.shadowRoot;\n  const childRenderNodes = [];\n  const slotNodes = [];\n  const shadowRootNodes = BUILD9.shadowDom && shadowRoot ? [] : null;\n  const vnode = hostRef.$vnode$ = newVNode(tagName, null);\n  if (!plt.$orgLocNodes$) {\n    initializeDocumentHydrate(doc.body, plt.$orgLocNodes$ = /* @__PURE__ */ new Map());\n  }\n  hostElm[HYDRATE_ID] = hostId;\n  hostElm.removeAttribute(HYDRATE_ID);\n  clientHydrate(vnode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, hostElm, hostId);\n  childRenderNodes.map((c) => {\n    const orgLocationId = c.$hostId$ + \".\" + c.$nodeId$;\n    const orgLocationNode = plt.$orgLocNodes$.get(orgLocationId);\n    const node = c.$elm$;\n    if (orgLocationNode && supportsShadow && orgLocationNode[\"s-en\"] === \"\") {\n      orgLocationNode.parentNode.insertBefore(node, orgLocationNode.nextSibling);\n    }\n    if (!shadowRoot) {\n      node[\"s-hn\"] = tagName;\n      if (orgLocationNode) {\n        node[\"s-ol\"] = orgLocationNode;\n        node[\"s-ol\"][\"s-nr\"] = node;\n      }\n    }\n    plt.$orgLocNodes$.delete(orgLocationId);\n  });\n  if (BUILD9.shadowDom && shadowRoot) {\n    shadowRootNodes.map((shadowRootNode) => {\n      if (shadowRootNode) {\n        shadowRoot.appendChild(shadowRootNode);\n      }\n    });\n  }\n  endHydrate();\n};\nvar clientHydrate = (parentVNode, childRenderNodes, slotNodes, shadowRootNodes, hostElm, node, hostId) => {\n  let childNodeType;\n  let childIdSplt;\n  let childVNode;\n  let i2;\n  if (node.nodeType === 1 /* ElementNode */) {\n    childNodeType = node.getAttribute(HYDRATE_CHILD_ID);\n    if (childNodeType) {\n      childIdSplt = childNodeType.split(\".\");\n      if (childIdSplt[0] === hostId || childIdSplt[0] === \"0\") {\n        childVNode = {\n          $flags$: 0,\n          $hostId$: childIdSplt[0],\n          $nodeId$: childIdSplt[1],\n          $depth$: childIdSplt[2],\n          $index$: childIdSplt[3],\n          $tag$: node.tagName.toLowerCase(),\n          $elm$: node,\n          $attrs$: null,\n          $children$: null,\n          $key$: null,\n          $name$: null,\n          $text$: null\n        };\n        childRenderNodes.push(childVNode);\n        node.removeAttribute(HYDRATE_CHILD_ID);\n        if (!parentVNode.$children$) {\n          parentVNode.$children$ = [];\n        }\n        parentVNode.$children$[childVNode.$index$] = childVNode;\n        parentVNode = childVNode;\n        if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n          shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n        }\n      }\n    }\n    if (node.shadowRoot) {\n      for (i2 = node.shadowRoot.childNodes.length - 1; i2 >= 0; i2--) {\n        clientHydrate(\n          parentVNode,\n          childRenderNodes,\n          slotNodes,\n          shadowRootNodes,\n          hostElm,\n          node.shadowRoot.childNodes[i2],\n          hostId\n        );\n      }\n    }\n    for (i2 = node.childNodes.length - 1; i2 >= 0; i2--) {\n      clientHydrate(\n        parentVNode,\n        childRenderNodes,\n        slotNodes,\n        shadowRootNodes,\n        hostElm,\n        node.childNodes[i2],\n        hostId\n      );\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[1] === hostId || childIdSplt[1] === \"0\") {\n      childNodeType = childIdSplt[0];\n      childVNode = {\n        $flags$: 0,\n        $hostId$: childIdSplt[1],\n        $nodeId$: childIdSplt[2],\n        $depth$: childIdSplt[3],\n        $index$: childIdSplt[4],\n        $elm$: node,\n        $attrs$: null,\n        $children$: null,\n        $key$: null,\n        $name$: null,\n        $tag$: null,\n        $text$: null\n      };\n      if (childNodeType === TEXT_NODE_ID) {\n        childVNode.$elm$ = node.nextSibling;\n        if (childVNode.$elm$ && childVNode.$elm$.nodeType === 3 /* TextNode */) {\n          childVNode.$text$ = childVNode.$elm$.textContent;\n          childRenderNodes.push(childVNode);\n          node.remove();\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n          if (shadowRootNodes && childVNode.$depth$ === \"0\") {\n            shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n          }\n        }\n      } else if (childVNode.$hostId$ === hostId) {\n        if (childNodeType === SLOT_NODE_ID) {\n          childVNode.$tag$ = \"slot\";\n          if (childIdSplt[5]) {\n            node[\"s-sn\"] = childVNode.$name$ = childIdSplt[5];\n          } else {\n            node[\"s-sn\"] = \"\";\n          }\n          node[\"s-sr\"] = true;\n          if (BUILD9.shadowDom && shadowRootNodes) {\n            childVNode.$elm$ = doc.createElement(childVNode.$tag$);\n            if (childVNode.$name$) {\n              childVNode.$elm$.setAttribute(\"name\", childVNode.$name$);\n            }\n            node.parentNode.insertBefore(childVNode.$elm$, node);\n            node.remove();\n            if (childVNode.$depth$ === \"0\") {\n              shadowRootNodes[childVNode.$index$] = childVNode.$elm$;\n            }\n          }\n          slotNodes.push(childVNode);\n          if (!parentVNode.$children$) {\n            parentVNode.$children$ = [];\n          }\n          parentVNode.$children$[childVNode.$index$] = childVNode;\n        } else if (childNodeType === CONTENT_REF_ID) {\n          if (BUILD9.shadowDom && shadowRootNodes) {\n            node.remove();\n          } else if (BUILD9.slotRelocation) {\n            hostElm[\"s-cr\"] = node;\n            node[\"s-cn\"] = true;\n          }\n        }\n      }\n    }\n  } else if (parentVNode && parentVNode.$tag$ === \"style\") {\n    const vnode = newVNode(null, node.textContent);\n    vnode.$elm$ = node;\n    vnode.$index$ = \"0\";\n    parentVNode.$children$ = [vnode];\n  }\n};\nvar initializeDocumentHydrate = (node, orgLocNodes) => {\n  if (node.nodeType === 1 /* ElementNode */) {\n    let i2 = 0;\n    if (node.shadowRoot) {\n      for (; i2 < node.shadowRoot.childNodes.length; i2++) {\n        initializeDocumentHydrate(node.shadowRoot.childNodes[i2], orgLocNodes);\n      }\n    }\n    for (i2 = 0; i2 < node.childNodes.length; i2++) {\n      initializeDocumentHydrate(node.childNodes[i2], orgLocNodes);\n    }\n  } else if (node.nodeType === 8 /* CommentNode */) {\n    const childIdSplt = node.nodeValue.split(\".\");\n    if (childIdSplt[0] === ORG_LOCATION_ID) {\n      orgLocNodes.set(childIdSplt[1] + \".\" + childIdSplt[2], node);\n      node.nodeValue = \"\";\n      node[\"s-en\"] = childIdSplt[3];\n    }\n  }\n};\n\n// src/runtime/initialize-component.ts\nimport { BUILD as BUILD20 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/mode.ts\nvar computeMode = (elm) => modeResolutionChain.map((h2) => h2(elm)).find((m) => !!m);\nvar setMode = (handler) => modeResolutionChain.push(handler);\nvar getMode = (ref) => getHostRef(ref).$modeName$;\n\n// src/runtime/proxy-component.ts\nimport { BUILD as BUILD19 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/set-value.ts\nimport { BUILD as BUILD18 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/parse-property-value.ts\nimport { BUILD as BUILD10 } from \"@stencil/core/internal/app-data\";\nvar parsePropertyValue = (propValue, propType) => {\n  if (propValue != null && !isComplexType(propValue)) {\n    if (BUILD10.propBoolean && propType & 4 /* Boolean */) {\n      return propValue === \"false\" ? false : propValue === \"\" || !!propValue;\n    }\n    if (BUILD10.propNumber && propType & 2 /* Number */) {\n      return parseFloat(propValue);\n    }\n    if (BUILD10.propString && propType & 1 /* String */) {\n      return String(propValue);\n    }\n    return propValue;\n  }\n  return propValue;\n};\n\n// src/runtime/update-component.ts\nimport { BUILD as BUILD17, NAMESPACE } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/event-emitter.ts\nimport { BUILD as BUILD12 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/element.ts\nimport { BUILD as BUILD11 } from \"@stencil/core/internal/app-data\";\nvar getElement = (ref) => BUILD11.lazyLoad ? getHostRef(ref).$hostElement$ : ref;\n\n// src/runtime/event-emitter.ts\nvar createEvent = (ref, name, flags) => {\n  const elm = getElement(ref);\n  return {\n    emit: (detail) => {\n      if (BUILD12.isDev && !elm.isConnected) {\n        consoleDevWarn(`The \"${name}\" event was emitted, but the dispatcher node is no longer connected to the dom.`);\n      }\n      return emitEvent(elm, name, {\n        bubbles: !!(flags & 4 /* Bubbles */),\n        composed: !!(flags & 2 /* Composed */),\n        cancelable: !!(flags & 1 /* Cancellable */),\n        detail\n      });\n    }\n  };\n};\nvar emitEvent = (elm, name, opts) => {\n  const ev = plt.ce(name, opts);\n  elm.dispatchEvent(ev);\n  return ev;\n};\n\n// src/runtime/styles.ts\nimport { BUILD as BUILD13 } from \"@stencil/core/internal/app-data\";\nvar rootAppliedStyles = /* @__PURE__ */ new WeakMap();\nvar registerStyle = (scopeId2, cssText, allowCS) => {\n  let style = styles.get(scopeId2);\n  if (supportsConstructableStylesheets && allowCS) {\n    style = style || new CSSStyleSheet();\n    if (typeof style === \"string\") {\n      style = cssText;\n    } else {\n      style.replaceSync(cssText);\n    }\n  } else {\n    style = cssText;\n  }\n  styles.set(scopeId2, style);\n};\nvar addStyle = (styleContainerNode, cmpMeta, mode) => {\n  var _a;\n  const scopeId2 = getScopeId(cmpMeta, mode);\n  const style = styles.get(scopeId2);\n  if (!BUILD13.attachStyles) {\n    return scopeId2;\n  }\n  styleContainerNode = styleContainerNode.nodeType === 11 /* DocumentFragment */ ? styleContainerNode : doc;\n  if (style) {\n    if (typeof style === \"string\") {\n      styleContainerNode = styleContainerNode.head || styleContainerNode;\n      let appliedStyles = rootAppliedStyles.get(styleContainerNode);\n      let styleElm;\n      if (!appliedStyles) {\n        rootAppliedStyles.set(styleContainerNode, appliedStyles = /* @__PURE__ */ new Set());\n      }\n      if (!appliedStyles.has(scopeId2)) {\n        if (BUILD13.hydrateClientSide && styleContainerNode.host && (styleElm = styleContainerNode.querySelector(`[${HYDRATED_STYLE_ID}=\"${scopeId2}\"]`))) {\n          styleElm.innerHTML = style;\n        } else {\n          styleElm = doc.createElement(\"style\");\n          styleElm.innerHTML = style;\n          const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n          if (nonce != null) {\n            styleElm.setAttribute(\"nonce\", nonce);\n          }\n          if ((BUILD13.hydrateServerSide || BUILD13.hotModuleReplacement) && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n            styleElm.setAttribute(HYDRATED_STYLE_ID, scopeId2);\n          }\n          const injectStyle = (\n            /**\n             * we render a scoped component\n             */\n            !(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) || /**\n             * we are using shadow dom and render the style tag within the shadowRoot\n             */\n            cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */ && styleContainerNode.nodeName !== \"HEAD\"\n          );\n          if (injectStyle) {\n            styleContainerNode.insertBefore(styleElm, styleContainerNode.querySelector(\"link\"));\n          }\n        }\n        if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n          styleElm.innerHTML += SLOT_FB_CSS;\n        }\n        if (appliedStyles) {\n          appliedStyles.add(scopeId2);\n        }\n      }\n    } else if (BUILD13.constructableCSS && !styleContainerNode.adoptedStyleSheets.includes(style)) {\n      styleContainerNode.adoptedStyleSheets = [...styleContainerNode.adoptedStyleSheets, style];\n    }\n  }\n  return scopeId2;\n};\nvar attachStyles = (hostRef) => {\n  const cmpMeta = hostRef.$cmpMeta$;\n  const elm = hostRef.$hostElement$;\n  const flags = cmpMeta.$flags$;\n  const endAttachStyles = createTime(\"attachStyles\", cmpMeta.$tagName$);\n  const scopeId2 = addStyle(\n    BUILD13.shadowDom && supportsShadow && elm.shadowRoot ? elm.shadowRoot : elm.getRootNode(),\n    cmpMeta,\n    hostRef.$modeName$\n  );\n  if ((BUILD13.shadowDom || BUILD13.scoped) && BUILD13.cssAnnotations && flags & 10 /* needsScopedEncapsulation */ && flags & 2 /* scopedCssEncapsulation */) {\n    elm[\"s-sc\"] = scopeId2;\n    elm.classList.add(scopeId2 + \"-h\");\n    if (BUILD13.scoped && flags & 2 /* scopedCssEncapsulation */) {\n      elm.classList.add(scopeId2 + \"-s\");\n    }\n  }\n  endAttachStyles();\n};\nvar getScopeId = (cmp, mode) => \"sc-\" + (BUILD13.mode && mode && cmp.$flags$ & 32 /* hasMode */ ? cmp.$tagName$ + \"-\" + mode : cmp.$tagName$);\n\n// src/runtime/vdom/vdom-render.ts\nimport { BUILD as BUILD16 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/update-element.ts\nimport { BUILD as BUILD15 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/vdom/set-accessor.ts\nimport { BUILD as BUILD14 } from \"@stencil/core/internal/app-data\";\nvar setAccessor = (elm, memberName, oldValue, newValue, isSvg, flags) => {\n  if (oldValue !== newValue) {\n    let isProp = isMemberInElement(elm, memberName);\n    let ln = memberName.toLowerCase();\n    if (BUILD14.vdomClass && memberName === \"class\") {\n      const classList = elm.classList;\n      const oldClasses = parseClassList(oldValue);\n      const newClasses = parseClassList(newValue);\n      classList.remove(...oldClasses.filter((c) => c && !newClasses.includes(c)));\n      classList.add(...newClasses.filter((c) => c && !oldClasses.includes(c)));\n    } else if (BUILD14.vdomStyle && memberName === \"style\") {\n      if (BUILD14.updatable) {\n        for (const prop in oldValue) {\n          if (!newValue || newValue[prop] == null) {\n            if (!BUILD14.hydrateServerSide && prop.includes(\"-\")) {\n              elm.style.removeProperty(prop);\n            } else {\n              elm.style[prop] = \"\";\n            }\n          }\n        }\n      }\n      for (const prop in newValue) {\n        if (!oldValue || newValue[prop] !== oldValue[prop]) {\n          if (!BUILD14.hydrateServerSide && prop.includes(\"-\")) {\n            elm.style.setProperty(prop, newValue[prop]);\n          } else {\n            elm.style[prop] = newValue[prop];\n          }\n        }\n      }\n    } else if (BUILD14.vdomKey && memberName === \"key\") {\n    } else if (BUILD14.vdomRef && memberName === \"ref\") {\n      if (newValue) {\n        newValue(elm);\n      }\n    } else if (BUILD14.vdomListener && (BUILD14.lazyLoad ? !isProp : !elm.__lookupSetter__(memberName)) && memberName[0] === \"o\" && memberName[1] === \"n\") {\n      if (memberName[2] === \"-\") {\n        memberName = memberName.slice(3);\n      } else if (isMemberInElement(win, ln)) {\n        memberName = ln.slice(2);\n      } else {\n        memberName = ln[2] + memberName.slice(3);\n      }\n      if (oldValue || newValue) {\n        const capture = memberName.endsWith(CAPTURE_EVENT_SUFFIX);\n        memberName = memberName.replace(CAPTURE_EVENT_REGEX, \"\");\n        if (oldValue) {\n          plt.rel(elm, memberName, oldValue, capture);\n        }\n        if (newValue) {\n          plt.ael(elm, memberName, newValue, capture);\n        }\n      }\n    } else if (BUILD14.vdomPropOrAttr) {\n      const isComplex = isComplexType(newValue);\n      if ((isProp || isComplex && newValue !== null) && !isSvg) {\n        try {\n          if (!elm.tagName.includes(\"-\")) {\n            const n = newValue == null ? \"\" : newValue;\n            if (memberName === \"list\") {\n              isProp = false;\n            } else if (oldValue == null || elm[memberName] != n) {\n              elm[memberName] = n;\n            }\n          } else {\n            elm[memberName] = newValue;\n          }\n        } catch (e) {\n        }\n      }\n      let xlink = false;\n      if (BUILD14.vdomXlink) {\n        if (ln !== (ln = ln.replace(/^xlink\\:?/, \"\"))) {\n          memberName = ln;\n          xlink = true;\n        }\n      }\n      if (newValue == null || newValue === false) {\n        if (newValue !== false || elm.getAttribute(memberName) === \"\") {\n          if (BUILD14.vdomXlink && xlink) {\n            elm.removeAttributeNS(XLINK_NS, memberName);\n          } else {\n            elm.removeAttribute(memberName);\n          }\n        }\n      } else if ((!isProp || flags & 4 /* isHost */ || isSvg) && !isComplex) {\n        newValue = newValue === true ? \"\" : newValue;\n        if (BUILD14.vdomXlink && xlink) {\n          elm.setAttributeNS(XLINK_NS, memberName, newValue);\n        } else {\n          elm.setAttribute(memberName, newValue);\n        }\n      }\n    }\n  }\n};\nvar parseClassListRegex = /\\s/;\nvar parseClassList = (value) => !value ? [] : value.split(parseClassListRegex);\nvar CAPTURE_EVENT_SUFFIX = \"Capture\";\nvar CAPTURE_EVENT_REGEX = new RegExp(CAPTURE_EVENT_SUFFIX + \"$\");\n\n// src/runtime/vdom/update-element.ts\nvar updateElement = (oldVnode, newVnode, isSvgMode2) => {\n  const elm = newVnode.$elm$.nodeType === 11 /* DocumentFragment */ && newVnode.$elm$.host ? newVnode.$elm$.host : newVnode.$elm$;\n  const oldVnodeAttrs = oldVnode && oldVnode.$attrs$ || EMPTY_OBJ;\n  const newVnodeAttrs = newVnode.$attrs$ || EMPTY_OBJ;\n  if (BUILD15.updatable) {\n    for (const memberName of sortedAttrNames(Object.keys(oldVnodeAttrs))) {\n      if (!(memberName in newVnodeAttrs)) {\n        setAccessor(elm, memberName, oldVnodeAttrs[memberName], void 0, isSvgMode2, newVnode.$flags$);\n      }\n    }\n  }\n  for (const memberName of sortedAttrNames(Object.keys(newVnodeAttrs))) {\n    setAccessor(elm, memberName, oldVnodeAttrs[memberName], newVnodeAttrs[memberName], isSvgMode2, newVnode.$flags$);\n  }\n};\nfunction sortedAttrNames(attrNames) {\n  return attrNames.includes(\"ref\") ? (\n    // we need to sort these to ensure that `'ref'` is the last attr\n    [...attrNames.filter((attr) => attr !== \"ref\"), \"ref\"]\n  ) : (\n    // no need to sort, return the original array\n    attrNames\n  );\n}\n\n// src/runtime/vdom/vdom-render.ts\nvar scopeId;\nvar contentRef;\nvar hostTagName;\nvar useNativeShadowDom = false;\nvar checkSlotFallbackVisibility = false;\nvar checkSlotRelocate = false;\nvar isSvgMode = false;\nvar createElm = (oldParentVNode, newParentVNode, childIndex, parentElm) => {\n  var _a;\n  const newVNode2 = newParentVNode.$children$[childIndex];\n  let i2 = 0;\n  let elm;\n  let childNode;\n  let oldVNode;\n  if (BUILD16.slotRelocation && !useNativeShadowDom) {\n    checkSlotRelocate = true;\n    if (newVNode2.$tag$ === \"slot\") {\n      if (scopeId) {\n        parentElm.classList.add(scopeId + \"-s\");\n      }\n      newVNode2.$flags$ |= newVNode2.$children$ ? (\n        // slot element has fallback content\n        // still create an element that \"mocks\" the slot element\n        2 /* isSlotFallback */\n      ) : (\n        // slot element does not have fallback content\n        // create an html comment we'll use to always reference\n        // where actual slot content should sit next to\n        1 /* isSlotReference */\n      );\n    }\n  }\n  if (BUILD16.isDev && newVNode2.$elm$) {\n    consoleDevError(\n      `The JSX ${newVNode2.$text$ !== null ? `\"${newVNode2.$text$}\" text` : `\"${newVNode2.$tag$}\" element`} node should not be shared within the same renderer. The renderer caches element lookups in order to improve performance. However, a side effect from this is that the exact same JSX node should not be reused. For more information please see https://stenciljs.com/docs/templating-jsx#avoid-shared-jsx-nodes`\n    );\n  }\n  if (BUILD16.vdomText && newVNode2.$text$ !== null) {\n    elm = newVNode2.$elm$ = doc.createTextNode(newVNode2.$text$);\n  } else if (BUILD16.slotRelocation && newVNode2.$flags$ & 1 /* isSlotReference */) {\n    elm = newVNode2.$elm$ = BUILD16.isDebug || BUILD16.hydrateServerSide ? slotReferenceDebugNode(newVNode2) : doc.createTextNode(\"\");\n  } else {\n    if (BUILD16.svg && !isSvgMode) {\n      isSvgMode = newVNode2.$tag$ === \"svg\";\n    }\n    elm = newVNode2.$elm$ = BUILD16.svg ? doc.createElementNS(\n      isSvgMode ? SVG_NS : HTML_NS,\n      !useNativeShadowDom && BUILD16.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$\n    ) : doc.createElement(\n      !useNativeShadowDom && BUILD16.slotRelocation && newVNode2.$flags$ & 2 /* isSlotFallback */ ? \"slot-fb\" : newVNode2.$tag$\n    );\n    if (BUILD16.svg && isSvgMode && newVNode2.$tag$ === \"foreignObject\") {\n      isSvgMode = false;\n    }\n    if (BUILD16.vdomAttribute) {\n      updateElement(null, newVNode2, isSvgMode);\n    }\n    const rootNode = elm.getRootNode();\n    const isElementWithinShadowRoot = !rootNode.querySelector(\"body\");\n    if (!isElementWithinShadowRoot && BUILD16.scoped && isDef(scopeId) && elm[\"s-si\"] !== scopeId) {\n      elm.classList.add(elm[\"s-si\"] = scopeId);\n    }\n    if (BUILD16.scoped) {\n      updateElementScopeIds(elm, parentElm);\n    }\n    if (newVNode2.$children$) {\n      for (i2 = 0; i2 < newVNode2.$children$.length; ++i2) {\n        childNode = createElm(oldParentVNode, newVNode2, i2, elm);\n        if (childNode) {\n          elm.appendChild(childNode);\n        }\n      }\n    }\n    if (BUILD16.svg) {\n      if (newVNode2.$tag$ === \"svg\") {\n        isSvgMode = false;\n      } else if (elm.tagName === \"foreignObject\") {\n        isSvgMode = true;\n      }\n    }\n  }\n  elm[\"s-hn\"] = hostTagName;\n  if (BUILD16.slotRelocation) {\n    if (newVNode2.$flags$ & (2 /* isSlotFallback */ | 1 /* isSlotReference */)) {\n      elm[\"s-sr\"] = true;\n      elm[\"s-cr\"] = contentRef;\n      elm[\"s-sn\"] = newVNode2.$name$ || \"\";\n      elm[\"s-rf\"] = (_a = newVNode2.$attrs$) == null ? void 0 : _a.ref;\n      oldVNode = oldParentVNode && oldParentVNode.$children$ && oldParentVNode.$children$[childIndex];\n      if (oldVNode && oldVNode.$tag$ === newVNode2.$tag$ && oldParentVNode.$elm$) {\n        if (BUILD16.experimentalSlotFixes) {\n          relocateToHostRoot(oldParentVNode.$elm$);\n        } else {\n          putBackInOriginalLocation(oldParentVNode.$elm$, false);\n        }\n      }\n    }\n  }\n  return elm;\n};\nvar relocateToHostRoot = (parentElm) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const host = parentElm.closest(hostTagName.toLowerCase());\n  if (host != null) {\n    const contentRefNode = Array.from(host.childNodes).find((ref) => ref[\"s-cr\"]);\n    const childNodeArray = Array.from(parentElm.childNodes);\n    for (const childNode of contentRefNode ? childNodeArray.reverse() : childNodeArray) {\n      if (childNode[\"s-sh\"] != null) {\n        insertBefore(host, childNode, contentRefNode != null ? contentRefNode : null);\n        childNode[\"s-sh\"] = void 0;\n        checkSlotRelocate = true;\n      }\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar putBackInOriginalLocation = (parentElm, recursive) => {\n  plt.$flags$ |= 1 /* isTmpDisconnected */;\n  const oldSlotChildNodes = Array.from(parentElm.childNodes);\n  if (parentElm[\"s-sr\"] && BUILD16.experimentalSlotFixes) {\n    let node = parentElm;\n    while (node = node.nextSibling) {\n      if (node && node[\"s-sn\"] === parentElm[\"s-sn\"] && node[\"s-sh\"] === hostTagName) {\n        oldSlotChildNodes.push(node);\n      }\n    }\n  }\n  for (let i2 = oldSlotChildNodes.length - 1; i2 >= 0; i2--) {\n    const childNode = oldSlotChildNodes[i2];\n    if (childNode[\"s-hn\"] !== hostTagName && childNode[\"s-ol\"]) {\n      insertBefore(parentReferenceNode(childNode), childNode, referenceNode(childNode));\n      childNode[\"s-ol\"].remove();\n      childNode[\"s-ol\"] = void 0;\n      childNode[\"s-sh\"] = void 0;\n      checkSlotRelocate = true;\n    }\n    if (recursive) {\n      putBackInOriginalLocation(childNode, recursive);\n    }\n  }\n  plt.$flags$ &= ~1 /* isTmpDisconnected */;\n};\nvar addVnodes = (parentElm, before, parentVNode, vnodes, startIdx, endIdx) => {\n  let containerElm = BUILD16.slotRelocation && parentElm[\"s-cr\"] && parentElm[\"s-cr\"].parentNode || parentElm;\n  let childNode;\n  if (BUILD16.shadowDom && containerElm.shadowRoot && containerElm.tagName === hostTagName) {\n    containerElm = containerElm.shadowRoot;\n  }\n  for (; startIdx <= endIdx; ++startIdx) {\n    if (vnodes[startIdx]) {\n      childNode = createElm(null, parentVNode, startIdx, parentElm);\n      if (childNode) {\n        vnodes[startIdx].$elm$ = childNode;\n        insertBefore(containerElm, childNode, BUILD16.slotRelocation ? referenceNode(before) : before);\n      }\n    }\n  }\n};\nvar removeVnodes = (vnodes, startIdx, endIdx) => {\n  for (let index = startIdx; index <= endIdx; ++index) {\n    const vnode = vnodes[index];\n    if (vnode) {\n      const elm = vnode.$elm$;\n      nullifyVNodeRefs(vnode);\n      if (elm) {\n        if (BUILD16.slotRelocation) {\n          checkSlotFallbackVisibility = true;\n          if (elm[\"s-ol\"]) {\n            elm[\"s-ol\"].remove();\n          } else {\n            putBackInOriginalLocation(elm, true);\n          }\n        }\n        elm.remove();\n      }\n    }\n  }\n};\nvar updateChildren = (parentElm, oldCh, newVNode2, newCh, isInitialRender = false) => {\n  let oldStartIdx = 0;\n  let newStartIdx = 0;\n  let idxInOld = 0;\n  let i2 = 0;\n  let oldEndIdx = oldCh.length - 1;\n  let oldStartVnode = oldCh[0];\n  let oldEndVnode = oldCh[oldEndIdx];\n  let newEndIdx = newCh.length - 1;\n  let newStartVnode = newCh[0];\n  let newEndVnode = newCh[newEndIdx];\n  let node;\n  let elmToMove;\n  while (oldStartIdx <= oldEndIdx && newStartIdx <= newEndIdx) {\n    if (oldStartVnode == null) {\n      oldStartVnode = oldCh[++oldStartIdx];\n    } else if (oldEndVnode == null) {\n      oldEndVnode = oldCh[--oldEndIdx];\n    } else if (newStartVnode == null) {\n      newStartVnode = newCh[++newStartIdx];\n    } else if (newEndVnode == null) {\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newStartVnode, isInitialRender)) {\n      patch(oldStartVnode, newStartVnode, isInitialRender);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else if (isSameVnode(oldEndVnode, newEndVnode, isInitialRender)) {\n      patch(oldEndVnode, newEndVnode, isInitialRender);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldStartVnode, newEndVnode, isInitialRender)) {\n      if (BUILD16.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldStartVnode.$elm$.parentNode, false);\n      }\n      patch(oldStartVnode, newEndVnode, isInitialRender);\n      insertBefore(parentElm, oldStartVnode.$elm$, oldEndVnode.$elm$.nextSibling);\n      oldStartVnode = oldCh[++oldStartIdx];\n      newEndVnode = newCh[--newEndIdx];\n    } else if (isSameVnode(oldEndVnode, newStartVnode, isInitialRender)) {\n      if (BUILD16.slotRelocation && (oldStartVnode.$tag$ === \"slot\" || newEndVnode.$tag$ === \"slot\")) {\n        putBackInOriginalLocation(oldEndVnode.$elm$.parentNode, false);\n      }\n      patch(oldEndVnode, newStartVnode, isInitialRender);\n      insertBefore(parentElm, oldEndVnode.$elm$, oldStartVnode.$elm$);\n      oldEndVnode = oldCh[--oldEndIdx];\n      newStartVnode = newCh[++newStartIdx];\n    } else {\n      idxInOld = -1;\n      if (BUILD16.vdomKey) {\n        for (i2 = oldStartIdx; i2 <= oldEndIdx; ++i2) {\n          if (oldCh[i2] && oldCh[i2].$key$ !== null && oldCh[i2].$key$ === newStartVnode.$key$) {\n            idxInOld = i2;\n            break;\n          }\n        }\n      }\n      if (BUILD16.vdomKey && idxInOld >= 0) {\n        elmToMove = oldCh[idxInOld];\n        if (elmToMove.$tag$ !== newStartVnode.$tag$) {\n          node = createElm(oldCh && oldCh[newStartIdx], newVNode2, idxInOld, parentElm);\n        } else {\n          patch(elmToMove, newStartVnode, isInitialRender);\n          oldCh[idxInOld] = void 0;\n          node = elmToMove.$elm$;\n        }\n        newStartVnode = newCh[++newStartIdx];\n      } else {\n        node = createElm(oldCh && oldCh[newStartIdx], newVNode2, newStartIdx, parentElm);\n        newStartVnode = newCh[++newStartIdx];\n      }\n      if (node) {\n        if (BUILD16.slotRelocation) {\n          insertBefore(parentReferenceNode(oldStartVnode.$elm$), node, referenceNode(oldStartVnode.$elm$));\n        } else {\n          insertBefore(oldStartVnode.$elm$.parentNode, node, oldStartVnode.$elm$);\n        }\n      }\n    }\n  }\n  if (oldStartIdx > oldEndIdx) {\n    addVnodes(\n      parentElm,\n      newCh[newEndIdx + 1] == null ? null : newCh[newEndIdx + 1].$elm$,\n      newVNode2,\n      newCh,\n      newStartIdx,\n      newEndIdx\n    );\n  } else if (BUILD16.updatable && newStartIdx > newEndIdx) {\n    removeVnodes(oldCh, oldStartIdx, oldEndIdx);\n  }\n};\nvar isSameVnode = (leftVNode, rightVNode, isInitialRender = false) => {\n  if (leftVNode.$tag$ === rightVNode.$tag$) {\n    if (BUILD16.slotRelocation && leftVNode.$tag$ === \"slot\") {\n      if (\n        // The component gets hydrated and no VDOM has been initialized.\n        // Here the comparison can't happen as $name$ property is not set for `leftNode`.\n        \"$nodeId$\" in leftVNode && isInitialRender && // `leftNode` is not from type HTMLComment which would cause many\n        // hydration comments to be removed\n        leftVNode.$elm$.nodeType !== 8\n      ) {\n        return false;\n      }\n      return leftVNode.$name$ === rightVNode.$name$;\n    }\n    if (BUILD16.vdomKey && !isInitialRender) {\n      return leftVNode.$key$ === rightVNode.$key$;\n    }\n    return true;\n  }\n  return false;\n};\nvar referenceNode = (node) => {\n  return node && node[\"s-ol\"] || node;\n};\nvar parentReferenceNode = (node) => (node[\"s-ol\"] ? node[\"s-ol\"] : node).parentNode;\nvar patch = (oldVNode, newVNode2, isInitialRender = false) => {\n  const elm = newVNode2.$elm$ = oldVNode.$elm$;\n  const oldChildren = oldVNode.$children$;\n  const newChildren = newVNode2.$children$;\n  const tag = newVNode2.$tag$;\n  const text = newVNode2.$text$;\n  let defaultHolder;\n  if (!BUILD16.vdomText || text === null) {\n    if (BUILD16.svg) {\n      isSvgMode = tag === \"svg\" ? true : tag === \"foreignObject\" ? false : isSvgMode;\n    }\n    if (BUILD16.vdomAttribute || BUILD16.reflect) {\n      if (BUILD16.slot && tag === \"slot\" && !useNativeShadowDom) {\n        if (BUILD16.experimentalSlotFixes && oldVNode.$name$ !== newVNode2.$name$) {\n          newVNode2.$elm$[\"s-sn\"] = newVNode2.$name$ || \"\";\n          relocateToHostRoot(newVNode2.$elm$.parentElement);\n        }\n      } else {\n        updateElement(oldVNode, newVNode2, isSvgMode);\n      }\n    }\n    if (BUILD16.updatable && oldChildren !== null && newChildren !== null) {\n      updateChildren(elm, oldChildren, newVNode2, newChildren, isInitialRender);\n    } else if (newChildren !== null) {\n      if (BUILD16.updatable && BUILD16.vdomText && oldVNode.$text$ !== null) {\n        elm.textContent = \"\";\n      }\n      addVnodes(elm, null, newVNode2, newChildren, 0, newChildren.length - 1);\n    } else if (\n      // don't do this on initial render as it can cause non-hydrated content to be removed\n      !isInitialRender && BUILD16.updatable && oldChildren !== null\n    ) {\n      removeVnodes(oldChildren, 0, oldChildren.length - 1);\n    }\n    if (BUILD16.svg && isSvgMode && tag === \"svg\") {\n      isSvgMode = false;\n    }\n  } else if (BUILD16.vdomText && BUILD16.slotRelocation && (defaultHolder = elm[\"s-cr\"])) {\n    defaultHolder.parentNode.textContent = text;\n  } else if (BUILD16.vdomText && oldVNode.$text$ !== text) {\n    elm.data = text;\n  }\n};\nvar updateFallbackSlotVisibility = (elm) => {\n  const childNodes = elm.childNodes;\n  for (const childNode of childNodes) {\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      if (childNode[\"s-sr\"]) {\n        const slotName = childNode[\"s-sn\"];\n        childNode.hidden = false;\n        for (const siblingNode of childNodes) {\n          if (siblingNode !== childNode) {\n            if (siblingNode[\"s-hn\"] !== childNode[\"s-hn\"] || slotName !== \"\") {\n              if (siblingNode.nodeType === 1 /* ElementNode */ && (slotName === siblingNode.getAttribute(\"slot\") || slotName === siblingNode[\"s-sn\"]) || siblingNode.nodeType === 3 /* TextNode */ && slotName === siblingNode[\"s-sn\"]) {\n                childNode.hidden = true;\n                break;\n              }\n            } else {\n              if (siblingNode.nodeType === 1 /* ElementNode */ || siblingNode.nodeType === 3 /* TextNode */ && siblingNode.textContent.trim() !== \"\") {\n                childNode.hidden = true;\n                break;\n              }\n            }\n          }\n        }\n      }\n      updateFallbackSlotVisibility(childNode);\n    }\n  }\n};\nvar relocateNodes = [];\nvar markSlotContentForRelocation = (elm) => {\n  let node;\n  let hostContentNodes;\n  let j;\n  for (const childNode of elm.childNodes) {\n    if (childNode[\"s-sr\"] && (node = childNode[\"s-cr\"]) && node.parentNode) {\n      hostContentNodes = node.parentNode.childNodes;\n      const slotName = childNode[\"s-sn\"];\n      for (j = hostContentNodes.length - 1; j >= 0; j--) {\n        node = hostContentNodes[j];\n        if (!node[\"s-cn\"] && !node[\"s-nr\"] && node[\"s-hn\"] !== childNode[\"s-hn\"] && (!BUILD16.experimentalSlotFixes || !node[\"s-sh\"] || node[\"s-sh\"] !== childNode[\"s-hn\"])) {\n          if (isNodeLocatedInSlot(node, slotName)) {\n            let relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n            checkSlotFallbackVisibility = true;\n            node[\"s-sn\"] = node[\"s-sn\"] || slotName;\n            if (relocateNodeData) {\n              relocateNodeData.$nodeToRelocate$[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodeData.$slotRefNode$ = childNode;\n            } else {\n              node[\"s-sh\"] = childNode[\"s-hn\"];\n              relocateNodes.push({\n                $slotRefNode$: childNode,\n                $nodeToRelocate$: node\n              });\n            }\n            if (node[\"s-sr\"]) {\n              relocateNodes.map((relocateNode) => {\n                if (isNodeLocatedInSlot(relocateNode.$nodeToRelocate$, node[\"s-sn\"])) {\n                  relocateNodeData = relocateNodes.find((r) => r.$nodeToRelocate$ === node);\n                  if (relocateNodeData && !relocateNode.$slotRefNode$) {\n                    relocateNode.$slotRefNode$ = relocateNodeData.$slotRefNode$;\n                  }\n                }\n              });\n            }\n          } else if (!relocateNodes.some((r) => r.$nodeToRelocate$ === node)) {\n            relocateNodes.push({\n              $nodeToRelocate$: node\n            });\n          }\n        }\n      }\n    }\n    if (childNode.nodeType === 1 /* ElementNode */) {\n      markSlotContentForRelocation(childNode);\n    }\n  }\n};\nvar isNodeLocatedInSlot = (nodeToRelocate, slotName) => {\n  if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n    if (nodeToRelocate.getAttribute(\"slot\") === null && slotName === \"\") {\n      return true;\n    }\n    if (nodeToRelocate.getAttribute(\"slot\") === slotName) {\n      return true;\n    }\n    return false;\n  }\n  if (nodeToRelocate[\"s-sn\"] === slotName) {\n    return true;\n  }\n  return slotName === \"\";\n};\nvar nullifyVNodeRefs = (vNode) => {\n  if (BUILD16.vdomRef) {\n    vNode.$attrs$ && vNode.$attrs$.ref && vNode.$attrs$.ref(null);\n    vNode.$children$ && vNode.$children$.map(nullifyVNodeRefs);\n  }\n};\nvar insertBefore = (parent, newNode, reference) => {\n  const inserted = parent == null ? void 0 : parent.insertBefore(newNode, reference);\n  if (BUILD16.scoped) {\n    updateElementScopeIds(newNode, parent);\n  }\n  return inserted;\n};\nvar findScopeIds = (element) => {\n  const scopeIds = [];\n  if (element) {\n    scopeIds.push(\n      ...element[\"s-scs\"] || [],\n      element[\"s-si\"],\n      element[\"s-sc\"],\n      ...findScopeIds(element.parentElement)\n    );\n  }\n  return scopeIds;\n};\nvar updateElementScopeIds = (element, parent, iterateChildNodes = false) => {\n  var _a;\n  if (element && parent && element.nodeType === 1 /* ElementNode */) {\n    const scopeIds = new Set(findScopeIds(parent).filter(Boolean));\n    if (scopeIds.size) {\n      (_a = element.classList) == null ? void 0 : _a.add(...element[\"s-scs\"] = [...scopeIds]);\n      if (element[\"s-ol\"] || iterateChildNodes) {\n        for (const childNode of Array.from(element.childNodes)) {\n          updateElementScopeIds(childNode, element, true);\n        }\n      }\n    }\n  }\n};\nvar renderVdom = (hostRef, renderFnResults, isInitialLoad = false) => {\n  var _a, _b, _c, _d, _e;\n  const hostElm = hostRef.$hostElement$;\n  const cmpMeta = hostRef.$cmpMeta$;\n  const oldVNode = hostRef.$vnode$ || newVNode(null, null);\n  const rootVnode = isHost(renderFnResults) ? renderFnResults : h(null, null, renderFnResults);\n  hostTagName = hostElm.tagName;\n  if (BUILD16.isDev && Array.isArray(renderFnResults) && renderFnResults.some(isHost)) {\n    throw new Error(`The <Host> must be the single root component.\nLooks like the render() function of \"${hostTagName.toLowerCase()}\" is returning an array that contains the <Host>.\n\nThe render() function should look like this instead:\n\nrender() {\n  // Do not return an array\n  return (\n    <Host>{content}</Host>\n  );\n}\n  `);\n  }\n  if (BUILD16.reflect && cmpMeta.$attrsToReflect$) {\n    rootVnode.$attrs$ = rootVnode.$attrs$ || {};\n    cmpMeta.$attrsToReflect$.map(\n      ([propName, attribute]) => rootVnode.$attrs$[attribute] = hostElm[propName]\n    );\n  }\n  if (isInitialLoad && rootVnode.$attrs$) {\n    for (const key of Object.keys(rootVnode.$attrs$)) {\n      if (hostElm.hasAttribute(key) && ![\"key\", \"ref\", \"style\", \"class\"].includes(key)) {\n        rootVnode.$attrs$[key] = hostElm[key];\n      }\n    }\n  }\n  rootVnode.$tag$ = null;\n  rootVnode.$flags$ |= 4 /* isHost */;\n  hostRef.$vnode$ = rootVnode;\n  rootVnode.$elm$ = oldVNode.$elm$ = BUILD16.shadowDom ? hostElm.shadowRoot || hostElm : hostElm;\n  if (BUILD16.scoped || BUILD16.shadowDom) {\n    scopeId = hostElm[\"s-sc\"];\n  }\n  useNativeShadowDom = supportsShadow && (cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) !== 0;\n  if (BUILD16.slotRelocation) {\n    contentRef = hostElm[\"s-cr\"];\n    checkSlotFallbackVisibility = false;\n  }\n  patch(oldVNode, rootVnode, isInitialLoad);\n  if (BUILD16.slotRelocation) {\n    plt.$flags$ |= 1 /* isTmpDisconnected */;\n    if (checkSlotRelocate) {\n      markSlotContentForRelocation(rootVnode.$elm$);\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        if (!nodeToRelocate[\"s-ol\"]) {\n          const orgLocationNode = BUILD16.isDebug || BUILD16.hydrateServerSide ? originalLocationDebugNode(nodeToRelocate) : doc.createTextNode(\"\");\n          orgLocationNode[\"s-nr\"] = nodeToRelocate;\n          insertBefore(nodeToRelocate.parentNode, nodeToRelocate[\"s-ol\"] = orgLocationNode, nodeToRelocate);\n        }\n      }\n      for (const relocateData of relocateNodes) {\n        const nodeToRelocate = relocateData.$nodeToRelocate$;\n        const slotRefNode = relocateData.$slotRefNode$;\n        if (slotRefNode) {\n          const parentNodeRef = slotRefNode.parentNode;\n          let insertBeforeNode = slotRefNode.nextSibling;\n          if (!BUILD16.experimentalSlotFixes || insertBeforeNode && insertBeforeNode.nodeType === 1 /* ElementNode */) {\n            let orgLocationNode = (_a = nodeToRelocate[\"s-ol\"]) == null ? void 0 : _a.previousSibling;\n            while (orgLocationNode) {\n              let refNode = (_b = orgLocationNode[\"s-nr\"]) != null ? _b : null;\n              if (refNode && refNode[\"s-sn\"] === nodeToRelocate[\"s-sn\"] && parentNodeRef === refNode.parentNode) {\n                refNode = refNode.nextSibling;\n                while (refNode === nodeToRelocate || (refNode == null ? void 0 : refNode[\"s-sr\"])) {\n                  refNode = refNode == null ? void 0 : refNode.nextSibling;\n                }\n                if (!refNode || !refNode[\"s-nr\"]) {\n                  insertBeforeNode = refNode;\n                  break;\n                }\n              }\n              orgLocationNode = orgLocationNode.previousSibling;\n            }\n          }\n          if (!insertBeforeNode && parentNodeRef !== nodeToRelocate.parentNode || nodeToRelocate.nextSibling !== insertBeforeNode) {\n            if (nodeToRelocate !== insertBeforeNode) {\n              if (!BUILD16.experimentalSlotFixes && !nodeToRelocate[\"s-hn\"] && nodeToRelocate[\"s-ol\"]) {\n                nodeToRelocate[\"s-hn\"] = nodeToRelocate[\"s-ol\"].parentNode.nodeName;\n              }\n              insertBefore(parentNodeRef, nodeToRelocate, insertBeforeNode);\n              if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n                nodeToRelocate.hidden = (_c = nodeToRelocate[\"s-ih\"]) != null ? _c : false;\n              }\n            }\n          }\n          nodeToRelocate && typeof slotRefNode[\"s-rf\"] === \"function\" && slotRefNode[\"s-rf\"](nodeToRelocate);\n        } else {\n          if (nodeToRelocate.nodeType === 1 /* ElementNode */) {\n            if (isInitialLoad) {\n              nodeToRelocate[\"s-ih\"] = (_d = nodeToRelocate.hidden) != null ? _d : false;\n            }\n            nodeToRelocate.hidden = true;\n          }\n        }\n      }\n    }\n    if (checkSlotFallbackVisibility) {\n      updateFallbackSlotVisibility(rootVnode.$elm$);\n    }\n    plt.$flags$ &= ~1 /* isTmpDisconnected */;\n    relocateNodes.length = 0;\n  }\n  if (BUILD16.experimentalScopedSlotChanges && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n    for (const childNode of rootVnode.$elm$.childNodes) {\n      if (childNode[\"s-hn\"] !== hostTagName && !childNode[\"s-sh\"]) {\n        if (isInitialLoad && childNode[\"s-ih\"] == null) {\n          childNode[\"s-ih\"] = (_e = childNode.hidden) != null ? _e : false;\n        }\n        childNode.hidden = true;\n      }\n    }\n  }\n  contentRef = void 0;\n};\nvar slotReferenceDebugNode = (slotVNode) => doc.createComment(\n  `<slot${slotVNode.$name$ ? ' name=\"' + slotVNode.$name$ + '\"' : \"\"}> (host=${hostTagName.toLowerCase()})`\n);\nvar originalLocationDebugNode = (nodeToRelocate) => doc.createComment(\n  `org-location for ` + (nodeToRelocate.localName ? `<${nodeToRelocate.localName}> (host=${nodeToRelocate[\"s-hn\"]})` : `[${nodeToRelocate.textContent}]`)\n);\n\n// src/runtime/update-component.ts\nvar attachToAncestor = (hostRef, ancestorComponent) => {\n  if (BUILD17.asyncLoading && ancestorComponent && !hostRef.$onRenderResolve$ && ancestorComponent[\"s-p\"]) {\n    ancestorComponent[\"s-p\"].push(new Promise((r) => hostRef.$onRenderResolve$ = r));\n  }\n};\nvar scheduleUpdate = (hostRef, isInitialLoad) => {\n  if (BUILD17.taskQueue && BUILD17.updatable) {\n    hostRef.$flags$ |= 16 /* isQueuedForUpdate */;\n  }\n  if (BUILD17.asyncLoading && hostRef.$flags$ & 4 /* isWaitingForChildren */) {\n    hostRef.$flags$ |= 512 /* needsRerender */;\n    return;\n  }\n  attachToAncestor(hostRef, hostRef.$ancestorComponent$);\n  const dispatch = () => dispatchHooks(hostRef, isInitialLoad);\n  return BUILD17.taskQueue ? writeTask(dispatch) : dispatch();\n};\nvar dispatchHooks = (hostRef, isInitialLoad) => {\n  const elm = hostRef.$hostElement$;\n  const endSchedule = createTime(\"scheduleUpdate\", hostRef.$cmpMeta$.$tagName$);\n  const instance = BUILD17.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  if (!instance) {\n    throw new Error(\n      `Can't render component <${elm.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \\`externalRuntime: true\\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`\n    );\n  }\n  let maybePromise;\n  if (isInitialLoad) {\n    if (BUILD17.lazyLoad && BUILD17.hostListener) {\n      hostRef.$flags$ |= 256 /* isListenReady */;\n      if (hostRef.$queuedListeners$) {\n        hostRef.$queuedListeners$.map(([methodName, event]) => safeCall(instance, methodName, event));\n        hostRef.$queuedListeners$ = void 0;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentWillLoad\");\n    if (BUILD17.cmpWillLoad) {\n      maybePromise = safeCall(instance, \"componentWillLoad\");\n    }\n  } else {\n    emitLifecycleEvent(elm, \"componentWillUpdate\");\n    if (BUILD17.cmpWillUpdate) {\n      maybePromise = safeCall(instance, \"componentWillUpdate\");\n    }\n  }\n  emitLifecycleEvent(elm, \"componentWillRender\");\n  if (BUILD17.cmpWillRender) {\n    maybePromise = enqueue(maybePromise, () => safeCall(instance, \"componentWillRender\"));\n  }\n  endSchedule();\n  return enqueue(maybePromise, () => updateComponent(hostRef, instance, isInitialLoad));\n};\nvar enqueue = (maybePromise, fn) => isPromisey(maybePromise) ? maybePromise.then(fn).catch((err2) => {\n  console.error(err2);\n  fn();\n}) : fn();\nvar isPromisey = (maybePromise) => maybePromise instanceof Promise || maybePromise && maybePromise.then && typeof maybePromise.then === \"function\";\nvar updateComponent = async (hostRef, instance, isInitialLoad) => {\n  var _a;\n  const elm = hostRef.$hostElement$;\n  const endUpdate = createTime(\"update\", hostRef.$cmpMeta$.$tagName$);\n  const rc = elm[\"s-rc\"];\n  if (BUILD17.style && isInitialLoad) {\n    attachStyles(hostRef);\n  }\n  const endRender = createTime(\"render\", hostRef.$cmpMeta$.$tagName$);\n  if (BUILD17.isDev) {\n    hostRef.$flags$ |= 1024 /* devOnRender */;\n  }\n  if (BUILD17.hydrateServerSide) {\n    await callRender(hostRef, instance, elm, isInitialLoad);\n  } else {\n    callRender(hostRef, instance, elm, isInitialLoad);\n  }\n  if (BUILD17.isDev) {\n    hostRef.$renderCount$ = hostRef.$renderCount$ === void 0 ? 1 : hostRef.$renderCount$ + 1;\n    hostRef.$flags$ &= ~1024 /* devOnRender */;\n  }\n  if (BUILD17.hydrateServerSide) {\n    try {\n      serverSideConnected(elm);\n      if (isInitialLoad) {\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          elm[\"s-en\"] = \"\";\n        } else if (hostRef.$cmpMeta$.$flags$ & 2 /* scopedCssEncapsulation */) {\n          elm[\"s-en\"] = \"c\";\n        }\n      }\n    } catch (e) {\n      consoleError(e, elm);\n    }\n  }\n  if (BUILD17.asyncLoading && rc) {\n    rc.map((cb) => cb());\n    elm[\"s-rc\"] = void 0;\n  }\n  endRender();\n  endUpdate();\n  if (BUILD17.asyncLoading) {\n    const childrenPromises = (_a = elm[\"s-p\"]) != null ? _a : [];\n    const postUpdate = () => postUpdateComponent(hostRef);\n    if (childrenPromises.length === 0) {\n      postUpdate();\n    } else {\n      Promise.all(childrenPromises).then(postUpdate);\n      hostRef.$flags$ |= 4 /* isWaitingForChildren */;\n      childrenPromises.length = 0;\n    }\n  } else {\n    postUpdateComponent(hostRef);\n  }\n};\nvar renderingRef = null;\nvar callRender = (hostRef, instance, elm, isInitialLoad) => {\n  const allRenderFn = BUILD17.allRenderFn ? true : false;\n  const lazyLoad = BUILD17.lazyLoad ? true : false;\n  const taskQueue = BUILD17.taskQueue ? true : false;\n  const updatable = BUILD17.updatable ? true : false;\n  try {\n    renderingRef = instance;\n    instance = allRenderFn ? instance.render() : instance.render && instance.render();\n    if (updatable && taskQueue) {\n      hostRef.$flags$ &= ~16 /* isQueuedForUpdate */;\n    }\n    if (updatable || lazyLoad) {\n      hostRef.$flags$ |= 2 /* hasRendered */;\n    }\n    if (BUILD17.hasRenderFn || BUILD17.reflect) {\n      if (BUILD17.vdomRender || BUILD17.reflect) {\n        if (BUILD17.hydrateServerSide) {\n          return Promise.resolve(instance).then((value) => renderVdom(hostRef, value, isInitialLoad));\n        } else {\n          renderVdom(hostRef, instance, isInitialLoad);\n        }\n      } else {\n        const shadowRoot = elm.shadowRoot;\n        if (hostRef.$cmpMeta$.$flags$ & 1 /* shadowDomEncapsulation */) {\n          shadowRoot.textContent = instance;\n        } else {\n          elm.textContent = instance;\n        }\n      }\n    }\n  } catch (e) {\n    consoleError(e, hostRef.$hostElement$);\n  }\n  renderingRef = null;\n  return null;\n};\nvar getRenderingRef = () => renderingRef;\nvar postUpdateComponent = (hostRef) => {\n  const tagName = hostRef.$cmpMeta$.$tagName$;\n  const elm = hostRef.$hostElement$;\n  const endPostUpdate = createTime(\"postUpdate\", tagName);\n  const instance = BUILD17.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  if (BUILD17.cmpDidRender) {\n    if (BUILD17.isDev) {\n      hostRef.$flags$ |= 1024 /* devOnRender */;\n    }\n    safeCall(instance, \"componentDidRender\");\n    if (BUILD17.isDev) {\n      hostRef.$flags$ &= ~1024 /* devOnRender */;\n    }\n  }\n  emitLifecycleEvent(elm, \"componentDidRender\");\n  if (!(hostRef.$flags$ & 64 /* hasLoadedComponent */)) {\n    hostRef.$flags$ |= 64 /* hasLoadedComponent */;\n    if (BUILD17.asyncLoading && BUILD17.cssAnnotations) {\n      addHydratedFlag(elm);\n    }\n    if (BUILD17.cmpDidLoad) {\n      if (BUILD17.isDev) {\n        hostRef.$flags$ |= 2048 /* devOnDidLoad */;\n      }\n      safeCall(instance, \"componentDidLoad\");\n      if (BUILD17.isDev) {\n        hostRef.$flags$ &= ~2048 /* devOnDidLoad */;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentDidLoad\");\n    endPostUpdate();\n    if (BUILD17.asyncLoading) {\n      hostRef.$onReadyResolve$(elm);\n      if (!ancestorComponent) {\n        appDidLoad(tagName);\n      }\n    }\n  } else {\n    if (BUILD17.cmpDidUpdate) {\n      if (BUILD17.isDev) {\n        hostRef.$flags$ |= 1024 /* devOnRender */;\n      }\n      safeCall(instance, \"componentDidUpdate\");\n      if (BUILD17.isDev) {\n        hostRef.$flags$ &= ~1024 /* devOnRender */;\n      }\n    }\n    emitLifecycleEvent(elm, \"componentDidUpdate\");\n    endPostUpdate();\n  }\n  if (BUILD17.method && BUILD17.lazyLoad) {\n    hostRef.$onInstanceResolve$(elm);\n  }\n  if (BUILD17.asyncLoading) {\n    if (hostRef.$onRenderResolve$) {\n      hostRef.$onRenderResolve$();\n      hostRef.$onRenderResolve$ = void 0;\n    }\n    if (hostRef.$flags$ & 512 /* needsRerender */) {\n      nextTick(() => scheduleUpdate(hostRef, false));\n    }\n    hostRef.$flags$ &= ~(4 /* isWaitingForChildren */ | 512 /* needsRerender */);\n  }\n};\nvar forceUpdate = (ref) => {\n  if (BUILD17.updatable && (Build.isBrowser || Build.isTesting)) {\n    const hostRef = getHostRef(ref);\n    const isConnected = hostRef.$hostElement$.isConnected;\n    if (isConnected && (hostRef.$flags$ & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n      scheduleUpdate(hostRef, false);\n    }\n    return isConnected;\n  }\n  return false;\n};\nvar appDidLoad = (who) => {\n  if (BUILD17.cssAnnotations) {\n    addHydratedFlag(doc.documentElement);\n  }\n  if (BUILD17.asyncQueue) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  nextTick(() => emitEvent(win, \"appload\", { detail: { namespace: NAMESPACE } }));\n  if (BUILD17.profile && performance.measure) {\n    performance.measure(`[Stencil] ${NAMESPACE} initial load (by ${who})`, \"st:app:start\");\n  }\n};\nvar safeCall = (instance, method, arg) => {\n  if (instance && instance[method]) {\n    try {\n      return instance[method](arg);\n    } catch (e) {\n      consoleError(e);\n    }\n  }\n  return void 0;\n};\nvar emitLifecycleEvent = (elm, lifecycleName) => {\n  if (BUILD17.lifecycleDOMEvents) {\n    emitEvent(elm, \"stencil_\" + lifecycleName, {\n      bubbles: true,\n      composed: true,\n      detail: {\n        namespace: NAMESPACE\n      }\n    });\n  }\n};\nvar addHydratedFlag = (elm) => {\n  var _a, _b;\n  return BUILD17.hydratedClass ? elm.classList.add((_a = BUILD17.hydratedSelectorName) != null ? _a : \"hydrated\") : BUILD17.hydratedAttribute ? elm.setAttribute((_b = BUILD17.hydratedSelectorName) != null ? _b : \"hydrated\", \"\") : void 0;\n};\nvar serverSideConnected = (elm) => {\n  const children = elm.children;\n  if (children != null) {\n    for (let i2 = 0, ii = children.length; i2 < ii; i2++) {\n      const childElm = children[i2];\n      if (typeof childElm.connectedCallback === \"function\") {\n        childElm.connectedCallback();\n      }\n      serverSideConnected(childElm);\n    }\n  }\n};\n\n// src/runtime/set-value.ts\nvar getValue = (ref, propName) => getHostRef(ref).$instanceValues$.get(propName);\nvar setValue = (ref, propName, newVal, cmpMeta) => {\n  const hostRef = getHostRef(ref);\n  if (BUILD18.lazyLoad && !hostRef) {\n    throw new Error(\n      `Couldn't find host element for \"${cmpMeta.$tagName$}\" as it is unknown to this Stencil runtime. This usually happens when integrating a 3rd party Stencil component with another Stencil component or application. Please reach out to the maintainers of the 3rd party Stencil component or report this on the Stencil Discord server (https://chat.stenciljs.com) or comment on this similar [GitHub issue](https://github.com/ionic-team/stencil/issues/5457).`\n    );\n  }\n  const elm = BUILD18.lazyLoad ? hostRef.$hostElement$ : ref;\n  const oldVal = hostRef.$instanceValues$.get(propName);\n  const flags = hostRef.$flags$;\n  const instance = BUILD18.lazyLoad ? hostRef.$lazyInstance$ : elm;\n  newVal = parsePropertyValue(newVal, cmpMeta.$members$[propName][0]);\n  const areBothNaN = Number.isNaN(oldVal) && Number.isNaN(newVal);\n  const didValueChange = newVal !== oldVal && !areBothNaN;\n  if ((!BUILD18.lazyLoad || !(flags & 8 /* isConstructingInstance */) || oldVal === void 0) && didValueChange) {\n    hostRef.$instanceValues$.set(propName, newVal);\n    if (BUILD18.isDev) {\n      if (hostRef.$flags$ & 1024 /* devOnRender */) {\n        consoleDevWarn(\n          `The state/prop \"${propName}\" changed during rendering. This can potentially lead to infinite-loops and other bugs.`,\n          \"\\nElement\",\n          elm,\n          \"\\nNew value\",\n          newVal,\n          \"\\nOld value\",\n          oldVal\n        );\n      } else if (hostRef.$flags$ & 2048 /* devOnDidLoad */) {\n        consoleDevWarn(\n          `The state/prop \"${propName}\" changed during \"componentDidLoad()\", this triggers extra re-renders, try to setup on \"componentWillLoad()\"`,\n          \"\\nElement\",\n          elm,\n          \"\\nNew value\",\n          newVal,\n          \"\\nOld value\",\n          oldVal\n        );\n      }\n    }\n    if (!BUILD18.lazyLoad || instance) {\n      if (BUILD18.watchCallback && cmpMeta.$watchers$ && flags & 128 /* isWatchReady */) {\n        const watchMethods = cmpMeta.$watchers$[propName];\n        if (watchMethods) {\n          watchMethods.map((watchMethodName) => {\n            try {\n              instance[watchMethodName](newVal, oldVal, propName);\n            } catch (e) {\n              consoleError(e, elm);\n            }\n          });\n        }\n      }\n      if (BUILD18.updatable && (flags & (2 /* hasRendered */ | 16 /* isQueuedForUpdate */)) === 2 /* hasRendered */) {\n        if (BUILD18.cmpShouldUpdate && instance.componentShouldUpdate) {\n          if (instance.componentShouldUpdate(newVal, oldVal, propName) === false) {\n            return;\n          }\n        }\n        scheduleUpdate(hostRef, false);\n      }\n    }\n  }\n};\n\n// src/runtime/proxy-component.ts\nvar proxyComponent = (Cstr, cmpMeta, flags) => {\n  var _a, _b;\n  const prototype = Cstr.prototype;\n  if (BUILD19.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */ && flags & 1 /* isElementConstructor */) {\n    FORM_ASSOCIATED_CUSTOM_ELEMENT_CALLBACKS.forEach(\n      (cbName) => Object.defineProperty(prototype, cbName, {\n        value(...args) {\n          const hostRef = getHostRef(this);\n          const elm = BUILD19.lazyLoad ? hostRef.$hostElement$ : this;\n          const instance = BUILD19.lazyLoad ? hostRef.$lazyInstance$ : elm;\n          if (!instance) {\n            hostRef.$onReadyPromise$.then((instance2) => {\n              const cb = instance2[cbName];\n              typeof cb === \"function\" && cb.call(instance2, ...args);\n            });\n          } else {\n            const cb = instance[cbName];\n            typeof cb === \"function\" && cb.call(instance, ...args);\n          }\n        }\n      })\n    );\n  }\n  if (BUILD19.member && cmpMeta.$members$ || BUILD19.watchCallback && (cmpMeta.$watchers$ || Cstr.watchers)) {\n    if (BUILD19.watchCallback && Cstr.watchers && !cmpMeta.$watchers$) {\n      cmpMeta.$watchers$ = Cstr.watchers;\n    }\n    const members = Object.entries((_a = cmpMeta.$members$) != null ? _a : {});\n    members.map(([memberName, [memberFlags]]) => {\n      if ((BUILD19.prop || BUILD19.state) && (memberFlags & 31 /* Prop */ || (!BUILD19.lazyLoad || flags & 2 /* proxyState */) && memberFlags & 32 /* State */)) {\n        Object.defineProperty(prototype, memberName, {\n          get() {\n            return getValue(this, memberName);\n          },\n          set(newValue) {\n            if (BUILD19.isDev) {\n              const ref = getHostRef(this);\n              if (\n                // we are proxying the instance (not element)\n                (flags & 1 /* isElementConstructor */) === 0 && // the element is not constructing\n                (ref && ref.$flags$ & 8 /* isConstructingInstance */) === 0 && // the member is a prop\n                (memberFlags & 31 /* Prop */) !== 0 && // the member is not mutable\n                (memberFlags & 1024 /* Mutable */) === 0\n              ) {\n                consoleDevWarn(\n                  `@Prop() \"${memberName}\" on <${cmpMeta.$tagName$}> is immutable but was modified from within the component.\nMore information: https://stenciljs.com/docs/properties#prop-mutability`\n                );\n              }\n            }\n            setValue(this, memberName, newValue, cmpMeta);\n          },\n          configurable: true,\n          enumerable: true\n        });\n      } else if (BUILD19.lazyLoad && BUILD19.method && flags & 1 /* isElementConstructor */ && memberFlags & 64 /* Method */) {\n        Object.defineProperty(prototype, memberName, {\n          value(...args) {\n            var _a2;\n            const ref = getHostRef(this);\n            return (_a2 = ref == null ? void 0 : ref.$onInstancePromise$) == null ? void 0 : _a2.then(() => {\n              var _a3;\n              return (_a3 = ref.$lazyInstance$) == null ? void 0 : _a3[memberName](...args);\n            });\n          }\n        });\n      }\n    });\n    if (BUILD19.observeAttribute && (!BUILD19.lazyLoad || flags & 1 /* isElementConstructor */)) {\n      const attrNameToPropName = /* @__PURE__ */ new Map();\n      prototype.attributeChangedCallback = function(attrName, oldValue, newValue) {\n        plt.jmp(() => {\n          var _a2;\n          const propName = attrNameToPropName.get(attrName);\n          if (this.hasOwnProperty(propName)) {\n            newValue = this[propName];\n            delete this[propName];\n          } else if (prototype.hasOwnProperty(propName) && typeof this[propName] === \"number\" && // cast type to number to avoid TS compiler issues\n          this[propName] == newValue) {\n            return;\n          } else if (propName == null) {\n            const hostRef = getHostRef(this);\n            const flags2 = hostRef == null ? void 0 : hostRef.$flags$;\n            if (flags2 && !(flags2 & 8 /* isConstructingInstance */) && flags2 & 128 /* isWatchReady */ && newValue !== oldValue) {\n              const elm = BUILD19.lazyLoad ? hostRef.$hostElement$ : this;\n              const instance = BUILD19.lazyLoad ? hostRef.$lazyInstance$ : elm;\n              const entry = (_a2 = cmpMeta.$watchers$) == null ? void 0 : _a2[attrName];\n              entry == null ? void 0 : entry.forEach((callbackName) => {\n                if (instance[callbackName] != null) {\n                  instance[callbackName].call(instance, newValue, oldValue, attrName);\n                }\n              });\n            }\n            return;\n          }\n          this[propName] = newValue === null && typeof this[propName] === \"boolean\" ? false : newValue;\n        });\n      };\n      Cstr.observedAttributes = Array.from(\n        /* @__PURE__ */ new Set([\n          ...Object.keys((_b = cmpMeta.$watchers$) != null ? _b : {}),\n          ...members.filter(([_, m]) => m[0] & 15 /* HasAttribute */).map(([propName, m]) => {\n            var _a2;\n            const attrName = m[1] || propName;\n            attrNameToPropName.set(attrName, propName);\n            if (BUILD19.reflect && m[0] & 512 /* ReflectAttr */) {\n              (_a2 = cmpMeta.$attrsToReflect$) == null ? void 0 : _a2.push([propName, attrName]);\n            }\n            return attrName;\n          })\n        ])\n      );\n    }\n  }\n  return Cstr;\n};\n\n// src/runtime/initialize-component.ts\nvar initializeComponent = async (elm, hostRef, cmpMeta, hmrVersionId) => {\n  let Cstr;\n  if ((hostRef.$flags$ & 32 /* hasInitializedComponent */) === 0) {\n    hostRef.$flags$ |= 32 /* hasInitializedComponent */;\n    const bundleId = cmpMeta.$lazyBundleId$;\n    if ((BUILD20.lazyLoad || BUILD20.hydrateClientSide) && bundleId) {\n      const CstrImport = loadModule(cmpMeta, hostRef, hmrVersionId);\n      if (CstrImport && \"then\" in CstrImport) {\n        const endLoad = uniqueTime(\n          `st:load:${cmpMeta.$tagName$}:${hostRef.$modeName$}`,\n          `[Stencil] Load module for <${cmpMeta.$tagName$}>`\n        );\n        Cstr = await CstrImport;\n        endLoad();\n      } else {\n        Cstr = CstrImport;\n      }\n      if (!Cstr) {\n        throw new Error(`Constructor for \"${cmpMeta.$tagName$}#${hostRef.$modeName$}\" was not found`);\n      }\n      if (BUILD20.member && !Cstr.isProxied) {\n        if (BUILD20.watchCallback) {\n          cmpMeta.$watchers$ = Cstr.watchers;\n        }\n        proxyComponent(Cstr, cmpMeta, 2 /* proxyState */);\n        Cstr.isProxied = true;\n      }\n      const endNewInstance = createTime(\"createInstance\", cmpMeta.$tagName$);\n      if (BUILD20.member) {\n        hostRef.$flags$ |= 8 /* isConstructingInstance */;\n      }\n      try {\n        new Cstr(hostRef);\n      } catch (e) {\n        consoleError(e);\n      }\n      if (BUILD20.member) {\n        hostRef.$flags$ &= ~8 /* isConstructingInstance */;\n      }\n      if (BUILD20.watchCallback) {\n        hostRef.$flags$ |= 128 /* isWatchReady */;\n      }\n      endNewInstance();\n      fireConnectedCallback(hostRef.$lazyInstance$);\n    } else {\n      Cstr = elm.constructor;\n      const cmpTag = elm.localName;\n      customElements.whenDefined(cmpTag).then(() => hostRef.$flags$ |= 128 /* isWatchReady */);\n    }\n    if (BUILD20.style && Cstr && Cstr.style) {\n      let style;\n      if (typeof Cstr.style === \"string\") {\n        style = Cstr.style;\n      } else if (BUILD20.mode && typeof Cstr.style !== \"string\") {\n        hostRef.$modeName$ = computeMode(elm);\n        if (hostRef.$modeName$) {\n          style = Cstr.style[hostRef.$modeName$];\n        }\n        if (BUILD20.hydrateServerSide && hostRef.$modeName$) {\n          elm.setAttribute(\"s-mode\", hostRef.$modeName$);\n        }\n      }\n      const scopeId2 = getScopeId(cmpMeta, hostRef.$modeName$);\n      if (!styles.has(scopeId2)) {\n        const endRegisterStyles = createTime(\"registerStyles\", cmpMeta.$tagName$);\n        if (!BUILD20.hydrateServerSide && BUILD20.shadowDom && // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        BUILD20.shadowDomShim && cmpMeta.$flags$ & 8 /* needsShadowDomShim */) {\n          style = await import(\"./shadow-css.js\").then((m) => m.scopeCss(style, scopeId2));\n        }\n        registerStyle(scopeId2, style, !!(cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */));\n        endRegisterStyles();\n      }\n    }\n  }\n  const ancestorComponent = hostRef.$ancestorComponent$;\n  const schedule = () => scheduleUpdate(hostRef, true);\n  if (BUILD20.asyncLoading && ancestorComponent && ancestorComponent[\"s-rc\"]) {\n    ancestorComponent[\"s-rc\"].push(schedule);\n  } else {\n    schedule();\n  }\n};\nvar fireConnectedCallback = (instance) => {\n  if (BUILD20.lazyLoad && BUILD20.connectedCallback) {\n    safeCall(instance, \"connectedCallback\");\n  }\n};\n\n// src/runtime/connected-callback.ts\nvar connectedCallback = (elm) => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    const cmpMeta = hostRef.$cmpMeta$;\n    const endConnected = createTime(\"connectedCallback\", cmpMeta.$tagName$);\n    if (BUILD21.hostListenerTargetParent) {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, true);\n    }\n    if (!(hostRef.$flags$ & 1 /* hasConnected */)) {\n      hostRef.$flags$ |= 1 /* hasConnected */;\n      let hostId;\n      if (BUILD21.hydrateClientSide) {\n        hostId = elm.getAttribute(HYDRATE_ID);\n        if (hostId) {\n          if (BUILD21.shadowDom && supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            const scopeId2 = BUILD21.mode ? addStyle(elm.shadowRoot, cmpMeta, elm.getAttribute(\"s-mode\")) : addStyle(elm.shadowRoot, cmpMeta);\n            elm.classList.remove(scopeId2 + \"-h\", scopeId2 + \"-s\");\n          }\n          initializeClientHydrate(elm, cmpMeta.$tagName$, hostId, hostRef);\n        }\n      }\n      if (BUILD21.slotRelocation && !hostId) {\n        if (BUILD21.hydrateServerSide || (BUILD21.slot || BUILD21.shadowDom) && // TODO(STENCIL-854): Remove code related to legacy shadowDomShim field\n        cmpMeta.$flags$ & (4 /* hasSlotRelocation */ | 8 /* needsShadowDomShim */)) {\n          setContentReference(elm);\n        }\n      }\n      if (BUILD21.asyncLoading) {\n        let ancestorComponent = elm;\n        while (ancestorComponent = ancestorComponent.parentNode || ancestorComponent.host) {\n          if (BUILD21.hydrateClientSide && ancestorComponent.nodeType === 1 /* ElementNode */ && ancestorComponent.hasAttribute(\"s-id\") && ancestorComponent[\"s-p\"] || ancestorComponent[\"s-p\"]) {\n            attachToAncestor(hostRef, hostRef.$ancestorComponent$ = ancestorComponent);\n            break;\n          }\n        }\n      }\n      if (BUILD21.prop && !BUILD21.hydrateServerSide && cmpMeta.$members$) {\n        Object.entries(cmpMeta.$members$).map(([memberName, [memberFlags]]) => {\n          if (memberFlags & 31 /* Prop */ && elm.hasOwnProperty(memberName)) {\n            const value = elm[memberName];\n            delete elm[memberName];\n            elm[memberName] = value;\n          }\n        });\n      }\n      if (BUILD21.initializeNextTick) {\n        nextTick(() => initializeComponent(elm, hostRef, cmpMeta));\n      } else {\n        initializeComponent(elm, hostRef, cmpMeta);\n      }\n    } else {\n      addHostEventListeners(elm, hostRef, cmpMeta.$listeners$, false);\n      if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n        fireConnectedCallback(hostRef.$lazyInstance$);\n      } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n        hostRef.$onReadyPromise$.then(() => fireConnectedCallback(hostRef.$lazyInstance$));\n      }\n    }\n    endConnected();\n  }\n};\nvar setContentReference = (elm) => {\n  const contentRefElm = elm[\"s-cr\"] = doc.createComment(\n    BUILD21.isDebug ? `content-ref (host=${elm.localName})` : \"\"\n  );\n  contentRefElm[\"s-cn\"] = true;\n  insertBefore(elm, contentRefElm, elm.firstChild);\n};\n\n// src/runtime/disconnected-callback.ts\nimport { BUILD as BUILD22 } from \"@stencil/core/internal/app-data\";\nvar disconnectInstance = (instance) => {\n  if (BUILD22.lazyLoad && BUILD22.disconnectedCallback) {\n    safeCall(instance, \"disconnectedCallback\");\n  }\n  if (BUILD22.cmpDidUnload) {\n    safeCall(instance, \"componentDidUnload\");\n  }\n};\nvar disconnectedCallback = async (elm) => {\n  if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0) {\n    const hostRef = getHostRef(elm);\n    if (BUILD22.hostListener) {\n      if (hostRef.$rmListeners$) {\n        hostRef.$rmListeners$.map((rmListener) => rmListener());\n        hostRef.$rmListeners$ = void 0;\n      }\n    }\n    if (!BUILD22.lazyLoad) {\n      disconnectInstance(elm);\n    } else if (hostRef == null ? void 0 : hostRef.$lazyInstance$) {\n      disconnectInstance(hostRef.$lazyInstance$);\n    } else if (hostRef == null ? void 0 : hostRef.$onReadyPromise$) {\n      hostRef.$onReadyPromise$.then(() => disconnectInstance(hostRef.$lazyInstance$));\n    }\n  }\n};\n\n// src/runtime/dom-extras.ts\nimport { BUILD as BUILD23 } from \"@stencil/core/internal/app-data\";\nvar patchPseudoShadowDom = (hostElementPrototype, descriptorPrototype) => {\n  patchCloneNode(hostElementPrototype);\n  patchSlotAppendChild(hostElementPrototype);\n  patchSlotAppend(hostElementPrototype);\n  patchSlotPrepend(hostElementPrototype);\n  patchSlotInsertAdjacentElement(hostElementPrototype);\n  patchSlotInsertAdjacentHTML(hostElementPrototype);\n  patchSlotInsertAdjacentText(hostElementPrototype);\n  patchTextContent(hostElementPrototype);\n  patchChildSlotNodes(hostElementPrototype, descriptorPrototype);\n  patchSlotRemoveChild(hostElementPrototype);\n};\nvar patchCloneNode = (HostElementPrototype) => {\n  const orgCloneNode = HostElementPrototype.cloneNode;\n  HostElementPrototype.cloneNode = function(deep) {\n    const srcNode = this;\n    const isShadowDom = BUILD23.shadowDom ? srcNode.shadowRoot && supportsShadow : false;\n    const clonedNode = orgCloneNode.call(srcNode, isShadowDom ? deep : false);\n    if (BUILD23.slot && !isShadowDom && deep) {\n      let i2 = 0;\n      let slotted, nonStencilNode;\n      const stencilPrivates = [\n        \"s-id\",\n        \"s-cr\",\n        \"s-lr\",\n        \"s-rc\",\n        \"s-sc\",\n        \"s-p\",\n        \"s-cn\",\n        \"s-sr\",\n        \"s-sn\",\n        \"s-hn\",\n        \"s-ol\",\n        \"s-nr\",\n        \"s-si\",\n        \"s-rf\",\n        \"s-scs\"\n      ];\n      for (; i2 < srcNode.childNodes.length; i2++) {\n        slotted = srcNode.childNodes[i2][\"s-nr\"];\n        nonStencilNode = stencilPrivates.every((privateField) => !srcNode.childNodes[i2][privateField]);\n        if (slotted) {\n          if (BUILD23.appendChildSlotFix && clonedNode.__appendChild) {\n            clonedNode.__appendChild(slotted.cloneNode(true));\n          } else {\n            clonedNode.appendChild(slotted.cloneNode(true));\n          }\n        }\n        if (nonStencilNode) {\n          clonedNode.appendChild(srcNode.childNodes[i2].cloneNode(true));\n        }\n      }\n    }\n    return clonedNode;\n  };\n};\nvar patchSlotAppendChild = (HostElementPrototype) => {\n  HostElementPrototype.__appendChild = HostElementPrototype.appendChild;\n  HostElementPrototype.appendChild = function(newChild) {\n    const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n    const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n    if (slotNode) {\n      const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n      const appendAfter = slotChildNodes[slotChildNodes.length - 1];\n      const insertedNode = insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      updateFallbackSlotVisibility(this);\n      return insertedNode;\n    }\n    return this.__appendChild(newChild);\n  };\n};\nvar patchSlotRemoveChild = (ElementPrototype) => {\n  ElementPrototype.__removeChild = ElementPrototype.removeChild;\n  ElementPrototype.removeChild = function(toRemove) {\n    if (toRemove && typeof toRemove[\"s-sn\"] !== \"undefined\") {\n      const slotNode = getHostSlotNode(this.childNodes, toRemove[\"s-sn\"], this.tagName);\n      if (slotNode) {\n        const slotChildNodes = getHostSlotChildNodes(slotNode, toRemove[\"s-sn\"]);\n        const existingNode = slotChildNodes.find((n) => n === toRemove);\n        if (existingNode) {\n          existingNode.remove();\n          updateFallbackSlotVisibility(this);\n          return;\n        }\n      }\n    }\n    return this.__removeChild(toRemove);\n  };\n};\nvar patchSlotPrepend = (HostElementPrototype) => {\n  const originalPrepend = HostElementPrototype.prepend;\n  HostElementPrototype.prepend = function(...newChildren) {\n    newChildren.forEach((newChild) => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      const slotName = newChild[\"s-sn\"] = getSlotName(newChild);\n      const slotNode = getHostSlotNode(this.childNodes, slotName, this.tagName);\n      if (slotNode) {\n        const slotPlaceholder = document.createTextNode(\"\");\n        slotPlaceholder[\"s-nr\"] = newChild;\n        slotNode[\"s-cr\"].parentNode.__appendChild(slotPlaceholder);\n        newChild[\"s-ol\"] = slotPlaceholder;\n        const slotChildNodes = getHostSlotChildNodes(slotNode, slotName);\n        const appendAfter = slotChildNodes[0];\n        return insertBefore(appendAfter.parentNode, newChild, appendAfter.nextSibling);\n      }\n      if (newChild.nodeType === 1 && !!newChild.getAttribute(\"slot\")) {\n        newChild.hidden = true;\n      }\n      return originalPrepend.call(this, newChild);\n    });\n  };\n};\nvar patchSlotAppend = (HostElementPrototype) => {\n  HostElementPrototype.append = function(...newChildren) {\n    newChildren.forEach((newChild) => {\n      if (typeof newChild === \"string\") {\n        newChild = this.ownerDocument.createTextNode(newChild);\n      }\n      this.appendChild(newChild);\n    });\n  };\n};\nvar patchSlotInsertAdjacentHTML = (HostElementPrototype) => {\n  const originalInsertAdjacentHtml = HostElementPrototype.insertAdjacentHTML;\n  HostElementPrototype.insertAdjacentHTML = function(position, text) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentHtml.call(this, position, text);\n    }\n    const container = this.ownerDocument.createElement(\"_\");\n    let node;\n    container.innerHTML = text;\n    if (position === \"afterbegin\") {\n      while (node = container.firstChild) {\n        this.prepend(node);\n      }\n    } else if (position === \"beforeend\") {\n      while (node = container.firstChild) {\n        this.append(node);\n      }\n    }\n  };\n};\nvar patchSlotInsertAdjacentText = (HostElementPrototype) => {\n  HostElementPrototype.insertAdjacentText = function(position, text) {\n    this.insertAdjacentHTML(position, text);\n  };\n};\nvar patchSlotInsertAdjacentElement = (HostElementPrototype) => {\n  const originalInsertAdjacentElement = HostElementPrototype.insertAdjacentElement;\n  HostElementPrototype.insertAdjacentElement = function(position, element) {\n    if (position !== \"afterbegin\" && position !== \"beforeend\") {\n      return originalInsertAdjacentElement.call(this, position, element);\n    }\n    if (position === \"afterbegin\") {\n      this.prepend(element);\n      return element;\n    } else if (position === \"beforeend\") {\n      this.append(element);\n      return element;\n    }\n    return element;\n  };\n};\nvar patchTextContent = (hostElementPrototype) => {\n  const descriptor = Object.getOwnPropertyDescriptor(Node.prototype, \"textContent\");\n  Object.defineProperty(hostElementPrototype, \"__textContent\", descriptor);\n  if (BUILD23.experimentalScopedSlotChanges) {\n    Object.defineProperty(hostElementPrototype, \"textContent\", {\n      // To mimic shadow root behavior, we need to return the text content of all\n      // nodes in a slot reference node\n      get() {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        const textContent = slotRefNodes.map((node) => {\n          var _a, _b;\n          const text = [];\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            if (slotContent.nodeType === 3 /* TEXT_NODE */ || slotContent.nodeType === 1 /* ELEMENT_NODE */) {\n              text.push((_b = (_a = slotContent.textContent) == null ? void 0 : _a.trim()) != null ? _b : \"\");\n            }\n            slotContent = slotContent.nextSibling;\n          }\n          return text.filter((ref) => ref !== \"\").join(\" \");\n        }).filter((text) => text !== \"\").join(\" \");\n        return \" \" + textContent + \" \";\n      },\n      // To mimic shadow root behavior, we need to overwrite all nodes in a slot\n      // reference node. If a default slot reference node exists, the text content will be\n      // placed there. Otherwise, the new text node will be hidden\n      set(value) {\n        const slotRefNodes = getAllChildSlotNodes(this.childNodes);\n        slotRefNodes.forEach((node) => {\n          let slotContent = node.nextSibling;\n          while (slotContent && slotContent[\"s-sn\"] === node[\"s-sn\"]) {\n            const tmp = slotContent;\n            slotContent = slotContent.nextSibling;\n            tmp.remove();\n          }\n          if (node[\"s-sn\"] === \"\") {\n            const textNode = this.ownerDocument.createTextNode(value);\n            textNode[\"s-sn\"] = \"\";\n            insertBefore(node.parentElement, textNode, node.nextSibling);\n          } else {\n            node.remove();\n          }\n        });\n      }\n    });\n  } else {\n    Object.defineProperty(hostElementPrototype, \"textContent\", {\n      get() {\n        var _a;\n        const slotNode = getHostSlotNode(this.childNodes, \"\", this.tagName);\n        if (((_a = slotNode == null ? void 0 : slotNode.nextSibling) == null ? void 0 : _a.nodeType) === 3 /* TEXT_NODE */) {\n          return slotNode.nextSibling.textContent;\n        } else if (slotNode) {\n          return slotNode.textContent;\n        } else {\n          return this.__textContent;\n        }\n      },\n      set(value) {\n        var _a;\n        const slotNode = getHostSlotNode(this.childNodes, \"\", this.tagName);\n        if (((_a = slotNode == null ? void 0 : slotNode.nextSibling) == null ? void 0 : _a.nodeType) === 3 /* TEXT_NODE */) {\n          slotNode.nextSibling.textContent = value;\n        } else if (slotNode) {\n          slotNode.textContent = value;\n        } else {\n          this.__textContent = value;\n          const contentRefElm = this[\"s-cr\"];\n          if (contentRefElm) {\n            insertBefore(this, contentRefElm, this.firstChild);\n          }\n        }\n      }\n    });\n  }\n};\nvar patchChildSlotNodes = (elm, cmpMeta) => {\n  class FakeNodeList extends Array {\n    item(n) {\n      return this[n];\n    }\n  }\n  if (cmpMeta.$flags$ & 8 /* needsShadowDomShim */) {\n    const childNodesFn = elm.__lookupGetter__(\"childNodes\");\n    Object.defineProperty(elm, \"children\", {\n      get() {\n        return this.childNodes.map((n) => n.nodeType === 1);\n      }\n    });\n    Object.defineProperty(elm, \"childElementCount\", {\n      get() {\n        return elm.children.length;\n      }\n    });\n    Object.defineProperty(elm, \"childNodes\", {\n      get() {\n        const childNodes = childNodesFn.call(this);\n        if ((plt.$flags$ & 1 /* isTmpDisconnected */) === 0 && getHostRef(this).$flags$ & 2 /* hasRendered */) {\n          const result = new FakeNodeList();\n          for (let i2 = 0; i2 < childNodes.length; i2++) {\n            const slot = childNodes[i2][\"s-nr\"];\n            if (slot) {\n              result.push(slot);\n            }\n          }\n          return result;\n        }\n        return FakeNodeList.from(childNodes);\n      }\n    });\n  }\n};\nvar getAllChildSlotNodes = (childNodes) => {\n  const slotRefNodes = [];\n  for (const childNode of Array.from(childNodes)) {\n    if (childNode[\"s-sr\"]) {\n      slotRefNodes.push(childNode);\n    }\n    slotRefNodes.push(...getAllChildSlotNodes(childNode.childNodes));\n  }\n  return slotRefNodes;\n};\nvar getSlotName = (node) => node[\"s-sn\"] || node.nodeType === 1 && node.getAttribute(\"slot\") || \"\";\nvar getHostSlotNode = (childNodes, slotName, hostName) => {\n  let i2 = 0;\n  let childNode;\n  for (; i2 < childNodes.length; i2++) {\n    childNode = childNodes[i2];\n    if (childNode[\"s-sr\"] && childNode[\"s-sn\"] === slotName && childNode[\"s-hn\"] === hostName) {\n      return childNode;\n    }\n    childNode = getHostSlotNode(childNode.childNodes, slotName, hostName);\n    if (childNode) {\n      return childNode;\n    }\n  }\n  return null;\n};\nvar getHostSlotChildNodes = (n, slotName) => {\n  const childNodes = [n];\n  while ((n = n.nextSibling) && n[\"s-sn\"] === slotName) {\n    childNodes.push(n);\n  }\n  return childNodes;\n};\n\n// src/runtime/bootstrap-custom-element.ts\nvar defineCustomElement = (Cstr, compactMeta) => {\n  customElements.define(compactMeta[1], proxyCustomElement(Cstr, compactMeta));\n};\nvar proxyCustomElement = (Cstr, compactMeta) => {\n  const cmpMeta = {\n    $flags$: compactMeta[0],\n    $tagName$: compactMeta[1]\n  };\n  if (BUILD24.member) {\n    cmpMeta.$members$ = compactMeta[2];\n  }\n  if (BUILD24.hostListener) {\n    cmpMeta.$listeners$ = compactMeta[3];\n  }\n  if (BUILD24.watchCallback) {\n    cmpMeta.$watchers$ = Cstr.$watchers$;\n  }\n  if (BUILD24.reflect) {\n    cmpMeta.$attrsToReflect$ = [];\n  }\n  if (BUILD24.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n    cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n  }\n  if (BUILD24.experimentalSlotFixes) {\n    if (BUILD24.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchPseudoShadowDom(Cstr.prototype, cmpMeta);\n    }\n  } else {\n    if (BUILD24.slotChildNodesFix) {\n      patchChildSlotNodes(Cstr.prototype, cmpMeta);\n    }\n    if (BUILD24.cloneNodeFix) {\n      patchCloneNode(Cstr.prototype);\n    }\n    if (BUILD24.appendChildSlotFix) {\n      patchSlotAppendChild(Cstr.prototype);\n    }\n    if (BUILD24.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n      patchTextContent(Cstr.prototype);\n    }\n  }\n  const originalConnectedCallback = Cstr.prototype.connectedCallback;\n  const originalDisconnectedCallback = Cstr.prototype.disconnectedCallback;\n  Object.assign(Cstr.prototype, {\n    __registerHost() {\n      registerHost(this, cmpMeta);\n    },\n    connectedCallback() {\n      const hostRef = getHostRef(this);\n      addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n      connectedCallback(this);\n      if (BUILD24.connectedCallback && originalConnectedCallback) {\n        originalConnectedCallback.call(this);\n      }\n    },\n    disconnectedCallback() {\n      disconnectedCallback(this);\n      if (BUILD24.disconnectedCallback && originalDisconnectedCallback) {\n        originalDisconnectedCallback.call(this);\n      }\n    },\n    __attachShadow() {\n      if (supportsShadow) {\n        if (!this.shadowRoot) {\n          if (BUILD24.shadowDelegatesFocus) {\n            this.attachShadow({\n              mode: \"open\",\n              delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n            });\n          } else {\n            this.attachShadow({ mode: \"open\" });\n          }\n        } else {\n          if (this.shadowRoot.mode !== \"open\") {\n            throw new Error(\n              `Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${this.shadowRoot.mode} but Stencil only supports open shadow roots.`\n            );\n          }\n        }\n      } else {\n        this.shadowRoot = this;\n      }\n    }\n  });\n  Cstr.is = cmpMeta.$tagName$;\n  return proxyComponent(Cstr, cmpMeta, 1 /* isElementConstructor */ | 2 /* proxyState */);\n};\nvar forceModeUpdate = (elm) => {\n  if (BUILD24.style && BUILD24.mode && !BUILD24.lazyLoad) {\n    const mode = computeMode(elm);\n    const hostRef = getHostRef(elm);\n    if (hostRef.$modeName$ !== mode) {\n      const cmpMeta = hostRef.$cmpMeta$;\n      const oldScopeId = elm[\"s-sc\"];\n      const scopeId2 = getScopeId(cmpMeta, mode);\n      const style = elm.constructor.style[mode];\n      const flags = cmpMeta.$flags$;\n      if (style) {\n        if (!styles.has(scopeId2)) {\n          registerStyle(scopeId2, style, !!(flags & 1 /* shadowDomEncapsulation */));\n        }\n        hostRef.$modeName$ = mode;\n        elm.classList.remove(oldScopeId + \"-h\", oldScopeId + \"-s\");\n        attachStyles(hostRef);\n        forceUpdate(elm);\n      }\n    }\n  }\n};\n\n// src/runtime/bootstrap-lazy.ts\nimport { BUILD as BUILD25 } from \"@stencil/core/internal/app-data\";\n\n// src/runtime/hmr-component.ts\nvar hmrStart = (hostElement, cmpMeta, hmrVersionId) => {\n  const hostRef = getHostRef(hostElement);\n  hostRef.$flags$ = 1 /* hasConnected */;\n  initializeComponent(hostElement, hostRef, cmpMeta, hmrVersionId);\n};\n\n// src/runtime/bootstrap-lazy.ts\nvar bootstrapLazy = (lazyBundles, options = {}) => {\n  var _a;\n  if (BUILD25.profile && performance.mark) {\n    performance.mark(\"st:app:start\");\n  }\n  installDevTools();\n  const endBootstrap = createTime(\"bootstrapLazy\");\n  const cmpTags = [];\n  const exclude = options.exclude || [];\n  const customElements2 = win.customElements;\n  const head = doc.head;\n  const metaCharset = /* @__PURE__ */ head.querySelector(\"meta[charset]\");\n  const dataStyles = /* @__PURE__ */ doc.createElement(\"style\");\n  const deferredConnectedCallbacks = [];\n  let appLoadFallback;\n  let isBootstrapping = true;\n  Object.assign(plt, options);\n  plt.$resourcesUrl$ = new URL(options.resourcesUrl || \"./\", doc.baseURI).href;\n  if (BUILD25.asyncQueue) {\n    if (options.syncQueue) {\n      plt.$flags$ |= 4 /* queueSync */;\n    }\n  }\n  if (BUILD25.hydrateClientSide) {\n    plt.$flags$ |= 2 /* appLoaded */;\n  }\n  let hasSlotRelocation = false;\n  lazyBundles.map((lazyBundle) => {\n    lazyBundle[1].map((compactMeta) => {\n      var _a2;\n      const cmpMeta = {\n        $flags$: compactMeta[0],\n        $tagName$: compactMeta[1],\n        $members$: compactMeta[2],\n        $listeners$: compactMeta[3]\n      };\n      if (cmpMeta.$flags$ & 4 /* hasSlotRelocation */) {\n        hasSlotRelocation = true;\n      }\n      if (BUILD25.member) {\n        cmpMeta.$members$ = compactMeta[2];\n      }\n      if (BUILD25.hostListener) {\n        cmpMeta.$listeners$ = compactMeta[3];\n      }\n      if (BUILD25.reflect) {\n        cmpMeta.$attrsToReflect$ = [];\n      }\n      if (BUILD25.watchCallback) {\n        cmpMeta.$watchers$ = (_a2 = compactMeta[4]) != null ? _a2 : {};\n      }\n      if (BUILD25.shadowDom && !supportsShadow && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n        cmpMeta.$flags$ |= 8 /* needsShadowDomShim */;\n      }\n      const tagName = BUILD25.transformTagName && options.transformTagName ? options.transformTagName(cmpMeta.$tagName$) : cmpMeta.$tagName$;\n      const HostElement = class extends HTMLElement {\n        // StencilLazyHost\n        constructor(self) {\n          super(self);\n          this.hasRegisteredEventListeners = false;\n          self = this;\n          registerHost(self, cmpMeta);\n          if (BUILD25.shadowDom && cmpMeta.$flags$ & 1 /* shadowDomEncapsulation */) {\n            if (supportsShadow) {\n              if (!self.shadowRoot) {\n                if (BUILD25.shadowDelegatesFocus) {\n                  self.attachShadow({\n                    mode: \"open\",\n                    delegatesFocus: !!(cmpMeta.$flags$ & 16 /* shadowDelegatesFocus */)\n                  });\n                } else {\n                  self.attachShadow({ mode: \"open\" });\n                }\n              } else {\n                if (self.shadowRoot.mode !== \"open\") {\n                  throw new Error(\n                    `Unable to re-use existing shadow root for ${cmpMeta.$tagName$}! Mode is set to ${self.shadowRoot.mode} but Stencil only supports open shadow roots.`\n                  );\n                }\n              }\n            } else if (!BUILD25.hydrateServerSide && !(\"shadowRoot\" in self)) {\n              self.shadowRoot = self;\n            }\n          }\n        }\n        connectedCallback() {\n          const hostRef = getHostRef(this);\n          if (!this.hasRegisteredEventListeners) {\n            this.hasRegisteredEventListeners = true;\n            addHostEventListeners(this, hostRef, cmpMeta.$listeners$, false);\n          }\n          if (appLoadFallback) {\n            clearTimeout(appLoadFallback);\n            appLoadFallback = null;\n          }\n          if (isBootstrapping) {\n            deferredConnectedCallbacks.push(this);\n          } else {\n            plt.jmp(() => connectedCallback(this));\n          }\n        }\n        disconnectedCallback() {\n          plt.jmp(() => disconnectedCallback(this));\n        }\n        componentOnReady() {\n          return getHostRef(this).$onReadyPromise$;\n        }\n      };\n      if (BUILD25.experimentalSlotFixes) {\n        if (BUILD25.scoped && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchPseudoShadowDom(HostElement.prototype, cmpMeta);\n        }\n      } else {\n        if (BUILD25.slotChildNodesFix) {\n          patchChildSlotNodes(HostElement.prototype, cmpMeta);\n        }\n        if (BUILD25.cloneNodeFix) {\n          patchCloneNode(HostElement.prototype);\n        }\n        if (BUILD25.appendChildSlotFix) {\n          patchSlotAppendChild(HostElement.prototype);\n        }\n        if (BUILD25.scopedSlotTextContentFix && cmpMeta.$flags$ & 2 /* scopedCssEncapsulation */) {\n          patchTextContent(HostElement.prototype);\n        }\n      }\n      if (BUILD25.formAssociated && cmpMeta.$flags$ & 64 /* formAssociated */) {\n        HostElement.formAssociated = true;\n      }\n      if (BUILD25.hotModuleReplacement) {\n        HostElement.prototype[\"s-hmr\"] = function(hmrVersionId) {\n          hmrStart(this, cmpMeta, hmrVersionId);\n        };\n      }\n      cmpMeta.$lazyBundleId$ = lazyBundle[0];\n      if (!exclude.includes(tagName) && !customElements2.get(tagName)) {\n        cmpTags.push(tagName);\n        customElements2.define(\n          tagName,\n          proxyComponent(HostElement, cmpMeta, 1 /* isElementConstructor */)\n        );\n      }\n    });\n  });\n  if (cmpTags.length > 0) {\n    if (hasSlotRelocation) {\n      dataStyles.textContent += SLOT_FB_CSS;\n    }\n    if (BUILD25.invisiblePrehydration && (BUILD25.hydratedClass || BUILD25.hydratedAttribute)) {\n      dataStyles.textContent += cmpTags.sort() + HYDRATED_CSS;\n    }\n    if (dataStyles.innerHTML.length) {\n      dataStyles.setAttribute(\"data-styles\", \"\");\n      const nonce = (_a = plt.$nonce$) != null ? _a : queryNonceMetaTagContent(doc);\n      if (nonce != null) {\n        dataStyles.setAttribute(\"nonce\", nonce);\n      }\n      head.insertBefore(dataStyles, metaCharset ? metaCharset.nextSibling : head.firstChild);\n    }\n  }\n  isBootstrapping = false;\n  if (deferredConnectedCallbacks.length) {\n    deferredConnectedCallbacks.map((host) => host.connectedCallback());\n  } else {\n    if (BUILD25.profile) {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30, \"timeout\"));\n    } else {\n      plt.jmp(() => appLoadFallback = setTimeout(appDidLoad, 30));\n    }\n  }\n  endBootstrap();\n};\n\n// src/runtime/fragment.ts\nvar Fragment = (_, children) => children;\n\n// src/runtime/host-listener.ts\nimport { BUILD as BUILD26 } from \"@stencil/core/internal/app-data\";\nvar addHostEventListeners = (elm, hostRef, listeners, attachParentListeners) => {\n  if (BUILD26.hostListener && listeners) {\n    if (BUILD26.hostListenerTargetParent) {\n      if (attachParentListeners) {\n        listeners = listeners.filter(([flags]) => flags & 32 /* TargetParent */);\n      } else {\n        listeners = listeners.filter(([flags]) => !(flags & 32 /* TargetParent */));\n      }\n    }\n    listeners.map(([flags, name, method]) => {\n      const target = BUILD26.hostListenerTarget ? getHostListenerTarget(elm, flags) : elm;\n      const handler = hostListenerProxy(hostRef, method);\n      const opts = hostListenerOpts(flags);\n      plt.ael(target, name, handler, opts);\n      (hostRef.$rmListeners$ = hostRef.$rmListeners$ || []).push(() => plt.rel(target, name, handler, opts));\n    });\n  }\n};\nvar hostListenerProxy = (hostRef, methodName) => (ev) => {\n  var _a;\n  try {\n    if (BUILD26.lazyLoad) {\n      if (hostRef.$flags$ & 256 /* isListenReady */) {\n        (_a = hostRef.$lazyInstance$) == null ? void 0 : _a[methodName](ev);\n      } else {\n        (hostRef.$queuedListeners$ = hostRef.$queuedListeners$ || []).push([methodName, ev]);\n      }\n    } else {\n      hostRef.$hostElement$[methodName](ev);\n    }\n  } catch (e) {\n    consoleError(e);\n  }\n};\nvar getHostListenerTarget = (elm, flags) => {\n  if (BUILD26.hostListenerTargetDocument && flags & 4 /* TargetDocument */) return doc;\n  if (BUILD26.hostListenerTargetWindow && flags & 8 /* TargetWindow */) return win;\n  if (BUILD26.hostListenerTargetBody && flags & 16 /* TargetBody */) return doc.body;\n  if (BUILD26.hostListenerTargetParent && flags & 32 /* TargetParent */ && elm.parentElement)\n    return elm.parentElement;\n  return elm;\n};\nvar hostListenerOpts = (flags) => supportsListenerOptions ? {\n  passive: (flags & 1 /* Passive */) !== 0,\n  capture: (flags & 2 /* Capture */) !== 0\n} : (flags & 2 /* Capture */) !== 0;\n\n// src/runtime/nonce.ts\nvar setNonce = (nonce) => plt.$nonce$ = nonce;\n\n// src/runtime/platform-options.ts\nvar setPlatformOptions = (opts) => Object.assign(plt, opts);\n\n// src/runtime/vdom/vdom-annotations.ts\nvar insertVdomAnnotations = (doc2, staticComponents) => {\n  if (doc2 != null) {\n    const docData = {\n      hostIds: 0,\n      rootLevelIds: 0,\n      staticComponents: new Set(staticComponents)\n    };\n    const orgLocationNodes = [];\n    parseVNodeAnnotations(doc2, doc2.body, docData, orgLocationNodes);\n    orgLocationNodes.forEach((orgLocationNode) => {\n      var _a;\n      if (orgLocationNode != null && orgLocationNode[\"s-nr\"]) {\n        const nodeRef = orgLocationNode[\"s-nr\"];\n        let hostId = nodeRef[\"s-host-id\"];\n        let nodeId = nodeRef[\"s-node-id\"];\n        let childId = `${hostId}.${nodeId}`;\n        if (hostId == null) {\n          hostId = 0;\n          docData.rootLevelIds++;\n          nodeId = docData.rootLevelIds;\n          childId = `${hostId}.${nodeId}`;\n          if (nodeRef.nodeType === 1 /* ElementNode */) {\n            nodeRef.setAttribute(HYDRATE_CHILD_ID, childId);\n          } else if (nodeRef.nodeType === 3 /* TextNode */) {\n            if (hostId === 0) {\n              const textContent = (_a = nodeRef.nodeValue) == null ? void 0 : _a.trim();\n              if (textContent === \"\") {\n                orgLocationNode.remove();\n                return;\n              }\n            }\n            const commentBeforeTextNode = doc2.createComment(childId);\n            commentBeforeTextNode.nodeValue = `${TEXT_NODE_ID}.${childId}`;\n            insertBefore(nodeRef.parentNode, commentBeforeTextNode, nodeRef);\n          }\n        }\n        let orgLocationNodeId = `${ORG_LOCATION_ID}.${childId}`;\n        const orgLocationParentNode = orgLocationNode.parentElement;\n        if (orgLocationParentNode) {\n          if (orgLocationParentNode[\"s-en\"] === \"\") {\n            orgLocationNodeId += `.`;\n          } else if (orgLocationParentNode[\"s-en\"] === \"c\") {\n            orgLocationNodeId += `.c`;\n          }\n        }\n        orgLocationNode.nodeValue = orgLocationNodeId;\n      }\n    });\n  }\n};\nvar parseVNodeAnnotations = (doc2, node, docData, orgLocationNodes) => {\n  var _a;\n  if (node == null) {\n    return;\n  }\n  if (node[\"s-nr\"] != null) {\n    orgLocationNodes.push(node);\n  }\n  if (node.nodeType === 1 /* ElementNode */) {\n    const childNodes = [...Array.from(node.childNodes), ...Array.from(((_a = node.shadowRoot) == null ? void 0 : _a.childNodes) || [])];\n    childNodes.forEach((childNode) => {\n      const hostRef = getHostRef(childNode);\n      if (hostRef != null && !docData.staticComponents.has(childNode.nodeName.toLowerCase())) {\n        const cmpData = {\n          nodeIds: 0\n        };\n        insertVNodeAnnotations(doc2, childNode, hostRef.$vnode$, docData, cmpData);\n      }\n      parseVNodeAnnotations(doc2, childNode, docData, orgLocationNodes);\n    });\n  }\n};\nvar insertVNodeAnnotations = (doc2, hostElm, vnode, docData, cmpData) => {\n  if (vnode != null) {\n    const hostId = ++docData.hostIds;\n    hostElm.setAttribute(HYDRATE_ID, hostId);\n    if (hostElm[\"s-cr\"] != null) {\n      hostElm[\"s-cr\"].nodeValue = `${CONTENT_REF_ID}.${hostId}`;\n    }\n    if (vnode.$children$ != null) {\n      const depth = 0;\n      vnode.$children$.forEach((vnodeChild, index) => {\n        insertChildVNodeAnnotations(doc2, vnodeChild, cmpData, hostId, depth, index);\n      });\n    }\n    if (hostElm && vnode && vnode.$elm$ && !hostElm.hasAttribute(HYDRATE_CHILD_ID)) {\n      const parent = hostElm.parentElement;\n      if (parent && parent.childNodes) {\n        const parentChildNodes = Array.from(parent.childNodes);\n        const comment = parentChildNodes.find(\n          (node) => node.nodeType === 8 /* CommentNode */ && node[\"s-sr\"]\n        );\n        if (comment) {\n          const index = parentChildNodes.indexOf(hostElm) - 1;\n          vnode.$elm$.setAttribute(\n            HYDRATE_CHILD_ID,\n            `${comment[\"s-host-id\"]}.${comment[\"s-node-id\"]}.0.${index}`\n          );\n        }\n      }\n    }\n  }\n};\nvar insertChildVNodeAnnotations = (doc2, vnodeChild, cmpData, hostId, depth, index) => {\n  const childElm = vnodeChild.$elm$;\n  if (childElm == null) {\n    return;\n  }\n  const nodeId = cmpData.nodeIds++;\n  const childId = `${hostId}.${nodeId}.${depth}.${index}`;\n  childElm[\"s-host-id\"] = hostId;\n  childElm[\"s-node-id\"] = nodeId;\n  if (childElm.nodeType === 1 /* ElementNode */) {\n    childElm.setAttribute(HYDRATE_CHILD_ID, childId);\n  } else if (childElm.nodeType === 3 /* TextNode */) {\n    const parentNode = childElm.parentNode;\n    const nodeName = parentNode == null ? void 0 : parentNode.nodeName;\n    if (nodeName !== \"STYLE\" && nodeName !== \"SCRIPT\") {\n      const textNodeId = `${TEXT_NODE_ID}.${childId}`;\n      const commentBeforeTextNode = doc2.createComment(textNodeId);\n      insertBefore(parentNode, commentBeforeTextNode, childElm);\n    }\n  } else if (childElm.nodeType === 8 /* CommentNode */) {\n    if (childElm[\"s-sr\"]) {\n      const slotName = childElm[\"s-sn\"] || \"\";\n      const slotNodeId = `${SLOT_NODE_ID}.${childId}.${slotName}`;\n      childElm.nodeValue = slotNodeId;\n    }\n  }\n  if (vnodeChild.$children$ != null) {\n    const childDepth = depth + 1;\n    vnodeChild.$children$.forEach((vnode, index2) => {\n      insertChildVNodeAnnotations(doc2, vnode, cmpData, hostId, childDepth, index2);\n    });\n  }\n};\nexport {\n  BUILD27 as BUILD,\n  Build,\n  Env,\n  Fragment,\n  H,\n  H as HTMLElement,\n  Host,\n  NAMESPACE2 as NAMESPACE,\n  STENCIL_DEV_MODE,\n  addHostEventListeners,\n  bootstrapLazy,\n  cmpModules,\n  connectedCallback,\n  consoleDevError,\n  consoleDevInfo,\n  consoleDevWarn,\n  consoleError,\n  createEvent,\n  defineCustomElement,\n  disconnectedCallback,\n  doc,\n  forceModeUpdate,\n  forceUpdate,\n  getAssetPath,\n  getElement,\n  getHostRef,\n  getMode,\n  getRenderingRef,\n  getValue,\n  h,\n  insertVdomAnnotations,\n  isMemberInElement,\n  loadModule,\n  modeResolutionChain,\n  nextTick,\n  parsePropertyValue,\n  plt,\n  postUpdateComponent,\n  promiseResolve,\n  proxyComponent,\n  proxyCustomElement,\n  readTask,\n  registerHost,\n  registerInstance,\n  renderVdom,\n  setAssetPath,\n  setErrorHandler,\n  setMode,\n  setNonce,\n  setPlatformHelpers,\n  setPlatformOptions,\n  setValue,\n  styles,\n  supportsConstructableStylesheets,\n  supportsListenerOptions,\n  supportsShadow,\n  win,\n  writeTask\n};\n"], "mappings": ";AAAA;AACA;AACA;AACA,IAAIA,SAAS,GAAGC,MAAM,CAACC,cAAc;AACrC,IAAIC,QAAQ,GAAGA,CAACC,MAAM,EAAEC,GAAG,KAAK;EAC9B,KAAK,IAAIC,IAAI,IAAID,GAAG,EAClBL,SAAS,CAACI,MAAM,EAAEE,IAAI,EAAE;IAAEC,GAAG,EAAEF,GAAG,CAACC,IAAI,CAAC;IAAEE,UAAU,EAAE;EAAK,CAAC,CAAC;AACjE,CAAC;;AAED;AACA,SAASC,KAAK,QAAQ,iCAAiC;AACvD,IAAIC,KAAK,GAAG;EACVC,KAAK,EAAEF,KAAK,CAACE,KAAK,GAAG,IAAI,GAAG,KAAK;EACjCC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,KAAK;EACfC,SAAS,EAAEL,KAAK,CAACK,SAAS,GAAG,IAAI,GAAG;AACtC,CAAC;;AAED;AACA,SAASL,KAAK,IAAIM,MAAM,QAAQ,iCAAiC;AACjE,IAAIC,QAAQ,GAAGD,MAAM,CAACE,oBAAoB,GAAGC,MAAM,CAACC,oBAAoB,KAAKD,MAAM,CAACC,oBAAoB,GAAG,eAAgB,IAAIC,OAAO,CAAC,CAAC,CAAC,GAAG,eAAgB,IAAIA,OAAO,CAAC,CAAC;AACzK,IAAIC,UAAU,GAAIC,GAAG,IAAKN,QAAQ,CAACT,GAAG,CAACe,GAAG,CAAC;AAC3C,IAAIC,gBAAgB,GAAGA,CAACC,YAAY,EAAEC,OAAO,KAAKT,QAAQ,CAACU,GAAG,CAACD,OAAO,CAACE,cAAc,GAAGH,YAAY,EAAEC,OAAO,CAAC;AAC9G,IAAIG,YAAY,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC3C,MAAML,OAAO,GAAG;IACdM,OAAO,EAAE,CAAC;IACVC,aAAa,EAAEH,WAAW;IAC1BI,SAAS,EAAEH,OAAO;IAClBI,gBAAgB,EAAE,eAAgB,IAAIC,GAAG,CAAC;EAC5C,CAAC;EACD,IAAIpB,MAAM,CAACJ,KAAK,EAAE;IAChBc,OAAO,CAACW,aAAa,GAAG,CAAC;EAC3B;EACA,IAAIrB,MAAM,CAACsB,MAAM,IAAItB,MAAM,CAACuB,QAAQ,EAAE;IACpCb,OAAO,CAACc,mBAAmB,GAAG,IAAIC,OAAO,CAAEC,CAAC,IAAKhB,OAAO,CAACiB,mBAAmB,GAAGD,CAAC,CAAC;EACnF;EACA,IAAI1B,MAAM,CAAC4B,YAAY,EAAE;IACvBlB,OAAO,CAACmB,gBAAgB,GAAG,IAAIJ,OAAO,CAAEC,CAAC,IAAKhB,OAAO,CAACoB,gBAAgB,GAAGJ,CAAC,CAAC;IAC3EZ,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE;IACvBA,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE;EAC1B;EACA,OAAOb,QAAQ,CAACU,GAAG,CAACG,WAAW,EAAEJ,OAAO,CAAC;AAC3C,CAAC;AACD,IAAIqB,iBAAiB,GAAGA,CAACC,GAAG,EAAEC,UAAU,KAAKA,UAAU,IAAID,GAAG;;AAE9D;AACA,SAAStC,KAAK,IAAIwC,MAAM,QAAQ,iCAAiC;;AAEjE;AACA,SAASxC,KAAK,IAAIyC,MAAM,QAAQ,iCAAiC;AACjE,IAAIC,WAAW;AACf,IAAIC,YAAY,GAAGA,CAACC,CAAC,EAAEC,EAAE,KAAK,CAACH,WAAW,IAAII,OAAO,CAACC,KAAK,EAAEH,CAAC,EAAEC,EAAE,CAAC;AACnE,IAAIG,gBAAgB,GAAGP,MAAM,CAACpC,SAAS,GAAG,CAAC,UAAU,CAAC,GAAG,CACvD,WAAW,EACX,wGAAwG,CACzG;AACD,IAAI4C,eAAe,GAAGA,CAAC,GAAGC,CAAC,KAAKJ,OAAO,CAACC,KAAK,CAAC,GAAGC,gBAAgB,EAAE,GAAGE,CAAC,CAAC;AACxE,IAAIC,cAAc,GAAGA,CAAC,GAAGD,CAAC,KAAKJ,OAAO,CAACM,IAAI,CAAC,GAAGJ,gBAAgB,EAAE,GAAGE,CAAC,CAAC;AACtE,IAAIG,cAAc,GAAGA,CAAC,GAAGH,CAAC,KAAKJ,OAAO,CAACQ,IAAI,CAAC,GAAGN,gBAAgB,EAAE,GAAGE,CAAC,CAAC;AACtE,IAAIK,eAAe,GAAIC,OAAO,IAAKd,WAAW,GAAGc,OAAO;;AAExD;AACA,IAAIC,UAAU,GAAG,eAAgB,IAAI/B,GAAG,CAAC,CAAC;AAC1C,IAAIgC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,UAAU,GAAGA,CAACtC,OAAO,EAAEL,OAAO,EAAE4C,YAAY,KAAK;EACnD,MAAMC,UAAU,GAAGxC,OAAO,CAACyC,SAAS,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EACvD,MAAMC,QAAQ,GAAG3C,OAAO,CAAC4C,cAAc;EACvC,IAAIzB,MAAM,CAACtC,KAAK,IAAI,OAAO8D,QAAQ,KAAK,QAAQ,EAAE;IAChDf,eAAe,CACb,oCAAoC5B,OAAO,CAACyC,SAAS,sBAAsB9C,OAAO,CAACkD,UAAU,2BAC/F,CAAC;IACD,OAAO,KAAK,CAAC;EACf,CAAC,MAAM,IAAI,CAACF,QAAQ,EAAE;IACpB,OAAO,KAAK,CAAC;EACf;EACA,MAAMG,MAAM,GAAG,CAAC3B,MAAM,CAAChC,oBAAoB,GAAGiD,UAAU,CAAC3D,GAAG,CAACkE,QAAQ,CAAC,GAAG,KAAK;EAC9E,IAAIG,MAAM,EAAE;IACV,OAAOA,MAAM,CAACN,UAAU,CAAC;EAC3B;EACA;EACA,OAAO,MAAM,CACX;EACA;EACA;EACA;EACA,KAAKG,QAAQ,YAAYxB,MAAM,CAAChC,oBAAoB,IAAIoD,YAAY,GAAG,SAAS,GAAGA,YAAY,GAAG,EAAE,EACtG,CAAC,CAACQ,IAAI,CAAEC,cAAc,IAAK;IACzB,IAAI,CAAC7B,MAAM,CAAChC,oBAAoB,EAAE;MAChCiD,UAAU,CAACxC,GAAG,CAAC+C,QAAQ,EAAEK,cAAc,CAAC;IAC1C;IACA,OAAOA,cAAc,CAACR,UAAU,CAAC;EACnC,CAAC,EAAElB,YAAY,CAAC;AAClB,CAAC;;AAED;AACA,IAAI2B,MAAM,GAAG,eAAgB,IAAI5C,GAAG,CAAC,CAAC;AACtC,IAAI6C,mBAAmB,GAAG,EAAE;;AAE5B;AACA,SAASvE,KAAK,IAAIwE,MAAM,QAAQ,iCAAiC;;AAEjE;AACA,IAAIC,cAAc,GAAG,GAAG;AACxB,IAAIC,eAAe,GAAG,GAAG;AACzB,IAAIC,YAAY,GAAG,GAAG;AACtB,IAAIC,YAAY,GAAG,GAAG;AACtB,IAAIC,UAAU,GAAG,MAAM;AACvB,IAAIC,iBAAiB,GAAG,QAAQ;AAChC,IAAIC,gBAAgB,GAAG,MAAM;AAC7B,IAAIC,YAAY,GAAG,kDAAkD;AACrE,IAAIC,WAAW,GAAG,wDAAwD;AAC1E,IAAIC,QAAQ,GAAG,8BAA8B;AAC7C,IAAIC,wCAAwC,GAAG,CAC7C,wBAAwB,EACxB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,CAC3B;;AAED;AACA,SAASnF,KAAK,IAAIoF,MAAM,QAAQ,iCAAiC;AACjE,IAAIC,GAAG,GAAG,OAAO5E,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,CAAC,CAAC;AACrD,IAAI6E,GAAG,GAAGD,GAAG,CAACE,QAAQ,IAAI;EAAEC,IAAI,EAAE,CAAC;AAAE,CAAC;AACtC,IAAIC,CAAC,GAAGJ,GAAG,CAACK,WAAW,IAAI,MAAM,EAChC;AACD,IAAIC,GAAG,GAAG;EACRrE,OAAO,EAAE,CAAC;EACVsE,cAAc,EAAE,EAAE;EAClBC,GAAG,EAAGC,EAAE,IAAKA,EAAE,CAAC,CAAC;EACjBC,GAAG,EAAGD,EAAE,IAAKE,qBAAqB,CAACF,EAAE,CAAC;EACtCG,GAAG,EAAEA,CAACpD,EAAE,EAAEqD,SAAS,EAAEC,QAAQ,EAAEC,IAAI,KAAKvD,EAAE,CAACwD,gBAAgB,CAACH,SAAS,EAAEC,QAAQ,EAAEC,IAAI,CAAC;EACtFE,GAAG,EAAEA,CAACzD,EAAE,EAAEqD,SAAS,EAAEC,QAAQ,EAAEC,IAAI,KAAKvD,EAAE,CAAC0D,mBAAmB,CAACL,SAAS,EAAEC,QAAQ,EAAEC,IAAI,CAAC;EACzFI,EAAE,EAAEA,CAACN,SAAS,EAAEE,IAAI,KAAK,IAAIK,WAAW,CAACP,SAAS,EAAEE,IAAI;AAC1D,CAAC;AACD,IAAIM,kBAAkB,GAAIC,OAAO,IAAK;EACpCnH,MAAM,CAACoH,MAAM,CAACjB,GAAG,EAAEgB,OAAO,CAAC;AAC7B,CAAC;AACD,IAAIE,cAAc,GAAGzB,MAAM,CAAC0B,SAAS;AACrC,IAAIC,uBAAuB,GAAG,eAAgB,CAAC,MAAM;EACnD,IAAIC,wBAAwB,GAAG,KAAK;EACpC,IAAI;IACF1B,GAAG,CAACe,gBAAgB,CAClB,GAAG,EACH,IAAI,EACJ7G,MAAM,CAACC,cAAc,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE;MACnCK,GAAGA,CAAA,EAAG;QACJkH,wBAAwB,GAAG,IAAI;MACjC;IACF,CAAC,CACH,CAAC;EACH,CAAC,CAAC,OAAOpE,CAAC,EAAE,CACZ;EACA,OAAOoE,wBAAwB;AACjC,CAAC,EAAE,CAAC;AACJ,IAAIC,cAAc,GAAIC,CAAC,IAAKnF,OAAO,CAACoF,OAAO,CAACD,CAAC,CAAC;AAC9C,IAAIE,gCAAgC,GAAGhC,MAAM,CAACiC,gBAAgB,GAAG,eAAgB,CAAC,MAAM;EACtF,IAAI;IACF,IAAIC,aAAa,CAAC,CAAC;IACnB,OAAO,OAAO,IAAIA,aAAa,CAAC,CAAC,CAACC,WAAW,KAAK,UAAU;EAC9D,CAAC,CAAC,OAAO3E,CAAC,EAAE,CACZ;EACA,OAAO,KAAK;AACd,CAAC,EAAE,CAAC,GAAG,KAAK;;AAEZ;AACA,IAAI4E,eAAe,GAAG,CAAC;AACvB,IAAIC,YAAY,GAAG,KAAK;AACxB,IAAIC,aAAa,GAAG,EAAE;AACtB,IAAIC,cAAc,GAAG,EAAE;AACvB,IAAIC,iBAAiB,GAAG,EAAE;AAC1B,IAAIC,SAAS,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAMC,EAAE,IAAK;EACxCF,KAAK,CAACG,IAAI,CAACD,EAAE,CAAC;EACd,IAAI,CAACP,YAAY,EAAE;IACjBA,YAAY,GAAG,IAAI;IACnB,IAAIM,KAAK,IAAIpC,GAAG,CAACrE,OAAO,GAAG,CAAC,CAAC,iBAAiB;MAC5C4G,QAAQ,CAACC,KAAK,CAAC;IACjB,CAAC,MAAM;MACLxC,GAAG,CAACI,GAAG,CAACoC,KAAK,CAAC;IAChB;EACF;AACF,CAAC;AACD,IAAIC,OAAO,GAAIN,KAAK,IAAK;EACvB,KAAK,IAAIO,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGP,KAAK,CAACQ,MAAM,EAAED,EAAE,EAAE,EAAE;IACxC,IAAI;MACFP,KAAK,CAACO,EAAE,CAAC,CAACE,WAAW,CAACC,GAAG,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAO5F,CAAC,EAAE;MACVD,YAAY,CAACC,CAAC,CAAC;IACjB;EACF;EACAkF,KAAK,CAACQ,MAAM,GAAG,CAAC;AAClB,CAAC;AACD,IAAIG,cAAc,GAAGA,CAACX,KAAK,EAAEY,OAAO,KAAK;EACvC,IAAIL,EAAE,GAAG,CAAC;EACV,IAAIM,EAAE,GAAG,CAAC;EACV,OAAON,EAAE,GAAGP,KAAK,CAACQ,MAAM,IAAI,CAACK,EAAE,GAAGJ,WAAW,CAACC,GAAG,CAAC,CAAC,IAAIE,OAAO,EAAE;IAC9D,IAAI;MACFZ,KAAK,CAACO,EAAE,EAAE,CAAC,CAACM,EAAE,CAAC;IACjB,CAAC,CAAC,OAAO/F,CAAC,EAAE;MACVD,YAAY,CAACC,CAAC,CAAC;IACjB;EACF;EACA,IAAIyF,EAAE,KAAKP,KAAK,CAACQ,MAAM,EAAE;IACvBR,KAAK,CAACQ,MAAM,GAAG,CAAC;EAClB,CAAC,MAAM,IAAID,EAAE,KAAK,CAAC,EAAE;IACnBP,KAAK,CAACc,MAAM,CAAC,CAAC,EAAEP,EAAE,CAAC;EACrB;AACF,CAAC;AACD,IAAIF,KAAK,GAAGA,CAAA,KAAM;EAChB,IAAI3D,MAAM,CAACqE,UAAU,EAAE;IACrBrB,eAAe,EAAE;EACnB;EACAY,OAAO,CAACV,aAAa,CAAC;EACtB,IAAIlD,MAAM,CAACqE,UAAU,EAAE;IACrB,MAAMH,OAAO,GAAG,CAAC/C,GAAG,CAACrE,OAAO,GAAG,CAAC,CAAC,qBAAqB,CAAC,CAAC,kBAAkBiH,WAAW,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAGM,IAAI,CAACC,IAAI,CAACvB,eAAe,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,GAAGwB,QAAQ;IACnJP,cAAc,CAACd,cAAc,EAAEe,OAAO,CAAC;IACvCD,cAAc,CAACb,iBAAiB,EAAEc,OAAO,CAAC;IAC1C,IAAIf,cAAc,CAACW,MAAM,GAAG,CAAC,EAAE;MAC7BV,iBAAiB,CAACK,IAAI,CAAC,GAAGN,cAAc,CAAC;MACzCA,cAAc,CAACW,MAAM,GAAG,CAAC;IAC3B;IACA,IAAIb,YAAY,GAAGC,aAAa,CAACY,MAAM,GAAGX,cAAc,CAACW,MAAM,GAAGV,iBAAiB,CAACU,MAAM,GAAG,CAAC,EAAE;MAC9F3C,GAAG,CAACI,GAAG,CAACoC,KAAK,CAAC;IAChB,CAAC,MAAM;MACLX,eAAe,GAAG,CAAC;IACrB;EACF,CAAC,MAAM;IACLY,OAAO,CAACT,cAAc,CAAC;IACvB,IAAIF,YAAY,GAAGC,aAAa,CAACY,MAAM,GAAG,CAAC,EAAE;MAC3C3C,GAAG,CAACI,GAAG,CAACoC,KAAK,CAAC;IAChB;EACF;AACF,CAAC;AACD,IAAID,QAAQ,GAAIF,EAAE,IAAKf,cAAc,CAAC,CAAC,CAAC7C,IAAI,CAAC4D,EAAE,CAAC;AAChD,IAAIiB,QAAQ,GAAG,eAAgBpB,SAAS,CAACH,aAAa,EAAE,KAAK,CAAC;AAC9D,IAAIwB,SAAS,GAAG,eAAgBrB,SAAS,CAACF,cAAc,EAAE,IAAI,CAAC;;AAE/D;AACA,SAAS3H,KAAK,IAAImJ,OAAO,EAAEC,GAAG,EAAEC,SAAS,IAAIC,UAAU,QAAQ,iCAAiC;;AAEhG;AACA,IAAIC,YAAY,GAAIC,IAAI,IAAK;EAC3B,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAACF,IAAI,EAAE7D,GAAG,CAACC,cAAc,CAAC;EAClD,OAAO6D,QAAQ,CAACE,MAAM,KAAKtE,GAAG,CAACuE,QAAQ,CAACD,MAAM,GAAGF,QAAQ,CAACI,IAAI,GAAGJ,QAAQ,CAACK,QAAQ;AACpF,CAAC;AACD,IAAIC,YAAY,GAAIP,IAAI,IAAK7D,GAAG,CAACC,cAAc,GAAG4D,IAAI;;AAEtD;AACA,SAASxJ,KAAK,IAAIgK,OAAO,QAAQ,iCAAiC;;AAElE;AACA,IAAIC,SAAS,GAAG,CAAC,CAAC;AAClB,IAAIC,MAAM,GAAG,4BAA4B;AACzC,IAAIC,OAAO,GAAG,8BAA8B;;AAE5C;AACA,IAAIC,KAAK,GAAIlD,CAAC,IAAKA,CAAC,IAAI,IAAI;AAC5B,IAAImD,aAAa,GAAIC,CAAC,IAAK;EACzBA,CAAC,GAAG,OAAOA,CAAC;EACZ,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,UAAU;AAC3C,CAAC;;AAED;AACA,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EACtC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGD,IAAI,CAAChF,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiF,EAAE,CAACG,aAAa,CAAC,wBAAwB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,EAAE,CAACG,YAAY,CAAC,SAAS,CAAC,KAAK,IAAI,GAAGF,EAAE,GAAG,KAAK,CAAC;AACzK;;AAEA;AACA,IAAIG,cAAc,GAAG,CAAC,CAAC;AACvBpL,QAAQ,CAACoL,cAAc,EAAE;EACvBC,GAAG,EAAEA,CAAA,KAAMA,GAAG;EACdC,GAAG,EAAEA,CAAA,KAAMA,GAAG;EACdC,EAAE,EAAEA,CAAA,KAAMA,EAAE;EACZC,MAAM,EAAEA,CAAA,KAAMA,MAAM;EACpBC,SAAS,EAAEA,CAAA,KAAMA;AACnB,CAAC,CAAC;AACF,IAAIF,EAAE,GAAIG,KAAK,KAAM;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,KAAK;EACZF;AACF,CAAC,CAAC;AACF,IAAIL,GAAG,GAAIK,KAAK,KAAM;EACpBC,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE,IAAI;EACXF;AACF,CAAC,CAAC;AACF,SAASJ,GAAGA,CAACO,MAAM,EAAEC,EAAE,EAAE;EACvB,IAAID,MAAM,CAACF,IAAI,EAAE;IACf,MAAMI,GAAG,GAAGD,EAAE,CAACD,MAAM,CAACH,KAAK,CAAC;IAC5B,IAAIK,GAAG,YAAY1J,OAAO,EAAE;MAC1B,OAAO0J,GAAG,CAACrH,IAAI,CAAEsH,MAAM,IAAKT,EAAE,CAACS,MAAM,CAAC,CAAC;IACzC,CAAC,MAAM;MACL,OAAOT,EAAE,CAACQ,GAAG,CAAC;IAChB;EACF;EACA,IAAIF,MAAM,CAACD,KAAK,EAAE;IAChB,MAAMF,KAAK,GAAGG,MAAM,CAACH,KAAK;IAC1B,OAAOL,GAAG,CAACK,KAAK,CAAC;EACnB;EACA,MAAM,uBAAuB;AAC/B;AACA,IAAIF,MAAM,GAAIK,MAAM,IAAK;EACvB,IAAIA,MAAM,CAACF,IAAI,EAAE;IACf,OAAOE,MAAM,CAACH,KAAK;EACrB,CAAC,MAAM;IACL,MAAMG,MAAM,CAACH,KAAK;EACpB;AACF,CAAC;AACD,IAAID,SAAS,GAAII,MAAM,IAAK;EAC1B,IAAIA,MAAM,CAACD,KAAK,EAAE;IAChB,OAAOC,MAAM,CAACH,KAAK;EACrB,CAAC,MAAM;IACL,MAAMG,MAAM,CAACH,KAAK;EACpB;AACF,CAAC;;AAED;AACA,SAASpL,KAAK,IAAI2L,OAAO,QAAQ,iCAAiC;;AAElE;AACA,SAAS3L,KAAK,IAAI4L,MAAM,QAAQ,iCAAiC;;AAEjE;AACA,SAAS5L,KAAK,IAAI6L,MAAM,QAAQ,iCAAiC;AACjE,IAAIC,CAAC,GAAG,CAAC;AACT,IAAIC,UAAU,GAAGA,CAACC,MAAM,EAAEC,OAAO,GAAG,EAAE,KAAK;EACzC,IAAIJ,MAAM,CAACK,OAAO,IAAI3D,WAAW,CAAC4D,IAAI,EAAE;IACtC,MAAMC,GAAG,GAAG,MAAMJ,MAAM,IAAIC,OAAO,IAAIH,CAAC,EAAE,EAAE;IAC5CvD,WAAW,CAAC4D,IAAI,CAACC,GAAG,CAAC;IACrB,OAAO,MAAM7D,WAAW,CAAC8D,OAAO,CAAC,aAAaL,MAAM,OAAOC,OAAO,GAAG,EAAEG,GAAG,CAAC;EAC7E,CAAC,MAAM;IACL,OAAO,MAAM;MACX;IACF,CAAC;EACH;AACF,CAAC;AACD,IAAIE,UAAU,GAAGA,CAACF,GAAG,EAAEG,WAAW,KAAK;EACrC,IAAIV,MAAM,CAACK,OAAO,IAAI3D,WAAW,CAAC4D,IAAI,EAAE;IACtC,IAAI5D,WAAW,CAACiE,gBAAgB,CAACJ,GAAG,EAAE,MAAM,CAAC,CAAC9D,MAAM,KAAK,CAAC,EAAE;MAC1DC,WAAW,CAAC4D,IAAI,CAACC,GAAG,CAAC;IACvB;IACA,OAAO,MAAM;MACX,IAAI7D,WAAW,CAACiE,gBAAgB,CAACD,WAAW,EAAE,SAAS,CAAC,CAACjE,MAAM,KAAK,CAAC,EAAE;QACrEC,WAAW,CAAC8D,OAAO,CAACE,WAAW,EAAEH,GAAG,CAAC;MACvC;IACF,CAAC;EACH,CAAC,MAAM;IACL,OAAO,MAAM;MACX;IACF,CAAC;EACH;AACF,CAAC;AACD,IAAIK,OAAO,GAAI5L,GAAG,IAAK;EACrB,MAAMG,OAAO,GAAGJ,UAAU,CAACC,GAAG,CAAC;EAC/B,IAAI,CAACG,OAAO,EAAE;IACZ,OAAO,KAAK,CAAC;EACf;EACA,MAAM0L,KAAK,GAAG1L,OAAO,CAACM,OAAO;EAC7B,MAAMF,WAAW,GAAGJ,OAAO,CAACO,aAAa;EACzC,OAAO;IACLoL,WAAW,EAAE3L,OAAO,CAACW,aAAa;IAClC+K,KAAK,EAAE;MACLE,WAAW,EAAE,CAAC,EAAEF,KAAK,GAAG,CAAC,CAAC,kBAAkB;MAC5CG,YAAY,EAAE,CAAC,EAAEH,KAAK,GAAG,CAAC,CAAC,mBAAmB;MAC9CI,oBAAoB,EAAE,CAAC,EAAEJ,KAAK,GAAG,CAAC,CAAC,2BAA2B;MAC9DK,sBAAsB,EAAE,CAAC,EAAEL,KAAK,GAAG,CAAC,CAAC,6BAA6B;MAClEM,iBAAiB,EAAE,CAAC,EAAEN,KAAK,GAAG,EAAE,CAAC,wBAAwB;MACzDO,uBAAuB,EAAE,CAAC,EAAEP,KAAK,GAAG,EAAE,CAAC,8BAA8B;MACrEQ,kBAAkB,EAAE,CAAC,EAAER,KAAK,GAAG,EAAE,CAAC,yBAAyB;MAC3DS,YAAY,EAAE,CAAC,EAAET,KAAK,GAAG,GAAG,CAAC,mBAAmB;MAChDU,aAAa,EAAE,CAAC,EAAEV,KAAK,GAAG,GAAG,CAAC,oBAAoB;MAClDW,aAAa,EAAE,CAAC,EAAEX,KAAK,GAAG,GAAG,CAAC;IAChC,CAAC;IACDY,cAAc,EAAEtM,OAAO,CAACS,gBAAgB;IACxC8L,iBAAiB,EAAEvM,OAAO,CAACwM,mBAAmB;IAC9CpM,WAAW;IACXL,YAAY,EAAEC,OAAO,CAACE,cAAc;IACpCuM,KAAK,EAAEzM,OAAO,CAAC0M,OAAO;IACtBC,QAAQ,EAAE3M,OAAO,CAACkD,UAAU;IAC5B0J,cAAc,EAAE5M,OAAO,CAACmB,gBAAgB;IACxC0L,cAAc,EAAE7M,OAAO,CAACoB,gBAAgB;IACxC0L,iBAAiB,EAAE9M,OAAO,CAACc,mBAAmB;IAC9CiM,iBAAiB,EAAE/M,OAAO,CAACiB,mBAAmB;IAC9C+L,eAAe,EAAEhN,OAAO,CAACiN,iBAAiB;IAC1CC,eAAe,EAAElN,OAAO,CAACmN,iBAAiB;IAC1CC,WAAW,EAAEpN,OAAO,CAACqN,aAAa;IAClC,CAAC,MAAM,GAAGjN,WAAW,CAAC,MAAM,CAAC;IAC7B,CAAC,MAAM,GAAGA,WAAW,CAAC,MAAM,CAAC;IAC7B,CAAC,MAAM,GAAGA,WAAW,CAAC,MAAM,CAAC;IAC7B,CAAC,KAAK,GAAGA,WAAW,CAAC,KAAK,CAAC;IAC3B,CAAC,MAAM,GAAGA,WAAW,CAAC,MAAM,CAAC;IAC7B,CAAC,MAAM,GAAGA,WAAW,CAAC,MAAM;EAC9B,CAAC;AACH,CAAC;AACD,IAAIkN,eAAe,GAAGA,CAAA,KAAM;EAC1B,IAAIzC,MAAM,CAAC0C,QAAQ,EAAE;IACnB,MAAMC,OAAO,GAAGnJ,GAAG,CAACmJ,OAAO,GAAGnJ,GAAG,CAACmJ,OAAO,IAAI,CAAC,CAAC;IAC/C,MAAMC,eAAe,GAAGD,OAAO,CAAC/B,OAAO;IACvC+B,OAAO,CAAC/B,OAAO,GAAI5L,GAAG,IAAK;MACzB,IAAI0K,MAAM,GAAGkB,OAAO,CAAC5L,GAAG,CAAC;MACzB,IAAI,CAAC0K,MAAM,IAAI,OAAOkD,eAAe,KAAK,UAAU,EAAE;QACpDlD,MAAM,GAAGkD,eAAe,CAAC5N,GAAG,CAAC;MAC/B;MACA,OAAO0K,MAAM;IACf,CAAC;EACH;AACF,CAAC;;AAED;AACA,SAASvL,KAAK,IAAI0O,MAAM,QAAQ,iCAAiC;AACjE,IAAIC,CAAC,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAE,GAAGC,QAAQ,KAAK;EAC5C,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAI3C,GAAG,GAAG,IAAI;EACd,IAAI4C,QAAQ,GAAG,IAAI;EACnB,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,UAAU,GAAG,KAAK;EACtB,MAAMC,aAAa,GAAG,EAAE;EACxB,MAAMC,IAAI,GAAIC,CAAC,IAAK;IAClB,KAAK,IAAIhH,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGgH,CAAC,CAAC/G,MAAM,EAAED,EAAE,EAAE,EAAE;MACpC0G,KAAK,GAAGM,CAAC,CAAChH,EAAE,CAAC;MACb,IAAIiH,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,EAAE;QACxBK,IAAI,CAACL,KAAK,CAAC;MACb,CAAC,MAAM,IAAIA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;QACtD,IAAIE,MAAM,GAAG,OAAOL,QAAQ,KAAK,UAAU,IAAI,CAACvE,aAAa,CAAC0E,KAAK,CAAC,EAAE;UACpEA,KAAK,GAAGS,MAAM,CAACT,KAAK,CAAC;QACvB,CAAC,MAAM,IAAIL,MAAM,CAACxO,KAAK,IAAI,OAAO0O,QAAQ,KAAK,UAAU,IAAIG,KAAK,CAACzN,OAAO,KAAK,KAAK,CAAC,EAAE;UACrF2B,eAAe,CAAC;AAC1B;AACA,gFAAgF,CAAC;QACzE;QACA,IAAIgM,MAAM,IAAIC,UAAU,EAAE;UACxBC,aAAa,CAACA,aAAa,CAAC7G,MAAM,GAAG,CAAC,CAAC,CAACmH,MAAM,IAAIV,KAAK;QACzD,CAAC,MAAM;UACLI,aAAa,CAAClH,IAAI,CAACgH,MAAM,GAAGS,QAAQ,CAAC,IAAI,EAAEX,KAAK,CAAC,GAAGA,KAAK,CAAC;QAC5D;QACAG,UAAU,GAAGD,MAAM;MACrB;IACF;EACF,CAAC;EACDG,IAAI,CAACN,QAAQ,CAAC;EACd,IAAID,SAAS,EAAE;IACb,IAAIH,MAAM,CAACxO,KAAK,IAAI0O,QAAQ,KAAK,OAAO,EAAE;MACxCe,uBAAuB,CAACd,SAAS,CAAC;IACpC;IACA,IAAIH,MAAM,CAACkB,OAAO,IAAIf,SAAS,CAACzC,GAAG,EAAE;MACnCA,GAAG,GAAGyC,SAAS,CAACzC,GAAG;IACrB;IACA,IAAIsC,MAAM,CAACmB,cAAc,IAAIhB,SAAS,CAAChP,IAAI,EAAE;MAC3CmP,QAAQ,GAAGH,SAAS,CAAChP,IAAI;IAC3B;IACA,IAAI6O,MAAM,CAACoB,SAAS,EAAE;MACpB,MAAMC,SAAS,GAAGlB,SAAS,CAACmB,SAAS,IAAInB,SAAS,CAACoB,KAAK;MACxD,IAAIF,SAAS,EAAE;QACblB,SAAS,CAACoB,KAAK,GAAG,OAAOF,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGvQ,MAAM,CAAC0Q,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,CAAEC,CAAC,IAAKL,SAAS,CAACK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MAC5H;IACF;EACF;EACA,IAAI3B,MAAM,CAACxO,KAAK,IAAIiP,aAAa,CAACmB,IAAI,CAACC,MAAM,CAAC,EAAE;IAC9CtN,eAAe,CAAC;AACpB;AACA,oFAAoF,CAAC;EACnF;EACA,IAAIyL,MAAM,CAAC8B,cAAc,IAAI,OAAO5B,QAAQ,KAAK,UAAU,EAAE;IAC3D,OAAOA,QAAQ,CACbC,SAAS,KAAK,IAAI,GAAG,CAAC,CAAC,GAAGA,SAAS,EACnCM,aAAa,EACbsB,WACF,CAAC;EACH;EACA,MAAMhD,KAAK,GAAGiC,QAAQ,CAACd,QAAQ,EAAE,IAAI,CAAC;EACtCnB,KAAK,CAACiD,OAAO,GAAG7B,SAAS;EACzB,IAAIM,aAAa,CAAC7G,MAAM,GAAG,CAAC,EAAE;IAC5BmF,KAAK,CAACkD,UAAU,GAAGxB,aAAa;EAClC;EACA,IAAIT,MAAM,CAACkB,OAAO,EAAE;IAClBnC,KAAK,CAACmD,KAAK,GAAGxE,GAAG;EACnB;EACA,IAAIsC,MAAM,CAACmB,cAAc,EAAE;IACzBpC,KAAK,CAACoD,MAAM,GAAG7B,QAAQ;EACzB;EACA,OAAOvB,KAAK;AACd,CAAC;AACD,IAAIiC,QAAQ,GAAGA,CAACoB,GAAG,EAAEC,IAAI,KAAK;EAC5B,MAAMtD,KAAK,GAAG;IACZnM,OAAO,EAAE,CAAC;IACV0P,KAAK,EAAEF,GAAG;IACVrB,MAAM,EAAEsB,IAAI;IACZE,KAAK,EAAE,IAAI;IACXN,UAAU,EAAE;EACd,CAAC;EACD,IAAIjC,MAAM,CAACwC,aAAa,EAAE;IACxBzD,KAAK,CAACiD,OAAO,GAAG,IAAI;EACtB;EACA,IAAIhC,MAAM,CAACkB,OAAO,EAAE;IAClBnC,KAAK,CAACmD,KAAK,GAAG,IAAI;EACpB;EACA,IAAIlC,MAAM,CAACmB,cAAc,EAAE;IACzBpC,KAAK,CAACoD,MAAM,GAAG,IAAI;EACrB;EACA,OAAOpD,KAAK;AACd,CAAC;AACD,IAAI0D,IAAI,GAAG,CAAC,CAAC;AACb,IAAIZ,MAAM,GAAIa,IAAI,IAAKA,IAAI,IAAIA,IAAI,CAACJ,KAAK,KAAKG,IAAI;AAClD,IAAIV,WAAW,GAAG;EAChBY,OAAO,EAAEA,CAACvC,QAAQ,EAAE9G,EAAE,KAAK8G,QAAQ,CAAC9D,GAAG,CAACsG,eAAe,CAAC,CAACD,OAAO,CAACrJ,EAAE,CAAC;EACpEgD,GAAG,EAAEA,CAAC8D,QAAQ,EAAE9G,EAAE,KAAK8G,QAAQ,CAAC9D,GAAG,CAACsG,eAAe,CAAC,CAACtG,GAAG,CAAChD,EAAE,CAAC,CAACgD,GAAG,CAACuG,gBAAgB;AACnF,CAAC;AACD,IAAID,eAAe,GAAIF,IAAI,KAAM;EAC/BI,MAAM,EAAEJ,IAAI,CAACV,OAAO;EACpBe,SAAS,EAAEL,IAAI,CAACT,UAAU;EAC1Be,IAAI,EAAEN,IAAI,CAACR,KAAK;EAChBe,KAAK,EAAEP,IAAI,CAACP,MAAM;EAClBe,IAAI,EAAER,IAAI,CAACJ,KAAK;EAChBa,KAAK,EAAET,IAAI,CAAC3B;AACd,CAAC,CAAC;AACF,IAAI8B,gBAAgB,GAAIH,IAAI,IAAK;EAC/B,IAAI,OAAOA,IAAI,CAACQ,IAAI,KAAK,UAAU,EAAE;IACnC,MAAM/C,SAAS,GAAG;MAAE,GAAGuC,IAAI,CAACI;IAAO,CAAC;IACpC,IAAIJ,IAAI,CAACM,IAAI,EAAE;MACb7C,SAAS,CAACzC,GAAG,GAAGgF,IAAI,CAACM,IAAI;IAC3B;IACA,IAAIN,IAAI,CAACO,KAAK,EAAE;MACd9C,SAAS,CAAChP,IAAI,GAAGuR,IAAI,CAACO,KAAK;IAC7B;IACA,OAAOhD,CAAC,CAACyC,IAAI,CAACQ,IAAI,EAAE/C,SAAS,EAAE,IAAGuC,IAAI,CAACK,SAAS,IAAI,EAAE,EAAC;EACzD;EACA,MAAMhE,KAAK,GAAGiC,QAAQ,CAAC0B,IAAI,CAACQ,IAAI,EAAER,IAAI,CAACS,KAAK,CAAC;EAC7CpE,KAAK,CAACiD,OAAO,GAAGU,IAAI,CAACI,MAAM;EAC3B/D,KAAK,CAACkD,UAAU,GAAGS,IAAI,CAACK,SAAS;EACjChE,KAAK,CAACmD,KAAK,GAAGQ,IAAI,CAACM,IAAI;EACvBjE,KAAK,CAACoD,MAAM,GAAGO,IAAI,CAACO,KAAK;EACzB,OAAOlE,KAAK;AACd,CAAC;AACD,IAAIkC,uBAAuB,GAAImC,QAAQ,IAAK;EAC1C,MAAMC,KAAK,GAAGvS,MAAM,CAAC0Q,IAAI,CAAC4B,QAAQ,CAAC;EACnC,MAAM1G,KAAK,GAAG2G,KAAK,CAACC,OAAO,CAAC,OAAO,CAAC;EACpC,IAAI5G,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB;EACF;EACA,MAAM6G,SAAS,GAAGF,KAAK,CAACC,OAAO,CAAC,MAAM,CAAC;EACvC,MAAME,QAAQ,GAAGH,KAAK,CAACC,OAAO,CAAC,KAAK,CAAC;EACrC,MAAMG,QAAQ,GAAGJ,KAAK,CAACC,OAAO,CAAC,KAAK,CAAC;EACrC,MAAMI,SAAS,GAAGL,KAAK,CAACC,OAAO,CAAC,MAAM,CAAC;EACvC,IAAI5G,KAAK,GAAG6G,SAAS,IAAI7G,KAAK,GAAG8G,QAAQ,IAAI9G,KAAK,GAAG+G,QAAQ,IAAI/G,KAAK,GAAGgH,SAAS,EAAE;IAClFjP,cAAc,CAAC,iFAAiF,CAAC;EACnG;AACF,CAAC;;AAED;AACA,IAAIkP,uBAAuB,GAAGA,CAACC,OAAO,EAAErG,OAAO,EAAEsG,MAAM,EAAEvR,OAAO,KAAK;EACnE,MAAMwR,UAAU,GAAGzG,UAAU,CAAC,eAAe,EAAEE,OAAO,CAAC;EACvD,MAAMwG,UAAU,GAAGH,OAAO,CAACG,UAAU;EACrC,MAAMC,gBAAgB,GAAG,EAAE;EAC3B,MAAMC,SAAS,GAAG,EAAE;EACpB,MAAMC,eAAe,GAAGhH,MAAM,CAAC9E,SAAS,IAAI2L,UAAU,GAAG,EAAE,GAAG,IAAI;EAClE,MAAMhF,KAAK,GAAGzM,OAAO,CAAC0M,OAAO,GAAGgC,QAAQ,CAACzD,OAAO,EAAE,IAAI,CAAC;EACvD,IAAI,CAACtG,GAAG,CAACkN,aAAa,EAAE;IACtBC,yBAAyB,CAACxN,GAAG,CAACyN,IAAI,EAAEpN,GAAG,CAACkN,aAAa,GAAG,eAAgB,IAAInR,GAAG,CAAC,CAAC,CAAC;EACpF;EACA4Q,OAAO,CAACzN,UAAU,CAAC,GAAG0N,MAAM;EAC5BD,OAAO,CAACU,eAAe,CAACnO,UAAU,CAAC;EACnCoO,aAAa,CAACxF,KAAK,EAAEiF,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,EAAEN,OAAO,EAAEA,OAAO,EAAEC,MAAM,CAAC;EAC5FG,gBAAgB,CAAC1H,GAAG,CAAEqE,CAAC,IAAK;IAC1B,MAAM6D,aAAa,GAAG7D,CAAC,CAAC8D,QAAQ,GAAG,GAAG,GAAG9D,CAAC,CAAC+D,QAAQ;IACnD,MAAMC,eAAe,GAAG1N,GAAG,CAACkN,aAAa,CAAC/S,GAAG,CAACoT,aAAa,CAAC;IAC5D,MAAM9B,IAAI,GAAG/B,CAAC,CAAC4B,KAAK;IACpB,IAAIoC,eAAe,IAAIxM,cAAc,IAAIwM,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;MACvEA,eAAe,CAACC,UAAU,CAACC,YAAY,CAACnC,IAAI,EAAEiC,eAAe,CAACG,WAAW,CAAC;IAC5E;IACA,IAAI,CAACf,UAAU,EAAE;MACfrB,IAAI,CAAC,MAAM,CAAC,GAAGnF,OAAO;MACtB,IAAIoH,eAAe,EAAE;QACnBjC,IAAI,CAAC,MAAM,CAAC,GAAGiC,eAAe;QAC9BjC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,GAAGA,IAAI;MAC7B;IACF;IACAzL,GAAG,CAACkN,aAAa,CAACY,MAAM,CAACP,aAAa,CAAC;EACzC,CAAC,CAAC;EACF,IAAItH,MAAM,CAAC9E,SAAS,IAAI2L,UAAU,EAAE;IAClCG,eAAe,CAAC5H,GAAG,CAAE0I,cAAc,IAAK;MACtC,IAAIA,cAAc,EAAE;QAClBjB,UAAU,CAACkB,WAAW,CAACD,cAAc,CAAC;MACxC;IACF,CAAC,CAAC;EACJ;EACAlB,UAAU,CAAC,CAAC;AACd,CAAC;AACD,IAAIS,aAAa,GAAGA,CAACW,WAAW,EAAElB,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,EAAEN,OAAO,EAAElB,IAAI,EAAEmB,MAAM,KAAK;EACxG,IAAIsB,aAAa;EACjB,IAAIC,WAAW;EACf,IAAIC,UAAU;EACd,IAAI1L,EAAE;EACN,IAAI+I,IAAI,CAAC4C,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACzCH,aAAa,GAAGzC,IAAI,CAACvG,YAAY,CAAC9F,gBAAgB,CAAC;IACnD,IAAI8O,aAAa,EAAE;MACjBC,WAAW,GAAGD,aAAa,CAACI,KAAK,CAAC,GAAG,CAAC;MACtC,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAKvB,MAAM,IAAIuB,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACvDC,UAAU,GAAG;UACXzS,OAAO,EAAE,CAAC;UACV6R,QAAQ,EAAEW,WAAW,CAAC,CAAC,CAAC;UACxBV,QAAQ,EAAEU,WAAW,CAAC,CAAC,CAAC;UACxBI,OAAO,EAAEJ,WAAW,CAAC,CAAC,CAAC;UACvBK,OAAO,EAAEL,WAAW,CAAC,CAAC,CAAC;UACvB9C,KAAK,EAAEI,IAAI,CAACnF,OAAO,CAACmI,WAAW,CAAC,CAAC;UACjCnD,KAAK,EAAEG,IAAI;UACXV,OAAO,EAAE,IAAI;UACbC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE,IAAI;UACXC,MAAM,EAAE,IAAI;UACZpB,MAAM,EAAE;QACV,CAAC;QACDiD,gBAAgB,CAACzK,IAAI,CAAC8L,UAAU,CAAC;QACjC3C,IAAI,CAAC4B,eAAe,CAACjO,gBAAgB,CAAC;QACtC,IAAI,CAAC6O,WAAW,CAACjD,UAAU,EAAE;UAC3BiD,WAAW,CAACjD,UAAU,GAAG,EAAE;QAC7B;QACAiD,WAAW,CAACjD,UAAU,CAACoD,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU;QACvDH,WAAW,GAAGG,UAAU;QACxB,IAAInB,eAAe,IAAImB,UAAU,CAACG,OAAO,KAAK,GAAG,EAAE;UACjDtB,eAAe,CAACmB,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU,CAAC9C,KAAK;QACxD;MACF;IACF;IACA,IAAIG,IAAI,CAACqB,UAAU,EAAE;MACnB,KAAKpK,EAAE,GAAG+I,IAAI,CAACqB,UAAU,CAAC4B,UAAU,CAAC/L,MAAM,GAAG,CAAC,EAAED,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;QAC9D4K,aAAa,CACXW,WAAW,EACXlB,gBAAgB,EAChBC,SAAS,EACTC,eAAe,EACfN,OAAO,EACPlB,IAAI,CAACqB,UAAU,CAAC4B,UAAU,CAAChM,EAAE,CAAC,EAC9BkK,MACF,CAAC;MACH;IACF;IACA,KAAKlK,EAAE,GAAG+I,IAAI,CAACiD,UAAU,CAAC/L,MAAM,GAAG,CAAC,EAAED,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;MACnD4K,aAAa,CACXW,WAAW,EACXlB,gBAAgB,EAChBC,SAAS,EACTC,eAAe,EACfN,OAAO,EACPlB,IAAI,CAACiD,UAAU,CAAChM,EAAE,CAAC,EACnBkK,MACF,CAAC;IACH;EACF,CAAC,MAAM,IAAInB,IAAI,CAAC4C,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IAChDF,WAAW,GAAG1C,IAAI,CAACkD,SAAS,CAACL,KAAK,CAAC,GAAG,CAAC;IACvC,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAKvB,MAAM,IAAIuB,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACvDD,aAAa,GAAGC,WAAW,CAAC,CAAC,CAAC;MAC9BC,UAAU,GAAG;QACXzS,OAAO,EAAE,CAAC;QACV6R,QAAQ,EAAEW,WAAW,CAAC,CAAC,CAAC;QACxBV,QAAQ,EAAEU,WAAW,CAAC,CAAC,CAAC;QACxBI,OAAO,EAAEJ,WAAW,CAAC,CAAC,CAAC;QACvBK,OAAO,EAAEL,WAAW,CAAC,CAAC,CAAC;QACvB7C,KAAK,EAAEG,IAAI;QACXV,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZG,KAAK,EAAE,IAAI;QACXvB,MAAM,EAAE;MACV,CAAC;MACD,IAAIoE,aAAa,KAAKjP,YAAY,EAAE;QAClCmP,UAAU,CAAC9C,KAAK,GAAGG,IAAI,CAACoC,WAAW;QACnC,IAAIO,UAAU,CAAC9C,KAAK,IAAI8C,UAAU,CAAC9C,KAAK,CAAC+C,QAAQ,KAAK,CAAC,CAAC,gBAAgB;UACtED,UAAU,CAACtE,MAAM,GAAGsE,UAAU,CAAC9C,KAAK,CAACsD,WAAW;UAChD7B,gBAAgB,CAACzK,IAAI,CAAC8L,UAAU,CAAC;UACjC3C,IAAI,CAACoD,MAAM,CAAC,CAAC;UACb,IAAI,CAACZ,WAAW,CAACjD,UAAU,EAAE;YAC3BiD,WAAW,CAACjD,UAAU,GAAG,EAAE;UAC7B;UACAiD,WAAW,CAACjD,UAAU,CAACoD,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU;UACvD,IAAInB,eAAe,IAAImB,UAAU,CAACG,OAAO,KAAK,GAAG,EAAE;YACjDtB,eAAe,CAACmB,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU,CAAC9C,KAAK;UACxD;QACF;MACF,CAAC,MAAM,IAAI8C,UAAU,CAACZ,QAAQ,KAAKZ,MAAM,EAAE;QACzC,IAAIsB,aAAa,KAAKlP,YAAY,EAAE;UAClCoP,UAAU,CAAC/C,KAAK,GAAG,MAAM;UACzB,IAAI8C,WAAW,CAAC,CAAC,CAAC,EAAE;YAClB1C,IAAI,CAAC,MAAM,CAAC,GAAG2C,UAAU,CAAClD,MAAM,GAAGiD,WAAW,CAAC,CAAC,CAAC;UACnD,CAAC,MAAM;YACL1C,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;UACnB;UACAA,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;UACnB,IAAIxF,MAAM,CAAC9E,SAAS,IAAI8L,eAAe,EAAE;YACvCmB,UAAU,CAAC9C,KAAK,GAAG3L,GAAG,CAACmP,aAAa,CAACV,UAAU,CAAC/C,KAAK,CAAC;YACtD,IAAI+C,UAAU,CAAClD,MAAM,EAAE;cACrBkD,UAAU,CAAC9C,KAAK,CAACyD,YAAY,CAAC,MAAM,EAAEX,UAAU,CAAClD,MAAM,CAAC;YAC1D;YACAO,IAAI,CAACkC,UAAU,CAACC,YAAY,CAACQ,UAAU,CAAC9C,KAAK,EAAEG,IAAI,CAAC;YACpDA,IAAI,CAACoD,MAAM,CAAC,CAAC;YACb,IAAIT,UAAU,CAACG,OAAO,KAAK,GAAG,EAAE;cAC9BtB,eAAe,CAACmB,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU,CAAC9C,KAAK;YACxD;UACF;UACA0B,SAAS,CAAC1K,IAAI,CAAC8L,UAAU,CAAC;UAC1B,IAAI,CAACH,WAAW,CAACjD,UAAU,EAAE;YAC3BiD,WAAW,CAACjD,UAAU,GAAG,EAAE;UAC7B;UACAiD,WAAW,CAACjD,UAAU,CAACoD,UAAU,CAACI,OAAO,CAAC,GAAGJ,UAAU;QACzD,CAAC,MAAM,IAAIF,aAAa,KAAKpP,cAAc,EAAE;UAC3C,IAAImH,MAAM,CAAC9E,SAAS,IAAI8L,eAAe,EAAE;YACvCxB,IAAI,CAACoD,MAAM,CAAC,CAAC;UACf,CAAC,MAAM,IAAI5I,MAAM,CAACiE,cAAc,EAAE;YAChCyC,OAAO,CAAC,MAAM,CAAC,GAAGlB,IAAI;YACtBA,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;UACrB;QACF;MACF;IACF;EACF,CAAC,MAAM,IAAIwC,WAAW,IAAIA,WAAW,CAAC5C,KAAK,KAAK,OAAO,EAAE;IACvD,MAAMvD,KAAK,GAAGiC,QAAQ,CAAC,IAAI,EAAE0B,IAAI,CAACmD,WAAW,CAAC;IAC9C9G,KAAK,CAACwD,KAAK,GAAGG,IAAI;IAClB3D,KAAK,CAAC0G,OAAO,GAAG,GAAG;IACnBP,WAAW,CAACjD,UAAU,GAAG,CAAClD,KAAK,CAAC;EAClC;AACF,CAAC;AACD,IAAIqF,yBAAyB,GAAGA,CAAC1B,IAAI,EAAEuD,WAAW,KAAK;EACrD,IAAIvD,IAAI,CAAC4C,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACzC,IAAI3L,EAAE,GAAG,CAAC;IACV,IAAI+I,IAAI,CAACqB,UAAU,EAAE;MACnB,OAAOpK,EAAE,GAAG+I,IAAI,CAACqB,UAAU,CAAC4B,UAAU,CAAC/L,MAAM,EAAED,EAAE,EAAE,EAAE;QACnDyK,yBAAyB,CAAC1B,IAAI,CAACqB,UAAU,CAAC4B,UAAU,CAAChM,EAAE,CAAC,EAAEsM,WAAW,CAAC;MACxE;IACF;IACA,KAAKtM,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG+I,IAAI,CAACiD,UAAU,CAAC/L,MAAM,EAAED,EAAE,EAAE,EAAE;MAC9CyK,yBAAyB,CAAC1B,IAAI,CAACiD,UAAU,CAAChM,EAAE,CAAC,EAAEsM,WAAW,CAAC;IAC7D;EACF,CAAC,MAAM,IAAIvD,IAAI,CAAC4C,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IAChD,MAAMF,WAAW,GAAG1C,IAAI,CAACkD,SAAS,CAACL,KAAK,CAAC,GAAG,CAAC;IAC7C,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAKpP,eAAe,EAAE;MACtCiQ,WAAW,CAAC1T,GAAG,CAAC6S,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,WAAW,CAAC,CAAC,CAAC,EAAE1C,IAAI,CAAC;MAC5DA,IAAI,CAACkD,SAAS,GAAG,EAAE;MACnBlD,IAAI,CAAC,MAAM,CAAC,GAAG0C,WAAW,CAAC,CAAC,CAAC;IAC/B;EACF;AACF,CAAC;;AAED;AACA,SAAS9T,KAAK,IAAI4U,OAAO,QAAQ,iCAAiC;;AAElE;AACA,IAAIC,WAAW,GAAIvS,GAAG,IAAKiC,mBAAmB,CAACyG,GAAG,CAAElF,EAAE,IAAKA,EAAE,CAACxD,GAAG,CAAC,CAAC,CAACwS,IAAI,CAAE5R,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;AACpF,IAAI6R,OAAO,GAAIvR,OAAO,IAAKe,mBAAmB,CAAC0D,IAAI,CAACzE,OAAO,CAAC;AAC5D,IAAIwR,OAAO,GAAInU,GAAG,IAAKD,UAAU,CAACC,GAAG,CAAC,CAACqD,UAAU;;AAEjD;AACA,SAASlE,KAAK,IAAIiV,OAAO,QAAQ,iCAAiC;;AAElE;AACA,SAASjV,KAAK,IAAIkV,OAAO,QAAQ,iCAAiC;;AAElE;AACA,SAASlV,KAAK,IAAImV,OAAO,QAAQ,iCAAiC;AAClE,IAAIC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,QAAQ,KAAK;EAChD,IAAID,SAAS,IAAI,IAAI,IAAI,CAAChL,aAAa,CAACgL,SAAS,CAAC,EAAE;IAClD,IAAIF,OAAO,CAACI,WAAW,IAAID,QAAQ,GAAG,CAAC,CAAC,eAAe;MACrD,OAAOD,SAAS,KAAK,OAAO,GAAG,KAAK,GAAGA,SAAS,KAAK,EAAE,IAAI,CAAC,CAACA,SAAS;IACxE;IACA,IAAIF,OAAO,CAACK,UAAU,IAAIF,QAAQ,GAAG,CAAC,CAAC,cAAc;MACnD,OAAOG,UAAU,CAACJ,SAAS,CAAC;IAC9B;IACA,IAAIF,OAAO,CAACO,UAAU,IAAIJ,QAAQ,GAAG,CAAC,CAAC,cAAc;MACnD,OAAO9F,MAAM,CAAC6F,SAAS,CAAC;IAC1B;IACA,OAAOA,SAAS;EAClB;EACA,OAAOA,SAAS;AAClB,CAAC;;AAED;AACA,SAASrV,KAAK,IAAI2V,OAAO,EAAEtM,SAAS,QAAQ,iCAAiC;;AAE7E;AACA,SAASrJ,KAAK,IAAI4V,OAAO,QAAQ,iCAAiC;;AAElE;AACA,SAAS5V,KAAK,IAAI6V,OAAO,QAAQ,iCAAiC;AAClE,IAAIC,UAAU,GAAIjV,GAAG,IAAKgV,OAAO,CAAChU,QAAQ,GAAGjB,UAAU,CAACC,GAAG,CAAC,CAACU,aAAa,GAAGV,GAAG;;AAEhF;AACA,IAAIkV,WAAW,GAAGA,CAAClV,GAAG,EAAEhB,IAAI,EAAE6M,KAAK,KAAK;EACtC,MAAMpK,GAAG,GAAGwT,UAAU,CAACjV,GAAG,CAAC;EAC3B,OAAO;IACLmV,IAAI,EAAGC,MAAM,IAAK;MAChB,IAAIL,OAAO,CAAC1V,KAAK,IAAI,CAACoC,GAAG,CAAC4T,WAAW,EAAE;QACrC/S,cAAc,CAAC,QAAQtD,IAAI,iFAAiF,CAAC;MAC/G;MACA,OAAOsW,SAAS,CAAC7T,GAAG,EAAEzC,IAAI,EAAE;QAC1BuW,OAAO,EAAE,CAAC,EAAE1J,KAAK,GAAG,CAAC,CAAC,cAAc;QACpC2J,QAAQ,EAAE,CAAC,EAAE3J,KAAK,GAAG,CAAC,CAAC,eAAe;QACtC4J,UAAU,EAAE,CAAC,EAAE5J,KAAK,GAAG,CAAC,CAAC,kBAAkB;QAC3CuJ;MACF,CAAC,CAAC;IACJ;EACF,CAAC;AACH,CAAC;AACD,IAAIE,SAAS,GAAGA,CAAC7T,GAAG,EAAEzC,IAAI,EAAEuG,IAAI,KAAK;EACnC,MAAMmQ,EAAE,GAAG5Q,GAAG,CAACa,EAAE,CAAC3G,IAAI,EAAEuG,IAAI,CAAC;EAC7B9D,GAAG,CAACkU,aAAa,CAACD,EAAE,CAAC;EACrB,OAAOA,EAAE;AACX,CAAC;;AAED;AACA,SAASvW,KAAK,IAAIyW,OAAO,QAAQ,iCAAiC;AAClE,IAAIC,iBAAiB,GAAG,eAAgB,IAAI/V,OAAO,CAAC,CAAC;AACrD,IAAIgW,aAAa,GAAGA,CAACC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,KAAK;EAClD,IAAIC,KAAK,GAAGzS,MAAM,CAACxE,GAAG,CAAC8W,QAAQ,CAAC;EAChC,IAAIxP,gCAAgC,IAAI0P,OAAO,EAAE;IAC/CC,KAAK,GAAGA,KAAK,IAAI,IAAIzP,aAAa,CAAC,CAAC;IACpC,IAAI,OAAOyP,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGF,OAAO;IACjB,CAAC,MAAM;MACLE,KAAK,CAACxP,WAAW,CAACsP,OAAO,CAAC;IAC5B;EACF,CAAC,MAAM;IACLE,KAAK,GAAGF,OAAO;EACjB;EACAvS,MAAM,CAACrD,GAAG,CAAC2V,QAAQ,EAAEG,KAAK,CAAC;AAC7B,CAAC;AACD,IAAIC,QAAQ,GAAGA,CAACC,kBAAkB,EAAE5V,OAAO,EAAE6V,IAAI,KAAK;EACpD,IAAIzM,EAAE;EACN,MAAMmM,QAAQ,GAAGO,UAAU,CAAC9V,OAAO,EAAE6V,IAAI,CAAC;EAC1C,MAAMH,KAAK,GAAGzS,MAAM,CAACxE,GAAG,CAAC8W,QAAQ,CAAC;EAClC,IAAI,CAACH,OAAO,CAACW,YAAY,EAAE;IACzB,OAAOR,QAAQ;EACjB;EACAK,kBAAkB,GAAGA,kBAAkB,CAACjD,QAAQ,KAAK,EAAE,CAAC,yBAAyBiD,kBAAkB,GAAG3R,GAAG;EACzG,IAAIyR,KAAK,EAAE;IACT,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7BE,kBAAkB,GAAGA,kBAAkB,CAACzR,IAAI,IAAIyR,kBAAkB;MAClE,IAAII,aAAa,GAAGX,iBAAiB,CAAC5W,GAAG,CAACmX,kBAAkB,CAAC;MAC7D,IAAIK,QAAQ;MACZ,IAAI,CAACD,aAAa,EAAE;QAClBX,iBAAiB,CAACzV,GAAG,CAACgW,kBAAkB,EAAEI,aAAa,GAAG,eAAgB,IAAIE,GAAG,CAAC,CAAC,CAAC;MACtF;MACA,IAAI,CAACF,aAAa,CAACG,GAAG,CAACZ,QAAQ,CAAC,EAAE;QAChC,IAAIH,OAAO,CAACgB,iBAAiB,IAAIR,kBAAkB,CAACS,IAAI,KAAKJ,QAAQ,GAAGL,kBAAkB,CAACrM,aAAa,CAAC,IAAI9F,iBAAiB,KAAK8R,QAAQ,IAAI,CAAC,CAAC,EAAE;UACjJU,QAAQ,CAACK,SAAS,GAAGZ,KAAK;QAC5B,CAAC,MAAM;UACLO,QAAQ,GAAGhS,GAAG,CAACmP,aAAa,CAAC,OAAO,CAAC;UACrC6C,QAAQ,CAACK,SAAS,GAAGZ,KAAK;UAC1B,MAAMa,KAAK,GAAG,CAACnN,EAAE,GAAG9E,GAAG,CAACkS,OAAO,KAAK,IAAI,GAAGpN,EAAE,GAAGF,wBAAwB,CAACjF,GAAG,CAAC;UAC7E,IAAIsS,KAAK,IAAI,IAAI,EAAE;YACjBN,QAAQ,CAAC5C,YAAY,CAAC,OAAO,EAAEkD,KAAK,CAAC;UACvC;UACA,IAAI,CAACnB,OAAO,CAACqB,iBAAiB,IAAIrB,OAAO,CAACjW,oBAAoB,KAAKa,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;YACnHgW,QAAQ,CAAC5C,YAAY,CAAC5P,iBAAiB,EAAE8R,QAAQ,CAAC;UACpD;UACA,MAAMmB,WAAW;UACf;AACZ;AACA;UACY,EAAE1W,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,6BAA6B;UAAI;AACnE;AACA;UACYD,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,gCAAgC2V,kBAAkB,CAACrI,QAAQ,KAAK,MACrF;UACD,IAAImJ,WAAW,EAAE;YACfd,kBAAkB,CAAC1D,YAAY,CAAC+D,QAAQ,EAAEL,kBAAkB,CAACrM,aAAa,CAAC,MAAM,CAAC,CAAC;UACrF;QACF;QACA,IAAIvJ,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,yBAAyB;UAC/CgW,QAAQ,CAACK,SAAS,IAAI1S,WAAW;QACnC;QACA,IAAIoS,aAAa,EAAE;UACjBA,aAAa,CAACW,GAAG,CAACpB,QAAQ,CAAC;QAC7B;MACF;IACF,CAAC,MAAM,IAAIH,OAAO,CAACpP,gBAAgB,IAAI,CAAC4P,kBAAkB,CAACgB,kBAAkB,CAACC,QAAQ,CAACnB,KAAK,CAAC,EAAE;MAC7FE,kBAAkB,CAACgB,kBAAkB,GAAG,CAAC,GAAGhB,kBAAkB,CAACgB,kBAAkB,EAAElB,KAAK,CAAC;IAC3F;EACF;EACA,OAAOH,QAAQ;AACjB,CAAC;AACD,IAAIQ,YAAY,GAAIpW,OAAO,IAAK;EAC9B,MAAMK,OAAO,GAAGL,OAAO,CAACQ,SAAS;EACjC,MAAMc,GAAG,GAAGtB,OAAO,CAACO,aAAa;EACjC,MAAMmL,KAAK,GAAGrL,OAAO,CAACC,OAAO;EAC7B,MAAM6W,eAAe,GAAGpM,UAAU,CAAC,cAAc,EAAE1K,OAAO,CAACyC,SAAS,CAAC;EACrE,MAAM8S,QAAQ,GAAGI,QAAQ,CACvBP,OAAO,CAAC3P,SAAS,IAAID,cAAc,IAAIvE,GAAG,CAACmQ,UAAU,GAAGnQ,GAAG,CAACmQ,UAAU,GAAGnQ,GAAG,CAAC8V,WAAW,CAAC,CAAC,EAC1F/W,OAAO,EACPL,OAAO,CAACkD,UACV,CAAC;EACD,IAAI,CAACuS,OAAO,CAAC3P,SAAS,IAAI2P,OAAO,CAAC4B,MAAM,KAAK5B,OAAO,CAAC6B,cAAc,IAAI5L,KAAK,GAAG,EAAE,CAAC,kCAAkCA,KAAK,GAAG,CAAC,CAAC,8BAA8B;IAC1JpK,GAAG,CAAC,MAAM,CAAC,GAAGsU,QAAQ;IACtBtU,GAAG,CAACiW,SAAS,CAACP,GAAG,CAACpB,QAAQ,GAAG,IAAI,CAAC;IAClC,IAAIH,OAAO,CAAC4B,MAAM,IAAI3L,KAAK,GAAG,CAAC,CAAC,8BAA8B;MAC5DpK,GAAG,CAACiW,SAAS,CAACP,GAAG,CAACpB,QAAQ,GAAG,IAAI,CAAC;IACpC;EACF;EACAuB,eAAe,CAAC,CAAC;AACnB,CAAC;AACD,IAAIhB,UAAU,GAAGA,CAACqB,GAAG,EAAEtB,IAAI,KAAK,KAAK,IAAIT,OAAO,CAACS,IAAI,IAAIA,IAAI,IAAIsB,GAAG,CAAClX,OAAO,GAAG,EAAE,CAAC,gBAAgBkX,GAAG,CAAC1U,SAAS,GAAG,GAAG,GAAGoT,IAAI,GAAGsB,GAAG,CAAC1U,SAAS,CAAC;;AAE7I;AACA,SAAS9D,KAAK,IAAIyY,OAAO,QAAQ,iCAAiC;;AAElE;AACA,SAASzY,KAAK,IAAI0Y,OAAO,QAAQ,iCAAiC;;AAElE;AACA,SAAS1Y,KAAK,IAAI2Y,OAAO,QAAQ,iCAAiC;AAClE,IAAIC,WAAW,GAAGA,CAACtW,GAAG,EAAEC,UAAU,EAAEsW,QAAQ,EAAEC,QAAQ,EAAEC,KAAK,EAAErM,KAAK,KAAK;EACvE,IAAImM,QAAQ,KAAKC,QAAQ,EAAE;IACzB,IAAIE,MAAM,GAAG3W,iBAAiB,CAACC,GAAG,EAAEC,UAAU,CAAC;IAC/C,IAAI0W,EAAE,GAAG1W,UAAU,CAAC6R,WAAW,CAAC,CAAC;IACjC,IAAIuE,OAAO,CAAC7I,SAAS,IAAIvN,UAAU,KAAK,OAAO,EAAE;MAC/C,MAAMgW,SAAS,GAAGjW,GAAG,CAACiW,SAAS;MAC/B,MAAMW,UAAU,GAAGC,cAAc,CAACN,QAAQ,CAAC;MAC3C,MAAMO,UAAU,GAAGD,cAAc,CAACL,QAAQ,CAAC;MAC3CP,SAAS,CAAC/D,MAAM,CAAC,GAAG0E,UAAU,CAAC/I,MAAM,CAAEd,CAAC,IAAKA,CAAC,IAAI,CAAC+J,UAAU,CAAClB,QAAQ,CAAC7I,CAAC,CAAC,CAAC,CAAC;MAC3EkJ,SAAS,CAACP,GAAG,CAAC,GAAGoB,UAAU,CAACjJ,MAAM,CAAEd,CAAC,IAAKA,CAAC,IAAI,CAAC6J,UAAU,CAAChB,QAAQ,CAAC7I,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC,MAAM,IAAIsJ,OAAO,CAACU,SAAS,IAAI9W,UAAU,KAAK,OAAO,EAAE;MACtD,IAAIoW,OAAO,CAACW,SAAS,EAAE;QACrB,KAAK,MAAMC,IAAI,IAAIV,QAAQ,EAAE;UAC3B,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACS,IAAI,CAAC,IAAI,IAAI,EAAE;YACvC,IAAI,CAACZ,OAAO,CAACb,iBAAiB,IAAIyB,IAAI,CAACrB,QAAQ,CAAC,GAAG,CAAC,EAAE;cACpD5V,GAAG,CAACyU,KAAK,CAACyC,cAAc,CAACD,IAAI,CAAC;YAChC,CAAC,MAAM;cACLjX,GAAG,CAACyU,KAAK,CAACwC,IAAI,CAAC,GAAG,EAAE;YACtB;UACF;QACF;MACF;MACA,KAAK,MAAMA,IAAI,IAAIT,QAAQ,EAAE;QAC3B,IAAI,CAACD,QAAQ,IAAIC,QAAQ,CAACS,IAAI,CAAC,KAAKV,QAAQ,CAACU,IAAI,CAAC,EAAE;UAClD,IAAI,CAACZ,OAAO,CAACb,iBAAiB,IAAIyB,IAAI,CAACrB,QAAQ,CAAC,GAAG,CAAC,EAAE;YACpD5V,GAAG,CAACyU,KAAK,CAAC0C,WAAW,CAACF,IAAI,EAAET,QAAQ,CAACS,IAAI,CAAC,CAAC;UAC7C,CAAC,MAAM;YACLjX,GAAG,CAACyU,KAAK,CAACwC,IAAI,CAAC,GAAGT,QAAQ,CAACS,IAAI,CAAC;UAClC;QACF;MACF;IACF,CAAC,MAAM,IAAIZ,OAAO,CAAC/I,OAAO,IAAIrN,UAAU,KAAK,KAAK,EAAE,CACpD,CAAC,MAAM,IAAIoW,OAAO,CAACe,OAAO,IAAInX,UAAU,KAAK,KAAK,EAAE;MAClD,IAAIuW,QAAQ,EAAE;QACZA,QAAQ,CAACxW,GAAG,CAAC;MACf;IACF,CAAC,MAAM,IAAIqW,OAAO,CAACgB,YAAY,KAAKhB,OAAO,CAAC9W,QAAQ,GAAG,CAACmX,MAAM,GAAG,CAAC1W,GAAG,CAACsX,gBAAgB,CAACrX,UAAU,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACrJ,IAAIA,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACzBA,UAAU,GAAGA,UAAU,CAACsX,KAAK,CAAC,CAAC,CAAC;MAClC,CAAC,MAAM,IAAIxX,iBAAiB,CAACgD,GAAG,EAAE4T,EAAE,CAAC,EAAE;QACrC1W,UAAU,GAAG0W,EAAE,CAACY,KAAK,CAAC,CAAC,CAAC;MAC1B,CAAC,MAAM;QACLtX,UAAU,GAAG0W,EAAE,CAAC,CAAC,CAAC,GAAG1W,UAAU,CAACsX,KAAK,CAAC,CAAC,CAAC;MAC1C;MACA,IAAIhB,QAAQ,IAAIC,QAAQ,EAAE;QACxB,MAAMgB,OAAO,GAAGvX,UAAU,CAACwX,QAAQ,CAACC,oBAAoB,CAAC;QACzDzX,UAAU,GAAGA,UAAU,CAACwB,OAAO,CAACkW,mBAAmB,EAAE,EAAE,CAAC;QACxD,IAAIpB,QAAQ,EAAE;UACZlT,GAAG,CAACW,GAAG,CAAChE,GAAG,EAAEC,UAAU,EAAEsW,QAAQ,EAAEiB,OAAO,CAAC;QAC7C;QACA,IAAIhB,QAAQ,EAAE;UACZnT,GAAG,CAACM,GAAG,CAAC3D,GAAG,EAAEC,UAAU,EAAEuW,QAAQ,EAAEgB,OAAO,CAAC;QAC7C;MACF;IACF,CAAC,MAAM,IAAInB,OAAO,CAACuB,cAAc,EAAE;MACjC,MAAMC,SAAS,GAAG9P,aAAa,CAACyO,QAAQ,CAAC;MACzC,IAAI,CAACE,MAAM,IAAImB,SAAS,IAAIrB,QAAQ,KAAK,IAAI,KAAK,CAACC,KAAK,EAAE;QACxD,IAAI;UACF,IAAI,CAACzW,GAAG,CAAC2J,OAAO,CAACiM,QAAQ,CAAC,GAAG,CAAC,EAAE;YAC9B,MAAMkC,CAAC,GAAGtB,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAGA,QAAQ;YAC1C,IAAIvW,UAAU,KAAK,MAAM,EAAE;cACzByW,MAAM,GAAG,KAAK;YAChB,CAAC,MAAM,IAAIH,QAAQ,IAAI,IAAI,IAAIvW,GAAG,CAACC,UAAU,CAAC,IAAI6X,CAAC,EAAE;cACnD9X,GAAG,CAACC,UAAU,CAAC,GAAG6X,CAAC;YACrB;UACF,CAAC,MAAM;YACL9X,GAAG,CAACC,UAAU,CAAC,GAAGuW,QAAQ;UAC5B;QACF,CAAC,CAAC,OAAOlW,CAAC,EAAE,CACZ;MACF;MACA,IAAIyX,KAAK,GAAG,KAAK;MACjB,IAAI1B,OAAO,CAAC2B,SAAS,EAAE;QACrB,IAAIrB,EAAE,MAAMA,EAAE,GAAGA,EAAE,CAAClV,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,EAAE;UAC7CxB,UAAU,GAAG0W,EAAE;UACfoB,KAAK,GAAG,IAAI;QACd;MACF;MACA,IAAIvB,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,KAAK,EAAE;QAC1C,IAAIA,QAAQ,KAAK,KAAK,IAAIxW,GAAG,CAACuI,YAAY,CAACtI,UAAU,CAAC,KAAK,EAAE,EAAE;UAC7D,IAAIoW,OAAO,CAAC2B,SAAS,IAAID,KAAK,EAAE;YAC9B/X,GAAG,CAACiY,iBAAiB,CAACrV,QAAQ,EAAE3C,UAAU,CAAC;UAC7C,CAAC,MAAM;YACLD,GAAG,CAAC0Q,eAAe,CAACzQ,UAAU,CAAC;UACjC;QACF;MACF,CAAC,MAAM,IAAI,CAAC,CAACyW,MAAM,IAAItM,KAAK,GAAG,CAAC,CAAC,gBAAgBqM,KAAK,KAAK,CAACoB,SAAS,EAAE;QACrErB,QAAQ,GAAGA,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAGA,QAAQ;QAC5C,IAAIH,OAAO,CAAC2B,SAAS,IAAID,KAAK,EAAE;UAC9B/X,GAAG,CAACkY,cAAc,CAACtV,QAAQ,EAAE3C,UAAU,EAAEuW,QAAQ,CAAC;QACpD,CAAC,MAAM;UACLxW,GAAG,CAACoS,YAAY,CAACnS,UAAU,EAAEuW,QAAQ,CAAC;QACxC;MACF;IACF;EACF;AACF,CAAC;AACD,IAAI2B,mBAAmB,GAAG,IAAI;AAC9B,IAAItB,cAAc,GAAI/N,KAAK,IAAK,CAACA,KAAK,GAAG,EAAE,GAAGA,KAAK,CAAC6I,KAAK,CAACwG,mBAAmB,CAAC;AAC9E,IAAIT,oBAAoB,GAAG,SAAS;AACpC,IAAIC,mBAAmB,GAAG,IAAIS,MAAM,CAACV,oBAAoB,GAAG,GAAG,CAAC;;AAEhE;AACA,IAAIW,aAAa,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,KAAK;EACtD,MAAMxY,GAAG,GAAGuY,QAAQ,CAAC5J,KAAK,CAAC+C,QAAQ,KAAK,EAAE,CAAC,0BAA0B6G,QAAQ,CAAC5J,KAAK,CAACyG,IAAI,GAAGmD,QAAQ,CAAC5J,KAAK,CAACyG,IAAI,GAAGmD,QAAQ,CAAC5J,KAAK;EAC/H,MAAM8J,aAAa,GAAGH,QAAQ,IAAIA,QAAQ,CAAClK,OAAO,IAAIzG,SAAS;EAC/D,MAAM+Q,aAAa,GAAGH,QAAQ,CAACnK,OAAO,IAAIzG,SAAS;EACnD,IAAIyO,OAAO,CAACY,SAAS,EAAE;IACrB,KAAK,MAAM/W,UAAU,IAAI0Y,eAAe,CAACzb,MAAM,CAAC0Q,IAAI,CAAC6K,aAAa,CAAC,CAAC,EAAE;MACpE,IAAI,EAAExY,UAAU,IAAIyY,aAAa,CAAC,EAAE;QAClCpC,WAAW,CAACtW,GAAG,EAAEC,UAAU,EAAEwY,aAAa,CAACxY,UAAU,CAAC,EAAE,KAAK,CAAC,EAAEuY,UAAU,EAAED,QAAQ,CAACvZ,OAAO,CAAC;MAC/F;IACF;EACF;EACA,KAAK,MAAMiB,UAAU,IAAI0Y,eAAe,CAACzb,MAAM,CAAC0Q,IAAI,CAAC8K,aAAa,CAAC,CAAC,EAAE;IACpEpC,WAAW,CAACtW,GAAG,EAAEC,UAAU,EAAEwY,aAAa,CAACxY,UAAU,CAAC,EAAEyY,aAAa,CAACzY,UAAU,CAAC,EAAEuY,UAAU,EAAED,QAAQ,CAACvZ,OAAO,CAAC;EAClH;AACF,CAAC;AACD,SAAS2Z,eAAeA,CAACC,SAAS,EAAE;EAClC,OAAOA,SAAS,CAAChD,QAAQ,CAAC,KAAK,CAAC;EAC9B;EACA,CAAC,GAAGgD,SAAS,CAAC/K,MAAM,CAAEgL,IAAI,IAAKA,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK,CAAC;EAEtD;EACAD,SACD;AACH;;AAEA;AACA,IAAIE,OAAO;AACX,IAAIC,UAAU;AACd,IAAIC,WAAW;AACf,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,2BAA2B,GAAG,KAAK;AACvC,IAAIC,iBAAiB,GAAG,KAAK;AAC7B,IAAIC,SAAS,GAAG,KAAK;AACrB,IAAIC,SAAS,GAAGA,CAACC,cAAc,EAAEC,cAAc,EAAEC,UAAU,EAAEC,SAAS,KAAK;EACzE,IAAItR,EAAE;EACN,MAAMuR,SAAS,GAAGH,cAAc,CAAClL,UAAU,CAACmL,UAAU,CAAC;EACvD,IAAIzT,EAAE,GAAG,CAAC;EACV,IAAI/F,GAAG;EACP,IAAI2Z,SAAS;EACb,IAAIC,QAAQ;EACZ,IAAIzD,OAAO,CAAC5I,cAAc,IAAI,CAAC0L,kBAAkB,EAAE;IACjDE,iBAAiB,GAAG,IAAI;IACxB,IAAIO,SAAS,CAAChL,KAAK,KAAK,MAAM,EAAE;MAC9B,IAAIoK,OAAO,EAAE;QACXW,SAAS,CAACxD,SAAS,CAACP,GAAG,CAACoD,OAAO,GAAG,IAAI,CAAC;MACzC;MACAY,SAAS,CAAC1a,OAAO,IAAI0a,SAAS,CAACrL,UAAU;MACvC;MACA;MACA,CAAC,CAAC;MAEF;MACA;MACA;MACA,CAAC,CAAC,qBACH;IACH;EACF;EACA,IAAI8H,OAAO,CAACvY,KAAK,IAAI8b,SAAS,CAAC/K,KAAK,EAAE;IACpChO,eAAe,CACb,WAAW+Y,SAAS,CAACvM,MAAM,KAAK,IAAI,GAAG,IAAIuM,SAAS,CAACvM,MAAM,QAAQ,GAAG,IAAIuM,SAAS,CAAChL,KAAK,WAAW,mTACtG,CAAC;EACH;EACA,IAAIyH,OAAO,CAAC0D,QAAQ,IAAIH,SAAS,CAACvM,MAAM,KAAK,IAAI,EAAE;IACjDnN,GAAG,GAAG0Z,SAAS,CAAC/K,KAAK,GAAG3L,GAAG,CAAC8W,cAAc,CAACJ,SAAS,CAACvM,MAAM,CAAC;EAC9D,CAAC,MAAM,IAAIgJ,OAAO,CAAC5I,cAAc,IAAImM,SAAS,CAAC1a,OAAO,GAAG,CAAC,CAAC,uBAAuB;IAChFgB,GAAG,GAAG0Z,SAAS,CAAC/K,KAAK,GAAGwH,OAAO,CAAC4D,OAAO,IAAI5D,OAAO,CAACX,iBAAiB,GAAGwE,sBAAsB,CAACN,SAAS,CAAC,GAAG1W,GAAG,CAAC8W,cAAc,CAAC,EAAE,CAAC;EACnI,CAAC,MAAM;IACL,IAAI3D,OAAO,CAAC8D,GAAG,IAAI,CAACb,SAAS,EAAE;MAC7BA,SAAS,GAAGM,SAAS,CAAChL,KAAK,KAAK,KAAK;IACvC;IACA1O,GAAG,GAAG0Z,SAAS,CAAC/K,KAAK,GAAGwH,OAAO,CAAC8D,GAAG,GAAGjX,GAAG,CAACkX,eAAe,CACvDd,SAAS,GAAGxR,MAAM,GAAGC,OAAO,EAC5B,CAACoR,kBAAkB,IAAI9C,OAAO,CAAC5I,cAAc,IAAImM,SAAS,CAAC1a,OAAO,GAAG,CAAC,CAAC,uBAAuB,SAAS,GAAG0a,SAAS,CAAChL,KACtH,CAAC,GAAG1L,GAAG,CAACmP,aAAa,CACnB,CAAC8G,kBAAkB,IAAI9C,OAAO,CAAC5I,cAAc,IAAImM,SAAS,CAAC1a,OAAO,GAAG,CAAC,CAAC,uBAAuB,SAAS,GAAG0a,SAAS,CAAChL,KACtH,CAAC;IACD,IAAIyH,OAAO,CAAC8D,GAAG,IAAIb,SAAS,IAAIM,SAAS,CAAChL,KAAK,KAAK,eAAe,EAAE;MACnE0K,SAAS,GAAG,KAAK;IACnB;IACA,IAAIjD,OAAO,CAACvH,aAAa,EAAE;MACzByJ,aAAa,CAAC,IAAI,EAAEqB,SAAS,EAAEN,SAAS,CAAC;IAC3C;IACA,MAAMe,QAAQ,GAAGna,GAAG,CAAC8V,WAAW,CAAC,CAAC;IAClC,MAAMsE,yBAAyB,GAAG,CAACD,QAAQ,CAAC7R,aAAa,CAAC,MAAM,CAAC;IACjE,IAAI,CAAC8R,yBAAyB,IAAIjE,OAAO,CAACJ,MAAM,IAAIjO,KAAK,CAACgR,OAAO,CAAC,IAAI9Y,GAAG,CAAC,MAAM,CAAC,KAAK8Y,OAAO,EAAE;MAC7F9Y,GAAG,CAACiW,SAAS,CAACP,GAAG,CAAC1V,GAAG,CAAC,MAAM,CAAC,GAAG8Y,OAAO,CAAC;IAC1C;IACA,IAAI3C,OAAO,CAACJ,MAAM,EAAE;MAClBsE,qBAAqB,CAACra,GAAG,EAAEyZ,SAAS,CAAC;IACvC;IACA,IAAIC,SAAS,CAACrL,UAAU,EAAE;MACxB,KAAKtI,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG2T,SAAS,CAACrL,UAAU,CAACrI,MAAM,EAAE,EAAED,EAAE,EAAE;QACnD4T,SAAS,GAAGN,SAAS,CAACC,cAAc,EAAEI,SAAS,EAAE3T,EAAE,EAAE/F,GAAG,CAAC;QACzD,IAAI2Z,SAAS,EAAE;UACb3Z,GAAG,CAACqR,WAAW,CAACsI,SAAS,CAAC;QAC5B;MACF;IACF;IACA,IAAIxD,OAAO,CAAC8D,GAAG,EAAE;MACf,IAAIP,SAAS,CAAChL,KAAK,KAAK,KAAK,EAAE;QAC7B0K,SAAS,GAAG,KAAK;MACnB,CAAC,MAAM,IAAIpZ,GAAG,CAAC2J,OAAO,KAAK,eAAe,EAAE;QAC1CyP,SAAS,GAAG,IAAI;MAClB;IACF;EACF;EACApZ,GAAG,CAAC,MAAM,CAAC,GAAGgZ,WAAW;EACzB,IAAI7C,OAAO,CAAC5I,cAAc,EAAE;IAC1B,IAAImM,SAAS,CAAC1a,OAAO,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAAC,sBAAsB,EAAE;MAC1EgB,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI;MAClBA,GAAG,CAAC,MAAM,CAAC,GAAG+Y,UAAU;MACxB/Y,GAAG,CAAC,MAAM,CAAC,GAAG0Z,SAAS,CAACnL,MAAM,IAAI,EAAE;MACpCvO,GAAG,CAAC,MAAM,CAAC,GAAG,CAACmI,EAAE,GAAGuR,SAAS,CAACtL,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGjG,EAAE,CAAC5J,GAAG;MAChEqb,QAAQ,GAAGN,cAAc,IAAIA,cAAc,CAACjL,UAAU,IAAIiL,cAAc,CAACjL,UAAU,CAACmL,UAAU,CAAC;MAC/F,IAAII,QAAQ,IAAIA,QAAQ,CAAClL,KAAK,KAAKgL,SAAS,CAAChL,KAAK,IAAI4K,cAAc,CAAC3K,KAAK,EAAE;QAC1E,IAAIwH,OAAO,CAACmE,qBAAqB,EAAE;UACjCC,kBAAkB,CAACjB,cAAc,CAAC3K,KAAK,CAAC;QAC1C,CAAC,MAAM;UACL6L,yBAAyB,CAAClB,cAAc,CAAC3K,KAAK,EAAE,KAAK,CAAC;QACxD;MACF;IACF;EACF;EACA,OAAO3O,GAAG;AACZ,CAAC;AACD,IAAIua,kBAAkB,GAAId,SAAS,IAAK;EACtCpW,GAAG,CAACrE,OAAO,IAAI,CAAC,CAAC;EACjB,MAAMoW,IAAI,GAAGqE,SAAS,CAACgB,OAAO,CAACzB,WAAW,CAAClH,WAAW,CAAC,CAAC,CAAC;EACzD,IAAIsD,IAAI,IAAI,IAAI,EAAE;IAChB,MAAMsF,cAAc,GAAG1N,KAAK,CAAC2N,IAAI,CAACvF,IAAI,CAACrD,UAAU,CAAC,CAACS,IAAI,CAAEjU,GAAG,IAAKA,GAAG,CAAC,MAAM,CAAC,CAAC;IAC7E,MAAMqc,cAAc,GAAG5N,KAAK,CAAC2N,IAAI,CAAClB,SAAS,CAAC1H,UAAU,CAAC;IACvD,KAAK,MAAM4H,SAAS,IAAIe,cAAc,GAAGE,cAAc,CAACC,OAAO,CAAC,CAAC,GAAGD,cAAc,EAAE;MAClF,IAAIjB,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QAC7B1I,YAAY,CAACmE,IAAI,EAAEuE,SAAS,EAAEe,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAG,IAAI,CAAC;QAC7Ef,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;QAC1BR,iBAAiB,GAAG,IAAI;MAC1B;IACF;EACF;EACA9V,GAAG,CAACrE,OAAO,IAAI,CAAC,CAAC,CAAC;AACpB,CAAC;AACD,IAAIwb,yBAAyB,GAAGA,CAACf,SAAS,EAAEqB,SAAS,KAAK;EACxDzX,GAAG,CAACrE,OAAO,IAAI,CAAC,CAAC;EACjB,MAAM+b,iBAAiB,GAAG/N,KAAK,CAAC2N,IAAI,CAAClB,SAAS,CAAC1H,UAAU,CAAC;EAC1D,IAAI0H,SAAS,CAAC,MAAM,CAAC,IAAItD,OAAO,CAACmE,qBAAqB,EAAE;IACtD,IAAIxL,IAAI,GAAG2K,SAAS;IACpB,OAAO3K,IAAI,GAAGA,IAAI,CAACoC,WAAW,EAAE;MAC9B,IAAIpC,IAAI,IAAIA,IAAI,CAAC,MAAM,CAAC,KAAK2K,SAAS,CAAC,MAAM,CAAC,IAAI3K,IAAI,CAAC,MAAM,CAAC,KAAKkK,WAAW,EAAE;QAC9E+B,iBAAiB,CAACpV,IAAI,CAACmJ,IAAI,CAAC;MAC9B;IACF;EACF;EACA,KAAK,IAAI/I,EAAE,GAAGgV,iBAAiB,CAAC/U,MAAM,GAAG,CAAC,EAAED,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;IACzD,MAAM4T,SAAS,GAAGoB,iBAAiB,CAAChV,EAAE,CAAC;IACvC,IAAI4T,SAAS,CAAC,MAAM,CAAC,KAAKX,WAAW,IAAIW,SAAS,CAAC,MAAM,CAAC,EAAE;MAC1D1I,YAAY,CAAC+J,mBAAmB,CAACrB,SAAS,CAAC,EAAEA,SAAS,EAAEsB,aAAa,CAACtB,SAAS,CAAC,CAAC;MACjFA,SAAS,CAAC,MAAM,CAAC,CAACzH,MAAM,CAAC,CAAC;MAC1ByH,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;MAC1BA,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;MAC1BR,iBAAiB,GAAG,IAAI;IAC1B;IACA,IAAI2B,SAAS,EAAE;MACbN,yBAAyB,CAACb,SAAS,EAAEmB,SAAS,CAAC;IACjD;EACF;EACAzX,GAAG,CAACrE,OAAO,IAAI,CAAC,CAAC,CAAC;AACpB,CAAC;AACD,IAAIkc,SAAS,GAAGA,CAACzB,SAAS,EAAE0B,MAAM,EAAE7J,WAAW,EAAE8J,MAAM,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAC5E,IAAIC,YAAY,GAAGpF,OAAO,CAAC5I,cAAc,IAAIkM,SAAS,CAAC,MAAM,CAAC,IAAIA,SAAS,CAAC,MAAM,CAAC,CAACzI,UAAU,IAAIyI,SAAS;EAC3G,IAAIE,SAAS;EACb,IAAIxD,OAAO,CAAC3R,SAAS,IAAI+W,YAAY,CAACpL,UAAU,IAAIoL,YAAY,CAAC5R,OAAO,KAAKqP,WAAW,EAAE;IACxFuC,YAAY,GAAGA,YAAY,CAACpL,UAAU;EACxC;EACA,OAAOkL,QAAQ,IAAIC,MAAM,EAAE,EAAED,QAAQ,EAAE;IACrC,IAAID,MAAM,CAACC,QAAQ,CAAC,EAAE;MACpB1B,SAAS,GAAGN,SAAS,CAAC,IAAI,EAAE/H,WAAW,EAAE+J,QAAQ,EAAE5B,SAAS,CAAC;MAC7D,IAAIE,SAAS,EAAE;QACbyB,MAAM,CAACC,QAAQ,CAAC,CAAC1M,KAAK,GAAGgL,SAAS;QAClC1I,YAAY,CAACsK,YAAY,EAAE5B,SAAS,EAAExD,OAAO,CAAC5I,cAAc,GAAG0N,aAAa,CAACE,MAAM,CAAC,GAAGA,MAAM,CAAC;MAChG;IACF;EACF;AACF,CAAC;AACD,IAAIK,YAAY,GAAGA,CAACJ,MAAM,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAC/C,KAAK,IAAIG,KAAK,GAAGJ,QAAQ,EAAEI,KAAK,IAAIH,MAAM,EAAE,EAAEG,KAAK,EAAE;IACnD,MAAMtQ,KAAK,GAAGiQ,MAAM,CAACK,KAAK,CAAC;IAC3B,IAAItQ,KAAK,EAAE;MACT,MAAMnL,GAAG,GAAGmL,KAAK,CAACwD,KAAK;MACvB+M,gBAAgB,CAACvQ,KAAK,CAAC;MACvB,IAAInL,GAAG,EAAE;QACP,IAAImW,OAAO,CAAC5I,cAAc,EAAE;UAC1B2L,2BAA2B,GAAG,IAAI;UAClC,IAAIlZ,GAAG,CAAC,MAAM,CAAC,EAAE;YACfA,GAAG,CAAC,MAAM,CAAC,CAACkS,MAAM,CAAC,CAAC;UACtB,CAAC,MAAM;YACLsI,yBAAyB,CAACxa,GAAG,EAAE,IAAI,CAAC;UACtC;QACF;QACAA,GAAG,CAACkS,MAAM,CAAC,CAAC;MACd;IACF;EACF;AACF,CAAC;AACD,IAAIyJ,cAAc,GAAGA,CAAClC,SAAS,EAAEmC,KAAK,EAAElC,SAAS,EAAEmC,KAAK,EAAEC,eAAe,GAAG,KAAK,KAAK;EACpF,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,QAAQ,GAAG,CAAC;EAChB,IAAIlW,EAAE,GAAG,CAAC;EACV,IAAImW,SAAS,GAAGN,KAAK,CAAC5V,MAAM,GAAG,CAAC;EAChC,IAAImW,aAAa,GAAGP,KAAK,CAAC,CAAC,CAAC;EAC5B,IAAIQ,WAAW,GAAGR,KAAK,CAACM,SAAS,CAAC;EAClC,IAAIG,SAAS,GAAGR,KAAK,CAAC7V,MAAM,GAAG,CAAC;EAChC,IAAIsW,aAAa,GAAGT,KAAK,CAAC,CAAC,CAAC;EAC5B,IAAIU,WAAW,GAAGV,KAAK,CAACQ,SAAS,CAAC;EAClC,IAAIvN,IAAI;EACR,IAAI0N,SAAS;EACb,OAAOT,WAAW,IAAIG,SAAS,IAAIF,WAAW,IAAIK,SAAS,EAAE;IAC3D,IAAIF,aAAa,IAAI,IAAI,EAAE;MACzBA,aAAa,GAAGP,KAAK,CAAC,EAAEG,WAAW,CAAC;IACtC,CAAC,MAAM,IAAIK,WAAW,IAAI,IAAI,EAAE;MAC9BA,WAAW,GAAGR,KAAK,CAAC,EAAEM,SAAS,CAAC;IAClC,CAAC,MAAM,IAAII,aAAa,IAAI,IAAI,EAAE;MAChCA,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;IACtC,CAAC,MAAM,IAAIO,WAAW,IAAI,IAAI,EAAE;MAC9BA,WAAW,GAAGV,KAAK,CAAC,EAAEQ,SAAS,CAAC;IAClC,CAAC,MAAM,IAAII,WAAW,CAACN,aAAa,EAAEG,aAAa,EAAER,eAAe,CAAC,EAAE;MACrEY,KAAK,CAACP,aAAa,EAAEG,aAAa,EAAER,eAAe,CAAC;MACpDK,aAAa,GAAGP,KAAK,CAAC,EAAEG,WAAW,CAAC;MACpCO,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;IACtC,CAAC,MAAM,IAAIS,WAAW,CAACL,WAAW,EAAEG,WAAW,EAAET,eAAe,CAAC,EAAE;MACjEY,KAAK,CAACN,WAAW,EAAEG,WAAW,EAAET,eAAe,CAAC;MAChDM,WAAW,GAAGR,KAAK,CAAC,EAAEM,SAAS,CAAC;MAChCK,WAAW,GAAGV,KAAK,CAAC,EAAEQ,SAAS,CAAC;IAClC,CAAC,MAAM,IAAII,WAAW,CAACN,aAAa,EAAEI,WAAW,EAAET,eAAe,CAAC,EAAE;MACnE,IAAI3F,OAAO,CAAC5I,cAAc,KAAK4O,aAAa,CAACzN,KAAK,KAAK,MAAM,IAAI6N,WAAW,CAAC7N,KAAK,KAAK,MAAM,CAAC,EAAE;QAC9F8L,yBAAyB,CAAC2B,aAAa,CAACxN,KAAK,CAACqC,UAAU,EAAE,KAAK,CAAC;MAClE;MACA0L,KAAK,CAACP,aAAa,EAAEI,WAAW,EAAET,eAAe,CAAC;MAClD7K,YAAY,CAACwI,SAAS,EAAE0C,aAAa,CAACxN,KAAK,EAAEyN,WAAW,CAACzN,KAAK,CAACuC,WAAW,CAAC;MAC3EiL,aAAa,GAAGP,KAAK,CAAC,EAAEG,WAAW,CAAC;MACpCQ,WAAW,GAAGV,KAAK,CAAC,EAAEQ,SAAS,CAAC;IAClC,CAAC,MAAM,IAAII,WAAW,CAACL,WAAW,EAAEE,aAAa,EAAER,eAAe,CAAC,EAAE;MACnE,IAAI3F,OAAO,CAAC5I,cAAc,KAAK4O,aAAa,CAACzN,KAAK,KAAK,MAAM,IAAI6N,WAAW,CAAC7N,KAAK,KAAK,MAAM,CAAC,EAAE;QAC9F8L,yBAAyB,CAAC4B,WAAW,CAACzN,KAAK,CAACqC,UAAU,EAAE,KAAK,CAAC;MAChE;MACA0L,KAAK,CAACN,WAAW,EAAEE,aAAa,EAAER,eAAe,CAAC;MAClD7K,YAAY,CAACwI,SAAS,EAAE2C,WAAW,CAACzN,KAAK,EAAEwN,aAAa,CAACxN,KAAK,CAAC;MAC/DyN,WAAW,GAAGR,KAAK,CAAC,EAAEM,SAAS,CAAC;MAChCI,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;IACtC,CAAC,MAAM;MACLC,QAAQ,GAAG,CAAC,CAAC;MACb,IAAI9F,OAAO,CAAC7I,OAAO,EAAE;QACnB,KAAKvH,EAAE,GAAGgW,WAAW,EAAEhW,EAAE,IAAImW,SAAS,EAAE,EAAEnW,EAAE,EAAE;UAC5C,IAAI6V,KAAK,CAAC7V,EAAE,CAAC,IAAI6V,KAAK,CAAC7V,EAAE,CAAC,CAACuI,KAAK,KAAK,IAAI,IAAIsN,KAAK,CAAC7V,EAAE,CAAC,CAACuI,KAAK,KAAKgO,aAAa,CAAChO,KAAK,EAAE;YACpF2N,QAAQ,GAAGlW,EAAE;YACb;UACF;QACF;MACF;MACA,IAAIoQ,OAAO,CAAC7I,OAAO,IAAI2O,QAAQ,IAAI,CAAC,EAAE;QACpCO,SAAS,GAAGZ,KAAK,CAACK,QAAQ,CAAC;QAC3B,IAAIO,SAAS,CAAC9N,KAAK,KAAK4N,aAAa,CAAC5N,KAAK,EAAE;UAC3CI,IAAI,GAAGuK,SAAS,CAACuC,KAAK,IAAIA,KAAK,CAACI,WAAW,CAAC,EAAEtC,SAAS,EAAEuC,QAAQ,EAAExC,SAAS,CAAC;QAC/E,CAAC,MAAM;UACLiD,KAAK,CAACF,SAAS,EAAEF,aAAa,EAAER,eAAe,CAAC;UAChDF,KAAK,CAACK,QAAQ,CAAC,GAAG,KAAK,CAAC;UACxBnN,IAAI,GAAG0N,SAAS,CAAC7N,KAAK;QACxB;QACA2N,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;MACtC,CAAC,MAAM;QACLlN,IAAI,GAAGuK,SAAS,CAACuC,KAAK,IAAIA,KAAK,CAACI,WAAW,CAAC,EAAEtC,SAAS,EAAEsC,WAAW,EAAEvC,SAAS,CAAC;QAChF6C,aAAa,GAAGT,KAAK,CAAC,EAAEG,WAAW,CAAC;MACtC;MACA,IAAIlN,IAAI,EAAE;QACR,IAAIqH,OAAO,CAAC5I,cAAc,EAAE;UAC1B0D,YAAY,CAAC+J,mBAAmB,CAACmB,aAAa,CAACxN,KAAK,CAAC,EAAEG,IAAI,EAAEmM,aAAa,CAACkB,aAAa,CAACxN,KAAK,CAAC,CAAC;QAClG,CAAC,MAAM;UACLsC,YAAY,CAACkL,aAAa,CAACxN,KAAK,CAACqC,UAAU,EAAElC,IAAI,EAAEqN,aAAa,CAACxN,KAAK,CAAC;QACzE;MACF;IACF;EACF;EACA,IAAIoN,WAAW,GAAGG,SAAS,EAAE;IAC3BhB,SAAS,CACPzB,SAAS,EACToC,KAAK,CAACQ,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGR,KAAK,CAACQ,SAAS,GAAG,CAAC,CAAC,CAAC1N,KAAK,EAChE+K,SAAS,EACTmC,KAAK,EACLG,WAAW,EACXK,SACF,CAAC;EACH,CAAC,MAAM,IAAIlG,OAAO,CAACa,SAAS,IAAIgF,WAAW,GAAGK,SAAS,EAAE;IACvDb,YAAY,CAACI,KAAK,EAAEG,WAAW,EAAEG,SAAS,CAAC;EAC7C;AACF,CAAC;AACD,IAAIO,WAAW,GAAGA,CAACE,SAAS,EAAEC,UAAU,EAAEd,eAAe,GAAG,KAAK,KAAK;EACpE,IAAIa,SAAS,CAACjO,KAAK,KAAKkO,UAAU,CAAClO,KAAK,EAAE;IACxC,IAAIyH,OAAO,CAAC5I,cAAc,IAAIoP,SAAS,CAACjO,KAAK,KAAK,MAAM,EAAE;MACxD;MACE;MACA;MACA,UAAU,IAAIiO,SAAS,IAAIb,eAAe;MAAI;MAC9C;MACAa,SAAS,CAAChO,KAAK,CAAC+C,QAAQ,KAAK,CAAC,EAC9B;QACA,OAAO,KAAK;MACd;MACA,OAAOiL,SAAS,CAACpO,MAAM,KAAKqO,UAAU,CAACrO,MAAM;IAC/C;IACA,IAAI4H,OAAO,CAAC7I,OAAO,IAAI,CAACwO,eAAe,EAAE;MACvC,OAAOa,SAAS,CAACrO,KAAK,KAAKsO,UAAU,CAACtO,KAAK;IAC7C;IACA,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;AACD,IAAI2M,aAAa,GAAInM,IAAI,IAAK;EAC5B,OAAOA,IAAI,IAAIA,IAAI,CAAC,MAAM,CAAC,IAAIA,IAAI;AACrC,CAAC;AACD,IAAIkM,mBAAmB,GAAIlM,IAAI,IAAK,CAACA,IAAI,CAAC,MAAM,CAAC,GAAGA,IAAI,CAAC,MAAM,CAAC,GAAGA,IAAI,EAAEkC,UAAU;AACnF,IAAI0L,KAAK,GAAGA,CAAC9C,QAAQ,EAAEF,SAAS,EAAEoC,eAAe,GAAG,KAAK,KAAK;EAC5D,MAAM9b,GAAG,GAAG0Z,SAAS,CAAC/K,KAAK,GAAGiL,QAAQ,CAACjL,KAAK;EAC5C,MAAMkO,WAAW,GAAGjD,QAAQ,CAACvL,UAAU;EACvC,MAAMyO,WAAW,GAAGpD,SAAS,CAACrL,UAAU;EACxC,MAAMG,GAAG,GAAGkL,SAAS,CAAChL,KAAK;EAC3B,MAAMD,IAAI,GAAGiL,SAAS,CAACvM,MAAM;EAC7B,IAAI4P,aAAa;EACjB,IAAI,CAAC5G,OAAO,CAAC0D,QAAQ,IAAIpL,IAAI,KAAK,IAAI,EAAE;IACtC,IAAI0H,OAAO,CAAC8D,GAAG,EAAE;MACfb,SAAS,GAAG5K,GAAG,KAAK,KAAK,GAAG,IAAI,GAAGA,GAAG,KAAK,eAAe,GAAG,KAAK,GAAG4K,SAAS;IAChF;IACA,IAAIjD,OAAO,CAACvH,aAAa,IAAIuH,OAAO,CAAC6G,OAAO,EAAE;MAC5C,IAAI7G,OAAO,CAAC8G,IAAI,IAAIzO,GAAG,KAAK,MAAM,IAAI,CAACyK,kBAAkB,EAAE;QACzD,IAAI9C,OAAO,CAACmE,qBAAqB,IAAIV,QAAQ,CAACrL,MAAM,KAAKmL,SAAS,CAACnL,MAAM,EAAE;UACzEmL,SAAS,CAAC/K,KAAK,CAAC,MAAM,CAAC,GAAG+K,SAAS,CAACnL,MAAM,IAAI,EAAE;UAChDgM,kBAAkB,CAACb,SAAS,CAAC/K,KAAK,CAACuO,aAAa,CAAC;QACnD;MACF,CAAC,MAAM;QACL7E,aAAa,CAACuB,QAAQ,EAAEF,SAAS,EAAEN,SAAS,CAAC;MAC/C;IACF;IACA,IAAIjD,OAAO,CAACa,SAAS,IAAI6F,WAAW,KAAK,IAAI,IAAIC,WAAW,KAAK,IAAI,EAAE;MACrEnB,cAAc,CAAC3b,GAAG,EAAE6c,WAAW,EAAEnD,SAAS,EAAEoD,WAAW,EAAEhB,eAAe,CAAC;IAC3E,CAAC,MAAM,IAAIgB,WAAW,KAAK,IAAI,EAAE;MAC/B,IAAI3G,OAAO,CAACa,SAAS,IAAIb,OAAO,CAAC0D,QAAQ,IAAID,QAAQ,CAACzM,MAAM,KAAK,IAAI,EAAE;QACrEnN,GAAG,CAACiS,WAAW,GAAG,EAAE;MACtB;MACAiJ,SAAS,CAAClb,GAAG,EAAE,IAAI,EAAE0Z,SAAS,EAAEoD,WAAW,EAAE,CAAC,EAAEA,WAAW,CAAC9W,MAAM,GAAG,CAAC,CAAC;IACzE,CAAC,MAAM;IACL;IACA,CAAC8V,eAAe,IAAI3F,OAAO,CAACa,SAAS,IAAI6F,WAAW,KAAK,IAAI,EAC7D;MACArB,YAAY,CAACqB,WAAW,EAAE,CAAC,EAAEA,WAAW,CAAC7W,MAAM,GAAG,CAAC,CAAC;IACtD;IACA,IAAImQ,OAAO,CAAC8D,GAAG,IAAIb,SAAS,IAAI5K,GAAG,KAAK,KAAK,EAAE;MAC7C4K,SAAS,GAAG,KAAK;IACnB;EACF,CAAC,MAAM,IAAIjD,OAAO,CAAC0D,QAAQ,IAAI1D,OAAO,CAAC5I,cAAc,KAAKwP,aAAa,GAAG/c,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE;IACtF+c,aAAa,CAAC/L,UAAU,CAACiB,WAAW,GAAGxD,IAAI;EAC7C,CAAC,MAAM,IAAI0H,OAAO,CAAC0D,QAAQ,IAAID,QAAQ,CAACzM,MAAM,KAAKsB,IAAI,EAAE;IACvDzO,GAAG,CAACmd,IAAI,GAAG1O,IAAI;EACjB;AACF,CAAC;AACD,IAAI2O,4BAA4B,GAAIpd,GAAG,IAAK;EAC1C,MAAM+R,UAAU,GAAG/R,GAAG,CAAC+R,UAAU;EACjC,KAAK,MAAM4H,SAAS,IAAI5H,UAAU,EAAE;IAClC,IAAI4H,SAAS,CAACjI,QAAQ,KAAK,CAAC,CAAC,mBAAmB;MAC9C,IAAIiI,SAAS,CAAC,MAAM,CAAC,EAAE;QACrB,MAAMjN,QAAQ,GAAGiN,SAAS,CAAC,MAAM,CAAC;QAClCA,SAAS,CAAC0D,MAAM,GAAG,KAAK;QACxB,KAAK,MAAMC,WAAW,IAAIvL,UAAU,EAAE;UACpC,IAAIuL,WAAW,KAAK3D,SAAS,EAAE;YAC7B,IAAI2D,WAAW,CAAC,MAAM,CAAC,KAAK3D,SAAS,CAAC,MAAM,CAAC,IAAIjN,QAAQ,KAAK,EAAE,EAAE;cAChE,IAAI4Q,WAAW,CAAC5L,QAAQ,KAAK,CAAC,CAAC,sBAAsBhF,QAAQ,KAAK4Q,WAAW,CAAC/U,YAAY,CAAC,MAAM,CAAC,IAAImE,QAAQ,KAAK4Q,WAAW,CAAC,MAAM,CAAC,CAAC,IAAIA,WAAW,CAAC5L,QAAQ,KAAK,CAAC,CAAC,kBAAkBhF,QAAQ,KAAK4Q,WAAW,CAAC,MAAM,CAAC,EAAE;gBACxN3D,SAAS,CAAC0D,MAAM,GAAG,IAAI;gBACvB;cACF;YACF,CAAC,MAAM;cACL,IAAIC,WAAW,CAAC5L,QAAQ,KAAK,CAAC,CAAC,qBAAqB4L,WAAW,CAAC5L,QAAQ,KAAK,CAAC,CAAC,kBAAkB4L,WAAW,CAACrL,WAAW,CAACsL,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;gBACtI5D,SAAS,CAAC0D,MAAM,GAAG,IAAI;gBACvB;cACF;YACF;UACF;QACF;MACF;MACAD,4BAA4B,CAACzD,SAAS,CAAC;IACzC;EACF;AACF,CAAC;AACD,IAAI6D,aAAa,GAAG,EAAE;AACtB,IAAIC,4BAA4B,GAAIzd,GAAG,IAAK;EAC1C,IAAI8O,IAAI;EACR,IAAI4O,gBAAgB;EACpB,IAAIC,CAAC;EACL,KAAK,MAAMhE,SAAS,IAAI3Z,GAAG,CAAC+R,UAAU,EAAE;IACtC,IAAI4H,SAAS,CAAC,MAAM,CAAC,KAAK7K,IAAI,GAAG6K,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI7K,IAAI,CAACkC,UAAU,EAAE;MACtE0M,gBAAgB,GAAG5O,IAAI,CAACkC,UAAU,CAACe,UAAU;MAC7C,MAAMrF,QAAQ,GAAGiN,SAAS,CAAC,MAAM,CAAC;MAClC,KAAKgE,CAAC,GAAGD,gBAAgB,CAAC1X,MAAM,GAAG,CAAC,EAAE2X,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACjD7O,IAAI,GAAG4O,gBAAgB,CAACC,CAAC,CAAC;QAC1B,IAAI,CAAC7O,IAAI,CAAC,MAAM,CAAC,IAAI,CAACA,IAAI,CAAC,MAAM,CAAC,IAAIA,IAAI,CAAC,MAAM,CAAC,KAAK6K,SAAS,CAAC,MAAM,CAAC,KAAK,CAACxD,OAAO,CAACmE,qBAAqB,IAAI,CAACxL,IAAI,CAAC,MAAM,CAAC,IAAIA,IAAI,CAAC,MAAM,CAAC,KAAK6K,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE;UACnK,IAAIiE,mBAAmB,CAAC9O,IAAI,EAAEpC,QAAQ,CAAC,EAAE;YACvC,IAAImR,gBAAgB,GAAGL,aAAa,CAAChL,IAAI,CAAE9S,CAAC,IAAKA,CAAC,CAACoe,gBAAgB,KAAKhP,IAAI,CAAC;YAC7EoK,2BAA2B,GAAG,IAAI;YAClCpK,IAAI,CAAC,MAAM,CAAC,GAAGA,IAAI,CAAC,MAAM,CAAC,IAAIpC,QAAQ;YACvC,IAAImR,gBAAgB,EAAE;cACpBA,gBAAgB,CAACC,gBAAgB,CAAC,MAAM,CAAC,GAAGnE,SAAS,CAAC,MAAM,CAAC;cAC7DkE,gBAAgB,CAACE,aAAa,GAAGpE,SAAS;YAC5C,CAAC,MAAM;cACL7K,IAAI,CAAC,MAAM,CAAC,GAAG6K,SAAS,CAAC,MAAM,CAAC;cAChC6D,aAAa,CAAC7X,IAAI,CAAC;gBACjBoY,aAAa,EAAEpE,SAAS;gBACxBmE,gBAAgB,EAAEhP;cACpB,CAAC,CAAC;YACJ;YACA,IAAIA,IAAI,CAAC,MAAM,CAAC,EAAE;cAChB0O,aAAa,CAAC9U,GAAG,CAAEsV,YAAY,IAAK;gBAClC,IAAIJ,mBAAmB,CAACI,YAAY,CAACF,gBAAgB,EAAEhP,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;kBACpE+O,gBAAgB,GAAGL,aAAa,CAAChL,IAAI,CAAE9S,CAAC,IAAKA,CAAC,CAACoe,gBAAgB,KAAKhP,IAAI,CAAC;kBACzE,IAAI+O,gBAAgB,IAAI,CAACG,YAAY,CAACD,aAAa,EAAE;oBACnDC,YAAY,CAACD,aAAa,GAAGF,gBAAgB,CAACE,aAAa;kBAC7D;gBACF;cACF,CAAC,CAAC;YACJ;UACF,CAAC,MAAM,IAAI,CAACP,aAAa,CAACxP,IAAI,CAAEtO,CAAC,IAAKA,CAAC,CAACoe,gBAAgB,KAAKhP,IAAI,CAAC,EAAE;YAClE0O,aAAa,CAAC7X,IAAI,CAAC;cACjBmY,gBAAgB,EAAEhP;YACpB,CAAC,CAAC;UACJ;QACF;MACF;IACF;IACA,IAAI6K,SAAS,CAACjI,QAAQ,KAAK,CAAC,CAAC,mBAAmB;MAC9C+L,4BAA4B,CAAC9D,SAAS,CAAC;IACzC;EACF;AACF,CAAC;AACD,IAAIiE,mBAAmB,GAAGA,CAACK,cAAc,EAAEvR,QAAQ,KAAK;EACtD,IAAIuR,cAAc,CAACvM,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACnD,IAAIuM,cAAc,CAAC1V,YAAY,CAAC,MAAM,CAAC,KAAK,IAAI,IAAImE,QAAQ,KAAK,EAAE,EAAE;MACnE,OAAO,IAAI;IACb;IACA,IAAIuR,cAAc,CAAC1V,YAAY,CAAC,MAAM,CAAC,KAAKmE,QAAQ,EAAE;MACpD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EACA,IAAIuR,cAAc,CAAC,MAAM,CAAC,KAAKvR,QAAQ,EAAE;IACvC,OAAO,IAAI;EACb;EACA,OAAOA,QAAQ,KAAK,EAAE;AACxB,CAAC;AACD,IAAIgP,gBAAgB,GAAIwC,KAAK,IAAK;EAChC,IAAI/H,OAAO,CAACiB,OAAO,EAAE;IACnB8G,KAAK,CAAC9P,OAAO,IAAI8P,KAAK,CAAC9P,OAAO,CAAC7P,GAAG,IAAI2f,KAAK,CAAC9P,OAAO,CAAC7P,GAAG,CAAC,IAAI,CAAC;IAC7D2f,KAAK,CAAC7P,UAAU,IAAI6P,KAAK,CAAC7P,UAAU,CAAC3F,GAAG,CAACgT,gBAAgB,CAAC;EAC5D;AACF,CAAC;AACD,IAAIzK,YAAY,GAAGA,CAACkN,MAAM,EAAEC,OAAO,EAAEC,SAAS,KAAK;EACjD,MAAMC,QAAQ,GAAGH,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAClN,YAAY,CAACmN,OAAO,EAAEC,SAAS,CAAC;EAClF,IAAIlI,OAAO,CAACJ,MAAM,EAAE;IAClBsE,qBAAqB,CAAC+D,OAAO,EAAED,MAAM,CAAC;EACxC;EACA,OAAOG,QAAQ;AACjB,CAAC;AACD,IAAIC,YAAY,GAAIC,OAAO,IAAK;EAC9B,MAAMC,QAAQ,GAAG,EAAE;EACnB,IAAID,OAAO,EAAE;IACXC,QAAQ,CAAC9Y,IAAI,CACX,IAAG6Y,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,GACzBA,OAAO,CAAC,MAAM,CAAC,EACfA,OAAO,CAAC,MAAM,CAAC,EACf,GAAGD,YAAY,CAACC,OAAO,CAACtB,aAAa,CACvC,CAAC;EACH;EACA,OAAOuB,QAAQ;AACjB,CAAC;AACD,IAAIpE,qBAAqB,GAAGA,CAACmE,OAAO,EAAEL,MAAM,EAAEO,iBAAiB,GAAG,KAAK,KAAK;EAC1E,IAAIvW,EAAE;EACN,IAAIqW,OAAO,IAAIL,MAAM,IAAIK,OAAO,CAAC9M,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACjE,MAAM+M,QAAQ,GAAG,IAAIxJ,GAAG,CAACsJ,YAAY,CAACJ,MAAM,CAAC,CAACtQ,MAAM,CAAC8Q,OAAO,CAAC,CAAC;IAC9D,IAAIF,QAAQ,CAACG,IAAI,EAAE;MACjB,CAACzW,EAAE,GAAGqW,OAAO,CAACvI,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG9N,EAAE,CAACuN,GAAG,CAAC,IAAG8I,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAGC,QAAQ,CAAC,EAAC;MACvF,IAAID,OAAO,CAAC,MAAM,CAAC,IAAIE,iBAAiB,EAAE;QACxC,KAAK,MAAM/E,SAAS,IAAI3M,KAAK,CAAC2N,IAAI,CAAC6D,OAAO,CAACzM,UAAU,CAAC,EAAE;UACtDsI,qBAAqB,CAACV,SAAS,EAAE6E,OAAO,EAAE,IAAI,CAAC;QACjD;MACF;IACF;EACF;AACF,CAAC;AACD,IAAIK,UAAU,GAAGA,CAACngB,OAAO,EAAEogB,eAAe,EAAEC,aAAa,GAAG,KAAK,KAAK;EACpE,IAAI5W,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE2W,EAAE,EAAEC,EAAE;EACtB,MAAMjP,OAAO,GAAGtR,OAAO,CAACO,aAAa;EACrC,MAAMF,OAAO,GAAGL,OAAO,CAACQ,SAAS;EACjC,MAAM0a,QAAQ,GAAGlb,OAAO,CAAC0M,OAAO,IAAIgC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC;EACxD,MAAM8R,SAAS,GAAGjR,MAAM,CAAC6Q,eAAe,CAAC,GAAGA,eAAe,GAAGzS,CAAC,CAAC,IAAI,EAAE,IAAI,EAAEyS,eAAe,CAAC;EAC5F9F,WAAW,GAAGhJ,OAAO,CAACrG,OAAO;EAC7B,IAAIwM,OAAO,CAACvY,KAAK,IAAIoP,KAAK,CAACC,OAAO,CAAC6R,eAAe,CAAC,IAAIA,eAAe,CAAC9Q,IAAI,CAACC,MAAM,CAAC,EAAE;IACnF,MAAM,IAAIkR,KAAK,CAAC;AACpB,uCAAuCnG,WAAW,CAAClH,WAAW,CAAC,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAAC;EACF;EACA,IAAIqE,OAAO,CAAC6G,OAAO,IAAIje,OAAO,CAACqgB,gBAAgB,EAAE;IAC/CF,SAAS,CAAC9Q,OAAO,GAAG8Q,SAAS,CAAC9Q,OAAO,IAAI,CAAC,CAAC;IAC3CrP,OAAO,CAACqgB,gBAAgB,CAAC1W,GAAG,CAC1B,CAAC,CAAC2W,QAAQ,EAAEC,SAAS,CAAC,KAAKJ,SAAS,CAAC9Q,OAAO,CAACkR,SAAS,CAAC,GAAGtP,OAAO,CAACqP,QAAQ,CAC5E,CAAC;EACH;EACA,IAAIN,aAAa,IAAIG,SAAS,CAAC9Q,OAAO,EAAE;IACtC,KAAK,MAAMtE,GAAG,IAAI5M,MAAM,CAAC0Q,IAAI,CAACsR,SAAS,CAAC9Q,OAAO,CAAC,EAAE;MAChD,IAAI4B,OAAO,CAACuP,YAAY,CAACzV,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC8L,QAAQ,CAAC9L,GAAG,CAAC,EAAE;QAChFoV,SAAS,CAAC9Q,OAAO,CAACtE,GAAG,CAAC,GAAGkG,OAAO,CAAClG,GAAG,CAAC;MACvC;IACF;EACF;EACAoV,SAAS,CAACxQ,KAAK,GAAG,IAAI;EACtBwQ,SAAS,CAAClgB,OAAO,IAAI,CAAC,CAAC;EACvBN,OAAO,CAAC0M,OAAO,GAAG8T,SAAS;EAC3BA,SAAS,CAACvQ,KAAK,GAAGiL,QAAQ,CAACjL,KAAK,GAAGwH,OAAO,CAAC3R,SAAS,GAAGwL,OAAO,CAACG,UAAU,IAAIH,OAAO,GAAGA,OAAO;EAC9F,IAAImG,OAAO,CAACJ,MAAM,IAAII,OAAO,CAAC3R,SAAS,EAAE;IACvCsU,OAAO,GAAG9I,OAAO,CAAC,MAAM,CAAC;EAC3B;EACAiJ,kBAAkB,GAAG1U,cAAc,IAAI,CAACxF,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,kCAAkC,CAAC;EAC/F,IAAImX,OAAO,CAAC5I,cAAc,EAAE;IAC1BwL,UAAU,GAAG/I,OAAO,CAAC,MAAM,CAAC;IAC5BkJ,2BAA2B,GAAG,KAAK;EACrC;EACAwD,KAAK,CAAC9C,QAAQ,EAAEsF,SAAS,EAAEH,aAAa,CAAC;EACzC,IAAI5I,OAAO,CAAC5I,cAAc,EAAE;IAC1BlK,GAAG,CAACrE,OAAO,IAAI,CAAC,CAAC;IACjB,IAAIma,iBAAiB,EAAE;MACrBsE,4BAA4B,CAACyB,SAAS,CAACvQ,KAAK,CAAC;MAC7C,KAAK,MAAM6Q,YAAY,IAAIhC,aAAa,EAAE;QACxC,MAAMS,cAAc,GAAGuB,YAAY,CAAC1B,gBAAgB;QACpD,IAAI,CAACG,cAAc,CAAC,MAAM,CAAC,EAAE;UAC3B,MAAMlN,eAAe,GAAGoF,OAAO,CAAC4D,OAAO,IAAI5D,OAAO,CAACX,iBAAiB,GAAGiK,yBAAyB,CAACxB,cAAc,CAAC,GAAGjb,GAAG,CAAC8W,cAAc,CAAC,EAAE,CAAC;UACzI/I,eAAe,CAAC,MAAM,CAAC,GAAGkN,cAAc;UACxChN,YAAY,CAACgN,cAAc,CAACjN,UAAU,EAAEiN,cAAc,CAAC,MAAM,CAAC,GAAGlN,eAAe,EAAEkN,cAAc,CAAC;QACnG;MACF;MACA,KAAK,MAAMuB,YAAY,IAAIhC,aAAa,EAAE;QACxC,MAAMS,cAAc,GAAGuB,YAAY,CAAC1B,gBAAgB;QACpD,MAAM4B,WAAW,GAAGF,YAAY,CAACzB,aAAa;QAC9C,IAAI2B,WAAW,EAAE;UACf,MAAMC,aAAa,GAAGD,WAAW,CAAC1O,UAAU;UAC5C,IAAI4O,gBAAgB,GAAGF,WAAW,CAACxO,WAAW;UAC9C,IAAI,CAACiF,OAAO,CAACmE,qBAAqB,IAAIsF,gBAAgB,IAAIA,gBAAgB,CAAClO,QAAQ,KAAK,CAAC,CAAC,mBAAmB;YAC3G,IAAIX,eAAe,GAAG,CAAC5I,EAAE,GAAG8V,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG9V,EAAE,CAAC0X,eAAe;YACzF,OAAO9O,eAAe,EAAE;cACtB,IAAI+O,OAAO,GAAG,CAAC1X,EAAE,GAAG2I,eAAe,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG3I,EAAE,GAAG,IAAI;cAChE,IAAI0X,OAAO,IAAIA,OAAO,CAAC,MAAM,CAAC,KAAK7B,cAAc,CAAC,MAAM,CAAC,IAAI0B,aAAa,KAAKG,OAAO,CAAC9O,UAAU,EAAE;gBACjG8O,OAAO,GAAGA,OAAO,CAAC5O,WAAW;gBAC7B,OAAO4O,OAAO,KAAK7B,cAAc,KAAK6B,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;kBACjFA,OAAO,GAAGA,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC5O,WAAW;gBAC1D;gBACA,IAAI,CAAC4O,OAAO,IAAI,CAACA,OAAO,CAAC,MAAM,CAAC,EAAE;kBAChCF,gBAAgB,GAAGE,OAAO;kBAC1B;gBACF;cACF;cACA/O,eAAe,GAAGA,eAAe,CAAC8O,eAAe;YACnD;UACF;UACA,IAAI,CAACD,gBAAgB,IAAID,aAAa,KAAK1B,cAAc,CAACjN,UAAU,IAAIiN,cAAc,CAAC/M,WAAW,KAAK0O,gBAAgB,EAAE;YACvH,IAAI3B,cAAc,KAAK2B,gBAAgB,EAAE;cACvC,IAAI,CAACzJ,OAAO,CAACmE,qBAAqB,IAAI,CAAC2D,cAAc,CAAC,MAAM,CAAC,IAAIA,cAAc,CAAC,MAAM,CAAC,EAAE;gBACvFA,cAAc,CAAC,MAAM,CAAC,GAAGA,cAAc,CAAC,MAAM,CAAC,CAACjN,UAAU,CAAC1E,QAAQ;cACrE;cACA2E,YAAY,CAAC0O,aAAa,EAAE1B,cAAc,EAAE2B,gBAAgB,CAAC;cAC7D,IAAI3B,cAAc,CAACvM,QAAQ,KAAK,CAAC,CAAC,mBAAmB;gBACnDuM,cAAc,CAACZ,MAAM,GAAG,CAAChV,EAAE,GAAG4V,cAAc,CAAC,MAAM,CAAC,KAAK,IAAI,GAAG5V,EAAE,GAAG,KAAK;cAC5E;YACF;UACF;UACA4V,cAAc,IAAI,OAAOyB,WAAW,CAAC,MAAM,CAAC,KAAK,UAAU,IAAIA,WAAW,CAAC,MAAM,CAAC,CAACzB,cAAc,CAAC;QACpG,CAAC,MAAM;UACL,IAAIA,cAAc,CAACvM,QAAQ,KAAK,CAAC,CAAC,mBAAmB;YACnD,IAAIqN,aAAa,EAAE;cACjBd,cAAc,CAAC,MAAM,CAAC,GAAG,CAACe,EAAE,GAAGf,cAAc,CAACZ,MAAM,KAAK,IAAI,GAAG2B,EAAE,GAAG,KAAK;YAC5E;YACAf,cAAc,CAACZ,MAAM,GAAG,IAAI;UAC9B;QACF;MACF;IACF;IACA,IAAInE,2BAA2B,EAAE;MAC/BkE,4BAA4B,CAAC8B,SAAS,CAACvQ,KAAK,CAAC;IAC/C;IACAtL,GAAG,CAACrE,OAAO,IAAI,CAAC,CAAC,CAAC;IAClBwe,aAAa,CAACxX,MAAM,GAAG,CAAC;EAC1B;EACA,IAAImQ,OAAO,CAAC4J,6BAA6B,IAAIhhB,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;IAC7F,KAAK,MAAM2a,SAAS,IAAIuF,SAAS,CAACvQ,KAAK,CAACoD,UAAU,EAAE;MAClD,IAAI4H,SAAS,CAAC,MAAM,CAAC,KAAKX,WAAW,IAAI,CAACW,SAAS,CAAC,MAAM,CAAC,EAAE;QAC3D,IAAIoF,aAAa,IAAIpF,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;UAC9CA,SAAS,CAAC,MAAM,CAAC,GAAG,CAACsF,EAAE,GAAGtF,SAAS,CAAC0D,MAAM,KAAK,IAAI,GAAG4B,EAAE,GAAG,KAAK;QAClE;QACAtF,SAAS,CAAC0D,MAAM,GAAG,IAAI;MACzB;IACF;EACF;EACAtE,UAAU,GAAG,KAAK,CAAC;AACrB,CAAC;AACD,IAAIiB,sBAAsB,GAAIgG,SAAS,IAAKhd,GAAG,CAACid,aAAa,CAC3D,QAAQD,SAAS,CAACzR,MAAM,GAAG,SAAS,GAAGyR,SAAS,CAACzR,MAAM,GAAG,GAAG,GAAG,EAAE,WAAWyK,WAAW,CAAClH,WAAW,CAAC,CAAC,GACxG,CAAC;AACD,IAAI2N,yBAAyB,GAAIxB,cAAc,IAAKjb,GAAG,CAACid,aAAa,CACnE,mBAAmB,IAAIhC,cAAc,CAACiC,SAAS,GAAG,IAAIjC,cAAc,CAACiC,SAAS,WAAWjC,cAAc,CAAC,MAAM,CAAC,GAAG,GAAG,IAAIA,cAAc,CAAChM,WAAW,GAAG,CACxJ,CAAC;;AAED;AACA,IAAIkO,gBAAgB,GAAGA,CAACzhB,OAAO,EAAEuM,iBAAiB,KAAK;EACrD,IAAIoI,OAAO,CAACzT,YAAY,IAAIqL,iBAAiB,IAAI,CAACvM,OAAO,CAACiN,iBAAiB,IAAIV,iBAAiB,CAAC,KAAK,CAAC,EAAE;IACvGA,iBAAiB,CAAC,KAAK,CAAC,CAACtF,IAAI,CAAC,IAAIlG,OAAO,CAAEC,CAAC,IAAKhB,OAAO,CAACiN,iBAAiB,GAAGjM,CAAC,CAAC,CAAC;EAClF;AACF,CAAC;AACD,IAAI0gB,cAAc,GAAGA,CAAC1hB,OAAO,EAAEqgB,aAAa,KAAK;EAC/C,IAAI1L,OAAO,CAACgN,SAAS,IAAIhN,OAAO,CAAC2D,SAAS,EAAE;IAC1CtY,OAAO,CAACM,OAAO,IAAI,EAAE,CAAC;EACxB;EACA,IAAIqU,OAAO,CAACzT,YAAY,IAAIlB,OAAO,CAACM,OAAO,GAAG,CAAC,CAAC,4BAA4B;IAC1EN,OAAO,CAACM,OAAO,IAAI,GAAG,CAAC;IACvB;EACF;EACAmhB,gBAAgB,CAACzhB,OAAO,EAAEA,OAAO,CAACwM,mBAAmB,CAAC;EACtD,MAAMoV,QAAQ,GAAGA,CAAA,KAAMC,aAAa,CAAC7hB,OAAO,EAAEqgB,aAAa,CAAC;EAC5D,OAAO1L,OAAO,CAACgN,SAAS,GAAGzZ,SAAS,CAAC0Z,QAAQ,CAAC,GAAGA,QAAQ,CAAC,CAAC;AAC7D,CAAC;AACD,IAAIC,aAAa,GAAGA,CAAC7hB,OAAO,EAAEqgB,aAAa,KAAK;EAC9C,MAAM/e,GAAG,GAAGtB,OAAO,CAACO,aAAa;EACjC,MAAMuhB,WAAW,GAAG/W,UAAU,CAAC,gBAAgB,EAAE/K,OAAO,CAACQ,SAAS,CAACsC,SAAS,CAAC;EAC7E,MAAMif,QAAQ,GAAGpN,OAAO,CAAC9T,QAAQ,GAAGb,OAAO,CAACE,cAAc,GAAGoB,GAAG;EAChE,IAAI,CAACygB,QAAQ,EAAE;IACb,MAAM,IAAItB,KAAK,CACb,2BAA2Bnf,GAAG,CAAC2J,OAAO,CAACmI,WAAW,CAAC,CAAC,yNACtD,CAAC;EACH;EACA,IAAI4O,YAAY;EAChB,IAAI3B,aAAa,EAAE;IACjB,IAAI1L,OAAO,CAAC9T,QAAQ,IAAI8T,OAAO,CAACsN,YAAY,EAAE;MAC5CjiB,OAAO,CAACM,OAAO,IAAI,GAAG,CAAC;MACvB,IAAIN,OAAO,CAACmN,iBAAiB,EAAE;QAC7BnN,OAAO,CAACmN,iBAAiB,CAACnD,GAAG,CAAC,CAAC,CAACkY,UAAU,EAAEC,KAAK,CAAC,KAAKC,QAAQ,CAACL,QAAQ,EAAEG,UAAU,EAAEC,KAAK,CAAC,CAAC;QAC7FniB,OAAO,CAACmN,iBAAiB,GAAG,KAAK,CAAC;MACpC;IACF;IACAkV,kBAAkB,CAAC/gB,GAAG,EAAE,mBAAmB,CAAC;IAC5C,IAAIqT,OAAO,CAAC2N,WAAW,EAAE;MACvBN,YAAY,GAAGI,QAAQ,CAACL,QAAQ,EAAE,mBAAmB,CAAC;IACxD;EACF,CAAC,MAAM;IACLM,kBAAkB,CAAC/gB,GAAG,EAAE,qBAAqB,CAAC;IAC9C,IAAIqT,OAAO,CAAC4N,aAAa,EAAE;MACzBP,YAAY,GAAGI,QAAQ,CAACL,QAAQ,EAAE,qBAAqB,CAAC;IAC1D;EACF;EACAM,kBAAkB,CAAC/gB,GAAG,EAAE,qBAAqB,CAAC;EAC9C,IAAIqT,OAAO,CAAC6N,aAAa,EAAE;IACzBR,YAAY,GAAGS,OAAO,CAACT,YAAY,EAAE,MAAMI,QAAQ,CAACL,QAAQ,EAAE,qBAAqB,CAAC,CAAC;EACvF;EACAD,WAAW,CAAC,CAAC;EACb,OAAOW,OAAO,CAACT,YAAY,EAAE,MAAMU,eAAe,CAAC1iB,OAAO,EAAE+hB,QAAQ,EAAE1B,aAAa,CAAC,CAAC;AACvF,CAAC;AACD,IAAIoC,OAAO,GAAGA,CAACT,YAAY,EAAExX,EAAE,KAAKmY,UAAU,CAACX,YAAY,CAAC,GAAGA,YAAY,CAAC5e,IAAI,CAACoH,EAAE,CAAC,CAACoY,KAAK,CAAEC,IAAI,IAAK;EACnG/gB,OAAO,CAACC,KAAK,CAAC8gB,IAAI,CAAC;EACnBrY,EAAE,CAAC,CAAC;AACN,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC;AACT,IAAImY,UAAU,GAAIX,YAAY,IAAKA,YAAY,YAAYjhB,OAAO,IAAIihB,YAAY,IAAIA,YAAY,CAAC5e,IAAI,IAAI,OAAO4e,YAAY,CAAC5e,IAAI,KAAK,UAAU;AAClJ,IAAIsf,eAAe;EAAA,IAAAI,IAAA,GAAAC,iBAAA,CAAG,WAAO/iB,OAAO,EAAE+hB,QAAQ,EAAE1B,aAAa,EAAK;IAChE,IAAI5W,EAAE;IACN,MAAMnI,GAAG,GAAGtB,OAAO,CAACO,aAAa;IACjC,MAAMyiB,SAAS,GAAGjY,UAAU,CAAC,QAAQ,EAAE/K,OAAO,CAACQ,SAAS,CAACsC,SAAS,CAAC;IACnE,MAAMmgB,EAAE,GAAG3hB,GAAG,CAAC,MAAM,CAAC;IACtB,IAAIqT,OAAO,CAACoB,KAAK,IAAIsK,aAAa,EAAE;MAClCjK,YAAY,CAACpW,OAAO,CAAC;IACvB;IACA,MAAMkjB,SAAS,GAAGnY,UAAU,CAAC,QAAQ,EAAE/K,OAAO,CAACQ,SAAS,CAACsC,SAAS,CAAC;IACnE,IAAI6R,OAAO,CAACzV,KAAK,EAAE;MACjBc,OAAO,CAACM,OAAO,IAAI,IAAI,CAAC;IAC1B;IACA,IAAIqU,OAAO,CAACmC,iBAAiB,EAAE;MAC7B,MAAMqM,UAAU,CAACnjB,OAAO,EAAE+hB,QAAQ,EAAEzgB,GAAG,EAAE+e,aAAa,CAAC;IACzD,CAAC,MAAM;MACL8C,UAAU,CAACnjB,OAAO,EAAE+hB,QAAQ,EAAEzgB,GAAG,EAAE+e,aAAa,CAAC;IACnD;IACA,IAAI1L,OAAO,CAACzV,KAAK,EAAE;MACjBc,OAAO,CAACW,aAAa,GAAGX,OAAO,CAACW,aAAa,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGX,OAAO,CAACW,aAAa,GAAG,CAAC;MACxFX,OAAO,CAACM,OAAO,IAAI,CAAC,IAAI,CAAC;IAC3B;IACA,IAAIqU,OAAO,CAACmC,iBAAiB,EAAE;MAC7B,IAAI;QACFsM,mBAAmB,CAAC9hB,GAAG,CAAC;QACxB,IAAI+e,aAAa,EAAE;UACjB,IAAIrgB,OAAO,CAACQ,SAAS,CAACF,OAAO,GAAG,CAAC,CAAC,8BAA8B;YAC9DgB,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE;UAClB,CAAC,MAAM,IAAItB,OAAO,CAACQ,SAAS,CAACF,OAAO,GAAG,CAAC,CAAC,8BAA8B;YACrEgB,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG;UACnB;QACF;MACF,CAAC,CAAC,OAAOM,CAAC,EAAE;QACVD,YAAY,CAACC,CAAC,EAAEN,GAAG,CAAC;MACtB;IACF;IACA,IAAIqT,OAAO,CAACzT,YAAY,IAAI+hB,EAAE,EAAE;MAC9BA,EAAE,CAACjZ,GAAG,CAAEhD,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;MACpB1F,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;IACtB;IACA4hB,SAAS,CAAC,CAAC;IACXF,SAAS,CAAC,CAAC;IACX,IAAIrO,OAAO,CAACzT,YAAY,EAAE;MACxB,MAAMmiB,gBAAgB,GAAG,CAAC5Z,EAAE,GAAGnI,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,GAAGmI,EAAE,GAAG,EAAE;MAC5D,MAAM6Z,UAAU,GAAGA,CAAA,KAAMC,mBAAmB,CAACvjB,OAAO,CAAC;MACrD,IAAIqjB,gBAAgB,CAAC/b,MAAM,KAAK,CAAC,EAAE;QACjCgc,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLviB,OAAO,CAACnC,GAAG,CAACykB,gBAAgB,CAAC,CAACjgB,IAAI,CAACkgB,UAAU,CAAC;QAC9CtjB,OAAO,CAACM,OAAO,IAAI,CAAC,CAAC;QACrB+iB,gBAAgB,CAAC/b,MAAM,GAAG,CAAC;MAC7B;IACF,CAAC,MAAM;MACLic,mBAAmB,CAACvjB,OAAO,CAAC;IAC9B;EACF,CAAC;EAAA,gBAtDG0iB,eAAeA,CAAAc,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAZ,IAAA,CAAAa,KAAA,OAAAC,SAAA;EAAA;AAAA,GAsDlB;AACD,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIV,UAAU,GAAGA,CAACnjB,OAAO,EAAE+hB,QAAQ,EAAEzgB,GAAG,EAAE+e,aAAa,KAAK;EAC1D,MAAMyD,WAAW,GAAGnP,OAAO,CAACmP,WAAW,GAAG,IAAI,GAAG,KAAK;EACtD,MAAMjjB,QAAQ,GAAG8T,OAAO,CAAC9T,QAAQ,GAAG,IAAI,GAAG,KAAK;EAChD,MAAM8gB,SAAS,GAAGhN,OAAO,CAACgN,SAAS,GAAG,IAAI,GAAG,KAAK;EAClD,MAAMrJ,SAAS,GAAG3D,OAAO,CAAC2D,SAAS,GAAG,IAAI,GAAG,KAAK;EAClD,IAAI;IACFuL,YAAY,GAAG9B,QAAQ;IACvBA,QAAQ,GAAG+B,WAAW,GAAG/B,QAAQ,CAACgC,MAAM,CAAC,CAAC,GAAGhC,QAAQ,CAACgC,MAAM,IAAIhC,QAAQ,CAACgC,MAAM,CAAC,CAAC;IACjF,IAAIzL,SAAS,IAAIqJ,SAAS,EAAE;MAC1B3hB,OAAO,CAACM,OAAO,IAAI,CAAC,EAAE,CAAC;IACzB;IACA,IAAIgY,SAAS,IAAIzX,QAAQ,EAAE;MACzBb,OAAO,CAACM,OAAO,IAAI,CAAC,CAAC;IACvB;IACA,IAAIqU,OAAO,CAACqP,WAAW,IAAIrP,OAAO,CAAC2J,OAAO,EAAE;MAC1C,IAAI3J,OAAO,CAACsP,UAAU,IAAItP,OAAO,CAAC2J,OAAO,EAAE;QACzC,IAAI3J,OAAO,CAACmC,iBAAiB,EAAE;UAC7B,OAAO/V,OAAO,CAACoF,OAAO,CAAC4b,QAAQ,CAAC,CAAC3e,IAAI,CAAEgH,KAAK,IAAK+V,UAAU,CAACngB,OAAO,EAAEoK,KAAK,EAAEiW,aAAa,CAAC,CAAC;QAC7F,CAAC,MAAM;UACLF,UAAU,CAACngB,OAAO,EAAE+hB,QAAQ,EAAE1B,aAAa,CAAC;QAC9C;MACF,CAAC,MAAM;QACL,MAAM5O,UAAU,GAAGnQ,GAAG,CAACmQ,UAAU;QACjC,IAAIzR,OAAO,CAACQ,SAAS,CAACF,OAAO,GAAG,CAAC,CAAC,8BAA8B;UAC9DmR,UAAU,CAAC8B,WAAW,GAAGwO,QAAQ;QACnC,CAAC,MAAM;UACLzgB,GAAG,CAACiS,WAAW,GAAGwO,QAAQ;QAC5B;MACF;IACF;EACF,CAAC,CAAC,OAAOngB,CAAC,EAAE;IACVD,YAAY,CAACC,CAAC,EAAE5B,OAAO,CAACO,aAAa,CAAC;EACxC;EACAsjB,YAAY,GAAG,IAAI;EACnB,OAAO,IAAI;AACb,CAAC;AACD,IAAIK,eAAe,GAAGA,CAAA,KAAML,YAAY;AACxC,IAAIN,mBAAmB,GAAIvjB,OAAO,IAAK;EACrC,MAAMiL,OAAO,GAAGjL,OAAO,CAACQ,SAAS,CAACsC,SAAS;EAC3C,MAAMxB,GAAG,GAAGtB,OAAO,CAACO,aAAa;EACjC,MAAM4jB,aAAa,GAAGpZ,UAAU,CAAC,YAAY,EAAEE,OAAO,CAAC;EACvD,MAAM8W,QAAQ,GAAGpN,OAAO,CAAC9T,QAAQ,GAAGb,OAAO,CAACE,cAAc,GAAGoB,GAAG;EAChE,MAAMiL,iBAAiB,GAAGvM,OAAO,CAACwM,mBAAmB;EACrD,IAAImI,OAAO,CAACyP,YAAY,EAAE;IACxB,IAAIzP,OAAO,CAACzV,KAAK,EAAE;MACjBc,OAAO,CAACM,OAAO,IAAI,IAAI,CAAC;IAC1B;IACA8hB,QAAQ,CAACL,QAAQ,EAAE,oBAAoB,CAAC;IACxC,IAAIpN,OAAO,CAACzV,KAAK,EAAE;MACjBc,OAAO,CAACM,OAAO,IAAI,CAAC,IAAI,CAAC;IAC3B;EACF;EACA+hB,kBAAkB,CAAC/gB,GAAG,EAAE,oBAAoB,CAAC;EAC7C,IAAI,EAAEtB,OAAO,CAACM,OAAO,GAAG,EAAE,CAAC,yBAAyB,EAAE;IACpDN,OAAO,CAACM,OAAO,IAAI,EAAE,CAAC;IACtB,IAAIqU,OAAO,CAACzT,YAAY,IAAIyT,OAAO,CAAC2C,cAAc,EAAE;MAClD+M,eAAe,CAAC/iB,GAAG,CAAC;IACtB;IACA,IAAIqT,OAAO,CAAC2P,UAAU,EAAE;MACtB,IAAI3P,OAAO,CAACzV,KAAK,EAAE;QACjBc,OAAO,CAACM,OAAO,IAAI,IAAI,CAAC;MAC1B;MACA8hB,QAAQ,CAACL,QAAQ,EAAE,kBAAkB,CAAC;MACtC,IAAIpN,OAAO,CAACzV,KAAK,EAAE;QACjBc,OAAO,CAACM,OAAO,IAAI,CAAC,IAAI,CAAC;MAC3B;IACF;IACA+hB,kBAAkB,CAAC/gB,GAAG,EAAE,kBAAkB,CAAC;IAC3C6iB,aAAa,CAAC,CAAC;IACf,IAAIxP,OAAO,CAACzT,YAAY,EAAE;MACxBlB,OAAO,CAACoB,gBAAgB,CAACE,GAAG,CAAC;MAC7B,IAAI,CAACiL,iBAAiB,EAAE;QACtBgY,UAAU,CAACtZ,OAAO,CAAC;MACrB;IACF;EACF,CAAC,MAAM;IACL,IAAI0J,OAAO,CAAC6P,YAAY,EAAE;MACxB,IAAI7P,OAAO,CAACzV,KAAK,EAAE;QACjBc,OAAO,CAACM,OAAO,IAAI,IAAI,CAAC;MAC1B;MACA8hB,QAAQ,CAACL,QAAQ,EAAE,oBAAoB,CAAC;MACxC,IAAIpN,OAAO,CAACzV,KAAK,EAAE;QACjBc,OAAO,CAACM,OAAO,IAAI,CAAC,IAAI,CAAC;MAC3B;IACF;IACA+hB,kBAAkB,CAAC/gB,GAAG,EAAE,oBAAoB,CAAC;IAC7C6iB,aAAa,CAAC,CAAC;EACjB;EACA,IAAIxP,OAAO,CAAC/T,MAAM,IAAI+T,OAAO,CAAC9T,QAAQ,EAAE;IACtCb,OAAO,CAACiB,mBAAmB,CAACK,GAAG,CAAC;EAClC;EACA,IAAIqT,OAAO,CAACzT,YAAY,EAAE;IACxB,IAAIlB,OAAO,CAACiN,iBAAiB,EAAE;MAC7BjN,OAAO,CAACiN,iBAAiB,CAAC,CAAC;MAC3BjN,OAAO,CAACiN,iBAAiB,GAAG,KAAK,CAAC;IACpC;IACA,IAAIjN,OAAO,CAACM,OAAO,GAAG,GAAG,CAAC,qBAAqB;MAC7C4G,QAAQ,CAAC,MAAMwa,cAAc,CAAC1hB,OAAO,EAAE,KAAK,CAAC,CAAC;IAChD;IACAA,OAAO,CAACM,OAAO,IAAI,EAAE,CAAC,CAAC,6BAA6B,GAAG,CAAC,oBAAoB;EAC9E;AACF,CAAC;AACD,IAAImkB,WAAW,GAAI5kB,GAAG,IAAK;EACzB,IAAI8U,OAAO,CAAC2D,SAAS,KAAKrZ,KAAK,CAACE,SAAS,IAAIF,KAAK,CAACI,SAAS,CAAC,EAAE;IAC7D,MAAMW,OAAO,GAAGJ,UAAU,CAACC,GAAG,CAAC;IAC/B,MAAMqV,WAAW,GAAGlV,OAAO,CAACO,aAAa,CAAC2U,WAAW;IACrD,IAAIA,WAAW,IAAI,CAAClV,OAAO,CAACM,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,CAAC,wBAAwB,MAAM,CAAC,CAAC,mBAAmB;MACjHohB,cAAc,CAAC1hB,OAAO,EAAE,KAAK,CAAC;IAChC;IACA,OAAOkV,WAAW;EACpB;EACA,OAAO,KAAK;AACd,CAAC;AACD,IAAIqP,UAAU,GAAIG,GAAG,IAAK;EACxB,IAAI/P,OAAO,CAAC2C,cAAc,EAAE;IAC1B+M,eAAe,CAAC/f,GAAG,CAACqgB,eAAe,CAAC;EACtC;EACA,IAAIhQ,OAAO,CAAC9M,UAAU,EAAE;IACtBlD,GAAG,CAACrE,OAAO,IAAI,CAAC,CAAC;EACnB;EACA4G,QAAQ,CAAC,MAAMiO,SAAS,CAAC9Q,GAAG,EAAE,SAAS,EAAE;IAAE4Q,MAAM,EAAE;MAAE2P,SAAS,EAAEvc;IAAU;EAAE,CAAC,CAAC,CAAC;EAC/E,IAAIsM,OAAO,CAACzJ,OAAO,IAAI3D,WAAW,CAAC8D,OAAO,EAAE;IAC1C9D,WAAW,CAAC8D,OAAO,CAAC,aAAahD,SAAS,qBAAqBqc,GAAG,GAAG,EAAE,cAAc,CAAC;EACxF;AACF,CAAC;AACD,IAAItC,QAAQ,GAAGA,CAACL,QAAQ,EAAEnhB,MAAM,EAAEikB,GAAG,KAAK;EACxC,IAAI9C,QAAQ,IAAIA,QAAQ,CAACnhB,MAAM,CAAC,EAAE;IAChC,IAAI;MACF,OAAOmhB,QAAQ,CAACnhB,MAAM,CAAC,CAACikB,GAAG,CAAC;IAC9B,CAAC,CAAC,OAAOjjB,CAAC,EAAE;MACVD,YAAY,CAACC,CAAC,CAAC;IACjB;EACF;EACA,OAAO,KAAK,CAAC;AACf,CAAC;AACD,IAAIygB,kBAAkB,GAAGA,CAAC/gB,GAAG,EAAEwjB,aAAa,KAAK;EAC/C,IAAInQ,OAAO,CAACoQ,kBAAkB,EAAE;IAC9B5P,SAAS,CAAC7T,GAAG,EAAE,UAAU,GAAGwjB,aAAa,EAAE;MACzC1P,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdJ,MAAM,EAAE;QACN2P,SAAS,EAAEvc;MACb;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAIgc,eAAe,GAAI/iB,GAAG,IAAK;EAC7B,IAAImI,EAAE,EAAEC,EAAE;EACV,OAAOiL,OAAO,CAACqQ,aAAa,GAAG1jB,GAAG,CAACiW,SAAS,CAACP,GAAG,CAAC,CAACvN,EAAE,GAAGkL,OAAO,CAACsQ,oBAAoB,KAAK,IAAI,GAAGxb,EAAE,GAAG,UAAU,CAAC,GAAGkL,OAAO,CAACuQ,iBAAiB,GAAG5jB,GAAG,CAACoS,YAAY,CAAC,CAAChK,EAAE,GAAGiL,OAAO,CAACsQ,oBAAoB,KAAK,IAAI,GAAGvb,EAAE,GAAG,UAAU,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;AAC5O,CAAC;AACD,IAAI0Z,mBAAmB,GAAI9hB,GAAG,IAAK;EACjC,MAAMwM,QAAQ,GAAGxM,GAAG,CAACwM,QAAQ;EAC7B,IAAIA,QAAQ,IAAI,IAAI,EAAE;IACpB,KAAK,IAAIzG,EAAE,GAAG,CAAC,EAAE8d,EAAE,GAAGrX,QAAQ,CAACxG,MAAM,EAAED,EAAE,GAAG8d,EAAE,EAAE9d,EAAE,EAAE,EAAE;MACpD,MAAM+d,QAAQ,GAAGtX,QAAQ,CAACzG,EAAE,CAAC;MAC7B,IAAI,OAAO+d,QAAQ,CAACC,iBAAiB,KAAK,UAAU,EAAE;QACpDD,QAAQ,CAACC,iBAAiB,CAAC,CAAC;MAC9B;MACAjC,mBAAmB,CAACgC,QAAQ,CAAC;IAC/B;EACF;AACF,CAAC;;AAED;AACA,IAAIE,QAAQ,GAAGA,CAACzlB,GAAG,EAAE8gB,QAAQ,KAAK/gB,UAAU,CAACC,GAAG,CAAC,CAACY,gBAAgB,CAAC3B,GAAG,CAAC6hB,QAAQ,CAAC;AAChF,IAAI4E,QAAQ,GAAGA,CAAC1lB,GAAG,EAAE8gB,QAAQ,EAAEjW,MAAM,EAAErK,OAAO,KAAK;EACjD,MAAML,OAAO,GAAGJ,UAAU,CAACC,GAAG,CAAC;EAC/B,IAAIqU,OAAO,CAACrT,QAAQ,IAAI,CAACb,OAAO,EAAE;IAChC,MAAM,IAAIygB,KAAK,CACb,mCAAmCpgB,OAAO,CAACyC,SAAS,+YACtD,CAAC;EACH;EACA,MAAMxB,GAAG,GAAG4S,OAAO,CAACrT,QAAQ,GAAGb,OAAO,CAACO,aAAa,GAAGV,GAAG;EAC1D,MAAM2lB,MAAM,GAAGxlB,OAAO,CAACS,gBAAgB,CAAC3B,GAAG,CAAC6hB,QAAQ,CAAC;EACrD,MAAMjV,KAAK,GAAG1L,OAAO,CAACM,OAAO;EAC7B,MAAMyhB,QAAQ,GAAG7N,OAAO,CAACrT,QAAQ,GAAGb,OAAO,CAACE,cAAc,GAAGoB,GAAG;EAChEoJ,MAAM,GAAG0J,kBAAkB,CAAC1J,MAAM,EAAErK,OAAO,CAAColB,SAAS,CAAC9E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnE,MAAM+E,UAAU,GAAGC,MAAM,CAACC,KAAK,CAACJ,MAAM,CAAC,IAAIG,MAAM,CAACC,KAAK,CAAClb,MAAM,CAAC;EAC/D,MAAMmb,cAAc,GAAGnb,MAAM,KAAK8a,MAAM,IAAI,CAACE,UAAU;EACvD,IAAI,CAAC,CAACxR,OAAO,CAACrT,QAAQ,IAAI,EAAE6K,KAAK,GAAG,CAAC,CAAC,6BAA6B,IAAI8Z,MAAM,KAAK,KAAK,CAAC,KAAKK,cAAc,EAAE;IAC3G7lB,OAAO,CAACS,gBAAgB,CAACR,GAAG,CAAC0gB,QAAQ,EAAEjW,MAAM,CAAC;IAC9C,IAAIwJ,OAAO,CAAChV,KAAK,EAAE;MACjB,IAAIc,OAAO,CAACM,OAAO,GAAG,IAAI,CAAC,mBAAmB;QAC5C6B,cAAc,CACZ,mBAAmBwe,QAAQ,yFAAyF,EACpH,WAAW,EACXrf,GAAG,EACH,aAAa,EACboJ,MAAM,EACN,aAAa,EACb8a,MACF,CAAC;MACH,CAAC,MAAM,IAAIxlB,OAAO,CAACM,OAAO,GAAG,IAAI,CAAC,oBAAoB;QACpD6B,cAAc,CACZ,mBAAmBwe,QAAQ,8GAA8G,EACzI,WAAW,EACXrf,GAAG,EACH,aAAa,EACboJ,MAAM,EACN,aAAa,EACb8a,MACF,CAAC;MACH;IACF;IACA,IAAI,CAACtR,OAAO,CAACrT,QAAQ,IAAIkhB,QAAQ,EAAE;MACjC,IAAI7N,OAAO,CAAC4R,aAAa,IAAIzlB,OAAO,CAAC0lB,UAAU,IAAIra,KAAK,GAAG,GAAG,CAAC,oBAAoB;QACjF,MAAMsa,YAAY,GAAG3lB,OAAO,CAAC0lB,UAAU,CAACpF,QAAQ,CAAC;QACjD,IAAIqF,YAAY,EAAE;UAChBA,YAAY,CAAChc,GAAG,CAAEic,eAAe,IAAK;YACpC,IAAI;cACFlE,QAAQ,CAACkE,eAAe,CAAC,CAACvb,MAAM,EAAE8a,MAAM,EAAE7E,QAAQ,CAAC;YACrD,CAAC,CAAC,OAAO/e,CAAC,EAAE;cACVD,YAAY,CAACC,CAAC,EAAEN,GAAG,CAAC;YACtB;UACF,CAAC,CAAC;QACJ;MACF;MACA,IAAI4S,OAAO,CAACoE,SAAS,IAAI,CAAC5M,KAAK,IAAI,CAAC,CAAC,oBAAoB,EAAE,CAAC,wBAAwB,MAAM,CAAC,CAAC,mBAAmB;QAC7G,IAAIwI,OAAO,CAACgS,eAAe,IAAInE,QAAQ,CAACoE,qBAAqB,EAAE;UAC7D,IAAIpE,QAAQ,CAACoE,qBAAqB,CAACzb,MAAM,EAAE8a,MAAM,EAAE7E,QAAQ,CAAC,KAAK,KAAK,EAAE;YACtE;UACF;QACF;QACAe,cAAc,CAAC1hB,OAAO,EAAE,KAAK,CAAC;MAChC;IACF;EACF;AACF,CAAC;;AAED;AACA,IAAIomB,cAAc,GAAGA,CAACC,IAAI,EAAEhmB,OAAO,EAAEqL,KAAK,KAAK;EAC7C,IAAIjC,EAAE,EAAEC,EAAE;EACV,MAAM4c,SAAS,GAAGD,IAAI,CAACC,SAAS;EAChC,IAAIrS,OAAO,CAACsS,cAAc,IAAIlmB,OAAO,CAACC,OAAO,GAAG,EAAE,CAAC,wBAAwBoL,KAAK,GAAG,CAAC,CAAC,4BAA4B;IAC/GvH,wCAAwC,CAACkM,OAAO,CAC7CmW,MAAM,IAAKhoB,MAAM,CAACC,cAAc,CAAC6nB,SAAS,EAAEE,MAAM,EAAE;MACnDpc,KAAKA,CAAC,GAAGqc,IAAI,EAAE;QACb,MAAMzmB,OAAO,GAAGJ,UAAU,CAAC,IAAI,CAAC;QAChC,MAAM0B,GAAG,GAAG2S,OAAO,CAACpT,QAAQ,GAAGb,OAAO,CAACO,aAAa,GAAG,IAAI;QAC3D,MAAMwhB,QAAQ,GAAG9N,OAAO,CAACpT,QAAQ,GAAGb,OAAO,CAACE,cAAc,GAAGoB,GAAG;QAChE,IAAI,CAACygB,QAAQ,EAAE;UACb/hB,OAAO,CAACmB,gBAAgB,CAACiC,IAAI,CAAEsjB,SAAS,IAAK;YAC3C,MAAM1f,EAAE,GAAG0f,SAAS,CAACF,MAAM,CAAC;YAC5B,OAAOxf,EAAE,KAAK,UAAU,IAAIA,EAAE,CAAC2f,IAAI,CAACD,SAAS,EAAE,GAAGD,IAAI,CAAC;UACzD,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,MAAMzf,EAAE,GAAG+a,QAAQ,CAACyE,MAAM,CAAC;UAC3B,OAAOxf,EAAE,KAAK,UAAU,IAAIA,EAAE,CAAC2f,IAAI,CAAC5E,QAAQ,EAAE,GAAG0E,IAAI,CAAC;QACxD;MACF;IACF,CAAC,CACH,CAAC;EACH;EACA,IAAIxS,OAAO,CAAC2S,MAAM,IAAIvmB,OAAO,CAAColB,SAAS,IAAIxR,OAAO,CAAC6R,aAAa,KAAKzlB,OAAO,CAAC0lB,UAAU,IAAIM,IAAI,CAACQ,QAAQ,CAAC,EAAE;IACzG,IAAI5S,OAAO,CAAC6R,aAAa,IAAIO,IAAI,CAACQ,QAAQ,IAAI,CAACxmB,OAAO,CAAC0lB,UAAU,EAAE;MACjE1lB,OAAO,CAAC0lB,UAAU,GAAGM,IAAI,CAACQ,QAAQ;IACpC;IACA,MAAMC,OAAO,GAAGtoB,MAAM,CAACuoB,OAAO,CAAC,CAACtd,EAAE,GAAGpJ,OAAO,CAAColB,SAAS,KAAK,IAAI,GAAGhc,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1Eqd,OAAO,CAAC9c,GAAG,CAAC,CAAC,CAACzI,UAAU,EAAE,CAACylB,WAAW,CAAC,CAAC,KAAK;MAC3C,IAAI,CAAC/S,OAAO,CAACsE,IAAI,IAAItE,OAAO,CAACgT,KAAK,MAAMD,WAAW,GAAG,EAAE,CAAC,cAAc,CAAC,CAAC/S,OAAO,CAACpT,QAAQ,IAAI6K,KAAK,GAAG,CAAC,CAAC,qBAAqBsb,WAAW,GAAG,EAAE,CAAC,YAAY,EAAE;QACzJxoB,MAAM,CAACC,cAAc,CAAC6nB,SAAS,EAAE/kB,UAAU,EAAE;UAC3CzC,GAAGA,CAAA,EAAG;YACJ,OAAOwmB,QAAQ,CAAC,IAAI,EAAE/jB,UAAU,CAAC;UACnC,CAAC;UACDtB,GAAGA,CAAC6X,QAAQ,EAAE;YACZ,IAAI7D,OAAO,CAAC/U,KAAK,EAAE;cACjB,MAAMW,GAAG,GAAGD,UAAU,CAAC,IAAI,CAAC;cAC5B;cACE;cACA,CAAC8L,KAAK,GAAG,CAAC,CAAC,gCAAgC,CAAC;cAAI;cAChD,CAAC7L,GAAG,IAAIA,GAAG,CAACS,OAAO,GAAG,CAAC,CAAC,kCAAkC,CAAC;cAAI;cAC/D,CAAC0mB,WAAW,GAAG,EAAE,CAAC,gBAAgB,CAAC;cAAI;cACvC,CAACA,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,EACxC;gBACA7kB,cAAc,CACZ,YAAYZ,UAAU,SAASlB,OAAO,CAACyC,SAAS;AAClE,wEACgB,CAAC;cACH;YACF;YACAyiB,QAAQ,CAAC,IAAI,EAAEhkB,UAAU,EAAEuW,QAAQ,EAAEzX,OAAO,CAAC;UAC/C,CAAC;UACD6mB,YAAY,EAAE,IAAI;UAClBnoB,UAAU,EAAE;QACd,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIkV,OAAO,CAACpT,QAAQ,IAAIoT,OAAO,CAACrT,MAAM,IAAI8K,KAAK,GAAG,CAAC,CAAC,8BAA8Bsb,WAAW,GAAG,EAAE,CAAC,cAAc;QACtHxoB,MAAM,CAACC,cAAc,CAAC6nB,SAAS,EAAE/kB,UAAU,EAAE;UAC3C6I,KAAKA,CAAC,GAAGqc,IAAI,EAAE;YACb,IAAIU,GAAG;YACP,MAAMtnB,GAAG,GAAGD,UAAU,CAAC,IAAI,CAAC;YAC5B,OAAO,CAACunB,GAAG,GAAGtnB,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACiB,mBAAmB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqmB,GAAG,CAAC/jB,IAAI,CAAC,MAAM;cAC9F,IAAIgkB,GAAG;cACP,OAAO,CAACA,GAAG,GAAGvnB,GAAG,CAACK,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGknB,GAAG,CAAC7lB,UAAU,CAAC,CAAC,GAAGklB,IAAI,CAAC;YAC/E,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,IAAIxS,OAAO,CAACoT,gBAAgB,KAAK,CAACpT,OAAO,CAACpT,QAAQ,IAAI6K,KAAK,GAAG,CAAC,CAAC,2BAA2B,EAAE;MAC3F,MAAM4b,kBAAkB,GAAG,eAAgB,IAAI5mB,GAAG,CAAC,CAAC;MACpD4lB,SAAS,CAACiB,wBAAwB,GAAG,UAASC,QAAQ,EAAE3P,QAAQ,EAAEC,QAAQ,EAAE;QAC1EnT,GAAG,CAACE,GAAG,CAAC,MAAM;UACZ,IAAIsiB,GAAG;UACP,MAAMxG,QAAQ,GAAG2G,kBAAkB,CAACxoB,GAAG,CAAC0oB,QAAQ,CAAC;UACjD,IAAI,IAAI,CAACC,cAAc,CAAC9G,QAAQ,CAAC,EAAE;YACjC7I,QAAQ,GAAG,IAAI,CAAC6I,QAAQ,CAAC;YACzB,OAAO,IAAI,CAACA,QAAQ,CAAC;UACvB,CAAC,MAAM,IAAI2F,SAAS,CAACmB,cAAc,CAAC9G,QAAQ,CAAC,IAAI,OAAO,IAAI,CAACA,QAAQ,CAAC,KAAK,QAAQ;UAAI;UACvF,IAAI,CAACA,QAAQ,CAAC,IAAI7I,QAAQ,EAAE;YAC1B;UACF,CAAC,MAAM,IAAI6I,QAAQ,IAAI,IAAI,EAAE;YAC3B,MAAM3gB,OAAO,GAAGJ,UAAU,CAAC,IAAI,CAAC;YAChC,MAAM8nB,MAAM,GAAG1nB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACM,OAAO;YACzD,IAAIonB,MAAM,IAAI,EAAEA,MAAM,GAAG,CAAC,CAAC,6BAA6B,IAAIA,MAAM,GAAG,GAAG,CAAC,sBAAsB5P,QAAQ,KAAKD,QAAQ,EAAE;cACpH,MAAMvW,GAAG,GAAG2S,OAAO,CAACpT,QAAQ,GAAGb,OAAO,CAACO,aAAa,GAAG,IAAI;cAC3D,MAAMwhB,QAAQ,GAAG9N,OAAO,CAACpT,QAAQ,GAAGb,OAAO,CAACE,cAAc,GAAGoB,GAAG;cAChE,MAAMqmB,KAAK,GAAG,CAACR,GAAG,GAAG9mB,OAAO,CAAC0lB,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoB,GAAG,CAACK,QAAQ,CAAC;cACzEG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACtX,OAAO,CAAEuX,YAAY,IAAK;gBACvD,IAAI7F,QAAQ,CAAC6F,YAAY,CAAC,IAAI,IAAI,EAAE;kBAClC7F,QAAQ,CAAC6F,YAAY,CAAC,CAACjB,IAAI,CAAC5E,QAAQ,EAAEjK,QAAQ,EAAED,QAAQ,EAAE2P,QAAQ,CAAC;gBACrE;cACF,CAAC,CAAC;YACJ;YACA;UACF;UACA,IAAI,CAAC7G,QAAQ,CAAC,GAAG7I,QAAQ,KAAK,IAAI,IAAI,OAAO,IAAI,CAAC6I,QAAQ,CAAC,KAAK,SAAS,GAAG,KAAK,GAAG7I,QAAQ;QAC9F,CAAC,CAAC;MACJ,CAAC;MACDuO,IAAI,CAACwB,kBAAkB,GAAGvZ,KAAK,CAAC2N,IAAI,CAClC,eAAgB,IAAI1F,GAAG,CAAC,CACtB,GAAG/X,MAAM,CAAC0Q,IAAI,CAAC,CAACxF,EAAE,GAAGrJ,OAAO,CAAC0lB,UAAU,KAAK,IAAI,GAAGrc,EAAE,GAAG,CAAC,CAAC,CAAC,EAC3D,GAAGod,OAAO,CAAC3X,MAAM,CAAC,CAAC,CAAC2Y,CAAC,EAAE5lB,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,CAAC8H,GAAG,CAAC,CAAC,CAAC2W,QAAQ,EAAEze,CAAC,CAAC,KAAK;QACjF,IAAIilB,GAAG;QACP,MAAMK,QAAQ,GAAGtlB,CAAC,CAAC,CAAC,CAAC,IAAIye,QAAQ;QACjC2G,kBAAkB,CAACrnB,GAAG,CAACunB,QAAQ,EAAE7G,QAAQ,CAAC;QAC1C,IAAI1M,OAAO,CAACqK,OAAO,IAAIpc,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,mBAAmB;UACnD,CAACilB,GAAG,GAAG9mB,OAAO,CAACqgB,gBAAgB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyG,GAAG,CAAClgB,IAAI,CAAC,CAAC0Z,QAAQ,EAAE6G,QAAQ,CAAC,CAAC;QACpF;QACA,OAAOA,QAAQ;MACjB,CAAC,CAAC,CACH,CACH,CAAC;IACH;EACF;EACA,OAAOnB,IAAI;AACb,CAAC;;AAED;AACA,IAAI0B,mBAAmB;EAAA,IAAAC,KAAA,GAAAjF,iBAAA,CAAG,WAAOzhB,GAAG,EAAEtB,OAAO,EAAEK,OAAO,EAAEuC,YAAY,EAAK;IACvE,IAAIyjB,IAAI;IACR,IAAI,CAACrmB,OAAO,CAACM,OAAO,GAAG,EAAE,CAAC,mCAAmC,CAAC,EAAE;MAC9DN,OAAO,CAACM,OAAO,IAAI,EAAE,CAAC;MACtB,MAAM0C,QAAQ,GAAG3C,OAAO,CAAC4C,cAAc;MACvC,IAAI,CAAC2Q,OAAO,CAAC/S,QAAQ,IAAI+S,OAAO,CAAC6C,iBAAiB,KAAKzT,QAAQ,EAAE;QAC/D,MAAMilB,UAAU,GAAGtlB,UAAU,CAACtC,OAAO,EAAEL,OAAO,EAAE4C,YAAY,CAAC;QAC7D,IAAIqlB,UAAU,IAAI,MAAM,IAAIA,UAAU,EAAE;UACtC,MAAMC,OAAO,GAAG5c,UAAU,CACxB,WAAWjL,OAAO,CAACyC,SAAS,IAAI9C,OAAO,CAACkD,UAAU,EAAE,EACpD,8BAA8B7C,OAAO,CAACyC,SAAS,GACjD,CAAC;UACDujB,IAAI,SAAS4B,UAAU;UACvBC,OAAO,CAAC,CAAC;QACX,CAAC,MAAM;UACL7B,IAAI,GAAG4B,UAAU;QACnB;QACA,IAAI,CAAC5B,IAAI,EAAE;UACT,MAAM,IAAI5F,KAAK,CAAC,oBAAoBpgB,OAAO,CAACyC,SAAS,IAAI9C,OAAO,CAACkD,UAAU,iBAAiB,CAAC;QAC/F;QACA,IAAI0Q,OAAO,CAACgT,MAAM,IAAI,CAACP,IAAI,CAAC8B,SAAS,EAAE;UACrC,IAAIvU,OAAO,CAACkS,aAAa,EAAE;YACzBzlB,OAAO,CAAC0lB,UAAU,GAAGM,IAAI,CAACQ,QAAQ;UACpC;UACAT,cAAc,CAACC,IAAI,EAAEhmB,OAAO,EAAE,CAAC,CAAC,gBAAgB,CAAC;UACjDgmB,IAAI,CAAC8B,SAAS,GAAG,IAAI;QACvB;QACA,MAAMC,cAAc,GAAGrd,UAAU,CAAC,gBAAgB,EAAE1K,OAAO,CAACyC,SAAS,CAAC;QACtE,IAAI8Q,OAAO,CAACgT,MAAM,EAAE;UAClB5mB,OAAO,CAACM,OAAO,IAAI,CAAC,CAAC;QACvB;QACA,IAAI;UACF,IAAI+lB,IAAI,CAACrmB,OAAO,CAAC;QACnB,CAAC,CAAC,OAAO4B,CAAC,EAAE;UACVD,YAAY,CAACC,CAAC,CAAC;QACjB;QACA,IAAIgS,OAAO,CAACgT,MAAM,EAAE;UAClB5mB,OAAO,CAACM,OAAO,IAAI,CAAC,CAAC,CAAC;QACxB;QACA,IAAIsT,OAAO,CAACkS,aAAa,EAAE;UACzB9lB,OAAO,CAACM,OAAO,IAAI,GAAG,CAAC;QACzB;QACA8nB,cAAc,CAAC,CAAC;QAChBC,qBAAqB,CAACroB,OAAO,CAACE,cAAc,CAAC;MAC/C,CAAC,MAAM;QACLmmB,IAAI,GAAG/kB,GAAG,CAACgnB,WAAW;QACtB,MAAMC,MAAM,GAAGjnB,GAAG,CAACkgB,SAAS;QAC5BgH,cAAc,CAACC,WAAW,CAACF,MAAM,CAAC,CAACnlB,IAAI,CAAC,MAAMpD,OAAO,CAACM,OAAO,IAAI,GAAG,CAAC,kBAAkB,CAAC;MAC1F;MACA,IAAIsT,OAAO,CAACmC,KAAK,IAAIsQ,IAAI,IAAIA,IAAI,CAACtQ,KAAK,EAAE;QACvC,IAAIA,KAAK;QACT,IAAI,OAAOsQ,IAAI,CAACtQ,KAAK,KAAK,QAAQ,EAAE;UAClCA,KAAK,GAAGsQ,IAAI,CAACtQ,KAAK;QACpB,CAAC,MAAM,IAAInC,OAAO,CAACsC,IAAI,IAAI,OAAOmQ,IAAI,CAACtQ,KAAK,KAAK,QAAQ,EAAE;UACzD/V,OAAO,CAACkD,UAAU,GAAG2Q,WAAW,CAACvS,GAAG,CAAC;UACrC,IAAItB,OAAO,CAACkD,UAAU,EAAE;YACtB6S,KAAK,GAAGsQ,IAAI,CAACtQ,KAAK,CAAC/V,OAAO,CAACkD,UAAU,CAAC;UACxC;UACA,IAAI0Q,OAAO,CAACkD,iBAAiB,IAAI9W,OAAO,CAACkD,UAAU,EAAE;YACnD5B,GAAG,CAACoS,YAAY,CAAC,QAAQ,EAAE1T,OAAO,CAACkD,UAAU,CAAC;UAChD;QACF;QACA,MAAM0S,QAAQ,GAAGO,UAAU,CAAC9V,OAAO,EAAEL,OAAO,CAACkD,UAAU,CAAC;QACxD,IAAI,CAACI,MAAM,CAACkT,GAAG,CAACZ,QAAQ,CAAC,EAAE;UACzB,MAAM8S,iBAAiB,GAAG3d,UAAU,CAAC,gBAAgB,EAAE1K,OAAO,CAACyC,SAAS,CAAC;UACzE,IAAI,CAAC8Q,OAAO,CAACkD,iBAAiB,IAAIlD,OAAO,CAAC9N,SAAS;UAAI;UACvD8N,OAAO,CAAC+U,aAAa,IAAItoB,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,0BAA0B;YACrEyV,KAAK,SAAS,MAAM,CAAC,iBAAiB,CAAC,CAAC3S,IAAI,CAAElB,CAAC,IAAKA,CAAC,CAAC0mB,QAAQ,CAAC7S,KAAK,EAAEH,QAAQ,CAAC,CAAC;UAClF;UACAD,aAAa,CAACC,QAAQ,EAAEG,KAAK,EAAE,CAAC,EAAE1V,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,6BAA6B,CAAC;UACpFooB,iBAAiB,CAAC,CAAC;QACrB;MACF;IACF;IACA,MAAMnc,iBAAiB,GAAGvM,OAAO,CAACwM,mBAAmB;IACrD,MAAMqc,QAAQ,GAAGA,CAAA,KAAMnH,cAAc,CAAC1hB,OAAO,EAAE,IAAI,CAAC;IACpD,IAAI4T,OAAO,CAAC1S,YAAY,IAAIqL,iBAAiB,IAAIA,iBAAiB,CAAC,MAAM,CAAC,EAAE;MAC1EA,iBAAiB,CAAC,MAAM,CAAC,CAACtF,IAAI,CAAC4hB,QAAQ,CAAC;IAC1C,CAAC,MAAM;MACLA,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EAAA,gBAjFGd,mBAAmBA,CAAAe,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAjB,KAAA,CAAArE,KAAA,OAAAC,SAAA;EAAA;AAAA,GAiFtB;AACD,IAAIyE,qBAAqB,GAAItG,QAAQ,IAAK;EACxC,IAAInO,OAAO,CAAC/S,QAAQ,IAAI+S,OAAO,CAACyR,iBAAiB,EAAE;IACjDjD,QAAQ,CAACL,QAAQ,EAAE,mBAAmB,CAAC;EACzC;AACF,CAAC;;AAED;AACA,IAAIsD,iBAAiB,GAAI/jB,GAAG,IAAK;EAC/B,IAAI,CAACqD,GAAG,CAACrE,OAAO,GAAG,CAAC,CAAC,6BAA6B,CAAC,EAAE;IACnD,MAAMN,OAAO,GAAGJ,UAAU,CAAC0B,GAAG,CAAC;IAC/B,MAAMjB,OAAO,GAAGL,OAAO,CAACQ,SAAS;IACjC,MAAM0oB,YAAY,GAAGne,UAAU,CAAC,mBAAmB,EAAE1K,OAAO,CAACyC,SAAS,CAAC;IACvE,IAAI6H,OAAO,CAACwe,wBAAwB,EAAE;MACpCC,qBAAqB,CAAC9nB,GAAG,EAAEtB,OAAO,EAAEK,OAAO,CAACgpB,WAAW,EAAE,IAAI,CAAC;IAChE;IACA,IAAI,EAAErpB,OAAO,CAACM,OAAO,GAAG,CAAC,CAAC,mBAAmB,EAAE;MAC7CN,OAAO,CAACM,OAAO,IAAI,CAAC,CAAC;MACrB,IAAIiR,MAAM;MACV,IAAI5G,OAAO,CAAC8L,iBAAiB,EAAE;QAC7BlF,MAAM,GAAGjQ,GAAG,CAACuI,YAAY,CAAChG,UAAU,CAAC;QACrC,IAAI0N,MAAM,EAAE;UACV,IAAI5G,OAAO,CAAC7E,SAAS,IAAID,cAAc,IAAIxF,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;YAC3F,MAAMsV,QAAQ,GAAGjL,OAAO,CAACuL,IAAI,GAAGF,QAAQ,CAAC1U,GAAG,CAACmQ,UAAU,EAAEpR,OAAO,EAAEiB,GAAG,CAACuI,YAAY,CAAC,QAAQ,CAAC,CAAC,GAAGmM,QAAQ,CAAC1U,GAAG,CAACmQ,UAAU,EAAEpR,OAAO,CAAC;YACjIiB,GAAG,CAACiW,SAAS,CAAC/D,MAAM,CAACoC,QAAQ,GAAG,IAAI,EAAEA,QAAQ,GAAG,IAAI,CAAC;UACxD;UACAvE,uBAAuB,CAAC/P,GAAG,EAAEjB,OAAO,CAACyC,SAAS,EAAEyO,MAAM,EAAEvR,OAAO,CAAC;QAClE;MACF;MACA,IAAI2K,OAAO,CAACkE,cAAc,IAAI,CAAC0C,MAAM,EAAE;QACrC,IAAI5G,OAAO,CAACmM,iBAAiB,IAAI,CAACnM,OAAO,CAAC4T,IAAI,IAAI5T,OAAO,CAAC7E,SAAS;QAAK;QACxEzF,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC,0BAA0B,CAAC,CAAC,yBAAyB,EAAE;UAC1EgpB,mBAAmB,CAAChoB,GAAG,CAAC;QAC1B;MACF;MACA,IAAIqJ,OAAO,CAACzJ,YAAY,EAAE;QACxB,IAAIqL,iBAAiB,GAAGjL,GAAG;QAC3B,OAAOiL,iBAAiB,GAAGA,iBAAiB,CAAC+F,UAAU,IAAI/F,iBAAiB,CAACmK,IAAI,EAAE;UACjF,IAAI/L,OAAO,CAAC8L,iBAAiB,IAAIlK,iBAAiB,CAACyG,QAAQ,KAAK,CAAC,CAAC,qBAAqBzG,iBAAiB,CAACsU,YAAY,CAAC,MAAM,CAAC,IAAItU,iBAAiB,CAAC,KAAK,CAAC,IAAIA,iBAAiB,CAAC,KAAK,CAAC,EAAE;YACrLkV,gBAAgB,CAACzhB,OAAO,EAAEA,OAAO,CAACwM,mBAAmB,GAAGD,iBAAiB,CAAC;YAC1E;UACF;QACF;MACF;MACA,IAAI5B,OAAO,CAAC4N,IAAI,IAAI,CAAC5N,OAAO,CAACmM,iBAAiB,IAAIzW,OAAO,CAAColB,SAAS,EAAE;QACnEjnB,MAAM,CAACuoB,OAAO,CAAC1mB,OAAO,CAAColB,SAAS,CAAC,CAACzb,GAAG,CAAC,CAAC,CAACzI,UAAU,EAAE,CAACylB,WAAW,CAAC,CAAC,KAAK;UACrE,IAAIA,WAAW,GAAG,EAAE,CAAC,cAAc1lB,GAAG,CAACmmB,cAAc,CAAClmB,UAAU,CAAC,EAAE;YACjE,MAAM6I,KAAK,GAAG9I,GAAG,CAACC,UAAU,CAAC;YAC7B,OAAOD,GAAG,CAACC,UAAU,CAAC;YACtBD,GAAG,CAACC,UAAU,CAAC,GAAG6I,KAAK;UACzB;QACF,CAAC,CAAC;MACJ;MACA,IAAIO,OAAO,CAAC4e,kBAAkB,EAAE;QAC9BriB,QAAQ,CAAC,MAAM6gB,mBAAmB,CAACzmB,GAAG,EAAEtB,OAAO,EAAEK,OAAO,CAAC,CAAC;MAC5D,CAAC,MAAM;QACL0nB,mBAAmB,CAACzmB,GAAG,EAAEtB,OAAO,EAAEK,OAAO,CAAC;MAC5C;IACF,CAAC,MAAM;MACL+oB,qBAAqB,CAAC9nB,GAAG,EAAEtB,OAAO,EAAEK,OAAO,CAACgpB,WAAW,EAAE,KAAK,CAAC;MAC/D,IAAIrpB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,cAAc,EAAE;QACrDmoB,qBAAqB,CAACroB,OAAO,CAACE,cAAc,CAAC;MAC/C,CAAC,MAAM,IAAIF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACmB,gBAAgB,EAAE;QAC9DnB,OAAO,CAACmB,gBAAgB,CAACiC,IAAI,CAAC,MAAMilB,qBAAqB,CAACroB,OAAO,CAACE,cAAc,CAAC,CAAC;MACpF;IACF;IACAgpB,YAAY,CAAC,CAAC;EAChB;AACF,CAAC;AACD,IAAII,mBAAmB,GAAIhoB,GAAG,IAAK;EACjC,MAAMkoB,aAAa,GAAGloB,GAAG,CAAC,MAAM,CAAC,GAAGgD,GAAG,CAACid,aAAa,CACnD5W,OAAO,CAAC0Q,OAAO,GAAG,qBAAqB/Z,GAAG,CAACkgB,SAAS,GAAG,GAAG,EAC5D,CAAC;EACDgI,aAAa,CAAC,MAAM,CAAC,GAAG,IAAI;EAC5BjX,YAAY,CAACjR,GAAG,EAAEkoB,aAAa,EAAEloB,GAAG,CAACmoB,UAAU,CAAC;AAClD,CAAC;;AAED;AACA,SAASzqB,KAAK,IAAI0qB,OAAO,QAAQ,iCAAiC;AAClE,IAAIC,kBAAkB,GAAI5H,QAAQ,IAAK;EACrC,IAAI2H,OAAO,CAAC7oB,QAAQ,IAAI6oB,OAAO,CAACE,oBAAoB,EAAE;IACpDxH,QAAQ,CAACL,QAAQ,EAAE,sBAAsB,CAAC;EAC5C;EACA,IAAI2H,OAAO,CAACG,YAAY,EAAE;IACxBzH,QAAQ,CAACL,QAAQ,EAAE,oBAAoB,CAAC;EAC1C;AACF,CAAC;AACD,IAAI6H,oBAAoB;EAAA,IAAAE,KAAA,GAAA/G,iBAAA,CAAG,WAAOzhB,GAAG,EAAK;IACxC,IAAI,CAACqD,GAAG,CAACrE,OAAO,GAAG,CAAC,CAAC,6BAA6B,CAAC,EAAE;MACnD,MAAMN,OAAO,GAAGJ,UAAU,CAAC0B,GAAG,CAAC;MAC/B,IAAIooB,OAAO,CAACzH,YAAY,EAAE;QACxB,IAAIjiB,OAAO,CAACqN,aAAa,EAAE;UACzBrN,OAAO,CAACqN,aAAa,CAACrD,GAAG,CAAE+f,UAAU,IAAKA,UAAU,CAAC,CAAC,CAAC;UACvD/pB,OAAO,CAACqN,aAAa,GAAG,KAAK,CAAC;QAChC;MACF;MACA,IAAI,CAACqc,OAAO,CAAC7oB,QAAQ,EAAE;QACrB8oB,kBAAkB,CAACroB,GAAG,CAAC;MACzB,CAAC,MAAM,IAAItB,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,cAAc,EAAE;QAC5DypB,kBAAkB,CAAC3pB,OAAO,CAACE,cAAc,CAAC;MAC5C,CAAC,MAAM,IAAIF,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACmB,gBAAgB,EAAE;QAC9DnB,OAAO,CAACmB,gBAAgB,CAACiC,IAAI,CAAC,MAAMumB,kBAAkB,CAAC3pB,OAAO,CAACE,cAAc,CAAC,CAAC;MACjF;IACF;EACF,CAAC;EAAA,gBAjBG0pB,oBAAoBA,CAAAI,GAAA;IAAA,OAAAF,KAAA,CAAAnG,KAAA,OAAAC,SAAA;EAAA;AAAA,GAiBvB;;AAED;AACA,SAAS5kB,KAAK,IAAIirB,OAAO,QAAQ,iCAAiC;AAClE,IAAIC,oBAAoB,GAAGA,CAACC,oBAAoB,EAAEC,mBAAmB,KAAK;EACxEC,cAAc,CAACF,oBAAoB,CAAC;EACpCG,oBAAoB,CAACH,oBAAoB,CAAC;EAC1CI,eAAe,CAACJ,oBAAoB,CAAC;EACrCK,gBAAgB,CAACL,oBAAoB,CAAC;EACtCM,8BAA8B,CAACN,oBAAoB,CAAC;EACpDO,2BAA2B,CAACP,oBAAoB,CAAC;EACjDQ,2BAA2B,CAACR,oBAAoB,CAAC;EACjDS,gBAAgB,CAACT,oBAAoB,CAAC;EACtCU,mBAAmB,CAACV,oBAAoB,EAAEC,mBAAmB,CAAC;EAC9DU,oBAAoB,CAACX,oBAAoB,CAAC;AAC5C,CAAC;AACD,IAAIE,cAAc,GAAIU,oBAAoB,IAAK;EAC7C,MAAMC,YAAY,GAAGD,oBAAoB,CAACE,SAAS;EACnDF,oBAAoB,CAACE,SAAS,GAAG,UAASC,IAAI,EAAE;IAC9C,MAAMC,OAAO,GAAG,IAAI;IACpB,MAAMC,WAAW,GAAGnB,OAAO,CAACnkB,SAAS,GAAGqlB,OAAO,CAAC1Z,UAAU,IAAI5L,cAAc,GAAG,KAAK;IACpF,MAAMwlB,UAAU,GAAGL,YAAY,CAACrE,IAAI,CAACwE,OAAO,EAAEC,WAAW,GAAGF,IAAI,GAAG,KAAK,CAAC;IACzE,IAAIjB,OAAO,CAAC1L,IAAI,IAAI,CAAC6M,WAAW,IAAIF,IAAI,EAAE;MACxC,IAAI7jB,EAAE,GAAG,CAAC;MACV,IAAIikB,OAAO,EAAEC,cAAc;MAC3B,MAAMC,eAAe,GAAG,CACtB,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,CACR;MACD,OAAOnkB,EAAE,GAAG8jB,OAAO,CAAC9X,UAAU,CAAC/L,MAAM,EAAED,EAAE,EAAE,EAAE;QAC3CikB,OAAO,GAAGH,OAAO,CAAC9X,UAAU,CAAChM,EAAE,CAAC,CAAC,MAAM,CAAC;QACxCkkB,cAAc,GAAGC,eAAe,CAACC,KAAK,CAAEC,YAAY,IAAK,CAACP,OAAO,CAAC9X,UAAU,CAAChM,EAAE,CAAC,CAACqkB,YAAY,CAAC,CAAC;QAC/F,IAAIJ,OAAO,EAAE;UACX,IAAIrB,OAAO,CAAC0B,kBAAkB,IAAIN,UAAU,CAACO,aAAa,EAAE;YAC1DP,UAAU,CAACO,aAAa,CAACN,OAAO,CAACL,SAAS,CAAC,IAAI,CAAC,CAAC;UACnD,CAAC,MAAM;YACLI,UAAU,CAAC1Y,WAAW,CAAC2Y,OAAO,CAACL,SAAS,CAAC,IAAI,CAAC,CAAC;UACjD;QACF;QACA,IAAIM,cAAc,EAAE;UAClBF,UAAU,CAAC1Y,WAAW,CAACwY,OAAO,CAAC9X,UAAU,CAAChM,EAAE,CAAC,CAAC4jB,SAAS,CAAC,IAAI,CAAC,CAAC;QAChE;MACF;IACF;IACA,OAAOI,UAAU;EACnB,CAAC;AACH,CAAC;AACD,IAAIf,oBAAoB,GAAIS,oBAAoB,IAAK;EACnDA,oBAAoB,CAACa,aAAa,GAAGb,oBAAoB,CAACpY,WAAW;EACrEoY,oBAAoB,CAACpY,WAAW,GAAG,UAASkZ,QAAQ,EAAE;IACpD,MAAM7d,QAAQ,GAAG6d,QAAQ,CAAC,MAAM,CAAC,GAAGC,WAAW,CAACD,QAAQ,CAAC;IACzD,MAAME,QAAQ,GAAGC,eAAe,CAAC,IAAI,CAAC3Y,UAAU,EAAErF,QAAQ,EAAE,IAAI,CAAC/C,OAAO,CAAC;IACzE,IAAI8gB,QAAQ,EAAE;MACZ,MAAME,cAAc,GAAGC,qBAAqB,CAACH,QAAQ,EAAE/d,QAAQ,CAAC;MAChE,MAAMme,WAAW,GAAGF,cAAc,CAACA,cAAc,CAAC3kB,MAAM,GAAG,CAAC,CAAC;MAC7D,MAAM8kB,YAAY,GAAG7Z,YAAY,CAAC4Z,WAAW,CAAC7Z,UAAU,EAAEuZ,QAAQ,EAAEM,WAAW,CAAC3Z,WAAW,CAAC;MAC5FkM,4BAA4B,CAAC,IAAI,CAAC;MAClC,OAAO0N,YAAY;IACrB;IACA,OAAO,IAAI,CAACR,aAAa,CAACC,QAAQ,CAAC;EACrC,CAAC;AACH,CAAC;AACD,IAAIf,oBAAoB,GAAIuB,gBAAgB,IAAK;EAC/CA,gBAAgB,CAACC,aAAa,GAAGD,gBAAgB,CAACE,WAAW;EAC7DF,gBAAgB,CAACE,WAAW,GAAG,UAASC,QAAQ,EAAE;IAChD,IAAIA,QAAQ,IAAI,OAAOA,QAAQ,CAAC,MAAM,CAAC,KAAK,WAAW,EAAE;MACvD,MAAMT,QAAQ,GAAGC,eAAe,CAAC,IAAI,CAAC3Y,UAAU,EAAEmZ,QAAQ,CAAC,MAAM,CAAC,EAAE,IAAI,CAACvhB,OAAO,CAAC;MACjF,IAAI8gB,QAAQ,EAAE;QACZ,MAAME,cAAc,GAAGC,qBAAqB,CAACH,QAAQ,EAAES,QAAQ,CAAC,MAAM,CAAC,CAAC;QACxE,MAAMC,YAAY,GAAGR,cAAc,CAACnY,IAAI,CAAEsF,CAAC,IAAKA,CAAC,KAAKoT,QAAQ,CAAC;QAC/D,IAAIC,YAAY,EAAE;UAChBA,YAAY,CAACjZ,MAAM,CAAC,CAAC;UACrBkL,4BAA4B,CAAC,IAAI,CAAC;UAClC;QACF;MACF;IACF;IACA,OAAO,IAAI,CAAC4N,aAAa,CAACE,QAAQ,CAAC;EACrC,CAAC;AACH,CAAC;AACD,IAAIhC,gBAAgB,GAAIO,oBAAoB,IAAK;EAC/C,MAAM2B,eAAe,GAAG3B,oBAAoB,CAAC4B,OAAO;EACpD5B,oBAAoB,CAAC4B,OAAO,GAAG,UAAS,GAAGvO,WAAW,EAAE;IACtDA,WAAW,CAAC/N,OAAO,CAAEwb,QAAQ,IAAK;MAChC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAG,IAAI,CAACe,aAAa,CAACxR,cAAc,CAACyQ,QAAQ,CAAC;MACxD;MACA,MAAM7d,QAAQ,GAAG6d,QAAQ,CAAC,MAAM,CAAC,GAAGC,WAAW,CAACD,QAAQ,CAAC;MACzD,MAAME,QAAQ,GAAGC,eAAe,CAAC,IAAI,CAAC3Y,UAAU,EAAErF,QAAQ,EAAE,IAAI,CAAC/C,OAAO,CAAC;MACzE,IAAI8gB,QAAQ,EAAE;QACZ,MAAMc,eAAe,GAAGtoB,QAAQ,CAAC6W,cAAc,CAAC,EAAE,CAAC;QACnDyR,eAAe,CAAC,MAAM,CAAC,GAAGhB,QAAQ;QAClCE,QAAQ,CAAC,MAAM,CAAC,CAACzZ,UAAU,CAACsZ,aAAa,CAACiB,eAAe,CAAC;QAC1DhB,QAAQ,CAAC,MAAM,CAAC,GAAGgB,eAAe;QAClC,MAAMZ,cAAc,GAAGC,qBAAqB,CAACH,QAAQ,EAAE/d,QAAQ,CAAC;QAChE,MAAMme,WAAW,GAAGF,cAAc,CAAC,CAAC,CAAC;QACrC,OAAO1Z,YAAY,CAAC4Z,WAAW,CAAC7Z,UAAU,EAAEuZ,QAAQ,EAAEM,WAAW,CAAC3Z,WAAW,CAAC;MAChF;MACA,IAAIqZ,QAAQ,CAAC7Y,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC6Y,QAAQ,CAAChiB,YAAY,CAAC,MAAM,CAAC,EAAE;QAC9DgiB,QAAQ,CAAClN,MAAM,GAAG,IAAI;MACxB;MACA,OAAO+N,eAAe,CAAC/F,IAAI,CAAC,IAAI,EAAEkF,QAAQ,CAAC;IAC7C,CAAC,CAAC;EACJ,CAAC;AACH,CAAC;AACD,IAAItB,eAAe,GAAIQ,oBAAoB,IAAK;EAC9CA,oBAAoB,CAAC+B,MAAM,GAAG,UAAS,GAAG1O,WAAW,EAAE;IACrDA,WAAW,CAAC/N,OAAO,CAAEwb,QAAQ,IAAK;MAChC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAG,IAAI,CAACe,aAAa,CAACxR,cAAc,CAACyQ,QAAQ,CAAC;MACxD;MACA,IAAI,CAAClZ,WAAW,CAACkZ,QAAQ,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC;AACH,CAAC;AACD,IAAInB,2BAA2B,GAAIK,oBAAoB,IAAK;EAC1D,MAAMgC,0BAA0B,GAAGhC,oBAAoB,CAACiC,kBAAkB;EAC1EjC,oBAAoB,CAACiC,kBAAkB,GAAG,UAASC,QAAQ,EAAEld,IAAI,EAAE;IACjE,IAAIkd,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,WAAW,EAAE;MACzD,OAAOF,0BAA0B,CAACpG,IAAI,CAAC,IAAI,EAAEsG,QAAQ,EAAEld,IAAI,CAAC;IAC9D;IACA,MAAMmd,SAAS,GAAG,IAAI,CAACN,aAAa,CAACnZ,aAAa,CAAC,GAAG,CAAC;IACvD,IAAIrD,IAAI;IACR8c,SAAS,CAACvW,SAAS,GAAG5G,IAAI;IAC1B,IAAIkd,QAAQ,KAAK,YAAY,EAAE;MAC7B,OAAO7c,IAAI,GAAG8c,SAAS,CAACzD,UAAU,EAAE;QAClC,IAAI,CAACkD,OAAO,CAACvc,IAAI,CAAC;MACpB;IACF,CAAC,MAAM,IAAI6c,QAAQ,KAAK,WAAW,EAAE;MACnC,OAAO7c,IAAI,GAAG8c,SAAS,CAACzD,UAAU,EAAE;QAClC,IAAI,CAACqD,MAAM,CAAC1c,IAAI,CAAC;MACnB;IACF;EACF,CAAC;AACH,CAAC;AACD,IAAIua,2BAA2B,GAAII,oBAAoB,IAAK;EAC1DA,oBAAoB,CAACoC,kBAAkB,GAAG,UAASF,QAAQ,EAAEld,IAAI,EAAE;IACjE,IAAI,CAACid,kBAAkB,CAACC,QAAQ,EAAEld,IAAI,CAAC;EACzC,CAAC;AACH,CAAC;AACD,IAAI0a,8BAA8B,GAAIM,oBAAoB,IAAK;EAC7D,MAAMqC,6BAA6B,GAAGrC,oBAAoB,CAACsC,qBAAqB;EAChFtC,oBAAoB,CAACsC,qBAAqB,GAAG,UAASJ,QAAQ,EAAEnN,OAAO,EAAE;IACvE,IAAImN,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,WAAW,EAAE;MACzD,OAAOG,6BAA6B,CAACzG,IAAI,CAAC,IAAI,EAAEsG,QAAQ,EAAEnN,OAAO,CAAC;IACpE;IACA,IAAImN,QAAQ,KAAK,YAAY,EAAE;MAC7B,IAAI,CAACN,OAAO,CAAC7M,OAAO,CAAC;MACrB,OAAOA,OAAO;IAChB,CAAC,MAAM,IAAImN,QAAQ,KAAK,WAAW,EAAE;MACnC,IAAI,CAACH,MAAM,CAAChN,OAAO,CAAC;MACpB,OAAOA,OAAO;IAChB;IACA,OAAOA,OAAO;EAChB,CAAC;AACH,CAAC;AACD,IAAI8K,gBAAgB,GAAIT,oBAAoB,IAAK;EAC/C,MAAMmD,UAAU,GAAG9uB,MAAM,CAAC+uB,wBAAwB,CAACC,IAAI,CAAClH,SAAS,EAAE,aAAa,CAAC;EACjF9nB,MAAM,CAACC,cAAc,CAAC0rB,oBAAoB,EAAE,eAAe,EAAEmD,UAAU,CAAC;EACxE,IAAIrD,OAAO,CAAC5I,6BAA6B,EAAE;IACzC7iB,MAAM,CAACC,cAAc,CAAC0rB,oBAAoB,EAAE,aAAa,EAAE;MACzD;MACA;MACArrB,GAAGA,CAAA,EAAG;QACJ,MAAM2uB,YAAY,GAAGC,oBAAoB,CAAC,IAAI,CAACra,UAAU,CAAC;QAC1D,MAAME,WAAW,GAAGka,YAAY,CAACzjB,GAAG,CAAEoG,IAAI,IAAK;UAC7C,IAAI3G,EAAE,EAAEC,EAAE;UACV,MAAMqG,IAAI,GAAG,EAAE;UACf,IAAI4d,WAAW,GAAGvd,IAAI,CAACoC,WAAW;UAClC,OAAOmb,WAAW,IAAIA,WAAW,CAAC,MAAM,CAAC,KAAKvd,IAAI,CAAC,MAAM,CAAC,EAAE;YAC1D,IAAIud,WAAW,CAAC3a,QAAQ,KAAK,CAAC,CAAC,mBAAmB2a,WAAW,CAAC3a,QAAQ,KAAK,CAAC,CAAC,oBAAoB;cAC/FjD,IAAI,CAAC9I,IAAI,CAAC,CAACyC,EAAE,GAAG,CAACD,EAAE,GAAGkkB,WAAW,CAACpa,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG9J,EAAE,CAACoV,IAAI,CAAC,CAAC,KAAK,IAAI,GAAGnV,EAAE,GAAG,EAAE,CAAC;YACjG;YACAikB,WAAW,GAAGA,WAAW,CAACnb,WAAW;UACvC;UACA,OAAOzC,IAAI,CAACZ,MAAM,CAAEtP,GAAG,IAAKA,GAAG,KAAK,EAAE,CAAC,CAACwP,IAAI,CAAC,GAAG,CAAC;QACnD,CAAC,CAAC,CAACF,MAAM,CAAEY,IAAI,IAAKA,IAAI,KAAK,EAAE,CAAC,CAACV,IAAI,CAAC,GAAG,CAAC;QAC1C,OAAO,GAAG,GAAGkE,WAAW,GAAG,GAAG;MAChC,CAAC;MACD;MACA;MACA;MACAtT,GAAGA,CAACmK,KAAK,EAAE;QACT,MAAMqjB,YAAY,GAAGC,oBAAoB,CAAC,IAAI,CAACra,UAAU,CAAC;QAC1Doa,YAAY,CAACpd,OAAO,CAAED,IAAI,IAAK;UAC7B,IAAIud,WAAW,GAAGvd,IAAI,CAACoC,WAAW;UAClC,OAAOmb,WAAW,IAAIA,WAAW,CAAC,MAAM,CAAC,KAAKvd,IAAI,CAAC,MAAM,CAAC,EAAE;YAC1D,MAAMwd,GAAG,GAAGD,WAAW;YACvBA,WAAW,GAAGA,WAAW,CAACnb,WAAW;YACrCob,GAAG,CAACpa,MAAM,CAAC,CAAC;UACd;UACA,IAAIpD,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;YACvB,MAAMyd,QAAQ,GAAG,IAAI,CAACjB,aAAa,CAACxR,cAAc,CAAChR,KAAK,CAAC;YACzDyjB,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE;YACrBtb,YAAY,CAACnC,IAAI,CAACoO,aAAa,EAAEqP,QAAQ,EAAEzd,IAAI,CAACoC,WAAW,CAAC;UAC9D,CAAC,MAAM;YACLpC,IAAI,CAACoD,MAAM,CAAC,CAAC;UACf;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IACLhV,MAAM,CAACC,cAAc,CAAC0rB,oBAAoB,EAAE,aAAa,EAAE;MACzDrrB,GAAGA,CAAA,EAAG;QACJ,IAAI2K,EAAE;QACN,MAAMsiB,QAAQ,GAAGC,eAAe,CAAC,IAAI,CAAC3Y,UAAU,EAAE,EAAE,EAAE,IAAI,CAACpI,OAAO,CAAC;QACnE,IAAI,CAAC,CAACxB,EAAE,GAAGsiB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACvZ,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG/I,EAAE,CAACuJ,QAAQ,MAAM,CAAC,CAAC,iBAAiB;UAClH,OAAO+Y,QAAQ,CAACvZ,WAAW,CAACe,WAAW;QACzC,CAAC,MAAM,IAAIwY,QAAQ,EAAE;UACnB,OAAOA,QAAQ,CAACxY,WAAW;QAC7B,CAAC,MAAM;UACL,OAAO,IAAI,CAACua,aAAa;QAC3B;MACF,CAAC;MACD7tB,GAAGA,CAACmK,KAAK,EAAE;QACT,IAAIX,EAAE;QACN,MAAMsiB,QAAQ,GAAGC,eAAe,CAAC,IAAI,CAAC3Y,UAAU,EAAE,EAAE,EAAE,IAAI,CAACpI,OAAO,CAAC;QACnE,IAAI,CAAC,CAACxB,EAAE,GAAGsiB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACvZ,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG/I,EAAE,CAACuJ,QAAQ,MAAM,CAAC,CAAC,iBAAiB;UAClH+Y,QAAQ,CAACvZ,WAAW,CAACe,WAAW,GAAGnJ,KAAK;QAC1C,CAAC,MAAM,IAAI2hB,QAAQ,EAAE;UACnBA,QAAQ,CAACxY,WAAW,GAAGnJ,KAAK;QAC9B,CAAC,MAAM;UACL,IAAI,CAAC0jB,aAAa,GAAG1jB,KAAK;UAC1B,MAAMof,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;UAClC,IAAIA,aAAa,EAAE;YACjBjX,YAAY,CAAC,IAAI,EAAEiX,aAAa,EAAE,IAAI,CAACC,UAAU,CAAC;UACpD;QACF;MACF;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAIoB,mBAAmB,GAAGA,CAACvpB,GAAG,EAAEjB,OAAO,KAAK;EAC1C,MAAM0tB,YAAY,SAASzf,KAAK,CAAC;IAC/B0f,IAAIA,CAAC5U,CAAC,EAAE;MACN,OAAO,IAAI,CAACA,CAAC,CAAC;IAChB;EACF;EACA,IAAI/Y,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,0BAA0B;IAChD,MAAM2tB,YAAY,GAAG3sB,GAAG,CAAC4sB,gBAAgB,CAAC,YAAY,CAAC;IACvD1vB,MAAM,CAACC,cAAc,CAAC6C,GAAG,EAAE,UAAU,EAAE;MACrCxC,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACuU,UAAU,CAACrJ,GAAG,CAAEoP,CAAC,IAAKA,CAAC,CAACpG,QAAQ,KAAK,CAAC,CAAC;MACrD;IACF,CAAC,CAAC;IACFxU,MAAM,CAACC,cAAc,CAAC6C,GAAG,EAAE,mBAAmB,EAAE;MAC9CxC,GAAGA,CAAA,EAAG;QACJ,OAAOwC,GAAG,CAACwM,QAAQ,CAACxG,MAAM;MAC5B;IACF,CAAC,CAAC;IACF9I,MAAM,CAACC,cAAc,CAAC6C,GAAG,EAAE,YAAY,EAAE;MACvCxC,GAAGA,CAAA,EAAG;QACJ,MAAMuU,UAAU,GAAG4a,YAAY,CAACtH,IAAI,CAAC,IAAI,CAAC;QAC1C,IAAI,CAAChiB,GAAG,CAACrE,OAAO,GAAG,CAAC,CAAC,6BAA6B,CAAC,IAAIV,UAAU,CAAC,IAAI,CAAC,CAACU,OAAO,GAAG,CAAC,CAAC,mBAAmB;UACrG,MAAMiK,MAAM,GAAG,IAAIwjB,YAAY,CAAC,CAAC;UACjC,KAAK,IAAI1mB,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGgM,UAAU,CAAC/L,MAAM,EAAED,EAAE,EAAE,EAAE;YAC7C,MAAMkX,IAAI,GAAGlL,UAAU,CAAChM,EAAE,CAAC,CAAC,MAAM,CAAC;YACnC,IAAIkX,IAAI,EAAE;cACRhU,MAAM,CAACtD,IAAI,CAACsX,IAAI,CAAC;YACnB;UACF;UACA,OAAOhU,MAAM;QACf;QACA,OAAOwjB,YAAY,CAAC9R,IAAI,CAAC5I,UAAU,CAAC;MACtC;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAIqa,oBAAoB,GAAIra,UAAU,IAAK;EACzC,MAAMoa,YAAY,GAAG,EAAE;EACvB,KAAK,MAAMxS,SAAS,IAAI3M,KAAK,CAAC2N,IAAI,CAAC5I,UAAU,CAAC,EAAE;IAC9C,IAAI4H,SAAS,CAAC,MAAM,CAAC,EAAE;MACrBwS,YAAY,CAACxmB,IAAI,CAACgU,SAAS,CAAC;IAC9B;IACAwS,YAAY,CAACxmB,IAAI,CAAC,GAAGymB,oBAAoB,CAACzS,SAAS,CAAC5H,UAAU,CAAC,CAAC;EAClE;EACA,OAAOoa,YAAY;AACrB,CAAC;AACD,IAAI3B,WAAW,GAAI1b,IAAI,IAAKA,IAAI,CAAC,MAAM,CAAC,IAAIA,IAAI,CAAC4C,QAAQ,KAAK,CAAC,IAAI5C,IAAI,CAACvG,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE;AAClG,IAAImiB,eAAe,GAAGA,CAAC3Y,UAAU,EAAErF,QAAQ,EAAEmgB,QAAQ,KAAK;EACxD,IAAI9mB,EAAE,GAAG,CAAC;EACV,IAAI4T,SAAS;EACb,OAAO5T,EAAE,GAAGgM,UAAU,CAAC/L,MAAM,EAAED,EAAE,EAAE,EAAE;IACnC4T,SAAS,GAAG5H,UAAU,CAAChM,EAAE,CAAC;IAC1B,IAAI4T,SAAS,CAAC,MAAM,CAAC,IAAIA,SAAS,CAAC,MAAM,CAAC,KAAKjN,QAAQ,IAAIiN,SAAS,CAAC,MAAM,CAAC,KAAKkT,QAAQ,EAAE;MACzF,OAAOlT,SAAS;IAClB;IACAA,SAAS,GAAG+Q,eAAe,CAAC/Q,SAAS,CAAC5H,UAAU,EAAErF,QAAQ,EAAEmgB,QAAQ,CAAC;IACrE,IAAIlT,SAAS,EAAE;MACb,OAAOA,SAAS;IAClB;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAIiR,qBAAqB,GAAGA,CAAC9S,CAAC,EAAEpL,QAAQ,KAAK;EAC3C,MAAMqF,UAAU,GAAG,CAAC+F,CAAC,CAAC;EACtB,OAAO,CAACA,CAAC,GAAGA,CAAC,CAAC5G,WAAW,KAAK4G,CAAC,CAAC,MAAM,CAAC,KAAKpL,QAAQ,EAAE;IACpDqF,UAAU,CAACpM,IAAI,CAACmS,CAAC,CAAC;EACpB;EACA,OAAO/F,UAAU;AACnB,CAAC;;AAED;AACA,IAAI+a,mBAAmB,GAAGA,CAAC/H,IAAI,EAAEgI,WAAW,KAAK;EAC/C7F,cAAc,CAAC8F,MAAM,CAACD,WAAW,CAAC,CAAC,CAAC,EAAEE,kBAAkB,CAAClI,IAAI,EAAEgI,WAAW,CAAC,CAAC;AAC9E,CAAC;AACD,IAAIE,kBAAkB,GAAGA,CAAClI,IAAI,EAAEgI,WAAW,KAAK;EAC9C,MAAMhuB,OAAO,GAAG;IACdC,OAAO,EAAE+tB,WAAW,CAAC,CAAC,CAAC;IACvBvrB,SAAS,EAAEurB,WAAW,CAAC,CAAC;EAC1B,CAAC;EACD,IAAIrlB,OAAO,CAAC4d,MAAM,EAAE;IAClBvmB,OAAO,CAAColB,SAAS,GAAG4I,WAAW,CAAC,CAAC,CAAC;EACpC;EACA,IAAIrlB,OAAO,CAACiZ,YAAY,EAAE;IACxB5hB,OAAO,CAACgpB,WAAW,GAAGgF,WAAW,CAAC,CAAC,CAAC;EACtC;EACA,IAAIrlB,OAAO,CAAC8c,aAAa,EAAE;IACzBzlB,OAAO,CAAC0lB,UAAU,GAAGM,IAAI,CAACN,UAAU;EACtC;EACA,IAAI/c,OAAO,CAACsV,OAAO,EAAE;IACnBje,OAAO,CAACqgB,gBAAgB,GAAG,EAAE;EAC/B;EACA,IAAI1X,OAAO,CAAClD,SAAS,IAAI,CAACD,cAAc,IAAIxF,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;IAC5FD,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC;EACvB;EACA,IAAI0I,OAAO,CAAC4S,qBAAqB,EAAE;IACjC,IAAI5S,OAAO,CAACqO,MAAM,IAAIhX,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;MACtE4pB,oBAAoB,CAAC7D,IAAI,CAACC,SAAS,EAAEjmB,OAAO,CAAC;IAC/C;EACF,CAAC,MAAM;IACL,IAAI2I,OAAO,CAACwlB,iBAAiB,EAAE;MAC7B3D,mBAAmB,CAACxE,IAAI,CAACC,SAAS,EAAEjmB,OAAO,CAAC;IAC9C;IACA,IAAI2I,OAAO,CAACylB,YAAY,EAAE;MACxBpE,cAAc,CAAChE,IAAI,CAACC,SAAS,CAAC;IAChC;IACA,IAAItd,OAAO,CAAC2iB,kBAAkB,EAAE;MAC9BrB,oBAAoB,CAACjE,IAAI,CAACC,SAAS,CAAC;IACtC;IACA,IAAItd,OAAO,CAAC0lB,wBAAwB,IAAIruB,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;MACxFsqB,gBAAgB,CAACvE,IAAI,CAACC,SAAS,CAAC;IAClC;EACF;EACA,MAAMqI,yBAAyB,GAAGtI,IAAI,CAACC,SAAS,CAACjB,iBAAiB;EAClE,MAAMuJ,4BAA4B,GAAGvI,IAAI,CAACC,SAAS,CAACsD,oBAAoB;EACxEprB,MAAM,CAACoH,MAAM,CAACygB,IAAI,CAACC,SAAS,EAAE;IAC5BuI,cAAcA,CAAA,EAAG;MACf1uB,YAAY,CAAC,IAAI,EAAEE,OAAO,CAAC;IAC7B,CAAC;IACDglB,iBAAiBA,CAAA,EAAG;MAClB,MAAMrlB,OAAO,GAAGJ,UAAU,CAAC,IAAI,CAAC;MAChCwpB,qBAAqB,CAAC,IAAI,EAAEppB,OAAO,EAAEK,OAAO,CAACgpB,WAAW,EAAE,KAAK,CAAC;MAChEhE,iBAAiB,CAAC,IAAI,CAAC;MACvB,IAAIrc,OAAO,CAACqc,iBAAiB,IAAIsJ,yBAAyB,EAAE;QAC1DA,yBAAyB,CAAChI,IAAI,CAAC,IAAI,CAAC;MACtC;IACF,CAAC;IACDiD,oBAAoBA,CAAA,EAAG;MACrBA,oBAAoB,CAAC,IAAI,CAAC;MAC1B,IAAI5gB,OAAO,CAAC4gB,oBAAoB,IAAIgF,4BAA4B,EAAE;QAChEA,4BAA4B,CAACjI,IAAI,CAAC,IAAI,CAAC;MACzC;IACF,CAAC;IACDmI,cAAcA,CAAA,EAAG;MACf,IAAIjpB,cAAc,EAAE;QAClB,IAAI,CAAC,IAAI,CAAC4L,UAAU,EAAE;UACpB,IAAIzI,OAAO,CAAC+lB,oBAAoB,EAAE;YAChC,IAAI,CAACC,YAAY,CAAC;cAChB9Y,IAAI,EAAE,MAAM;cACZ+Y,cAAc,EAAE,CAAC,EAAE5uB,OAAO,CAACC,OAAO,GAAG,EAAE,CAAC;YAC1C,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,IAAI,CAAC0uB,YAAY,CAAC;cAAE9Y,IAAI,EAAE;YAAO,CAAC,CAAC;UACrC;QACF,CAAC,MAAM;UACL,IAAI,IAAI,CAACzE,UAAU,CAACyE,IAAI,KAAK,MAAM,EAAE;YACnC,MAAM,IAAIuK,KAAK,CACb,6CAA6CpgB,OAAO,CAACyC,SAAS,oBAAoB,IAAI,CAAC2O,UAAU,CAACyE,IAAI,+CACxG,CAAC;UACH;QACF;MACF,CAAC,MAAM;QACL,IAAI,CAACzE,UAAU,GAAG,IAAI;MACxB;IACF;EACF,CAAC,CAAC;EACF4U,IAAI,CAAC6I,EAAE,GAAG7uB,OAAO,CAACyC,SAAS;EAC3B,OAAOsjB,cAAc,CAACC,IAAI,EAAEhmB,OAAO,EAAE,CAAC,CAAC,6BAA6B,CAAC,CAAC,gBAAgB,CAAC;AACzF,CAAC;AACD,IAAI8uB,eAAe,GAAI7tB,GAAG,IAAK;EAC7B,IAAI0H,OAAO,CAAC+M,KAAK,IAAI/M,OAAO,CAACkN,IAAI,IAAI,CAAClN,OAAO,CAACnI,QAAQ,EAAE;IACtD,MAAMqV,IAAI,GAAGrC,WAAW,CAACvS,GAAG,CAAC;IAC7B,MAAMtB,OAAO,GAAGJ,UAAU,CAAC0B,GAAG,CAAC;IAC/B,IAAItB,OAAO,CAACkD,UAAU,KAAKgT,IAAI,EAAE;MAC/B,MAAM7V,OAAO,GAAGL,OAAO,CAACQ,SAAS;MACjC,MAAM4uB,UAAU,GAAG9tB,GAAG,CAAC,MAAM,CAAC;MAC9B,MAAMsU,QAAQ,GAAGO,UAAU,CAAC9V,OAAO,EAAE6V,IAAI,CAAC;MAC1C,MAAMH,KAAK,GAAGzU,GAAG,CAACgnB,WAAW,CAACvS,KAAK,CAACG,IAAI,CAAC;MACzC,MAAMxK,KAAK,GAAGrL,OAAO,CAACC,OAAO;MAC7B,IAAIyV,KAAK,EAAE;QACT,IAAI,CAACzS,MAAM,CAACkT,GAAG,CAACZ,QAAQ,CAAC,EAAE;UACzBD,aAAa,CAACC,QAAQ,EAAEG,KAAK,EAAE,CAAC,EAAErK,KAAK,GAAG,CAAC,CAAC,6BAA6B,CAAC;QAC5E;QACA1L,OAAO,CAACkD,UAAU,GAAGgT,IAAI;QACzB5U,GAAG,CAACiW,SAAS,CAAC/D,MAAM,CAAC4b,UAAU,GAAG,IAAI,EAAEA,UAAU,GAAG,IAAI,CAAC;QAC1DhZ,YAAY,CAACpW,OAAO,CAAC;QACrBykB,WAAW,CAACnjB,GAAG,CAAC;MAClB;IACF;EACF;AACF,CAAC;;AAED;AACA,SAAStC,KAAK,IAAIqwB,OAAO,QAAQ,iCAAiC;;AAElE;AACA,IAAIC,QAAQ,GAAGA,CAAClvB,WAAW,EAAEC,OAAO,EAAEuC,YAAY,KAAK;EACrD,MAAM5C,OAAO,GAAGJ,UAAU,CAACQ,WAAW,CAAC;EACvCJ,OAAO,CAACM,OAAO,GAAG,CAAC,CAAC;EACpBynB,mBAAmB,CAAC3nB,WAAW,EAAEJ,OAAO,EAAEK,OAAO,EAAEuC,YAAY,CAAC;AAClE,CAAC;;AAED;AACA,IAAI2sB,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACjD,IAAIhmB,EAAE;EACN,IAAI4lB,OAAO,CAACnkB,OAAO,IAAI3D,WAAW,CAAC4D,IAAI,EAAE;IACvC5D,WAAW,CAAC4D,IAAI,CAAC,cAAc,CAAC;EAClC;EACAmC,eAAe,CAAC,CAAC;EACjB,MAAMoiB,YAAY,GAAG3kB,UAAU,CAAC,eAAe,CAAC;EAChD,MAAM4kB,OAAO,GAAG,EAAE;EAClB,MAAMC,OAAO,GAAGH,OAAO,CAACG,OAAO,IAAI,EAAE;EACrC,MAAMC,eAAe,GAAGxrB,GAAG,CAACmkB,cAAc;EAC1C,MAAMhkB,IAAI,GAAGF,GAAG,CAACE,IAAI;EACrB,MAAMsrB,WAAW,GAAG,eAAgBtrB,IAAI,CAACoF,aAAa,CAAC,eAAe,CAAC;EACvE,MAAMmmB,UAAU,GAAG,eAAgBzrB,GAAG,CAACmP,aAAa,CAAC,OAAO,CAAC;EAC7D,MAAMuc,0BAA0B,GAAG,EAAE;EACrC,IAAIC,eAAe;EACnB,IAAIC,eAAe,GAAG,IAAI;EAC1B1xB,MAAM,CAACoH,MAAM,CAACjB,GAAG,EAAE8qB,OAAO,CAAC;EAC3B9qB,GAAG,CAACC,cAAc,GAAG,IAAI8D,GAAG,CAAC+mB,OAAO,CAACU,YAAY,IAAI,IAAI,EAAE7rB,GAAG,CAAC8rB,OAAO,CAAC,CAACvnB,IAAI;EAC5E,IAAIwmB,OAAO,CAACxnB,UAAU,EAAE;IACtB,IAAI4nB,OAAO,CAACY,SAAS,EAAE;MACrB1rB,GAAG,CAACrE,OAAO,IAAI,CAAC,CAAC;IACnB;EACF;EACA,IAAI+uB,OAAO,CAAC5Y,iBAAiB,EAAE;IAC7B9R,GAAG,CAACrE,OAAO,IAAI,CAAC,CAAC;EACnB;EACA,IAAIgwB,iBAAiB,GAAG,KAAK;EAC7Bd,WAAW,CAACxlB,GAAG,CAAEumB,UAAU,IAAK;IAC9BA,UAAU,CAAC,CAAC,CAAC,CAACvmB,GAAG,CAAEqkB,WAAW,IAAK;MACjC,IAAIlH,GAAG;MACP,MAAM9mB,OAAO,GAAG;QACdC,OAAO,EAAE+tB,WAAW,CAAC,CAAC,CAAC;QACvBvrB,SAAS,EAAEurB,WAAW,CAAC,CAAC,CAAC;QACzB5I,SAAS,EAAE4I,WAAW,CAAC,CAAC,CAAC;QACzBhF,WAAW,EAAEgF,WAAW,CAAC,CAAC;MAC5B,CAAC;MACD,IAAIhuB,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,yBAAyB;QAC/CgwB,iBAAiB,GAAG,IAAI;MAC1B;MACA,IAAIjB,OAAO,CAACzI,MAAM,EAAE;QAClBvmB,OAAO,CAAColB,SAAS,GAAG4I,WAAW,CAAC,CAAC,CAAC;MACpC;MACA,IAAIgB,OAAO,CAACpN,YAAY,EAAE;QACxB5hB,OAAO,CAACgpB,WAAW,GAAGgF,WAAW,CAAC,CAAC,CAAC;MACtC;MACA,IAAIgB,OAAO,CAAC/Q,OAAO,EAAE;QACnBje,OAAO,CAACqgB,gBAAgB,GAAG,EAAE;MAC/B;MACA,IAAI2O,OAAO,CAACvJ,aAAa,EAAE;QACzBzlB,OAAO,CAAC0lB,UAAU,GAAG,CAACoB,GAAG,GAAGkH,WAAW,CAAC,CAAC,CAAC,KAAK,IAAI,GAAGlH,GAAG,GAAG,CAAC,CAAC;MAChE;MACA,IAAIkI,OAAO,CAACvpB,SAAS,IAAI,CAACD,cAAc,IAAIxF,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;QAC5FD,OAAO,CAACC,OAAO,IAAI,CAAC,CAAC;MACvB;MACA,MAAM2K,OAAO,GAAGokB,OAAO,CAACmB,gBAAgB,IAAIf,OAAO,CAACe,gBAAgB,GAAGf,OAAO,CAACe,gBAAgB,CAACnwB,OAAO,CAACyC,SAAS,CAAC,GAAGzC,OAAO,CAACyC,SAAS;MACtI,MAAM2tB,WAAW,GAAG,cAAc/rB,WAAW,CAAC;QAC5C;QACA4jB,WAAWA,CAACoI,IAAI,EAAE;UAChB,KAAK,CAACA,IAAI,CAAC;UACX,IAAI,CAACC,2BAA2B,GAAG,KAAK;UACxCD,IAAI,GAAG,IAAI;UACXvwB,YAAY,CAACuwB,IAAI,EAAErwB,OAAO,CAAC;UAC3B,IAAIgvB,OAAO,CAACvpB,SAAS,IAAIzF,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;YACzE,IAAIuF,cAAc,EAAE;cAClB,IAAI,CAAC6qB,IAAI,CAACjf,UAAU,EAAE;gBACpB,IAAI4d,OAAO,CAACN,oBAAoB,EAAE;kBAChC2B,IAAI,CAAC1B,YAAY,CAAC;oBAChB9Y,IAAI,EAAE,MAAM;oBACZ+Y,cAAc,EAAE,CAAC,EAAE5uB,OAAO,CAACC,OAAO,GAAG,EAAE,CAAC;kBAC1C,CAAC,CAAC;gBACJ,CAAC,MAAM;kBACLowB,IAAI,CAAC1B,YAAY,CAAC;oBAAE9Y,IAAI,EAAE;kBAAO,CAAC,CAAC;gBACrC;cACF,CAAC,MAAM;gBACL,IAAIwa,IAAI,CAACjf,UAAU,CAACyE,IAAI,KAAK,MAAM,EAAE;kBACnC,MAAM,IAAIuK,KAAK,CACb,6CAA6CpgB,OAAO,CAACyC,SAAS,oBAAoB4tB,IAAI,CAACjf,UAAU,CAACyE,IAAI,+CACxG,CAAC;gBACH;cACF;YACF,CAAC,MAAM,IAAI,CAACmZ,OAAO,CAACvY,iBAAiB,IAAI,EAAE,YAAY,IAAI4Z,IAAI,CAAC,EAAE;cAChEA,IAAI,CAACjf,UAAU,GAAGif,IAAI;YACxB;UACF;QACF;QACArL,iBAAiBA,CAAA,EAAG;UAClB,MAAMrlB,OAAO,GAAGJ,UAAU,CAAC,IAAI,CAAC;UAChC,IAAI,CAAC,IAAI,CAAC+wB,2BAA2B,EAAE;YACrC,IAAI,CAACA,2BAA2B,GAAG,IAAI;YACvCvH,qBAAqB,CAAC,IAAI,EAAEppB,OAAO,EAAEK,OAAO,CAACgpB,WAAW,EAAE,KAAK,CAAC;UAClE;UACA,IAAI4G,eAAe,EAAE;YACnBW,YAAY,CAACX,eAAe,CAAC;YAC7BA,eAAe,GAAG,IAAI;UACxB;UACA,IAAIC,eAAe,EAAE;YACnBF,0BAA0B,CAAC/oB,IAAI,CAAC,IAAI,CAAC;UACvC,CAAC,MAAM;YACLtC,GAAG,CAACE,GAAG,CAAC,MAAMwgB,iBAAiB,CAAC,IAAI,CAAC,CAAC;UACxC;QACF;QACAuE,oBAAoBA,CAAA,EAAG;UACrBjlB,GAAG,CAACE,GAAG,CAAC,MAAM+kB,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC3C;QACAiH,gBAAgBA,CAAA,EAAG;UACjB,OAAOjxB,UAAU,CAAC,IAAI,CAAC,CAACuB,gBAAgB;QAC1C;MACF,CAAC;MACD,IAAIkuB,OAAO,CAACzT,qBAAqB,EAAE;QACjC,IAAIyT,OAAO,CAAChY,MAAM,IAAIhX,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;UACtE4pB,oBAAoB,CAACuG,WAAW,CAACnK,SAAS,EAAEjmB,OAAO,CAAC;QACtD;MACF,CAAC,MAAM;QACL,IAAIgvB,OAAO,CAACb,iBAAiB,EAAE;UAC7B3D,mBAAmB,CAAC4F,WAAW,CAACnK,SAAS,EAAEjmB,OAAO,CAAC;QACrD;QACA,IAAIgvB,OAAO,CAACZ,YAAY,EAAE;UACxBpE,cAAc,CAACoG,WAAW,CAACnK,SAAS,CAAC;QACvC;QACA,IAAI+I,OAAO,CAAC1D,kBAAkB,EAAE;UAC9BrB,oBAAoB,CAACmG,WAAW,CAACnK,SAAS,CAAC;QAC7C;QACA,IAAI+I,OAAO,CAACX,wBAAwB,IAAIruB,OAAO,CAACC,OAAO,GAAG,CAAC,CAAC,8BAA8B;UACxFsqB,gBAAgB,CAAC6F,WAAW,CAACnK,SAAS,CAAC;QACzC;MACF;MACA,IAAI+I,OAAO,CAAC9I,cAAc,IAAIlmB,OAAO,CAACC,OAAO,GAAG,EAAE,CAAC,sBAAsB;QACvEmwB,WAAW,CAAClK,cAAc,GAAG,IAAI;MACnC;MACA,IAAI8I,OAAO,CAAC7vB,oBAAoB,EAAE;QAChCixB,WAAW,CAACnK,SAAS,CAAC,OAAO,CAAC,GAAG,UAAS1jB,YAAY,EAAE;UACtD0sB,QAAQ,CAAC,IAAI,EAAEjvB,OAAO,EAAEuC,YAAY,CAAC;QACvC,CAAC;MACH;MACAvC,OAAO,CAAC4C,cAAc,GAAGstB,UAAU,CAAC,CAAC,CAAC;MACtC,IAAI,CAACX,OAAO,CAAC1Y,QAAQ,CAACjM,OAAO,CAAC,IAAI,CAAC4kB,eAAe,CAAC/wB,GAAG,CAACmM,OAAO,CAAC,EAAE;QAC/D0kB,OAAO,CAAC1oB,IAAI,CAACgE,OAAO,CAAC;QACrB4kB,eAAe,CAACvB,MAAM,CACpBrjB,OAAO,EACPmb,cAAc,CAACqK,WAAW,EAAEpwB,OAAO,EAAE,CAAC,CAAC,0BAA0B,CACnE,CAAC;MACH;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAIsvB,OAAO,CAACroB,MAAM,GAAG,CAAC,EAAE;IACtB,IAAIgpB,iBAAiB,EAAE;MACrBP,UAAU,CAACxc,WAAW,IAAItP,WAAW;IACvC;IACA,IAAIorB,OAAO,CAACyB,qBAAqB,KAAKzB,OAAO,CAACrK,aAAa,IAAIqK,OAAO,CAACnK,iBAAiB,CAAC,EAAE;MACzF6K,UAAU,CAACxc,WAAW,IAAIoc,OAAO,CAACoB,IAAI,CAAC,CAAC,GAAG/sB,YAAY;IACzD;IACA,IAAI+rB,UAAU,CAACpZ,SAAS,CAACrP,MAAM,EAAE;MAC/ByoB,UAAU,CAACrc,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC;MAC1C,MAAMkD,KAAK,GAAG,CAACnN,EAAE,GAAG9E,GAAG,CAACkS,OAAO,KAAK,IAAI,GAAGpN,EAAE,GAAGF,wBAAwB,CAACjF,GAAG,CAAC;MAC7E,IAAIsS,KAAK,IAAI,IAAI,EAAE;QACjBmZ,UAAU,CAACrc,YAAY,CAAC,OAAO,EAAEkD,KAAK,CAAC;MACzC;MACApS,IAAI,CAAC+N,YAAY,CAACwd,UAAU,EAAED,WAAW,GAAGA,WAAW,CAACtd,WAAW,GAAGhO,IAAI,CAACilB,UAAU,CAAC;IACxF;EACF;EACAyG,eAAe,GAAG,KAAK;EACvB,IAAIF,0BAA0B,CAAC1oB,MAAM,EAAE;IACrC0oB,0BAA0B,CAAChmB,GAAG,CAAE0M,IAAI,IAAKA,IAAI,CAAC2O,iBAAiB,CAAC,CAAC,CAAC;EACpE,CAAC,MAAM;IACL,IAAIgK,OAAO,CAACnkB,OAAO,EAAE;MACnBvG,GAAG,CAACE,GAAG,CAAC,MAAMorB,eAAe,GAAGe,UAAU,CAACzM,UAAU,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC;IACxE,CAAC,MAAM;MACL5f,GAAG,CAACE,GAAG,CAAC,MAAMorB,eAAe,GAAGe,UAAU,CAACzM,UAAU,EAAE,EAAE,CAAC,CAAC;IAC7D;EACF;EACAmL,YAAY,CAAC,CAAC;AAChB,CAAC;;AAED;AACA,IAAIuB,QAAQ,GAAGA,CAACnJ,CAAC,EAAEha,QAAQ,KAAKA,QAAQ;;AAExC;AACA,SAAS9O,KAAK,IAAIkyB,OAAO,QAAQ,iCAAiC;AAClE,IAAI9H,qBAAqB,GAAGA,CAAC9nB,GAAG,EAAEtB,OAAO,EAAEmxB,SAAS,EAAEC,qBAAqB,KAAK;EAC9E,IAAIF,OAAO,CAACjP,YAAY,IAAIkP,SAAS,EAAE;IACrC,IAAID,OAAO,CAAC/H,wBAAwB,EAAE;MACpC,IAAIiI,qBAAqB,EAAE;QACzBD,SAAS,GAAGA,SAAS,CAAChiB,MAAM,CAAC,CAAC,CAACzD,KAAK,CAAC,KAAKA,KAAK,GAAG,EAAE,CAAC,kBAAkB,CAAC;MAC1E,CAAC,MAAM;QACLylB,SAAS,GAAGA,SAAS,CAAChiB,MAAM,CAAC,CAAC,CAACzD,KAAK,CAAC,KAAK,EAAEA,KAAK,GAAG,EAAE,CAAC,mBAAmB,CAAC;MAC7E;IACF;IACAylB,SAAS,CAACnnB,GAAG,CAAC,CAAC,CAAC0B,KAAK,EAAE7M,IAAI,EAAE+B,MAAM,CAAC,KAAK;MACvC,MAAMjC,MAAM,GAAGuyB,OAAO,CAACG,kBAAkB,GAAGC,qBAAqB,CAAChwB,GAAG,EAAEoK,KAAK,CAAC,GAAGpK,GAAG;MACnF,MAAMkB,OAAO,GAAG+uB,iBAAiB,CAACvxB,OAAO,EAAEY,MAAM,CAAC;MAClD,MAAMwE,IAAI,GAAGosB,gBAAgB,CAAC9lB,KAAK,CAAC;MACpC/G,GAAG,CAACM,GAAG,CAACtG,MAAM,EAAEE,IAAI,EAAE2D,OAAO,EAAE4C,IAAI,CAAC;MACpC,CAACpF,OAAO,CAACqN,aAAa,GAAGrN,OAAO,CAACqN,aAAa,IAAI,EAAE,EAAEpG,IAAI,CAAC,MAAMtC,GAAG,CAACW,GAAG,CAAC3G,MAAM,EAAEE,IAAI,EAAE2D,OAAO,EAAE4C,IAAI,CAAC,CAAC;IACxG,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAImsB,iBAAiB,GAAGA,CAACvxB,OAAO,EAAEkiB,UAAU,KAAM3M,EAAE,IAAK;EACvD,IAAI9L,EAAE;EACN,IAAI;IACF,IAAIynB,OAAO,CAACrwB,QAAQ,EAAE;MACpB,IAAIb,OAAO,CAACM,OAAO,GAAG,GAAG,CAAC,qBAAqB;QAC7C,CAACmJ,EAAE,GAAGzJ,OAAO,CAACE,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGuJ,EAAE,CAACyY,UAAU,CAAC,CAAC3M,EAAE,CAAC;MACrE,CAAC,MAAM;QACL,CAACvV,OAAO,CAACmN,iBAAiB,GAAGnN,OAAO,CAACmN,iBAAiB,IAAI,EAAE,EAAElG,IAAI,CAAC,CAACib,UAAU,EAAE3M,EAAE,CAAC,CAAC;MACtF;IACF,CAAC,MAAM;MACLvV,OAAO,CAACO,aAAa,CAAC2hB,UAAU,CAAC,CAAC3M,EAAE,CAAC;IACvC;EACF,CAAC,CAAC,OAAO3T,CAAC,EAAE;IACVD,YAAY,CAACC,CAAC,CAAC;EACjB;AACF,CAAC;AACD,IAAI0vB,qBAAqB,GAAGA,CAAChwB,GAAG,EAAEoK,KAAK,KAAK;EAC1C,IAAIwlB,OAAO,CAACO,0BAA0B,IAAI/lB,KAAK,GAAG,CAAC,CAAC,sBAAsB,OAAOpH,GAAG;EACpF,IAAI4sB,OAAO,CAACQ,wBAAwB,IAAIhmB,KAAK,GAAG,CAAC,CAAC,oBAAoB,OAAOrH,GAAG;EAChF,IAAI6sB,OAAO,CAACS,sBAAsB,IAAIjmB,KAAK,GAAG,EAAE,CAAC,kBAAkB,OAAOpH,GAAG,CAACyN,IAAI;EAClF,IAAImf,OAAO,CAAC/H,wBAAwB,IAAIzd,KAAK,GAAG,EAAE,CAAC,sBAAsBpK,GAAG,CAACkd,aAAa,EACxF,OAAOld,GAAG,CAACkd,aAAa;EAC1B,OAAOld,GAAG;AACZ,CAAC;AACD,IAAIkwB,gBAAgB,GAAI9lB,KAAK,IAAK3F,uBAAuB,GAAG;EAC1D6rB,OAAO,EAAE,CAAClmB,KAAK,GAAG,CAAC,CAAC,mBAAmB,CAAC;EACxCoN,OAAO,EAAE,CAACpN,KAAK,GAAG,CAAC,CAAC,mBAAmB;AACzC,CAAC,GAAG,CAACA,KAAK,GAAG,CAAC,CAAC,mBAAmB,CAAC;;AAEnC;AACA,IAAImmB,QAAQ,GAAIjb,KAAK,IAAKjS,GAAG,CAACkS,OAAO,GAAGD,KAAK;;AAE7C;AACA,IAAIkb,kBAAkB,GAAI1sB,IAAI,IAAK5G,MAAM,CAACoH,MAAM,CAACjB,GAAG,EAAES,IAAI,CAAC;;AAE3D;AACA,IAAI2sB,qBAAqB,GAAGA,CAACvoB,IAAI,EAAEwoB,gBAAgB,KAAK;EACtD,IAAIxoB,IAAI,IAAI,IAAI,EAAE;IAChB,MAAMyoB,OAAO,GAAG;MACdC,OAAO,EAAE,CAAC;MACVC,YAAY,EAAE,CAAC;MACfH,gBAAgB,EAAE,IAAIzb,GAAG,CAACyb,gBAAgB;IAC5C,CAAC;IACD,MAAMI,gBAAgB,GAAG,EAAE;IAC3BC,qBAAqB,CAAC7oB,IAAI,EAAEA,IAAI,CAACuI,IAAI,EAAEkgB,OAAO,EAAEG,gBAAgB,CAAC;IACjEA,gBAAgB,CAAC/hB,OAAO,CAAEgC,eAAe,IAAK;MAC5C,IAAI5I,EAAE;MACN,IAAI4I,eAAe,IAAI,IAAI,IAAIA,eAAe,CAAC,MAAM,CAAC,EAAE;QACtD,MAAMigB,OAAO,GAAGjgB,eAAe,CAAC,MAAM,CAAC;QACvC,IAAId,MAAM,GAAG+gB,OAAO,CAAC,WAAW,CAAC;QACjC,IAAIC,MAAM,GAAGD,OAAO,CAAC,WAAW,CAAC;QACjC,IAAIE,OAAO,GAAG,GAAGjhB,MAAM,IAAIghB,MAAM,EAAE;QACnC,IAAIhhB,MAAM,IAAI,IAAI,EAAE;UAClBA,MAAM,GAAG,CAAC;UACV0gB,OAAO,CAACE,YAAY,EAAE;UACtBI,MAAM,GAAGN,OAAO,CAACE,YAAY;UAC7BK,OAAO,GAAG,GAAGjhB,MAAM,IAAIghB,MAAM,EAAE;UAC/B,IAAID,OAAO,CAACtf,QAAQ,KAAK,CAAC,CAAC,mBAAmB;YAC5Csf,OAAO,CAAC5e,YAAY,CAAC3P,gBAAgB,EAAEyuB,OAAO,CAAC;UACjD,CAAC,MAAM,IAAIF,OAAO,CAACtf,QAAQ,KAAK,CAAC,CAAC,gBAAgB;YAChD,IAAIzB,MAAM,KAAK,CAAC,EAAE;cAChB,MAAMgC,WAAW,GAAG,CAAC9J,EAAE,GAAG6oB,OAAO,CAAChf,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG7J,EAAE,CAACoV,IAAI,CAAC,CAAC;cACzE,IAAItL,WAAW,KAAK,EAAE,EAAE;gBACtBlB,eAAe,CAACmB,MAAM,CAAC,CAAC;gBACxB;cACF;YACF;YACA,MAAMif,qBAAqB,GAAGjpB,IAAI,CAAC+X,aAAa,CAACiR,OAAO,CAAC;YACzDC,qBAAqB,CAACnf,SAAS,GAAG,GAAG1P,YAAY,IAAI4uB,OAAO,EAAE;YAC9DjgB,YAAY,CAAC+f,OAAO,CAAChgB,UAAU,EAAEmgB,qBAAqB,EAAEH,OAAO,CAAC;UAClE;QACF;QACA,IAAII,iBAAiB,GAAG,GAAGhvB,eAAe,IAAI8uB,OAAO,EAAE;QACvD,MAAMG,qBAAqB,GAAGtgB,eAAe,CAACmM,aAAa;QAC3D,IAAImU,qBAAqB,EAAE;UACzB,IAAIA,qBAAqB,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE;YACxCD,iBAAiB,IAAI,GAAG;UAC1B,CAAC,MAAM,IAAIC,qBAAqB,CAAC,MAAM,CAAC,KAAK,GAAG,EAAE;YAChDD,iBAAiB,IAAI,IAAI;UAC3B;QACF;QACArgB,eAAe,CAACiB,SAAS,GAAGof,iBAAiB;MAC/C;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAIL,qBAAqB,GAAGA,CAAC7oB,IAAI,EAAE4G,IAAI,EAAE6hB,OAAO,EAAEG,gBAAgB,KAAK;EACrE,IAAI3oB,EAAE;EACN,IAAI2G,IAAI,IAAI,IAAI,EAAE;IAChB;EACF;EACA,IAAIA,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;IACxBgiB,gBAAgB,CAACnrB,IAAI,CAACmJ,IAAI,CAAC;EAC7B;EACA,IAAIA,IAAI,CAAC4C,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACzC,MAAMK,UAAU,GAAG,CAAC,GAAG/E,KAAK,CAAC2N,IAAI,CAAC7L,IAAI,CAACiD,UAAU,CAAC,EAAE,GAAG/E,KAAK,CAAC2N,IAAI,CAAC,CAAC,CAACxS,EAAE,GAAG2G,IAAI,CAACqB,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGhI,EAAE,CAAC4J,UAAU,KAAK,EAAE,CAAC,CAAC;IACnIA,UAAU,CAAChD,OAAO,CAAE4K,SAAS,IAAK;MAChC,MAAMjb,OAAO,GAAGJ,UAAU,CAACqb,SAAS,CAAC;MACrC,IAAIjb,OAAO,IAAI,IAAI,IAAI,CAACiyB,OAAO,CAACD,gBAAgB,CAACxb,GAAG,CAACyE,SAAS,CAACrN,QAAQ,CAACwF,WAAW,CAAC,CAAC,CAAC,EAAE;QACtF,MAAMwf,OAAO,GAAG;UACdC,OAAO,EAAE;QACX,CAAC;QACDC,sBAAsB,CAACtpB,IAAI,EAAEyR,SAAS,EAAEjb,OAAO,CAAC0M,OAAO,EAAEulB,OAAO,EAAEW,OAAO,CAAC;MAC5E;MACAP,qBAAqB,CAAC7oB,IAAI,EAAEyR,SAAS,EAAEgX,OAAO,EAAEG,gBAAgB,CAAC;IACnE,CAAC,CAAC;EACJ;AACF,CAAC;AACD,IAAIU,sBAAsB,GAAGA,CAACtpB,IAAI,EAAE8H,OAAO,EAAE7E,KAAK,EAAEwlB,OAAO,EAAEW,OAAO,KAAK;EACvE,IAAInmB,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM8E,MAAM,GAAG,EAAE0gB,OAAO,CAACC,OAAO;IAChC5gB,OAAO,CAACoC,YAAY,CAAC7P,UAAU,EAAE0N,MAAM,CAAC;IACxC,IAAID,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;MAC3BA,OAAO,CAAC,MAAM,CAAC,CAACgC,SAAS,GAAG,GAAG7P,cAAc,IAAI8N,MAAM,EAAE;IAC3D;IACA,IAAI9E,KAAK,CAACkD,UAAU,IAAI,IAAI,EAAE;MAC5B,MAAMojB,KAAK,GAAG,CAAC;MACftmB,KAAK,CAACkD,UAAU,CAACU,OAAO,CAAC,CAAC2iB,UAAU,EAAEjW,KAAK,KAAK;QAC9CkW,2BAA2B,CAACzpB,IAAI,EAAEwpB,UAAU,EAAEJ,OAAO,EAAErhB,MAAM,EAAEwhB,KAAK,EAAEhW,KAAK,CAAC;MAC9E,CAAC,CAAC;IACJ;IACA,IAAIzL,OAAO,IAAI7E,KAAK,IAAIA,KAAK,CAACwD,KAAK,IAAI,CAACqB,OAAO,CAACuP,YAAY,CAAC9c,gBAAgB,CAAC,EAAE;MAC9E,MAAM0b,MAAM,GAAGnO,OAAO,CAACkN,aAAa;MACpC,IAAIiB,MAAM,IAAIA,MAAM,CAACpM,UAAU,EAAE;QAC/B,MAAM6f,gBAAgB,GAAG5kB,KAAK,CAAC2N,IAAI,CAACwD,MAAM,CAACpM,UAAU,CAAC;QACtD,MAAM8f,OAAO,GAAGD,gBAAgB,CAACpf,IAAI,CAClC1D,IAAI,IAAKA,IAAI,CAAC4C,QAAQ,KAAK,CAAC,CAAC,qBAAqB5C,IAAI,CAAC,MAAM,CAChE,CAAC;QACD,IAAI+iB,OAAO,EAAE;UACX,MAAMpW,KAAK,GAAGmW,gBAAgB,CAACliB,OAAO,CAACM,OAAO,CAAC,GAAG,CAAC;UACnD7E,KAAK,CAACwD,KAAK,CAACyD,YAAY,CACtB3P,gBAAgB,EAChB,GAAGovB,OAAO,CAAC,WAAW,CAAC,IAAIA,OAAO,CAAC,WAAW,CAAC,MAAMpW,KAAK,EAC5D,CAAC;QACH;MACF;IACF;EACF;AACF,CAAC;AACD,IAAIkW,2BAA2B,GAAGA,CAACzpB,IAAI,EAAEwpB,UAAU,EAAEJ,OAAO,EAAErhB,MAAM,EAAEwhB,KAAK,EAAEhW,KAAK,KAAK;EACrF,MAAMqI,QAAQ,GAAG4N,UAAU,CAAC/iB,KAAK;EACjC,IAAImV,QAAQ,IAAI,IAAI,EAAE;IACpB;EACF;EACA,MAAMmN,MAAM,GAAGK,OAAO,CAACC,OAAO,EAAE;EAChC,MAAML,OAAO,GAAG,GAAGjhB,MAAM,IAAIghB,MAAM,IAAIQ,KAAK,IAAIhW,KAAK,EAAE;EACvDqI,QAAQ,CAAC,WAAW,CAAC,GAAG7T,MAAM;EAC9B6T,QAAQ,CAAC,WAAW,CAAC,GAAGmN,MAAM;EAC9B,IAAInN,QAAQ,CAACpS,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IAC7CoS,QAAQ,CAAC1R,YAAY,CAAC3P,gBAAgB,EAAEyuB,OAAO,CAAC;EAClD,CAAC,MAAM,IAAIpN,QAAQ,CAACpS,QAAQ,KAAK,CAAC,CAAC,gBAAgB;IACjD,MAAMV,UAAU,GAAG8S,QAAQ,CAAC9S,UAAU;IACtC,MAAM1E,QAAQ,GAAG0E,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC1E,QAAQ;IAClE,IAAIA,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,QAAQ,EAAE;MACjD,MAAMwlB,UAAU,GAAG,GAAGxvB,YAAY,IAAI4uB,OAAO,EAAE;MAC/C,MAAMC,qBAAqB,GAAGjpB,IAAI,CAAC+X,aAAa,CAAC6R,UAAU,CAAC;MAC5D7gB,YAAY,CAACD,UAAU,EAAEmgB,qBAAqB,EAAErN,QAAQ,CAAC;IAC3D;EACF,CAAC,MAAM,IAAIA,QAAQ,CAACpS,QAAQ,KAAK,CAAC,CAAC,mBAAmB;IACpD,IAAIoS,QAAQ,CAAC,MAAM,CAAC,EAAE;MACpB,MAAMpX,QAAQ,GAAGoX,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE;MACvC,MAAMiO,UAAU,GAAG,GAAG1vB,YAAY,IAAI6uB,OAAO,IAAIxkB,QAAQ,EAAE;MAC3DoX,QAAQ,CAAC9R,SAAS,GAAG+f,UAAU;IACjC;EACF;EACA,IAAIL,UAAU,CAACrjB,UAAU,IAAI,IAAI,EAAE;IACjC,MAAM2jB,UAAU,GAAGP,KAAK,GAAG,CAAC;IAC5BC,UAAU,CAACrjB,UAAU,CAACU,OAAO,CAAC,CAAC5D,KAAK,EAAE8mB,MAAM,KAAK;MAC/CN,2BAA2B,CAACzpB,IAAI,EAAEiD,KAAK,EAAEmmB,OAAO,EAAErhB,MAAM,EAAE+hB,UAAU,EAAEC,MAAM,CAAC;IAC/E,CAAC,CAAC;EACJ;AACF,CAAC;AACD,SACEprB,OAAO,IAAInJ,KAAK,EAChBC,KAAK,EACLmJ,GAAG,EACH6oB,QAAQ,EACRxsB,CAAC,EACDA,CAAC,IAAIC,WAAW,EAChByL,IAAI,EACJ7H,UAAU,IAAID,SAAS,EACvBrG,gBAAgB,EAChBonB,qBAAqB,EACrBmG,aAAa,EACb9sB,UAAU,EACV4iB,iBAAiB,EACjBpjB,eAAe,EACfI,cAAc,EACdF,cAAc,EACdR,YAAY,EACZoT,WAAW,EACXqZ,mBAAmB,EACnBxE,oBAAoB,EACpBtlB,GAAG,EACH6qB,eAAe,EACf1K,WAAW,EACXlc,YAAY,EACZuM,UAAU,EACVlV,UAAU,EACVoU,OAAO,EACPkQ,eAAe,EACfoB,QAAQ,EACR3X,CAAC,EACDokB,qBAAqB,EACrB1wB,iBAAiB,EACjBsB,UAAU,EACVY,mBAAmB,EACnB2D,QAAQ,EACRkN,kBAAkB,EAClBzP,GAAG,EACH4e,mBAAmB,EACnBtd,cAAc,EACdmgB,cAAc,EACdmI,kBAAkB,EAClBtmB,QAAQ,EACR9H,YAAY,EACZL,gBAAgB,EAChBqgB,UAAU,EACVpX,YAAY,EACZxG,eAAe,EACfwR,OAAO,EACP8d,QAAQ,EACRnsB,kBAAkB,EAClBosB,kBAAkB,EAClBvM,QAAQ,EACRjiB,MAAM,EACN8C,gCAAgC,EAChCL,uBAAuB,EACvBF,cAAc,EACdxB,GAAG,EACH6D,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}