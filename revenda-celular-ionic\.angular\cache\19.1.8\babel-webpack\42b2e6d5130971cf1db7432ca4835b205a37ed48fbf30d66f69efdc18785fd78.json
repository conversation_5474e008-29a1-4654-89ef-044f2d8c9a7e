{"ast": null, "code": "var _AppModule;\nimport { BrowserModule } from '@angular/platform-browser';\nimport { RouteReuseStrategy } from '@angular/router';\nimport { IonicModule, IonicRouteStrategy } from '@ionic/angular';\nimport { AppComponent } from './app.component';\nimport { AppRoutingModule } from './app-routing.module';\nimport { HttpClientModule } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nexport class AppModule {}\n_AppModule = AppModule;\n_AppModule.ɵfac = function AppModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AppModule)();\n};\n_AppModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _AppModule,\n  bootstrap: [AppComponent]\n});\n_AppModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  providers: [{\n    provide: RouteReuseStrategy,\n    useClass: IonicRouteStrategy\n  }],\n  imports: [BrowserModule, HttpClientModule, IonicModule.forRoot(), AppRoutingModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [BrowserModule, HttpClientModule, i1.IonicModule, AppRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "RouteReuseStrategy", "IonicModule", "IonicRouteStrategy", "AppComponent", "AppRoutingModule", "HttpClientModule", "AppModule", "bootstrap", "provide", "useClass", "imports", "forRoot", "declarations", "i1"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { RouteReuseStrategy } from '@angular/router';\r\n\r\nimport { IonicModule, IonicRouteStrategy } from '@ionic/angular';\r\n\r\nimport { AppComponent } from './app.component';\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { HttpClientModule } from '@angular/common/http';\r\n\r\n@NgModule({\r\n  declarations: [AppComponent],\r\n  imports: [\r\n    BrowserModule,\r\n    HttpClientModule,\r\n    IonicModule.forRoot(),\r\n    AppRoutingModule\r\n  ],\r\n  providers: [{ provide: RouteReuseStrategy, useClass: IonicRouteStrategy }],\r\n  bootstrap: [AppComponent],\r\n  schemas: [CUSTOM_ELEMENTS_SCHEMA]\r\n})\r\nexport class AppModule {}\r\n"], "mappings": ";AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,kBAAkB,QAAQ,iBAAiB;AAEpD,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,gBAAgB;AAEhE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,gBAAgB,QAAQ,sBAAsB;;;AAcvD,OAAM,MAAOC,SAAS;aAATA,SAAS;;mCAATA,UAAS;AAAA;;QAATA,UAAS;EAAAC,SAAA,GAHRJ,YAAY;AAAA;;aADb,CAAC;IAAEK,OAAO,EAAER,kBAAkB;IAAES,QAAQ,EAAEP;EAAkB,CAAE,CAAC;EAAAQ,OAAA,GALxEX,aAAa,EACbM,gBAAgB,EAChBJ,WAAW,CAACU,OAAO,EAAE,EACrBP,gBAAgB;AAAA;;2EAMPE,SAAS;IAAAM,YAAA,GAXLT,YAAY;IAAAO,OAAA,GAEzBX,aAAa,EACbM,gBAAgB,EAAAQ,EAAA,CAAAZ,WAAA,EAEhBG,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}