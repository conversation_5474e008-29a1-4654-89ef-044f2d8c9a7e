{"ast": null, "code": "var _StoreFormComponent;\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/store.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/forms\";\nconst _forTrack0 = ($index, $item) => $item.value;\nfunction StoreFormComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction StoreFormComponent_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00EDnimo 3 caracteres \");\n  }\n}\nfunction StoreFormComponent_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00E1ximo 100 caracteres \");\n  }\n}\nfunction StoreFormComponent_Conditional_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O nome deve conter uma palavra identificadora (loja, store, shop) \");\n  }\n}\nfunction StoreFormComponent_Conditional_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction StoreFormComponent_Conditional_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Endere\\u00E7o deve ter pelo menos 10 caracteres \");\n  }\n}\nfunction StoreFormComponent_Conditional_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction StoreFormComponent_Conditional_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Cidade deve conter apenas letras e espa\\u00E7os \");\n  }\n}\nfunction StoreFormComponent_For_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const state_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", state_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(state_r1);\n  }\n}\nfunction StoreFormComponent_Conditional_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction StoreFormComponent_Conditional_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction StoreFormComponent_Conditional_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Telefone deve ter 10 ou 11 d\\u00EDgitos \");\n  }\n}\nfunction StoreFormComponent_Conditional_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction StoreFormComponent_Conditional_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Nome do gerente deve conter apenas letras e espa\\u00E7os \");\n  }\n}\nfunction StoreFormComponent_For_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(option_r2.label);\n  }\n}\nfunction StoreFormComponent_Conditional_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nexport class StoreFormComponent {\n  constructor(storeService, router, activatedRoute, toastController) {\n    this.storeService = storeService;\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    this.toastController = toastController;\n    this.storeForm = new FormGroup({\n      name: new FormControl('', [Validators.required, Validators.minLength(3), Validators.maxLength(100), this.storeNameValidator]),\n      address: new FormControl('', [Validators.required, Validators.minLength(10)]),\n      city: new FormControl('', [Validators.required, Validators.pattern(/^[a-zA-ZÀ-ÿ\\s]+$/)]),\n      state: new FormControl('', [Validators.required]),\n      phone: new FormControl('', [Validators.required, this.phoneValidator]),\n      manager: new FormControl('', [Validators.required, Validators.pattern(/^[a-zA-ZÀ-ÿ\\s]+$/)]),\n      isHeadquarters: new FormControl(false),\n      status: new FormControl('active', [Validators.required])\n    });\n    this.stateOptions = ['AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA', 'MT', 'MS', 'MG', 'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'];\n    this.statusOptions = [{\n      value: 'active',\n      label: 'Ativa'\n    }, {\n      value: 'inactive',\n      label: 'Inativa'\n    }, {\n      value: 'underMaintenance',\n      label: 'Em Manutenção'\n    }];\n  }\n  ngOnInit() {\n    const storeId = this.activatedRoute.snapshot.params['id'];\n    if (storeId) {\n      this.storeService.getById(+storeId).subscribe({\n        next: store => {\n          if (store) {\n            this.storeId = +storeId;\n            this.storeForm.patchValue({\n              name: store.name,\n              address: store.address,\n              city: store.city,\n              state: store.state,\n              phone: store.phone,\n              manager: store.manager,\n              isHeadquarters: store.isHeadquarters,\n              status: store.status\n            });\n          }\n        },\n        error: error => {\n          alert('Erro ao carregar a loja com id ' + storeId);\n          console.error(error);\n        }\n      });\n    }\n  }\n  hasError(field, error) {\n    var _formControl$errors;\n    const formControl = this.storeForm.get(field);\n    return (formControl === null || formControl === void 0 ? void 0 : formControl.touched) && (formControl === null || formControl === void 0 || (_formControl$errors = formControl.errors) === null || _formControl$errors === void 0 ? void 0 : _formControl$errors[error]);\n  }\n  save() {\n    const {\n      value\n    } = this.storeForm;\n    console.log('Salvando loja:', value);\n    this.storeService.save({\n      ...value,\n      id: this.storeId\n    }).subscribe({\n      next: () => {\n        this.toastController.create({\n          message: 'Loja salva com sucesso!',\n          duration: 3000\n        }).then(toast => toast.present());\n        this.router.navigate(['/stores']);\n      },\n      error: error => {\n        var _error$error;\n        let errorMessage = 'Erro ao salvar a loja ' + value.name + '!';\n        if ((_error$error = error.error) !== null && _error$error !== void 0 && _error$error.message) {\n          errorMessage = error.error.message;\n        }\n        alert(errorMessage);\n        console.error(error);\n      }\n    });\n  }\n  hasError(field, error) {\n    var _formControl$errors2;\n    const formControl = this.storeForm.get(field);\n    return !!(formControl !== null && formControl !== void 0 && formControl.touched) && !!(formControl !== null && formControl !== void 0 && (_formControl$errors2 = formControl.errors) !== null && _formControl$errors2 !== void 0 && _formControl$errors2[error]);\n  }\n  // Validators customizados\n  storeNameValidator(control) {\n    if (!control.value) return null;\n    const name = control.value.toLowerCase();\n    const forbiddenWords = ['loja', 'store', 'shop'];\n    if (!forbiddenWords.some(word => name.includes(word))) {\n      return {\n        missingStoreIdentifier: true\n      };\n    }\n    return null;\n  }\n  phoneValidator(control) {\n    if (!control.value) return null;\n    const phone = control.value.replace(/\\D/g, '');\n    if (phone.length !== 10 && phone.length !== 11) {\n      return {\n        invalidPhone: true\n      };\n    }\n    return null;\n  }\n}\n_StoreFormComponent = StoreFormComponent;\n_StoreFormComponent.ɵfac = function StoreFormComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _StoreFormComponent)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ToastController));\n};\n_StoreFormComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _StoreFormComponent,\n  selectors: [[\"app-store-form\"]],\n  standalone: false,\n  decls: 56,\n  vars: 18,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [3, \"fullscreen\"], [1, \"form-container\"], [3, \"formGroup\"], [\"formControlName\", \"name\", \"labelPlacement\", \"floating\", \"label\", \"Nome\", \"type\", \"text\"], [\"formControlName\", \"address\", \"labelPlacement\", \"floating\", \"label\", \"Endere\\u00E7o\", \"type\", \"text\"], [\"formControlName\", \"city\", \"labelPlacement\", \"floating\", \"label\", \"Cidade\", \"type\", \"text\"], [\"formControlName\", \"state\", \"labelPlacement\", \"floating\", \"label\", \"Estado\"], [3, \"value\"], [\"formControlName\", \"phone\", \"labelPlacement\", \"floating\", \"label\", \"Telefone\", \"type\", \"tel\"], [\"formControlName\", \"manager\", \"labelPlacement\", \"floating\", \"label\", \"Gerente\", \"type\", \"text\"], [\"formControlName\", \"isHeadquarters\", \"slot\", \"end\"], [\"formControlName\", \"status\", \"labelPlacement\", \"floating\", \"label\", \"Status\"], [\"vertical\", \"bottom\", \"horizontal\", \"end\", \"slot\", \"fixed\"], [3, \"click\", \"disabled\"], [\"name\", \"checkmark\"]],\n  template: function StoreFormComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-menu-button\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5, \"Cadastro de Lojas\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 3)(7, \"div\", 4)(8, \"form\", 5)(9, \"ion-list\")(10, \"ion-item\");\n      i0.ɵɵelement(11, \"ion-input\", 6);\n      i0.ɵɵelementStart(12, \"p\");\n      i0.ɵɵtemplate(13, StoreFormComponent_Conditional_13_Template, 1, 0)(14, StoreFormComponent_Conditional_14_Template, 1, 0)(15, StoreFormComponent_Conditional_15_Template, 1, 0)(16, StoreFormComponent_Conditional_16_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(17, \"ion-item\");\n      i0.ɵɵelement(18, \"ion-input\", 7);\n      i0.ɵɵelementStart(19, \"p\");\n      i0.ɵɵtemplate(20, StoreFormComponent_Conditional_20_Template, 1, 0)(21, StoreFormComponent_Conditional_21_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(22, \"ion-item\");\n      i0.ɵɵelement(23, \"ion-input\", 8);\n      i0.ɵɵelementStart(24, \"p\");\n      i0.ɵɵtemplate(25, StoreFormComponent_Conditional_25_Template, 1, 0)(26, StoreFormComponent_Conditional_26_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(27, \"ion-item\")(28, \"ion-select\", 9);\n      i0.ɵɵrepeaterCreate(29, StoreFormComponent_For_30_Template, 2, 2, \"ion-select-option\", 10, i0.ɵɵrepeaterTrackByIdentity);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(31, \"p\");\n      i0.ɵɵtemplate(32, StoreFormComponent_Conditional_32_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(33, \"ion-item\");\n      i0.ɵɵelement(34, \"ion-input\", 11);\n      i0.ɵɵelementStart(35, \"p\");\n      i0.ɵɵtemplate(36, StoreFormComponent_Conditional_36_Template, 1, 0)(37, StoreFormComponent_Conditional_37_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(38, \"ion-item\");\n      i0.ɵɵelement(39, \"ion-input\", 12);\n      i0.ɵɵelementStart(40, \"p\");\n      i0.ɵɵtemplate(41, StoreFormComponent_Conditional_41_Template, 1, 0)(42, StoreFormComponent_Conditional_42_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(43, \"ion-item\")(44, \"ion-label\");\n      i0.ɵɵtext(45, \"Matriz:\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(46, \"ion-toggle\", 13);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(47, \"ion-item\")(48, \"ion-select\", 14);\n      i0.ɵɵrepeaterCreate(49, StoreFormComponent_For_50_Template, 2, 2, \"ion-select-option\", 10, _forTrack0);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(51, \"p\");\n      i0.ɵɵtemplate(52, StoreFormComponent_Conditional_52_Template, 1, 0);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(53, \"ion-fab\", 15)(54, \"ion-fab-button\", 16);\n      i0.ɵɵlistener(\"click\", function StoreFormComponent_Template_ion_fab_button_click_54_listener() {\n        return ctx.save();\n      });\n      i0.ɵɵelement(55, \"ion-icon\", 17);\n      i0.ɵɵelementEnd()()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.storeForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"required\") ? 13 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"minlength\") ? 14 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"maxlength\") ? 15 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"missingStoreIdentifier\") ? 16 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"address\", \"required\") ? 20 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"address\", \"minlength\") ? 21 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"city\", \"required\") ? 25 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"city\", \"pattern\") ? 26 : -1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵrepeater(ctx.stateOptions);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"state\", \"required\") ? 32 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"phone\", \"required\") ? 36 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"phone\", \"invalidPhone\") ? 37 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"manager\", \"required\") ? 41 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"manager\", \"pattern\") ? 42 : -1);\n      i0.ɵɵadvance(7);\n      i0.ɵɵrepeater(ctx.statusOptions);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"status\", \"required\") ? 52 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"disabled\", ctx.storeForm.invalid);\n    }\n  },\n  dependencies: [i4.ɵNgNoValidate, i4.NgControlStatus, i4.NgControlStatusGroup, i3.IonButtons, i3.IonContent, i3.IonFab, i3.IonFabButton, i3.IonHeader, i3.IonIcon, i3.IonInput, i3.IonItem, i3.IonLabel, i3.IonList, i3.IonMenuButton, i3.IonSelect, i3.IonSelectOption, i3.IonTitle, i3.IonToggle, i3.IonToolbar, i3.BooleanValueAccessor, i3.SelectValueAccessor, i3.TextValueAccessor, i4.FormGroupDirective, i4.FormControlName],\n  styles: [\".form-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\nion-item[_ngcontent-%COMP%] {\\n  --padding-start: 0;\\n  margin-bottom: 8px;\\n}\\nion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--ion-color-danger);\\n  padding-left: 16px;\\n}\\n\\nion-toggle[_ngcontent-%COMP%] {\\n  padding-right: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc3RvcmVzL3N0b3JlLWZvcm0vc3RvcmUtZm9ybS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7QUFDRjs7QUFFQTtFQUNFLGtCQUFBO0VBQ0Esa0JBQUE7QUFDRjtBQUNFO0VBQ0UsZUFBQTtFQUNBLDhCQUFBO0VBQ0Esa0JBQUE7QUFDSjs7QUFHQTtFQUNFLG1CQUFBO0FBQUYiLCJzb3VyY2VzQ29udGVudCI6WyIuZm9ybS1jb250YWluZXIge1xyXG4gIHBhZGRpbmc6IDE2cHg7XHJcbiAgbWF4LXdpZHRoOiA4MDBweDtcclxuICBtYXJnaW46IDAgYXV0bztcclxufVxyXG5cclxuaW9uLWl0ZW0ge1xyXG4gIC0tcGFkZGluZy1zdGFydDogMDtcclxuICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgXHJcbiAgcCB7XHJcbiAgICBmb250LXNpemU6IDEycHg7XHJcbiAgICBjb2xvcjogdmFyKC0taW9uLWNvbG9yLWRhbmdlcik7XHJcbiAgICBwYWRkaW5nLWxlZnQ6IDE2cHg7XHJcbiAgfVxyXG59XHJcblxyXG5pb24tdG9nZ2xlIHtcclxuICBwYWRkaW5nLXJpZ2h0OiAxNnB4O1xyXG59XHJcblxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n});", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "i0", "ɵɵtext", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵproperty", "state_r1", "ɵɵadvance", "ɵɵtextInterpolate", "option_r2", "value", "label", "StoreFormComponent", "constructor", "storeService", "router", "activatedRoute", "toastController", "storeForm", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "storeNameValidator", "address", "city", "pattern", "state", "phone", "phoneValidator", "manager", "isHeadquarters", "status", "stateOptions", "statusOptions", "ngOnInit", "storeId", "snapshot", "params", "getById", "subscribe", "next", "store", "patchValue", "error", "alert", "console", "<PERSON><PERSON><PERSON><PERSON>", "field", "_formControl$errors", "formControl", "get", "touched", "errors", "save", "log", "id", "create", "message", "duration", "then", "toast", "present", "navigate", "_error$error", "errorMessage", "_formControl$errors2", "control", "toLowerCase", "forbidden<PERSON><PERSON><PERSON>", "some", "word", "includes", "missingStoreIdentifier", "replace", "length", "invalidPhone", "ɵɵdirectiveInject", "i1", "StoreService", "i2", "Router", "ActivatedRoute", "i3", "ToastController", "selectors", "standalone", "decls", "vars", "consts", "template", "StoreFormComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "StoreFormComponent_Conditional_13_Template", "StoreFormComponent_Conditional_14_Template", "StoreFormComponent_Conditional_15_Template", "StoreFormComponent_Conditional_16_Template", "StoreFormComponent_Conditional_20_Template", "StoreFormComponent_Conditional_21_Template", "StoreFormComponent_Conditional_25_Template", "StoreFormComponent_Conditional_26_Template", "ɵɵrepeaterCreate", "StoreFormComponent_For_30_Template", "ɵɵrepeaterTrackByIdentity", "StoreFormComponent_Conditional_32_Template", "StoreFormComponent_Conditional_36_Template", "StoreFormComponent_Conditional_37_Template", "StoreFormComponent_Conditional_41_Template", "StoreFormComponent_Conditional_42_Template", "StoreFormComponent_For_50_Template", "_forTrack0", "StoreFormComponent_Conditional_52_Template", "ɵɵlistener", "StoreFormComponent_Template_ion_fab_button_click_54_listener", "ɵɵconditional", "ɵɵrepeater", "invalid"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\stores\\store-form\\store-form.component.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\stores\\store-form\\store-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ToastController } from '@ionic/angular';\r\nimport { StoreService } from '../services/store.service';\r\n\r\n@Component({\r\n  selector: 'app-store-form',\r\n  templateUrl: './store-form.component.html',\r\n  styleUrls: ['./store-form.component.scss'],\r\n  standalone: false,\r\n})\r\nexport class StoreFormComponent implements OnInit {\r\n  storeForm: FormGroup = new FormGroup({\r\n    name: new FormControl('', [\r\n      Validators.required,\r\n      Validators.minLength(3),\r\n      Validators.maxLength(100),\r\n      this.storeNameValidator\r\n    ]),\r\n    address: new FormControl('', [\r\n      Validators.required,\r\n      Validators.minLength(10)\r\n    ]),\r\n    city: new FormControl('', [\r\n      Validators.required,\r\n      Validators.pattern(/^[a-zA-ZÀ-ÿ\\s]+$/)\r\n    ]),\r\n    state: new FormControl('', [Validators.required]),\r\n    phone: new FormControl('', [\r\n      Validators.required,\r\n      this.phoneValidator\r\n    ]),\r\n    manager: new FormControl('', [\r\n      Validators.required,\r\n      Validators.pattern(/^[a-zA-ZÀ-ÿ\\s]+$/)\r\n    ]),\r\n    isHeadquarters: new FormControl(false),\r\n    status: new FormControl('active', [Validators.required])\r\n  });\r\n\r\n  storeId!: number;\r\n  \r\n  stateOptions: string[] = [\r\n    'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA', 'MT', 'MS', 'MG', \r\n    'PA', 'PB', 'PR', 'PE', 'PI', 'RJ', 'RN', 'RS', 'RO', 'RR', 'SC', 'SP', 'SE', 'TO'\r\n  ];\r\n  \r\n  statusOptions = [\r\n    { value: 'active', label: 'Ativa' },\r\n    { value: 'inactive', label: 'Inativa' },\r\n    { value: 'underMaintenance', label: 'Em Manutenção' }\r\n  ];\r\n\r\n  constructor(\r\n    private storeService: StoreService,\r\n    private router: Router,\r\n    private activatedRoute: ActivatedRoute,\r\n    private toastController: ToastController\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    const storeId = this.activatedRoute.snapshot.params['id'];\r\n    if (storeId) {\r\n      this.storeService.getById(+storeId).subscribe({\r\n        next: (store) => {\r\n          if (store) {\r\n            this.storeId = +storeId;\r\n            this.storeForm.patchValue({\r\n              name: store.name,\r\n              address: store.address,\r\n              city: store.city,\r\n              state: store.state,\r\n              phone: store.phone,\r\n              manager: store.manager,\r\n              isHeadquarters: store.isHeadquarters,\r\n              status: store.status\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          alert('Erro ao carregar a loja com id ' + storeId);\r\n          console.error(error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  hasError(field: string, error: string) {\r\n    const formControl = this.storeForm.get(field);\r\n    return formControl?.touched && formControl?.errors?.[error];\r\n  }\r\n\r\n  save() {\r\n    const { value } = this.storeForm;\r\n\r\n    console.log('Salvando loja:', value);\r\n\r\n    this.storeService.save({\r\n      ...value,\r\n      id: this.storeId\r\n    }).subscribe({\r\n      next: () => {\r\n        this.toastController.create({\r\n          message: 'Loja salva com sucesso!',\r\n          duration: 3000,\r\n        }).then(toast => toast.present());\r\n        this.router.navigate(['/stores']);\r\n      },\r\n      error: (error) => {\r\n        let errorMessage = 'Erro ao salvar a loja ' + value.name + '!';\r\n        if (error.error?.message) {\r\n          errorMessage = error.error.message;\r\n        }\r\n        alert(errorMessage);\r\n        console.error(error);\r\n      }\r\n    });\r\n  }\r\n\r\n  hasError(field: string, error: string): boolean {\r\n    const formControl = this.storeForm.get(field);\r\n    return !!formControl?.touched && !!formControl?.errors?.[error];\r\n  }\r\n\r\n  // Validators customizados\r\n  storeNameValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const name = control.value.toLowerCase();\r\n    const forbiddenWords = ['loja', 'store', 'shop'];\r\n\r\n    if (!forbiddenWords.some(word => name.includes(word))) {\r\n      return { missingStoreIdentifier: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  phoneValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const phone = control.value.replace(/\\D/g, '');\r\n    if (phone.length !== 10 && phone.length !== 11) {\r\n      return { invalidPhone: true };\r\n    }\r\n    return null;\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-menu-button></ion-menu-button>\r\n    </ion-buttons>\r\n    <ion-title>Cadastro de Lojas</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div class=\"form-container\">\r\n    <form [formGroup]=\"storeForm\">\r\n      <ion-list>\r\n        <ion-item>\r\n          <ion-input formControlName=\"name\" labelPlacement=\"floating\" label=\"Nome\" type=\"text\"></ion-input>\r\n          <p>\r\n          @if(hasError('name', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          @if(hasError('name', 'minlength')) {\r\n            O campo deve ter no mínimo 3 caracteres\r\n          }\r\n          @if(hasError('name', 'maxlength')) {\r\n            O campo deve ter no máximo 100 caracteres\r\n          }\r\n          @if(hasError('name', 'missingStoreIdentifier')) {\r\n            O nome deve conter uma palavra identificadora (loja, store, shop)\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"address\" labelPlacement=\"floating\" label=\"Endereço\" type=\"text\"></ion-input>\r\n          <p>\r\n          @if(hasError('address', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          @if(hasError('address', 'minlength')) {\r\n            Endereço deve ter pelo menos 10 caracteres\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"city\" labelPlacement=\"floating\" label=\"Cidade\" type=\"text\"></ion-input>\r\n          <p>\r\n          @if(hasError('city', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          @if(hasError('city', 'pattern')) {\r\n            Cidade deve conter apenas letras e espaços\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-select formControlName=\"state\" labelPlacement=\"floating\" label=\"Estado\">\r\n            @for(state of stateOptions; track state) {\r\n              <ion-select-option [value]=\"state\">{{ state }}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n          @if(hasError('state', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"phone\" labelPlacement=\"floating\" label=\"Telefone\" type=\"tel\"></ion-input>\r\n          <p>\r\n          @if(hasError('phone', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          @if(hasError('phone', 'invalidPhone')) {\r\n            Telefone deve ter 10 ou 11 dígitos\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"manager\" labelPlacement=\"floating\" label=\"Gerente\" type=\"text\"></ion-input>\r\n          <p>\r\n          @if(hasError('manager', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          @if(hasError('manager', 'pattern')) {\r\n            Nome do gerente deve conter apenas letras e espaços\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-label>Matriz:</ion-label>\r\n          <ion-toggle formControlName=\"isHeadquarters\" slot=\"end\"></ion-toggle>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-select formControlName=\"status\" labelPlacement=\"floating\" label=\"Status\">\r\n            @for(option of statusOptions; track option.value) {\r\n              <ion-select-option [value]=\"option.value\">{{ option.label }}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n          @if(hasError('status', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          </p>\r\n        </ion-item>\r\n      </ion-list>\r\n      \r\n      <ion-fab vertical=\"bottom\" horizontal=\"end\" slot=\"fixed\">\r\n        <ion-fab-button [disabled]=\"storeForm.invalid\" (click)=\"save()\">\r\n          <ion-icon name=\"checkmark\"></ion-icon>\r\n        </ion-fab-button>\r\n      </ion-fab>\r\n    </form>\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAA2C,gBAAgB;;;;;;;;;ICgB1FC,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,qDACF;;;;;IAEED,EAAA,CAAAC,MAAA,uDACF;;;;;IAEED,EAAA,CAAAC,MAAA,0EACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,wDACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,wDACF;;;;;IAOID,EAAA,CAAAE,cAAA,4BAAmC;IAAAF,EAAA,CAAAC,MAAA,GAAW;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAA/CH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAe;IAACL,EAAA,CAAAM,SAAA,EAAW;IAAXN,EAAA,CAAAO,iBAAA,CAAAF,QAAA,CAAW;;;;;IAKhDL,EAAA,CAAAC,MAAA,wCACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,gDACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,iEACF;;;;;IAYID,EAAA,CAAAE,cAAA,4BAA0C;IAAAF,EAAA,CAAAC,MAAA,GAAkB;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAA7DH,EAAA,CAAAI,UAAA,UAAAI,SAAA,CAAAC,KAAA,CAAsB;IAACT,EAAA,CAAAM,SAAA,EAAkB;IAAlBN,EAAA,CAAAO,iBAAA,CAAAC,SAAA,CAAAE,KAAA,CAAkB;;;;;IAK9DV,EAAA,CAAAC,MAAA,wCACF;;;AD9FV,OAAM,MAAOU,kBAAkB;EA0C7BC,YACUC,YAA0B,EAC1BC,MAAc,EACdC,cAA8B,EAC9BC,eAAgC;IAHhC,KAAAH,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IA7CzB,KAAAC,SAAS,GAAc,IAAInB,SAAS,CAAC;MACnCoB,IAAI,EAAE,IAAIrB,WAAW,CAAC,EAAE,EAAE,CACxBE,UAAU,CAACoB,QAAQ,EACnBpB,UAAU,CAACqB,SAAS,CAAC,CAAC,CAAC,EACvBrB,UAAU,CAACsB,SAAS,CAAC,GAAG,CAAC,EACzB,IAAI,CAACC,kBAAkB,CACxB,CAAC;MACFC,OAAO,EAAE,IAAI1B,WAAW,CAAC,EAAE,EAAE,CAC3BE,UAAU,CAACoB,QAAQ,EACnBpB,UAAU,CAACqB,SAAS,CAAC,EAAE,CAAC,CACzB,CAAC;MACFI,IAAI,EAAE,IAAI3B,WAAW,CAAC,EAAE,EAAE,CACxBE,UAAU,CAACoB,QAAQ,EACnBpB,UAAU,CAAC0B,OAAO,CAAC,kBAAkB,CAAC,CACvC,CAAC;MACFC,KAAK,EAAE,IAAI7B,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACoB,QAAQ,CAAC,CAAC;MACjDQ,KAAK,EAAE,IAAI9B,WAAW,CAAC,EAAE,EAAE,CACzBE,UAAU,CAACoB,QAAQ,EACnB,IAAI,CAACS,cAAc,CACpB,CAAC;MACFC,OAAO,EAAE,IAAIhC,WAAW,CAAC,EAAE,EAAE,CAC3BE,UAAU,CAACoB,QAAQ,EACnBpB,UAAU,CAAC0B,OAAO,CAAC,kBAAkB,CAAC,CACvC,CAAC;MACFK,cAAc,EAAE,IAAIjC,WAAW,CAAC,KAAK,CAAC;MACtCkC,MAAM,EAAE,IAAIlC,WAAW,CAAC,QAAQ,EAAE,CAACE,UAAU,CAACoB,QAAQ,CAAC;KACxD,CAAC;IAIF,KAAAa,YAAY,GAAa,CACvB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAC5E,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CACnF;IAED,KAAAC,aAAa,GAAG,CACd;MAAExB,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAO,CAAE,EACnC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAS,CAAE,EACvC;MAAED,KAAK,EAAE,kBAAkB;MAAEC,KAAK,EAAE;IAAe,CAAE,CACtD;EAOG;EAEJwB,QAAQA,CAAA;IACN,MAAMC,OAAO,GAAG,IAAI,CAACpB,cAAc,CAACqB,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;IACzD,IAAIF,OAAO,EAAE;MACX,IAAI,CAACtB,YAAY,CAACyB,OAAO,CAAC,CAACH,OAAO,CAAC,CAACI,SAAS,CAAC;QAC5CC,IAAI,EAAGC,KAAK,IAAI;UACd,IAAIA,KAAK,EAAE;YACT,IAAI,CAACN,OAAO,GAAG,CAACA,OAAO;YACvB,IAAI,CAAClB,SAAS,CAACyB,UAAU,CAAC;cACxBxB,IAAI,EAAEuB,KAAK,CAACvB,IAAI;cAChBK,OAAO,EAAEkB,KAAK,CAAClB,OAAO;cACtBC,IAAI,EAAEiB,KAAK,CAACjB,IAAI;cAChBE,KAAK,EAAEe,KAAK,CAACf,KAAK;cAClBC,KAAK,EAAEc,KAAK,CAACd,KAAK;cAClBE,OAAO,EAAEY,KAAK,CAACZ,OAAO;cACtBC,cAAc,EAAEW,KAAK,CAACX,cAAc;cACpCC,MAAM,EAAEU,KAAK,CAACV;aACf,CAAC;UACJ;QACF,CAAC;QACDY,KAAK,EAAGA,KAAK,IAAI;UACfC,KAAK,CAAC,iCAAiC,GAAGT,OAAO,CAAC;UAClDU,OAAO,CAACF,KAAK,CAACA,KAAK,CAAC;QACtB;OACD,CAAC;IACJ;EACF;EAEAG,QAAQA,CAACC,KAAa,EAAEJ,KAAa;IAAA,IAAAK,mBAAA;IACnC,MAAMC,WAAW,GAAG,IAAI,CAAChC,SAAS,CAACiC,GAAG,CAACH,KAAK,CAAC;IAC7C,OAAO,CAAAE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEE,OAAO,MAAIF,WAAW,aAAXA,WAAW,gBAAAD,mBAAA,GAAXC,WAAW,CAAEG,MAAM,cAAAJ,mBAAA,uBAAnBA,mBAAA,CAAsBL,KAAK,CAAC;EAC7D;EAEAU,IAAIA,CAAA;IACF,MAAM;MAAE5C;IAAK,CAAE,GAAG,IAAI,CAACQ,SAAS;IAEhC4B,OAAO,CAACS,GAAG,CAAC,gBAAgB,EAAE7C,KAAK,CAAC;IAEpC,IAAI,CAACI,YAAY,CAACwC,IAAI,CAAC;MACrB,GAAG5C,KAAK;MACR8C,EAAE,EAAE,IAAI,CAACpB;KACV,CAAC,CAACI,SAAS,CAAC;MACXC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACxB,eAAe,CAACwC,MAAM,CAAC;UAC1BC,OAAO,EAAE,yBAAyB;UAClCC,QAAQ,EAAE;SACX,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC/C,MAAM,CAACgD,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACnC,CAAC;MACDnB,KAAK,EAAGA,KAAK,IAAI;QAAA,IAAAoB,YAAA;QACf,IAAIC,YAAY,GAAG,wBAAwB,GAAGvD,KAAK,CAACS,IAAI,GAAG,GAAG;QAC9D,KAAA6C,YAAA,GAAIpB,KAAK,CAACA,KAAK,cAAAoB,YAAA,eAAXA,YAAA,CAAaN,OAAO,EAAE;UACxBO,YAAY,GAAGrB,KAAK,CAACA,KAAK,CAACc,OAAO;QACpC;QACAb,KAAK,CAACoB,YAAY,CAAC;QACnBnB,OAAO,CAACF,KAAK,CAACA,KAAK,CAAC;MACtB;KACD,CAAC;EACJ;EAEAG,QAAQA,CAACC,KAAa,EAAEJ,KAAa;IAAA,IAAAsB,oBAAA;IACnC,MAAMhB,WAAW,GAAG,IAAI,CAAChC,SAAS,CAACiC,GAAG,CAACH,KAAK,CAAC;IAC7C,OAAO,CAAC,EAACE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEE,OAAO,KAAI,CAAC,EAACF,WAAW,aAAXA,WAAW,gBAAAgB,oBAAA,GAAXhB,WAAW,CAAEG,MAAM,cAAAa,oBAAA,eAAnBA,oBAAA,CAAsBtB,KAAK,CAAC;EACjE;EAEA;EACArB,kBAAkBA,CAAC4C,OAAwB;IACzC,IAAI,CAACA,OAAO,CAACzD,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAMS,IAAI,GAAGgD,OAAO,CAACzD,KAAK,CAAC0D,WAAW,EAAE;IACxC,MAAMC,cAAc,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;IAEhD,IAAI,CAACA,cAAc,CAACC,IAAI,CAACC,IAAI,IAAIpD,IAAI,CAACqD,QAAQ,CAACD,IAAI,CAAC,CAAC,EAAE;MACrD,OAAO;QAAEE,sBAAsB,EAAE;MAAI,CAAE;IACzC;IACA,OAAO,IAAI;EACb;EAEA5C,cAAcA,CAACsC,OAAwB;IACrC,IAAI,CAACA,OAAO,CAACzD,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAMkB,KAAK,GAAGuC,OAAO,CAACzD,KAAK,CAACgE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC9C,IAAI9C,KAAK,CAAC+C,MAAM,KAAK,EAAE,IAAI/C,KAAK,CAAC+C,MAAM,KAAK,EAAE,EAAE;MAC9C,OAAO;QAAEC,YAAY,EAAE;MAAI,CAAE;IAC/B;IACA,OAAO,IAAI;EACb;;sBAtIWhE,kBAAkB;;mCAAlBA,mBAAkB,EAAAX,EAAA,CAAA4E,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA9E,EAAA,CAAA4E,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAhF,EAAA,CAAA4E,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAjF,EAAA,CAAA4E,iBAAA,CAAAM,EAAA,CAAAC,eAAA;AAAA;;QAAlBxE,mBAAkB;EAAAyE,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCV3B3F,EAFJ,CAAAE,cAAA,oBAAiC,qBACA,qBACH;MACxBF,EAAA,CAAA6F,SAAA,sBAAmC;MACrC7F,EAAA,CAAAG,YAAA,EAAc;MACdH,EAAA,CAAAE,cAAA,gBAAW;MAAAF,EAAA,CAAAC,MAAA,wBAAiB;MAEhCD,EAFgC,CAAAG,YAAA,EAAY,EAC5B,EACH;MAMLH,EAJR,CAAAE,cAAA,qBAAiC,aACH,cACI,eAClB,gBACE;MACRF,EAAA,CAAA6F,SAAA,oBAAiG;MACjG7F,EAAA,CAAAE,cAAA,SAAG;MAUHF,EATA,CAAA8F,UAAA,KAAAC,0CAAA,OAAmC,KAAAC,0CAAA,OAGC,KAAAC,0CAAA,OAGA,KAAAC,0CAAA,OAGa;MAInDlG,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA6F,SAAA,oBAAwG;MACxG7F,EAAA,CAAAE,cAAA,SAAG;MAIHF,EAHA,CAAA8F,UAAA,KAAAK,0CAAA,OAAsC,KAAAC,0CAAA,OAGC;MAIzCpG,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA6F,SAAA,oBAAmG;MACnG7F,EAAA,CAAAE,cAAA,SAAG;MAIHF,EAHA,CAAA8F,UAAA,KAAAO,0CAAA,OAAmC,KAAAC,0CAAA,OAGD;MAIpCtG,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,qBACqE;MAC3EF,EAAA,CAAAuG,gBAAA,KAAAC,kCAAA,iCAAAxG,EAAA,CAAAyG,yBAAA,CAEC;MACHzG,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACHF,EAAA,CAAA8F,UAAA,KAAAY,0CAAA,OAAoC;MAItC1G,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA6F,SAAA,qBAAqG;MACrG7F,EAAA,CAAAE,cAAA,SAAG;MAIHF,EAHA,CAAA8F,UAAA,KAAAa,0CAAA,OAAoC,KAAAC,0CAAA,OAGI;MAI1C5G,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA6F,SAAA,qBAAuG;MACvG7F,EAAA,CAAAE,cAAA,SAAG;MAIHF,EAHA,CAAA8F,UAAA,KAAAe,0CAAA,OAAsC,KAAAC,0CAAA,OAGD;MAIvC9G,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,iBACG;MAAAF,EAAA,CAAAC,MAAA,eAAO;MAAAD,EAAA,CAAAG,YAAA,EAAY;MAC9BH,EAAA,CAAA6F,SAAA,sBAAqE;MACvE7F,EAAA,CAAAG,YAAA,EAAW;MAGTH,EADF,CAAAE,cAAA,gBAAU,sBACsE;MAC5EF,EAAA,CAAAuG,gBAAA,KAAAQ,kCAAA,iCAAAC,UAAA,CAEC;MACHhH,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACHF,EAAA,CAAA8F,UAAA,KAAAmB,0CAAA,OAAqC;MAKzCjH,EAFI,CAAAG,YAAA,EAAI,EACK,EACF;MAGTH,EADF,CAAAE,cAAA,mBAAyD,0BACS;MAAjBF,EAAA,CAAAkH,UAAA,mBAAAC,6DAAA;QAAA,OAASvB,GAAA,CAAAvC,IAAA,EAAM;MAAA,EAAC;MAC7DrD,EAAA,CAAA6F,SAAA,oBAAsC;MAKhD7F,EAJQ,CAAAG,YAAA,EAAiB,EACT,EACL,EACH,EACM;;;MAtHFH,EAAA,CAAAI,UAAA,qBAAoB;MASnBJ,EAAA,CAAAM,SAAA,GAAmB;MAAnBN,EAAA,CAAAI,UAAA,oBAAmB;MAEtBJ,EAAA,CAAAM,SAAA,GAAuB;MAAvBN,EAAA,CAAAI,UAAA,cAAAwF,GAAA,CAAA3E,SAAA,CAAuB;MAKvBjB,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,+BAEC;MACD9C,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,gCAEC;MACD9C,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,gCAEC;MACD9C,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,6CAEC;MAOD9C,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,kCAEC;MACD9C,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,mCAEC;MAOD9C,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,+BAEC;MACD9C,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,8BAEC;MAMC9C,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAqH,UAAA,CAAAzB,GAAA,CAAA5D,YAAA,CAEC;MAGHhC,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,gCAEC;MAOD9C,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,gCAEC;MACD9C,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,oCAEC;MAOD9C,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,kCAEC;MACD9C,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,iCAEC;MAWC9C,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAqH,UAAA,CAAAzB,GAAA,CAAA3D,aAAA,CAEC;MAGHjC,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAxB,GAAA,CAAA9C,QAAA,iCAEC;MAMa9C,EAAA,CAAAM,SAAA,GAA8B;MAA9BN,EAAA,CAAAI,UAAA,aAAAwF,GAAA,CAAA3E,SAAA,CAAAqG,OAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}