{"ast": null, "code": "var _CustomerFormComponent;\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { dateMask, phoneMask, maskitoElement, parseDateMask, formatDateMask } from '../../core/constants/mask.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/customer.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@maskito/angular\";\nconst _forTrack0 = ($index, $item) => $item.value;\nfunction CustomerFormComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction CustomerFormComponent_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00EDnimo 3 caracteres \");\n  }\n}\nfunction CustomerFormComponent_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00E1ximo 100 caracteres \");\n  }\n}\nfunction CustomerFormComponent_Conditional_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O nome deve conter apenas letras e espa\\u00E7os \");\n  }\n}\nfunction CustomerFormComponent_Conditional_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction CustomerFormComponent_Conditional_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Email inv\\u00E1lido \");\n  }\n}\nfunction CustomerFormComponent_Conditional_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Use um email de dom\\u00EDnio v\\u00E1lido (gmail.com, hotmail.com, outlook.com, yahoo.com) \");\n  }\n}\nfunction CustomerFormComponent_Conditional_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction CustomerFormComponent_Conditional_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Telefone deve ter 11 d\\u00EDgitos (com DDD) \");\n  }\n}\nfunction CustomerFormComponent_Conditional_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction CustomerFormComponent_Conditional_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Cliente deve ter pelo menos 16 anos \");\n  }\n}\nfunction CustomerFormComponent_Conditional_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Data de nascimento inv\\u00E1lida \");\n  }\n}\nfunction CustomerFormComponent_Conditional_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction CustomerFormComponent_Conditional_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Endere\\u00E7o deve ter pelo menos 10 caracteres \");\n  }\n}\nfunction CustomerFormComponent_For_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(type_r1.label);\n  }\n}\nfunction CustomerFormComponent_Conditional_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nexport class CustomerFormComponent {\n  constructor(customerService, router, activatedRoute, toastController) {\n    this.customerService = customerService;\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    this.toastController = toastController;\n    this.dateMask = dateMask;\n    this.phoneMask = phoneMask;\n    this.maskitoElement = maskitoElement;\n    this.customerForm = new FormGroup({\n      name: new FormControl('', [Validators.required, Validators.minLength(3), Validators.maxLength(100), ApplicationValidators.nameValidator]),\n      email: new FormControl('', [Validators.required, Validators.email, ApplicationValidators.emailDomainValidator]),\n      phone: new FormControl('', [Validators.required, ApplicationValidators.phoneValidator]),\n      birthDate: new FormControl('', [Validators.required, ApplicationValidators.ageValidator]),\n      address: new FormControl('', [Validators.required, ApplicationValidators.addressValidator]),\n      customerType: new FormControl('regular', [Validators.required]),\n      active: new FormControl(true)\n    });\n    this.customerTypes = [{\n      value: 'regular',\n      label: 'Regular'\n    }, {\n      value: 'premium',\n      label: 'Premium'\n    }, {\n      value: 'vip',\n      label: 'VIP'\n    }];\n  }\n  ngOnInit() {\n    const customerId = this.activatedRoute.snapshot.params['id'];\n    if (customerId) {\n      this.customerService.getById(+customerId).subscribe({\n        next: customer => {\n          if (customer) {\n            this.customerId = +customerId;\n            let formattedBirthDate = '';\n            if (customer.birthDate instanceof Date) {\n              formattedBirthDate = formatDateMask(customer.birthDate);\n            } else if (typeof customer.birthDate === 'string') {\n              const parsedDate = parseDateMask(customer.birthDate, 'yyyy/mm/dd');\n              if (parsedDate) {\n                formattedBirthDate = formatDateMask(parsedDate);\n              } else {\n                formattedBirthDate = customer.birthDate;\n              }\n            }\n            this.customerForm.patchValue({\n              name: customer.name,\n              email: customer.email,\n              phone: customer.phone,\n              birthDate: formattedBirthDate,\n              address: customer.address,\n              customerType: customer.customerType,\n              active: customer.active\n            });\n          }\n        },\n        error: error => {\n          console.error('Erro ao carregar o cliente', error);\n          this.toastController.create({\n            message: 'Erro ao carregar o cliente',\n            duration: 3000,\n            color: 'danger'\n          }).then(toast => toast.present());\n        }\n      });\n    }\n  }\n  save() {\n    let {\n      value\n    } = this.customerForm;\n    if (value.birthDate) {\n      const parsedDate = parseDateMask(value.birthDate);\n      if (parsedDate) {\n        value.birthDate = parsedDate;\n      }\n    }\n    console.log('Salvando cliente:', value);\n    this.customerService.save({\n      ...value,\n      id: this.customerId\n    }).subscribe({\n      next: () => {\n        this.toastController.create({\n          message: 'Cliente salvo com sucesso!',\n          duration: 3000\n        }).then(toast => toast.present());\n        this.router.navigate(['/customers']);\n      },\n      error: error => {\n        var _error$error;\n        let errorMessage = 'Erro ao salvar o cliente ' + value.name + '!';\n        if ((_error$error = error.error) !== null && _error$error !== void 0 && _error$error.message) {\n          errorMessage = error.error.message;\n        }\n        console.error('Erro ao salvar o cliente', error);\n        this.toastController.create({\n          message: errorMessage,\n          duration: 3000,\n          color: 'danger'\n        }).then(toast => toast.present());\n      }\n    });\n  }\n  hasError(field, error) {\n    var _formControl$errors;\n    const formControl = this.customerForm.get(field);\n    return !!(formControl !== null && formControl !== void 0 && formControl.touched) && !!(formControl !== null && formControl !== void 0 && (_formControl$errors = formControl.errors) !== null && _formControl$errors !== void 0 && _formControl$errors[error]);\n  }\n}\n_CustomerFormComponent = CustomerFormComponent;\n_CustomerFormComponent.ɵfac = function CustomerFormComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CustomerFormComponent)(i0.ɵɵdirectiveInject(i1.CustomerService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ToastController));\n};\n_CustomerFormComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _CustomerFormComponent,\n  selectors: [[\"app-customer-form\"]],\n  standalone: false,\n  decls: 52,\n  vars: 23,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [3, \"fullscreen\"], [1, \"form-container\"], [3, \"formGroup\"], [\"formControlName\", \"name\", \"labelPlacement\", \"floating\", \"label\", \"Nome\", \"type\", \"text\"], [\"formControlName\", \"email\", \"labelPlacement\", \"floating\", \"label\", \"Email\", \"type\", \"email\"], [\"formControlName\", \"phone\", \"labelPlacement\", \"floating\", \"label\", \"Telefone\", \"type\", \"tel\", 3, \"maskito\", \"maskitoElement\"], [\"formControlName\", \"birthDate\", \"labelPlacement\", \"floating\", \"label\", \"Data de Nascimento\", 3, \"maskito\", \"maskitoElement\"], [\"formControlName\", \"address\", \"labelPlacement\", \"floating\", \"label\", \"Endere\\u00E7o\", \"type\", \"text\"], [\"formControlName\", \"customerType\", \"labelPlacement\", \"floating\", \"label\", \"Tipo de Cliente\"], [3, \"value\"], [\"formControlName\", \"active\", \"slot\", \"end\"], [\"vertical\", \"bottom\", \"horizontal\", \"end\", \"slot\", \"fixed\"], [3, \"click\", \"disabled\"], [\"name\", \"checkmark\"]],\n  template: function CustomerFormComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-menu-button\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5, \"Cadastro de Clientes\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 3)(7, \"div\", 4)(8, \"form\", 5)(9, \"ion-list\")(10, \"ion-item\");\n      i0.ɵɵelement(11, \"ion-input\", 6);\n      i0.ɵɵelementStart(12, \"p\");\n      i0.ɵɵtemplate(13, CustomerFormComponent_Conditional_13_Template, 1, 0)(14, CustomerFormComponent_Conditional_14_Template, 1, 0)(15, CustomerFormComponent_Conditional_15_Template, 1, 0)(16, CustomerFormComponent_Conditional_16_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(17, \"ion-item\");\n      i0.ɵɵelement(18, \"ion-input\", 7);\n      i0.ɵɵelementStart(19, \"p\");\n      i0.ɵɵtemplate(20, CustomerFormComponent_Conditional_20_Template, 1, 0)(21, CustomerFormComponent_Conditional_21_Template, 1, 0)(22, CustomerFormComponent_Conditional_22_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(23, \"ion-item\");\n      i0.ɵɵelement(24, \"ion-input\", 8);\n      i0.ɵɵelementStart(25, \"p\");\n      i0.ɵɵtemplate(26, CustomerFormComponent_Conditional_26_Template, 1, 0)(27, CustomerFormComponent_Conditional_27_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(28, \"ion-item\");\n      i0.ɵɵelement(29, \"ion-input\", 9);\n      i0.ɵɵelementStart(30, \"p\");\n      i0.ɵɵtemplate(31, CustomerFormComponent_Conditional_31_Template, 1, 0)(32, CustomerFormComponent_Conditional_32_Template, 1, 0)(33, CustomerFormComponent_Conditional_33_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(34, \"ion-item\");\n      i0.ɵɵelement(35, \"ion-input\", 10);\n      i0.ɵɵelementStart(36, \"p\");\n      i0.ɵɵtemplate(37, CustomerFormComponent_Conditional_37_Template, 1, 0)(38, CustomerFormComponent_Conditional_38_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(39, \"ion-item\")(40, \"ion-select\", 11);\n      i0.ɵɵrepeaterCreate(41, CustomerFormComponent_For_42_Template, 2, 2, \"ion-select-option\", 12, _forTrack0);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"p\");\n      i0.ɵɵtemplate(44, CustomerFormComponent_Conditional_44_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(45, \"ion-item\")(46, \"ion-label\");\n      i0.ɵɵtext(47, \"Cliente Ativo:\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(48, \"ion-toggle\", 13);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(49, \"ion-fab\", 14)(50, \"ion-fab-button\", 15);\n      i0.ɵɵlistener(\"click\", function CustomerFormComponent_Template_ion_fab_button_click_50_listener() {\n        return ctx.save();\n      });\n      i0.ɵɵelement(51, \"ion-icon\", 16);\n      i0.ɵɵelementEnd()()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.customerForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"required\") ? 13 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"minlength\") ? 14 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"maxlength\") ? 15 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"invalidName\") ? 16 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"email\", \"required\") ? 20 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"email\", \"email\") ? 21 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"email\", \"invalidDomain\") ? 22 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maskito\", ctx.phoneMask)(\"maskitoElement\", ctx.maskitoElement);\n      i0.ɵɵadvance(2);\n      i0.ɵɵconditional(ctx.hasError(\"phone\", \"required\") ? 26 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"phone\", \"invalidPhone\") ? 27 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maskito\", ctx.dateMask)(\"maskitoElement\", ctx.maskitoElement);\n      i0.ɵɵadvance(2);\n      i0.ɵɵconditional(ctx.hasError(\"birthDate\", \"required\") ? 31 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"birthDate\", \"tooYoung\") ? 32 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"birthDate\", \"tooOld\") ? 33 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"address\", \"required\") ? 37 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"address\", \"addressTooShort\") ? 38 : -1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵrepeater(ctx.customerTypes);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"customerType\", \"required\") ? 44 : -1);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"disabled\", ctx.customerForm.invalid);\n    }\n  },\n  dependencies: [i4.ɵNgNoValidate, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i3.IonButtons, i3.IonContent, i3.IonFab, i3.IonFabButton, i3.IonHeader, i3.IonIcon, i3.IonInput, i3.IonItem, i3.IonLabel, i3.IonList, i3.IonMenuButton, i3.IonSelect, i3.IonSelectOption, i3.IonTitle, i3.IonToggle, i3.IonToolbar, i3.BooleanValueAccessor, i3.SelectValueAccessor, i3.TextValueAccessor, i5.MaskitoDirective],\n  styles: [\".form-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\nion-item[_ngcontent-%COMP%] {\\n  --padding-start: 0;\\n  margin-bottom: 8px;\\n}\\nion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--ion-color-danger);\\n  padding-left: 16px;\\n}\\n\\nion-toggle[_ngcontent-%COMP%] {\\n  padding-right: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY3VzdG9tZXJzL2N1c3RvbWVyLWZvcm0vY3VzdG9tZXItZm9ybS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFBZ0IsZ0JBQUE7RUFDaEIsY0FBQTtBQUVGOztBQURBO0VBQ0Usa0JBQUE7RUFBcUIsa0JBQUE7QUFLdkI7QUFKSTtFQUNBLGVBQUE7RUFBb0IsOEJBQUE7RUFDcEIsa0JBQUE7QUFPSjs7QUFMQTtFQUNFLG1CQUFBO0FBUUYiLCJzb3VyY2VzQ29udGVudCI6WyIuZm9ybS1jb250YWluZXIge1xyXG4gIHBhZGRpbmc6IDE2cHg7ICBtYXgtd2lkdGg6IDgwMHB4O1xyXG4gIG1hcmdpbjogMCBhdXRvO31cclxuaW9uLWl0ZW0ge1xyXG4gIC0tcGFkZGluZy1zdGFydDogMDsgIG1hcmdpbi1ib3R0b206IDhweDtcclxuICAgIHAge1xyXG4gICAgZm9udC1zaXplOiAxMnB4OyAgICBjb2xvcjogdmFyKC0taW9uLWNvbG9yLWRhbmdlcik7XHJcbiAgICBwYWRkaW5nLWxlZnQ6IDE2cHg7ICB9XHJcbn1cclxuaW9uLXRvZ2dsZSB7XHJcbiAgcGFkZGluZy1yaWdodDogMTZweDtcclxufVxyXG5cclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n});", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "dateMask", "phoneMask", "maskitoElement", "parseDateMask", "formatDateMask", "i0", "ɵɵtext", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵproperty", "type_r1", "value", "ɵɵadvance", "ɵɵtextInterpolate", "label", "CustomerFormComponent", "constructor", "customerService", "router", "activatedRoute", "toastController", "customerForm", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "ApplicationValidators", "nameValidator", "email", "emailDomainValidator", "phone", "phoneValidator", "birthDate", "ageValida<PERSON>", "address", "addressValidator", "customerType", "active", "customerTypes", "ngOnInit", "customerId", "snapshot", "params", "getById", "subscribe", "next", "customer", "formattedBirthDate", "Date", "parsedDate", "patchValue", "error", "console", "create", "message", "duration", "color", "then", "toast", "present", "save", "log", "id", "navigate", "_error$error", "errorMessage", "<PERSON><PERSON><PERSON><PERSON>", "field", "_formControl$errors", "formControl", "get", "touched", "errors", "ɵɵdirectiveInject", "i1", "CustomerService", "i2", "Router", "ActivatedRoute", "i3", "ToastController", "selectors", "standalone", "decls", "vars", "consts", "template", "CustomerFormComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "CustomerFormComponent_Conditional_13_Template", "CustomerFormComponent_Conditional_14_Template", "CustomerFormComponent_Conditional_15_Template", "CustomerFormComponent_Conditional_16_Template", "CustomerFormComponent_Conditional_20_Template", "CustomerFormComponent_Conditional_21_Template", "CustomerFormComponent_Conditional_22_Template", "CustomerFormComponent_Conditional_26_Template", "CustomerFormComponent_Conditional_27_Template", "CustomerFormComponent_Conditional_31_Template", "CustomerFormComponent_Conditional_32_Template", "CustomerFormComponent_Conditional_33_Template", "CustomerFormComponent_Conditional_37_Template", "CustomerFormComponent_Conditional_38_Template", "ɵɵrepeaterCreate", "CustomerFormComponent_For_42_Template", "_forTrack0", "CustomerFormComponent_Conditional_44_Template", "ɵɵlistener", "CustomerFormComponent_Template_ion_fab_button_click_50_listener", "ɵɵconditional", "ɵɵrepeater", "invalid"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\customers\\customer-form\\customer-form.component.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\customers\\customer-form\\customer-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ToastController } from '@ionic/angular';\r\nimport { CustomerService } from '../services/customer.service';\r\nimport { dateMask, phoneMask, maskitoElement, parseDateMask, formatDateMask } from '../../core/constants/mask.constants';\r\n\r\n@Component({\r\n  selector: 'app-customer-form',\r\n  templateUrl: './customer-form.component.html',\r\n  styleUrls: ['./customer-form.component.scss'],\r\n  standalone: false,\r\n})\r\nexport class CustomerFormComponent implements OnInit {\r\n  dateMask = dateMask;\r\n  phoneMask = phoneMask;\r\n  maskitoElement = maskitoElement;\r\n\r\n  customerForm: FormGroup = new FormGroup({\r\n    name: new FormControl('', [\r\n      Validators.required,\r\n      Validators.minLength(3),\r\n      Validators.maxLength(100),\r\n      ApplicationValidators.nameValidator\r\n    ]),\r\n    email: new FormControl('', [\r\n      Validators.required,\r\n      Validators.email,\r\n      ApplicationValidators.emailDomainValidator\r\n    ]),\r\n    phone: new FormControl('', [\r\n      Validators.required,\r\n      ApplicationValidators.phoneValidator\r\n    ]),\r\n    birthDate: new FormControl('', [\r\n      Validators.required,\r\n      ApplicationValidators.ageValidator\r\n    ]),\r\n    address: new FormControl('', [\r\n      Validators.required,\r\n      ApplicationValidators.addressValidator\r\n    ]),\r\n    customerType: new FormControl('regular', [Validators.required]),\r\n    active: new FormControl(true)\r\n  });\r\n\r\n  customerId!: number;\r\n  \r\n  customerTypes = [\r\n    { value: 'regular', label: 'Regular' },\r\n    { value: 'premium', label: 'Premium' },\r\n    { value: 'vip', label: 'VIP' }\r\n  ];\r\n\r\n  constructor(\r\n    private customerService: CustomerService,\r\n    private router: Router,\r\n    private activatedRoute: ActivatedRoute,\r\n    private toastController: ToastController\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    const customerId = this.activatedRoute.snapshot.params['id'];\r\n    if (customerId) {\r\n      this.customerService.getById(+customerId).subscribe({\r\n        next: (customer) => {\r\n          if (customer) {\r\n            this.customerId = +customerId;\r\n\r\n            let formattedBirthDate = '';\r\n            if (customer.birthDate instanceof Date) {\r\n              formattedBirthDate = formatDateMask(customer.birthDate);\r\n            } else if (typeof customer.birthDate === 'string') {\r\n              const parsedDate = parseDateMask(customer.birthDate, 'yyyy/mm/dd');\r\n              if (parsedDate) {\r\n                formattedBirthDate = formatDateMask(parsedDate);\r\n              } else {\r\n                formattedBirthDate = customer.birthDate;\r\n              }\r\n            }\r\n\r\n            this.customerForm.patchValue({\r\n              name: customer.name,\r\n              email: customer.email,\r\n              phone: customer.phone,\r\n              birthDate: formattedBirthDate,\r\n              address: customer.address,\r\n              customerType: customer.customerType,\r\n              active: customer.active\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.error('Erro ao carregar o cliente', error);\r\n          this.toastController.create({\r\n            message: 'Erro ao carregar o cliente',\r\n            duration: 3000,\r\n            color: 'danger'\r\n          }).then(toast => toast.present());\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  save() {\r\n    let { value } = this.customerForm;\r\n\r\n    if (value.birthDate) {\r\n      const parsedDate = parseDateMask(value.birthDate);\r\n      if (parsedDate) {\r\n        value.birthDate = parsedDate;\r\n      }\r\n    }\r\n\r\n    console.log('Salvando cliente:', value);\r\n\r\n    this.customerService.save({\r\n      ...value,\r\n      id: this.customerId\r\n    }).subscribe({\r\n      next: () => {\r\n        this.toastController.create({\r\n          message: 'Cliente salvo com sucesso!',\r\n          duration: 3000,\r\n        }).then(toast => toast.present());\r\n        this.router.navigate(['/customers']);\r\n      },\r\n      error: (error) => {\r\n        let errorMessage = 'Erro ao salvar o cliente ' + value.name + '!';\r\n        if (error.error?.message) {\r\n          errorMessage = error.error.message;\r\n        }\r\n        console.error('Erro ao salvar o cliente', error);\r\n        this.toastController.create({\r\n          message: errorMessage,\r\n          duration: 3000,\r\n          color: 'danger'\r\n        }).then(toast => toast.present());\r\n      }\r\n    });\r\n  }\r\n\r\n  hasError(field: string, error: string): boolean {\r\n    const formControl = this.customerForm.get(field);\r\n    return !!formControl?.touched && !!formControl?.errors?.[error];\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-menu-button></ion-menu-button>\r\n    </ion-buttons>\r\n    <ion-title>Cadastro de Clientes</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div class=\"form-container\">\r\n    <form [formGroup]=\"customerForm\">\r\n      <ion-list>\r\n        <ion-item>\r\n          <ion-input formControlName=\"name\" labelPlacement=\"floating\" label=\"Nome\" type=\"text\"></ion-input>\r\n          <p>\r\n            @if(hasError('name', 'required')) {\r\n            O campo é obrigatório\r\n            }\r\n            @if(hasError('name', 'minlength')) {\r\n            O campo deve ter no mínimo 3 caracteres\r\n            }\r\n            @if(hasError('name', 'maxlength')) {\r\n            O campo deve ter no máximo 100 caracteres\r\n            }\r\n            @if(hasError('name', 'invalidName')) {\r\n            O nome deve conter apenas letras e espaços\r\n            }\r\n          </p>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-input formControlName=\"email\" labelPlacement=\"floating\" label=\"Email\" type=\"email\"></ion-input>\r\n          <p>\r\n            @if(hasError('email', 'required')) {\r\n            O campo é obrigatório\r\n            }\r\n            @if(hasError('email', 'email')) {\r\n            Email inválido\r\n            }\r\n            @if(hasError('email', 'invalidDomain')) {\r\n            Use um email de domínio válido (gmail.com, hotmail.com, outlook.com, yahoo.com)\r\n            }\r\n          </p>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-input formControlName=\"phone\" labelPlacement=\"floating\" label=\"Telefone\" type=\"tel\" [maskito]=\"phoneMask\"\r\n            [maskitoElement]=\"maskitoElement\"></ion-input>\r\n          <p>\r\n            @if(hasError('phone', 'required')) {\r\n            O campo é obrigatório\r\n            }\r\n            @if(hasError('phone', 'invalidPhone')) {\r\n            Telefone deve ter 11 dígitos (com DDD)\r\n            }\r\n          </p>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-input formControlName=\"birthDate\" labelPlacement=\"floating\" label=\"Data de Nascimento\"\r\n            [maskito]=\"dateMask\" [maskitoElement]=\"maskitoElement\"></ion-input>\r\n          <p>\r\n            @if(hasError('birthDate', 'required')) {\r\n            O campo é obrigatório\r\n            }\r\n            @if(hasError('birthDate', 'tooYoung')) {\r\n            Cliente deve ter pelo menos 16 anos\r\n            }\r\n            @if(hasError('birthDate', 'tooOld')) {\r\n            Data de nascimento inválida\r\n            }\r\n          </p>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-input formControlName=\"address\" labelPlacement=\"floating\" label=\"Endereço\" type=\"text\"></ion-input>\r\n          <p>\r\n            @if(hasError('address', 'required')) {\r\n            O campo é obrigatório\r\n            }\r\n            @if(hasError('address', 'addressTooShort')) {\r\n            Endereço deve ter pelo menos 10 caracteres\r\n            }\r\n          </p>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-select formControlName=\"customerType\" labelPlacement=\"floating\" label=\"Tipo de Cliente\">\r\n            @for(type of customerTypes; track type.value) {\r\n            <ion-select-option [value]=\"type.value\">{{ type.label }}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n            @if(hasError('customerType', 'required')) {\r\n            O campo é obrigatório\r\n            }\r\n          </p>\r\n        </ion-item>\r\n\r\n        <ion-item>\r\n          <ion-label>Cliente Ativo:</ion-label>\r\n          <ion-toggle formControlName=\"active\" slot=\"end\"></ion-toggle>\r\n        </ion-item>\r\n      </ion-list>\r\n\r\n      <ion-fab vertical=\"bottom\" horizontal=\"end\" slot=\"fixed\">\r\n        <ion-fab-button [disabled]=\"customerForm.invalid\" (click)=\"save()\">\r\n          <ion-icon name=\"checkmark\"></ion-icon>\r\n        </ion-fab-button>\r\n      </ion-fab>\r\n    </form>\r\n  </div>\r\n</ion-content>"], "mappings": ";AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAInE,SAASC,QAAQ,EAAEC,SAAS,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,QAAQ,qCAAqC;;;;;;;;;;ICY5GC,EAAA,CAAAC,MAAA,wCACA;;;;;IAEAD,EAAA,CAAAC,MAAA,qDACA;;;;;IAEAD,EAAA,CAAAC,MAAA,uDACA;;;;;IAEAD,EAAA,CAAAC,MAAA,wDACA;;;;;IAQAD,EAAA,CAAAC,MAAA,wCACA;;;;;IAEAD,EAAA,CAAAC,MAAA,4BACA;;;;;IAEAD,EAAA,CAAAC,MAAA,kGACA;;;;;IASAD,EAAA,CAAAC,MAAA,wCACA;;;;;IAEAD,EAAA,CAAAC,MAAA,oDACA;;;;;IASAD,EAAA,CAAAC,MAAA,wCACA;;;;;IAEAD,EAAA,CAAAC,MAAA,4CACA;;;;;IAEAD,EAAA,CAAAC,MAAA,yCACA;;;;;IAQAD,EAAA,CAAAC,MAAA,wCACA;;;;;IAEAD,EAAA,CAAAC,MAAA,wDACA;;;;;IAOAD,EAAA,CAAAE,cAAA,4BAAwC;IAAAF,EAAA,CAAAC,MAAA,GAAgB;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAAzDH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,KAAA,CAAoB;IAACN,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAI,KAAA,CAAgB;;;;;IAKxDT,EAAA,CAAAC,MAAA,wCACA;;;ADnFZ,OAAM,MAAOS,qBAAqB;EAyChCC,YACUC,eAAgC,EAChCC,MAAc,EACdC,cAA8B,EAC9BC,eAAgC;IAHhC,KAAAH,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IA5CzB,KAAApB,QAAQ,GAAGA,QAAQ;IACnB,KAAAC,SAAS,GAAGA,SAAS;IACrB,KAAAC,cAAc,GAAGA,cAAc;IAE/B,KAAAmB,YAAY,GAAc,IAAIvB,SAAS,CAAC;MACtCwB,IAAI,EAAE,IAAIzB,WAAW,CAAC,EAAE,EAAE,CACxBE,UAAU,CAACwB,QAAQ,EACnBxB,UAAU,CAACyB,SAAS,CAAC,CAAC,CAAC,EACvBzB,UAAU,CAAC0B,SAAS,CAAC,GAAG,CAAC,EACzBC,qBAAqB,CAACC,aAAa,CACpC,CAAC;MACFC,KAAK,EAAE,IAAI/B,WAAW,CAAC,EAAE,EAAE,CACzBE,UAAU,CAACwB,QAAQ,EACnBxB,UAAU,CAAC6B,KAAK,EAChBF,qBAAqB,CAACG,oBAAoB,CAC3C,CAAC;MACFC,KAAK,EAAE,IAAIjC,WAAW,CAAC,EAAE,EAAE,CACzBE,UAAU,CAACwB,QAAQ,EACnBG,qBAAqB,CAACK,cAAc,CACrC,CAAC;MACFC,SAAS,EAAE,IAAInC,WAAW,CAAC,EAAE,EAAE,CAC7BE,UAAU,CAACwB,QAAQ,EACnBG,qBAAqB,CAACO,YAAY,CACnC,CAAC;MACFC,OAAO,EAAE,IAAIrC,WAAW,CAAC,EAAE,EAAE,CAC3BE,UAAU,CAACwB,QAAQ,EACnBG,qBAAqB,CAACS,gBAAgB,CACvC,CAAC;MACFC,YAAY,EAAE,IAAIvC,WAAW,CAAC,SAAS,EAAE,CAACE,UAAU,CAACwB,QAAQ,CAAC,CAAC;MAC/Dc,MAAM,EAAE,IAAIxC,WAAW,CAAC,IAAI;KAC7B,CAAC;IAIF,KAAAyC,aAAa,GAAG,CACd;MAAE3B,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEH,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEH,KAAK,EAAE,KAAK;MAAEG,KAAK,EAAE;IAAK,CAAE,CAC/B;EAOG;EAEJyB,QAAQA,CAAA;IACN,MAAMC,UAAU,GAAG,IAAI,CAACrB,cAAc,CAACsB,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;IAC5D,IAAIF,UAAU,EAAE;MACd,IAAI,CAACvB,eAAe,CAAC0B,OAAO,CAAC,CAACH,UAAU,CAAC,CAACI,SAAS,CAAC;QAClDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,EAAE;YACZ,IAAI,CAACN,UAAU,GAAG,CAACA,UAAU;YAE7B,IAAIO,kBAAkB,GAAG,EAAE;YAC3B,IAAID,QAAQ,CAACd,SAAS,YAAYgB,IAAI,EAAE;cACtCD,kBAAkB,GAAG3C,cAAc,CAAC0C,QAAQ,CAACd,SAAS,CAAC;YACzD,CAAC,MAAM,IAAI,OAAOc,QAAQ,CAACd,SAAS,KAAK,QAAQ,EAAE;cACjD,MAAMiB,UAAU,GAAG9C,aAAa,CAAC2C,QAAQ,CAACd,SAAS,EAAE,YAAY,CAAC;cAClE,IAAIiB,UAAU,EAAE;gBACdF,kBAAkB,GAAG3C,cAAc,CAAC6C,UAAU,CAAC;cACjD,CAAC,MAAM;gBACLF,kBAAkB,GAAGD,QAAQ,CAACd,SAAS;cACzC;YACF;YAEA,IAAI,CAACX,YAAY,CAAC6B,UAAU,CAAC;cAC3B5B,IAAI,EAAEwB,QAAQ,CAACxB,IAAI;cACnBM,KAAK,EAAEkB,QAAQ,CAAClB,KAAK;cACrBE,KAAK,EAAEgB,QAAQ,CAAChB,KAAK;cACrBE,SAAS,EAAEe,kBAAkB;cAC7Bb,OAAO,EAAEY,QAAQ,CAACZ,OAAO;cACzBE,YAAY,EAAEU,QAAQ,CAACV,YAAY;cACnCC,MAAM,EAAES,QAAQ,CAACT;aAClB,CAAC;UACJ;QACF,CAAC;QACDc,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClD,IAAI,CAAC/B,eAAe,CAACiC,MAAM,CAAC;YAC1BC,OAAO,EAAE,4BAA4B;YACrCC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;WACR,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;QACnC;OACD,CAAC;IACJ;EACF;EAEAC,IAAIA,CAAA;IACF,IAAI;MAAEjD;IAAK,CAAE,GAAG,IAAI,CAACU,YAAY;IAEjC,IAAIV,KAAK,CAACqB,SAAS,EAAE;MACnB,MAAMiB,UAAU,GAAG9C,aAAa,CAACQ,KAAK,CAACqB,SAAS,CAAC;MACjD,IAAIiB,UAAU,EAAE;QACdtC,KAAK,CAACqB,SAAS,GAAGiB,UAAU;MAC9B;IACF;IAEAG,OAAO,CAACS,GAAG,CAAC,mBAAmB,EAAElD,KAAK,CAAC;IAEvC,IAAI,CAACM,eAAe,CAAC2C,IAAI,CAAC;MACxB,GAAGjD,KAAK;MACRmD,EAAE,EAAE,IAAI,CAACtB;KACV,CAAC,CAACI,SAAS,CAAC;MACXC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACzB,eAAe,CAACiC,MAAM,CAAC;UAC1BC,OAAO,EAAE,4BAA4B;UACrCC,QAAQ,EAAE;SACX,CAAC,CAACE,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;QACjC,IAAI,CAACzC,MAAM,CAAC6C,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MACtC,CAAC;MACDZ,KAAK,EAAGA,KAAK,IAAI;QAAA,IAAAa,YAAA;QACf,IAAIC,YAAY,GAAG,2BAA2B,GAAGtD,KAAK,CAACW,IAAI,GAAG,GAAG;QACjE,KAAA0C,YAAA,GAAIb,KAAK,CAACA,KAAK,cAAAa,YAAA,eAAXA,YAAA,CAAaV,OAAO,EAAE;UACxBW,YAAY,GAAGd,KAAK,CAACA,KAAK,CAACG,OAAO;QACpC;QACAF,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAAC/B,eAAe,CAACiC,MAAM,CAAC;UAC1BC,OAAO,EAAEW,YAAY;UACrBV,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;SACR,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;MACnC;KACD,CAAC;EACJ;EAEAO,QAAQA,CAACC,KAAa,EAAEhB,KAAa;IAAA,IAAAiB,mBAAA;IACnC,MAAMC,WAAW,GAAG,IAAI,CAAChD,YAAY,CAACiD,GAAG,CAACH,KAAK,CAAC;IAChD,OAAO,CAAC,EAACE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEE,OAAO,KAAI,CAAC,EAACF,WAAW,aAAXA,WAAW,gBAAAD,mBAAA,GAAXC,WAAW,CAAEG,MAAM,cAAAJ,mBAAA,eAAnBA,mBAAA,CAAsBjB,KAAK,CAAC;EACjE;;yBApIWpC,qBAAqB;;mCAArBA,sBAAqB,EAAAV,EAAA,CAAAoE,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAtE,EAAA,CAAAoE,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAxE,EAAA,CAAAoE,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAzE,EAAA,CAAAoE,iBAAA,CAAAM,EAAA,CAAAC,eAAA;AAAA;;QAArBjE,sBAAqB;EAAAkE,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCX9BnF,EAFJ,CAAAE,cAAA,oBAAiC,qBACA,qBACH;MACxBF,EAAA,CAAAqF,SAAA,sBAAmC;MACrCrF,EAAA,CAAAG,YAAA,EAAc;MACdH,EAAA,CAAAE,cAAA,gBAAW;MAAAF,EAAA,CAAAC,MAAA,2BAAoB;MAEnCD,EAFmC,CAAAG,YAAA,EAAY,EAC/B,EACH;MAMLH,EAJR,CAAAE,cAAA,qBAAiC,aACH,cACO,eACrB,gBACE;MACRF,EAAA,CAAAqF,SAAA,oBAAiG;MACjGrF,EAAA,CAAAE,cAAA,SAAG;MAUDF,EATA,CAAAsF,UAAA,KAAAC,6CAAA,OAAmC,KAAAC,6CAAA,OAGC,KAAAC,6CAAA,OAGA,KAAAC,6CAAA,OAGE;MAI1C1F,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAAqF,SAAA,oBAAoG;MACpGrF,EAAA,CAAAE,cAAA,SAAG;MAODF,EANA,CAAAsF,UAAA,KAAAK,6CAAA,OAAoC,KAAAC,6CAAA,OAGH,KAAAC,6CAAA,OAGQ;MAI7C7F,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAAqF,SAAA,oBACgD;MAChDrF,EAAA,CAAAE,cAAA,SAAG;MAIDF,EAHA,CAAAsF,UAAA,KAAAQ,6CAAA,OAAoC,KAAAC,6CAAA,OAGI;MAI5C/F,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAAqF,SAAA,oBACqE;MACrErF,EAAA,CAAAE,cAAA,SAAG;MAODF,EANA,CAAAsF,UAAA,KAAAU,6CAAA,OAAwC,KAAAC,6CAAA,OAGA,KAAAC,6CAAA,OAGF;MAI1ClG,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAAqF,SAAA,qBAAwG;MACxGrF,EAAA,CAAAE,cAAA,SAAG;MAIDF,EAHA,CAAAsF,UAAA,KAAAa,6CAAA,OAAsC,KAAAC,6CAAA,OAGO;MAIjDpG,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,sBACqF;MAC3FF,EAAA,CAAAqG,gBAAA,KAAAC,qCAAA,iCAAAC,UAAA,CAEC;MACHvG,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACDF,EAAA,CAAAsF,UAAA,KAAAkB,6CAAA,OAA2C;MAI/CxG,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,iBACG;MAAAF,EAAA,CAAAC,MAAA,sBAAc;MAAAD,EAAA,CAAAG,YAAA,EAAY;MACrCH,EAAA,CAAAqF,SAAA,sBAA6D;MAEjErF,EADE,CAAAG,YAAA,EAAW,EACF;MAGTH,EADF,CAAAE,cAAA,mBAAyD,0BACY;MAAjBF,EAAA,CAAAyG,UAAA,mBAAAC,gEAAA;QAAA,OAAStB,GAAA,CAAA7B,IAAA,EAAM;MAAA,EAAC;MAChEvD,EAAA,CAAAqF,SAAA,oBAAsC;MAKhDrF,EAJQ,CAAAG,YAAA,EAAiB,EACT,EACL,EACH,EACM;;;MAjHFH,EAAA,CAAAI,UAAA,qBAAoB;MASnBJ,EAAA,CAAAO,SAAA,GAAmB;MAAnBP,EAAA,CAAAI,UAAA,oBAAmB;MAEtBJ,EAAA,CAAAO,SAAA,GAA0B;MAA1BP,EAAA,CAAAI,UAAA,cAAAgF,GAAA,CAAApE,YAAA,CAA0B;MAKxBhB,EAAA,CAAAO,SAAA,GAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,+BAEC;MACD7D,EAAA,CAAAO,SAAA,EAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,gCAEC;MACD7D,EAAA,CAAAO,SAAA,EAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,gCAEC;MACD7D,EAAA,CAAAO,SAAA,EAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,kCAEC;MAOD7D,EAAA,CAAAO,SAAA,GAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,gCAEC;MACD7D,EAAA,CAAAO,SAAA,EAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,6BAEC;MACD7D,EAAA,CAAAO,SAAA,EAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,qCAEC;MAKsF7D,EAAA,CAAAO,SAAA,GAAqB;MAC5GP,EADuF,CAAAI,UAAA,YAAAgF,GAAA,CAAAxF,SAAA,CAAqB,mBAAAwF,GAAA,CAAAvF,cAAA,CAC3E;MAEjCG,EAAA,CAAAO,SAAA,GAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,gCAEC;MACD7D,EAAA,CAAAO,SAAA,EAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,oCAEC;MAMD7D,EAAA,CAAAO,SAAA,GAAoB;MAACP,EAArB,CAAAI,UAAA,YAAAgF,GAAA,CAAAzF,QAAA,CAAoB,mBAAAyF,GAAA,CAAAvF,cAAA,CAAkC;MAEtDG,EAAA,CAAAO,SAAA,GAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,oCAEC;MACD7D,EAAA,CAAAO,SAAA,EAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,oCAEC;MACD7D,EAAA,CAAAO,SAAA,EAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,kCAEC;MAOD7D,EAAA,CAAAO,SAAA,GAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,kCAEC;MACD7D,EAAA,CAAAO,SAAA,EAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,yCAEC;MAMD7D,EAAA,CAAAO,SAAA,GAEC;MAFDP,EAAA,CAAA4G,UAAA,CAAAxB,GAAA,CAAAnD,aAAA,CAEC;MAGDjC,EAAA,CAAAO,SAAA,GAEC;MAFDP,EAAA,CAAA2G,aAAA,CAAAvB,GAAA,CAAAvB,QAAA,uCAEC;MAWW7D,EAAA,CAAAO,SAAA,GAAiC;MAAjCP,EAAA,CAAAI,UAAA,aAAAgF,GAAA,CAAApE,YAAA,CAAA6F,OAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}