{"ast": null, "code": "export const PaymentMethods = [{\n  value: 'pix',\n  label: 'PIX'\n}, {\n  value: 'debit',\n  label: 'Cartão de Débito'\n}, {\n  value: 'credit',\n  label: 'Cartão de Crédito'\n}];\nexport const SaleStatus = [{\n  value: 'pending',\n  label: '<PERSON>dent<PERSON>'\n}, {\n  value: 'completed',\n  label: '<PERSON><PERSON><PERSON><PERSON><PERSON>'\n}, {\n  value: 'canceled',\n  label: '<PERSON><PERSON><PERSON>'\n}];", "map": {"version": 3, "names": ["PaymentMethods", "value", "label", "SaleStatus"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\sales\\models\\sale.type.ts"], "sourcesContent": ["import { Customer } from \"../../customers/models/customer.type\";\r\nimport { Store } from \"../../stores/models/store.type\";\r\nimport { SaleItem } from \"./sale-item.type\";\r\n\r\nexport type Sale = {\r\n  id?: number;\r\n  date: Date | string;\r\n  customerId: number;\r\n  storeId: number;\r\n  customer?: Customer;\r\n  store?: Store;\r\n  items: SaleItem[];\r\n  totalValue: number;\r\n  paymentMethod: 'pix' | 'debit' | 'credit';\r\n  status: 'pending' | 'completed' | 'canceled';\r\n  seller: string;\r\n}\r\n\r\nexport type CreateSaleDto = {\r\n  customerId: number;\r\n  storeId: number;\r\n  paymentMethod: 'pix' | 'debit' | 'credit';\r\n  status?: 'pending' | 'completed' | 'canceled';\r\n  seller: string;\r\n  items: CreateSaleItemDto[];\r\n}\r\n\r\nexport type CreateSaleItemDto = {\r\n  productId: number;\r\n  productType: 'phone' | 'accessory';\r\n  quantity: number;\r\n  unitPrice: number;\r\n  subtotal: number;\r\n}\r\n\r\nexport type UpdateSaleDto = Partial<CreateSaleDto>\r\n\r\nexport type DashboardStats = {\r\n  totalSales: number;\r\n  totalRevenue: number;\r\n  monthlyRevenue: number;\r\n  pendingSales: number;\r\n}\r\n\r\nexport const PaymentMethods = [\r\n  { value: 'pix', label: 'PIX' },\r\n  { value: 'debit', label: 'Cartão de Débito' },\r\n  { value: 'credit', label: 'Cartão de Crédito' }\r\n];\r\n\r\nexport const SaleStatus = [\r\n  { value: 'pending', label: 'Pendente' },\r\n  { value: 'completed', label: 'Concluída' },\r\n  { value: 'canceled', label: 'Cancelada' }\r\n];\r\n"], "mappings": "AA4CA,OAAO,MAAMA,cAAc,GAAG,CAC5B;EAAEC,KAAK,EAAE,KAAK;EAAEC,KAAK,EAAE;AAAK,CAAE,EAC9B;EAAED,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAkB,CAAE,EAC7C;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAmB,CAAE,CAChD;AAED,OAAO,MAAMC,UAAU,GAAG,CACxB;EAAEF,KAAK,EAAE,SAAS;EAAEC,KAAK,EAAE;AAAU,CAAE,EACvC;EAAED,KAAK,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAW,CAAE,EAC1C;EAAED,KAAK,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAW,CAAE,CAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}