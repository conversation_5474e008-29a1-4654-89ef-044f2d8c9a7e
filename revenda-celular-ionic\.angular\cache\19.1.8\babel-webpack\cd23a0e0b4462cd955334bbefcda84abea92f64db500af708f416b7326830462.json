{"ast": null, "code": "var _AccessoryFormComponent;\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { ApplicationValidators } from '../../core/validators/url.validator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/accessory.service\";\nimport * as i2 from \"../../phones/services/phone.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@ionic/angular\";\nimport * as i5 from \"@angular/forms\";\nconst _forTrack0 = ($index, $item) => $item.id;\nfunction AccessoryFormComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00EDnimo 3 caracteres \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00E1ximo 100 caracteres \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Nome deve conter uma palavra identificadora (case, capa, carregador, fone, pel\\u00EDcula, suporte) \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00EDnimo 10 caracteres \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00E1ximo 500 caracteres \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Descri\\u00E7\\u00E3o n\\u00E3o pode conter palavras negativas \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O pre\\u00E7o deve ser maior ou igual a zero \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Pre\\u00E7o n\\u00E3o pode ser superior a R$ 5.000 \");\n  }\n}\nfunction AccessoryFormComponent_For_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(category_r1);\n  }\n}\nfunction AccessoryFormComponent_Conditional_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo de imagem deve ser uma URL v\\u00E1lida \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O estoque deve ser maior ou igual a zero \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O estoque deve ser um n\\u00FAmero inteiro \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Estoque n\\u00E3o pode ser superior a 10.000 unidades \");\n  }\n}\nfunction AccessoryFormComponent_For_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const phone_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", phone_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(phone_r2.model);\n  }\n}\nexport class AccessoryFormComponent {\n  constructor(accessoryService, phoneService, router, activatedRoute, toastController) {\n    this.accessoryService = accessoryService;\n    this.phoneService = phoneService;\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    this.toastController = toastController;\n    this.accessoryForm = new FormGroup({\n      name: new FormControl('', [Validators.required, Validators.minLength(3), Validators.maxLength(100), ApplicationValidators.accessoryNameValidator]),\n      description: new FormControl('', [Validators.required, Validators.minLength(10), Validators.maxLength(500), ApplicationValidators.descriptionValidator]),\n      price: new FormControl(0, [Validators.required, Validators.min(0), ApplicationValidators.accessoryPriceValidator]),\n      category: new FormControl('', Validators.required),\n      image: new FormControl('', [Validators.required, ApplicationValidators.urlValidator]),\n      compatiblePhones: new FormControl([]),\n      stock: new FormControl(0, [Validators.required, Validators.min(0), Validators.pattern('^[0-9]*$'), ApplicationValidators.accessoryStockValidator])\n    });\n    this.phones = [];\n    this.categories = ['Capa', 'Carregador', 'Fone de Ouvido', 'Película', 'Bateria Externa', 'Outro'];\n  }\n  ngOnInit() {\n    this.loadPhones();\n    const accessoryId = this.activatedRoute.snapshot.params['id'];\n    if (accessoryId) {\n      this.accessoryService.getById(+accessoryId).subscribe({\n        next: accessory => {\n          if (accessory) {\n            this.accessoryId = +accessoryId;\n            // Tratar preço como number\n            let priceValue = 0;\n            if (accessory.price) {\n              if (typeof accessory.price === 'number') {\n                priceValue = accessory.price;\n              } else if (typeof accessory.price === 'string') {\n                priceValue = parseFloat(accessory.price) || 0;\n              }\n            }\n            console.log('Carregando acessório:', accessory);\n            console.log('Celulares compatíveis do acessório:', accessory.compatiblePhones);\n            this.accessoryForm.patchValue({\n              name: accessory.name,\n              description: accessory.description,\n              price: priceValue,\n              category: accessory.category,\n              image: accessory.image,\n              compatiblePhones: accessory.compatiblePhones || [],\n              stock: accessory.stock\n            });\n          }\n        },\n        error: error => {\n          alert('Erro ao carregar o acessório com id ' + accessoryId);\n          console.error(error);\n        }\n      });\n    }\n  }\n  loadPhones() {\n    this.phoneService.getList().subscribe({\n      next: phones => {\n        this.phones = phones;\n      },\n      error: error => {\n        alert('Erro ao carregar lista de celulares');\n        console.error(error);\n      }\n    });\n  }\n  compareWith(o1, o2) {\n    return o1 && o2 ? o1.id === o2.id : o1 === o2;\n  }\n  hasError(field, error) {\n    var _formControl$errors;\n    const formControl = this.accessoryForm.get(field);\n    return !!(formControl !== null && formControl !== void 0 && formControl.touched) && !!(formControl !== null && formControl !== void 0 && (_formControl$errors = formControl.errors) !== null && _formControl$errors !== void 0 && _formControl$errors[error]);\n  }\n  save() {\n    let {\n      value\n    } = this.accessoryForm;\n    // Garantir que o preço seja number\n    value.price = Number(value.price) || 0;\n    // Garantir que o stock seja number\n    value.stock = Number(value.stock) || 0;\n    console.log('Salvando acessório - Dados do formulário:', value);\n    console.log('Celulares compatíveis selecionados:', value.compatiblePhones);\n    this.accessoryService.save({\n      ...value,\n      id: this.accessoryId\n    }).subscribe({\n      next: () => {\n        this.toastController.create({\n          message: 'Acessório salvo com sucesso!',\n          duration: 3000\n        }).then(toast => toast.present());\n        this.router.navigate(['/accessories']);\n      },\n      error: error => {\n        var _error$error;\n        let errorMessage = 'Erro ao salvar o acessório ' + value.name + '!';\n        if ((_error$error = error.error) !== null && _error$error !== void 0 && _error$error.message) {\n          errorMessage = error.error.message;\n        }\n        alert(errorMessage);\n        console.error(error);\n      }\n    });\n  }\n}\n_AccessoryFormComponent = AccessoryFormComponent;\n_AccessoryFormComponent.ɵfac = function AccessoryFormComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AccessoryFormComponent)(i0.ɵɵdirectiveInject(i1.AccessoryService), i0.ɵɵdirectiveInject(i2.PhoneService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.ToastController));\n};\n_AccessoryFormComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _AccessoryFormComponent,\n  selectors: [[\"app-accessory-form\"]],\n  standalone: false,\n  decls: 55,\n  vars: 24,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [\"defaultHref\", \"/accessories\"], [3, \"fullscreen\"], [1, \"form-container\"], [3, \"formGroup\"], [\"formControlName\", \"name\", \"labelPlacement\", \"floating\", \"label\", \"Nome\", \"type\", \"text\"], [\"formControlName\", \"description\", \"labelPlacement\", \"floating\", \"label\", \"Descri\\u00E7\\u00E3o\", \"rows\", \"3\"], [\"formControlName\", \"price\", \"labelPlacement\", \"floating\", \"label\", \"Pre\\u00E7o (R$)\", \"type\", \"number\", \"step\", \"0.01\", \"min\", \"0\"], [\"formControlName\", \"category\", \"labelPlacement\", \"floating\", \"label\", \"Categoria\"], [3, \"value\"], [\"formControlName\", \"image\", \"labelPlacement\", \"floating\", \"label\", \"Imagem (URL)\", \"type\", \"url\"], [\"formControlName\", \"stock\", \"labelPlacement\", \"floating\", \"label\", \"Estoque\", \"type\", \"number\", \"min\", \"0\"], [\"formControlName\", \"compatiblePhones\", \"label\", \"Celulares Compat\\u00EDveis\", \"labelPlacement\", \"floating\", \"multiple\", \"true\", 3, \"compareWith\"], [\"vertical\", \"bottom\", \"horizontal\", \"end\", \"slot\", \"fixed\"], [3, \"click\", \"disabled\"], [\"name\", \"checkmark\"]],\n  template: function AccessoryFormComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-back-button\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 4)(7, \"div\", 5)(8, \"form\", 6)(9, \"ion-list\")(10, \"ion-item\");\n      i0.ɵɵelement(11, \"ion-input\", 7);\n      i0.ɵɵelementStart(12, \"p\");\n      i0.ɵɵtemplate(13, AccessoryFormComponent_Conditional_13_Template, 1, 0)(14, AccessoryFormComponent_Conditional_14_Template, 1, 0)(15, AccessoryFormComponent_Conditional_15_Template, 1, 0)(16, AccessoryFormComponent_Conditional_16_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(17, \"ion-item\");\n      i0.ɵɵelement(18, \"ion-textarea\", 8);\n      i0.ɵɵelementStart(19, \"p\");\n      i0.ɵɵtemplate(20, AccessoryFormComponent_Conditional_20_Template, 1, 0)(21, AccessoryFormComponent_Conditional_21_Template, 1, 0)(22, AccessoryFormComponent_Conditional_22_Template, 1, 0)(23, AccessoryFormComponent_Conditional_23_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(24, \"ion-item\");\n      i0.ɵɵelement(25, \"ion-input\", 9);\n      i0.ɵɵelementStart(26, \"p\");\n      i0.ɵɵtemplate(27, AccessoryFormComponent_Conditional_27_Template, 1, 0)(28, AccessoryFormComponent_Conditional_28_Template, 1, 0)(29, AccessoryFormComponent_Conditional_29_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(30, \"ion-item\")(31, \"ion-select\", 10);\n      i0.ɵɵrepeaterCreate(32, AccessoryFormComponent_For_33_Template, 2, 2, \"ion-select-option\", 11, i0.ɵɵrepeaterTrackByIdentity);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(34, \"p\");\n      i0.ɵɵtemplate(35, AccessoryFormComponent_Conditional_35_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(36, \"ion-item\");\n      i0.ɵɵelement(37, \"ion-input\", 12);\n      i0.ɵɵelementStart(38, \"p\");\n      i0.ɵɵtemplate(39, AccessoryFormComponent_Conditional_39_Template, 1, 0)(40, AccessoryFormComponent_Conditional_40_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(41, \"ion-item\");\n      i0.ɵɵelement(42, \"ion-input\", 13);\n      i0.ɵɵelementStart(43, \"p\");\n      i0.ɵɵtemplate(44, AccessoryFormComponent_Conditional_44_Template, 1, 0)(45, AccessoryFormComponent_Conditional_45_Template, 1, 0)(46, AccessoryFormComponent_Conditional_46_Template, 1, 0)(47, AccessoryFormComponent_Conditional_47_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(48, \"ion-item\")(49, \"ion-select\", 14);\n      i0.ɵɵrepeaterCreate(50, AccessoryFormComponent_For_51_Template, 2, 2, \"ion-select-option\", 11, _forTrack0);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(52, \"ion-fab\", 15)(53, \"ion-fab-button\", 16);\n      i0.ɵɵlistener(\"click\", function AccessoryFormComponent_Template_ion_fab_button_click_53_listener() {\n        return ctx.save();\n      });\n      i0.ɵɵelement(54, \"ion-icon\", 17);\n      i0.ɵɵelementEnd()()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵtextInterpolate1(\"\", ctx.accessoryId ? \"Editar\" : \"Novo\", \" Acess\\u00F3rio\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.accessoryForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"required\") ? 13 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"minlength\") ? 14 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"maxlength\") ? 15 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"invalidAccessoryName\") ? 16 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"description\", \"required\") ? 20 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"description\", \"minlength\") ? 21 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"description\", \"maxlength\") ? 22 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"description\", \"negativeDescription\") ? 23 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"price\", \"required\") ? 27 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"price\", \"min\") ? 28 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"price\", \"tooExpensive\") ? 29 : -1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵrepeater(ctx.categories);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"category\", \"required\") ? 35 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"image\", \"required\") ? 39 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"image\", \"invalidUrl\") ? 40 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"stock\", \"required\") ? 44 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"stock\", \"min\") ? 45 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"stock\", \"pattern\") ? 46 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"stock\", \"excessiveStock\") ? 47 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"compareWith\", ctx.compareWith);\n      i0.ɵɵadvance();\n      i0.ɵɵrepeater(ctx.phones);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"disabled\", ctx.accessoryForm.invalid);\n    }\n  },\n  dependencies: [i5.ɵNgNoValidate, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, i4.IonButtons, i4.IonContent, i4.IonFab, i4.IonFabButton, i4.IonHeader, i4.IonIcon, i4.IonInput, i4.IonItem, i4.IonList, i4.IonSelect, i4.IonSelectOption, i4.IonTextarea, i4.IonTitle, i4.IonToolbar, i4.NumericValueAccessor, i4.SelectValueAccessor, i4.TextValueAccessor, i4.IonBackButton, i4.IonMinValidator],\n  styles: [\".form-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\nion-item[_ngcontent-%COMP%] {\\n  --padding-start: 0;\\n  margin-bottom: 8px;\\n}\\nion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--ion-color-danger);\\n  padding-left: 16px;\\n  margin: 4px 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWNjZXNzb3JpZXMvYWNjZXNzb3J5LWZvcm0vYWNjZXNzb3J5LWZvcm0uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxrQkFBQTtFQUNBLGtCQUFBO0FBQ0Y7QUFDRTtFQUNFLGVBQUE7RUFDQSw4QkFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsiLmZvcm0tY29udGFpbmVyIHtcclxuICBwYWRkaW5nOiAxNnB4O1xyXG4gIG1heC13aWR0aDogODAwcHg7XHJcbiAgbWFyZ2luOiAwIGF1dG87XHJcbn1cclxuXHJcbmlvbi1pdGVtIHtcclxuICAtLXBhZGRpbmctc3RhcnQ6IDA7XHJcbiAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gIFxyXG4gIHAge1xyXG4gICAgZm9udC1zaXplOiAxMnB4O1xyXG4gICAgY29sb3I6IHZhcigtLWlvbi1jb2xvci1kYW5nZXIpO1xyXG4gICAgcGFkZGluZy1sZWZ0OiAxNnB4O1xyXG4gICAgbWFyZ2luOiA0cHggMDtcclxuICB9XHJcbn1cclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuXHJcblxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n});", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "ApplicationValidators", "i0", "ɵɵtext", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵproperty", "category_r1", "ɵɵadvance", "ɵɵtextInterpolate", "phone_r2", "model", "AccessoryFormComponent", "constructor", "accessoryService", "phoneService", "router", "activatedRoute", "toastController", "accessoryForm", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "accessoryNameValidator", "description", "descriptionValidator", "price", "min", "accessoryPriceValidator", "category", "image", "urlValidator", "compatiblePhones", "stock", "pattern", "accessoryStockValidator", "phones", "categories", "ngOnInit", "loadPhones", "accessoryId", "snapshot", "params", "getById", "subscribe", "next", "accessory", "priceValue", "parseFloat", "console", "log", "patchValue", "error", "alert", "getList", "compareWith", "o1", "o2", "id", "<PERSON><PERSON><PERSON><PERSON>", "field", "_formControl$errors", "formControl", "get", "touched", "errors", "save", "value", "Number", "create", "message", "duration", "then", "toast", "present", "navigate", "_error$error", "errorMessage", "ɵɵdirectiveInject", "i1", "AccessoryService", "i2", "PhoneService", "i3", "Router", "ActivatedRoute", "i4", "ToastController", "selectors", "standalone", "decls", "vars", "consts", "template", "AccessoryFormComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "AccessoryFormComponent_Conditional_13_Template", "AccessoryFormComponent_Conditional_14_Template", "AccessoryFormComponent_Conditional_15_Template", "AccessoryFormComponent_Conditional_16_Template", "AccessoryFormComponent_Conditional_20_Template", "AccessoryFormComponent_Conditional_21_Template", "AccessoryFormComponent_Conditional_22_Template", "AccessoryFormComponent_Conditional_23_Template", "AccessoryFormComponent_Conditional_27_Template", "AccessoryFormComponent_Conditional_28_Template", "AccessoryFormComponent_Conditional_29_Template", "ɵɵrepeaterCreate", "AccessoryFormComponent_For_33_Template", "ɵɵrepeaterTrackByIdentity", "AccessoryFormComponent_Conditional_35_Template", "AccessoryFormComponent_Conditional_39_Template", "AccessoryFormComponent_Conditional_40_Template", "AccessoryFormComponent_Conditional_44_Template", "AccessoryFormComponent_Conditional_45_Template", "AccessoryFormComponent_Conditional_46_Template", "AccessoryFormComponent_Conditional_47_Template", "AccessoryFormComponent_For_51_Template", "_forTrack0", "ɵɵlistener", "AccessoryFormComponent_Template_ion_fab_button_click_53_listener", "ɵɵtextInterpolate1", "ɵɵconditional", "ɵɵrepeater", "invalid"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\accessories\\accessory-form\\accessory-form.component.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\accessories\\accessory-form\\accessory-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ToastController } from '@ionic/angular';\r\nimport { AccessoryService } from '../services/accessory.service';\r\nimport { PhoneService } from '../../phones/services/phone.service';\r\nimport { Phone } from '../../phones/models/phone.type';\r\nimport { ApplicationValidators } from '../../core/validators/url.validator';\r\n\r\n\r\n@Component({\r\n  selector: 'app-accessory-form',\r\n  templateUrl: './accessory-form.component.html',\r\n  styleUrls: ['./accessory-form.component.scss'],\r\n  standalone: false,\r\n})\r\nexport class AccessoryFormComponent implements OnInit {\r\n\r\n  accessoryForm: FormGroup = new FormGroup({\r\n    name: new FormControl('', [\r\n      Validators.required,\r\n      Validators.minLength(3),\r\n      Validators.maxLength(100),\r\n      ApplicationValidators.accessoryNameValidator\r\n    ]),\r\n    description: new FormControl('', [\r\n      Validators.required,\r\n      Validators.minLength(10),\r\n      Validators.maxLength(500),\r\n      ApplicationValidators.descriptionValidator\r\n    ]),\r\n    price: new FormControl(0, [\r\n      Validators.required,\r\n      Validators.min(0),\r\n      ApplicationValidators.accessoryPriceValidator\r\n    ]),\r\n    category: new FormControl('', Validators.required),\r\n    image: new FormControl('', [\r\n      Validators.required,\r\n      ApplicationValidators.urlValidator\r\n    ]),\r\n    compatiblePhones: new FormControl([]),\r\n    stock: new FormControl(0, [\r\n      Validators.required,\r\n      Validators.min(0),\r\n      Validators.pattern('^[0-9]*$'),\r\n      ApplicationValidators.accessoryStockValidator\r\n    ])\r\n  });\r\n\r\n  accessoryId!: number;\r\n  phones: Phone[] = [];\r\n  categories: string[] = ['Capa', 'Carregador', 'Fone de Ouvido', 'Película', 'Bateria Externa', 'Outro'];\r\n\r\n  constructor(\r\n    private accessoryService: AccessoryService,\r\n    private phoneService: PhoneService,\r\n    private router: Router,\r\n    private activatedRoute: ActivatedRoute,\r\n    private toastController: ToastController\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.loadPhones();\r\n    \r\n    const accessoryId = this.activatedRoute.snapshot.params['id'];\r\n    if (accessoryId) {\r\n      this.accessoryService.getById(+accessoryId).subscribe({\r\n        next: (accessory) => {\r\n          if (accessory) {\r\n            this.accessoryId = +accessoryId;\r\n\r\n            // Tratar preço como number\r\n            let priceValue = 0;\r\n            if (accessory.price) {\r\n              if (typeof accessory.price === 'number') {\r\n                priceValue = accessory.price;\r\n              } else if (typeof accessory.price === 'string') {\r\n                priceValue = parseFloat(accessory.price) || 0;\r\n              }\r\n            }\r\n\r\n            console.log('Carregando acessório:', accessory);\r\n            console.log('Celulares compatíveis do acessório:', accessory.compatiblePhones);\r\n\r\n            this.accessoryForm.patchValue({\r\n              name: accessory.name,\r\n              description: accessory.description,\r\n              price: priceValue,\r\n              category: accessory.category,\r\n              image: accessory.image,\r\n              compatiblePhones: accessory.compatiblePhones || [],\r\n              stock: accessory.stock\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          alert('Erro ao carregar o acessório com id ' + accessoryId);\r\n          console.error(error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  loadPhones() {\r\n    this.phoneService.getList().subscribe({\r\n      next: (phones) => {\r\n        this.phones = phones;\r\n      },\r\n      error: (error) => {\r\n        alert('Erro ao carregar lista de celulares');\r\n        console.error(error);\r\n      }\r\n    });\r\n  }\r\n\r\n  compareWith(o1: Phone, o2: Phone): boolean {\r\n    return o1 && o2 ? o1.id === o2.id : o1 === o2;\r\n  }\r\n\r\n  hasError(field: string, error: string): boolean {\r\n    const formControl = this.accessoryForm.get(field);\r\n    return !!formControl?.touched && !!formControl?.errors?.[error];\r\n  }\r\n\r\n  save() {\r\n    let { value } = this.accessoryForm;\r\n\r\n    // Garantir que o preço seja number\r\n    value.price = Number(value.price) || 0;\r\n    // Garantir que o stock seja number\r\n    value.stock = Number(value.stock) || 0;\r\n\r\n    console.log('Salvando acessório - Dados do formulário:', value);\r\n    console.log('Celulares compatíveis selecionados:', value.compatiblePhones);\r\n\r\n    this.accessoryService.save({\r\n      ...value,\r\n      id: this.accessoryId\r\n    }).subscribe({\r\n      next: () => {\r\n        this.toastController.create({\r\n          message: 'Acessório salvo com sucesso!',\r\n          duration: 3000,\r\n        }).then(toast => toast.present());\r\n        this.router.navigate(['/accessories']);\r\n      },\r\n      error: (error) => {\r\n        let errorMessage = 'Erro ao salvar o acessório ' + value.name + '!';\r\n        if (error.error?.message) {\r\n          errorMessage = error.error.message;\r\n        }\r\n        alert(errorMessage);\r\n        console.error(error);\r\n      }\r\n    });\r\n  }\r\n\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-back-button defaultHref=\"/accessories\"></ion-back-button>\r\n    </ion-buttons>\r\n    <ion-title>{{ accessoryId ? 'Editar' : 'Novo' }} Acessório</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div class=\"form-container\">\r\n    <form [formGroup]=\"accessoryForm\">\r\n      <ion-list>\r\n        <ion-item>\r\n          <ion-input formControlName=\"name\" labelPlacement=\"floating\" label=\"Nome\" type=\"text\"></ion-input>\r\n          <p>\r\n            @if(hasError('name', 'required')) {\r\n              O campo é obrigatório\r\n            }\r\n            @if(hasError('name', 'minlength')) {\r\n              O campo deve ter no mínimo 3 caracteres\r\n            }\r\n            @if(hasError('name', 'maxlength')) {\r\n              O campo deve ter no máximo 100 caracteres\r\n            }\r\n            @if(hasError('name', 'invalidAccessoryName')) {\r\n              Nome deve conter uma palavra identificadora (case, capa, carregador, fone, película, suporte)\r\n            }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-textarea formControlName=\"description\" labelPlacement=\"floating\" label=\"Descrição\" rows=\"3\"></ion-textarea>\r\n          <p>\r\n            @if(hasError('description', 'required')) {\r\n              O campo é obrigatório\r\n            }\r\n            @if(hasError('description', 'minlength')) {\r\n              O campo deve ter no mínimo 10 caracteres\r\n            }\r\n            @if(hasError('description', 'maxlength')) {\r\n              O campo deve ter no máximo 500 caracteres\r\n            }\r\n            @if(hasError('description', 'negativeDescription')) {\r\n              Descrição não pode conter palavras negativas\r\n            }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"price\" labelPlacement=\"floating\" label=\"Preço (R$)\" type=\"number\" step=\"0.01\" min=\"0\"></ion-input>\r\n          <p>\r\n            @if(hasError('price', 'required')) {\r\n              O campo é obrigatório\r\n            }\r\n            @if(hasError('price', 'min')) {\r\n              O preço deve ser maior ou igual a zero\r\n            }\r\n            @if(hasError('price', 'tooExpensive')) {\r\n              Preço não pode ser superior a R$ 5.000\r\n            }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-select formControlName=\"category\" labelPlacement=\"floating\" label=\"Categoria\">\r\n            @for(category of categories; track category) {\r\n              <ion-select-option [value]=\"category\">{{ category }}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n            @if(hasError('category', 'required')) {\r\n              O campo é obrigatório\r\n            }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"image\" labelPlacement=\"floating\" label=\"Imagem (URL)\" type=\"url\"></ion-input>\r\n          <p>\r\n            @if(hasError('image', 'required')) {\r\n              O campo é obrigatório\r\n            }\r\n            @if(hasError('image', 'invalidUrl')) {\r\n              O campo de imagem deve ser uma URL válida\r\n            }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"stock\" labelPlacement=\"floating\" label=\"Estoque\" type=\"number\" min=\"0\"></ion-input>\r\n          <p>\r\n            @if(hasError('stock', 'required')) {\r\n              O campo é obrigatório\r\n            }\r\n            @if(hasError('stock', 'min')) {\r\n              O estoque deve ser maior ou igual a zero\r\n            }\r\n            @if(hasError('stock', 'pattern')) {\r\n              O estoque deve ser um número inteiro\r\n            }\r\n            @if(hasError('stock', 'excessiveStock')) {\r\n              Estoque não pode ser superior a 10.000 unidades\r\n            }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-select formControlName=\"compatiblePhones\" [compareWith]=\"compareWith\" label=\"Celulares Compatíveis\" \r\n            labelPlacement=\"floating\" multiple=\"true\">\r\n            @for(phone of phones; track phone.id) {\r\n              <ion-select-option [value]=\"phone\">{{phone.model}}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n        </ion-item>\r\n      </ion-list>\r\n      \r\n      <ion-fab vertical=\"bottom\" horizontal=\"end\" slot=\"fixed\">\r\n        <ion-fab-button [disabled]=\"accessoryForm.invalid\" (click)=\"save()\">\r\n          <ion-icon name=\"checkmark\"></ion-icon>\r\n        </ion-fab-button>\r\n      </ion-fab>\r\n    </form>\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAMnE,SAASC,qBAAqB,QAAQ,qCAAqC;;;;;;;;;;ICU7DC,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,qDACF;;;;;IAEED,EAAA,CAAAC,MAAA,uDACF;;;;;IAEED,EAAA,CAAAC,MAAA,2GACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,sDACF;;;;;IAEED,EAAA,CAAAC,MAAA,uDACF;;;;;IAEED,EAAA,CAAAC,MAAA,oEACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,oDACF;;;;;IAEED,EAAA,CAAAC,MAAA,yDACF;;;;;IAOED,EAAA,CAAAE,cAAA,4BAAsC;IAAAF,EAAA,CAAAC,MAAA,GAAc;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAArDH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAkB;IAACL,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,iBAAA,CAAAF,WAAA,CAAc;;;;;IAKpDL,EAAA,CAAAC,MAAA,wCACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,uDACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,iDACF;;;;;IAEED,EAAA,CAAAC,MAAA,kDACF;;;;;IAEED,EAAA,CAAAC,MAAA,6DACF;;;;;IAQED,EAAA,CAAAE,cAAA,4BAAmC;IAAAF,EAAA,CAAAC,MAAA,GAAe;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAAnDH,EAAA,CAAAI,UAAA,UAAAI,QAAA,CAAe;IAACR,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAC,QAAA,CAAAC,KAAA,CAAe;;;AD/FhE,OAAM,MAAOC,sBAAsB;EAsCjCC,YACUC,gBAAkC,EAClCC,YAA0B,EAC1BC,MAAc,EACdC,cAA8B,EAC9BC,eAAgC;IAJhC,KAAAJ,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IAzCzB,KAAAC,aAAa,GAAc,IAAIpB,SAAS,CAAC;MACvCqB,IAAI,EAAE,IAAItB,WAAW,CAAC,EAAE,EAAE,CACxBE,UAAU,CAACqB,QAAQ,EACnBrB,UAAU,CAACsB,SAAS,CAAC,CAAC,CAAC,EACvBtB,UAAU,CAACuB,SAAS,CAAC,GAAG,CAAC,EACzBtB,qBAAqB,CAACuB,sBAAsB,CAC7C,CAAC;MACFC,WAAW,EAAE,IAAI3B,WAAW,CAAC,EAAE,EAAE,CAC/BE,UAAU,CAACqB,QAAQ,EACnBrB,UAAU,CAACsB,SAAS,CAAC,EAAE,CAAC,EACxBtB,UAAU,CAACuB,SAAS,CAAC,GAAG,CAAC,EACzBtB,qBAAqB,CAACyB,oBAAoB,CAC3C,CAAC;MACFC,KAAK,EAAE,IAAI7B,WAAW,CAAC,CAAC,EAAE,CACxBE,UAAU,CAACqB,QAAQ,EACnBrB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,EACjB3B,qBAAqB,CAAC4B,uBAAuB,CAC9C,CAAC;MACFC,QAAQ,EAAE,IAAIhC,WAAW,CAAC,EAAE,EAAEE,UAAU,CAACqB,QAAQ,CAAC;MAClDU,KAAK,EAAE,IAAIjC,WAAW,CAAC,EAAE,EAAE,CACzBE,UAAU,CAACqB,QAAQ,EACnBpB,qBAAqB,CAAC+B,YAAY,CACnC,CAAC;MACFC,gBAAgB,EAAE,IAAInC,WAAW,CAAC,EAAE,CAAC;MACrCoC,KAAK,EAAE,IAAIpC,WAAW,CAAC,CAAC,EAAE,CACxBE,UAAU,CAACqB,QAAQ,EACnBrB,UAAU,CAAC4B,GAAG,CAAC,CAAC,CAAC,EACjB5B,UAAU,CAACmC,OAAO,CAAC,UAAU,CAAC,EAC9BlC,qBAAqB,CAACmC,uBAAuB,CAC9C;KACF,CAAC;IAGF,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAC,UAAU,GAAa,CAAC,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,UAAU,EAAE,iBAAiB,EAAE,OAAO,CAAC;EAQnG;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;IAEjB,MAAMC,WAAW,GAAG,IAAI,CAACxB,cAAc,CAACyB,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;IAC7D,IAAIF,WAAW,EAAE;MACf,IAAI,CAAC3B,gBAAgB,CAAC8B,OAAO,CAAC,CAACH,WAAW,CAAC,CAACI,SAAS,CAAC;QACpDC,IAAI,EAAGC,SAAS,IAAI;UAClB,IAAIA,SAAS,EAAE;YACb,IAAI,CAACN,WAAW,GAAG,CAACA,WAAW;YAE/B;YACA,IAAIO,UAAU,GAAG,CAAC;YAClB,IAAID,SAAS,CAACpB,KAAK,EAAE;cACnB,IAAI,OAAOoB,SAAS,CAACpB,KAAK,KAAK,QAAQ,EAAE;gBACvCqB,UAAU,GAAGD,SAAS,CAACpB,KAAK;cAC9B,CAAC,MAAM,IAAI,OAAOoB,SAAS,CAACpB,KAAK,KAAK,QAAQ,EAAE;gBAC9CqB,UAAU,GAAGC,UAAU,CAACF,SAAS,CAACpB,KAAK,CAAC,IAAI,CAAC;cAC/C;YACF;YAEAuB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEJ,SAAS,CAAC;YAC/CG,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEJ,SAAS,CAACd,gBAAgB,CAAC;YAE9E,IAAI,CAACd,aAAa,CAACiC,UAAU,CAAC;cAC5BhC,IAAI,EAAE2B,SAAS,CAAC3B,IAAI;cACpBK,WAAW,EAAEsB,SAAS,CAACtB,WAAW;cAClCE,KAAK,EAAEqB,UAAU;cACjBlB,QAAQ,EAAEiB,SAAS,CAACjB,QAAQ;cAC5BC,KAAK,EAAEgB,SAAS,CAAChB,KAAK;cACtBE,gBAAgB,EAAEc,SAAS,CAACd,gBAAgB,IAAI,EAAE;cAClDC,KAAK,EAAEa,SAAS,CAACb;aAClB,CAAC;UACJ;QACF,CAAC;QACDmB,KAAK,EAAGA,KAAK,IAAI;UACfC,KAAK,CAAC,sCAAsC,GAAGb,WAAW,CAAC;UAC3DS,OAAO,CAACG,KAAK,CAACA,KAAK,CAAC;QACtB;OACD,CAAC;IACJ;EACF;EAEAb,UAAUA,CAAA;IACR,IAAI,CAACzB,YAAY,CAACwC,OAAO,EAAE,CAACV,SAAS,CAAC;MACpCC,IAAI,EAAGT,MAAM,IAAI;QACf,IAAI,CAACA,MAAM,GAAGA,MAAM;MACtB,CAAC;MACDgB,KAAK,EAAGA,KAAK,IAAI;QACfC,KAAK,CAAC,qCAAqC,CAAC;QAC5CJ,OAAO,CAACG,KAAK,CAACA,KAAK,CAAC;MACtB;KACD,CAAC;EACJ;EAEAG,WAAWA,CAACC,EAAS,EAAEC,EAAS;IAC9B,OAAOD,EAAE,IAAIC,EAAE,GAAGD,EAAE,CAACE,EAAE,KAAKD,EAAE,CAACC,EAAE,GAAGF,EAAE,KAAKC,EAAE;EAC/C;EAEAE,QAAQA,CAACC,KAAa,EAAER,KAAa;IAAA,IAAAS,mBAAA;IACnC,MAAMC,WAAW,GAAG,IAAI,CAAC5C,aAAa,CAAC6C,GAAG,CAACH,KAAK,CAAC;IACjD,OAAO,CAAC,EAACE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEE,OAAO,KAAI,CAAC,EAACF,WAAW,aAAXA,WAAW,gBAAAD,mBAAA,GAAXC,WAAW,CAAEG,MAAM,cAAAJ,mBAAA,eAAnBA,mBAAA,CAAsBT,KAAK,CAAC;EACjE;EAEAc,IAAIA,CAAA;IACF,IAAI;MAAEC;IAAK,CAAE,GAAG,IAAI,CAACjD,aAAa;IAElC;IACAiD,KAAK,CAACzC,KAAK,GAAG0C,MAAM,CAACD,KAAK,CAACzC,KAAK,CAAC,IAAI,CAAC;IACtC;IACAyC,KAAK,CAAClC,KAAK,GAAGmC,MAAM,CAACD,KAAK,CAAClC,KAAK,CAAC,IAAI,CAAC;IAEtCgB,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEiB,KAAK,CAAC;IAC/DlB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEiB,KAAK,CAACnC,gBAAgB,CAAC;IAE1E,IAAI,CAACnB,gBAAgB,CAACqD,IAAI,CAAC;MACzB,GAAGC,KAAK;MACRT,EAAE,EAAE,IAAI,CAAClB;KACV,CAAC,CAACI,SAAS,CAAC;MACXC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC5B,eAAe,CAACoD,MAAM,CAAC;UAC1BC,OAAO,EAAE,8BAA8B;UACvCC,QAAQ,EAAE;SACX,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC3D,MAAM,CAAC4D,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;MACxC,CAAC;MACDvB,KAAK,EAAGA,KAAK,IAAI;QAAA,IAAAwB,YAAA;QACf,IAAIC,YAAY,GAAG,6BAA6B,GAAGV,KAAK,CAAChD,IAAI,GAAG,GAAG;QACnE,KAAAyD,YAAA,GAAIxB,KAAK,CAACA,KAAK,cAAAwB,YAAA,eAAXA,YAAA,CAAaN,OAAO,EAAE;UACxBO,YAAY,GAAGzB,KAAK,CAACA,KAAK,CAACkB,OAAO;QACpC;QACAjB,KAAK,CAACwB,YAAY,CAAC;QACnB5B,OAAO,CAACG,KAAK,CAACA,KAAK,CAAC;MACtB;KACD,CAAC;EACJ;;0BA5IWzC,sBAAsB;;mCAAtBA,uBAAsB,EAAAV,EAAA,CAAA6E,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA/E,EAAA,CAAA6E,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAjF,EAAA,CAAA6E,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAnF,EAAA,CAAA6E,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAApF,EAAA,CAAA6E,iBAAA,CAAAQ,EAAA,CAAAC,eAAA;AAAA;;QAAtB5E,uBAAsB;EAAA6E,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCd/B9F,EAFJ,CAAAE,cAAA,oBAAiC,qBACA,qBACH;MACxBF,EAAA,CAAAgG,SAAA,yBAA8D;MAChEhG,EAAA,CAAAG,YAAA,EAAc;MACdH,EAAA,CAAAE,cAAA,gBAAW;MAAAF,EAAA,CAAAC,MAAA,GAA+C;MAE9DD,EAF8D,CAAAG,YAAA,EAAY,EAC1D,EACH;MAMLH,EAJR,CAAAE,cAAA,qBAAiC,aACH,cACQ,eACtB,gBACE;MACRF,EAAA,CAAAgG,SAAA,oBAAiG;MACjGhG,EAAA,CAAAE,cAAA,SAAG;MAUDF,EATA,CAAAiG,UAAA,KAAAC,8CAAA,OAAmC,KAAAC,8CAAA,OAGC,KAAAC,8CAAA,OAGA,KAAAC,8CAAA,OAGW;MAInDrG,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAAgG,SAAA,uBAAgH;MAChHhG,EAAA,CAAAE,cAAA,SAAG;MAUDF,EATA,CAAAiG,UAAA,KAAAK,8CAAA,OAA0C,KAAAC,8CAAA,OAGC,KAAAC,8CAAA,OAGA,KAAAC,8CAAA,OAGU;MAIzDzG,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAAgG,SAAA,oBAA8H;MAC9HhG,EAAA,CAAAE,cAAA,SAAG;MAODF,EANA,CAAAiG,UAAA,KAAAS,8CAAA,OAAoC,KAAAC,8CAAA,OAGL,KAAAC,8CAAA,OAGS;MAI5C5G,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,sBAC2E;MACjFF,EAAA,CAAA6G,gBAAA,KAAAC,sCAAA,iCAAA9G,EAAA,CAAA+G,yBAAA,CAEC;MACH/G,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACDF,EAAA,CAAAiG,UAAA,KAAAe,8CAAA,OAAuC;MAI3ChH,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAAgG,SAAA,qBAAyG;MACzGhG,EAAA,CAAAE,cAAA,SAAG;MAIDF,EAHA,CAAAiG,UAAA,KAAAgB,8CAAA,OAAoC,KAAAC,8CAAA,OAGE;MAI1ClH,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAAgG,SAAA,qBAA+G;MAC/GhG,EAAA,CAAAE,cAAA,SAAG;MAUDF,EATA,CAAAiG,UAAA,KAAAkB,8CAAA,OAAoC,KAAAC,8CAAA,OAGL,KAAAC,8CAAA,OAGI,KAAAC,8CAAA,OAGO;MAI9CtH,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,sBAEoC;MAC1CF,EAAA,CAAA6G,gBAAA,KAAAU,sCAAA,iCAAAC,UAAA,CAEC;MAGPxH,EAFI,CAAAG,YAAA,EAAa,EACJ,EACF;MAGTH,EADF,CAAAE,cAAA,mBAAyD,0BACa;MAAjBF,EAAA,CAAAyH,UAAA,mBAAAC,iEAAA;QAAA,OAAS3B,GAAA,CAAA9B,IAAA,EAAM;MAAA,EAAC;MACjEjE,EAAA,CAAAgG,SAAA,oBAAsC;MAKhDhG,EAJQ,CAAAG,YAAA,EAAiB,EACT,EACL,EACH,EACM;;;MA5HFH,EAAA,CAAAI,UAAA,qBAAoB;MAKjBJ,EAAA,CAAAM,SAAA,GAA+C;MAA/CN,EAAA,CAAA2H,kBAAA,KAAA5B,GAAA,CAAAxD,WAAA,wCAA+C;MAIjDvC,EAAA,CAAAM,SAAA,EAAmB;MAAnBN,EAAA,CAAAI,UAAA,oBAAmB;MAEtBJ,EAAA,CAAAM,SAAA,GAA2B;MAA3BN,EAAA,CAAAI,UAAA,cAAA2F,GAAA,CAAA9E,aAAA,CAA2B;MAKzBjB,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,+BAEC;MACD1D,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,gCAEC;MACD1D,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,gCAEC;MACD1D,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,2CAEC;MAOD1D,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,sCAEC;MACD1D,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,uCAEC;MACD1D,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,uCAEC;MACD1D,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,iDAEC;MAOD1D,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,gCAEC;MACD1D,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,2BAEC;MACD1D,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,oCAEC;MAMD1D,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA6H,UAAA,CAAA9B,GAAA,CAAA3D,UAAA,CAEC;MAGDpC,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,mCAEC;MAOD1D,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,gCAEC;MACD1D,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,kCAEC;MAOD1D,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,gCAEC;MACD1D,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,2BAEC;MACD1D,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,+BAEC;MACD1D,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA4H,aAAA,CAAA7B,GAAA,CAAArC,QAAA,sCAEC;MAK4C1D,EAAA,CAAAM,SAAA,GAA2B;MAA3BN,EAAA,CAAAI,UAAA,gBAAA2F,GAAA,CAAAzC,WAAA,CAA2B;MAExEtD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAA6H,UAAA,CAAA9B,GAAA,CAAA5D,MAAA,CAEC;MAMWnC,EAAA,CAAAM,SAAA,GAAkC;MAAlCN,EAAA,CAAAI,UAAA,aAAA2F,GAAA,CAAA9E,aAAA,CAAA6G,OAAA,CAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}