<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/accessories"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ accessoryId ? 'Editar' : 'Novo' }} Acessório</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="form-container">
    <form [formGroup]="accessoryForm">
      <ion-list>
        <ion-item>
          <ion-input formControlName="name" labelPlacement="floating" label="Nome" type="text"></ion-input>
          <p>
            @if(hasError('name', 'required')) {
              O campo é obrigatório
            }
            @if(hasError('name', 'minlength')) {
              O campo deve ter no mínimo 3 caracteres
            }
            @if(hasError('name', 'maxlength')) {
              O campo deve ter no máximo 100 caracteres
            }
            @if(hasError('name', 'invalidAccessoryName')) {
              Nome deve conter uma palavra identificadora (case, capa, carregador, fone, película, suporte)
            }
          </p>
        </ion-item>
        
        <ion-item>
          <ion-textarea formControlName="description" labelPlacement="floating" label="Descrição" rows="3"></ion-textarea>
          <p>
            @if(hasError('description', 'required')) {
              O campo é obrigatório
            }
            @if(hasError('description', 'minlength')) {
              O campo deve ter no mínimo 10 caracteres
            }
            @if(hasError('description', 'maxlength')) {
              O campo deve ter no máximo 500 caracteres
            }
            @if(hasError('description', 'negativeDescription')) {
              Descrição não pode conter palavras negativas
            }
          </p>
        </ion-item>
        
        <ion-item>
          <ion-input formControlName="price" labelPlacement="floating" label="Preço (R$)" type="number" step="0.01" min="0"></ion-input>
          <p>
            @if(hasError('price', 'required')) {
              O campo é obrigatório
            }
            @if(hasError('price', 'min')) {
              O preço deve ser maior ou igual a zero
            }
            @if(hasError('price', 'tooExpensive')) {
              Preço não pode ser superior a R$ 5.000
            }
          </p>
        </ion-item>
        
        <ion-item>
          <ion-select formControlName="category" labelPlacement="floating" label="Categoria">
            @for(category of categories; track category) {
              <ion-select-option [value]="category">{{ category }}</ion-select-option>
            }
          </ion-select>
          <p>
            @if(hasError('category', 'required')) {
              O campo é obrigatório
            }
          </p>
        </ion-item>
        
        <ion-item>
          <ion-input formControlName="image" labelPlacement="floating" label="Imagem (URL)" type="url"></ion-input>
          <p>
            @if(hasError('image', 'required')) {
              O campo é obrigatório
            }
            @if(hasError('image', 'invalidUrl')) {
              O campo de imagem deve ser uma URL válida
            }
          </p>
        </ion-item>
        
        <ion-item>
          <ion-input formControlName="stock" labelPlacement="floating" label="Estoque" type="number" min="0"></ion-input>
          <p>
            @if(hasError('stock', 'required')) {
              O campo é obrigatório
            }
            @if(hasError('stock', 'min')) {
              O estoque deve ser maior ou igual a zero
            }
            @if(hasError('stock', 'pattern')) {
              O estoque deve ser um número inteiro
            }
            @if(hasError('stock', 'excessiveStock')) {
              Estoque não pode ser superior a 10.000 unidades
            }
          </p>
        </ion-item>
        
        <ion-item>
          <ion-select formControlName="compatiblePhones" [compareWith]="compareWith" label="Celulares Compatíveis" 
            labelPlacement="floating" multiple="true">
            @for(phone of phones; track phone.id) {
              <ion-select-option [value]="phone">{{phone.model}}</ion-select-option>
            }
          </ion-select>
        </ion-item>
      </ion-list>
      
      <ion-fab vertical="bottom" horizontal="end" slot="fixed">
        <ion-fab-button [disabled]="accessoryForm.invalid" (click)="save()">
          <ion-icon name="checkmark"></ion-icon>
        </ion-fab-button>
      </ion-fab>
    </form>
  </div>
</ion-content>
