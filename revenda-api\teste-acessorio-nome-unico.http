### 🧪 TESTE ESPECÍFICO: REGRA DE NOME ÚNICO PARA ACESSÓRIOS
### Este arquivo testa especificamente a regra de nome único para acessórios

@baseUrl = http://localhost:3000
@contentType = application/json

### ========================================
### 📝 TESTE: REGRA DE NOME ÚNICO - ACESSÓRIOS
### ========================================

### 1. Criar primeiro acessório (deve funcionar)
POST {{baseUrl}}/accessories
Content-Type: {{contentType}}

{
  "name": "Capa iPhone 15 Pro",
  "description": "Capa de proteção premium para iPhone 15 Pro",
  "price": 89.90,
  "category": "Capa",
  "image": "https://example.com/capa-iphone15.jpg",
  "stock": 50
}

### 2. Tentar criar acessório com nome duplicado (deve falhar)
POST {{baseUrl}}/accessories
Content-Type: {{contentType}}

{
  "name": "Capa iPhone 15 Pro",
  "description": "Outra capa para iPhone 15 Pro",
  "price": 99.90,
  "category": "Capa",
  "image": "https://example.com/capa-iphone15-2.jpg",
  "stock": 30
}

### 3. Criar segundo acessório com nome diferente (deve funcionar)
POST {{baseUrl}}/accessories
Content-Type: {{contentType}}

{
  "name": "Carregador Wireless",
  "description": "Carregador sem fio de alta velocidade",
  "price": 149.90,
  "category": "Carregador",
  "image": "https://example.com/carregador-wireless.jpg",
  "stock": 25
}

### 4. Tentar atualizar primeiro acessório com nome do segundo (deve falhar)
PATCH {{baseUrl}}/accessories/1
Content-Type: {{contentType}}

{
  "name": "Carregador Wireless"
}

### 5. Atualizar primeiro acessório com nome único (deve funcionar)
PATCH {{baseUrl}}/accessories/1
Content-Type: {{contentType}}

{
  "name": "Capa iPhone 15 Pro Max",
  "description": "Capa atualizada para iPhone 15 Pro Max"
}

### 6. Atualizar acessório mantendo o mesmo nome (deve funcionar)
PATCH {{baseUrl}}/accessories/1
Content-Type: {{contentType}}

{
  "description": "Descrição atualizada mantendo o mesmo nome",
  "price": 95.90
}

### 7. Criar terceiro acessório com nome que já foi alterado (deve funcionar)
POST {{baseUrl}}/accessories
Content-Type: {{contentType}}

{
  "name": "Capa iPhone 15 Pro",
  "description": "Agora posso usar este nome pois o primeiro foi alterado",
  "price": 79.90,
  "category": "Capa",
  "image": "https://example.com/capa-iphone15-nova.jpg",
  "stock": 40
}

### ========================================
### 🧹 LIMPEZA: REMOVER ACESSÓRIOS CRIADOS
### ========================================

### Deletar acessórios criados para limpeza
DELETE {{baseUrl}}/accessories/1

###
DELETE {{baseUrl}}/accessories/2

###
DELETE {{baseUrl}}/accessories/3

### ========================================
### 📊 RESULTADOS ESPERADOS
### ========================================

# ✅ DEVEM FUNCIONAR (200/201):
# - POST "Capa iPhone 15 Pro" (primeiro)
# - POST "Carregador Wireless" 
# - PATCH com nome único "Capa iPhone 15 Pro Max"
# - PATCH mantendo mesmo nome (só alterando descrição/preço)
# - POST "Capa iPhone 15 Pro" (após alteração do primeiro)
# - DELETE todos os acessórios

# ❌ DEVEM FALHAR (409 Conflict):
# - POST "Capa iPhone 15 Pro" (duplicado)
#   Mensagem: "Acessório 'Capa iPhone 15 Pro' já existe no sistema"
# - PATCH tentando usar nome "Carregador Wireless" (já existe)
#   Mensagem: "Acessório 'Carregador Wireless' já existe no sistema"

### ========================================
### 🔍 VERIFICAÇÕES ADICIONAIS
### ========================================

### Listar todos os acessórios para verificar estado
GET {{baseUrl}}/accessories

### Buscar acessório específico por ID
GET {{baseUrl}}/accessories/1

### Verificar se acessório foi realmente deletado (deve retornar 404)
GET {{baseUrl}}/accessories/999
