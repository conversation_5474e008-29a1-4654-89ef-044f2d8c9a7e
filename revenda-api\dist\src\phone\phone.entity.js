"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Phone = void 0;
const typeorm_1 = require("typeorm");
let Phone = class Phone {
    id;
    model;
    image;
    releaseDate;
    price;
    category;
    brandId;
    brand;
    accessories;
};
exports.Phone = Phone;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Phone.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 200 }),
    __metadata("design:type", String)
], Phone.prototype, "model", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500 }),
    __metadata("design:type", String)
], Phone.prototype, "image", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], Phone.prototype, "releaseDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Phone.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], Phone.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'brand_id' }),
    __metadata("design:type", Number)
], Phone.prototype, "brandId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)('Brand', (brand) => brand.phones),
    (0, typeorm_1.JoinColumn)({ name: 'brand_id', referencedColumnName: 'id' }),
    __metadata("design:type", Object)
], Phone.prototype, "brand", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)('Accessory', (accessory) => accessory.compatiblePhones),
    __metadata("design:type", Array)
], Phone.prototype, "accessories", void 0);
exports.Phone = Phone = __decorate([
    (0, typeorm_1.Entity)('phones')
], Phone);
//# sourceMappingURL=phone.entity.js.map