{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _MaskitoDirective, _MaskitoPipe, _MaskitoPattern;\nimport * as i0 from '@angular/core';\nimport { inject, ElementRef, NgZone, Directive, Input, Pipe } from '@angular/core';\nimport { DefaultValueAccessor } from '@angular/forms';\nimport { MASKITO_DEFAULT_ELEMENT_PREDICATE, maskitoTransform, Maskito, MASKITO_DEFAULT_OPTIONS } from '@maskito/core';\nclass MaskitoDirective {\n  constructor() {\n    this.elementRef = inject(ElementRef).nativeElement;\n    this.ngZone = inject(NgZone);\n    this.maskedElement = null;\n    this.options = null;\n    this.elementPredicate = MASKITO_DEFAULT_ELEMENT_PREDICATE;\n    const accessor = inject(DefaultValueAccessor, {\n      self: true,\n      optional: true\n    });\n    if (accessor) {\n      const original = accessor.writeValue.bind(accessor);\n      accessor.writeValue = value => {\n        original(this.options ? maskitoTransform(String(value !== null && value !== void 0 ? value : ''), this.options) : value);\n      };\n    }\n  }\n  ngOnChanges() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const {\n        elementPredicate,\n        options,\n        maskedElement,\n        elementRef,\n        ngZone\n      } = _this;\n      maskedElement === null || maskedElement === void 0 || maskedElement.destroy();\n      if (!options) {\n        return;\n      }\n      const predicateResult = yield elementPredicate(elementRef);\n      if (_this.elementPredicate !== elementPredicate || _this.options !== options) {\n        // Ignore the result of the predicate if the\n        // maskito element (or its options) has changed before the predicate was resolved.\n        return;\n      }\n      ngZone.runOutsideAngular(() => {\n        _this.maskedElement = new Maskito(predicateResult, options);\n      });\n    })();\n  }\n  ngOnDestroy() {\n    var _this$maskedElement;\n    (_this$maskedElement = this.maskedElement) === null || _this$maskedElement === void 0 || _this$maskedElement.destroy();\n  }\n}\n_MaskitoDirective = MaskitoDirective;\n_MaskitoDirective.ɵfac = function _MaskitoDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MaskitoDirective)();\n};\n_MaskitoDirective.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MaskitoDirective,\n  selectors: [[\"\", \"maskito\", \"\"]],\n  inputs: {\n    options: [0, \"maskito\", \"options\"],\n    elementPredicate: [0, \"maskitoElement\", \"elementPredicate\"]\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MaskitoDirective, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[maskito]'\n    }]\n  }], function () {\n    return [];\n  }, {\n    options: [{\n      type: Input,\n      args: ['maskito']\n    }],\n    elementPredicate: [{\n      type: Input,\n      args: ['maskitoElement']\n    }]\n  });\n})();\nclass MaskitoPipe {\n  transform(value, maskitoOptions) {\n    return maskitoTransform(String(value !== null && value !== void 0 ? value : ''), maskitoOptions !== null && maskitoOptions !== void 0 ? maskitoOptions : MASKITO_DEFAULT_OPTIONS);\n  }\n}\n_MaskitoPipe = MaskitoPipe;\n_MaskitoPipe.ɵfac = function _MaskitoPipe_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MaskitoPipe)();\n};\n_MaskitoPipe.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n  name: \"maskito\",\n  type: _MaskitoPipe,\n  pure: true\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MaskitoPipe, [{\n    type: Pipe,\n    args: [{\n      standalone: true,\n      name: 'maskito'\n    }]\n  }], null, null);\n})();\nclass MaskitoPattern {\n  constructor() {\n    this.maskitoDirective = inject(MaskitoDirective, {\n      self: true\n    });\n  }\n  set regExpStr(pattern) {\n    this.maskitoDirective.options = {\n      mask: typeof pattern === 'string' ? new RegExp(`^${pattern}$`) : pattern\n    };\n    this.maskitoDirective.ngOnChanges();\n  }\n}\n_MaskitoPattern = MaskitoPattern;\n_MaskitoPattern.ɵfac = function _MaskitoPattern_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _MaskitoPattern)();\n};\n_MaskitoPattern.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _MaskitoPattern,\n  selectors: [[\"\", \"maskitoPattern\", \"\"]],\n  inputs: {\n    regExpStr: [0, \"maskitoPattern\", \"regExpStr\"]\n  },\n  features: [i0.ɵɵHostDirectivesFeature([MaskitoDirective])]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MaskitoPattern, [{\n    type: Directive,\n    args: [{\n      standalone: true,\n      selector: '[maskitoPattern]',\n      hostDirectives: [MaskitoDirective]\n    }]\n  }], null, {\n    regExpStr: [{\n      type: Input,\n      args: ['maskitoPattern']\n    }]\n  });\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MaskitoDirective, MaskitoPattern, MaskitoPipe };", "map": {"version": 3, "names": ["i0", "inject", "ElementRef", "NgZone", "Directive", "Input", "<PERSON><PERSON>", "DefaultValueAccessor", "MASKITO_DEFAULT_ELEMENT_PREDICATE", "maskitoTransform", "<PERSON><PERSON>", "MASKITO_DEFAULT_OPTIONS", "MaskitoDirective", "constructor", "elementRef", "nativeElement", "ngZone", "maskedElement", "options", "elementPredicate", "accessor", "self", "optional", "original", "writeValue", "bind", "value", "String", "ngOnChanges", "_this", "_asyncToGenerator", "destroy", "predicateResult", "runOutsideAngular", "ngOnDestroy", "_this$maskedElement", "_MaskitoDirective", "ɵfac", "_MaskitoDirective_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "features", "ɵɵNgOnChangesFeature", "ngDevMode", "ɵsetClassMetadata", "args", "standalone", "selector", "MaskitoPipe", "transform", "maskitoOptions", "_MaskitoPipe", "_MaskitoPipe_Factory", "ɵpipe", "ɵɵdefinePipe", "name", "pure", "MaskitoPattern", "maskitoDirective", "regExpStr", "pattern", "mask", "RegExp", "_MaskitoPattern", "_MaskitoPattern_Factory", "ɵɵHostDirectivesFeature", "hostDirectives"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@maskito/angular/fesm2022/maskito-angular.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, ElementRef, NgZone, Directive, Input, Pipe } from '@angular/core';\nimport { DefaultValueAccessor } from '@angular/forms';\nimport { MASKITO_DEFAULT_ELEMENT_PREDICATE, maskitoTransform, Maskito, MASKITO_DEFAULT_OPTIONS } from '@maskito/core';\n\nclass MaskitoDirective {\n    constructor() {\n        this.elementRef = inject(ElementRef).nativeElement;\n        this.ngZone = inject(NgZone);\n        this.maskedElement = null;\n        this.options = null;\n        this.elementPredicate = MASKITO_DEFAULT_ELEMENT_PREDICATE;\n        const accessor = inject(DefaultValueAccessor, { self: true, optional: true });\n        if (accessor) {\n            const original = accessor.writeValue.bind(accessor);\n            accessor.writeValue = (value) => {\n                original(this.options\n                    ? maskitoTransform(String(value ?? ''), this.options)\n                    : value);\n            };\n        }\n    }\n    async ngOnChanges() {\n        const { elementPredicate, options, maskedElement, elementRef, ngZone } = this;\n        maskedElement?.destroy();\n        if (!options) {\n            return;\n        }\n        const predicateResult = await elementPredicate(elementRef);\n        if (this.elementPredicate !== elementPredicate || this.options !== options) {\n            // Ignore the result of the predicate if the\n            // maskito element (or its options) has changed before the predicate was resolved.\n            return;\n        }\n        ngZone.runOutsideAngular(() => {\n            this.maskedElement = new Maskito(predicateResult, options);\n        });\n    }\n    ngOnDestroy() {\n        this.maskedElement?.destroy();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: MaskitoDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: MaskitoDirective, isStandalone: true, selector: \"[maskito]\", inputs: { options: [\"maskito\", \"options\"], elementPredicate: [\"maskitoElement\", \"elementPredicate\"] }, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: MaskitoDirective, decorators: [{\n            type: Directive,\n            args: [{ standalone: true, selector: '[maskito]' }]\n        }], ctorParameters: function () { return []; }, propDecorators: { options: [{\n                type: Input,\n                args: ['maskito']\n            }], elementPredicate: [{\n                type: Input,\n                args: ['maskitoElement']\n            }] } });\n\nclass MaskitoPipe {\n    transform(value, maskitoOptions) {\n        return maskitoTransform(String(value ?? ''), maskitoOptions ?? MASKITO_DEFAULT_OPTIONS);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: MaskitoPipe, deps: [], target: i0.ɵɵFactoryTarget.Pipe }); }\n    static { this.ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: MaskitoPipe, isStandalone: true, name: \"maskito\" }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: MaskitoPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    standalone: true,\n                    name: 'maskito',\n                }]\n        }] });\n\nclass MaskitoPattern {\n    constructor() {\n        this.maskitoDirective = inject(MaskitoDirective, { self: true });\n    }\n    set regExpStr(pattern) {\n        this.maskitoDirective.options = {\n            mask: typeof pattern === 'string' ? new RegExp(`^${pattern}$`) : pattern,\n        };\n        this.maskitoDirective.ngOnChanges();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: MaskitoPattern, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: MaskitoPattern, isStandalone: true, selector: \"[maskitoPattern]\", inputs: { regExpStr: [\"maskitoPattern\", \"regExpStr\"] }, hostDirectives: [{ directive: MaskitoDirective }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: MaskitoPattern, decorators: [{\n            type: Directive,\n            args: [{\n                    standalone: true,\n                    selector: '[maskitoPattern]',\n                    hostDirectives: [MaskitoDirective],\n                }]\n        }], propDecorators: { regExpStr: [{\n                type: Input,\n                args: ['maskitoPattern']\n            }] } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MaskitoDirective, MaskitoPattern, MaskitoPipe };\n"], "mappings": ";;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAEC,IAAI,QAAQ,eAAe;AAClF,SAASC,oBAAoB,QAAQ,gBAAgB;AACrD,SAASC,iCAAiC,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,uBAAuB,QAAQ,eAAe;AAErH,MAAMC,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAGb,MAAM,CAACC,UAAU,CAAC,CAACa,aAAa;IAClD,IAAI,CAACC,MAAM,GAAGf,MAAM,CAACE,MAAM,CAAC;IAC5B,IAAI,CAACc,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,gBAAgB,GAAGX,iCAAiC;IACzD,MAAMY,QAAQ,GAAGnB,MAAM,CAACM,oBAAoB,EAAE;MAAEc,IAAI,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC7E,IAAIF,QAAQ,EAAE;MACV,MAAMG,QAAQ,GAAGH,QAAQ,CAACI,UAAU,CAACC,IAAI,CAACL,QAAQ,CAAC;MACnDA,QAAQ,CAACI,UAAU,GAAIE,KAAK,IAAK;QAC7BH,QAAQ,CAAC,IAAI,CAACL,OAAO,GACfT,gBAAgB,CAACkB,MAAM,CAACD,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAE,CAAC,EAAE,IAAI,CAACR,OAAO,CAAC,GACnDQ,KAAK,CAAC;MAChB,CAAC;IACL;EACJ;EACME,WAAWA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChB,MAAM;QAAEX,gBAAgB;QAAED,OAAO;QAAED,aAAa;QAAEH,UAAU;QAAEE;MAAO,CAAC,GAAGa,KAAI;MAC7EZ,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEc,OAAO,CAAC,CAAC;MACxB,IAAI,CAACb,OAAO,EAAE;QACV;MACJ;MACA,MAAMc,eAAe,SAASb,gBAAgB,CAACL,UAAU,CAAC;MAC1D,IAAIe,KAAI,CAACV,gBAAgB,KAAKA,gBAAgB,IAAIU,KAAI,CAACX,OAAO,KAAKA,OAAO,EAAE;QACxE;QACA;QACA;MACJ;MACAF,MAAM,CAACiB,iBAAiB,CAAC,MAAM;QAC3BJ,KAAI,CAACZ,aAAa,GAAG,IAAIP,OAAO,CAACsB,eAAe,EAAEd,OAAO,CAAC;MAC9D,CAAC,CAAC;IAAC;EACP;EACAgB,WAAWA,CAAA,EAAG;IAAA,IAAAC,mBAAA;IACV,CAAAA,mBAAA,OAAI,CAAClB,aAAa,cAAAkB,mBAAA,eAAlBA,mBAAA,CAAoBJ,OAAO,CAAC,CAAC;EACjC;AAGJ;AAACK,iBAAA,GAtCKxB,gBAAgB;AAoCTwB,iBAAA,CAAKC,IAAI,YAAAC,0BAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAAyF3B,iBAAgB;AAAA,CAAmD;AACrKwB,iBAAA,CAAKI,IAAI,kBAE4DxC,EAAE,CAAAyC,iBAAA;EAAAC,IAAA,EAFe9B,iBAAgB;EAAA+B,SAAA;EAAAC,MAAA;IAAA1B,OAAA;IAAAC,gBAAA;EAAA;EAAA0B,QAAA,GAEjC7C,EAAE,CAAA8C,oBAAA;AAAA,EAFuN;AAE3S;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAkF/C,EAAE,CAAAgD,iBAAA,CAAQpC,gBAAgB,EAAc,CAAC;IAC/G8B,IAAI,EAAEtC,SAAS;IACf6C,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAY,CAAC;EACtD,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC,EAAkB;IAAEjC,OAAO,EAAE,CAAC;MACpEwB,IAAI,EAAErC,KAAK;MACX4C,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAE9B,gBAAgB,EAAE,CAAC;MACnBuB,IAAI,EAAErC,KAAK;MACX4C,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMG,WAAW,CAAC;EACdC,SAASA,CAAC3B,KAAK,EAAE4B,cAAc,EAAE;IAC7B,OAAO7C,gBAAgB,CAACkB,MAAM,CAACD,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAE,CAAC,EAAE4B,cAAc,aAAdA,cAAc,cAAdA,cAAc,GAAI3C,uBAAuB,CAAC;EAC3F;AAGJ;AAAC4C,YAAA,GANKH,WAAW;AAIJG,YAAA,CAAKlB,IAAI,YAAAmB,qBAAAjB,iBAAA;EAAA,YAAAA,iBAAA,IAAyFa,YAAW;AAAA,CAA8C;AAC3JG,YAAA,CAAKE,KAAK,kBAhB2DzD,EAAE,CAAA0D,YAAA;EAAAC,IAAA;EAAAjB,IAAA,EAgByBU,YAAW;EAAAQ,IAAA;AAAA,EAAwC;AAEhK;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KAlBkF/C,EAAE,CAAAgD,iBAAA,CAkBQI,WAAW,EAAc,CAAC;IAC1GV,IAAI,EAAEpC,IAAI;IACV2C,IAAI,EAAE,CAAC;MACCC,UAAU,EAAE,IAAI;MAChBS,IAAI,EAAE;IACV,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAME,cAAc,CAAC;EACjBhD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiD,gBAAgB,GAAG7D,MAAM,CAACW,gBAAgB,EAAE;MAAES,IAAI,EAAE;IAAK,CAAC,CAAC;EACpE;EACA,IAAI0C,SAASA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACF,gBAAgB,CAAC5C,OAAO,GAAG;MAC5B+C,IAAI,EAAE,OAAOD,OAAO,KAAK,QAAQ,GAAG,IAAIE,MAAM,CAAC,IAAIF,OAAO,GAAG,CAAC,GAAGA;IACrE,CAAC;IACD,IAAI,CAACF,gBAAgB,CAAClC,WAAW,CAAC,CAAC;EACvC;AAGJ;AAACuC,eAAA,GAZKN,cAAc;AAUPM,eAAA,CAAK9B,IAAI,YAAA+B,wBAAA7B,iBAAA;EAAA,YAAAA,iBAAA,IAAyFsB,eAAc;AAAA,CAAmD;AACnKM,eAAA,CAAK3B,IAAI,kBArC4DxC,EAAE,CAAAyC,iBAAA;EAAAC,IAAA,EAqCemB,eAAc;EAAAlB,SAAA;EAAAC,MAAA;IAAAmB,SAAA;EAAA;EAAAlB,QAAA,GArC/B7C,EAAE,CAAAqE,uBAAA,EAqCuKzD,gBAAgB;AAAA,EAAoB;AAE/R;EAAA,QAAAmC,SAAA,oBAAAA,SAAA,KAvCkF/C,EAAE,CAAAgD,iBAAA,CAuCQa,cAAc,EAAc,CAAC;IAC7GnB,IAAI,EAAEtC,SAAS;IACf6C,IAAI,EAAE,CAAC;MACCC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,kBAAkB;MAC5BmB,cAAc,EAAE,CAAC1D,gBAAgB;IACrC,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEmD,SAAS,EAAE,CAAC;MAC1BrB,IAAI,EAAErC,KAAK;MACX4C,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;;AAEA,SAASrC,gBAAgB,EAAEiD,cAAc,EAAET,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}