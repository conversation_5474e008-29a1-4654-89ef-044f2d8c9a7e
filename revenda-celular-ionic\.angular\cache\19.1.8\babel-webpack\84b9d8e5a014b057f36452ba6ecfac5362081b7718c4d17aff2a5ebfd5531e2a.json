{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _defineProperty from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _DomController, _Platform, _NavController, _Config, _AngularDelegate, _IonPopover, _IonModal, _IonRouterOutlet, _RoutedComponentInputBinder, _IonBackButton, _RouterLinkDelegateDirective, _RouterLinkWithHrefDelegateDirective, _IonNav, _IonTabs, _ValueAccessor;\nconst _c0 = [\"tabsInner\"];\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, InjectionToken, inject, NgZone, ApplicationRef, Injector, createComponent, TemplateRef, Directive, ContentChild, EventEmitter, ViewContainerRef, EnvironmentInjector, Attribute, SkipSelf, Input, Output, reflectComponentType, HostListener, ElementRef, ViewChild } from '@angular/core';\nimport * as i3 from '@angular/router';\nimport { NavigationStart, PRIMARY_OUTLET, ChildrenOutletContexts, ActivatedRoute, Router } from '@angular/router';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport { isPlatform, getPlatforms, LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD, componentOnReady } from '@ionic/core/components';\nimport { Subject, fromEvent, BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { filter, switchMap, distinctUntilChanged } from 'rxjs/operators';\nimport { NgControl } from '@angular/forms';\nclass MenuController {\n  constructor(menuController) {\n    _defineProperty(this, \"menuController\", void 0);\n    this.menuController = menuController;\n  }\n  /**\n   * Programmatically open the Menu.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu is fully opened\n   */\n  open(menuId) {\n    return this.menuController.open(menuId);\n  }\n  /**\n   * Programmatically close the Menu. If no `menuId` is given as the first\n   * argument then it'll close any menu which is open. If a `menuId`\n   * is given then it'll close that exact menu.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu is fully closed\n   */\n  close(menuId) {\n    return this.menuController.close(menuId);\n  }\n  /**\n   * Toggle the menu. If it's closed, it will open, and if opened, it\n   * will close.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return returns a promise when the menu has been toggled\n   */\n  toggle(menuId) {\n    return this.menuController.toggle(menuId);\n  }\n  /**\n   * Used to enable or disable a menu. For example, there could be multiple\n   * left menus, but only one of them should be able to be opened at the same\n   * time. If there are multiple menus on the same side, then enabling one menu\n   * will also automatically disable all the others that are on the same side.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu, which is useful for chaining.\n   */\n  enable(shouldEnable, menuId) {\n    return this.menuController.enable(shouldEnable, menuId);\n  }\n  /**\n   * Used to enable or disable the ability to swipe open the menu.\n   * @param shouldEnable  True if it should be swipe-able, false if not.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu, which is useful for chaining.\n   */\n  swipeGesture(shouldEnable, menuId) {\n    return this.menuController.swipeGesture(shouldEnable, menuId);\n  }\n  /**\n   * @param [menuId] Optionally get the menu by its id, or side.\n   * @return Returns true if the specified menu is currently open, otherwise false.\n   * If the menuId is not specified, it returns true if ANY menu is currenly open.\n   */\n  isOpen(menuId) {\n    return this.menuController.isOpen(menuId);\n  }\n  /**\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns true if the menu is currently enabled, otherwise false.\n   */\n  isEnabled(menuId) {\n    return this.menuController.isEnabled(menuId);\n  }\n  /**\n   * Used to get a menu instance. If a `menuId` is not provided then it'll\n   * return the first menu found. If a `menuId` is `left` or `right`, then\n   * it'll return the enabled menu on that side. Otherwise, if a `menuId` is\n   * provided, then it'll try to find the menu using the menu's `id`\n   * property. If a menu is not found then it'll return `null`.\n   * @param [menuId]  Optionally get the menu by its id, or side.\n   * @return Returns the instance of the menu if found, otherwise `null`.\n   */\n  get(menuId) {\n    return this.menuController.get(menuId);\n  }\n  /**\n   * @return Returns the instance of the menu already opened, otherwise `null`.\n   */\n  getOpen() {\n    return this.menuController.getOpen();\n  }\n  /**\n   * @return Returns an array of all menu instances.\n   */\n  getMenus() {\n    return this.menuController.getMenus();\n  }\n  registerAnimation(name, animation) {\n    return this.menuController.registerAnimation(name, animation);\n  }\n  isAnimating() {\n    return this.menuController.isAnimating();\n  }\n  _getOpenSync() {\n    return this.menuController._getOpenSync();\n  }\n  _createAnimation(type, menuCmp) {\n    return this.menuController._createAnimation(type, menuCmp);\n  }\n  _register(menu) {\n    return this.menuController._register(menu);\n  }\n  _unregister(menu) {\n    return this.menuController._unregister(menu);\n  }\n  _setOpen(menu, shouldOpen, animated) {\n    return this.menuController._setOpen(menu, shouldOpen, animated);\n  }\n}\nclass DomController {\n  /**\n   * Schedules a task to run during the READ phase of the next frame.\n   * This task should only read the DOM, but never modify it.\n   */\n  read(cb) {\n    getQueue().read(cb);\n  }\n  /**\n   * Schedules a task to run during the WRITE phase of the next frame.\n   * This task should write the DOM, but never READ it.\n   */\n  write(cb) {\n    getQueue().write(cb);\n  }\n  /** @nocollapse */\n}\n_DomController = DomController;\n_defineProperty(DomController, \"\\u0275fac\", function _DomController_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _DomController)();\n});\n/** @nocollapse */\n_defineProperty(DomController, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _DomController,\n  factory: _DomController.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomController, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst getQueue = () => {\n  const win = typeof window !== 'undefined' ? window : null;\n  if (win != null) {\n    const Ionic = win.Ionic;\n    if (Ionic !== null && Ionic !== void 0 && Ionic.queue) {\n      return Ionic.queue;\n    }\n    return {\n      read: cb => win.requestAnimationFrame(cb),\n      write: cb => win.requestAnimationFrame(cb)\n    };\n  }\n  return {\n    read: cb => cb(),\n    write: cb => cb()\n  };\n};\nclass Platform {\n  constructor(doc, zone) {\n    _defineProperty(this, \"doc\", void 0);\n    _defineProperty(this, \"_readyPromise\", void 0);\n    _defineProperty(this, \"win\", void 0);\n    /**\n     * @hidden\n     */\n    _defineProperty(this, \"backButton\", new Subject());\n    /**\n     * The keyboardDidShow event emits when the\n     * on-screen keyboard is presented.\n     */\n    _defineProperty(this, \"keyboardDidShow\", new Subject());\n    /**\n     * The keyboardDidHide event emits when the\n     * on-screen keyboard is hidden.\n     */\n    _defineProperty(this, \"keyboardDidHide\", new Subject());\n    /**\n     * The pause event emits when the native platform puts the application\n     * into the background, typically when the user switches to a different\n     * application. This event would emit when a Cordova app is put into\n     * the background, however, it would not fire on a standard web browser.\n     */\n    _defineProperty(this, \"pause\", new Subject());\n    /**\n     * The resume event emits when the native platform pulls the application\n     * out from the background. This event would emit when a Cordova app comes\n     * out from the background, however, it would not fire on a standard web browser.\n     */\n    _defineProperty(this, \"resume\", new Subject());\n    /**\n     * The resize event emits when the browser window has changed dimensions. This\n     * could be from a browser window being physically resized, or from a device\n     * changing orientation.\n     */\n    _defineProperty(this, \"resize\", new Subject());\n    this.doc = doc;\n    zone.run(() => {\n      var _this$win;\n      this.win = doc.defaultView;\n      this.backButton.subscribeWithPriority = function (priority, callback) {\n        return this.subscribe(ev => {\n          return ev.register(priority, processNextHandler => zone.run(() => callback(processNextHandler)));\n        });\n      };\n      proxyEvent(this.pause, doc, 'pause', zone);\n      proxyEvent(this.resume, doc, 'resume', zone);\n      proxyEvent(this.backButton, doc, 'ionBackButton', zone);\n      proxyEvent(this.resize, this.win, 'resize', zone);\n      proxyEvent(this.keyboardDidShow, this.win, 'ionKeyboardDidShow', zone);\n      proxyEvent(this.keyboardDidHide, this.win, 'ionKeyboardDidHide', zone);\n      let readyResolve;\n      this._readyPromise = new Promise(res => {\n        readyResolve = res;\n      });\n      if ((_this$win = this.win) !== null && _this$win !== void 0 && _this$win['cordova']) {\n        doc.addEventListener('deviceready', () => {\n          readyResolve('cordova');\n        }, {\n          once: true\n        });\n      } else {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        readyResolve('dom');\n      }\n    });\n  }\n  /**\n   * @returns returns true/false based on platform.\n   * @description\n   * Depending on the platform the user is on, `is(platformName)` will\n   * return `true` or `false`. Note that the same app can return `true`\n   * for more than one platform name. For example, an app running from\n   * an iPad would return `true` for the platform names: `mobile`,\n   * `ios`, `ipad`, and `tablet`. Additionally, if the app was running\n   * from Cordova then `cordova` would be true, and if it was running\n   * from a web browser on the iPad then `mobileweb` would be `true`.\n   *\n   * ```\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyPage {\n   *   constructor(public platform: Platform) {\n   *     if (this.platform.is('ios')) {\n   *       // This will only print when on iOS\n   *       console.log('I am an iOS device!');\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * | Platform Name   | Description                        |\n   * |-----------------|------------------------------------|\n   * | android         | on a device running Android.       |\n   * | capacitor       | on a device running Capacitor.     |\n   * | cordova         | on a device running Cordova.       |\n   * | ios             | on a device running iOS.           |\n   * | ipad            | on an iPad device.                 |\n   * | iphone          | on an iPhone device.               |\n   * | phablet         | on a phablet device.               |\n   * | tablet          | on a tablet device.                |\n   * | electron        | in Electron on a desktop device.   |\n   * | pwa             | as a PWA app.                      |\n   * | mobile          | on a mobile device.                |\n   * | mobileweb       | on a mobile device in a browser.   |\n   * | desktop         | on a desktop device.               |\n   * | hybrid          | is a cordova or capacitor app.     |\n   *\n   */\n  is(platformName) {\n    return isPlatform(this.win, platformName);\n  }\n  /**\n   * @returns the array of platforms\n   * @description\n   * Depending on what device you are on, `platforms` can return multiple values.\n   * Each possible value is a hierarchy of platforms. For example, on an iPhone,\n   * it would return `mobile`, `ios`, and `iphone`.\n   *\n   * ```\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyPage {\n   *   constructor(public platform: Platform) {\n   *     // This will print an array of the current platforms\n   *     console.log(this.platform.platforms());\n   *   }\n   * }\n   * ```\n   */\n  platforms() {\n    return getPlatforms(this.win);\n  }\n  /**\n   * Returns a promise when the platform is ready and native functionality\n   * can be called. If the app is running from within a web browser, then\n   * the promise will resolve when the DOM is ready. When the app is running\n   * from an application engine such as Cordova, then the promise will\n   * resolve when Cordova triggers the `deviceready` event.\n   *\n   * The resolved value is the `readySource`, which states which platform\n   * ready was used. For example, when Cordova is ready, the resolved ready\n   * source is `cordova`. The default ready source value will be `dom`. The\n   * `readySource` is useful if different logic should run depending on the\n   * platform the app is running from. For example, only Cordova can execute\n   * the status bar plugin, so the web should not run status bar plugin logic.\n   *\n   * ```\n   * import { Component } from '@angular/core';\n   * import { Platform } from 'ionic-angular';\n   *\n   * @Component({...})\n   * export MyApp {\n   *   constructor(public platform: Platform) {\n   *     this.platform.ready().then((readySource) => {\n   *       console.log('Platform ready from', readySource);\n   *       // Platform now ready, execute any required native code\n   *     });\n   *   }\n   * }\n   * ```\n   */\n  ready() {\n    return this._readyPromise;\n  }\n  /**\n   * Returns if this app is using right-to-left language direction or not.\n   * We recommend the app's `index.html` file already has the correct `dir`\n   * attribute value set, such as `<html dir=\"ltr\">` or `<html dir=\"rtl\">`.\n   * [W3C: Structural markup and right-to-left text in HTML](http://www.w3.org/International/questions/qa-html-dir)\n   */\n  get isRTL() {\n    return this.doc.dir === 'rtl';\n  }\n  /**\n   * Get the query string parameter\n   */\n  getQueryParam(key) {\n    return readQueryParam(this.win.location.href, key);\n  }\n  /**\n   * Returns `true` if the app is in landscape mode.\n   */\n  isLandscape() {\n    return !this.isPortrait();\n  }\n  /**\n   * Returns `true` if the app is in portrait mode.\n   */\n  isPortrait() {\n    var _this$win$matchMedia, _this$win2;\n    return (_this$win$matchMedia = (_this$win2 = this.win).matchMedia) === null || _this$win$matchMedia === void 0 ? void 0 : _this$win$matchMedia.call(_this$win2, '(orientation: portrait)').matches;\n  }\n  testUserAgent(expression) {\n    const nav = this.win.navigator;\n    return !!(nav !== null && nav !== void 0 && nav.userAgent && nav.userAgent.indexOf(expression) >= 0);\n  }\n  /**\n   * Get the current url.\n   */\n  url() {\n    return this.win.location.href;\n  }\n  /**\n   * Gets the width of the platform's viewport using `window.innerWidth`.\n   */\n  width() {\n    return this.win.innerWidth;\n  }\n  /**\n   * Gets the height of the platform's viewport using `window.innerHeight`.\n   */\n  height() {\n    return this.win.innerHeight;\n  }\n  /** @nocollapse */\n}\n_Platform = Platform;\n_defineProperty(Platform, \"\\u0275fac\", function _Platform_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _Platform)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone));\n});\n/** @nocollapse */\n_defineProperty(Platform, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _Platform,\n  factory: _Platform.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\nconst readQueryParam = (url, key) => {\n  key = key.replace(/[[\\]\\\\]/g, '\\\\$&');\n  const regex = new RegExp('[\\\\?&]' + key + '=([^&#]*)');\n  const results = regex.exec(url);\n  return results ? decodeURIComponent(results[1].replace(/\\+/g, ' ')) : null;\n};\nconst proxyEvent = (emitter, el, eventName, zone) => {\n  if (el) {\n    el.addEventListener(eventName, ev => {\n      /**\n       * `zone.run` is required to make sure that we are running inside the Angular zone\n       * at all times. This is necessary since an app that has Capacitor will\n       * override the `document.addEventListener` with its own implementation.\n       * The override causes the event to no longer be in the Angular zone.\n       */\n      zone.run(() => {\n        // ?? cordova might emit \"null\" events\n        const value = ev != null ? ev.detail : undefined;\n        emitter.next(value);\n      });\n    });\n  }\n};\nclass NavController {\n  constructor(platform, location, serializer, router) {\n    _defineProperty(this, \"location\", void 0);\n    _defineProperty(this, \"serializer\", void 0);\n    _defineProperty(this, \"router\", void 0);\n    _defineProperty(this, \"topOutlet\", void 0);\n    _defineProperty(this, \"direction\", DEFAULT_DIRECTION);\n    _defineProperty(this, \"animated\", DEFAULT_ANIMATED);\n    _defineProperty(this, \"animationBuilder\", void 0);\n    _defineProperty(this, \"guessDirection\", 'forward');\n    _defineProperty(this, \"guessAnimation\", void 0);\n    _defineProperty(this, \"lastNavId\", -1);\n    this.location = location;\n    this.serializer = serializer;\n    this.router = router;\n    // Subscribe to router events to detect direction\n    if (router) {\n      router.events.subscribe(ev => {\n        if (ev instanceof NavigationStart) {\n          // restoredState is set if the browser back/forward button is used\n          const id = ev.restoredState ? ev.restoredState.navigationId : ev.id;\n          this.guessDirection = this.guessAnimation = id < this.lastNavId ? 'back' : 'forward';\n          this.lastNavId = this.guessDirection === 'forward' ? ev.id : id;\n        }\n      });\n    }\n    // Subscribe to backButton events\n    platform.backButton.subscribeWithPriority(0, processNextHandler => {\n      this.pop();\n      processNextHandler();\n    });\n  }\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling `this.router.navigateByUrl()`, but it's explicit about the **direction** of the transition.\n   *\n   * Going **forward** means that a new page is going to be pushed to the stack of the outlet (ion-router-outlet),\n   * and that it will show a \"forward\" animation by default.\n   *\n   * Navigating forward can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"forward\">Link</a>\n   * ```\n   */\n  navigateForward(url, options = {}) {\n    this.setDirection('forward', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling:\n   *\n   * ```ts\n   * this.navController.setDirection('back');\n   * this.router.navigateByUrl(path);\n   * ```\n   *\n   * Going **back** means that all the pages in the stack until the navigated page is found will be popped,\n   * and that it will show a \"back\" animation by default.\n   *\n   * Navigating back can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"back\">Link</a>\n   * ```\n   */\n  navigateBack(url, options = {}) {\n    this.setDirection('back', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n  /**\n   * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n   * it's equivalent to calling:\n   *\n   * ```ts\n   * this.navController.setDirection('root');\n   * this.router.navigateByUrl(path);\n   * ```\n   *\n   * Going **root** means that all existing pages in the stack will be removed,\n   * and the navigated page will become the single page in the stack.\n   *\n   * Navigating root can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n   *\n   * ```html\n   * <a routerLink=\"/path/to/page\" routerDirection=\"root\">Link</a>\n   * ```\n   */\n  navigateRoot(url, options = {}) {\n    this.setDirection('root', options.animated, options.animationDirection, options.animation);\n    return this.navigate(url, options);\n  }\n  /**\n   * Same as [Location](https://angular.io/api/common/Location)'s back() method.\n   * It will use the standard `window.history.back()` under the hood, but featuring a `back` animation\n   * by default.\n   */\n  back(options = {\n    animated: true,\n    animationDirection: 'back'\n  }) {\n    this.setDirection('back', options.animated, options.animationDirection, options.animation);\n    return this.location.back();\n  }\n  /**\n   * This methods goes back in the context of Ionic's stack navigation.\n   *\n   * It recursively finds the top active `ion-router-outlet` and calls `pop()`.\n   * This is the recommended way to go back when you are using `ion-router-outlet`.\n   *\n   * Resolves to `true` if it was able to pop.\n   */\n  pop() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      let outlet = _this.topOutlet;\n      while (outlet) {\n        if (yield outlet.pop()) {\n          return true;\n        } else {\n          outlet = outlet.parentOutlet;\n        }\n      }\n      return false;\n    })();\n  }\n  /**\n   * This methods specifies the direction of the next navigation performed by the Angular router.\n   *\n   * `setDirection()` does not trigger any transition, it just sets some flags to be consumed by `ion-router-outlet`.\n   *\n   * It's recommended to use `navigateForward()`, `navigateBack()` and `navigateRoot()` instead of `setDirection()`.\n   */\n  setDirection(direction, animated, animationDirection, animationBuilder) {\n    this.direction = direction;\n    this.animated = getAnimation(direction, animated, animationDirection);\n    this.animationBuilder = animationBuilder;\n  }\n  /**\n   * @internal\n   */\n  setTopOutlet(outlet) {\n    this.topOutlet = outlet;\n  }\n  /**\n   * @internal\n   */\n  consumeTransition() {\n    let direction = 'root';\n    let animation;\n    const animationBuilder = this.animationBuilder;\n    if (this.direction === 'auto') {\n      direction = this.guessDirection;\n      animation = this.guessAnimation;\n    } else {\n      animation = this.animated;\n      direction = this.direction;\n    }\n    this.direction = DEFAULT_DIRECTION;\n    this.animated = DEFAULT_ANIMATED;\n    this.animationBuilder = undefined;\n    return {\n      direction,\n      animation,\n      animationBuilder\n    };\n  }\n  navigate(url, options) {\n    if (Array.isArray(url)) {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      return this.router.navigate(url, options);\n    } else {\n      /**\n       * navigateByUrl ignores any properties that\n       * would change the url, so things like queryParams\n       * would be ignored unless we create a url tree\n       * More Info: https://github.com/angular/angular/issues/18798\n       */\n      const urlTree = this.serializer.parse(url.toString());\n      if (options.queryParams !== undefined) {\n        urlTree.queryParams = {\n          ...options.queryParams\n        };\n      }\n      if (options.fragment !== undefined) {\n        urlTree.fragment = options.fragment;\n      }\n      /**\n       * `navigateByUrl` will still apply `NavigationExtras` properties\n       * that do not modify the url, such as `replaceUrl` which is why\n       * `options` is passed in here.\n       */\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      return this.router.navigateByUrl(urlTree, options);\n    }\n  }\n  /** @nocollapse */\n}\n_NavController = NavController;\n_defineProperty(NavController, \"\\u0275fac\", function _NavController_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _NavController)(i0.ɵɵinject(Platform), i0.ɵɵinject(i1.Location), i0.ɵɵinject(i3.UrlSerializer), i0.ɵɵinject(i3.Router, 8));\n});\n/** @nocollapse */\n_defineProperty(NavController, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _NavController,\n  factory: _NavController.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NavController, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Platform\n    }, {\n      type: i1.Location\n    }, {\n      type: i3.UrlSerializer\n    }, {\n      type: i3.Router,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\nconst getAnimation = (direction, animated, animationDirection) => {\n  if (animated === false) {\n    return undefined;\n  }\n  if (animationDirection !== undefined) {\n    return animationDirection;\n  }\n  if (direction === 'forward' || direction === 'back') {\n    return direction;\n  } else if (direction === 'root' && animated === true) {\n    return 'forward';\n  }\n  return undefined;\n};\nconst DEFAULT_DIRECTION = 'auto';\nconst DEFAULT_ANIMATED = undefined;\nclass Config {\n  get(key, fallback) {\n    const c = getConfig();\n    if (c) {\n      return c.get(key, fallback);\n    }\n    return null;\n  }\n  getBoolean(key, fallback) {\n    const c = getConfig();\n    if (c) {\n      return c.getBoolean(key, fallback);\n    }\n    return false;\n  }\n  getNumber(key, fallback) {\n    const c = getConfig();\n    if (c) {\n      return c.getNumber(key, fallback);\n    }\n    return 0;\n  }\n  /** @nocollapse */\n}\n_Config = Config;\n_defineProperty(Config, \"\\u0275fac\", function _Config_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _Config)();\n});\n/** @nocollapse */\n_defineProperty(Config, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _Config,\n  factory: _Config.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Config, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nconst ConfigToken = new InjectionToken('USERCONFIG');\nconst getConfig = () => {\n  if (typeof window !== 'undefined') {\n    const Ionic = window.Ionic;\n    if (Ionic !== null && Ionic !== void 0 && Ionic.config) {\n      return Ionic.config;\n    }\n  }\n  return null;\n};\n\n/**\n * @description\n * NavParams are an object that exists on a page and can contain data for that particular view.\n * Similar to how data was pass to a view in V1 with `$stateParams`, NavParams offer a much more flexible\n * option with a simple `get` method.\n *\n * @usage\n * ```ts\n * import { NavParams } from '@ionic/angular';\n *\n * export class MyClass{\n *\n *  constructor(navParams: NavParams){\n *    // userParams is an object we have in our nav-parameters\n *    navParams.get('userParams');\n *  }\n *\n * }\n * ```\n */\nclass NavParams {\n  constructor(data = {}) {\n    _defineProperty(this, \"data\", void 0);\n    this.data = data;\n    console.warn(`[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.`);\n  }\n  /**\n   * Get the value of a nav-parameter for the current view\n   *\n   * ```ts\n   * import { NavParams } from 'ionic-angular';\n   *\n   * export class MyClass{\n   *  constructor(public navParams: NavParams){\n   *    // userParams is an object we have in our nav-parameters\n   *    this.navParams.get('userParams');\n   *  }\n   * }\n   * ```\n   *\n   * @param param Which param you want to look up\n   */\n  get(param) {\n    return this.data[param];\n  }\n}\n\n// TODO(FW-2827): types\nclass AngularDelegate {\n  constructor() {\n    _defineProperty(this, \"zone\", inject(NgZone));\n    _defineProperty(this, \"applicationRef\", inject(ApplicationRef));\n    _defineProperty(this, \"config\", inject(ConfigToken));\n  }\n  create(environmentInjector, injector, elementReferenceKey) {\n    var _this$config$useSetIn;\n    return new AngularFrameworkDelegate(environmentInjector, injector, this.applicationRef, this.zone, elementReferenceKey, (_this$config$useSetIn = this.config.useSetInputAPI) !== null && _this$config$useSetIn !== void 0 ? _this$config$useSetIn : false);\n  }\n  /** @nocollapse */\n}\n_AngularDelegate = AngularDelegate;\n_defineProperty(AngularDelegate, \"\\u0275fac\", function _AngularDelegate_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AngularDelegate)();\n});\n/** @nocollapse */\n_defineProperty(AngularDelegate, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _AngularDelegate,\n  factory: _AngularDelegate.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularDelegate, [{\n    type: Injectable\n  }], null, null);\n})();\nclass AngularFrameworkDelegate {\n  constructor(environmentInjector, injector, applicationRef, zone, elementReferenceKey, enableSignalsSupport) {\n    _defineProperty(this, \"environmentInjector\", void 0);\n    _defineProperty(this, \"injector\", void 0);\n    _defineProperty(this, \"applicationRef\", void 0);\n    _defineProperty(this, \"zone\", void 0);\n    _defineProperty(this, \"elementReferenceKey\", void 0);\n    _defineProperty(this, \"enableSignalsSupport\", void 0);\n    _defineProperty(this, \"elRefMap\", new WeakMap());\n    _defineProperty(this, \"elEventsMap\", new WeakMap());\n    this.environmentInjector = environmentInjector;\n    this.injector = injector;\n    this.applicationRef = applicationRef;\n    this.zone = zone;\n    this.elementReferenceKey = elementReferenceKey;\n    this.enableSignalsSupport = enableSignalsSupport;\n  }\n  attachViewToDom(container, component, params, cssClasses) {\n    return this.zone.run(() => {\n      return new Promise(resolve => {\n        const componentProps = {\n          ...params\n        };\n        /**\n         * Ionic Angular passes a reference to a modal\n         * or popover that can be accessed using a\n         * variable in the overlay component. If\n         * elementReferenceKey is defined, then we should\n         * pass a reference to the component using\n         * elementReferenceKey as the key.\n         */\n        if (this.elementReferenceKey !== undefined) {\n          componentProps[this.elementReferenceKey] = container;\n        }\n        const el = attachView(this.zone, this.environmentInjector, this.injector, this.applicationRef, this.elRefMap, this.elEventsMap, container, component, componentProps, cssClasses, this.elementReferenceKey, this.enableSignalsSupport);\n        resolve(el);\n      });\n    });\n  }\n  removeViewFromDom(_container, component) {\n    return this.zone.run(() => {\n      return new Promise(resolve => {\n        const componentRef = this.elRefMap.get(component);\n        if (componentRef) {\n          componentRef.destroy();\n          this.elRefMap.delete(component);\n          const unbindEvents = this.elEventsMap.get(component);\n          if (unbindEvents) {\n            unbindEvents();\n            this.elEventsMap.delete(component);\n          }\n        }\n        resolve();\n      });\n    });\n  }\n}\nconst attachView = (zone, environmentInjector, injector, applicationRef, elRefMap, elEventsMap, container, component, params, cssClasses, elementReferenceKey, enableSignalsSupport) => {\n  /**\n   * Wraps the injector with a custom injector that\n   * provides NavParams to the component.\n   *\n   * NavParams is a legacy feature from Ionic v3 that allows\n   * Angular developers to provide data to a component\n   * and access it by providing NavParams as a dependency\n   * in the constructor.\n   *\n   * The modern approach is to access the data directly\n   * from the component's class instance.\n   */\n  const childInjector = Injector.create({\n    providers: getProviders(params),\n    parent: injector\n  });\n  const componentRef = createComponent(component, {\n    environmentInjector,\n    elementInjector: childInjector\n  });\n  const instance = componentRef.instance;\n  const hostElement = componentRef.location.nativeElement;\n  if (params) {\n    /**\n     * For modals and popovers, a reference to the component is\n     * added to `params` during the call to attachViewToDom. If\n     * a reference using this name is already set, this means\n     * the app is trying to use the name as a component prop,\n     * which will cause collisions.\n     */\n    if (elementReferenceKey && instance[elementReferenceKey] !== undefined) {\n      console.error(`[Ionic Error]: ${elementReferenceKey} is a reserved property when using ${container.tagName.toLowerCase()}. Rename or remove the \"${elementReferenceKey}\" property from ${component.name}.`);\n    }\n    /**\n     * Angular 14.1 added support for setInput\n     * so we need to fall back to Object.assign\n     * for Angular 14.0.\n     */\n    if (enableSignalsSupport === true && componentRef.setInput !== undefined) {\n      const {\n        modal,\n        popover,\n        ...otherParams\n      } = params;\n      /**\n       * Any key/value pairs set in componentProps\n       * must be set as inputs on the component instance.\n       */\n      for (const key in otherParams) {\n        componentRef.setInput(key, otherParams[key]);\n      }\n      /**\n       * Using setInput will cause an error when\n       * setting modal/popover on a component that\n       * does not define them as an input. For backwards\n       * compatibility purposes we fall back to using\n       * Object.assign for these properties.\n       */\n      if (modal !== undefined) {\n        Object.assign(instance, {\n          modal\n        });\n      }\n      if (popover !== undefined) {\n        Object.assign(instance, {\n          popover\n        });\n      }\n    } else {\n      Object.assign(instance, params);\n    }\n  }\n  if (cssClasses) {\n    for (const cssClass of cssClasses) {\n      hostElement.classList.add(cssClass);\n    }\n  }\n  const unbindEvents = bindLifecycleEvents(zone, instance, hostElement);\n  container.appendChild(hostElement);\n  applicationRef.attachView(componentRef.hostView);\n  elRefMap.set(hostElement, componentRef);\n  elEventsMap.set(hostElement, unbindEvents);\n  return hostElement;\n};\nconst LIFECYCLES = [LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD];\nconst bindLifecycleEvents = (zone, instance, element) => {\n  return zone.run(() => {\n    const unregisters = LIFECYCLES.filter(eventName => typeof instance[eventName] === 'function').map(eventName => {\n      const handler = ev => instance[eventName](ev.detail);\n      element.addEventListener(eventName, handler);\n      return () => element.removeEventListener(eventName, handler);\n    });\n    return () => unregisters.forEach(fn => fn());\n  });\n};\nconst NavParamsToken = new InjectionToken('NavParamsToken');\nconst getProviders = params => {\n  return [{\n    provide: NavParamsToken,\n    useValue: params\n  }, {\n    provide: NavParams,\n    useFactory: provideNavParamsInjectable,\n    deps: [NavParamsToken]\n  }];\n};\nconst provideNavParamsInjectable = params => {\n  return new NavParams(params);\n};\n\n// TODO: Is there a way we can grab this from angular-component-lib instead?\n/* eslint-disable */\n/* tslint:disable */\nconst proxyInputs = (Cmp, inputs) => {\n  const Prototype = Cmp.prototype;\n  inputs.forEach(item => {\n    Object.defineProperty(Prototype, item, {\n      get() {\n        return this.el[item];\n      },\n      set(val) {\n        this.z.runOutsideAngular(() => this.el[item] = val);\n      }\n    });\n  });\n};\nconst proxyMethods = (Cmp, methods) => {\n  const Prototype = Cmp.prototype;\n  methods.forEach(methodName => {\n    Prototype[methodName] = function () {\n      const args = arguments;\n      return this.z.runOutsideAngular(() => this.el[methodName].apply(this.el, args));\n    };\n  });\n};\nconst proxyOutputs = (instance, el, events) => {\n  events.forEach(eventName => instance[eventName] = fromEvent(el, eventName));\n};\n// tslint:disable-next-line: only-arrow-functions\nfunction ProxyCmp(opts) {\n  const decorator = function (cls) {\n    const {\n      defineCustomElementFn,\n      inputs,\n      methods\n    } = opts;\n    if (defineCustomElementFn !== undefined) {\n      defineCustomElementFn();\n    }\n    if (inputs) {\n      proxyInputs(cls, inputs);\n    }\n    if (methods) {\n      proxyMethods(cls, methods);\n    }\n    return cls;\n  };\n  return decorator;\n}\nconst POPOVER_INPUTS = ['alignment', 'animated', 'arrow', 'keepContentsMounted', 'backdropDismiss', 'cssClass', 'dismissOnSelect', 'enterAnimation', 'event', 'focusTrap', 'isOpen', 'keyboardClose', 'leaveAnimation', 'mode', 'showBackdrop', 'translucent', 'trigger', 'triggerAction', 'reference', 'size', 'side'];\nconst POPOVER_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss'];\nlet IonPopover = (_IonPopover = class IonPopover {\n  constructor(c, r, z) {\n    _defineProperty(this, \"z\", void 0);\n    // TODO(FW-2827): type\n    _defineProperty(this, \"template\", void 0);\n    _defineProperty(this, \"isCmpOpen\", false);\n    _defineProperty(this, \"el\", void 0);\n    this.z = z;\n    this.el = r.nativeElement;\n    this.el.addEventListener('ionMount', () => {\n      this.isCmpOpen = true;\n      c.detectChanges();\n    });\n    this.el.addEventListener('didDismiss', () => {\n      this.isCmpOpen = false;\n      c.detectChanges();\n    });\n    proxyOutputs(this, this.el, ['ionPopoverDidPresent', 'ionPopoverWillPresent', 'ionPopoverWillDismiss', 'ionPopoverDidDismiss', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n  }\n  /** @nocollapse */\n}, _defineProperty(_IonPopover, \"\\u0275fac\", function _IonPopover_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _IonPopover)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n}), _defineProperty(_IonPopover, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _IonPopover,\n  selectors: [[\"ion-popover\"]],\n  contentQueries: function _IonPopover_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n    }\n  },\n  inputs: {\n    alignment: \"alignment\",\n    animated: \"animated\",\n    arrow: \"arrow\",\n    keepContentsMounted: \"keepContentsMounted\",\n    backdropDismiss: \"backdropDismiss\",\n    cssClass: \"cssClass\",\n    dismissOnSelect: \"dismissOnSelect\",\n    enterAnimation: \"enterAnimation\",\n    event: \"event\",\n    focusTrap: \"focusTrap\",\n    isOpen: \"isOpen\",\n    keyboardClose: \"keyboardClose\",\n    leaveAnimation: \"leaveAnimation\",\n    mode: \"mode\",\n    showBackdrop: \"showBackdrop\",\n    translucent: \"translucent\",\n    trigger: \"trigger\",\n    triggerAction: \"triggerAction\",\n    reference: \"reference\",\n    size: \"size\",\n    side: \"side\"\n  },\n  standalone: false\n})), _IonPopover);\nIonPopover = __decorate([ProxyCmp({\n  inputs: POPOVER_INPUTS,\n  methods: POPOVER_METHODS\n})\n/**\n * @Component extends from @Directive\n * so by defining the inputs here we\n * do not need to re-define them for the\n * lazy loaded popover.\n */], IonPopover);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonPopover, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-popover',\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: POPOVER_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    template: [{\n      type: ContentChild,\n      args: [TemplateRef, {\n        static: false\n      }]\n    }]\n  });\n})();\nconst MODAL_INPUTS = ['animated', 'keepContentsMounted', 'backdropBreakpoint', 'backdropDismiss', 'breakpoints', 'canDismiss', 'cssClass', 'enterAnimation', 'event', 'focusTrap', 'handle', 'handleBehavior', 'initialBreakpoint', 'isOpen', 'keyboardClose', 'leaveAnimation', 'mode', 'presentingElement', 'showBackdrop', 'translucent', 'trigger'];\nconst MODAL_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss', 'setCurrentBreakpoint', 'getCurrentBreakpoint'];\nlet IonModal = (_IonModal = class IonModal {\n  constructor(c, r, z) {\n    _defineProperty(this, \"z\", void 0);\n    // TODO(FW-2827): type\n    _defineProperty(this, \"template\", void 0);\n    _defineProperty(this, \"isCmpOpen\", false);\n    _defineProperty(this, \"el\", void 0);\n    this.z = z;\n    this.el = r.nativeElement;\n    this.el.addEventListener('ionMount', () => {\n      this.isCmpOpen = true;\n      c.detectChanges();\n    });\n    this.el.addEventListener('didDismiss', () => {\n      this.isCmpOpen = false;\n      c.detectChanges();\n    });\n    proxyOutputs(this, this.el, ['ionModalDidPresent', 'ionModalWillPresent', 'ionModalWillDismiss', 'ionModalDidDismiss', 'ionBreakpointDidChange', 'didPresent', 'willPresent', 'willDismiss', 'didDismiss']);\n  }\n  /** @nocollapse */\n}, _defineProperty(_IonModal, \"\\u0275fac\", function _IonModal_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _IonModal)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n}), _defineProperty(_IonModal, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _IonModal,\n  selectors: [[\"ion-modal\"]],\n  contentQueries: function _IonModal_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n    }\n  },\n  inputs: {\n    animated: \"animated\",\n    keepContentsMounted: \"keepContentsMounted\",\n    backdropBreakpoint: \"backdropBreakpoint\",\n    backdropDismiss: \"backdropDismiss\",\n    breakpoints: \"breakpoints\",\n    canDismiss: \"canDismiss\",\n    cssClass: \"cssClass\",\n    enterAnimation: \"enterAnimation\",\n    event: \"event\",\n    focusTrap: \"focusTrap\",\n    handle: \"handle\",\n    handleBehavior: \"handleBehavior\",\n    initialBreakpoint: \"initialBreakpoint\",\n    isOpen: \"isOpen\",\n    keyboardClose: \"keyboardClose\",\n    leaveAnimation: \"leaveAnimation\",\n    mode: \"mode\",\n    presentingElement: \"presentingElement\",\n    showBackdrop: \"showBackdrop\",\n    translucent: \"translucent\",\n    trigger: \"trigger\"\n  },\n  standalone: false\n})), _IonModal);\nIonModal = __decorate([ProxyCmp({\n  inputs: MODAL_INPUTS,\n  methods: MODAL_METHODS\n})\n/**\n * @Component extends from @Directive\n * so by defining the inputs here we\n * do not need to re-define them for the\n * lazy loaded popover.\n */], IonModal);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonModal, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-modal',\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: MODAL_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    template: [{\n      type: ContentChild,\n      args: [TemplateRef, {\n        static: false\n      }]\n    }]\n  });\n})();\nconst insertView = (views, view, direction) => {\n  if (direction === 'root') {\n    return setRoot(views, view);\n  } else if (direction === 'forward') {\n    return setForward(views, view);\n  } else {\n    return setBack(views, view);\n  }\n};\nconst setRoot = (views, view) => {\n  views = views.filter(v => v.stackId !== view.stackId);\n  views.push(view);\n  return views;\n};\nconst setForward = (views, view) => {\n  const index = views.indexOf(view);\n  if (index >= 0) {\n    views = views.filter(v => v.stackId !== view.stackId || v.id <= view.id);\n  } else {\n    views.push(view);\n  }\n  return views;\n};\nconst setBack = (views, view) => {\n  const index = views.indexOf(view);\n  if (index >= 0) {\n    return views.filter(v => v.stackId !== view.stackId || v.id <= view.id);\n  } else {\n    return setRoot(views, view);\n  }\n};\nconst getUrl = (router, activatedRoute) => {\n  const urlTree = router.createUrlTree(['.'], {\n    relativeTo: activatedRoute\n  });\n  return router.serializeUrl(urlTree);\n};\nconst isTabSwitch = (enteringView, leavingView) => {\n  if (!leavingView) {\n    return true;\n  }\n  return enteringView.stackId !== leavingView.stackId;\n};\nconst computeStackId = (prefixUrl, url) => {\n  if (!prefixUrl) {\n    return undefined;\n  }\n  const segments = toSegments(url);\n  for (let i = 0; i < segments.length; i++) {\n    if (i >= prefixUrl.length) {\n      return segments[i];\n    }\n    if (segments[i] !== prefixUrl[i]) {\n      return undefined;\n    }\n  }\n  return undefined;\n};\nconst toSegments = path => {\n  return path.split('/').map(s => s.trim()).filter(s => s !== '');\n};\nconst destroyView = view => {\n  if (view) {\n    view.ref.destroy();\n    view.unlistenEvents();\n  }\n};\n\n// TODO(FW-2827): types\nclass StackController {\n  constructor(tabsPrefix, containerEl, router, navCtrl, zone, location) {\n    _defineProperty(this, \"containerEl\", void 0);\n    _defineProperty(this, \"router\", void 0);\n    _defineProperty(this, \"navCtrl\", void 0);\n    _defineProperty(this, \"zone\", void 0);\n    _defineProperty(this, \"location\", void 0);\n    _defineProperty(this, \"views\", []);\n    _defineProperty(this, \"runningTask\", void 0);\n    _defineProperty(this, \"skipTransition\", false);\n    _defineProperty(this, \"tabsPrefix\", void 0);\n    _defineProperty(this, \"activeView\", void 0);\n    _defineProperty(this, \"nextId\", 0);\n    this.containerEl = containerEl;\n    this.router = router;\n    this.navCtrl = navCtrl;\n    this.zone = zone;\n    this.location = location;\n    this.tabsPrefix = tabsPrefix !== undefined ? toSegments(tabsPrefix) : undefined;\n  }\n  createView(ref, activatedRoute) {\n    var _ref$location;\n    const url = getUrl(this.router, activatedRoute);\n    const element = ref === null || ref === void 0 || (_ref$location = ref.location) === null || _ref$location === void 0 ? void 0 : _ref$location.nativeElement;\n    const unlistenEvents = bindLifecycleEvents(this.zone, ref.instance, element);\n    return {\n      id: this.nextId++,\n      stackId: computeStackId(this.tabsPrefix, url),\n      unlistenEvents,\n      element,\n      ref,\n      url\n    };\n  }\n  getExistingView(activatedRoute) {\n    const activatedUrlKey = getUrl(this.router, activatedRoute);\n    const view = this.views.find(vw => vw.url === activatedUrlKey);\n    if (view) {\n      view.ref.changeDetectorRef.reattach();\n    }\n    return view;\n  }\n  setActive(enteringView) {\n    var _router$navigations, _currentNavigation;\n    const consumeResult = this.navCtrl.consumeTransition();\n    let {\n      direction,\n      animation,\n      animationBuilder\n    } = consumeResult;\n    const leavingView = this.activeView;\n    const tabSwitch = isTabSwitch(enteringView, leavingView);\n    if (tabSwitch) {\n      direction = 'back';\n      animation = undefined;\n    }\n    const viewsSnapshot = this.views.slice();\n    let currentNavigation;\n    const router = this.router;\n    // Angular >= 7.2.0\n    if (router.getCurrentNavigation) {\n      currentNavigation = router.getCurrentNavigation();\n      // Angular < 7.2.0\n    } else if ((_router$navigations = router.navigations) !== null && _router$navigations !== void 0 && _router$navigations.value) {\n      currentNavigation = router.navigations.value;\n    }\n    /**\n     * If the navigation action\n     * sets `replaceUrl: true`\n     * then we need to make sure\n     * we remove the last item\n     * from our views stack\n     */\n    if ((_currentNavigation = currentNavigation) !== null && _currentNavigation !== void 0 && (_currentNavigation = _currentNavigation.extras) !== null && _currentNavigation !== void 0 && _currentNavigation.replaceUrl) {\n      if (this.views.length > 0) {\n        this.views.splice(-1, 1);\n      }\n    }\n    const reused = this.views.includes(enteringView);\n    const views = this.insertView(enteringView, direction);\n    // Trigger change detection before transition starts\n    // This will call ngOnInit() the first time too, just after the view\n    // was attached to the dom, but BEFORE the transition starts\n    if (!reused) {\n      enteringView.ref.changeDetectorRef.detectChanges();\n    }\n    /**\n     * If we are going back from a page that\n     * was presented using a custom animation\n     * we should default to using that\n     * unless the developer explicitly\n     * provided another animation.\n     */\n    const customAnimation = enteringView.animationBuilder;\n    if (animationBuilder === undefined && direction === 'back' && !tabSwitch && customAnimation !== undefined) {\n      animationBuilder = customAnimation;\n    }\n    /**\n     * Save any custom animation so that navigating\n     * back will use this custom animation by default.\n     */\n    if (leavingView) {\n      leavingView.animationBuilder = animationBuilder;\n    }\n    // Wait until previous transitions finish\n    return this.zone.runOutsideAngular(() => {\n      return this.wait(() => {\n        // disconnect leaving page from change detection to\n        // reduce jank during the page transition\n        if (leavingView) {\n          leavingView.ref.changeDetectorRef.detach();\n        }\n        // In case the enteringView is the same as the leavingPage we need to reattach()\n        enteringView.ref.changeDetectorRef.reattach();\n        return this.transition(enteringView, leavingView, animation, this.canGoBack(1), false, animationBuilder).then(() => cleanupAsync(enteringView, views, viewsSnapshot, this.location, this.zone)).then(() => ({\n          enteringView,\n          direction,\n          animation,\n          tabSwitch\n        }));\n      });\n    });\n  }\n  canGoBack(deep, stackId = this.getActiveStackId()) {\n    return this.getStack(stackId).length > deep;\n  }\n  pop(deep, stackId = this.getActiveStackId()) {\n    return this.zone.run(() => {\n      const views = this.getStack(stackId);\n      if (views.length <= deep) {\n        return Promise.resolve(false);\n      }\n      const view = views[views.length - deep - 1];\n      let url = view.url;\n      const viewSavedData = view.savedData;\n      if (viewSavedData) {\n        var _primaryOutlet$route;\n        const primaryOutlet = viewSavedData.get('primary');\n        if (primaryOutlet !== null && primaryOutlet !== void 0 && (_primaryOutlet$route = primaryOutlet.route) !== null && _primaryOutlet$route !== void 0 && (_primaryOutlet$route = _primaryOutlet$route._routerState) !== null && _primaryOutlet$route !== void 0 && _primaryOutlet$route.snapshot.url) {\n          url = primaryOutlet.route._routerState.snapshot.url;\n        }\n      }\n      const {\n        animationBuilder\n      } = this.navCtrl.consumeTransition();\n      return this.navCtrl.navigateBack(url, {\n        ...view.savedExtras,\n        animation: animationBuilder\n      }).then(() => true);\n    });\n  }\n  startBackTransition() {\n    const leavingView = this.activeView;\n    if (leavingView) {\n      const views = this.getStack(leavingView.stackId);\n      const enteringView = views[views.length - 2];\n      const customAnimation = enteringView.animationBuilder;\n      return this.wait(() => {\n        return this.transition(enteringView,\n        // entering view\n        leavingView,\n        // leaving view\n        'back', this.canGoBack(2), true, customAnimation);\n      });\n    }\n    return Promise.resolve();\n  }\n  endBackTransition(shouldComplete) {\n    if (shouldComplete) {\n      this.skipTransition = true;\n      this.pop(1);\n    } else if (this.activeView) {\n      cleanup(this.activeView, this.views, this.views, this.location, this.zone);\n    }\n  }\n  getLastUrl(stackId) {\n    const views = this.getStack(stackId);\n    return views.length > 0 ? views[views.length - 1] : undefined;\n  }\n  /**\n   * @internal\n   */\n  getRootUrl(stackId) {\n    const views = this.getStack(stackId);\n    return views.length > 0 ? views[0] : undefined;\n  }\n  getActiveStackId() {\n    return this.activeView ? this.activeView.stackId : undefined;\n  }\n  /**\n   * @internal\n   */\n  getActiveView() {\n    return this.activeView;\n  }\n  hasRunningTask() {\n    return this.runningTask !== undefined;\n  }\n  destroy() {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    this.containerEl = undefined;\n    this.views.forEach(destroyView);\n    this.activeView = undefined;\n    this.views = [];\n  }\n  getStack(stackId) {\n    return this.views.filter(v => v.stackId === stackId);\n  }\n  insertView(enteringView, direction) {\n    this.activeView = enteringView;\n    this.views = insertView(this.views, enteringView, direction);\n    return this.views.slice();\n  }\n  transition(enteringView, leavingView, direction, showGoBack, progressAnimation, animationBuilder) {\n    if (this.skipTransition) {\n      this.skipTransition = false;\n      return Promise.resolve(false);\n    }\n    if (leavingView === enteringView) {\n      return Promise.resolve(false);\n    }\n    const enteringEl = enteringView ? enteringView.element : undefined;\n    const leavingEl = leavingView ? leavingView.element : undefined;\n    const containerEl = this.containerEl;\n    if (enteringEl && enteringEl !== leavingEl) {\n      enteringEl.classList.add('ion-page');\n      enteringEl.classList.add('ion-page-invisible');\n      if (containerEl.commit) {\n        return containerEl.commit(enteringEl, leavingEl, {\n          duration: direction === undefined ? 0 : undefined,\n          direction,\n          showGoBack,\n          progressAnimation,\n          animationBuilder\n        });\n      }\n    }\n    return Promise.resolve(false);\n  }\n  wait(task) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.runningTask !== undefined) {\n        yield _this2.runningTask;\n        _this2.runningTask = undefined;\n      }\n      const promise = _this2.runningTask = task();\n      promise.finally(() => _this2.runningTask = undefined);\n      return promise;\n    })();\n  }\n}\nconst cleanupAsync = (activeRoute, views, viewsSnapshot, location, zone) => {\n  if (typeof requestAnimationFrame === 'function') {\n    return new Promise(resolve => {\n      requestAnimationFrame(() => {\n        cleanup(activeRoute, views, viewsSnapshot, location, zone);\n        resolve();\n      });\n    });\n  }\n  return Promise.resolve();\n};\nconst cleanup = (activeRoute, views, viewsSnapshot, location, zone) => {\n  /**\n   * Re-enter the Angular zone when destroying page components. This will allow\n   * lifecycle events (`ngOnDestroy`) to be run inside the Angular zone.\n   */\n  zone.run(() => viewsSnapshot.filter(view => !views.includes(view)).forEach(destroyView));\n  views.forEach(view => {\n    /**\n     * In the event that a user navigated multiple\n     * times in rapid succession, we want to make sure\n     * we don't pre-emptively detach a view while\n     * it is in mid-transition.\n     *\n     * In this instance we also do not care about query\n     * params or fragments as it will be the same view regardless\n     */\n    const locationWithoutParams = location.path().split('?')[0];\n    const locationWithoutFragment = locationWithoutParams.split('#')[0];\n    if (view !== activeRoute && view.url !== locationWithoutFragment) {\n      const element = view.element;\n      element.setAttribute('aria-hidden', 'true');\n      element.classList.add('ion-page-hidden');\n      view.ref.changeDetectorRef.detach();\n    }\n  });\n};\n\n// TODO(FW-2827): types\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonRouterOutlet {\n  /** @internal */\n  get activatedComponentRef() {\n    return this.activated;\n  }\n  set animation(animation) {\n    this.nativeEl.animation = animation;\n  }\n  set animated(animated) {\n    this.nativeEl.animated = animated;\n  }\n  set swipeGesture(swipe) {\n    this._swipeGesture = swipe;\n    this.nativeEl.swipeHandler = swipe ? {\n      canStart: () => this.stackCtrl.canGoBack(1) && !this.stackCtrl.hasRunningTask(),\n      onStart: () => this.stackCtrl.startBackTransition(),\n      onEnd: shouldContinue => this.stackCtrl.endBackTransition(shouldContinue)\n    } : undefined;\n  }\n  constructor(name, tabs, commonLocation, elementRef, router, zone, activatedRoute, parentOutlet) {\n    _defineProperty(this, \"parentOutlet\", void 0);\n    _defineProperty(this, \"nativeEl\", void 0);\n    _defineProperty(this, \"activatedView\", null);\n    _defineProperty(this, \"tabsPrefix\", void 0);\n    _defineProperty(this, \"_swipeGesture\", void 0);\n    _defineProperty(this, \"stackCtrl\", void 0);\n    // Maintain map of activated route proxies for each component instance\n    _defineProperty(this, \"proxyMap\", new WeakMap());\n    // Keep the latest activated route in a subject for the proxy routes to switch map to\n    _defineProperty(this, \"currentActivatedRoute$\", new BehaviorSubject(null));\n    _defineProperty(this, \"activated\", null);\n    _defineProperty(this, \"_activatedRoute\", null);\n    /**\n     * The name of the outlet\n     */\n    _defineProperty(this, \"name\", PRIMARY_OUTLET);\n    /** @internal */\n    _defineProperty(this, \"stackWillChange\", new EventEmitter());\n    /** @internal */\n    _defineProperty(this, \"stackDidChange\", new EventEmitter());\n    // eslint-disable-next-line @angular-eslint/no-output-rename\n    _defineProperty(this, \"activateEvents\", new EventEmitter());\n    // eslint-disable-next-line @angular-eslint/no-output-rename\n    _defineProperty(this, \"deactivateEvents\", new EventEmitter());\n    _defineProperty(this, \"parentContexts\", inject(ChildrenOutletContexts));\n    _defineProperty(this, \"location\", inject(ViewContainerRef));\n    _defineProperty(this, \"environmentInjector\", inject(EnvironmentInjector));\n    _defineProperty(this, \"inputBinder\", inject(INPUT_BINDER, {\n      optional: true\n    }));\n    /** @nodoc */\n    _defineProperty(this, \"supportsBindingToComponentInputs\", true);\n    // Ionic providers\n    _defineProperty(this, \"config\", inject(Config));\n    _defineProperty(this, \"navCtrl\", inject(NavController));\n    this.parentOutlet = parentOutlet;\n    this.nativeEl = elementRef.nativeElement;\n    this.name = name || PRIMARY_OUTLET;\n    this.tabsPrefix = tabs === 'true' ? getUrl(router, activatedRoute) : undefined;\n    this.stackCtrl = new StackController(this.tabsPrefix, this.nativeEl, router, this.navCtrl, zone, commonLocation);\n    this.parentContexts.onChildOutletCreated(this.name, this);\n  }\n  ngOnDestroy() {\n    var _this$inputBinder;\n    this.stackCtrl.destroy();\n    (_this$inputBinder = this.inputBinder) === null || _this$inputBinder === void 0 || _this$inputBinder.unsubscribeFromRouteData(this);\n  }\n  getContext() {\n    return this.parentContexts.getContext(this.name);\n  }\n  ngOnInit() {\n    this.initializeOutletWithName();\n  }\n  // Note: Ionic deviates from the Angular Router implementation here\n  initializeOutletWithName() {\n    if (!this.activated) {\n      // If the outlet was not instantiated at the time the route got activated we need to populate\n      // the outlet when it is initialized (ie inside a NgIf)\n      const context = this.getContext();\n      if (context !== null && context !== void 0 && context.route) {\n        this.activateWith(context.route, context.injector);\n      }\n    }\n    new Promise(resolve => componentOnReady(this.nativeEl, resolve)).then(() => {\n      if (this._swipeGesture === undefined) {\n        this.swipeGesture = this.config.getBoolean('swipeBackEnabled', this.nativeEl.mode === 'ios');\n      }\n    });\n  }\n  get isActivated() {\n    return !!this.activated;\n  }\n  get component() {\n    if (!this.activated) {\n      throw new Error('Outlet is not activated');\n    }\n    return this.activated.instance;\n  }\n  get activatedRoute() {\n    if (!this.activated) {\n      throw new Error('Outlet is not activated');\n    }\n    return this._activatedRoute;\n  }\n  get activatedRouteData() {\n    if (this._activatedRoute) {\n      return this._activatedRoute.snapshot.data;\n    }\n    return {};\n  }\n  /**\n   * Called when the `RouteReuseStrategy` instructs to detach the subtree\n   */\n  detach() {\n    throw new Error('incompatible reuse strategy');\n  }\n  /**\n   * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n   */\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  attach(_ref, _activatedRoute) {\n    throw new Error('incompatible reuse strategy');\n  }\n  deactivate() {\n    if (this.activated) {\n      if (this.activatedView) {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const context = this.getContext();\n        this.activatedView.savedData = new Map(context.children['contexts']);\n        /**\n         * Angular v11.2.10 introduced a change\n         * where this route context is cleared out when\n         * a router-outlet is deactivated, However,\n         * we need this route information in order to\n         * return a user back to the correct tab when\n         * leaving and then going back to the tab context.\n         */\n        const primaryOutlet = this.activatedView.savedData.get('primary');\n        if (primaryOutlet && context.route) {\n          primaryOutlet.route = {\n            ...context.route\n          };\n        }\n        /**\n         * Ensure we are saving the NavigationExtras\n         * data otherwise it will be lost\n         */\n        this.activatedView.savedExtras = {};\n        if (context.route) {\n          const contextSnapshot = context.route.snapshot;\n          this.activatedView.savedExtras.queryParams = contextSnapshot.queryParams;\n          this.activatedView.savedExtras.fragment = contextSnapshot.fragment;\n        }\n      }\n      const c = this.component;\n      this.activatedView = null;\n      this.activated = null;\n      this._activatedRoute = null;\n      this.deactivateEvents.emit(c);\n    }\n  }\n  activateWith(activatedRoute, environmentInjector) {\n    var _this$inputBinder2;\n    if (this.isActivated) {\n      throw new Error('Cannot activate an already activated outlet');\n    }\n    this._activatedRoute = activatedRoute;\n    let cmpRef;\n    let enteringView = this.stackCtrl.getExistingView(activatedRoute);\n    if (enteringView) {\n      cmpRef = this.activated = enteringView.ref;\n      const saved = enteringView.savedData;\n      if (saved) {\n        // self-restore\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        const context = this.getContext();\n        context.children['contexts'] = saved;\n      }\n      // Updated activated route proxy for this component\n      this.updateActivatedRouteProxy(cmpRef.instance, activatedRoute);\n    } else {\n      var _snapshot$routeConfig;\n      const snapshot = activatedRoute._futureSnapshot;\n      /**\n       * Angular 14 introduces a new `loadComponent` property to the route config.\n       * This function will assign a `component` property to the route snapshot.\n       * We check for the presence of this property to determine if the route is\n       * using standalone components.\n       */\n      const childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n      // We create an activated route proxy object that will maintain future updates for this component\n      // over its lifecycle in the stack.\n      const component$ = new BehaviorSubject(null);\n      const activatedRouteProxy = this.createActivatedRouteProxy(component$, activatedRoute);\n      const injector = new OutletInjector(activatedRouteProxy, childContexts, this.location.injector);\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const component = (_snapshot$routeConfig = snapshot.routeConfig.component) !== null && _snapshot$routeConfig !== void 0 ? _snapshot$routeConfig : snapshot.component;\n      /**\n       * View components need to be added as a child of ion-router-outlet\n       * for page transitions and swipe to go back.\n       * However, createComponent mounts components as siblings of the\n       * ViewContainerRef. As a result, outletContent must reference\n       * an ng-container inside of ion-router-outlet and not\n       * ion-router-outlet itself.\n       */\n      cmpRef = this.activated = this.outletContent.createComponent(component, {\n        index: this.outletContent.length,\n        injector,\n        environmentInjector: environmentInjector !== null && environmentInjector !== void 0 ? environmentInjector : this.environmentInjector\n      });\n      // Once the component is created we can push it to our local subject supplied to the proxy\n      component$.next(cmpRef.instance);\n      // Calling `markForCheck` to make sure we will run the change detection when the\n      // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n      /**\n       * At this point this.activated has been set earlier\n       * in this function, so it is guaranteed to be non-null.\n       */\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      enteringView = this.stackCtrl.createView(this.activated, activatedRoute);\n      // Store references to the proxy by component\n      this.proxyMap.set(cmpRef.instance, activatedRouteProxy);\n      this.currentActivatedRoute$.next({\n        component: cmpRef.instance,\n        activatedRoute\n      });\n    }\n    (_this$inputBinder2 = this.inputBinder) === null || _this$inputBinder2 === void 0 || _this$inputBinder2.bindActivatedRouteToOutletComponent(this);\n    this.activatedView = enteringView;\n    /**\n     * The top outlet is set prior to the entering view's transition completing,\n     * so that when we have nested outlets (e.g. ion-tabs inside an ion-router-outlet),\n     * the tabs outlet will be assigned as the top outlet when a view inside tabs is\n     * activated.\n     *\n     * In this scenario, activeWith is called for both the tabs and the root router outlet.\n     * To avoid a race condition, we assign the top outlet synchronously.\n     */\n    this.navCtrl.setTopOutlet(this);\n    const leavingView = this.stackCtrl.getActiveView();\n    this.stackWillChange.emit({\n      enteringView,\n      tabSwitch: isTabSwitch(enteringView, leavingView)\n    });\n    this.stackCtrl.setActive(enteringView).then(data => {\n      this.activateEvents.emit(cmpRef.instance);\n      this.stackDidChange.emit(data);\n    });\n  }\n  /**\n   * Returns `true` if there are pages in the stack to go back.\n   */\n  canGoBack(deep = 1, stackId) {\n    return this.stackCtrl.canGoBack(deep, stackId);\n  }\n  /**\n   * Resolves to `true` if it the outlet was able to sucessfully pop the last N pages.\n   */\n  pop(deep = 1, stackId) {\n    return this.stackCtrl.pop(deep, stackId);\n  }\n  /**\n   * Returns the URL of the active page of each stack.\n   */\n  getLastUrl(stackId) {\n    const active = this.stackCtrl.getLastUrl(stackId);\n    return active ? active.url : undefined;\n  }\n  /**\n   * Returns the RouteView of the active page of each stack.\n   * @internal\n   */\n  getLastRouteView(stackId) {\n    return this.stackCtrl.getLastUrl(stackId);\n  }\n  /**\n   * Returns the root view in the tab stack.\n   * @internal\n   */\n  getRootView(stackId) {\n    return this.stackCtrl.getRootUrl(stackId);\n  }\n  /**\n   * Returns the active stack ID. In the context of ion-tabs, it means the active tab.\n   */\n  getActiveStackId() {\n    return this.stackCtrl.getActiveStackId();\n  }\n  /**\n   * Since the activated route can change over the life time of a component in an ion router outlet, we create\n   * a proxy so that we can update the values over time as a user navigates back to components already in the stack.\n   */\n  createActivatedRouteProxy(component$, activatedRoute) {\n    const proxy = new ActivatedRoute();\n    proxy._futureSnapshot = activatedRoute._futureSnapshot;\n    proxy._routerState = activatedRoute._routerState;\n    proxy.snapshot = activatedRoute.snapshot;\n    proxy.outlet = activatedRoute.outlet;\n    proxy.component = activatedRoute.component;\n    // Setup wrappers for the observables so consumers don't have to worry about switching to new observables as the state updates\n    proxy._paramMap = this.proxyObservable(component$, 'paramMap');\n    proxy._queryParamMap = this.proxyObservable(component$, 'queryParamMap');\n    proxy.url = this.proxyObservable(component$, 'url');\n    proxy.params = this.proxyObservable(component$, 'params');\n    proxy.queryParams = this.proxyObservable(component$, 'queryParams');\n    proxy.fragment = this.proxyObservable(component$, 'fragment');\n    proxy.data = this.proxyObservable(component$, 'data');\n    return proxy;\n  }\n  /**\n   * Create a wrapped observable that will switch to the latest activated route matched by the given component\n   */\n  proxyObservable(component$, path) {\n    return component$.pipe(\n    // First wait until the component instance is pushed\n    filter(component => !!component), switchMap(component => this.currentActivatedRoute$.pipe(filter(current => current !== null && current.component === component), switchMap(current => current && current.activatedRoute[path]), distinctUntilChanged())));\n  }\n  /**\n   * Updates the activated route proxy for the given component to the new incoming router state\n   */\n  updateActivatedRouteProxy(component, activatedRoute) {\n    const proxy = this.proxyMap.get(component);\n    if (!proxy) {\n      throw new Error(`Could not find activated route proxy for view`);\n    }\n    proxy._futureSnapshot = activatedRoute._futureSnapshot;\n    proxy._routerState = activatedRoute._routerState;\n    proxy.snapshot = activatedRoute.snapshot;\n    proxy.outlet = activatedRoute.outlet;\n    proxy.component = activatedRoute.component;\n    this.currentActivatedRoute$.next({\n      component,\n      activatedRoute\n    });\n  }\n  /** @nocollapse */\n}\n_IonRouterOutlet = IonRouterOutlet;\n_defineProperty(IonRouterOutlet, \"\\u0275fac\", function _IonRouterOutlet_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _IonRouterOutlet)(i0.ɵɵinjectAttribute('name'), i0.ɵɵinjectAttribute('tabs'), i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(_IonRouterOutlet, 12));\n});\n/** @nocollapse */\n_defineProperty(IonRouterOutlet, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _IonRouterOutlet,\n  selectors: [[\"ion-router-outlet\"]],\n  inputs: {\n    animated: \"animated\",\n    animation: \"animation\",\n    mode: \"mode\",\n    swipeGesture: \"swipeGesture\",\n    name: \"name\"\n  },\n  outputs: {\n    stackWillChange: \"stackWillChange\",\n    stackDidChange: \"stackDidChange\",\n    activateEvents: \"activate\",\n    deactivateEvents: \"deactivate\"\n  },\n  exportAs: [\"outlet\"],\n  standalone: false\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonRouterOutlet, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-router-outlet',\n      exportAs: 'outlet',\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: ['animated', 'animation', 'mode', 'swipeGesture']\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Attribute,\n        args: ['name']\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Attribute,\n        args: ['tabs']\n      }]\n    }, {\n      type: i1.Location\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Router\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.ActivatedRoute\n    }, {\n      type: IonRouterOutlet,\n      decorators: [{\n        type: SkipSelf\n      }, {\n        type: Optional\n      }]\n    }];\n  }, {\n    name: [{\n      type: Input\n    }],\n    stackWillChange: [{\n      type: Output\n    }],\n    stackDidChange: [{\n      type: Output\n    }],\n    activateEvents: [{\n      type: Output,\n      args: ['activate']\n    }],\n    deactivateEvents: [{\n      type: Output,\n      args: ['deactivate']\n    }]\n  });\n})();\nclass OutletInjector {\n  constructor(route, childContexts, parent) {\n    _defineProperty(this, \"route\", void 0);\n    _defineProperty(this, \"childContexts\", void 0);\n    _defineProperty(this, \"parent\", void 0);\n    this.route = route;\n    this.childContexts = childContexts;\n    this.parent = parent;\n  }\n  get(token, notFoundValue) {\n    if (token === ActivatedRoute) {\n      return this.route;\n    }\n    if (token === ChildrenOutletContexts) {\n      return this.childContexts;\n    }\n    return this.parent.get(token, notFoundValue);\n  }\n}\n// TODO: FW-4785 - Remove this once Angular 15 support is dropped\nconst INPUT_BINDER = new InjectionToken('');\n/**\n * Injectable used as a tree-shakable provider for opting in to binding router data to component\n * inputs.\n *\n * The RouterOutlet registers itself with this service when an `ActivatedRoute` is attached or\n * activated. When this happens, the service subscribes to the `ActivatedRoute` observables (params,\n * queryParams, data) and sets the inputs of the component using `ComponentRef.setInput`.\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. If it were not done this way, the previous information would be\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n *\n * The `RouterOutlet` should unregister itself when destroyed via `unsubscribeFromRouteData` so that\n * the subscriptions are cleaned up.\n */\nclass RoutedComponentInputBinder {\n  constructor() {\n    _defineProperty(this, \"outletDataSubscriptions\", new Map());\n  }\n  bindActivatedRouteToOutletComponent(outlet) {\n    this.unsubscribeFromRouteData(outlet);\n    this.subscribeToRouteData(outlet);\n  }\n  unsubscribeFromRouteData(outlet) {\n    var _this$outletDataSubsc;\n    (_this$outletDataSubsc = this.outletDataSubscriptions.get(outlet)) === null || _this$outletDataSubsc === void 0 || _this$outletDataSubsc.unsubscribe();\n    this.outletDataSubscriptions.delete(outlet);\n  }\n  subscribeToRouteData(outlet) {\n    const {\n      activatedRoute\n    } = outlet;\n    const dataSubscription = combineLatest([activatedRoute.queryParams, activatedRoute.params, activatedRoute.data]).pipe(switchMap(([queryParams, params, data], index) => {\n      data = {\n        ...queryParams,\n        ...params,\n        ...data\n      };\n      // Get the first result from the data subscription synchronously so it's available to\n      // the component as soon as possible (and doesn't require a second change detection).\n      if (index === 0) {\n        return of(data);\n      }\n      // Promise.resolve is used to avoid synchronously writing the wrong data when\n      // two of the Observables in the `combineLatest` stream emit one after\n      // another.\n      return Promise.resolve(data);\n    })).subscribe(data => {\n      // Outlet may have been deactivated or changed names to be associated with a different\n      // route\n      if (!outlet.isActivated || !outlet.activatedComponentRef || outlet.activatedRoute !== activatedRoute || activatedRoute.component === null) {\n        this.unsubscribeFromRouteData(outlet);\n        return;\n      }\n      const mirror = reflectComponentType(activatedRoute.component);\n      if (!mirror) {\n        this.unsubscribeFromRouteData(outlet);\n        return;\n      }\n      for (const {\n        templateName\n      } of mirror.inputs) {\n        outlet.activatedComponentRef.setInput(templateName, data[templateName]);\n      }\n    });\n    this.outletDataSubscriptions.set(outlet, dataSubscription);\n  }\n  /** @nocollapse */\n}\n_RoutedComponentInputBinder = RoutedComponentInputBinder;\n_defineProperty(RoutedComponentInputBinder, \"\\u0275fac\", function _RoutedComponentInputBinder_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _RoutedComponentInputBinder)();\n});\n/** @nocollapse */\n_defineProperty(RoutedComponentInputBinder, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _RoutedComponentInputBinder,\n  factory: _RoutedComponentInputBinder.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RoutedComponentInputBinder, [{\n    type: Injectable\n  }], null, null);\n})();\nconst provideComponentInputBinding = () => {\n  return {\n    provide: INPUT_BINDER,\n    useFactory: componentInputBindingFactory,\n    deps: [Router]\n  };\n};\nfunction componentInputBindingFactory(router) {\n  /**\n   * We cast the router to any here, since the componentInputBindingEnabled\n   * property is not available until Angular v16.\n   */\n  if (router !== null && router !== void 0 && router.componentInputBindingEnabled) {\n    return new RoutedComponentInputBinder();\n  }\n  return null;\n}\nconst BACK_BUTTON_INPUTS = ['color', 'defaultHref', 'disabled', 'icon', 'mode', 'routerAnimation', 'text', 'type'];\nlet IonBackButton = (_IonBackButton = class IonBackButton {\n  constructor(routerOutlet, navCtrl, config, r, z, c) {\n    _defineProperty(this, \"routerOutlet\", void 0);\n    _defineProperty(this, \"navCtrl\", void 0);\n    _defineProperty(this, \"config\", void 0);\n    _defineProperty(this, \"r\", void 0);\n    _defineProperty(this, \"z\", void 0);\n    _defineProperty(this, \"el\", void 0);\n    this.routerOutlet = routerOutlet;\n    this.navCtrl = navCtrl;\n    this.config = config;\n    this.r = r;\n    this.z = z;\n    c.detach();\n    this.el = this.r.nativeElement;\n  }\n  /**\n   * @internal\n   */\n  onClick(ev) {\n    var _this$routerOutlet;\n    const defaultHref = this.defaultHref || this.config.get('backButtonDefaultHref');\n    if ((_this$routerOutlet = this.routerOutlet) !== null && _this$routerOutlet !== void 0 && _this$routerOutlet.canGoBack()) {\n      this.navCtrl.setDirection('back', undefined, undefined, this.routerAnimation);\n      this.routerOutlet.pop();\n      ev.preventDefault();\n    } else if (defaultHref != null) {\n      this.navCtrl.navigateBack(defaultHref, {\n        animation: this.routerAnimation\n      });\n      ev.preventDefault();\n    }\n  }\n  /** @nocollapse */\n}, _defineProperty(_IonBackButton, \"\\u0275fac\", function _IonBackButton_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _IonBackButton)(i0.ɵɵdirectiveInject(IonRouterOutlet, 8), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(Config), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n}), _defineProperty(_IonBackButton, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _IonBackButton,\n  hostBindings: function _IonBackButton_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _IonBackButton_click_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      });\n    }\n  },\n  inputs: {\n    color: \"color\",\n    defaultHref: \"defaultHref\",\n    disabled: \"disabled\",\n    icon: \"icon\",\n    mode: \"mode\",\n    routerAnimation: \"routerAnimation\",\n    text: \"text\",\n    type: \"type\"\n  },\n  standalone: false\n})), _IonBackButton);\nIonBackButton = __decorate([ProxyCmp({\n  inputs: BACK_BUTTON_INPUTS\n})], IonBackButton);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonBackButton, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: BACK_BUTTON_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: IonRouterOutlet,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: NavController\n    }, {\n      type: Config\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\n\n/**\n * Adds support for Ionic routing directions and animations to the base Angular router link directive.\n *\n * When the router link is clicked, the directive will assign the direction and\n * animation so that the routing integration will transition correctly.\n */\nclass RouterLinkDelegateDirective {\n  constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n    _defineProperty(this, \"locationStrategy\", void 0);\n    _defineProperty(this, \"navCtrl\", void 0);\n    _defineProperty(this, \"elementRef\", void 0);\n    _defineProperty(this, \"router\", void 0);\n    _defineProperty(this, \"routerLink\", void 0);\n    _defineProperty(this, \"routerDirection\", 'forward');\n    _defineProperty(this, \"routerAnimation\", void 0);\n    this.locationStrategy = locationStrategy;\n    this.navCtrl = navCtrl;\n    this.elementRef = elementRef;\n    this.router = router;\n    this.routerLink = routerLink;\n  }\n  ngOnInit() {\n    this.updateTargetUrlAndHref();\n    this.updateTabindex();\n  }\n  ngOnChanges() {\n    this.updateTargetUrlAndHref();\n  }\n  /**\n   * The `tabindex` is set to `0` by default on the host element when\n   * the `routerLink` directive is used. This causes issues with Ionic\n   * components that wrap an `a` or `button` element, such as `ion-item`.\n   * See issue https://github.com/angular/angular/issues/28345\n   *\n   * This method removes the `tabindex` attribute from the host element\n   * to allow the Ionic component to manage the focus state correctly.\n   */\n  updateTabindex() {\n    // Ionic components that render a native anchor or button element\n    const ionicComponents = ['ION-BACK-BUTTON', 'ION-BREADCRUMB', 'ION-BUTTON', 'ION-CARD', 'ION-FAB-BUTTON', 'ION-ITEM', 'ION-ITEM-OPTION', 'ION-MENU-BUTTON', 'ION-SEGMENT-BUTTON', 'ION-TAB-BUTTON'];\n    const hostElement = this.elementRef.nativeElement;\n    if (ionicComponents.includes(hostElement.tagName)) {\n      if (hostElement.getAttribute('tabindex') === '0') {\n        hostElement.removeAttribute('tabindex');\n      }\n    }\n  }\n  updateTargetUrlAndHref() {\n    var _this$routerLink;\n    if ((_this$routerLink = this.routerLink) !== null && _this$routerLink !== void 0 && _this$routerLink.urlTree) {\n      const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n      this.elementRef.nativeElement.href = href;\n    }\n  }\n  /**\n   * @internal\n   */\n  onClick(ev) {\n    this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n    /**\n     * This prevents the browser from\n     * performing a page reload when pressing\n     * an Ionic component with routerLink.\n     * The page reload interferes with routing\n     * and causes ion-back-button to disappear\n     * since the local history is wiped on reload.\n     */\n    ev.preventDefault();\n  }\n  /** @nocollapse */\n}\n_RouterLinkDelegateDirective = RouterLinkDelegateDirective;\n_defineProperty(RouterLinkDelegateDirective, \"\\u0275fac\", function _RouterLinkDelegateDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _RouterLinkDelegateDirective)(i0.ɵɵdirectiveInject(i1.LocationStrategy), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.RouterLink, 8));\n});\n/** @nocollapse */\n_defineProperty(RouterLinkDelegateDirective, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _RouterLinkDelegateDirective,\n  selectors: [[\"\", \"routerLink\", \"\", 5, \"a\", 5, \"area\"]],\n  hostBindings: function _RouterLinkDelegateDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _RouterLinkDelegateDirective_click_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      });\n    }\n  },\n  inputs: {\n    routerDirection: \"routerDirection\",\n    routerAnimation: \"routerAnimation\"\n  },\n  standalone: false,\n  features: [i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLinkDelegateDirective, [{\n    type: Directive,\n    args: [{\n      selector: ':not(a):not(area)[routerLink]'\n    }]\n  }], function () {\n    return [{\n      type: i1.LocationStrategy\n    }, {\n      type: NavController\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Router\n    }, {\n      type: i3.RouterLink,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    routerDirection: [{\n      type: Input\n    }],\n    routerAnimation: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }]\n  });\n})();\nclass RouterLinkWithHrefDelegateDirective {\n  constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n    _defineProperty(this, \"locationStrategy\", void 0);\n    _defineProperty(this, \"navCtrl\", void 0);\n    _defineProperty(this, \"elementRef\", void 0);\n    _defineProperty(this, \"router\", void 0);\n    _defineProperty(this, \"routerLink\", void 0);\n    _defineProperty(this, \"routerDirection\", 'forward');\n    _defineProperty(this, \"routerAnimation\", void 0);\n    this.locationStrategy = locationStrategy;\n    this.navCtrl = navCtrl;\n    this.elementRef = elementRef;\n    this.router = router;\n    this.routerLink = routerLink;\n  }\n  ngOnInit() {\n    this.updateTargetUrlAndHref();\n  }\n  ngOnChanges() {\n    this.updateTargetUrlAndHref();\n  }\n  updateTargetUrlAndHref() {\n    var _this$routerLink2;\n    if ((_this$routerLink2 = this.routerLink) !== null && _this$routerLink2 !== void 0 && _this$routerLink2.urlTree) {\n      const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n      this.elementRef.nativeElement.href = href;\n    }\n  }\n  /**\n   * @internal\n   */\n  onClick() {\n    this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n  }\n  /** @nocollapse */\n}\n_RouterLinkWithHrefDelegateDirective = RouterLinkWithHrefDelegateDirective;\n_defineProperty(RouterLinkWithHrefDelegateDirective, \"\\u0275fac\", function _RouterLinkWithHrefDelegateDirective_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _RouterLinkWithHrefDelegateDirective)(i0.ɵɵdirectiveInject(i1.LocationStrategy), i0.ɵɵdirectiveInject(NavController), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.RouterLink, 8));\n});\n/** @nocollapse */\n_defineProperty(RouterLinkWithHrefDelegateDirective, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _RouterLinkWithHrefDelegateDirective,\n  selectors: [[\"a\", \"routerLink\", \"\"], [\"area\", \"routerLink\", \"\"]],\n  hostBindings: function _RouterLinkWithHrefDelegateDirective_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function _RouterLinkWithHrefDelegateDirective_click_HostBindingHandler() {\n        return ctx.onClick();\n      });\n    }\n  },\n  inputs: {\n    routerDirection: \"routerDirection\",\n    routerAnimation: \"routerAnimation\"\n  },\n  standalone: false,\n  features: [i0.ɵɵNgOnChangesFeature]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterLinkWithHrefDelegateDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'a[routerLink],area[routerLink]'\n    }]\n  }], function () {\n    return [{\n      type: i1.LocationStrategy\n    }, {\n      type: NavController\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i3.Router\n    }, {\n      type: i3.RouterLink,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, {\n    routerDirection: [{\n      type: Input\n    }],\n    routerAnimation: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }]\n  });\n})();\nconst NAV_INPUTS = ['animated', 'animation', 'root', 'rootParams', 'swipeGesture'];\nconst NAV_METHODS = ['push', 'insert', 'insertPages', 'pop', 'popTo', 'popToRoot', 'removeIndex', 'setRoot', 'setPages', 'getActive', 'getByIndex', 'canGoBack', 'getPrevious'];\nlet IonNav = (_IonNav = class IonNav {\n  constructor(ref, environmentInjector, injector, angularDelegate, z, c) {\n    _defineProperty(this, \"z\", void 0);\n    _defineProperty(this, \"el\", void 0);\n    this.z = z;\n    c.detach();\n    this.el = ref.nativeElement;\n    ref.nativeElement.delegate = angularDelegate.create(environmentInjector, injector);\n    proxyOutputs(this, this.el, ['ionNavDidChange', 'ionNavWillChange']);\n  }\n  /** @nocollapse */\n}, _defineProperty(_IonNav, \"\\u0275fac\", function _IonNav_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _IonNav)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.EnvironmentInjector), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(AngularDelegate), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n}), _defineProperty(_IonNav, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _IonNav,\n  inputs: {\n    animated: \"animated\",\n    animation: \"animation\",\n    root: \"root\",\n    rootParams: \"rootParams\",\n    swipeGesture: \"swipeGesture\"\n  },\n  standalone: false\n})), _IonNav);\nIonNav = __decorate([ProxyCmp({\n  inputs: NAV_INPUTS,\n  methods: NAV_METHODS\n})], IonNav);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonNav, [{\n    type: Directive,\n    args: [{\n      // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n      inputs: NAV_INPUTS\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.EnvironmentInjector\n    }, {\n      type: i0.Injector\n    }, {\n      type: AngularDelegate\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, null);\n})();\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonTabs {\n  constructor(navCtrl) {\n    _defineProperty(this, \"navCtrl\", void 0);\n    _defineProperty(this, \"tabsInner\", void 0);\n    /**\n     * Emitted before the tab view is changed.\n     */\n    _defineProperty(this, \"ionTabsWillChange\", new EventEmitter());\n    /**\n     * Emitted after the tab view is changed.\n     */\n    _defineProperty(this, \"ionTabsDidChange\", new EventEmitter());\n    _defineProperty(this, \"tabBarSlot\", 'bottom');\n    _defineProperty(this, \"hasTab\", false);\n    _defineProperty(this, \"selectedTab\", void 0);\n    _defineProperty(this, \"leavingTab\", void 0);\n    this.navCtrl = navCtrl;\n  }\n  ngAfterViewInit() {\n    /**\n     * Developers must pass at least one ion-tab\n     * inside of ion-tabs if they want to use a\n     * basic tab-based navigation without the\n     * history stack or URL updates associated\n     * with the router.\n     */\n    const firstTab = this.tabs.length > 0 ? this.tabs.first : undefined;\n    if (firstTab) {\n      this.hasTab = true;\n      this.setActiveTab(firstTab.tab);\n      this.tabSwitch();\n    }\n  }\n  ngAfterContentInit() {\n    this.detectSlotChanges();\n  }\n  ngAfterContentChecked() {\n    this.detectSlotChanges();\n  }\n  /**\n   * @internal\n   */\n  onStackWillChange({\n    enteringView,\n    tabSwitch\n  }) {\n    const stackId = enteringView.stackId;\n    if (tabSwitch && stackId !== undefined) {\n      this.ionTabsWillChange.emit({\n        tab: stackId\n      });\n    }\n  }\n  /**\n   * @internal\n   */\n  onStackDidChange({\n    enteringView,\n    tabSwitch\n  }) {\n    const stackId = enteringView.stackId;\n    if (tabSwitch && stackId !== undefined) {\n      if (this.tabBar) {\n        this.tabBar.selectedTab = stackId;\n      }\n      this.ionTabsDidChange.emit({\n        tab: stackId\n      });\n    }\n  }\n  /**\n   * When a tab button is clicked, there are several scenarios:\n   * 1. If the selected tab is currently active (the tab button has been clicked\n   *    again), then it should go to the root view for that tab.\n   *\n   *   a. Get the saved root view from the router outlet. If the saved root view\n   *      matches the tabRootUrl, set the route view to this view including the\n   *      navigation extras.\n   *   b. If the saved root view from the router outlet does\n   *      not match, navigate to the tabRootUrl. No navigation extras are\n   *      included.\n   *\n   * 2. If the current tab tab is not currently selected, get the last route\n   *    view from the router outlet.\n   *\n   *   a. If the last route view exists, navigate to that view including any\n   *      navigation extras\n   *   b. If the last route view doesn't exist, then navigate\n   *      to the default tabRootUrl\n   */\n  select(tabOrEvent) {\n    const isTabString = typeof tabOrEvent === 'string';\n    const tab = isTabString ? tabOrEvent : tabOrEvent.detail.tab;\n    /**\n     * If the tabs are not using the router, then\n     * the tab switch logic is handled by the tabs\n     * component itself.\n     */\n    if (this.hasTab) {\n      this.setActiveTab(tab);\n      this.tabSwitch();\n      return;\n    }\n    const alreadySelected = this.outlet.getActiveStackId() === tab;\n    const tabRootUrl = `${this.outlet.tabsPrefix}/${tab}`;\n    /**\n     * If this is a nested tab, prevent the event\n     * from bubbling otherwise the outer tabs\n     * will respond to this event too, causing\n     * the app to get directed to the wrong place.\n     */\n    if (!isTabString) {\n      tabOrEvent.stopPropagation();\n    }\n    if (alreadySelected) {\n      const activeStackId = this.outlet.getActiveStackId();\n      const activeView = this.outlet.getLastRouteView(activeStackId);\n      // If on root tab, do not navigate to root tab again\n      if ((activeView === null || activeView === void 0 ? void 0 : activeView.url) === tabRootUrl) {\n        return;\n      }\n      const rootView = this.outlet.getRootView(tab);\n      const navigationExtras = rootView && tabRootUrl === rootView.url && rootView.savedExtras;\n      return this.navCtrl.navigateRoot(tabRootUrl, {\n        ...navigationExtras,\n        animated: true,\n        animationDirection: 'back'\n      });\n    } else {\n      const lastRoute = this.outlet.getLastRouteView(tab);\n      /**\n       * If there is a lastRoute, goto that, otherwise goto the fallback url of the\n       * selected tab\n       */\n      const url = (lastRoute === null || lastRoute === void 0 ? void 0 : lastRoute.url) || tabRootUrl;\n      const navigationExtras = lastRoute === null || lastRoute === void 0 ? void 0 : lastRoute.savedExtras;\n      return this.navCtrl.navigateRoot(url, {\n        ...navigationExtras,\n        animated: true,\n        animationDirection: 'back'\n      });\n    }\n  }\n  setActiveTab(tab) {\n    const tabs = this.tabs;\n    const selectedTab = tabs.find(t => t.tab === tab);\n    if (!selectedTab) {\n      console.error(`[Ionic Error]: Tab with id: \"${tab}\" does not exist`);\n      return;\n    }\n    this.leavingTab = this.selectedTab;\n    this.selectedTab = selectedTab;\n    this.ionTabsWillChange.emit({\n      tab\n    });\n    selectedTab.el.active = true;\n  }\n  tabSwitch() {\n    const {\n      selectedTab,\n      leavingTab\n    } = this;\n    if (this.tabBar && selectedTab) {\n      this.tabBar.selectedTab = selectedTab.tab;\n    }\n    if ((leavingTab === null || leavingTab === void 0 ? void 0 : leavingTab.tab) !== (selectedTab === null || selectedTab === void 0 ? void 0 : selectedTab.tab)) {\n      if (leavingTab !== null && leavingTab !== void 0 && leavingTab.el) {\n        leavingTab.el.active = false;\n      }\n    }\n    if (selectedTab) {\n      this.ionTabsDidChange.emit({\n        tab: selectedTab.tab\n      });\n    }\n  }\n  getSelected() {\n    if (this.hasTab) {\n      var _this$selectedTab;\n      return (_this$selectedTab = this.selectedTab) === null || _this$selectedTab === void 0 ? void 0 : _this$selectedTab.tab;\n    }\n    return this.outlet.getActiveStackId();\n  }\n  /**\n   * Detects changes to the slot attribute of the tab bar.\n   *\n   * If the slot attribute has changed, then the tab bar\n   * should be relocated to the new slot position.\n   */\n  detectSlotChanges() {\n    this.tabBars.forEach(tabBar => {\n      // el is a protected attribute from the generated component wrapper\n      const currentSlot = tabBar.el.getAttribute('slot');\n      if (currentSlot !== this.tabBarSlot) {\n        this.tabBarSlot = currentSlot;\n        this.relocateTabBar();\n      }\n    });\n  }\n  /**\n   * Relocates the tab bar to the new slot position.\n   */\n  relocateTabBar() {\n    /**\n     * `el` is a protected attribute from the generated component wrapper.\n     * To avoid having to manually create the wrapper for tab bar, we\n     * cast the tab bar to any and access the protected attribute.\n     */\n    const tabBar = this.tabBar.el;\n    if (this.tabBarSlot === 'top') {\n      /**\n       * A tab bar with a slot of \"top\" should be inserted\n       * at the top of the container.\n       */\n      this.tabsInner.nativeElement.before(tabBar);\n    } else {\n      /**\n       * A tab bar with a slot of \"bottom\" or without a slot\n       * should be inserted at the end of the container.\n       */\n      this.tabsInner.nativeElement.after(tabBar);\n    }\n  }\n  /** @nocollapse */\n}\n_IonTabs = IonTabs;\n_defineProperty(IonTabs, \"\\u0275fac\", function _IonTabs_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _IonTabs)(i0.ɵɵdirectiveInject(NavController));\n});\n/** @nocollapse */\n_defineProperty(IonTabs, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _IonTabs,\n  selectors: [[\"ion-tabs\"]],\n  viewQuery: function _IonTabs_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7, ElementRef);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabsInner = _t.first);\n    }\n  },\n  hostBindings: function _IonTabs_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"ionTabButtonClick\", function _IonTabs_ionTabButtonClick_HostBindingHandler($event) {\n        return ctx.select($event);\n      });\n    }\n  },\n  outputs: {\n    ionTabsWillChange: \"ionTabsWillChange\",\n    ionTabsDidChange: \"ionTabsDidChange\"\n  },\n  standalone: false\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IonTabs, [{\n    type: Directive,\n    args: [{\n      selector: 'ion-tabs'\n    }]\n  }], function () {\n    return [{\n      type: NavController\n    }];\n  }, {\n    tabsInner: [{\n      type: ViewChild,\n      args: ['tabsInner', {\n        read: ElementRef,\n        static: true\n      }]\n    }],\n    ionTabsWillChange: [{\n      type: Output\n    }],\n    ionTabsDidChange: [{\n      type: Output\n    }],\n    select: [{\n      type: HostListener,\n      args: ['ionTabButtonClick', ['$event']]\n    }]\n  });\n})();\nconst raf = h => {\n  if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n    return __zone_symbol__requestAnimationFrame(h);\n  }\n  if (typeof requestAnimationFrame === 'function') {\n    return requestAnimationFrame(h);\n  }\n  return setTimeout(h);\n};\n\n// TODO(FW-2827): types\nclass ValueAccessor {\n  constructor(injector, elementRef) {\n    _defineProperty(this, \"injector\", void 0);\n    _defineProperty(this, \"elementRef\", void 0);\n    _defineProperty(this, \"onChange\", () => {\n      /**/\n    });\n    _defineProperty(this, \"onTouched\", () => {\n      /**/\n    });\n    _defineProperty(this, \"lastValue\", void 0);\n    _defineProperty(this, \"statusChanges\", void 0);\n    this.injector = injector;\n    this.elementRef = elementRef;\n  }\n  writeValue(value) {\n    this.elementRef.nativeElement.value = this.lastValue = value;\n    setIonicClasses(this.elementRef);\n  }\n  /**\n   * Notifies the ControlValueAccessor of a change in the value of the control.\n   *\n   * This is called by each of the ValueAccessor directives when we want to update\n   * the status and validity of the form control. For example with text components this\n   * is called when the ionInput event is fired. For select components this is called\n   * when the ionChange event is fired.\n   *\n   * This also updates the Ionic form status classes on the element.\n   *\n   * @param el The component element.\n   * @param value The new value of the control.\n   */\n  handleValueChange(el, value) {\n    if (el === this.elementRef.nativeElement) {\n      if (value !== this.lastValue) {\n        this.lastValue = value;\n        this.onChange(value);\n      }\n      setIonicClasses(this.elementRef);\n    }\n  }\n  _handleBlurEvent(el) {\n    if (el === this.elementRef.nativeElement) {\n      this.onTouched();\n      setIonicClasses(this.elementRef);\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.elementRef.nativeElement.disabled = isDisabled;\n  }\n  ngOnDestroy() {\n    if (this.statusChanges) {\n      this.statusChanges.unsubscribe();\n    }\n  }\n  ngAfterViewInit() {\n    let ngControl;\n    try {\n      ngControl = this.injector.get(NgControl);\n    } catch {\n      /* No FormControl or ngModel binding */\n    }\n    if (!ngControl) {\n      return;\n    }\n    // Listen for changes in validity, disabled, or pending states\n    if (ngControl.statusChanges) {\n      this.statusChanges = ngControl.statusChanges.subscribe(() => setIonicClasses(this.elementRef));\n    }\n    /**\n     * TODO FW-2787: Remove this in favor of https://github.com/angular/angular/issues/10887\n     * whenever it is implemented.\n     */\n    const formControl = ngControl.control;\n    if (formControl) {\n      const methodsToPatch = ['markAsTouched', 'markAllAsTouched', 'markAsUntouched', 'markAsDirty', 'markAsPristine'];\n      methodsToPatch.forEach(method => {\n        if (typeof formControl[method] !== 'undefined') {\n          const oldFn = formControl[method].bind(formControl);\n          formControl[method] = (...params) => {\n            oldFn(...params);\n            setIonicClasses(this.elementRef);\n          };\n        }\n      });\n    }\n  }\n  /** @nocollapse */\n}\n_ValueAccessor = ValueAccessor;\n_defineProperty(ValueAccessor, \"\\u0275fac\", function _ValueAccessor_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _ValueAccessor)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ElementRef));\n});\n/** @nocollapse */\n_defineProperty(ValueAccessor, \"\\u0275dir\", /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: _ValueAccessor,\n  hostBindings: function _ValueAccessor_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"ionBlur\", function _ValueAccessor_ionBlur_HostBindingHandler($event) {\n        return ctx._handleBlurEvent($event.target);\n      });\n    }\n  },\n  standalone: false\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ValueAccessor, [{\n    type: Directive\n  }], function () {\n    return [{\n      type: i0.Injector\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    _handleBlurEvent: [{\n      type: HostListener,\n      args: ['ionBlur', ['$event.target']]\n    }]\n  });\n})();\nconst setIonicClasses = element => {\n  raf(() => {\n    const input = element.nativeElement;\n    const hasValue = input.value != null && input.value.toString().length > 0;\n    const classes = getClasses(input);\n    setClasses(input, classes);\n    const item = input.closest('ion-item');\n    if (item) {\n      if (hasValue) {\n        setClasses(item, [...classes, 'item-has-value']);\n      } else {\n        setClasses(item, classes);\n      }\n    }\n  });\n};\nconst getClasses = element => {\n  const classList = element.classList;\n  const classes = [];\n  for (let i = 0; i < classList.length; i++) {\n    const item = classList.item(i);\n    if (item !== null && startsWith(item, 'ng-')) {\n      classes.push(`ion-${item.substring(3)}`);\n    }\n  }\n  return classes;\n};\nconst setClasses = (element, classes) => {\n  const classList = element.classList;\n  classList.remove('ion-valid', 'ion-invalid', 'ion-touched', 'ion-untouched', 'ion-dirty', 'ion-pristine');\n  classList.add(...classes);\n};\nconst startsWith = (input, search) => {\n  return input.substring(0, search.length) === search;\n};\n\n/**\n * Provides a way to customize when activated routes get reused.\n */\nclass IonicRouteStrategy {\n  /**\n   * Whether the given route should detach for later reuse.\n   */\n  shouldDetach(_route) {\n    return false;\n  }\n  /**\n   * Returns `false`, meaning the route (and its subtree) is never reattached\n   */\n  shouldAttach(_route) {\n    return false;\n  }\n  /**\n   * A no-op; the route is never stored since this strategy never detaches routes for later re-use.\n   */\n  store(_route, _detachedTree) {\n    return;\n  }\n  /**\n   * Returns `null` because this strategy does not store routes for later re-use.\n   */\n  retrieve(_route) {\n    return null;\n  }\n  /**\n   * Determines if a route should be reused.\n   * This strategy returns `true` when the future route config and\n   * current route config are identical and all route parameters are identical.\n   */\n  shouldReuseRoute(future, curr) {\n    if (future.routeConfig !== curr.routeConfig) {\n      return false;\n    }\n    // checking router params\n    const futureParams = future.params;\n    const currentParams = curr.params;\n    const keysA = Object.keys(futureParams);\n    const keysB = Object.keys(currentParams);\n    if (keysA.length !== keysB.length) {\n      return false;\n    }\n    // Test for A's keys different from B.\n    for (const key of keysA) {\n      if (currentParams[key] !== futureParams[key]) {\n        return false;\n      }\n    }\n    return true;\n  }\n}\n\n// TODO(FW-2827): types\nclass OverlayBaseController {\n  constructor(ctrl) {\n    _defineProperty(this, \"ctrl\", void 0);\n    this.ctrl = ctrl;\n  }\n  /**\n   * Creates a new overlay\n   */\n  create(opts) {\n    return this.ctrl.create(opts || {});\n  }\n  /**\n   * When `id` is not provided, it dismisses the top overlay.\n   */\n  dismiss(data, role, id) {\n    return this.ctrl.dismiss(data, role, id);\n  }\n  /**\n   * Returns the top overlay.\n   */\n  getTop() {\n    return this.ctrl.getTop();\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularDelegate, Config, ConfigToken, DomController, IonBackButton, IonModal, IonNav, IonPopover, IonRouterOutlet, IonTabs, IonicRouteStrategy, MenuController, NavController, NavParams, OverlayBaseController, Platform, ProxyCmp, RouterLinkDelegateDirective, RouterLinkWithHrefDelegateDirective, ValueAccessor, bindLifecycleEvents, provideComponentInputBinding, raf, setIonicClasses };", "map": {"version": 3, "names": ["i0", "Injectable", "Inject", "Optional", "InjectionToken", "inject", "NgZone", "ApplicationRef", "Injector", "createComponent", "TemplateRef", "Directive", "ContentChild", "EventEmitter", "ViewContainerRef", "EnvironmentInjector", "Attribute", "SkipSelf", "Input", "Output", "reflectComponentType", "HostListener", "ElementRef", "ViewChild", "i3", "NavigationStart", "PRIMARY_OUTLET", "ChildrenOutletContexts", "ActivatedRoute", "Router", "i1", "DOCUMENT", "isPlatform", "getPlatforms", "LIFECYCLE_WILL_ENTER", "LIFECYCLE_DID_ENTER", "LIFECYCLE_WILL_LEAVE", "LIFECYCLE_DID_LEAVE", "LIFECYCLE_WILL_UNLOAD", "componentOnReady", "Subject", "fromEvent", "BehaviorSubject", "combineLatest", "of", "__decorate", "filter", "switchMap", "distinctUntilChanged", "NgControl", "MenuController", "constructor", "menuController", "_defineProperty", "open", "menuId", "close", "toggle", "enable", "shouldEnable", "swipeGesture", "isOpen", "isEnabled", "get", "get<PERSON>pen", "getMenus", "registerAnimation", "name", "animation", "isAnimating", "_getOpenSync", "_createAnimation", "type", "menuCmp", "_register", "menu", "_unregister", "_setOpen", "shouldOpen", "animated", "DomController", "read", "cb", "getQueue", "write", "_DomController", "_DomController_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "providedIn", "ngDevMode", "ɵsetClassMetadata", "args", "win", "window", "<PERSON><PERSON>", "queue", "requestAnimationFrame", "Platform", "doc", "zone", "run", "_this$win", "defaultView", "backButton", "subscribeWithPriority", "priority", "callback", "subscribe", "ev", "register", "processNextHandler", "proxyEvent", "pause", "resume", "resize", "keyboardDidShow", "keyboardDidHide", "readyResolve", "_readyPromise", "Promise", "res", "addEventListener", "once", "is", "platformName", "platforms", "ready", "isRTL", "dir", "getQueryParam", "key", "readQueryParam", "location", "href", "isLandscape", "isPortrait", "_this$win$matchMedia", "_this$win2", "matchMedia", "call", "matches", "testUserAgent", "expression", "nav", "navigator", "userAgent", "indexOf", "url", "width", "innerWidth", "height", "innerHeight", "_Platform", "_Platform_Factory", "ɵɵinject", "undefined", "decorators", "replace", "regex", "RegExp", "results", "exec", "decodeURIComponent", "emitter", "el", "eventName", "value", "detail", "next", "NavController", "platform", "serializer", "router", "DEFAULT_DIRECTION", "DEFAULT_ANIMATED", "events", "id", "restoredState", "navigationId", "guessDirection", "guessAnimation", "lastNavId", "pop", "navigateForward", "options", "setDirection", "animationDirection", "navigate", "navigateBack", "navigateRoot", "back", "_this", "_asyncToGenerator", "outlet", "topOutlet", "parentOutlet", "direction", "animationBuilder", "getAnimation", "setTopOutlet", "consumeTransition", "Array", "isArray", "urlTree", "parse", "toString", "queryParams", "fragment", "navigateByUrl", "_NavController", "_NavController_Factory", "Location", "UrlSerializer", "Config", "fallback", "c", "getConfig", "getBoolean", "getNumber", "_Config", "_Config_Factory", "ConfigToken", "config", "NavParams", "data", "console", "warn", "param", "AngularDelegate", "create", "environmentInjector", "injector", "elementReferenceKey", "_this$config$useSetIn", "AngularFrameworkDelegate", "applicationRef", "useSetInputAPI", "_AngularDelegate", "_AngularDelegate_Factory", "enableSignalsSupport", "WeakMap", "attachViewToDom", "container", "component", "params", "cssClasses", "resolve", "componentProps", "attachView", "elRefMap", "elEventsMap", "removeViewFromDom", "_container", "componentRef", "destroy", "delete", "unbindEvents", "childInjector", "providers", "getProviders", "parent", "elementInjector", "instance", "hostElement", "nativeElement", "error", "tagName", "toLowerCase", "setInput", "modal", "popover", "otherParams", "Object", "assign", "cssClass", "classList", "add", "bindLifecycleEvents", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "set", "LIFECYCLES", "element", "unregisters", "map", "handler", "removeEventListener", "for<PERSON>ach", "fn", "NavParamsToken", "provide", "useValue", "useFactory", "provideNavParamsInjectable", "deps", "proxyInputs", "Cmp", "inputs", "Prototype", "prototype", "item", "defineProperty", "val", "z", "runOutsideAngular", "proxyMethods", "methods", "methodName", "arguments", "apply", "proxyOutputs", "ProxyCmp", "opts", "decorator", "cls", "defineCustomElementFn", "POPOVER_INPUTS", "POPOVER_METHODS", "IonPopover", "_IonPopover", "r", "isCmpOpen", "detectChanges", "_IonPopover_Factory", "ɵɵdirectiveInject", "ChangeDetectorRef", "ɵɵdefineDirective", "selectors", "contentQueries", "_IonPopover_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "template", "first", "alignment", "arrow", "keepContentsMounted", "<PERSON><PERSON><PERSON><PERSON>", "dismissOnSelect", "enterAnimation", "event", "focusTrap", "keyboardClose", "leaveAnimation", "mode", "showBackdrop", "translucent", "trigger", "triggerAction", "reference", "size", "side", "standalone", "selector", "static", "MODAL_INPUTS", "MODAL_METHODS", "IonModal", "_IonModal", "_IonModal_Factory", "_IonModal_ContentQueries", "backdropBreakpoint", "breakpoints", "<PERSON><PERSON><PERSON><PERSON>", "handle", "handleBehavior", "initialBreakpoint", "presentingElement", "insertView", "views", "view", "setRoot", "setForward", "setBack", "v", "stackId", "push", "index", "getUrl", "activatedRoute", "createUrlTree", "relativeTo", "serializeUrl", "isTabSwitch", "enteringView", "leavingView", "computeStackId", "prefixUrl", "segments", "toSegments", "i", "length", "path", "split", "s", "trim", "destroyView", "ref", "unlistenEvents", "StackController", "tabsPrefix", "containerEl", "navCtrl", "createView", "_ref$location", "nextId", "getExistingView", "activatedUrlKey", "find", "vw", "changeDetectorRef", "reattach", "setActive", "_router$navigations", "_currentNavigation", "consumeResult", "activeView", "tabSwitch", "viewsSnapshot", "slice", "currentNavigation", "getCurrentNavigation", "navigations", "extras", "replaceUrl", "splice", "reused", "includes", "customAnimation", "wait", "detach", "transition", "canGoBack", "then", "cleanupAsync", "deep", "getActiveStackId", "getStack", "viewSavedData", "savedData", "_primaryOutlet$route", "primaryOutlet", "route", "_routerState", "snapshot", "savedExtras", "startBackTransition", "endBackTransition", "shouldComplete", "skipTransition", "cleanup", "getLastUrl", "getRootUrl", "getActiveView", "hasRunningTask", "runningTask", "showGoBack", "progressAnimation", "enteringEl", "leavingEl", "commit", "duration", "task", "_this2", "promise", "finally", "activeRoute", "locationWithoutParams", "locationWithoutFragment", "setAttribute", "IonRouterOutlet", "activatedComponentRef", "activated", "nativeEl", "swipe", "_swipeGesture", "swi<PERSON><PERSON><PERSON><PERSON>", "canStart", "stackCtrl", "onStart", "onEnd", "shouldC<PERSON><PERSON>ue", "tabs", "commonLocation", "elementRef", "INPUT_BINDER", "optional", "parentContexts", "onChildOutletCreated", "ngOnDestroy", "_this$inputBinder", "inputBinder", "unsubscribeFromRouteData", "getContext", "ngOnInit", "initializeOutletWithName", "context", "activateWith", "isActivated", "Error", "_activatedRoute", "activatedRouteData", "attach", "_ref", "deactivate", "activatedView", "Map", "children", "contextSnapshot", "deactivateEvents", "emit", "_this$inputBinder2", "cmpRef", "saved", "updateActivatedRouteProxy", "_snapshot$routeConfig", "_futureSnapshot", "childContexts", "getOrCreateContext", "component$", "activatedRouteProxy", "createActivatedRouteProxy", "OutletInjector", "routeConfig", "outletContent", "proxyMap", "currentActivatedRoute$", "bindActivatedRouteToOutletComponent", "stackWillChange", "activateEvents", "stackDidChange", "active", "getLastRouteView", "getRootView", "proxy", "_paramMap", "proxyObservable", "_queryParamMap", "pipe", "current", "_IonRouterOutlet", "_IonRouterOutlet_Factory", "ɵɵinjectAttribute", "outputs", "exportAs", "notFoundValue", "RoutedComponentInputBinder", "subscribeToRouteData", "_this$outletDataSubsc", "outletDataSubscriptions", "unsubscribe", "dataSubscription", "mirror", "templateName", "_RoutedComponentInputBinder", "_RoutedComponentInputBinder_Factory", "provideComponentInputBinding", "componentInputBindingFactory", "componentInputBindingEnabled", "BACK_BUTTON_INPUTS", "IonBackButton", "_IonBackButton", "routerOutlet", "onClick", "_this$routerOutlet", "defaultHref", "routerAnimation", "preventDefault", "_IonBackButton_Factory", "hostBindings", "_IonBackButton_HostBindings", "ɵɵlistener", "_IonBackButton_click_HostBindingHandler", "$event", "color", "disabled", "icon", "text", "RouterLinkDelegateDirective", "locationStrategy", "routerLink", "updateTargetUrlAndHref", "updateTabindex", "ngOnChanges", "ionicComponents", "getAttribute", "removeAttribute", "_this$routerLink", "prepareExternalUrl", "routerDirection", "_RouterLinkDelegateDirective", "_RouterLinkDelegateDirective_Factory", "LocationStrategy", "RouterLink", "_RouterLinkDelegateDirective_HostBindings", "_RouterLinkDelegateDirective_click_HostBindingHandler", "features", "ɵɵNgOnChangesFeature", "RouterLinkWithHrefDelegateDirective", "_this$routerLink2", "_RouterLinkWithHrefDelegateDirective", "_RouterLinkWithHrefDelegateDirective_Factory", "_RouterLinkWithHrefDelegateDirective_HostBindings", "_RouterLinkWithHrefDelegateDirective_click_HostBindingHandler", "NAV_INPUTS", "NAV_METHODS", "IonNav", "_IonNav", "angularDelegate", "delegate", "_IonNav_Factory", "root", "rootParams", "IonTabs", "ngAfterViewInit", "firstTab", "hasTab", "setActiveTab", "tab", "ngAfterContentInit", "detectSlotChanges", "ngAfterContentChecked", "onStackWillChange", "ionTabsWillChange", "onStackDidChange", "tabBar", "selectedTab", "ionTabsDidChange", "select", "tabOrEvent", "isTabString", "alreadySelected", "tabRootUrl", "stopPropagation", "activeStackId", "rootView", "navigationExtras", "lastRoute", "t", "leavingTab", "getSelected", "_this$selectedTab", "tabBars", "currentSlot", "tabBarSlot", "relocateTabBar", "tabsInner", "before", "after", "_IonTabs", "_IonTabs_Factory", "viewQuery", "_IonTabs_Query", "ɵɵviewQuery", "_c0", "_IonTabs_HostBindings", "_IonTabs_ionTabButtonClick_HostBindingHandler", "raf", "h", "__zone_symbol__requestAnimationFrame", "setTimeout", "ValueAccessor", "writeValue", "lastValue", "setIonicClasses", "handleValueChange", "onChange", "_handleBlurEvent", "onTouched", "registerOnChange", "registerOnTouched", "setDisabledState", "isDisabled", "statusChanges", "ngControl", "formControl", "control", "methodsToPatch", "method", "oldFn", "bind", "_ValueAccessor", "_ValueAccessor_Factory", "_ValueAccessor_HostBindings", "_ValueAccessor_ionBlur_HostBindingHandler", "target", "input", "hasValue", "classes", "getClasses", "setClasses", "closest", "startsWith", "substring", "remove", "search", "IonicRouteStrategy", "<PERSON><PERSON><PERSON><PERSON>", "_route", "<PERSON><PERSON><PERSON><PERSON>", "store", "_detachedTree", "retrieve", "shouldReuseRoute", "future", "curr", "futureParams", "currentParams", "keysA", "keys", "keysB", "OverlayBaseController", "ctrl", "dismiss", "role", "getTop"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/angular/fesm2022/ionic-angular-common.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Inject, Optional, InjectionToken, inject, NgZone, ApplicationRef, Injector, createComponent, TemplateRef, Directive, ContentChild, EventEmitter, ViewContainerRef, EnvironmentInjector, Attribute, SkipSelf, Input, Output, reflectComponentType, HostListener, ElementRef, ViewChild } from '@angular/core';\nimport * as i3 from '@angular/router';\nimport { NavigationStart, PRIMARY_OUTLET, ChildrenOutletContexts, ActivatedRoute, Router } from '@angular/router';\nimport * as i1 from '@angular/common';\nimport { DOCUMENT } from '@angular/common';\nimport { isPlatform, getPlatforms, LIFECYCLE_WILL_ENTER, LIFECYCLE_DID_ENTER, LIFECYCLE_WILL_LEAVE, LIFECYCLE_DID_LEAVE, LIFECYCLE_WILL_UNLOAD, componentOnReady } from '@ionic/core/components';\nimport { Subject, fromEvent, BehaviorSubject, combineLatest, of } from 'rxjs';\nimport { __decorate } from 'tslib';\nimport { filter, switchMap, distinctUntilChanged } from 'rxjs/operators';\nimport { NgControl } from '@angular/forms';\n\nclass MenuController {\n    menuController;\n    constructor(menuController) {\n        this.menuController = menuController;\n    }\n    /**\n     * Programmatically open the Menu.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return returns a promise when the menu is fully opened\n     */\n    open(menuId) {\n        return this.menuController.open(menuId);\n    }\n    /**\n     * Programmatically close the Menu. If no `menuId` is given as the first\n     * argument then it'll close any menu which is open. If a `menuId`\n     * is given then it'll close that exact menu.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return returns a promise when the menu is fully closed\n     */\n    close(menuId) {\n        return this.menuController.close(menuId);\n    }\n    /**\n     * Toggle the menu. If it's closed, it will open, and if opened, it\n     * will close.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return returns a promise when the menu has been toggled\n     */\n    toggle(menuId) {\n        return this.menuController.toggle(menuId);\n    }\n    /**\n     * Used to enable or disable a menu. For example, there could be multiple\n     * left menus, but only one of them should be able to be opened at the same\n     * time. If there are multiple menus on the same side, then enabling one menu\n     * will also automatically disable all the others that are on the same side.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return Returns the instance of the menu, which is useful for chaining.\n     */\n    enable(shouldEnable, menuId) {\n        return this.menuController.enable(shouldEnable, menuId);\n    }\n    /**\n     * Used to enable or disable the ability to swipe open the menu.\n     * @param shouldEnable  True if it should be swipe-able, false if not.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return Returns the instance of the menu, which is useful for chaining.\n     */\n    swipeGesture(shouldEnable, menuId) {\n        return this.menuController.swipeGesture(shouldEnable, menuId);\n    }\n    /**\n     * @param [menuId] Optionally get the menu by its id, or side.\n     * @return Returns true if the specified menu is currently open, otherwise false.\n     * If the menuId is not specified, it returns true if ANY menu is currenly open.\n     */\n    isOpen(menuId) {\n        return this.menuController.isOpen(menuId);\n    }\n    /**\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return Returns true if the menu is currently enabled, otherwise false.\n     */\n    isEnabled(menuId) {\n        return this.menuController.isEnabled(menuId);\n    }\n    /**\n     * Used to get a menu instance. If a `menuId` is not provided then it'll\n     * return the first menu found. If a `menuId` is `left` or `right`, then\n     * it'll return the enabled menu on that side. Otherwise, if a `menuId` is\n     * provided, then it'll try to find the menu using the menu's `id`\n     * property. If a menu is not found then it'll return `null`.\n     * @param [menuId]  Optionally get the menu by its id, or side.\n     * @return Returns the instance of the menu if found, otherwise `null`.\n     */\n    get(menuId) {\n        return this.menuController.get(menuId);\n    }\n    /**\n     * @return Returns the instance of the menu already opened, otherwise `null`.\n     */\n    getOpen() {\n        return this.menuController.getOpen();\n    }\n    /**\n     * @return Returns an array of all menu instances.\n     */\n    getMenus() {\n        return this.menuController.getMenus();\n    }\n    registerAnimation(name, animation) {\n        return this.menuController.registerAnimation(name, animation);\n    }\n    isAnimating() {\n        return this.menuController.isAnimating();\n    }\n    _getOpenSync() {\n        return this.menuController._getOpenSync();\n    }\n    _createAnimation(type, menuCmp) {\n        return this.menuController._createAnimation(type, menuCmp);\n    }\n    _register(menu) {\n        return this.menuController._register(menu);\n    }\n    _unregister(menu) {\n        return this.menuController._unregister(menu);\n    }\n    _setOpen(menu, shouldOpen, animated) {\n        return this.menuController._setOpen(menu, shouldOpen, animated);\n    }\n}\n\nclass DomController {\n    /**\n     * Schedules a task to run during the READ phase of the next frame.\n     * This task should only read the DOM, but never modify it.\n     */\n    read(cb) {\n        getQueue().read(cb);\n    }\n    /**\n     * Schedules a task to run during the WRITE phase of the next frame.\n     * This task should write the DOM, but never READ it.\n     */\n    write(cb) {\n        getQueue().write(cb);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomController, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    /** @nocollapse */ static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomController, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: DomController, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\nconst getQueue = () => {\n    const win = typeof window !== 'undefined' ? window : null;\n    if (win != null) {\n        const Ionic = win.Ionic;\n        if (Ionic?.queue) {\n            return Ionic.queue;\n        }\n        return {\n            read: (cb) => win.requestAnimationFrame(cb),\n            write: (cb) => win.requestAnimationFrame(cb),\n        };\n    }\n    return {\n        read: (cb) => cb(),\n        write: (cb) => cb(),\n    };\n};\n\nclass Platform {\n    doc;\n    _readyPromise;\n    win;\n    /**\n     * @hidden\n     */\n    backButton = new Subject();\n    /**\n     * The keyboardDidShow event emits when the\n     * on-screen keyboard is presented.\n     */\n    keyboardDidShow = new Subject();\n    /**\n     * The keyboardDidHide event emits when the\n     * on-screen keyboard is hidden.\n     */\n    keyboardDidHide = new Subject();\n    /**\n     * The pause event emits when the native platform puts the application\n     * into the background, typically when the user switches to a different\n     * application. This event would emit when a Cordova app is put into\n     * the background, however, it would not fire on a standard web browser.\n     */\n    pause = new Subject();\n    /**\n     * The resume event emits when the native platform pulls the application\n     * out from the background. This event would emit when a Cordova app comes\n     * out from the background, however, it would not fire on a standard web browser.\n     */\n    resume = new Subject();\n    /**\n     * The resize event emits when the browser window has changed dimensions. This\n     * could be from a browser window being physically resized, or from a device\n     * changing orientation.\n     */\n    resize = new Subject();\n    constructor(doc, zone) {\n        this.doc = doc;\n        zone.run(() => {\n            this.win = doc.defaultView;\n            this.backButton.subscribeWithPriority = function (priority, callback) {\n                return this.subscribe((ev) => {\n                    return ev.register(priority, (processNextHandler) => zone.run(() => callback(processNextHandler)));\n                });\n            };\n            proxyEvent(this.pause, doc, 'pause', zone);\n            proxyEvent(this.resume, doc, 'resume', zone);\n            proxyEvent(this.backButton, doc, 'ionBackButton', zone);\n            proxyEvent(this.resize, this.win, 'resize', zone);\n            proxyEvent(this.keyboardDidShow, this.win, 'ionKeyboardDidShow', zone);\n            proxyEvent(this.keyboardDidHide, this.win, 'ionKeyboardDidHide', zone);\n            let readyResolve;\n            this._readyPromise = new Promise((res) => {\n                readyResolve = res;\n            });\n            if (this.win?.['cordova']) {\n                doc.addEventListener('deviceready', () => {\n                    readyResolve('cordova');\n                }, { once: true });\n            }\n            else {\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                readyResolve('dom');\n            }\n        });\n    }\n    /**\n     * @returns returns true/false based on platform.\n     * @description\n     * Depending on the platform the user is on, `is(platformName)` will\n     * return `true` or `false`. Note that the same app can return `true`\n     * for more than one platform name. For example, an app running from\n     * an iPad would return `true` for the platform names: `mobile`,\n     * `ios`, `ipad`, and `tablet`. Additionally, if the app was running\n     * from Cordova then `cordova` would be true, and if it was running\n     * from a web browser on the iPad then `mobileweb` would be `true`.\n     *\n     * ```\n     * import { Platform } from 'ionic-angular';\n     *\n     * @Component({...})\n     * export MyPage {\n     *   constructor(public platform: Platform) {\n     *     if (this.platform.is('ios')) {\n     *       // This will only print when on iOS\n     *       console.log('I am an iOS device!');\n     *     }\n     *   }\n     * }\n     * ```\n     *\n     * | Platform Name   | Description                        |\n     * |-----------------|------------------------------------|\n     * | android         | on a device running Android.       |\n     * | capacitor       | on a device running Capacitor.     |\n     * | cordova         | on a device running Cordova.       |\n     * | ios             | on a device running iOS.           |\n     * | ipad            | on an iPad device.                 |\n     * | iphone          | on an iPhone device.               |\n     * | phablet         | on a phablet device.               |\n     * | tablet          | on a tablet device.                |\n     * | electron        | in Electron on a desktop device.   |\n     * | pwa             | as a PWA app.                      |\n     * | mobile          | on a mobile device.                |\n     * | mobileweb       | on a mobile device in a browser.   |\n     * | desktop         | on a desktop device.               |\n     * | hybrid          | is a cordova or capacitor app.     |\n     *\n     */\n    is(platformName) {\n        return isPlatform(this.win, platformName);\n    }\n    /**\n     * @returns the array of platforms\n     * @description\n     * Depending on what device you are on, `platforms` can return multiple values.\n     * Each possible value is a hierarchy of platforms. For example, on an iPhone,\n     * it would return `mobile`, `ios`, and `iphone`.\n     *\n     * ```\n     * import { Platform } from 'ionic-angular';\n     *\n     * @Component({...})\n     * export MyPage {\n     *   constructor(public platform: Platform) {\n     *     // This will print an array of the current platforms\n     *     console.log(this.platform.platforms());\n     *   }\n     * }\n     * ```\n     */\n    platforms() {\n        return getPlatforms(this.win);\n    }\n    /**\n     * Returns a promise when the platform is ready and native functionality\n     * can be called. If the app is running from within a web browser, then\n     * the promise will resolve when the DOM is ready. When the app is running\n     * from an application engine such as Cordova, then the promise will\n     * resolve when Cordova triggers the `deviceready` event.\n     *\n     * The resolved value is the `readySource`, which states which platform\n     * ready was used. For example, when Cordova is ready, the resolved ready\n     * source is `cordova`. The default ready source value will be `dom`. The\n     * `readySource` is useful if different logic should run depending on the\n     * platform the app is running from. For example, only Cordova can execute\n     * the status bar plugin, so the web should not run status bar plugin logic.\n     *\n     * ```\n     * import { Component } from '@angular/core';\n     * import { Platform } from 'ionic-angular';\n     *\n     * @Component({...})\n     * export MyApp {\n     *   constructor(public platform: Platform) {\n     *     this.platform.ready().then((readySource) => {\n     *       console.log('Platform ready from', readySource);\n     *       // Platform now ready, execute any required native code\n     *     });\n     *   }\n     * }\n     * ```\n     */\n    ready() {\n        return this._readyPromise;\n    }\n    /**\n     * Returns if this app is using right-to-left language direction or not.\n     * We recommend the app's `index.html` file already has the correct `dir`\n     * attribute value set, such as `<html dir=\"ltr\">` or `<html dir=\"rtl\">`.\n     * [W3C: Structural markup and right-to-left text in HTML](http://www.w3.org/International/questions/qa-html-dir)\n     */\n    get isRTL() {\n        return this.doc.dir === 'rtl';\n    }\n    /**\n     * Get the query string parameter\n     */\n    getQueryParam(key) {\n        return readQueryParam(this.win.location.href, key);\n    }\n    /**\n     * Returns `true` if the app is in landscape mode.\n     */\n    isLandscape() {\n        return !this.isPortrait();\n    }\n    /**\n     * Returns `true` if the app is in portrait mode.\n     */\n    isPortrait() {\n        return this.win.matchMedia?.('(orientation: portrait)').matches;\n    }\n    testUserAgent(expression) {\n        const nav = this.win.navigator;\n        return !!(nav?.userAgent && nav.userAgent.indexOf(expression) >= 0);\n    }\n    /**\n     * Get the current url.\n     */\n    url() {\n        return this.win.location.href;\n    }\n    /**\n     * Gets the width of the platform's viewport using `window.innerWidth`.\n     */\n    width() {\n        return this.win.innerWidth;\n    }\n    /**\n     * Gets the height of the platform's viewport using `window.innerHeight`.\n     */\n    height() {\n        return this.win.innerHeight;\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Platform, deps: [{ token: DOCUMENT }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\n    /** @nocollapse */ static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Platform, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Platform, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone }]; } });\nconst readQueryParam = (url, key) => {\n    key = key.replace(/[[\\]\\\\]/g, '\\\\$&');\n    const regex = new RegExp('[\\\\?&]' + key + '=([^&#]*)');\n    const results = regex.exec(url);\n    return results ? decodeURIComponent(results[1].replace(/\\+/g, ' ')) : null;\n};\nconst proxyEvent = (emitter, el, eventName, zone) => {\n    if (el) {\n        el.addEventListener(eventName, (ev) => {\n            /**\n             * `zone.run` is required to make sure that we are running inside the Angular zone\n             * at all times. This is necessary since an app that has Capacitor will\n             * override the `document.addEventListener` with its own implementation.\n             * The override causes the event to no longer be in the Angular zone.\n             */\n            zone.run(() => {\n                // ?? cordova might emit \"null\" events\n                const value = ev != null ? ev.detail : undefined;\n                emitter.next(value);\n            });\n        });\n    }\n};\n\nclass NavController {\n    location;\n    serializer;\n    router;\n    topOutlet;\n    direction = DEFAULT_DIRECTION;\n    animated = DEFAULT_ANIMATED;\n    animationBuilder;\n    guessDirection = 'forward';\n    guessAnimation;\n    lastNavId = -1;\n    constructor(platform, location, serializer, router) {\n        this.location = location;\n        this.serializer = serializer;\n        this.router = router;\n        // Subscribe to router events to detect direction\n        if (router) {\n            router.events.subscribe((ev) => {\n                if (ev instanceof NavigationStart) {\n                    // restoredState is set if the browser back/forward button is used\n                    const id = ev.restoredState ? ev.restoredState.navigationId : ev.id;\n                    this.guessDirection = this.guessAnimation = id < this.lastNavId ? 'back' : 'forward';\n                    this.lastNavId = this.guessDirection === 'forward' ? ev.id : id;\n                }\n            });\n        }\n        // Subscribe to backButton events\n        platform.backButton.subscribeWithPriority(0, (processNextHandler) => {\n            this.pop();\n            processNextHandler();\n        });\n    }\n    /**\n     * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n     * it's equivalent to calling `this.router.navigateByUrl()`, but it's explicit about the **direction** of the transition.\n     *\n     * Going **forward** means that a new page is going to be pushed to the stack of the outlet (ion-router-outlet),\n     * and that it will show a \"forward\" animation by default.\n     *\n     * Navigating forward can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n     *\n     * ```html\n     * <a routerLink=\"/path/to/page\" routerDirection=\"forward\">Link</a>\n     * ```\n     */\n    navigateForward(url, options = {}) {\n        this.setDirection('forward', options.animated, options.animationDirection, options.animation);\n        return this.navigate(url, options);\n    }\n    /**\n     * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n     * it's equivalent to calling:\n     *\n     * ```ts\n     * this.navController.setDirection('back');\n     * this.router.navigateByUrl(path);\n     * ```\n     *\n     * Going **back** means that all the pages in the stack until the navigated page is found will be popped,\n     * and that it will show a \"back\" animation by default.\n     *\n     * Navigating back can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n     *\n     * ```html\n     * <a routerLink=\"/path/to/page\" routerDirection=\"back\">Link</a>\n     * ```\n     */\n    navigateBack(url, options = {}) {\n        this.setDirection('back', options.animated, options.animationDirection, options.animation);\n        return this.navigate(url, options);\n    }\n    /**\n     * This method uses Angular's [Router](https://angular.io/api/router/Router) under the hood,\n     * it's equivalent to calling:\n     *\n     * ```ts\n     * this.navController.setDirection('root');\n     * this.router.navigateByUrl(path);\n     * ```\n     *\n     * Going **root** means that all existing pages in the stack will be removed,\n     * and the navigated page will become the single page in the stack.\n     *\n     * Navigating root can also be triggered in a declarative manner by using the `[routerDirection]` directive:\n     *\n     * ```html\n     * <a routerLink=\"/path/to/page\" routerDirection=\"root\">Link</a>\n     * ```\n     */\n    navigateRoot(url, options = {}) {\n        this.setDirection('root', options.animated, options.animationDirection, options.animation);\n        return this.navigate(url, options);\n    }\n    /**\n     * Same as [Location](https://angular.io/api/common/Location)'s back() method.\n     * It will use the standard `window.history.back()` under the hood, but featuring a `back` animation\n     * by default.\n     */\n    back(options = { animated: true, animationDirection: 'back' }) {\n        this.setDirection('back', options.animated, options.animationDirection, options.animation);\n        return this.location.back();\n    }\n    /**\n     * This methods goes back in the context of Ionic's stack navigation.\n     *\n     * It recursively finds the top active `ion-router-outlet` and calls `pop()`.\n     * This is the recommended way to go back when you are using `ion-router-outlet`.\n     *\n     * Resolves to `true` if it was able to pop.\n     */\n    async pop() {\n        let outlet = this.topOutlet;\n        while (outlet) {\n            if (await outlet.pop()) {\n                return true;\n            }\n            else {\n                outlet = outlet.parentOutlet;\n            }\n        }\n        return false;\n    }\n    /**\n     * This methods specifies the direction of the next navigation performed by the Angular router.\n     *\n     * `setDirection()` does not trigger any transition, it just sets some flags to be consumed by `ion-router-outlet`.\n     *\n     * It's recommended to use `navigateForward()`, `navigateBack()` and `navigateRoot()` instead of `setDirection()`.\n     */\n    setDirection(direction, animated, animationDirection, animationBuilder) {\n        this.direction = direction;\n        this.animated = getAnimation(direction, animated, animationDirection);\n        this.animationBuilder = animationBuilder;\n    }\n    /**\n     * @internal\n     */\n    setTopOutlet(outlet) {\n        this.topOutlet = outlet;\n    }\n    /**\n     * @internal\n     */\n    consumeTransition() {\n        let direction = 'root';\n        let animation;\n        const animationBuilder = this.animationBuilder;\n        if (this.direction === 'auto') {\n            direction = this.guessDirection;\n            animation = this.guessAnimation;\n        }\n        else {\n            animation = this.animated;\n            direction = this.direction;\n        }\n        this.direction = DEFAULT_DIRECTION;\n        this.animated = DEFAULT_ANIMATED;\n        this.animationBuilder = undefined;\n        return {\n            direction,\n            animation,\n            animationBuilder,\n        };\n    }\n    navigate(url, options) {\n        if (Array.isArray(url)) {\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            return this.router.navigate(url, options);\n        }\n        else {\n            /**\n             * navigateByUrl ignores any properties that\n             * would change the url, so things like queryParams\n             * would be ignored unless we create a url tree\n             * More Info: https://github.com/angular/angular/issues/18798\n             */\n            const urlTree = this.serializer.parse(url.toString());\n            if (options.queryParams !== undefined) {\n                urlTree.queryParams = { ...options.queryParams };\n            }\n            if (options.fragment !== undefined) {\n                urlTree.fragment = options.fragment;\n            }\n            /**\n             * `navigateByUrl` will still apply `NavigationExtras` properties\n             * that do not modify the url, such as `replaceUrl` which is why\n             * `options` is passed in here.\n             */\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            return this.router.navigateByUrl(urlTree, options);\n        }\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: NavController, deps: [{ token: Platform }, { token: i1.Location }, { token: i3.UrlSerializer }, { token: i3.Router, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    /** @nocollapse */ static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: NavController, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: NavController, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: function () { return [{ type: Platform }, { type: i1.Location }, { type: i3.UrlSerializer }, { type: i3.Router, decorators: [{\n                    type: Optional\n                }] }]; } });\nconst getAnimation = (direction, animated, animationDirection) => {\n    if (animated === false) {\n        return undefined;\n    }\n    if (animationDirection !== undefined) {\n        return animationDirection;\n    }\n    if (direction === 'forward' || direction === 'back') {\n        return direction;\n    }\n    else if (direction === 'root' && animated === true) {\n        return 'forward';\n    }\n    return undefined;\n};\nconst DEFAULT_DIRECTION = 'auto';\nconst DEFAULT_ANIMATED = undefined;\n\nclass Config {\n    get(key, fallback) {\n        const c = getConfig();\n        if (c) {\n            return c.get(key, fallback);\n        }\n        return null;\n    }\n    getBoolean(key, fallback) {\n        const c = getConfig();\n        if (c) {\n            return c.getBoolean(key, fallback);\n        }\n        return false;\n    }\n    getNumber(key, fallback) {\n        const c = getConfig();\n        if (c) {\n            return c.getNumber(key, fallback);\n        }\n        return 0;\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Config, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    /** @nocollapse */ static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Config, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: Config, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }] });\nconst ConfigToken = new InjectionToken('USERCONFIG');\nconst getConfig = () => {\n    if (typeof window !== 'undefined') {\n        const Ionic = window.Ionic;\n        if (Ionic?.config) {\n            return Ionic.config;\n        }\n    }\n    return null;\n};\n\n/**\n * @description\n * NavParams are an object that exists on a page and can contain data for that particular view.\n * Similar to how data was pass to a view in V1 with `$stateParams`, NavParams offer a much more flexible\n * option with a simple `get` method.\n *\n * @usage\n * ```ts\n * import { NavParams } from '@ionic/angular';\n *\n * export class MyClass{\n *\n *  constructor(navParams: NavParams){\n *    // userParams is an object we have in our nav-parameters\n *    navParams.get('userParams');\n *  }\n *\n * }\n * ```\n */\nclass NavParams {\n    data;\n    constructor(data = {}) {\n        this.data = data;\n        console.warn(`[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.`);\n    }\n    /**\n     * Get the value of a nav-parameter for the current view\n     *\n     * ```ts\n     * import { NavParams } from 'ionic-angular';\n     *\n     * export class MyClass{\n     *  constructor(public navParams: NavParams){\n     *    // userParams is an object we have in our nav-parameters\n     *    this.navParams.get('userParams');\n     *  }\n     * }\n     * ```\n     *\n     * @param param Which param you want to look up\n     */\n    get(param) {\n        return this.data[param];\n    }\n}\n\n// TODO(FW-2827): types\nclass AngularDelegate {\n    zone = inject(NgZone);\n    applicationRef = inject(ApplicationRef);\n    config = inject(ConfigToken);\n    create(environmentInjector, injector, elementReferenceKey) {\n        return new AngularFrameworkDelegate(environmentInjector, injector, this.applicationRef, this.zone, elementReferenceKey, this.config.useSetInputAPI ?? false);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: AngularDelegate, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    /** @nocollapse */ static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: AngularDelegate });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: AngularDelegate, decorators: [{\n            type: Injectable\n        }] });\nclass AngularFrameworkDelegate {\n    environmentInjector;\n    injector;\n    applicationRef;\n    zone;\n    elementReferenceKey;\n    enableSignalsSupport;\n    elRefMap = new WeakMap();\n    elEventsMap = new WeakMap();\n    constructor(environmentInjector, injector, applicationRef, zone, elementReferenceKey, enableSignalsSupport) {\n        this.environmentInjector = environmentInjector;\n        this.injector = injector;\n        this.applicationRef = applicationRef;\n        this.zone = zone;\n        this.elementReferenceKey = elementReferenceKey;\n        this.enableSignalsSupport = enableSignalsSupport;\n    }\n    attachViewToDom(container, component, params, cssClasses) {\n        return this.zone.run(() => {\n            return new Promise((resolve) => {\n                const componentProps = {\n                    ...params,\n                };\n                /**\n                 * Ionic Angular passes a reference to a modal\n                 * or popover that can be accessed using a\n                 * variable in the overlay component. If\n                 * elementReferenceKey is defined, then we should\n                 * pass a reference to the component using\n                 * elementReferenceKey as the key.\n                 */\n                if (this.elementReferenceKey !== undefined) {\n                    componentProps[this.elementReferenceKey] = container;\n                }\n                const el = attachView(this.zone, this.environmentInjector, this.injector, this.applicationRef, this.elRefMap, this.elEventsMap, container, component, componentProps, cssClasses, this.elementReferenceKey, this.enableSignalsSupport);\n                resolve(el);\n            });\n        });\n    }\n    removeViewFromDom(_container, component) {\n        return this.zone.run(() => {\n            return new Promise((resolve) => {\n                const componentRef = this.elRefMap.get(component);\n                if (componentRef) {\n                    componentRef.destroy();\n                    this.elRefMap.delete(component);\n                    const unbindEvents = this.elEventsMap.get(component);\n                    if (unbindEvents) {\n                        unbindEvents();\n                        this.elEventsMap.delete(component);\n                    }\n                }\n                resolve();\n            });\n        });\n    }\n}\nconst attachView = (zone, environmentInjector, injector, applicationRef, elRefMap, elEventsMap, container, component, params, cssClasses, elementReferenceKey, enableSignalsSupport) => {\n    /**\n     * Wraps the injector with a custom injector that\n     * provides NavParams to the component.\n     *\n     * NavParams is a legacy feature from Ionic v3 that allows\n     * Angular developers to provide data to a component\n     * and access it by providing NavParams as a dependency\n     * in the constructor.\n     *\n     * The modern approach is to access the data directly\n     * from the component's class instance.\n     */\n    const childInjector = Injector.create({\n        providers: getProviders(params),\n        parent: injector,\n    });\n    const componentRef = createComponent(component, {\n        environmentInjector,\n        elementInjector: childInjector,\n    });\n    const instance = componentRef.instance;\n    const hostElement = componentRef.location.nativeElement;\n    if (params) {\n        /**\n         * For modals and popovers, a reference to the component is\n         * added to `params` during the call to attachViewToDom. If\n         * a reference using this name is already set, this means\n         * the app is trying to use the name as a component prop,\n         * which will cause collisions.\n         */\n        if (elementReferenceKey && instance[elementReferenceKey] !== undefined) {\n            console.error(`[Ionic Error]: ${elementReferenceKey} is a reserved property when using ${container.tagName.toLowerCase()}. Rename or remove the \"${elementReferenceKey}\" property from ${component.name}.`);\n        }\n        /**\n         * Angular 14.1 added support for setInput\n         * so we need to fall back to Object.assign\n         * for Angular 14.0.\n         */\n        if (enableSignalsSupport === true && componentRef.setInput !== undefined) {\n            const { modal, popover, ...otherParams } = params;\n            /**\n             * Any key/value pairs set in componentProps\n             * must be set as inputs on the component instance.\n             */\n            for (const key in otherParams) {\n                componentRef.setInput(key, otherParams[key]);\n            }\n            /**\n             * Using setInput will cause an error when\n             * setting modal/popover on a component that\n             * does not define them as an input. For backwards\n             * compatibility purposes we fall back to using\n             * Object.assign for these properties.\n             */\n            if (modal !== undefined) {\n                Object.assign(instance, { modal });\n            }\n            if (popover !== undefined) {\n                Object.assign(instance, { popover });\n            }\n        }\n        else {\n            Object.assign(instance, params);\n        }\n    }\n    if (cssClasses) {\n        for (const cssClass of cssClasses) {\n            hostElement.classList.add(cssClass);\n        }\n    }\n    const unbindEvents = bindLifecycleEvents(zone, instance, hostElement);\n    container.appendChild(hostElement);\n    applicationRef.attachView(componentRef.hostView);\n    elRefMap.set(hostElement, componentRef);\n    elEventsMap.set(hostElement, unbindEvents);\n    return hostElement;\n};\nconst LIFECYCLES = [\n    LIFECYCLE_WILL_ENTER,\n    LIFECYCLE_DID_ENTER,\n    LIFECYCLE_WILL_LEAVE,\n    LIFECYCLE_DID_LEAVE,\n    LIFECYCLE_WILL_UNLOAD,\n];\nconst bindLifecycleEvents = (zone, instance, element) => {\n    return zone.run(() => {\n        const unregisters = LIFECYCLES.filter((eventName) => typeof instance[eventName] === 'function').map((eventName) => {\n            const handler = (ev) => instance[eventName](ev.detail);\n            element.addEventListener(eventName, handler);\n            return () => element.removeEventListener(eventName, handler);\n        });\n        return () => unregisters.forEach((fn) => fn());\n    });\n};\nconst NavParamsToken = new InjectionToken('NavParamsToken');\nconst getProviders = (params) => {\n    return [\n        {\n            provide: NavParamsToken,\n            useValue: params,\n        },\n        {\n            provide: NavParams,\n            useFactory: provideNavParamsInjectable,\n            deps: [NavParamsToken],\n        },\n    ];\n};\nconst provideNavParamsInjectable = (params) => {\n    return new NavParams(params);\n};\n\n// TODO: Is there a way we can grab this from angular-component-lib instead?\n/* eslint-disable */\n/* tslint:disable */\nconst proxyInputs = (Cmp, inputs) => {\n    const Prototype = Cmp.prototype;\n    inputs.forEach((item) => {\n        Object.defineProperty(Prototype, item, {\n            get() {\n                return this.el[item];\n            },\n            set(val) {\n                this.z.runOutsideAngular(() => (this.el[item] = val));\n            },\n        });\n    });\n};\nconst proxyMethods = (Cmp, methods) => {\n    const Prototype = Cmp.prototype;\n    methods.forEach((methodName) => {\n        Prototype[methodName] = function () {\n            const args = arguments;\n            return this.z.runOutsideAngular(() => this.el[methodName].apply(this.el, args));\n        };\n    });\n};\nconst proxyOutputs = (instance, el, events) => {\n    events.forEach((eventName) => (instance[eventName] = fromEvent(el, eventName)));\n};\n// tslint:disable-next-line: only-arrow-functions\nfunction ProxyCmp(opts) {\n    const decorator = function (cls) {\n        const { defineCustomElementFn, inputs, methods } = opts;\n        if (defineCustomElementFn !== undefined) {\n            defineCustomElementFn();\n        }\n        if (inputs) {\n            proxyInputs(cls, inputs);\n        }\n        if (methods) {\n            proxyMethods(cls, methods);\n        }\n        return cls;\n    };\n    return decorator;\n}\n\nconst POPOVER_INPUTS = [\n    'alignment',\n    'animated',\n    'arrow',\n    'keepContentsMounted',\n    'backdropDismiss',\n    'cssClass',\n    'dismissOnSelect',\n    'enterAnimation',\n    'event',\n    'focusTrap',\n    'isOpen',\n    'keyboardClose',\n    'leaveAnimation',\n    'mode',\n    'showBackdrop',\n    'translucent',\n    'trigger',\n    'triggerAction',\n    'reference',\n    'size',\n    'side',\n];\nconst POPOVER_METHODS = ['present', 'dismiss', 'onDidDismiss', 'onWillDismiss'];\nlet IonPopover = class IonPopover {\n    z;\n    // TODO(FW-2827): type\n    template;\n    isCmpOpen = false;\n    el;\n    constructor(c, r, z) {\n        this.z = z;\n        this.el = r.nativeElement;\n        this.el.addEventListener('ionMount', () => {\n            this.isCmpOpen = true;\n            c.detectChanges();\n        });\n        this.el.addEventListener('didDismiss', () => {\n            this.isCmpOpen = false;\n            c.detectChanges();\n        });\n        proxyOutputs(this, this.el, [\n            'ionPopoverDidPresent',\n            'ionPopoverWillPresent',\n            'ionPopoverWillDismiss',\n            'ionPopoverDidDismiss',\n            'didPresent',\n            'willPresent',\n            'willDismiss',\n            'didDismiss',\n        ]);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonPopover, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: IonPopover, selector: \"ion-popover\", inputs: { alignment: \"alignment\", animated: \"animated\", arrow: \"arrow\", keepContentsMounted: \"keepContentsMounted\", backdropDismiss: \"backdropDismiss\", cssClass: \"cssClass\", dismissOnSelect: \"dismissOnSelect\", enterAnimation: \"enterAnimation\", event: \"event\", focusTrap: \"focusTrap\", isOpen: \"isOpen\", keyboardClose: \"keyboardClose\", leaveAnimation: \"leaveAnimation\", mode: \"mode\", showBackdrop: \"showBackdrop\", translucent: \"translucent\", trigger: \"trigger\", triggerAction: \"triggerAction\", reference: \"reference\", size: \"size\", side: \"side\" }, queries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true }], ngImport: i0 });\n};\nIonPopover = __decorate([\n    ProxyCmp({\n        inputs: POPOVER_INPUTS,\n        methods: POPOVER_METHODS,\n    })\n    /**\n     * @Component extends from @Directive\n     * so by defining the inputs here we\n     * do not need to re-define them for the\n     * lazy loaded popover.\n     */\n], IonPopover);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonPopover, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ion-popover',\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: POPOVER_INPUTS,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { template: [{\n                type: ContentChild,\n                args: [TemplateRef, { static: false }]\n            }] } });\n\nconst MODAL_INPUTS = [\n    'animated',\n    'keepContentsMounted',\n    'backdropBreakpoint',\n    'backdropDismiss',\n    'breakpoints',\n    'canDismiss',\n    'cssClass',\n    'enterAnimation',\n    'event',\n    'focusTrap',\n    'handle',\n    'handleBehavior',\n    'initialBreakpoint',\n    'isOpen',\n    'keyboardClose',\n    'leaveAnimation',\n    'mode',\n    'presentingElement',\n    'showBackdrop',\n    'translucent',\n    'trigger',\n];\nconst MODAL_METHODS = [\n    'present',\n    'dismiss',\n    'onDidDismiss',\n    'onWillDismiss',\n    'setCurrentBreakpoint',\n    'getCurrentBreakpoint',\n];\nlet IonModal = class IonModal {\n    z;\n    // TODO(FW-2827): type\n    template;\n    isCmpOpen = false;\n    el;\n    constructor(c, r, z) {\n        this.z = z;\n        this.el = r.nativeElement;\n        this.el.addEventListener('ionMount', () => {\n            this.isCmpOpen = true;\n            c.detectChanges();\n        });\n        this.el.addEventListener('didDismiss', () => {\n            this.isCmpOpen = false;\n            c.detectChanges();\n        });\n        proxyOutputs(this, this.el, [\n            'ionModalDidPresent',\n            'ionModalWillPresent',\n            'ionModalWillDismiss',\n            'ionModalDidDismiss',\n            'ionBreakpointDidChange',\n            'didPresent',\n            'willPresent',\n            'willDismiss',\n            'didDismiss',\n        ]);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonModal, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: IonModal, selector: \"ion-modal\", inputs: { animated: \"animated\", keepContentsMounted: \"keepContentsMounted\", backdropBreakpoint: \"backdropBreakpoint\", backdropDismiss: \"backdropDismiss\", breakpoints: \"breakpoints\", canDismiss: \"canDismiss\", cssClass: \"cssClass\", enterAnimation: \"enterAnimation\", event: \"event\", focusTrap: \"focusTrap\", handle: \"handle\", handleBehavior: \"handleBehavior\", initialBreakpoint: \"initialBreakpoint\", isOpen: \"isOpen\", keyboardClose: \"keyboardClose\", leaveAnimation: \"leaveAnimation\", mode: \"mode\", presentingElement: \"presentingElement\", showBackdrop: \"showBackdrop\", translucent: \"translucent\", trigger: \"trigger\" }, queries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true }], ngImport: i0 });\n};\nIonModal = __decorate([\n    ProxyCmp({\n        inputs: MODAL_INPUTS,\n        methods: MODAL_METHODS,\n    })\n    /**\n     * @Component extends from @Directive\n     * so by defining the inputs here we\n     * do not need to re-define them for the\n     * lazy loaded popover.\n     */\n], IonModal);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonModal, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ion-modal',\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: MODAL_INPUTS,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { template: [{\n                type: ContentChild,\n                args: [TemplateRef, { static: false }]\n            }] } });\n\nconst insertView = (views, view, direction) => {\n    if (direction === 'root') {\n        return setRoot(views, view);\n    }\n    else if (direction === 'forward') {\n        return setForward(views, view);\n    }\n    else {\n        return setBack(views, view);\n    }\n};\nconst setRoot = (views, view) => {\n    views = views.filter((v) => v.stackId !== view.stackId);\n    views.push(view);\n    return views;\n};\nconst setForward = (views, view) => {\n    const index = views.indexOf(view);\n    if (index >= 0) {\n        views = views.filter((v) => v.stackId !== view.stackId || v.id <= view.id);\n    }\n    else {\n        views.push(view);\n    }\n    return views;\n};\nconst setBack = (views, view) => {\n    const index = views.indexOf(view);\n    if (index >= 0) {\n        return views.filter((v) => v.stackId !== view.stackId || v.id <= view.id);\n    }\n    else {\n        return setRoot(views, view);\n    }\n};\nconst getUrl = (router, activatedRoute) => {\n    const urlTree = router.createUrlTree(['.'], { relativeTo: activatedRoute });\n    return router.serializeUrl(urlTree);\n};\nconst isTabSwitch = (enteringView, leavingView) => {\n    if (!leavingView) {\n        return true;\n    }\n    return enteringView.stackId !== leavingView.stackId;\n};\nconst computeStackId = (prefixUrl, url) => {\n    if (!prefixUrl) {\n        return undefined;\n    }\n    const segments = toSegments(url);\n    for (let i = 0; i < segments.length; i++) {\n        if (i >= prefixUrl.length) {\n            return segments[i];\n        }\n        if (segments[i] !== prefixUrl[i]) {\n            return undefined;\n        }\n    }\n    return undefined;\n};\nconst toSegments = (path) => {\n    return path\n        .split('/')\n        .map((s) => s.trim())\n        .filter((s) => s !== '');\n};\nconst destroyView = (view) => {\n    if (view) {\n        view.ref.destroy();\n        view.unlistenEvents();\n    }\n};\n\n// TODO(FW-2827): types\nclass StackController {\n    containerEl;\n    router;\n    navCtrl;\n    zone;\n    location;\n    views = [];\n    runningTask;\n    skipTransition = false;\n    tabsPrefix;\n    activeView;\n    nextId = 0;\n    constructor(tabsPrefix, containerEl, router, navCtrl, zone, location) {\n        this.containerEl = containerEl;\n        this.router = router;\n        this.navCtrl = navCtrl;\n        this.zone = zone;\n        this.location = location;\n        this.tabsPrefix = tabsPrefix !== undefined ? toSegments(tabsPrefix) : undefined;\n    }\n    createView(ref, activatedRoute) {\n        const url = getUrl(this.router, activatedRoute);\n        const element = ref?.location?.nativeElement;\n        const unlistenEvents = bindLifecycleEvents(this.zone, ref.instance, element);\n        return {\n            id: this.nextId++,\n            stackId: computeStackId(this.tabsPrefix, url),\n            unlistenEvents,\n            element,\n            ref,\n            url,\n        };\n    }\n    getExistingView(activatedRoute) {\n        const activatedUrlKey = getUrl(this.router, activatedRoute);\n        const view = this.views.find((vw) => vw.url === activatedUrlKey);\n        if (view) {\n            view.ref.changeDetectorRef.reattach();\n        }\n        return view;\n    }\n    setActive(enteringView) {\n        const consumeResult = this.navCtrl.consumeTransition();\n        let { direction, animation, animationBuilder } = consumeResult;\n        const leavingView = this.activeView;\n        const tabSwitch = isTabSwitch(enteringView, leavingView);\n        if (tabSwitch) {\n            direction = 'back';\n            animation = undefined;\n        }\n        const viewsSnapshot = this.views.slice();\n        let currentNavigation;\n        const router = this.router;\n        // Angular >= 7.2.0\n        if (router.getCurrentNavigation) {\n            currentNavigation = router.getCurrentNavigation();\n            // Angular < 7.2.0\n        }\n        else if (router.navigations?.value) {\n            currentNavigation = router.navigations.value;\n        }\n        /**\n         * If the navigation action\n         * sets `replaceUrl: true`\n         * then we need to make sure\n         * we remove the last item\n         * from our views stack\n         */\n        if (currentNavigation?.extras?.replaceUrl) {\n            if (this.views.length > 0) {\n                this.views.splice(-1, 1);\n            }\n        }\n        const reused = this.views.includes(enteringView);\n        const views = this.insertView(enteringView, direction);\n        // Trigger change detection before transition starts\n        // This will call ngOnInit() the first time too, just after the view\n        // was attached to the dom, but BEFORE the transition starts\n        if (!reused) {\n            enteringView.ref.changeDetectorRef.detectChanges();\n        }\n        /**\n         * If we are going back from a page that\n         * was presented using a custom animation\n         * we should default to using that\n         * unless the developer explicitly\n         * provided another animation.\n         */\n        const customAnimation = enteringView.animationBuilder;\n        if (animationBuilder === undefined && direction === 'back' && !tabSwitch && customAnimation !== undefined) {\n            animationBuilder = customAnimation;\n        }\n        /**\n         * Save any custom animation so that navigating\n         * back will use this custom animation by default.\n         */\n        if (leavingView) {\n            leavingView.animationBuilder = animationBuilder;\n        }\n        // Wait until previous transitions finish\n        return this.zone.runOutsideAngular(() => {\n            return this.wait(() => {\n                // disconnect leaving page from change detection to\n                // reduce jank during the page transition\n                if (leavingView) {\n                    leavingView.ref.changeDetectorRef.detach();\n                }\n                // In case the enteringView is the same as the leavingPage we need to reattach()\n                enteringView.ref.changeDetectorRef.reattach();\n                return this.transition(enteringView, leavingView, animation, this.canGoBack(1), false, animationBuilder)\n                    .then(() => cleanupAsync(enteringView, views, viewsSnapshot, this.location, this.zone))\n                    .then(() => ({\n                    enteringView,\n                    direction,\n                    animation,\n                    tabSwitch,\n                }));\n            });\n        });\n    }\n    canGoBack(deep, stackId = this.getActiveStackId()) {\n        return this.getStack(stackId).length > deep;\n    }\n    pop(deep, stackId = this.getActiveStackId()) {\n        return this.zone.run(() => {\n            const views = this.getStack(stackId);\n            if (views.length <= deep) {\n                return Promise.resolve(false);\n            }\n            const view = views[views.length - deep - 1];\n            let url = view.url;\n            const viewSavedData = view.savedData;\n            if (viewSavedData) {\n                const primaryOutlet = viewSavedData.get('primary');\n                if (primaryOutlet?.route?._routerState?.snapshot.url) {\n                    url = primaryOutlet.route._routerState.snapshot.url;\n                }\n            }\n            const { animationBuilder } = this.navCtrl.consumeTransition();\n            return this.navCtrl.navigateBack(url, { ...view.savedExtras, animation: animationBuilder }).then(() => true);\n        });\n    }\n    startBackTransition() {\n        const leavingView = this.activeView;\n        if (leavingView) {\n            const views = this.getStack(leavingView.stackId);\n            const enteringView = views[views.length - 2];\n            const customAnimation = enteringView.animationBuilder;\n            return this.wait(() => {\n                return this.transition(enteringView, // entering view\n                leavingView, // leaving view\n                'back', this.canGoBack(2), true, customAnimation);\n            });\n        }\n        return Promise.resolve();\n    }\n    endBackTransition(shouldComplete) {\n        if (shouldComplete) {\n            this.skipTransition = true;\n            this.pop(1);\n        }\n        else if (this.activeView) {\n            cleanup(this.activeView, this.views, this.views, this.location, this.zone);\n        }\n    }\n    getLastUrl(stackId) {\n        const views = this.getStack(stackId);\n        return views.length > 0 ? views[views.length - 1] : undefined;\n    }\n    /**\n     * @internal\n     */\n    getRootUrl(stackId) {\n        const views = this.getStack(stackId);\n        return views.length > 0 ? views[0] : undefined;\n    }\n    getActiveStackId() {\n        return this.activeView ? this.activeView.stackId : undefined;\n    }\n    /**\n     * @internal\n     */\n    getActiveView() {\n        return this.activeView;\n    }\n    hasRunningTask() {\n        return this.runningTask !== undefined;\n    }\n    destroy() {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        this.containerEl = undefined;\n        this.views.forEach(destroyView);\n        this.activeView = undefined;\n        this.views = [];\n    }\n    getStack(stackId) {\n        return this.views.filter((v) => v.stackId === stackId);\n    }\n    insertView(enteringView, direction) {\n        this.activeView = enteringView;\n        this.views = insertView(this.views, enteringView, direction);\n        return this.views.slice();\n    }\n    transition(enteringView, leavingView, direction, showGoBack, progressAnimation, animationBuilder) {\n        if (this.skipTransition) {\n            this.skipTransition = false;\n            return Promise.resolve(false);\n        }\n        if (leavingView === enteringView) {\n            return Promise.resolve(false);\n        }\n        const enteringEl = enteringView ? enteringView.element : undefined;\n        const leavingEl = leavingView ? leavingView.element : undefined;\n        const containerEl = this.containerEl;\n        if (enteringEl && enteringEl !== leavingEl) {\n            enteringEl.classList.add('ion-page');\n            enteringEl.classList.add('ion-page-invisible');\n            if (containerEl.commit) {\n                return containerEl.commit(enteringEl, leavingEl, {\n                    duration: direction === undefined ? 0 : undefined,\n                    direction,\n                    showGoBack,\n                    progressAnimation,\n                    animationBuilder,\n                });\n            }\n        }\n        return Promise.resolve(false);\n    }\n    async wait(task) {\n        if (this.runningTask !== undefined) {\n            await this.runningTask;\n            this.runningTask = undefined;\n        }\n        const promise = (this.runningTask = task());\n        promise.finally(() => (this.runningTask = undefined));\n        return promise;\n    }\n}\nconst cleanupAsync = (activeRoute, views, viewsSnapshot, location, zone) => {\n    if (typeof requestAnimationFrame === 'function') {\n        return new Promise((resolve) => {\n            requestAnimationFrame(() => {\n                cleanup(activeRoute, views, viewsSnapshot, location, zone);\n                resolve();\n            });\n        });\n    }\n    return Promise.resolve();\n};\nconst cleanup = (activeRoute, views, viewsSnapshot, location, zone) => {\n    /**\n     * Re-enter the Angular zone when destroying page components. This will allow\n     * lifecycle events (`ngOnDestroy`) to be run inside the Angular zone.\n     */\n    zone.run(() => viewsSnapshot.filter((view) => !views.includes(view)).forEach(destroyView));\n    views.forEach((view) => {\n        /**\n         * In the event that a user navigated multiple\n         * times in rapid succession, we want to make sure\n         * we don't pre-emptively detach a view while\n         * it is in mid-transition.\n         *\n         * In this instance we also do not care about query\n         * params or fragments as it will be the same view regardless\n         */\n        const locationWithoutParams = location.path().split('?')[0];\n        const locationWithoutFragment = locationWithoutParams.split('#')[0];\n        if (view !== activeRoute && view.url !== locationWithoutFragment) {\n            const element = view.element;\n            element.setAttribute('aria-hidden', 'true');\n            element.classList.add('ion-page-hidden');\n            view.ref.changeDetectorRef.detach();\n        }\n    });\n};\n\n// TODO(FW-2827): types\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonRouterOutlet {\n    parentOutlet;\n    nativeEl;\n    activatedView = null;\n    tabsPrefix;\n    _swipeGesture;\n    stackCtrl;\n    // Maintain map of activated route proxies for each component instance\n    proxyMap = new WeakMap();\n    // Keep the latest activated route in a subject for the proxy routes to switch map to\n    currentActivatedRoute$ = new BehaviorSubject(null);\n    activated = null;\n    /** @internal */\n    get activatedComponentRef() {\n        return this.activated;\n    }\n    _activatedRoute = null;\n    /**\n     * The name of the outlet\n     */\n    name = PRIMARY_OUTLET;\n    /** @internal */\n    stackWillChange = new EventEmitter();\n    /** @internal */\n    stackDidChange = new EventEmitter();\n    // eslint-disable-next-line @angular-eslint/no-output-rename\n    activateEvents = new EventEmitter();\n    // eslint-disable-next-line @angular-eslint/no-output-rename\n    deactivateEvents = new EventEmitter();\n    parentContexts = inject(ChildrenOutletContexts);\n    location = inject(ViewContainerRef);\n    environmentInjector = inject(EnvironmentInjector);\n    inputBinder = inject(INPUT_BINDER, { optional: true });\n    /** @nodoc */\n    supportsBindingToComponentInputs = true;\n    // Ionic providers\n    config = inject(Config);\n    navCtrl = inject(NavController);\n    set animation(animation) {\n        this.nativeEl.animation = animation;\n    }\n    set animated(animated) {\n        this.nativeEl.animated = animated;\n    }\n    set swipeGesture(swipe) {\n        this._swipeGesture = swipe;\n        this.nativeEl.swipeHandler = swipe\n            ? {\n                canStart: () => this.stackCtrl.canGoBack(1) && !this.stackCtrl.hasRunningTask(),\n                onStart: () => this.stackCtrl.startBackTransition(),\n                onEnd: (shouldContinue) => this.stackCtrl.endBackTransition(shouldContinue),\n            }\n            : undefined;\n    }\n    constructor(name, tabs, commonLocation, elementRef, router, zone, activatedRoute, parentOutlet) {\n        this.parentOutlet = parentOutlet;\n        this.nativeEl = elementRef.nativeElement;\n        this.name = name || PRIMARY_OUTLET;\n        this.tabsPrefix = tabs === 'true' ? getUrl(router, activatedRoute) : undefined;\n        this.stackCtrl = new StackController(this.tabsPrefix, this.nativeEl, router, this.navCtrl, zone, commonLocation);\n        this.parentContexts.onChildOutletCreated(this.name, this);\n    }\n    ngOnDestroy() {\n        this.stackCtrl.destroy();\n        this.inputBinder?.unsubscribeFromRouteData(this);\n    }\n    getContext() {\n        return this.parentContexts.getContext(this.name);\n    }\n    ngOnInit() {\n        this.initializeOutletWithName();\n    }\n    // Note: Ionic deviates from the Angular Router implementation here\n    initializeOutletWithName() {\n        if (!this.activated) {\n            // If the outlet was not instantiated at the time the route got activated we need to populate\n            // the outlet when it is initialized (ie inside a NgIf)\n            const context = this.getContext();\n            if (context?.route) {\n                this.activateWith(context.route, context.injector);\n            }\n        }\n        new Promise((resolve) => componentOnReady(this.nativeEl, resolve)).then(() => {\n            if (this._swipeGesture === undefined) {\n                this.swipeGesture = this.config.getBoolean('swipeBackEnabled', this.nativeEl.mode === 'ios');\n            }\n        });\n    }\n    get isActivated() {\n        return !!this.activated;\n    }\n    get component() {\n        if (!this.activated) {\n            throw new Error('Outlet is not activated');\n        }\n        return this.activated.instance;\n    }\n    get activatedRoute() {\n        if (!this.activated) {\n            throw new Error('Outlet is not activated');\n        }\n        return this._activatedRoute;\n    }\n    get activatedRouteData() {\n        if (this._activatedRoute) {\n            return this._activatedRoute.snapshot.data;\n        }\n        return {};\n    }\n    /**\n     * Called when the `RouteReuseStrategy` instructs to detach the subtree\n     */\n    detach() {\n        throw new Error('incompatible reuse strategy');\n    }\n    /**\n     * Called when the `RouteReuseStrategy` instructs to re-attach a previously detached subtree\n     */\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    attach(_ref, _activatedRoute) {\n        throw new Error('incompatible reuse strategy');\n    }\n    deactivate() {\n        if (this.activated) {\n            if (this.activatedView) {\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                const context = this.getContext();\n                this.activatedView.savedData = new Map(context.children['contexts']);\n                /**\n                 * Angular v11.2.10 introduced a change\n                 * where this route context is cleared out when\n                 * a router-outlet is deactivated, However,\n                 * we need this route information in order to\n                 * return a user back to the correct tab when\n                 * leaving and then going back to the tab context.\n                 */\n                const primaryOutlet = this.activatedView.savedData.get('primary');\n                if (primaryOutlet && context.route) {\n                    primaryOutlet.route = { ...context.route };\n                }\n                /**\n                 * Ensure we are saving the NavigationExtras\n                 * data otherwise it will be lost\n                 */\n                this.activatedView.savedExtras = {};\n                if (context.route) {\n                    const contextSnapshot = context.route.snapshot;\n                    this.activatedView.savedExtras.queryParams = contextSnapshot.queryParams;\n                    this.activatedView.savedExtras.fragment = contextSnapshot.fragment;\n                }\n            }\n            const c = this.component;\n            this.activatedView = null;\n            this.activated = null;\n            this._activatedRoute = null;\n            this.deactivateEvents.emit(c);\n        }\n    }\n    activateWith(activatedRoute, environmentInjector) {\n        if (this.isActivated) {\n            throw new Error('Cannot activate an already activated outlet');\n        }\n        this._activatedRoute = activatedRoute;\n        let cmpRef;\n        let enteringView = this.stackCtrl.getExistingView(activatedRoute);\n        if (enteringView) {\n            cmpRef = this.activated = enteringView.ref;\n            const saved = enteringView.savedData;\n            if (saved) {\n                // self-restore\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                const context = this.getContext();\n                context.children['contexts'] = saved;\n            }\n            // Updated activated route proxy for this component\n            this.updateActivatedRouteProxy(cmpRef.instance, activatedRoute);\n        }\n        else {\n            const snapshot = activatedRoute._futureSnapshot;\n            /**\n             * Angular 14 introduces a new `loadComponent` property to the route config.\n             * This function will assign a `component` property to the route snapshot.\n             * We check for the presence of this property to determine if the route is\n             * using standalone components.\n             */\n            const childContexts = this.parentContexts.getOrCreateContext(this.name).children;\n            // We create an activated route proxy object that will maintain future updates for this component\n            // over its lifecycle in the stack.\n            const component$ = new BehaviorSubject(null);\n            const activatedRouteProxy = this.createActivatedRouteProxy(component$, activatedRoute);\n            const injector = new OutletInjector(activatedRouteProxy, childContexts, this.location.injector);\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            const component = snapshot.routeConfig.component ?? snapshot.component;\n            /**\n             * View components need to be added as a child of ion-router-outlet\n             * for page transitions and swipe to go back.\n             * However, createComponent mounts components as siblings of the\n             * ViewContainerRef. As a result, outletContent must reference\n             * an ng-container inside of ion-router-outlet and not\n             * ion-router-outlet itself.\n             */\n            cmpRef = this.activated = this.outletContent.createComponent(component, {\n                index: this.outletContent.length,\n                injector,\n                environmentInjector: environmentInjector ?? this.environmentInjector,\n            });\n            // Once the component is created we can push it to our local subject supplied to the proxy\n            component$.next(cmpRef.instance);\n            // Calling `markForCheck` to make sure we will run the change detection when the\n            // `RouterOutlet` is inside a `ChangeDetectionStrategy.OnPush` component.\n            /**\n             * At this point this.activated has been set earlier\n             * in this function, so it is guaranteed to be non-null.\n             */\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            enteringView = this.stackCtrl.createView(this.activated, activatedRoute);\n            // Store references to the proxy by component\n            this.proxyMap.set(cmpRef.instance, activatedRouteProxy);\n            this.currentActivatedRoute$.next({ component: cmpRef.instance, activatedRoute });\n        }\n        this.inputBinder?.bindActivatedRouteToOutletComponent(this);\n        this.activatedView = enteringView;\n        /**\n         * The top outlet is set prior to the entering view's transition completing,\n         * so that when we have nested outlets (e.g. ion-tabs inside an ion-router-outlet),\n         * the tabs outlet will be assigned as the top outlet when a view inside tabs is\n         * activated.\n         *\n         * In this scenario, activeWith is called for both the tabs and the root router outlet.\n         * To avoid a race condition, we assign the top outlet synchronously.\n         */\n        this.navCtrl.setTopOutlet(this);\n        const leavingView = this.stackCtrl.getActiveView();\n        this.stackWillChange.emit({\n            enteringView,\n            tabSwitch: isTabSwitch(enteringView, leavingView),\n        });\n        this.stackCtrl.setActive(enteringView).then((data) => {\n            this.activateEvents.emit(cmpRef.instance);\n            this.stackDidChange.emit(data);\n        });\n    }\n    /**\n     * Returns `true` if there are pages in the stack to go back.\n     */\n    canGoBack(deep = 1, stackId) {\n        return this.stackCtrl.canGoBack(deep, stackId);\n    }\n    /**\n     * Resolves to `true` if it the outlet was able to sucessfully pop the last N pages.\n     */\n    pop(deep = 1, stackId) {\n        return this.stackCtrl.pop(deep, stackId);\n    }\n    /**\n     * Returns the URL of the active page of each stack.\n     */\n    getLastUrl(stackId) {\n        const active = this.stackCtrl.getLastUrl(stackId);\n        return active ? active.url : undefined;\n    }\n    /**\n     * Returns the RouteView of the active page of each stack.\n     * @internal\n     */\n    getLastRouteView(stackId) {\n        return this.stackCtrl.getLastUrl(stackId);\n    }\n    /**\n     * Returns the root view in the tab stack.\n     * @internal\n     */\n    getRootView(stackId) {\n        return this.stackCtrl.getRootUrl(stackId);\n    }\n    /**\n     * Returns the active stack ID. In the context of ion-tabs, it means the active tab.\n     */\n    getActiveStackId() {\n        return this.stackCtrl.getActiveStackId();\n    }\n    /**\n     * Since the activated route can change over the life time of a component in an ion router outlet, we create\n     * a proxy so that we can update the values over time as a user navigates back to components already in the stack.\n     */\n    createActivatedRouteProxy(component$, activatedRoute) {\n        const proxy = new ActivatedRoute();\n        proxy._futureSnapshot = activatedRoute._futureSnapshot;\n        proxy._routerState = activatedRoute._routerState;\n        proxy.snapshot = activatedRoute.snapshot;\n        proxy.outlet = activatedRoute.outlet;\n        proxy.component = activatedRoute.component;\n        // Setup wrappers for the observables so consumers don't have to worry about switching to new observables as the state updates\n        proxy._paramMap = this.proxyObservable(component$, 'paramMap');\n        proxy._queryParamMap = this.proxyObservable(component$, 'queryParamMap');\n        proxy.url = this.proxyObservable(component$, 'url');\n        proxy.params = this.proxyObservable(component$, 'params');\n        proxy.queryParams = this.proxyObservable(component$, 'queryParams');\n        proxy.fragment = this.proxyObservable(component$, 'fragment');\n        proxy.data = this.proxyObservable(component$, 'data');\n        return proxy;\n    }\n    /**\n     * Create a wrapped observable that will switch to the latest activated route matched by the given component\n     */\n    proxyObservable(component$, path) {\n        return component$.pipe(\n        // First wait until the component instance is pushed\n        filter((component) => !!component), switchMap((component) => this.currentActivatedRoute$.pipe(filter((current) => current !== null && current.component === component), switchMap((current) => current && current.activatedRoute[path]), distinctUntilChanged())));\n    }\n    /**\n     * Updates the activated route proxy for the given component to the new incoming router state\n     */\n    updateActivatedRouteProxy(component, activatedRoute) {\n        const proxy = this.proxyMap.get(component);\n        if (!proxy) {\n            throw new Error(`Could not find activated route proxy for view`);\n        }\n        proxy._futureSnapshot = activatedRoute._futureSnapshot;\n        proxy._routerState = activatedRoute._routerState;\n        proxy.snapshot = activatedRoute.snapshot;\n        proxy.outlet = activatedRoute.outlet;\n        proxy.component = activatedRoute.component;\n        this.currentActivatedRoute$.next({ component, activatedRoute });\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonRouterOutlet, deps: [{ token: 'name', attribute: true }, { token: 'tabs', attribute: true, optional: true }, { token: i1.Location }, { token: i0.ElementRef }, { token: i3.Router }, { token: i0.NgZone }, { token: i3.ActivatedRoute }, { token: IonRouterOutlet, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: IonRouterOutlet, selector: \"ion-router-outlet\", inputs: { animated: \"animated\", animation: \"animation\", mode: \"mode\", swipeGesture: \"swipeGesture\", name: \"name\" }, outputs: { stackWillChange: \"stackWillChange\", stackDidChange: \"stackDidChange\", activateEvents: \"activate\", deactivateEvents: \"deactivate\" }, exportAs: [\"outlet\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonRouterOutlet, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ion-router-outlet',\n                    exportAs: 'outlet',\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: ['animated', 'animation', 'mode', 'swipeGesture'],\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['name']\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Attribute,\n                    args: ['tabs']\n                }] }, { type: i1.Location }, { type: i0.ElementRef }, { type: i3.Router }, { type: i0.NgZone }, { type: i3.ActivatedRoute }, { type: IonRouterOutlet, decorators: [{\n                    type: SkipSelf\n                }, {\n                    type: Optional\n                }] }]; }, propDecorators: { name: [{\n                type: Input\n            }], stackWillChange: [{\n                type: Output\n            }], stackDidChange: [{\n                type: Output\n            }], activateEvents: [{\n                type: Output,\n                args: ['activate']\n            }], deactivateEvents: [{\n                type: Output,\n                args: ['deactivate']\n            }] } });\nclass OutletInjector {\n    route;\n    childContexts;\n    parent;\n    constructor(route, childContexts, parent) {\n        this.route = route;\n        this.childContexts = childContexts;\n        this.parent = parent;\n    }\n    get(token, notFoundValue) {\n        if (token === ActivatedRoute) {\n            return this.route;\n        }\n        if (token === ChildrenOutletContexts) {\n            return this.childContexts;\n        }\n        return this.parent.get(token, notFoundValue);\n    }\n}\n// TODO: FW-4785 - Remove this once Angular 15 support is dropped\nconst INPUT_BINDER = new InjectionToken('');\n/**\n * Injectable used as a tree-shakable provider for opting in to binding router data to component\n * inputs.\n *\n * The RouterOutlet registers itself with this service when an `ActivatedRoute` is attached or\n * activated. When this happens, the service subscribes to the `ActivatedRoute` observables (params,\n * queryParams, data) and sets the inputs of the component using `ComponentRef.setInput`.\n * Importantly, when an input does not have an item in the route data with a matching key, this\n * input is set to `undefined`. If it were not done this way, the previous information would be\n * retained if the data got removed from the route (i.e. if a query parameter is removed).\n *\n * The `RouterOutlet` should unregister itself when destroyed via `unsubscribeFromRouteData` so that\n * the subscriptions are cleaned up.\n */\nclass RoutedComponentInputBinder {\n    outletDataSubscriptions = new Map();\n    bindActivatedRouteToOutletComponent(outlet) {\n        this.unsubscribeFromRouteData(outlet);\n        this.subscribeToRouteData(outlet);\n    }\n    unsubscribeFromRouteData(outlet) {\n        this.outletDataSubscriptions.get(outlet)?.unsubscribe();\n        this.outletDataSubscriptions.delete(outlet);\n    }\n    subscribeToRouteData(outlet) {\n        const { activatedRoute } = outlet;\n        const dataSubscription = combineLatest([activatedRoute.queryParams, activatedRoute.params, activatedRoute.data])\n            .pipe(switchMap(([queryParams, params, data], index) => {\n            data = { ...queryParams, ...params, ...data };\n            // Get the first result from the data subscription synchronously so it's available to\n            // the component as soon as possible (and doesn't require a second change detection).\n            if (index === 0) {\n                return of(data);\n            }\n            // Promise.resolve is used to avoid synchronously writing the wrong data when\n            // two of the Observables in the `combineLatest` stream emit one after\n            // another.\n            return Promise.resolve(data);\n        }))\n            .subscribe((data) => {\n            // Outlet may have been deactivated or changed names to be associated with a different\n            // route\n            if (!outlet.isActivated ||\n                !outlet.activatedComponentRef ||\n                outlet.activatedRoute !== activatedRoute ||\n                activatedRoute.component === null) {\n                this.unsubscribeFromRouteData(outlet);\n                return;\n            }\n            const mirror = reflectComponentType(activatedRoute.component);\n            if (!mirror) {\n                this.unsubscribeFromRouteData(outlet);\n                return;\n            }\n            for (const { templateName } of mirror.inputs) {\n                outlet.activatedComponentRef.setInput(templateName, data[templateName]);\n            }\n        });\n        this.outletDataSubscriptions.set(outlet, dataSubscription);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RoutedComponentInputBinder, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    /** @nocollapse */ static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RoutedComponentInputBinder });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RoutedComponentInputBinder, decorators: [{\n            type: Injectable\n        }] });\nconst provideComponentInputBinding = () => {\n    return {\n        provide: INPUT_BINDER,\n        useFactory: componentInputBindingFactory,\n        deps: [Router],\n    };\n};\nfunction componentInputBindingFactory(router) {\n    /**\n     * We cast the router to any here, since the componentInputBindingEnabled\n     * property is not available until Angular v16.\n     */\n    if (router?.componentInputBindingEnabled) {\n        return new RoutedComponentInputBinder();\n    }\n    return null;\n}\n\nconst BACK_BUTTON_INPUTS = ['color', 'defaultHref', 'disabled', 'icon', 'mode', 'routerAnimation', 'text', 'type'];\nlet IonBackButton = class IonBackButton {\n    routerOutlet;\n    navCtrl;\n    config;\n    r;\n    z;\n    el;\n    constructor(routerOutlet, navCtrl, config, r, z, c) {\n        this.routerOutlet = routerOutlet;\n        this.navCtrl = navCtrl;\n        this.config = config;\n        this.r = r;\n        this.z = z;\n        c.detach();\n        this.el = this.r.nativeElement;\n    }\n    /**\n     * @internal\n     */\n    onClick(ev) {\n        const defaultHref = this.defaultHref || this.config.get('backButtonDefaultHref');\n        if (this.routerOutlet?.canGoBack()) {\n            this.navCtrl.setDirection('back', undefined, undefined, this.routerAnimation);\n            this.routerOutlet.pop();\n            ev.preventDefault();\n        }\n        else if (defaultHref != null) {\n            this.navCtrl.navigateBack(defaultHref, { animation: this.routerAnimation });\n            ev.preventDefault();\n        }\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonBackButton, deps: [{ token: IonRouterOutlet, optional: true }, { token: NavController }, { token: Config }, { token: i0.ElementRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: IonBackButton, inputs: { color: \"color\", defaultHref: \"defaultHref\", disabled: \"disabled\", icon: \"icon\", mode: \"mode\", routerAnimation: \"routerAnimation\", text: \"text\", type: \"type\" }, host: { listeners: { \"click\": \"onClick($event)\" } }, ngImport: i0 });\n};\nIonBackButton = __decorate([\n    ProxyCmp({\n        inputs: BACK_BUTTON_INPUTS,\n    })\n], IonBackButton);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonBackButton, decorators: [{\n            type: Directive,\n            args: [{\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: BACK_BUTTON_INPUTS,\n                }]\n        }], ctorParameters: function () { return [{ type: IonRouterOutlet, decorators: [{\n                    type: Optional\n                }] }, { type: NavController }, { type: Config }, { type: i0.ElementRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }] } });\n\n/**\n * Adds support for Ionic routing directions and animations to the base Angular router link directive.\n *\n * When the router link is clicked, the directive will assign the direction and\n * animation so that the routing integration will transition correctly.\n */\nclass RouterLinkDelegateDirective {\n    locationStrategy;\n    navCtrl;\n    elementRef;\n    router;\n    routerLink;\n    routerDirection = 'forward';\n    routerAnimation;\n    constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n        this.locationStrategy = locationStrategy;\n        this.navCtrl = navCtrl;\n        this.elementRef = elementRef;\n        this.router = router;\n        this.routerLink = routerLink;\n    }\n    ngOnInit() {\n        this.updateTargetUrlAndHref();\n        this.updateTabindex();\n    }\n    ngOnChanges() {\n        this.updateTargetUrlAndHref();\n    }\n    /**\n     * The `tabindex` is set to `0` by default on the host element when\n     * the `routerLink` directive is used. This causes issues with Ionic\n     * components that wrap an `a` or `button` element, such as `ion-item`.\n     * See issue https://github.com/angular/angular/issues/28345\n     *\n     * This method removes the `tabindex` attribute from the host element\n     * to allow the Ionic component to manage the focus state correctly.\n     */\n    updateTabindex() {\n        // Ionic components that render a native anchor or button element\n        const ionicComponents = [\n            'ION-BACK-BUTTON',\n            'ION-BREADCRUMB',\n            'ION-BUTTON',\n            'ION-CARD',\n            'ION-FAB-BUTTON',\n            'ION-ITEM',\n            'ION-ITEM-OPTION',\n            'ION-MENU-BUTTON',\n            'ION-SEGMENT-BUTTON',\n            'ION-TAB-BUTTON',\n        ];\n        const hostElement = this.elementRef.nativeElement;\n        if (ionicComponents.includes(hostElement.tagName)) {\n            if (hostElement.getAttribute('tabindex') === '0') {\n                hostElement.removeAttribute('tabindex');\n            }\n        }\n    }\n    updateTargetUrlAndHref() {\n        if (this.routerLink?.urlTree) {\n            const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n            this.elementRef.nativeElement.href = href;\n        }\n    }\n    /**\n     * @internal\n     */\n    onClick(ev) {\n        this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n        /**\n         * This prevents the browser from\n         * performing a page reload when pressing\n         * an Ionic component with routerLink.\n         * The page reload interferes with routing\n         * and causes ion-back-button to disappear\n         * since the local history is wiped on reload.\n         */\n        ev.preventDefault();\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RouterLinkDelegateDirective, deps: [{ token: i1.LocationStrategy }, { token: NavController }, { token: i0.ElementRef }, { token: i3.Router }, { token: i3.RouterLink, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: RouterLinkDelegateDirective, selector: \":not(a):not(area)[routerLink]\", inputs: { routerDirection: \"routerDirection\", routerAnimation: \"routerAnimation\" }, host: { listeners: { \"click\": \"onClick($event)\" } }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RouterLinkDelegateDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: ':not(a):not(area)[routerLink]',\n                }]\n        }], ctorParameters: function () { return [{ type: i1.LocationStrategy }, { type: NavController }, { type: i0.ElementRef }, { type: i3.Router }, { type: i3.RouterLink, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { routerDirection: [{\n                type: Input\n            }], routerAnimation: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }] } });\nclass RouterLinkWithHrefDelegateDirective {\n    locationStrategy;\n    navCtrl;\n    elementRef;\n    router;\n    routerLink;\n    routerDirection = 'forward';\n    routerAnimation;\n    constructor(locationStrategy, navCtrl, elementRef, router, routerLink) {\n        this.locationStrategy = locationStrategy;\n        this.navCtrl = navCtrl;\n        this.elementRef = elementRef;\n        this.router = router;\n        this.routerLink = routerLink;\n    }\n    ngOnInit() {\n        this.updateTargetUrlAndHref();\n    }\n    ngOnChanges() {\n        this.updateTargetUrlAndHref();\n    }\n    updateTargetUrlAndHref() {\n        if (this.routerLink?.urlTree) {\n            const href = this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));\n            this.elementRef.nativeElement.href = href;\n        }\n    }\n    /**\n     * @internal\n     */\n    onClick() {\n        this.navCtrl.setDirection(this.routerDirection, undefined, undefined, this.routerAnimation);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RouterLinkWithHrefDelegateDirective, deps: [{ token: i1.LocationStrategy }, { token: NavController }, { token: i0.ElementRef }, { token: i3.Router }, { token: i3.RouterLink, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: RouterLinkWithHrefDelegateDirective, selector: \"a[routerLink],area[routerLink]\", inputs: { routerDirection: \"routerDirection\", routerAnimation: \"routerAnimation\" }, host: { listeners: { \"click\": \"onClick()\" } }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RouterLinkWithHrefDelegateDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'a[routerLink],area[routerLink]',\n                }]\n        }], ctorParameters: function () { return [{ type: i1.LocationStrategy }, { type: NavController }, { type: i0.ElementRef }, { type: i3.Router }, { type: i3.RouterLink, decorators: [{\n                    type: Optional\n                }] }]; }, propDecorators: { routerDirection: [{\n                type: Input\n            }], routerAnimation: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click']\n            }] } });\n\nconst NAV_INPUTS = ['animated', 'animation', 'root', 'rootParams', 'swipeGesture'];\nconst NAV_METHODS = [\n    'push',\n    'insert',\n    'insertPages',\n    'pop',\n    'popTo',\n    'popToRoot',\n    'removeIndex',\n    'setRoot',\n    'setPages',\n    'getActive',\n    'getByIndex',\n    'canGoBack',\n    'getPrevious',\n];\nlet IonNav = class IonNav {\n    z;\n    el;\n    constructor(ref, environmentInjector, injector, angularDelegate, z, c) {\n        this.z = z;\n        c.detach();\n        this.el = ref.nativeElement;\n        ref.nativeElement.delegate = angularDelegate.create(environmentInjector, injector);\n        proxyOutputs(this, this.el, ['ionNavDidChange', 'ionNavWillChange']);\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonNav, deps: [{ token: i0.ElementRef }, { token: i0.EnvironmentInjector }, { token: i0.Injector }, { token: AngularDelegate }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: IonNav, inputs: { animated: \"animated\", animation: \"animation\", root: \"root\", rootParams: \"rootParams\", swipeGesture: \"swipeGesture\" }, ngImport: i0 });\n};\nIonNav = __decorate([\n    ProxyCmp({\n        inputs: NAV_INPUTS,\n        methods: NAV_METHODS,\n    })\n], IonNav);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonNav, decorators: [{\n            type: Directive,\n            args: [{\n                    // eslint-disable-next-line @angular-eslint/no-inputs-metadata-property\n                    inputs: NAV_INPUTS,\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.EnvironmentInjector }, { type: i0.Injector }, { type: AngularDelegate }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }]; } });\n\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass IonTabs {\n    navCtrl;\n    tabsInner;\n    /**\n     * Emitted before the tab view is changed.\n     */\n    ionTabsWillChange = new EventEmitter();\n    /**\n     * Emitted after the tab view is changed.\n     */\n    ionTabsDidChange = new EventEmitter();\n    tabBarSlot = 'bottom';\n    hasTab = false;\n    selectedTab;\n    leavingTab;\n    constructor(navCtrl) {\n        this.navCtrl = navCtrl;\n    }\n    ngAfterViewInit() {\n        /**\n         * Developers must pass at least one ion-tab\n         * inside of ion-tabs if they want to use a\n         * basic tab-based navigation without the\n         * history stack or URL updates associated\n         * with the router.\n         */\n        const firstTab = this.tabs.length > 0 ? this.tabs.first : undefined;\n        if (firstTab) {\n            this.hasTab = true;\n            this.setActiveTab(firstTab.tab);\n            this.tabSwitch();\n        }\n    }\n    ngAfterContentInit() {\n        this.detectSlotChanges();\n    }\n    ngAfterContentChecked() {\n        this.detectSlotChanges();\n    }\n    /**\n     * @internal\n     */\n    onStackWillChange({ enteringView, tabSwitch }) {\n        const stackId = enteringView.stackId;\n        if (tabSwitch && stackId !== undefined) {\n            this.ionTabsWillChange.emit({ tab: stackId });\n        }\n    }\n    /**\n     * @internal\n     */\n    onStackDidChange({ enteringView, tabSwitch }) {\n        const stackId = enteringView.stackId;\n        if (tabSwitch && stackId !== undefined) {\n            if (this.tabBar) {\n                this.tabBar.selectedTab = stackId;\n            }\n            this.ionTabsDidChange.emit({ tab: stackId });\n        }\n    }\n    /**\n     * When a tab button is clicked, there are several scenarios:\n     * 1. If the selected tab is currently active (the tab button has been clicked\n     *    again), then it should go to the root view for that tab.\n     *\n     *   a. Get the saved root view from the router outlet. If the saved root view\n     *      matches the tabRootUrl, set the route view to this view including the\n     *      navigation extras.\n     *   b. If the saved root view from the router outlet does\n     *      not match, navigate to the tabRootUrl. No navigation extras are\n     *      included.\n     *\n     * 2. If the current tab tab is not currently selected, get the last route\n     *    view from the router outlet.\n     *\n     *   a. If the last route view exists, navigate to that view including any\n     *      navigation extras\n     *   b. If the last route view doesn't exist, then navigate\n     *      to the default tabRootUrl\n     */\n    select(tabOrEvent) {\n        const isTabString = typeof tabOrEvent === 'string';\n        const tab = isTabString ? tabOrEvent : tabOrEvent.detail.tab;\n        /**\n         * If the tabs are not using the router, then\n         * the tab switch logic is handled by the tabs\n         * component itself.\n         */\n        if (this.hasTab) {\n            this.setActiveTab(tab);\n            this.tabSwitch();\n            return;\n        }\n        const alreadySelected = this.outlet.getActiveStackId() === tab;\n        const tabRootUrl = `${this.outlet.tabsPrefix}/${tab}`;\n        /**\n         * If this is a nested tab, prevent the event\n         * from bubbling otherwise the outer tabs\n         * will respond to this event too, causing\n         * the app to get directed to the wrong place.\n         */\n        if (!isTabString) {\n            tabOrEvent.stopPropagation();\n        }\n        if (alreadySelected) {\n            const activeStackId = this.outlet.getActiveStackId();\n            const activeView = this.outlet.getLastRouteView(activeStackId);\n            // If on root tab, do not navigate to root tab again\n            if (activeView?.url === tabRootUrl) {\n                return;\n            }\n            const rootView = this.outlet.getRootView(tab);\n            const navigationExtras = rootView && tabRootUrl === rootView.url && rootView.savedExtras;\n            return this.navCtrl.navigateRoot(tabRootUrl, {\n                ...navigationExtras,\n                animated: true,\n                animationDirection: 'back',\n            });\n        }\n        else {\n            const lastRoute = this.outlet.getLastRouteView(tab);\n            /**\n             * If there is a lastRoute, goto that, otherwise goto the fallback url of the\n             * selected tab\n             */\n            const url = lastRoute?.url || tabRootUrl;\n            const navigationExtras = lastRoute?.savedExtras;\n            return this.navCtrl.navigateRoot(url, {\n                ...navigationExtras,\n                animated: true,\n                animationDirection: 'back',\n            });\n        }\n    }\n    setActiveTab(tab) {\n        const tabs = this.tabs;\n        const selectedTab = tabs.find((t) => t.tab === tab);\n        if (!selectedTab) {\n            console.error(`[Ionic Error]: Tab with id: \"${tab}\" does not exist`);\n            return;\n        }\n        this.leavingTab = this.selectedTab;\n        this.selectedTab = selectedTab;\n        this.ionTabsWillChange.emit({ tab });\n        selectedTab.el.active = true;\n    }\n    tabSwitch() {\n        const { selectedTab, leavingTab } = this;\n        if (this.tabBar && selectedTab) {\n            this.tabBar.selectedTab = selectedTab.tab;\n        }\n        if (leavingTab?.tab !== selectedTab?.tab) {\n            if (leavingTab?.el) {\n                leavingTab.el.active = false;\n            }\n        }\n        if (selectedTab) {\n            this.ionTabsDidChange.emit({ tab: selectedTab.tab });\n        }\n    }\n    getSelected() {\n        if (this.hasTab) {\n            return this.selectedTab?.tab;\n        }\n        return this.outlet.getActiveStackId();\n    }\n    /**\n     * Detects changes to the slot attribute of the tab bar.\n     *\n     * If the slot attribute has changed, then the tab bar\n     * should be relocated to the new slot position.\n     */\n    detectSlotChanges() {\n        this.tabBars.forEach((tabBar) => {\n            // el is a protected attribute from the generated component wrapper\n            const currentSlot = tabBar.el.getAttribute('slot');\n            if (currentSlot !== this.tabBarSlot) {\n                this.tabBarSlot = currentSlot;\n                this.relocateTabBar();\n            }\n        });\n    }\n    /**\n     * Relocates the tab bar to the new slot position.\n     */\n    relocateTabBar() {\n        /**\n         * `el` is a protected attribute from the generated component wrapper.\n         * To avoid having to manually create the wrapper for tab bar, we\n         * cast the tab bar to any and access the protected attribute.\n         */\n        const tabBar = this.tabBar.el;\n        if (this.tabBarSlot === 'top') {\n            /**\n             * A tab bar with a slot of \"top\" should be inserted\n             * at the top of the container.\n             */\n            this.tabsInner.nativeElement.before(tabBar);\n        }\n        else {\n            /**\n             * A tab bar with a slot of \"bottom\" or without a slot\n             * should be inserted at the end of the container.\n             */\n            this.tabsInner.nativeElement.after(tabBar);\n        }\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonTabs, deps: [{ token: NavController }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: IonTabs, selector: \"ion-tabs\", outputs: { ionTabsWillChange: \"ionTabsWillChange\", ionTabsDidChange: \"ionTabsDidChange\" }, host: { listeners: { \"ionTabButtonClick\": \"select($event)\" } }, viewQueries: [{ propertyName: \"tabsInner\", first: true, predicate: [\"tabsInner\"], descendants: true, read: ElementRef, static: true }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: IonTabs, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ion-tabs',\n                }]\n        }], ctorParameters: function () { return [{ type: NavController }]; }, propDecorators: { tabsInner: [{\n                type: ViewChild,\n                args: ['tabsInner', { read: ElementRef, static: true }]\n            }], ionTabsWillChange: [{\n                type: Output\n            }], ionTabsDidChange: [{\n                type: Output\n            }], select: [{\n                type: HostListener,\n                args: ['ionTabButtonClick', ['$event']]\n            }] } });\n\nconst raf = (h) => {\n    if (typeof __zone_symbol__requestAnimationFrame === 'function') {\n        return __zone_symbol__requestAnimationFrame(h);\n    }\n    if (typeof requestAnimationFrame === 'function') {\n        return requestAnimationFrame(h);\n    }\n    return setTimeout(h);\n};\n\n// TODO(FW-2827): types\nclass ValueAccessor {\n    injector;\n    elementRef;\n    onChange = () => {\n        /**/\n    };\n    onTouched = () => {\n        /**/\n    };\n    lastValue;\n    statusChanges;\n    constructor(injector, elementRef) {\n        this.injector = injector;\n        this.elementRef = elementRef;\n    }\n    writeValue(value) {\n        this.elementRef.nativeElement.value = this.lastValue = value;\n        setIonicClasses(this.elementRef);\n    }\n    /**\n     * Notifies the ControlValueAccessor of a change in the value of the control.\n     *\n     * This is called by each of the ValueAccessor directives when we want to update\n     * the status and validity of the form control. For example with text components this\n     * is called when the ionInput event is fired. For select components this is called\n     * when the ionChange event is fired.\n     *\n     * This also updates the Ionic form status classes on the element.\n     *\n     * @param el The component element.\n     * @param value The new value of the control.\n     */\n    handleValueChange(el, value) {\n        if (el === this.elementRef.nativeElement) {\n            if (value !== this.lastValue) {\n                this.lastValue = value;\n                this.onChange(value);\n            }\n            setIonicClasses(this.elementRef);\n        }\n    }\n    _handleBlurEvent(el) {\n        if (el === this.elementRef.nativeElement) {\n            this.onTouched();\n            setIonicClasses(this.elementRef);\n        }\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    setDisabledState(isDisabled) {\n        this.elementRef.nativeElement.disabled = isDisabled;\n    }\n    ngOnDestroy() {\n        if (this.statusChanges) {\n            this.statusChanges.unsubscribe();\n        }\n    }\n    ngAfterViewInit() {\n        let ngControl;\n        try {\n            ngControl = this.injector.get(NgControl);\n        }\n        catch {\n            /* No FormControl or ngModel binding */\n        }\n        if (!ngControl) {\n            return;\n        }\n        // Listen for changes in validity, disabled, or pending states\n        if (ngControl.statusChanges) {\n            this.statusChanges = ngControl.statusChanges.subscribe(() => setIonicClasses(this.elementRef));\n        }\n        /**\n         * TODO FW-2787: Remove this in favor of https://github.com/angular/angular/issues/10887\n         * whenever it is implemented.\n         */\n        const formControl = ngControl.control;\n        if (formControl) {\n            const methodsToPatch = ['markAsTouched', 'markAllAsTouched', 'markAsUntouched', 'markAsDirty', 'markAsPristine'];\n            methodsToPatch.forEach((method) => {\n                if (typeof formControl[method] !== 'undefined') {\n                    const oldFn = formControl[method].bind(formControl);\n                    formControl[method] = (...params) => {\n                        oldFn(...params);\n                        setIonicClasses(this.elementRef);\n                    };\n                }\n            });\n        }\n    }\n    /** @nocollapse */ static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: ValueAccessor, deps: [{ token: i0.Injector }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\n    /** @nocollapse */ static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.2.12\", type: ValueAccessor, host: { listeners: { \"ionBlur\": \"_handleBlurEvent($event.target)\" } }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: ValueAccessor, decorators: [{\n            type: Directive\n        }], ctorParameters: function () { return [{ type: i0.Injector }, { type: i0.ElementRef }]; }, propDecorators: { _handleBlurEvent: [{\n                type: HostListener,\n                args: ['ionBlur', ['$event.target']]\n            }] } });\nconst setIonicClasses = (element) => {\n    raf(() => {\n        const input = element.nativeElement;\n        const hasValue = input.value != null && input.value.toString().length > 0;\n        const classes = getClasses(input);\n        setClasses(input, classes);\n        const item = input.closest('ion-item');\n        if (item) {\n            if (hasValue) {\n                setClasses(item, [...classes, 'item-has-value']);\n            }\n            else {\n                setClasses(item, classes);\n            }\n        }\n    });\n};\nconst getClasses = (element) => {\n    const classList = element.classList;\n    const classes = [];\n    for (let i = 0; i < classList.length; i++) {\n        const item = classList.item(i);\n        if (item !== null && startsWith(item, 'ng-')) {\n            classes.push(`ion-${item.substring(3)}`);\n        }\n    }\n    return classes;\n};\nconst setClasses = (element, classes) => {\n    const classList = element.classList;\n    classList.remove('ion-valid', 'ion-invalid', 'ion-touched', 'ion-untouched', 'ion-dirty', 'ion-pristine');\n    classList.add(...classes);\n};\nconst startsWith = (input, search) => {\n    return input.substring(0, search.length) === search;\n};\n\n/**\n * Provides a way to customize when activated routes get reused.\n */\nclass IonicRouteStrategy {\n    /**\n     * Whether the given route should detach for later reuse.\n     */\n    shouldDetach(_route) {\n        return false;\n    }\n    /**\n     * Returns `false`, meaning the route (and its subtree) is never reattached\n     */\n    shouldAttach(_route) {\n        return false;\n    }\n    /**\n     * A no-op; the route is never stored since this strategy never detaches routes for later re-use.\n     */\n    store(_route, _detachedTree) {\n        return;\n    }\n    /**\n     * Returns `null` because this strategy does not store routes for later re-use.\n     */\n    retrieve(_route) {\n        return null;\n    }\n    /**\n     * Determines if a route should be reused.\n     * This strategy returns `true` when the future route config and\n     * current route config are identical and all route parameters are identical.\n     */\n    shouldReuseRoute(future, curr) {\n        if (future.routeConfig !== curr.routeConfig) {\n            return false;\n        }\n        // checking router params\n        const futureParams = future.params;\n        const currentParams = curr.params;\n        const keysA = Object.keys(futureParams);\n        const keysB = Object.keys(currentParams);\n        if (keysA.length !== keysB.length) {\n            return false;\n        }\n        // Test for A's keys different from B.\n        for (const key of keysA) {\n            if (currentParams[key] !== futureParams[key]) {\n                return false;\n            }\n        }\n        return true;\n    }\n}\n\n// TODO(FW-2827): types\nclass OverlayBaseController {\n    ctrl;\n    constructor(ctrl) {\n        this.ctrl = ctrl;\n    }\n    /**\n     * Creates a new overlay\n     */\n    create(opts) {\n        return this.ctrl.create((opts || {}));\n    }\n    /**\n     * When `id` is not provided, it dismisses the top overlay.\n     */\n    dismiss(data, role, id) {\n        return this.ctrl.dismiss(data, role, id);\n    }\n    /**\n     * Returns the top overlay.\n     */\n    getTop() {\n        return this.ctrl.getTop();\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularDelegate, Config, ConfigToken, DomController, IonBackButton, IonModal, IonNav, IonPopover, IonRouterOutlet, IonTabs, IonicRouteStrategy, MenuController, NavController, NavParams, OverlayBaseController, Platform, ProxyCmp, RouterLinkDelegateDirective, RouterLinkWithHrefDelegateDirective, ValueAccessor, bindLifecycleEvents, provideComponentInputBinding, raf, setIonicClasses };\n"], "mappings": ";;;;AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,WAAW,EAAEC,SAAS,EAAEC,YAAY,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,oBAAoB,EAAEC,YAAY,EAAEC,UAAU,EAAEC,SAAS,QAAQ,eAAe;AACjU,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,eAAe,EAAEC,cAAc,EAAEC,sBAAsB,EAAEC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACjH,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,UAAU,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,gBAAgB,QAAQ,wBAAwB;AAChM,SAASC,OAAO,EAAEC,SAAS,EAAEC,eAAe,EAAEC,aAAa,EAAEC,EAAE,QAAQ,MAAM;AAC7E,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,MAAM,EAAEC,SAAS,EAAEC,oBAAoB,QAAQ,gBAAgB;AACxE,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,MAAMC,cAAc,CAAC;EAEjBC,WAAWA,CAACC,cAAc,EAAE;IAAAC,eAAA;IACxB,IAAI,CAACD,cAAc,GAAGA,cAAc;EACxC;EACA;AACJ;AACA;AACA;AACA;EACIE,IAAIA,CAACC,MAAM,EAAE;IACT,OAAO,IAAI,CAACH,cAAc,CAACE,IAAI,CAACC,MAAM,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,KAAKA,CAACD,MAAM,EAAE;IACV,OAAO,IAAI,CAACH,cAAc,CAACI,KAAK,CAACD,MAAM,CAAC;EAC5C;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,MAAMA,CAACF,MAAM,EAAE;IACX,OAAO,IAAI,CAACH,cAAc,CAACK,MAAM,CAACF,MAAM,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,MAAMA,CAACC,YAAY,EAAEJ,MAAM,EAAE;IACzB,OAAO,IAAI,CAACH,cAAc,CAACM,MAAM,CAACC,YAAY,EAAEJ,MAAM,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIK,YAAYA,CAACD,YAAY,EAAEJ,MAAM,EAAE;IAC/B,OAAO,IAAI,CAACH,cAAc,CAACQ,YAAY,CAACD,YAAY,EAAEJ,MAAM,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACIM,MAAMA,CAACN,MAAM,EAAE;IACX,OAAO,IAAI,CAACH,cAAc,CAACS,MAAM,CAACN,MAAM,CAAC;EAC7C;EACA;AACJ;AACA;AACA;EACIO,SAASA,CAACP,MAAM,EAAE;IACd,OAAO,IAAI,CAACH,cAAc,CAACU,SAAS,CAACP,MAAM,CAAC;EAChD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIQ,GAAGA,CAACR,MAAM,EAAE;IACR,OAAO,IAAI,CAACH,cAAc,CAACW,GAAG,CAACR,MAAM,CAAC;EAC1C;EACA;AACJ;AACA;EACIS,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACZ,cAAc,CAACY,OAAO,CAAC,CAAC;EACxC;EACA;AACJ;AACA;EACIC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACb,cAAc,CAACa,QAAQ,CAAC,CAAC;EACzC;EACAC,iBAAiBA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAC/B,OAAO,IAAI,CAAChB,cAAc,CAACc,iBAAiB,CAACC,IAAI,EAAEC,SAAS,CAAC;EACjE;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACjB,cAAc,CAACiB,WAAW,CAAC,CAAC;EAC5C;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAClB,cAAc,CAACkB,YAAY,CAAC,CAAC;EAC7C;EACAC,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;IAC5B,OAAO,IAAI,CAACrB,cAAc,CAACmB,gBAAgB,CAACC,IAAI,EAAEC,OAAO,CAAC;EAC9D;EACAC,SAASA,CAACC,IAAI,EAAE;IACZ,OAAO,IAAI,CAACvB,cAAc,CAACsB,SAAS,CAACC,IAAI,CAAC;EAC9C;EACAC,WAAWA,CAACD,IAAI,EAAE;IACd,OAAO,IAAI,CAACvB,cAAc,CAACwB,WAAW,CAACD,IAAI,CAAC;EAChD;EACAE,QAAQA,CAACF,IAAI,EAAEG,UAAU,EAAEC,QAAQ,EAAE;IACjC,OAAO,IAAI,CAAC3B,cAAc,CAACyB,QAAQ,CAACF,IAAI,EAAEG,UAAU,EAAEC,QAAQ,CAAC;EACnE;AACJ;AAEA,MAAMC,aAAa,CAAC;EAChB;AACJ;AACA;AACA;EACIC,IAAIA,CAACC,EAAE,EAAE;IACLC,QAAQ,CAAC,CAAC,CAACF,IAAI,CAACC,EAAE,CAAC;EACvB;EACA;AACJ;AACA;AACA;EACIE,KAAKA,CAACF,EAAE,EAAE;IACNC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAACF,EAAE,CAAC;EACxB;EACA;AAEJ;AAACG,cAAA,GAjBKL,aAAa;AAAA3B,eAAA,CAAb2B,aAAa,wBAAAM,uBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAewGP,cAAa;AAAA;AACpI;AAAA3B,eAAA,CAhBE2B,aAAa,+BAkB+DhF,EAAE,CAAAwF,kBAAA;EAAAC,KAAA,EAF2CT,cAAa;EAAAU,OAAA,EAAbV,cAAa,CAAAW,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEhK;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAAkF7F,EAAE,CAAA8F,iBAAA,CAAQd,aAAa,EAAc,CAAC;IAC5GR,IAAI,EAAEvE,UAAU;IAChB8F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMT,QAAQ,GAAGA,CAAA,KAAM;EACnB,MAAMa,GAAG,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,IAAI;EACzD,IAAID,GAAG,IAAI,IAAI,EAAE;IACb,MAAME,KAAK,GAAGF,GAAG,CAACE,KAAK;IACvB,IAAIA,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEC,KAAK,EAAE;MACd,OAAOD,KAAK,CAACC,KAAK;IACtB;IACA,OAAO;MACHlB,IAAI,EAAGC,EAAE,IAAKc,GAAG,CAACI,qBAAqB,CAAClB,EAAE,CAAC;MAC3CE,KAAK,EAAGF,EAAE,IAAKc,GAAG,CAACI,qBAAqB,CAAClB,EAAE;IAC/C,CAAC;EACL;EACA,OAAO;IACHD,IAAI,EAAGC,EAAE,IAAKA,EAAE,CAAC,CAAC;IAClBE,KAAK,EAAGF,EAAE,IAAKA,EAAE,CAAC;EACtB,CAAC;AACL,CAAC;AAED,MAAMmB,QAAQ,CAAC;EAqCXlD,WAAWA,CAACmD,GAAG,EAAEC,IAAI,EAAE;IAAAlD,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAjCvB;AACJ;AACA;IAFIA,eAAA,qBAGa,IAAIb,OAAO,CAAC,CAAC;IAC1B;AACJ;AACA;AACA;IAHIa,eAAA,0BAIkB,IAAIb,OAAO,CAAC,CAAC;IAC/B;AACJ;AACA;AACA;IAHIa,eAAA,0BAIkB,IAAIb,OAAO,CAAC,CAAC;IAC/B;AACJ;AACA;AACA;AACA;AACA;IALIa,eAAA,gBAMQ,IAAIb,OAAO,CAAC,CAAC;IACrB;AACJ;AACA;AACA;AACA;IAJIa,eAAA,iBAKS,IAAIb,OAAO,CAAC,CAAC;IACtB;AACJ;AACA;AACA;AACA;IAJIa,eAAA,iBAKS,IAAIb,OAAO,CAAC,CAAC;IAElB,IAAI,CAAC8D,GAAG,GAAGA,GAAG;IACdC,IAAI,CAACC,GAAG,CAAC,MAAM;MAAA,IAAAC,SAAA;MACX,IAAI,CAACT,GAAG,GAAGM,GAAG,CAACI,WAAW;MAC1B,IAAI,CAACC,UAAU,CAACC,qBAAqB,GAAG,UAAUC,QAAQ,EAAEC,QAAQ,EAAE;QAClE,OAAO,IAAI,CAACC,SAAS,CAAEC,EAAE,IAAK;UAC1B,OAAOA,EAAE,CAACC,QAAQ,CAACJ,QAAQ,EAAGK,kBAAkB,IAAKX,IAAI,CAACC,GAAG,CAAC,MAAMM,QAAQ,CAACI,kBAAkB,CAAC,CAAC,CAAC;QACtG,CAAC,CAAC;MACN,CAAC;MACDC,UAAU,CAAC,IAAI,CAACC,KAAK,EAAEd,GAAG,EAAE,OAAO,EAAEC,IAAI,CAAC;MAC1CY,UAAU,CAAC,IAAI,CAACE,MAAM,EAAEf,GAAG,EAAE,QAAQ,EAAEC,IAAI,CAAC;MAC5CY,UAAU,CAAC,IAAI,CAACR,UAAU,EAAEL,GAAG,EAAE,eAAe,EAAEC,IAAI,CAAC;MACvDY,UAAU,CAAC,IAAI,CAACG,MAAM,EAAE,IAAI,CAACtB,GAAG,EAAE,QAAQ,EAAEO,IAAI,CAAC;MACjDY,UAAU,CAAC,IAAI,CAACI,eAAe,EAAE,IAAI,CAACvB,GAAG,EAAE,oBAAoB,EAAEO,IAAI,CAAC;MACtEY,UAAU,CAAC,IAAI,CAACK,eAAe,EAAE,IAAI,CAACxB,GAAG,EAAE,oBAAoB,EAAEO,IAAI,CAAC;MACtE,IAAIkB,YAAY;MAChB,IAAI,CAACC,aAAa,GAAG,IAAIC,OAAO,CAAEC,GAAG,IAAK;QACtCH,YAAY,GAAGG,GAAG;MACtB,CAAC,CAAC;MACF,KAAAnB,SAAA,GAAI,IAAI,CAACT,GAAG,cAAAS,SAAA,eAARA,SAAA,CAAW,SAAS,CAAC,EAAE;QACvBH,GAAG,CAACuB,gBAAgB,CAAC,aAAa,EAAE,MAAM;UACtCJ,YAAY,CAAC,SAAS,CAAC;QAC3B,CAAC,EAAE;UAAEK,IAAI,EAAE;QAAK,CAAC,CAAC;MACtB,CAAC,MACI;QACD;QACAL,YAAY,CAAC,KAAK,CAAC;MACvB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIM,EAAEA,CAACC,YAAY,EAAE;IACb,OAAOhG,UAAU,CAAC,IAAI,CAACgE,GAAG,EAAEgC,YAAY,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAAA,EAAG;IACR,OAAOhG,YAAY,CAAC,IAAI,CAAC+D,GAAG,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACR,aAAa;EAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIS,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC7B,GAAG,CAAC8B,GAAG,KAAK,KAAK;EACjC;EACA;AACJ;AACA;EACIC,aAAaA,CAACC,GAAG,EAAE;IACf,OAAOC,cAAc,CAAC,IAAI,CAACvC,GAAG,CAACwC,QAAQ,CAACC,IAAI,EAAEH,GAAG,CAAC;EACtD;EACA;AACJ;AACA;EACII,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;EACIA,UAAUA,CAAA,EAAG;IAAA,IAAAC,oBAAA,EAAAC,UAAA;IACT,QAAAD,oBAAA,GAAO,CAAAC,UAAA,OAAI,CAAC7C,GAAG,EAAC8C,UAAU,cAAAF,oBAAA,uBAAnBA,oBAAA,CAAAG,IAAA,CAAAF,UAAA,EAAsB,yBAAyB,CAAC,CAACG,OAAO;EACnE;EACAC,aAAaA,CAACC,UAAU,EAAE;IACtB,MAAMC,GAAG,GAAG,IAAI,CAACnD,GAAG,CAACoD,SAAS;IAC9B,OAAO,CAAC,EAAED,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEE,SAAS,IAAIF,GAAG,CAACE,SAAS,CAACC,OAAO,CAACJ,UAAU,CAAC,IAAI,CAAC,CAAC;EACvE;EACA;AACJ;AACA;EACIK,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAACvD,GAAG,CAACwC,QAAQ,CAACC,IAAI;EACjC;EACA;AACJ;AACA;EACIe,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACxD,GAAG,CAACyD,UAAU;EAC9B;EACA;AACJ;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAAC1D,GAAG,CAAC2D,WAAW;EAC/B;EACA;AAEJ;AAACC,SAAA,GA1NKvD,QAAQ;AAAAhD,eAAA,CAARgD,QAAQ,wBAAAwD,kBAAAtE,iBAAA;EAAA,YAAAA,iBAAA,IAwN6Gc,SAAQ,EAhPjDrG,EAAE,CAAA8J,QAAA,CAgPiE/H,QAAQ,GAhP3E/B,EAAE,CAAA8J,QAAA,CAgPsF9J,EAAE,CAACM,MAAM;AAAA;AAC/K;AAAA+C,eAAA,CAzNEgD,QAAQ,+BAxBoErG,EAAE,CAAAwF,kBAAA;EAAAC,KAAA,EAiP2CY,SAAQ;EAAAX,OAAA,EAARW,SAAQ,CAAAV,IAAA;EAAAC,UAAA,EAAc;AAAM;AAE3J;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAnPkF7F,EAAE,CAAA8F,iBAAA,CAmPQO,QAAQ,EAAc,CAAC;IACvG7B,IAAI,EAAEvE,UAAU;IAChB8F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpB,IAAI,EAAEuF,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DxF,IAAI,EAAEtE,MAAM;QACZ6F,IAAI,EAAE,CAAChE,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEyC,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,CAAC;EAAE,CAAC;AAAA;AAC7C,MAAMiI,cAAc,GAAGA,CAACgB,GAAG,EAAEjB,GAAG,KAAK;EACjCA,GAAG,GAAGA,GAAG,CAAC2B,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;EACrC,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAAC,QAAQ,GAAG7B,GAAG,GAAG,WAAW,CAAC;EACtD,MAAM8B,OAAO,GAAGF,KAAK,CAACG,IAAI,CAACd,GAAG,CAAC;EAC/B,OAAOa,OAAO,GAAGE,kBAAkB,CAACF,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI;AAC9E,CAAC;AACD,MAAM9C,UAAU,GAAGA,CAACoD,OAAO,EAAEC,EAAE,EAAEC,SAAS,EAAElE,IAAI,KAAK;EACjD,IAAIiE,EAAE,EAAE;IACJA,EAAE,CAAC3C,gBAAgB,CAAC4C,SAAS,EAAGzD,EAAE,IAAK;MACnC;AACZ;AACA;AACA;AACA;AACA;MACYT,IAAI,CAACC,GAAG,CAAC,MAAM;QACX;QACA,MAAMkE,KAAK,GAAG1D,EAAE,IAAI,IAAI,GAAGA,EAAE,CAAC2D,MAAM,GAAGZ,SAAS;QAChDQ,OAAO,CAACK,IAAI,CAACF,KAAK,CAAC;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ,CAAC;AAED,MAAMG,aAAa,CAAC;EAWhB1H,WAAWA,CAAC2H,QAAQ,EAAEtC,QAAQ,EAAEuC,UAAU,EAAEC,MAAM,EAAE;IAAA3H,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,oBANxC4H,iBAAiB;IAAA5H,eAAA,mBAClB6H,gBAAgB;IAAA7H,eAAA;IAAAA,eAAA,yBAEV,SAAS;IAAAA,eAAA;IAAAA,eAAA,oBAEd,CAAC,CAAC;IAEV,IAAI,CAACmF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACuC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB;IACA,IAAIA,MAAM,EAAE;MACRA,MAAM,CAACG,MAAM,CAACpE,SAAS,CAAEC,EAAE,IAAK;QAC5B,IAAIA,EAAE,YAAYvF,eAAe,EAAE;UAC/B;UACA,MAAM2J,EAAE,GAAGpE,EAAE,CAACqE,aAAa,GAAGrE,EAAE,CAACqE,aAAa,CAACC,YAAY,GAAGtE,EAAE,CAACoE,EAAE;UACnE,IAAI,CAACG,cAAc,GAAG,IAAI,CAACC,cAAc,GAAGJ,EAAE,GAAG,IAAI,CAACK,SAAS,GAAG,MAAM,GAAG,SAAS;UACpF,IAAI,CAACA,SAAS,GAAG,IAAI,CAACF,cAAc,KAAK,SAAS,GAAGvE,EAAE,CAACoE,EAAE,GAAGA,EAAE;QACnE;MACJ,CAAC,CAAC;IACN;IACA;IACAN,QAAQ,CAACnE,UAAU,CAACC,qBAAqB,CAAC,CAAC,EAAGM,kBAAkB,IAAK;MACjE,IAAI,CAACwE,GAAG,CAAC,CAAC;MACVxE,kBAAkB,CAAC,CAAC;IACxB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIyE,eAAeA,CAACpC,GAAG,EAAEqC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/B,IAAI,CAACC,YAAY,CAAC,SAAS,EAAED,OAAO,CAAC7G,QAAQ,EAAE6G,OAAO,CAACE,kBAAkB,EAAEF,OAAO,CAACxH,SAAS,CAAC;IAC7F,OAAO,IAAI,CAAC2H,QAAQ,CAACxC,GAAG,EAAEqC,OAAO,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACII,YAAYA,CAACzC,GAAG,EAAEqC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5B,IAAI,CAACC,YAAY,CAAC,MAAM,EAAED,OAAO,CAAC7G,QAAQ,EAAE6G,OAAO,CAACE,kBAAkB,EAAEF,OAAO,CAACxH,SAAS,CAAC;IAC1F,OAAO,IAAI,CAAC2H,QAAQ,CAACxC,GAAG,EAAEqC,OAAO,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIK,YAAYA,CAAC1C,GAAG,EAAEqC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC5B,IAAI,CAACC,YAAY,CAAC,MAAM,EAAED,OAAO,CAAC7G,QAAQ,EAAE6G,OAAO,CAACE,kBAAkB,EAAEF,OAAO,CAACxH,SAAS,CAAC;IAC1F,OAAO,IAAI,CAAC2H,QAAQ,CAACxC,GAAG,EAAEqC,OAAO,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIM,IAAIA,CAACN,OAAO,GAAG;IAAE7G,QAAQ,EAAE,IAAI;IAAE+G,kBAAkB,EAAE;EAAO,CAAC,EAAE;IAC3D,IAAI,CAACD,YAAY,CAAC,MAAM,EAAED,OAAO,CAAC7G,QAAQ,EAAE6G,OAAO,CAACE,kBAAkB,EAAEF,OAAO,CAACxH,SAAS,CAAC;IAC1F,OAAO,IAAI,CAACoE,QAAQ,CAAC0D,IAAI,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUR,GAAGA,CAAA,EAAG;IAAA,IAAAS,KAAA;IAAA,OAAAC,iBAAA;MACR,IAAIC,MAAM,GAAGF,KAAI,CAACG,SAAS;MAC3B,OAAOD,MAAM,EAAE;QACX,UAAUA,MAAM,CAACX,GAAG,CAAC,CAAC,EAAE;UACpB,OAAO,IAAI;QACf,CAAC,MACI;UACDW,MAAM,GAAGA,MAAM,CAACE,YAAY;QAChC;MACJ;MACA,OAAO,KAAK;IAAC;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIV,YAAYA,CAACW,SAAS,EAAEzH,QAAQ,EAAE+G,kBAAkB,EAAEW,gBAAgB,EAAE;IACpE,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACzH,QAAQ,GAAG2H,YAAY,CAACF,SAAS,EAAEzH,QAAQ,EAAE+G,kBAAkB,CAAC;IACrE,IAAI,CAACW,gBAAgB,GAAGA,gBAAgB;EAC5C;EACA;AACJ;AACA;EACIE,YAAYA,CAACN,MAAM,EAAE;IACjB,IAAI,CAACC,SAAS,GAAGD,MAAM;EAC3B;EACA;AACJ;AACA;EACIO,iBAAiBA,CAAA,EAAG;IAChB,IAAIJ,SAAS,GAAG,MAAM;IACtB,IAAIpI,SAAS;IACb,MAAMqI,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAI,IAAI,CAACD,SAAS,KAAK,MAAM,EAAE;MAC3BA,SAAS,GAAG,IAAI,CAACjB,cAAc;MAC/BnH,SAAS,GAAG,IAAI,CAACoH,cAAc;IACnC,CAAC,MACI;MACDpH,SAAS,GAAG,IAAI,CAACW,QAAQ;MACzByH,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B;IACA,IAAI,CAACA,SAAS,GAAGvB,iBAAiB;IAClC,IAAI,CAAClG,QAAQ,GAAGmG,gBAAgB;IAChC,IAAI,CAACuB,gBAAgB,GAAG1C,SAAS;IACjC,OAAO;MACHyC,SAAS;MACTpI,SAAS;MACTqI;IACJ,CAAC;EACL;EACAV,QAAQA,CAACxC,GAAG,EAAEqC,OAAO,EAAE;IACnB,IAAIiB,KAAK,CAACC,OAAO,CAACvD,GAAG,CAAC,EAAE;MACpB;MACA,OAAO,IAAI,CAACyB,MAAM,CAACe,QAAQ,CAACxC,GAAG,EAAEqC,OAAO,CAAC;IAC7C,CAAC,MACI;MACD;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMmB,OAAO,GAAG,IAAI,CAAChC,UAAU,CAACiC,KAAK,CAACzD,GAAG,CAAC0D,QAAQ,CAAC,CAAC,CAAC;MACrD,IAAIrB,OAAO,CAACsB,WAAW,KAAKnD,SAAS,EAAE;QACnCgD,OAAO,CAACG,WAAW,GAAG;UAAE,GAAGtB,OAAO,CAACsB;QAAY,CAAC;MACpD;MACA,IAAItB,OAAO,CAACuB,QAAQ,KAAKpD,SAAS,EAAE;QAChCgD,OAAO,CAACI,QAAQ,GAAGvB,OAAO,CAACuB,QAAQ;MACvC;MACA;AACZ;AACA;AACA;AACA;MACY;MACA,OAAO,IAAI,CAACnC,MAAM,CAACoC,aAAa,CAACL,OAAO,EAAEnB,OAAO,CAAC;IACtD;EACJ;EACA;AAEJ;AAACyB,cAAA,GAlMKxC,aAAa;AAAAxH,eAAA,CAAbwH,aAAa,wBAAAyC,uBAAA/H,iBAAA;EAAA,YAAAA,iBAAA,IAgMwGsF,cAAa,EApdtD7K,EAAE,CAAA8J,QAAA,CAodsEzD,QAAQ,GApdhFrG,EAAE,CAAA8J,QAAA,CAod2FhI,EAAE,CAACyL,QAAQ,GApdxGvN,EAAE,CAAA8J,QAAA,CAodmHtI,EAAE,CAACgM,aAAa,GApdrIxN,EAAE,CAAA8J,QAAA,CAodgJtI,EAAE,CAACK,MAAM;AAAA;AACzO;AAAAwB,eAAA,CAjMEwH,aAAa,+BApR+D7K,EAAE,CAAAwF,kBAAA;EAAAC,KAAA,EAqd2CoF,cAAa;EAAAnF,OAAA,EAAbmF,cAAa,CAAAlF,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEhK;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvdkF7F,EAAE,CAAA8F,iBAAA,CAudQ+E,aAAa,EAAc,CAAC;IAC5GrG,IAAI,EAAEvE,UAAU;IAChB8F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpB,IAAI,EAAE6B;IAAS,CAAC,EAAE;MAAE7B,IAAI,EAAE1C,EAAE,CAACyL;IAAS,CAAC,EAAE;MAAE/I,IAAI,EAAEhD,EAAE,CAACgM;IAAc,CAAC,EAAE;MAAEhJ,IAAI,EAAEhD,EAAE,CAACK,MAAM;MAAEmI,UAAU,EAAE,CAAC;QACrIxF,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB,MAAMuM,YAAY,GAAGA,CAACF,SAAS,EAAEzH,QAAQ,EAAE+G,kBAAkB,KAAK;EAC9D,IAAI/G,QAAQ,KAAK,KAAK,EAAE;IACpB,OAAOgF,SAAS;EACpB;EACA,IAAI+B,kBAAkB,KAAK/B,SAAS,EAAE;IAClC,OAAO+B,kBAAkB;EAC7B;EACA,IAAIU,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,MAAM,EAAE;IACjD,OAAOA,SAAS;EACpB,CAAC,MACI,IAAIA,SAAS,KAAK,MAAM,IAAIzH,QAAQ,KAAK,IAAI,EAAE;IAChD,OAAO,SAAS;EACpB;EACA,OAAOgF,SAAS;AACpB,CAAC;AACD,MAAMkB,iBAAiB,GAAG,MAAM;AAChC,MAAMC,gBAAgB,GAAGnB,SAAS;AAElC,MAAM0D,MAAM,CAAC;EACT1J,GAAGA,CAACuE,GAAG,EAAEoF,QAAQ,EAAE;IACf,MAAMC,CAAC,GAAGC,SAAS,CAAC,CAAC;IACrB,IAAID,CAAC,EAAE;MACH,OAAOA,CAAC,CAAC5J,GAAG,CAACuE,GAAG,EAAEoF,QAAQ,CAAC;IAC/B;IACA,OAAO,IAAI;EACf;EACAG,UAAUA,CAACvF,GAAG,EAAEoF,QAAQ,EAAE;IACtB,MAAMC,CAAC,GAAGC,SAAS,CAAC,CAAC;IACrB,IAAID,CAAC,EAAE;MACH,OAAOA,CAAC,CAACE,UAAU,CAACvF,GAAG,EAAEoF,QAAQ,CAAC;IACtC;IACA,OAAO,KAAK;EAChB;EACAI,SAASA,CAACxF,GAAG,EAAEoF,QAAQ,EAAE;IACrB,MAAMC,CAAC,GAAGC,SAAS,CAAC,CAAC;IACrB,IAAID,CAAC,EAAE;MACH,OAAOA,CAAC,CAACG,SAAS,CAACxF,GAAG,EAAEoF,QAAQ,CAAC;IACrC;IACA,OAAO,CAAC;EACZ;EACA;AAEJ;AAACK,OAAA,GAxBKN,MAAM;AAAApK,eAAA,CAANoK,MAAM,wBAAAO,gBAAAzI,iBAAA;EAAA,YAAAA,iBAAA,IAsB+GkI,OAAM;AAAA;AAC7H;AAAApK,eAAA,CAvBEoK,MAAM,+BAjfsEzN,EAAE,CAAAwF,kBAAA;EAAAC,KAAA,EAwgB2CgI,OAAM;EAAA/H,OAAA,EAAN+H,OAAM,CAAA9H,IAAA;EAAAC,UAAA,EAAc;AAAM;AAEzJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1gBkF7F,EAAE,CAAA8F,iBAAA,CA0gBQ2H,MAAM,EAAc,CAAC;IACrGjJ,IAAI,EAAEvE,UAAU;IAChB8F,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMqI,WAAW,GAAG,IAAI7N,cAAc,CAAC,YAAY,CAAC;AACpD,MAAMwN,SAAS,GAAGA,CAAA,KAAM;EACpB,IAAI,OAAO3H,MAAM,KAAK,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGD,MAAM,CAACC,KAAK;IAC1B,IAAIA,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEgI,MAAM,EAAE;MACf,OAAOhI,KAAK,CAACgI,MAAM;IACvB;EACJ;EACA,OAAO,IAAI;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EAEZhL,WAAWA,CAACiL,IAAI,GAAG,CAAC,CAAC,EAAE;IAAA/K,eAAA;IACnB,IAAI,CAAC+K,IAAI,GAAGA,IAAI;IAChBC,OAAO,CAACC,IAAI,CAAC,gLAAgL,CAAC;EAClM;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIvK,GAAGA,CAACwK,KAAK,EAAE;IACP,OAAO,IAAI,CAACH,IAAI,CAACG,KAAK,CAAC;EAC3B;AACJ;;AAEA;AACA,MAAMC,eAAe,CAAC;EAAArL,YAAA;IAAAE,eAAA,eACXhD,MAAM,CAACC,MAAM,CAAC;IAAA+C,eAAA,yBACJhD,MAAM,CAACE,cAAc,CAAC;IAAA8C,eAAA,iBAC9BhD,MAAM,CAAC4N,WAAW,CAAC;EAAA;EAC5BQ,MAAMA,CAACC,mBAAmB,EAAEC,QAAQ,EAAEC,mBAAmB,EAAE;IAAA,IAAAC,qBAAA;IACvD,OAAO,IAAIC,wBAAwB,CAACJ,mBAAmB,EAAEC,QAAQ,EAAE,IAAI,CAACI,cAAc,EAAE,IAAI,CAACxI,IAAI,EAAEqI,mBAAmB,GAAAC,qBAAA,GAAE,IAAI,CAACX,MAAM,CAACc,cAAc,cAAAH,qBAAA,cAAAA,qBAAA,GAAI,KAAK,CAAC;EAChK;EACA;AAEJ;AAACI,gBAAA,GATKT,eAAe;AAAAnL,eAAA,CAAfmL,eAAe,wBAAAU,yBAAA3J,iBAAA;EAAA,YAAAA,iBAAA,IAOsGiJ,gBAAe;AAAA;AACtI;AAAAnL,eAAA,CAREmL,eAAe,+BA3kB6DxO,EAAE,CAAAwF,kBAAA;EAAAC,KAAA,EAmlB2C+I,gBAAe;EAAA9I,OAAA,EAAf8I,gBAAe,CAAA7I;AAAA;AAE9I;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KArlBkF7F,EAAE,CAAA8F,iBAAA,CAqlBQ0I,eAAe,EAAc,CAAC;IAC9GhK,IAAI,EAAEvE;EACV,CAAC,CAAC;AAAA;AACV,MAAM6O,wBAAwB,CAAC;EAS3B3L,WAAWA,CAACuL,mBAAmB,EAAEC,QAAQ,EAAEI,cAAc,EAAExI,IAAI,EAAEqI,mBAAmB,EAAEO,oBAAoB,EAAE;IAAA9L,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,mBAFjG,IAAI+L,OAAO,CAAC,CAAC;IAAA/L,eAAA,sBACV,IAAI+L,OAAO,CAAC,CAAC;IAEvB,IAAI,CAACV,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACI,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACxI,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACqI,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACO,oBAAoB,GAAGA,oBAAoB;EACpD;EACAE,eAAeA,CAACC,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAE;IACtD,OAAO,IAAI,CAAClJ,IAAI,CAACC,GAAG,CAAC,MAAM;MACvB,OAAO,IAAImB,OAAO,CAAE+H,OAAO,IAAK;QAC5B,MAAMC,cAAc,GAAG;UACnB,GAAGH;QACP,CAAC;QACD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAI,IAAI,CAACZ,mBAAmB,KAAK7E,SAAS,EAAE;UACxC4F,cAAc,CAAC,IAAI,CAACf,mBAAmB,CAAC,GAAGU,SAAS;QACxD;QACA,MAAM9E,EAAE,GAAGoF,UAAU,CAAC,IAAI,CAACrJ,IAAI,EAAE,IAAI,CAACmI,mBAAmB,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACI,cAAc,EAAE,IAAI,CAACc,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAER,SAAS,EAAEC,SAAS,EAAEI,cAAc,EAAEF,UAAU,EAAE,IAAI,CAACb,mBAAmB,EAAE,IAAI,CAACO,oBAAoB,CAAC;QACtOO,OAAO,CAAClF,EAAE,CAAC;MACf,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAuF,iBAAiBA,CAACC,UAAU,EAAET,SAAS,EAAE;IACrC,OAAO,IAAI,CAAChJ,IAAI,CAACC,GAAG,CAAC,MAAM;MACvB,OAAO,IAAImB,OAAO,CAAE+H,OAAO,IAAK;QAC5B,MAAMO,YAAY,GAAG,IAAI,CAACJ,QAAQ,CAAC9L,GAAG,CAACwL,SAAS,CAAC;QACjD,IAAIU,YAAY,EAAE;UACdA,YAAY,CAACC,OAAO,CAAC,CAAC;UACtB,IAAI,CAACL,QAAQ,CAACM,MAAM,CAACZ,SAAS,CAAC;UAC/B,MAAMa,YAAY,GAAG,IAAI,CAACN,WAAW,CAAC/L,GAAG,CAACwL,SAAS,CAAC;UACpD,IAAIa,YAAY,EAAE;YACdA,YAAY,CAAC,CAAC;YACd,IAAI,CAACN,WAAW,CAACK,MAAM,CAACZ,SAAS,CAAC;UACtC;QACJ;QACAG,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IACN,CAAC,CAAC;EACN;AACJ;AACA,MAAME,UAAU,GAAGA,CAACrJ,IAAI,EAAEmI,mBAAmB,EAAEC,QAAQ,EAAEI,cAAc,EAAEc,QAAQ,EAAEC,WAAW,EAAER,SAAS,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEb,mBAAmB,EAAEO,oBAAoB,KAAK;EACpL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMkB,aAAa,GAAG7P,QAAQ,CAACiO,MAAM,CAAC;IAClC6B,SAAS,EAAEC,YAAY,CAACf,MAAM,CAAC;IAC/BgB,MAAM,EAAE7B;EACZ,CAAC,CAAC;EACF,MAAMsB,YAAY,GAAGxP,eAAe,CAAC8O,SAAS,EAAE;IAC5Cb,mBAAmB;IACnB+B,eAAe,EAAEJ;EACrB,CAAC,CAAC;EACF,MAAMK,QAAQ,GAAGT,YAAY,CAACS,QAAQ;EACtC,MAAMC,WAAW,GAAGV,YAAY,CAACzH,QAAQ,CAACoI,aAAa;EACvD,IAAIpB,MAAM,EAAE;IACR;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIZ,mBAAmB,IAAI8B,QAAQ,CAAC9B,mBAAmB,CAAC,KAAK7E,SAAS,EAAE;MACpEsE,OAAO,CAACwC,KAAK,CAAC,kBAAkBjC,mBAAmB,sCAAsCU,SAAS,CAACwB,OAAO,CAACC,WAAW,CAAC,CAAC,2BAA2BnC,mBAAmB,mBAAmBW,SAAS,CAACpL,IAAI,GAAG,CAAC;IAC/M;IACA;AACR;AACA;AACA;AACA;IACQ,IAAIgL,oBAAoB,KAAK,IAAI,IAAIc,YAAY,CAACe,QAAQ,KAAKjH,SAAS,EAAE;MACtE,MAAM;QAAEkH,KAAK;QAAEC,OAAO;QAAE,GAAGC;MAAY,CAAC,GAAG3B,MAAM;MACjD;AACZ;AACA;AACA;MACY,KAAK,MAAMlH,GAAG,IAAI6I,WAAW,EAAE;QAC3BlB,YAAY,CAACe,QAAQ,CAAC1I,GAAG,EAAE6I,WAAW,CAAC7I,GAAG,CAAC,CAAC;MAChD;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI2I,KAAK,KAAKlH,SAAS,EAAE;QACrBqH,MAAM,CAACC,MAAM,CAACX,QAAQ,EAAE;UAAEO;QAAM,CAAC,CAAC;MACtC;MACA,IAAIC,OAAO,KAAKnH,SAAS,EAAE;QACvBqH,MAAM,CAACC,MAAM,CAACX,QAAQ,EAAE;UAAEQ;QAAQ,CAAC,CAAC;MACxC;IACJ,CAAC,MACI;MACDE,MAAM,CAACC,MAAM,CAACX,QAAQ,EAAElB,MAAM,CAAC;IACnC;EACJ;EACA,IAAIC,UAAU,EAAE;IACZ,KAAK,MAAM6B,QAAQ,IAAI7B,UAAU,EAAE;MAC/BkB,WAAW,CAACY,SAAS,CAACC,GAAG,CAACF,QAAQ,CAAC;IACvC;EACJ;EACA,MAAMlB,YAAY,GAAGqB,mBAAmB,CAAClL,IAAI,EAAEmK,QAAQ,EAAEC,WAAW,CAAC;EACrErB,SAAS,CAACoC,WAAW,CAACf,WAAW,CAAC;EAClC5B,cAAc,CAACa,UAAU,CAACK,YAAY,CAAC0B,QAAQ,CAAC;EAChD9B,QAAQ,CAAC+B,GAAG,CAACjB,WAAW,EAAEV,YAAY,CAAC;EACvCH,WAAW,CAAC8B,GAAG,CAACjB,WAAW,EAAEP,YAAY,CAAC;EAC1C,OAAOO,WAAW;AACtB,CAAC;AACD,MAAMkB,UAAU,GAAG,CACf3P,oBAAoB,EACpBC,mBAAmB,EACnBC,oBAAoB,EACpBC,mBAAmB,EACnBC,qBAAqB,CACxB;AACD,MAAMmP,mBAAmB,GAAGA,CAAClL,IAAI,EAAEmK,QAAQ,EAAEoB,OAAO,KAAK;EACrD,OAAOvL,IAAI,CAACC,GAAG,CAAC,MAAM;IAClB,MAAMuL,WAAW,GAAGF,UAAU,CAAC/O,MAAM,CAAE2H,SAAS,IAAK,OAAOiG,QAAQ,CAACjG,SAAS,CAAC,KAAK,UAAU,CAAC,CAACuH,GAAG,CAAEvH,SAAS,IAAK;MAC/G,MAAMwH,OAAO,GAAIjL,EAAE,IAAK0J,QAAQ,CAACjG,SAAS,CAAC,CAACzD,EAAE,CAAC2D,MAAM,CAAC;MACtDmH,OAAO,CAACjK,gBAAgB,CAAC4C,SAAS,EAAEwH,OAAO,CAAC;MAC5C,OAAO,MAAMH,OAAO,CAACI,mBAAmB,CAACzH,SAAS,EAAEwH,OAAO,CAAC;IAChE,CAAC,CAAC;IACF,OAAO,MAAMF,WAAW,CAACI,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC;EAClD,CAAC,CAAC;AACN,CAAC;AACD,MAAMC,cAAc,GAAG,IAAIjS,cAAc,CAAC,gBAAgB,CAAC;AAC3D,MAAMmQ,YAAY,GAAIf,MAAM,IAAK;EAC7B,OAAO,CACH;IACI8C,OAAO,EAAED,cAAc;IACvBE,QAAQ,EAAE/C;EACd,CAAC,EACD;IACI8C,OAAO,EAAEnE,SAAS;IAClBqE,UAAU,EAAEC,0BAA0B;IACtCC,IAAI,EAAE,CAACL,cAAc;EACzB,CAAC,CACJ;AACL,CAAC;AACD,MAAMI,0BAA0B,GAAIjD,MAAM,IAAK;EAC3C,OAAO,IAAIrB,SAAS,CAACqB,MAAM,CAAC;AAChC,CAAC;;AAED;AACA;AACA;AACA,MAAMmD,WAAW,GAAGA,CAACC,GAAG,EAAEC,MAAM,KAAK;EACjC,MAAMC,SAAS,GAAGF,GAAG,CAACG,SAAS;EAC/BF,MAAM,CAACV,OAAO,CAAEa,IAAI,IAAK;IACrB5B,MAAM,CAAC6B,cAAc,CAACH,SAAS,EAAEE,IAAI,EAAE;MACnCjP,GAAGA,CAAA,EAAG;QACF,OAAO,IAAI,CAACyG,EAAE,CAACwI,IAAI,CAAC;MACxB,CAAC;MACDpB,GAAGA,CAACsB,GAAG,EAAE;QACL,IAAI,CAACC,CAAC,CAACC,iBAAiB,CAAC,MAAO,IAAI,CAAC5I,EAAE,CAACwI,IAAI,CAAC,GAAGE,GAAI,CAAC;MACzD;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AACD,MAAMG,YAAY,GAAGA,CAACT,GAAG,EAAEU,OAAO,KAAK;EACnC,MAAMR,SAAS,GAAGF,GAAG,CAACG,SAAS;EAC/BO,OAAO,CAACnB,OAAO,CAAEoB,UAAU,IAAK;IAC5BT,SAAS,CAACS,UAAU,CAAC,GAAG,YAAY;MAChC,MAAMxN,IAAI,GAAGyN,SAAS;MACtB,OAAO,IAAI,CAACL,CAAC,CAACC,iBAAiB,CAAC,MAAM,IAAI,CAAC5I,EAAE,CAAC+I,UAAU,CAAC,CAACE,KAAK,CAAC,IAAI,CAACjJ,EAAE,EAAEzE,IAAI,CAAC,CAAC;IACnF,CAAC;EACL,CAAC,CAAC;AACN,CAAC;AACD,MAAM2N,YAAY,GAAGA,CAAChD,QAAQ,EAAElG,EAAE,EAAEW,MAAM,KAAK;EAC3CA,MAAM,CAACgH,OAAO,CAAE1H,SAAS,IAAMiG,QAAQ,CAACjG,SAAS,CAAC,GAAGhI,SAAS,CAAC+H,EAAE,EAAEC,SAAS,CAAE,CAAC;AACnF,CAAC;AACD;AACA,SAASkJ,QAAQA,CAACC,IAAI,EAAE;EACpB,MAAMC,SAAS,GAAG,SAAAA,CAAUC,GAAG,EAAE;IAC7B,MAAM;MAAEC,qBAAqB;MAAElB,MAAM;MAAES;IAAQ,CAAC,GAAGM,IAAI;IACvD,IAAIG,qBAAqB,KAAKhK,SAAS,EAAE;MACrCgK,qBAAqB,CAAC,CAAC;IAC3B;IACA,IAAIlB,MAAM,EAAE;MACRF,WAAW,CAACmB,GAAG,EAAEjB,MAAM,CAAC;IAC5B;IACA,IAAIS,OAAO,EAAE;MACTD,YAAY,CAACS,GAAG,EAAER,OAAO,CAAC;IAC9B;IACA,OAAOQ,GAAG;EACd,CAAC;EACD,OAAOD,SAAS;AACpB;AAEA,MAAMG,cAAc,GAAG,CACnB,WAAW,EACX,UAAU,EACV,OAAO,EACP,qBAAqB,EACrB,iBAAiB,EACjB,UAAU,EACV,iBAAiB,EACjB,gBAAgB,EAChB,OAAO,EACP,WAAW,EACX,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,MAAM,EACN,cAAc,EACd,aAAa,EACb,SAAS,EACT,eAAe,EACf,WAAW,EACX,MAAM,EACN,MAAM,CACT;AACD,MAAMC,eAAe,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,eAAe,CAAC;AAC/E,IAAIC,UAAU,IAAAC,WAAA,GAAG,MAAMD,UAAU,CAAC;EAM9B/Q,WAAWA,CAACwK,CAAC,EAAEyG,CAAC,EAAEjB,CAAC,EAAE;IAAA9P,eAAA;IAJrB;IAAAA,eAAA;IAAAA,eAAA,oBAEY,KAAK;IAAAA,eAAA;IAGb,IAAI,CAAC8P,CAAC,GAAGA,CAAC;IACV,IAAI,CAAC3I,EAAE,GAAG4J,CAAC,CAACxD,aAAa;IACzB,IAAI,CAACpG,EAAE,CAAC3C,gBAAgB,CAAC,UAAU,EAAE,MAAM;MACvC,IAAI,CAACwM,SAAS,GAAG,IAAI;MACrB1G,CAAC,CAAC2G,aAAa,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,IAAI,CAAC9J,EAAE,CAAC3C,gBAAgB,CAAC,YAAY,EAAE,MAAM;MACzC,IAAI,CAACwM,SAAS,GAAG,KAAK;MACtB1G,CAAC,CAAC2G,aAAa,CAAC,CAAC;IACrB,CAAC,CAAC;IACFZ,YAAY,CAAC,IAAI,EAAE,IAAI,CAAClJ,EAAE,EAAE,CACxB,sBAAsB,EACtB,uBAAuB,EACvB,uBAAuB,EACvB,sBAAsB,EACtB,YAAY,EACZ,aAAa,EACb,aAAa,EACb,YAAY,CACf,CAAC;EACN;EACA;AAEJ,CAAC,EAAAnH,eAAA,CAAA8Q,WAAA,wBAAAI,oBAAAhP,iBAAA;EAAA,YAAAA,iBAAA,IAF0H2O,WAAU,EAp2BnDlU,EAAE,CAAAwU,iBAAA,CAo2BmExU,EAAE,CAACyU,iBAAiB,GAp2BzFzU,EAAE,CAAAwU,iBAAA,CAo2BoGxU,EAAE,CAACsB,UAAU,GAp2BnHtB,EAAE,CAAAwU,iBAAA,CAo2B8HxU,EAAE,CAACM,MAAM;AAAA,IAAA+C,eAAA,CAAA8Q,WAAA,8BAp2BzInU,EAAE,CAAA0U,iBAAA;EAAAlQ,IAAA,EAq2B2B0P,WAAU;EAAAS,SAAA;EAAAC,cAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;IAAA,IAAAF,EAAA;MAr2BvC9U,EAAE,CAAAiV,cAAA,CAAAD,QAAA,EAq2BgqBtU,WAAW;IAAA;IAAA,IAAAoU,EAAA;MAAA,IAAAI,EAAA;MAr2B7qBlV,EAAE,CAAAmV,cAAA,CAAAD,EAAA,GAAFlV,EAAE,CAAAoV,WAAA,QAAAL,GAAA,CAAAM,QAAA,GAAAH,EAAA,CAAAI,KAAA;IAAA;EAAA;EAAAzC,MAAA;IAAA0C,SAAA;IAAAxQ,QAAA;IAAAyQ,KAAA;IAAAC,mBAAA;IAAAC,eAAA;IAAApE,QAAA;IAAAqE,eAAA;IAAAC,cAAA;IAAAC,KAAA;IAAAC,SAAA;IAAAjS,MAAA;IAAAkS,aAAA;IAAAC,cAAA;IAAAC,IAAA;IAAAC,YAAA;IAAAC,WAAA;IAAAC,OAAA;IAAAC,aAAA;IAAAC,SAAA;IAAAC,IAAA;IAAAC,IAAA;EAAA;EAAAC,UAAA;AAAA,KAAAtC,WAAA,CAs2BnF;AACDD,UAAU,GAAGrR,UAAU,CAAC,CACpB8Q,QAAQ,CAAC;EACLd,MAAM,EAAEmB,cAAc;EACtBV,OAAO,EAAEW;AACb,CAAC;AACD;AACJ;AACA;AACA;AACA;AACA,GALI,CAMH,EAAEC,UAAU,CAAC;AACd;EAAA,QAAArO,SAAA,oBAAAA,SAAA,KAn3BkF7F,EAAE,CAAA8F,iBAAA,CAm3BQoO,UAAU,EAAc,CAAC;IACzG1P,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC2Q,QAAQ,EAAE,aAAa;MACvB;MACA7D,MAAM,EAAEmB;IACZ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAExP,IAAI,EAAExE,EAAE,CAACyU;IAAkB,CAAC,EAAE;MAAEjQ,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE+U,QAAQ,EAAE,CAAC;MACjJ7Q,IAAI,EAAE5D,YAAY;MAClBmF,IAAI,EAAE,CAACrF,WAAW,EAAE;QAAEiW,MAAM,EAAE;MAAM,CAAC;IACzC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,YAAY,GAAG,CACjB,UAAU,EACV,qBAAqB,EACrB,oBAAoB,EACpB,iBAAiB,EACjB,aAAa,EACb,YAAY,EACZ,UAAU,EACV,gBAAgB,EAChB,OAAO,EACP,WAAW,EACX,QAAQ,EACR,gBAAgB,EAChB,mBAAmB,EACnB,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,MAAM,EACN,mBAAmB,EACnB,cAAc,EACd,aAAa,EACb,SAAS,CACZ;AACD,MAAMC,aAAa,GAAG,CAClB,SAAS,EACT,SAAS,EACT,cAAc,EACd,eAAe,EACf,sBAAsB,EACtB,sBAAsB,CACzB;AACD,IAAIC,QAAQ,IAAAC,SAAA,GAAG,MAAMD,QAAQ,CAAC;EAM1B3T,WAAWA,CAACwK,CAAC,EAAEyG,CAAC,EAAEjB,CAAC,EAAE;IAAA9P,eAAA;IAJrB;IAAAA,eAAA;IAAAA,eAAA,oBAEY,KAAK;IAAAA,eAAA;IAGb,IAAI,CAAC8P,CAAC,GAAGA,CAAC;IACV,IAAI,CAAC3I,EAAE,GAAG4J,CAAC,CAACxD,aAAa;IACzB,IAAI,CAACpG,EAAE,CAAC3C,gBAAgB,CAAC,UAAU,EAAE,MAAM;MACvC,IAAI,CAACwM,SAAS,GAAG,IAAI;MACrB1G,CAAC,CAAC2G,aAAa,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,IAAI,CAAC9J,EAAE,CAAC3C,gBAAgB,CAAC,YAAY,EAAE,MAAM;MACzC,IAAI,CAACwM,SAAS,GAAG,KAAK;MACtB1G,CAAC,CAAC2G,aAAa,CAAC,CAAC;IACrB,CAAC,CAAC;IACFZ,YAAY,CAAC,IAAI,EAAE,IAAI,CAAClJ,EAAE,EAAE,CACxB,oBAAoB,EACpB,qBAAqB,EACrB,qBAAqB,EACrB,oBAAoB,EACpB,wBAAwB,EACxB,YAAY,EACZ,aAAa,EACb,aAAa,EACb,YAAY,CACf,CAAC;EACN;EACA;AAEJ,CAAC,EAAAnH,eAAA,CAAA0T,SAAA,wBAAAC,kBAAAzR,iBAAA;EAAA,YAAAA,iBAAA,IAF0HuR,SAAQ,EA37BjD9W,EAAE,CAAAwU,iBAAA,CA27BiExU,EAAE,CAACyU,iBAAiB,GA37BvFzU,EAAE,CAAAwU,iBAAA,CA27BkGxU,EAAE,CAACsB,UAAU,GA37BjHtB,EAAE,CAAAwU,iBAAA,CA27B4HxU,EAAE,CAACM,MAAM;AAAA,IAAA+C,eAAA,CAAA0T,SAAA,8BA37BvI/W,EAAE,CAAA0U,iBAAA;EAAAlQ,IAAA,EA47B2BsS,SAAQ;EAAAnC,SAAA;EAAAC,cAAA,WAAAqC,yBAAAnC,EAAA,EAAAC,GAAA,EAAAC,QAAA;IAAA,IAAAF,EAAA;MA57BrC9U,EAAE,CAAAiV,cAAA,CAAAD,QAAA,EA47BguBtU,WAAW;IAAA;IAAA,IAAAoU,EAAA;MAAA,IAAAI,EAAA;MA57B7uBlV,EAAE,CAAAmV,cAAA,CAAAD,EAAA,GAAFlV,EAAE,CAAAoV,WAAA,QAAAL,GAAA,CAAAM,QAAA,GAAAH,EAAA,CAAAI,KAAA;IAAA;EAAA;EAAAzC,MAAA;IAAA9N,QAAA;IAAA0Q,mBAAA;IAAAyB,kBAAA;IAAAxB,eAAA;IAAAyB,WAAA;IAAAC,UAAA;IAAA9F,QAAA;IAAAsE,cAAA;IAAAC,KAAA;IAAAC,SAAA;IAAAuB,MAAA;IAAAC,cAAA;IAAAC,iBAAA;IAAA1T,MAAA;IAAAkS,aAAA;IAAAC,cAAA;IAAAC,IAAA;IAAAuB,iBAAA;IAAAtB,YAAA;IAAAC,WAAA;IAAAC,OAAA;EAAA;EAAAK,UAAA;AAAA,KAAAM,SAAA,CA67BnF;AACDD,QAAQ,GAAGjU,UAAU,CAAC,CAClB8Q,QAAQ,CAAC;EACLd,MAAM,EAAE+D,YAAY;EACpBtD,OAAO,EAAEuD;AACb,CAAC;AACD;AACJ;AACA;AACA;AACA;AACA,GALI,CAMH,EAAEC,QAAQ,CAAC;AACZ;EAAA,QAAAjR,SAAA,oBAAAA,SAAA,KA18BkF7F,EAAE,CAAA8F,iBAAA,CA08BQgR,QAAQ,EAAc,CAAC;IACvGtS,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC2Q,QAAQ,EAAE,WAAW;MACrB;MACA7D,MAAM,EAAE+D;IACZ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpS,IAAI,EAAExE,EAAE,CAACyU;IAAkB,CAAC,EAAE;MAAEjQ,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE+U,QAAQ,EAAE,CAAC;MACjJ7Q,IAAI,EAAE5D,YAAY;MAClBmF,IAAI,EAAE,CAACrF,WAAW,EAAE;QAAEiW,MAAM,EAAE;MAAM,CAAC;IACzC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMc,UAAU,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAEnL,SAAS,KAAK;EAC3C,IAAIA,SAAS,KAAK,MAAM,EAAE;IACtB,OAAOoL,OAAO,CAACF,KAAK,EAAEC,IAAI,CAAC;EAC/B,CAAC,MACI,IAAInL,SAAS,KAAK,SAAS,EAAE;IAC9B,OAAOqL,UAAU,CAACH,KAAK,EAAEC,IAAI,CAAC;EAClC,CAAC,MACI;IACD,OAAOG,OAAO,CAACJ,KAAK,EAAEC,IAAI,CAAC;EAC/B;AACJ,CAAC;AACD,MAAMC,OAAO,GAAGA,CAACF,KAAK,EAAEC,IAAI,KAAK;EAC7BD,KAAK,GAAGA,KAAK,CAAC5U,MAAM,CAAEiV,CAAC,IAAKA,CAAC,CAACC,OAAO,KAAKL,IAAI,CAACK,OAAO,CAAC;EACvDN,KAAK,CAACO,IAAI,CAACN,IAAI,CAAC;EAChB,OAAOD,KAAK;AAChB,CAAC;AACD,MAAMG,UAAU,GAAGA,CAACH,KAAK,EAAEC,IAAI,KAAK;EAChC,MAAMO,KAAK,GAAGR,KAAK,CAACpO,OAAO,CAACqO,IAAI,CAAC;EACjC,IAAIO,KAAK,IAAI,CAAC,EAAE;IACZR,KAAK,GAAGA,KAAK,CAAC5U,MAAM,CAAEiV,CAAC,IAAKA,CAAC,CAACC,OAAO,KAAKL,IAAI,CAACK,OAAO,IAAID,CAAC,CAAC3M,EAAE,IAAIuM,IAAI,CAACvM,EAAE,CAAC;EAC9E,CAAC,MACI;IACDsM,KAAK,CAACO,IAAI,CAACN,IAAI,CAAC;EACpB;EACA,OAAOD,KAAK;AAChB,CAAC;AACD,MAAMI,OAAO,GAAGA,CAACJ,KAAK,EAAEC,IAAI,KAAK;EAC7B,MAAMO,KAAK,GAAGR,KAAK,CAACpO,OAAO,CAACqO,IAAI,CAAC;EACjC,IAAIO,KAAK,IAAI,CAAC,EAAE;IACZ,OAAOR,KAAK,CAAC5U,MAAM,CAAEiV,CAAC,IAAKA,CAAC,CAACC,OAAO,KAAKL,IAAI,CAACK,OAAO,IAAID,CAAC,CAAC3M,EAAE,IAAIuM,IAAI,CAACvM,EAAE,CAAC;EAC7E,CAAC,MACI;IACD,OAAOwM,OAAO,CAACF,KAAK,EAAEC,IAAI,CAAC;EAC/B;AACJ,CAAC;AACD,MAAMQ,MAAM,GAAGA,CAACnN,MAAM,EAAEoN,cAAc,KAAK;EACvC,MAAMrL,OAAO,GAAG/B,MAAM,CAACqN,aAAa,CAAC,CAAC,GAAG,CAAC,EAAE;IAAEC,UAAU,EAAEF;EAAe,CAAC,CAAC;EAC3E,OAAOpN,MAAM,CAACuN,YAAY,CAACxL,OAAO,CAAC;AACvC,CAAC;AACD,MAAMyL,WAAW,GAAGA,CAACC,YAAY,EAAEC,WAAW,KAAK;EAC/C,IAAI,CAACA,WAAW,EAAE;IACd,OAAO,IAAI;EACf;EACA,OAAOD,YAAY,CAACT,OAAO,KAAKU,WAAW,CAACV,OAAO;AACvD,CAAC;AACD,MAAMW,cAAc,GAAGA,CAACC,SAAS,EAAErP,GAAG,KAAK;EACvC,IAAI,CAACqP,SAAS,EAAE;IACZ,OAAO7O,SAAS;EACpB;EACA,MAAM8O,QAAQ,GAAGC,UAAU,CAACvP,GAAG,CAAC;EAChC,KAAK,IAAIwP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAIA,CAAC,IAAIH,SAAS,CAACI,MAAM,EAAE;MACvB,OAAOH,QAAQ,CAACE,CAAC,CAAC;IACtB;IACA,IAAIF,QAAQ,CAACE,CAAC,CAAC,KAAKH,SAAS,CAACG,CAAC,CAAC,EAAE;MAC9B,OAAOhP,SAAS;IACpB;EACJ;EACA,OAAOA,SAAS;AACpB,CAAC;AACD,MAAM+O,UAAU,GAAIG,IAAI,IAAK;EACzB,OAAOA,IAAI,CACNC,KAAK,CAAC,GAAG,CAAC,CACVlH,GAAG,CAAEmH,CAAC,IAAKA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CACpBtW,MAAM,CAAEqW,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC;AAChC,CAAC;AACD,MAAME,WAAW,GAAI1B,IAAI,IAAK;EAC1B,IAAIA,IAAI,EAAE;IACNA,IAAI,CAAC2B,GAAG,CAACpJ,OAAO,CAAC,CAAC;IAClByH,IAAI,CAAC4B,cAAc,CAAC,CAAC;EACzB;AACJ,CAAC;;AAED;AACA,MAAMC,eAAe,CAAC;EAYlBrW,WAAWA,CAACsW,UAAU,EAAEC,WAAW,EAAE1O,MAAM,EAAE2O,OAAO,EAAEpT,IAAI,EAAEiC,QAAQ,EAAE;IAAAnF,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,gBAN9D,EAAE;IAAAA,eAAA;IAAAA,eAAA,yBAEO,KAAK;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,iBAGb,CAAC;IAEN,IAAI,CAACqW,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC1O,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC2O,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACpT,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACiC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACiR,UAAU,GAAGA,UAAU,KAAK1P,SAAS,GAAG+O,UAAU,CAACW,UAAU,CAAC,GAAG1P,SAAS;EACnF;EACA6P,UAAUA,CAACN,GAAG,EAAElB,cAAc,EAAE;IAAA,IAAAyB,aAAA;IAC5B,MAAMtQ,GAAG,GAAG4O,MAAM,CAAC,IAAI,CAACnN,MAAM,EAAEoN,cAAc,CAAC;IAC/C,MAAMtG,OAAO,GAAGwH,GAAG,aAAHA,GAAG,gBAAAO,aAAA,GAAHP,GAAG,CAAE9Q,QAAQ,cAAAqR,aAAA,uBAAbA,aAAA,CAAejJ,aAAa;IAC5C,MAAM2I,cAAc,GAAG9H,mBAAmB,CAAC,IAAI,CAAClL,IAAI,EAAE+S,GAAG,CAAC5I,QAAQ,EAAEoB,OAAO,CAAC;IAC5E,OAAO;MACH1G,EAAE,EAAE,IAAI,CAAC0O,MAAM,EAAE;MACjB9B,OAAO,EAAEW,cAAc,CAAC,IAAI,CAACc,UAAU,EAAElQ,GAAG,CAAC;MAC7CgQ,cAAc;MACdzH,OAAO;MACPwH,GAAG;MACH/P;IACJ,CAAC;EACL;EACAwQ,eAAeA,CAAC3B,cAAc,EAAE;IAC5B,MAAM4B,eAAe,GAAG7B,MAAM,CAAC,IAAI,CAACnN,MAAM,EAAEoN,cAAc,CAAC;IAC3D,MAAMT,IAAI,GAAG,IAAI,CAACD,KAAK,CAACuC,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAAC3Q,GAAG,KAAKyQ,eAAe,CAAC;IAChE,IAAIrC,IAAI,EAAE;MACNA,IAAI,CAAC2B,GAAG,CAACa,iBAAiB,CAACC,QAAQ,CAAC,CAAC;IACzC;IACA,OAAOzC,IAAI;EACf;EACA0C,SAASA,CAAC5B,YAAY,EAAE;IAAA,IAAA6B,mBAAA,EAAAC,kBAAA;IACpB,MAAMC,aAAa,GAAG,IAAI,CAACb,OAAO,CAAC/M,iBAAiB,CAAC,CAAC;IACtD,IAAI;MAAEJ,SAAS;MAAEpI,SAAS;MAAEqI;IAAiB,CAAC,GAAG+N,aAAa;IAC9D,MAAM9B,WAAW,GAAG,IAAI,CAAC+B,UAAU;IACnC,MAAMC,SAAS,GAAGlC,WAAW,CAACC,YAAY,EAAEC,WAAW,CAAC;IACxD,IAAIgC,SAAS,EAAE;MACXlO,SAAS,GAAG,MAAM;MAClBpI,SAAS,GAAG2F,SAAS;IACzB;IACA,MAAM4Q,aAAa,GAAG,IAAI,CAACjD,KAAK,CAACkD,KAAK,CAAC,CAAC;IACxC,IAAIC,iBAAiB;IACrB,MAAM7P,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B;IACA,IAAIA,MAAM,CAAC8P,oBAAoB,EAAE;MAC7BD,iBAAiB,GAAG7P,MAAM,CAAC8P,oBAAoB,CAAC,CAAC;MACjD;IACJ,CAAC,MACI,KAAAR,mBAAA,GAAItP,MAAM,CAAC+P,WAAW,cAAAT,mBAAA,eAAlBA,mBAAA,CAAoB5P,KAAK,EAAE;MAChCmQ,iBAAiB,GAAG7P,MAAM,CAAC+P,WAAW,CAACrQ,KAAK;IAChD;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,KAAA6P,kBAAA,GAAIM,iBAAiB,cAAAN,kBAAA,gBAAAA,kBAAA,GAAjBA,kBAAA,CAAmBS,MAAM,cAAAT,kBAAA,eAAzBA,kBAAA,CAA2BU,UAAU,EAAE;MACvC,IAAI,IAAI,CAACvD,KAAK,CAACsB,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAACtB,KAAK,CAACwD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAC5B;IACJ;IACA,MAAMC,MAAM,GAAG,IAAI,CAACzD,KAAK,CAAC0D,QAAQ,CAAC3C,YAAY,CAAC;IAChD,MAAMf,KAAK,GAAG,IAAI,CAACD,UAAU,CAACgB,YAAY,EAAEjM,SAAS,CAAC;IACtD;IACA;IACA;IACA,IAAI,CAAC2O,MAAM,EAAE;MACT1C,YAAY,CAACa,GAAG,CAACa,iBAAiB,CAAC7F,aAAa,CAAC,CAAC;IACtD;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM+G,eAAe,GAAG5C,YAAY,CAAChM,gBAAgB;IACrD,IAAIA,gBAAgB,KAAK1C,SAAS,IAAIyC,SAAS,KAAK,MAAM,IAAI,CAACkO,SAAS,IAAIW,eAAe,KAAKtR,SAAS,EAAE;MACvG0C,gBAAgB,GAAG4O,eAAe;IACtC;IACA;AACR;AACA;AACA;IACQ,IAAI3C,WAAW,EAAE;MACbA,WAAW,CAACjM,gBAAgB,GAAGA,gBAAgB;IACnD;IACA;IACA,OAAO,IAAI,CAAClG,IAAI,CAAC6M,iBAAiB,CAAC,MAAM;MACrC,OAAO,IAAI,CAACkI,IAAI,CAAC,MAAM;QACnB;QACA;QACA,IAAI5C,WAAW,EAAE;UACbA,WAAW,CAACY,GAAG,CAACa,iBAAiB,CAACoB,MAAM,CAAC,CAAC;QAC9C;QACA;QACA9C,YAAY,CAACa,GAAG,CAACa,iBAAiB,CAACC,QAAQ,CAAC,CAAC;QAC7C,OAAO,IAAI,CAACoB,UAAU,CAAC/C,YAAY,EAAEC,WAAW,EAAEtU,SAAS,EAAE,IAAI,CAACqX,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAEhP,gBAAgB,CAAC,CACnGiP,IAAI,CAAC,MAAMC,YAAY,CAAClD,YAAY,EAAEf,KAAK,EAAEiD,aAAa,EAAE,IAAI,CAACnS,QAAQ,EAAE,IAAI,CAACjC,IAAI,CAAC,CAAC,CACtFmV,IAAI,CAAC,OAAO;UACbjD,YAAY;UACZjM,SAAS;UACTpI,SAAS;UACTsW;QACJ,CAAC,CAAC,CAAC;MACP,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAe,SAASA,CAACG,IAAI,EAAE5D,OAAO,GAAG,IAAI,CAAC6D,gBAAgB,CAAC,CAAC,EAAE;IAC/C,OAAO,IAAI,CAACC,QAAQ,CAAC9D,OAAO,CAAC,CAACgB,MAAM,GAAG4C,IAAI;EAC/C;EACAlQ,GAAGA,CAACkQ,IAAI,EAAE5D,OAAO,GAAG,IAAI,CAAC6D,gBAAgB,CAAC,CAAC,EAAE;IACzC,OAAO,IAAI,CAACtV,IAAI,CAACC,GAAG,CAAC,MAAM;MACvB,MAAMkR,KAAK,GAAG,IAAI,CAACoE,QAAQ,CAAC9D,OAAO,CAAC;MACpC,IAAIN,KAAK,CAACsB,MAAM,IAAI4C,IAAI,EAAE;QACtB,OAAOjU,OAAO,CAAC+H,OAAO,CAAC,KAAK,CAAC;MACjC;MACA,MAAMiI,IAAI,GAAGD,KAAK,CAACA,KAAK,CAACsB,MAAM,GAAG4C,IAAI,GAAG,CAAC,CAAC;MAC3C,IAAIrS,GAAG,GAAGoO,IAAI,CAACpO,GAAG;MAClB,MAAMwS,aAAa,GAAGpE,IAAI,CAACqE,SAAS;MACpC,IAAID,aAAa,EAAE;QAAA,IAAAE,oBAAA;QACf,MAAMC,aAAa,GAAGH,aAAa,CAAChY,GAAG,CAAC,SAAS,CAAC;QAClD,IAAImY,aAAa,aAAbA,aAAa,gBAAAD,oBAAA,GAAbC,aAAa,CAAEC,KAAK,cAAAF,oBAAA,gBAAAA,oBAAA,GAApBA,oBAAA,CAAsBG,YAAY,cAAAH,oBAAA,eAAlCA,oBAAA,CAAoCI,QAAQ,CAAC9S,GAAG,EAAE;UAClDA,GAAG,GAAG2S,aAAa,CAACC,KAAK,CAACC,YAAY,CAACC,QAAQ,CAAC9S,GAAG;QACvD;MACJ;MACA,MAAM;QAAEkD;MAAiB,CAAC,GAAG,IAAI,CAACkN,OAAO,CAAC/M,iBAAiB,CAAC,CAAC;MAC7D,OAAO,IAAI,CAAC+M,OAAO,CAAC3N,YAAY,CAACzC,GAAG,EAAE;QAAE,GAAGoO,IAAI,CAAC2E,WAAW;QAAElY,SAAS,EAAEqI;MAAiB,CAAC,CAAC,CAACiP,IAAI,CAAC,MAAM,IAAI,CAAC;IAChH,CAAC,CAAC;EACN;EACAa,mBAAmBA,CAAA,EAAG;IAClB,MAAM7D,WAAW,GAAG,IAAI,CAAC+B,UAAU;IACnC,IAAI/B,WAAW,EAAE;MACb,MAAMhB,KAAK,GAAG,IAAI,CAACoE,QAAQ,CAACpD,WAAW,CAACV,OAAO,CAAC;MAChD,MAAMS,YAAY,GAAGf,KAAK,CAACA,KAAK,CAACsB,MAAM,GAAG,CAAC,CAAC;MAC5C,MAAMqC,eAAe,GAAG5C,YAAY,CAAChM,gBAAgB;MACrD,OAAO,IAAI,CAAC6O,IAAI,CAAC,MAAM;QACnB,OAAO,IAAI,CAACE,UAAU,CAAC/C,YAAY;QAAE;QACrCC,WAAW;QAAE;QACb,MAAM,EAAE,IAAI,CAAC+C,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAEJ,eAAe,CAAC;MACrD,CAAC,CAAC;IACN;IACA,OAAO1T,OAAO,CAAC+H,OAAO,CAAC,CAAC;EAC5B;EACA8M,iBAAiBA,CAACC,cAAc,EAAE;IAC9B,IAAIA,cAAc,EAAE;MAChB,IAAI,CAACC,cAAc,GAAG,IAAI;MAC1B,IAAI,CAAChR,GAAG,CAAC,CAAC,CAAC;IACf,CAAC,MACI,IAAI,IAAI,CAAC+O,UAAU,EAAE;MACtBkC,OAAO,CAAC,IAAI,CAAClC,UAAU,EAAE,IAAI,CAAC/C,KAAK,EAAE,IAAI,CAACA,KAAK,EAAE,IAAI,CAAClP,QAAQ,EAAE,IAAI,CAACjC,IAAI,CAAC;IAC9E;EACJ;EACAqW,UAAUA,CAAC5E,OAAO,EAAE;IAChB,MAAMN,KAAK,GAAG,IAAI,CAACoE,QAAQ,CAAC9D,OAAO,CAAC;IACpC,OAAON,KAAK,CAACsB,MAAM,GAAG,CAAC,GAAGtB,KAAK,CAACA,KAAK,CAACsB,MAAM,GAAG,CAAC,CAAC,GAAGjP,SAAS;EACjE;EACA;AACJ;AACA;EACI8S,UAAUA,CAAC7E,OAAO,EAAE;IAChB,MAAMN,KAAK,GAAG,IAAI,CAACoE,QAAQ,CAAC9D,OAAO,CAAC;IACpC,OAAON,KAAK,CAACsB,MAAM,GAAG,CAAC,GAAGtB,KAAK,CAAC,CAAC,CAAC,GAAG3N,SAAS;EAClD;EACA8R,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACpB,UAAU,GAAG,IAAI,CAACA,UAAU,CAACzC,OAAO,GAAGjO,SAAS;EAChE;EACA;AACJ;AACA;EACI+S,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACrC,UAAU;EAC1B;EACAsC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW,KAAKjT,SAAS;EACzC;EACAmG,OAAOA,CAAA,EAAG;IACN;IACA,IAAI,CAACwJ,WAAW,GAAG3P,SAAS;IAC5B,IAAI,CAAC2N,KAAK,CAACvF,OAAO,CAACkH,WAAW,CAAC;IAC/B,IAAI,CAACoB,UAAU,GAAG1Q,SAAS;IAC3B,IAAI,CAAC2N,KAAK,GAAG,EAAE;EACnB;EACAoE,QAAQA,CAAC9D,OAAO,EAAE;IACd,OAAO,IAAI,CAACN,KAAK,CAAC5U,MAAM,CAAEiV,CAAC,IAAKA,CAAC,CAACC,OAAO,KAAKA,OAAO,CAAC;EAC1D;EACAP,UAAUA,CAACgB,YAAY,EAAEjM,SAAS,EAAE;IAChC,IAAI,CAACiO,UAAU,GAAGhC,YAAY;IAC9B,IAAI,CAACf,KAAK,GAAGD,UAAU,CAAC,IAAI,CAACC,KAAK,EAAEe,YAAY,EAAEjM,SAAS,CAAC;IAC5D,OAAO,IAAI,CAACkL,KAAK,CAACkD,KAAK,CAAC,CAAC;EAC7B;EACAY,UAAUA,CAAC/C,YAAY,EAAEC,WAAW,EAAElM,SAAS,EAAEyQ,UAAU,EAAEC,iBAAiB,EAAEzQ,gBAAgB,EAAE;IAC9F,IAAI,IAAI,CAACiQ,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,GAAG,KAAK;MAC3B,OAAO/U,OAAO,CAAC+H,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,IAAIgJ,WAAW,KAAKD,YAAY,EAAE;MAC9B,OAAO9Q,OAAO,CAAC+H,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,MAAMyN,UAAU,GAAG1E,YAAY,GAAGA,YAAY,CAAC3G,OAAO,GAAG/H,SAAS;IAClE,MAAMqT,SAAS,GAAG1E,WAAW,GAAGA,WAAW,CAAC5G,OAAO,GAAG/H,SAAS;IAC/D,MAAM2P,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC,IAAIyD,UAAU,IAAIA,UAAU,KAAKC,SAAS,EAAE;MACxCD,UAAU,CAAC5L,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MACpC2L,UAAU,CAAC5L,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;MAC9C,IAAIkI,WAAW,CAAC2D,MAAM,EAAE;QACpB,OAAO3D,WAAW,CAAC2D,MAAM,CAACF,UAAU,EAAEC,SAAS,EAAE;UAC7CE,QAAQ,EAAE9Q,SAAS,KAAKzC,SAAS,GAAG,CAAC,GAAGA,SAAS;UACjDyC,SAAS;UACTyQ,UAAU;UACVC,iBAAiB;UACjBzQ;QACJ,CAAC,CAAC;MACN;IACJ;IACA,OAAO9E,OAAO,CAAC+H,OAAO,CAAC,KAAK,CAAC;EACjC;EACM4L,IAAIA,CAACiC,IAAI,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAApR,iBAAA;MACb,IAAIoR,MAAI,CAACR,WAAW,KAAKjT,SAAS,EAAE;QAChC,MAAMyT,MAAI,CAACR,WAAW;QACtBQ,MAAI,CAACR,WAAW,GAAGjT,SAAS;MAChC;MACA,MAAM0T,OAAO,GAAID,MAAI,CAACR,WAAW,GAAGO,IAAI,CAAC,CAAE;MAC3CE,OAAO,CAACC,OAAO,CAAC,MAAOF,MAAI,CAACR,WAAW,GAAGjT,SAAU,CAAC;MACrD,OAAO0T,OAAO;IAAC;EACnB;AACJ;AACA,MAAM9B,YAAY,GAAGA,CAACgC,WAAW,EAAEjG,KAAK,EAAEiD,aAAa,EAAEnS,QAAQ,EAAEjC,IAAI,KAAK;EACxE,IAAI,OAAOH,qBAAqB,KAAK,UAAU,EAAE;IAC7C,OAAO,IAAIuB,OAAO,CAAE+H,OAAO,IAAK;MAC5BtJ,qBAAqB,CAAC,MAAM;QACxBuW,OAAO,CAACgB,WAAW,EAAEjG,KAAK,EAAEiD,aAAa,EAAEnS,QAAQ,EAAEjC,IAAI,CAAC;QAC1DmJ,OAAO,CAAC,CAAC;MACb,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA,OAAO/H,OAAO,CAAC+H,OAAO,CAAC,CAAC;AAC5B,CAAC;AACD,MAAMiN,OAAO,GAAGA,CAACgB,WAAW,EAAEjG,KAAK,EAAEiD,aAAa,EAAEnS,QAAQ,EAAEjC,IAAI,KAAK;EACnE;AACJ;AACA;AACA;EACIA,IAAI,CAACC,GAAG,CAAC,MAAMmU,aAAa,CAAC7X,MAAM,CAAE6U,IAAI,IAAK,CAACD,KAAK,CAAC0D,QAAQ,CAACzD,IAAI,CAAC,CAAC,CAACxF,OAAO,CAACkH,WAAW,CAAC,CAAC;EAC1F3B,KAAK,CAACvF,OAAO,CAAEwF,IAAI,IAAK;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMiG,qBAAqB,GAAGpV,QAAQ,CAACyQ,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3D,MAAM2E,uBAAuB,GAAGD,qBAAqB,CAAC1E,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACnE,IAAIvB,IAAI,KAAKgG,WAAW,IAAIhG,IAAI,CAACpO,GAAG,KAAKsU,uBAAuB,EAAE;MAC9D,MAAM/L,OAAO,GAAG6F,IAAI,CAAC7F,OAAO;MAC5BA,OAAO,CAACgM,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MAC3ChM,OAAO,CAACP,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;MACxCmG,IAAI,CAAC2B,GAAG,CAACa,iBAAiB,CAACoB,MAAM,CAAC,CAAC;IACvC;EACJ,CAAC,CAAC;AACN,CAAC;;AAED;AACA;AACA,MAAMwC,eAAe,CAAC;EAYlB;EACA,IAAIC,qBAAqBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,SAAS;EACzB;EAuBA,IAAI7Z,SAASA,CAACA,SAAS,EAAE;IACrB,IAAI,CAAC8Z,QAAQ,CAAC9Z,SAAS,GAAGA,SAAS;EACvC;EACA,IAAIW,QAAQA,CAACA,QAAQ,EAAE;IACnB,IAAI,CAACmZ,QAAQ,CAACnZ,QAAQ,GAAGA,QAAQ;EACrC;EACA,IAAInB,YAAYA,CAACua,KAAK,EAAE;IACpB,IAAI,CAACC,aAAa,GAAGD,KAAK;IAC1B,IAAI,CAACD,QAAQ,CAACG,YAAY,GAAGF,KAAK,GAC5B;MACEG,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACC,SAAS,CAAC9C,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC8C,SAAS,CAACxB,cAAc,CAAC,CAAC;MAC/EyB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACD,SAAS,CAAChC,mBAAmB,CAAC,CAAC;MACnDkC,KAAK,EAAGC,cAAc,IAAK,IAAI,CAACH,SAAS,CAAC/B,iBAAiB,CAACkC,cAAc;IAC9E,CAAC,GACC3U,SAAS;EACnB;EACA5G,WAAWA,CAACgB,IAAI,EAAEwa,IAAI,EAAEC,cAAc,EAAEC,UAAU,EAAE7T,MAAM,EAAEzE,IAAI,EAAE6R,cAAc,EAAE7L,YAAY,EAAE;IAAAlJ,eAAA;IAAAA,eAAA;IAAAA,eAAA,wBAnDhF,IAAI;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAIpB;IAAAA,eAAA,mBACW,IAAI+L,OAAO,CAAC,CAAC;IACxB;IAAA/L,eAAA,iCACyB,IAAIX,eAAe,CAAC,IAAI,CAAC;IAAAW,eAAA,oBACtC,IAAI;IAAAA,eAAA,0BAKE,IAAI;IACtB;AACJ;AACA;IAFIA,eAAA,eAGO3B,cAAc;IACrB;IAAA2B,eAAA,0BACkB,IAAIxC,YAAY,CAAC,CAAC;IACpC;IAAAwC,eAAA,yBACiB,IAAIxC,YAAY,CAAC,CAAC;IACnC;IAAAwC,eAAA,yBACiB,IAAIxC,YAAY,CAAC,CAAC;IACnC;IAAAwC,eAAA,2BACmB,IAAIxC,YAAY,CAAC,CAAC;IAAAwC,eAAA,yBACpBhD,MAAM,CAACsB,sBAAsB,CAAC;IAAA0B,eAAA,mBACpChD,MAAM,CAACS,gBAAgB,CAAC;IAAAuC,eAAA,8BACbhD,MAAM,CAACU,mBAAmB,CAAC;IAAAsC,eAAA,sBACnChD,MAAM,CAACye,YAAY,EAAE;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;IACtD;IAAA1b,eAAA,2CACmC,IAAI;IACvC;IAAAA,eAAA,iBACShD,MAAM,CAACoN,MAAM,CAAC;IAAApK,eAAA,kBACbhD,MAAM,CAACwK,aAAa,CAAC;IAkB3B,IAAI,CAAC0B,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC2R,QAAQ,GAAGW,UAAU,CAACjO,aAAa;IACxC,IAAI,CAACzM,IAAI,GAAGA,IAAI,IAAIzC,cAAc;IAClC,IAAI,CAAC+X,UAAU,GAAGkF,IAAI,KAAK,MAAM,GAAGxG,MAAM,CAACnN,MAAM,EAAEoN,cAAc,CAAC,GAAGrO,SAAS;IAC9E,IAAI,CAACwU,SAAS,GAAG,IAAI/E,eAAe,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACyE,QAAQ,EAAElT,MAAM,EAAE,IAAI,CAAC2O,OAAO,EAAEpT,IAAI,EAAEqY,cAAc,CAAC;IAChH,IAAI,CAACI,cAAc,CAACC,oBAAoB,CAAC,IAAI,CAAC9a,IAAI,EAAE,IAAI,CAAC;EAC7D;EACA+a,WAAWA,CAAA,EAAG;IAAA,IAAAC,iBAAA;IACV,IAAI,CAACZ,SAAS,CAACrO,OAAO,CAAC,CAAC;IACxB,CAAAiP,iBAAA,OAAI,CAACC,WAAW,cAAAD,iBAAA,eAAhBA,iBAAA,CAAkBE,wBAAwB,CAAC,IAAI,CAAC;EACpD;EACAC,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACN,cAAc,CAACM,UAAU,CAAC,IAAI,CAACnb,IAAI,CAAC;EACpD;EACAob,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,wBAAwB,CAAC,CAAC;EACnC;EACA;EACAA,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAAC,IAAI,CAACvB,SAAS,EAAE;MACjB;MACA;MACA,MAAMwB,OAAO,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;MACjC,IAAIG,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEtD,KAAK,EAAE;QAChB,IAAI,CAACuD,YAAY,CAACD,OAAO,CAACtD,KAAK,EAAEsD,OAAO,CAAC9Q,QAAQ,CAAC;MACtD;IACJ;IACA,IAAIhH,OAAO,CAAE+H,OAAO,IAAKnN,gBAAgB,CAAC,IAAI,CAAC2b,QAAQ,EAAExO,OAAO,CAAC,CAAC,CAACgM,IAAI,CAAC,MAAM;MAC1E,IAAI,IAAI,CAAC0C,aAAa,KAAKrU,SAAS,EAAE;QAClC,IAAI,CAACnG,YAAY,GAAG,IAAI,CAACsK,MAAM,CAACL,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAACqQ,QAAQ,CAACjI,IAAI,KAAK,KAAK,CAAC;MAChG;IACJ,CAAC,CAAC;EACN;EACA,IAAI0J,WAAWA,CAAA,EAAG;IACd,OAAO,CAAC,CAAC,IAAI,CAAC1B,SAAS;EAC3B;EACA,IAAI1O,SAASA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAAC0O,SAAS,EAAE;MACjB,MAAM,IAAI2B,KAAK,CAAC,yBAAyB,CAAC;IAC9C;IACA,OAAO,IAAI,CAAC3B,SAAS,CAACvN,QAAQ;EAClC;EACA,IAAI0H,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC6F,SAAS,EAAE;MACjB,MAAM,IAAI2B,KAAK,CAAC,yBAAyB,CAAC;IAC9C;IACA,OAAO,IAAI,CAACC,eAAe;EAC/B;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACD,eAAe,EAAE;MACtB,OAAO,IAAI,CAACA,eAAe,CAACxD,QAAQ,CAACjO,IAAI;IAC7C;IACA,OAAO,CAAC,CAAC;EACb;EACA;AACJ;AACA;EACImN,MAAMA,CAAA,EAAG;IACL,MAAM,IAAIqE,KAAK,CAAC,6BAA6B,CAAC;EAClD;EACA;AACJ;AACA;EACI;EACAG,MAAMA,CAACC,IAAI,EAAEH,eAAe,EAAE;IAC1B,MAAM,IAAID,KAAK,CAAC,6BAA6B,CAAC;EAClD;EACAK,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAAChC,SAAS,EAAE;MAChB,IAAI,IAAI,CAACiC,aAAa,EAAE;QACpB;QACA,MAAMT,OAAO,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;QACjC,IAAI,CAACY,aAAa,CAAClE,SAAS,GAAG,IAAImE,GAAG,CAACV,OAAO,CAACW,QAAQ,CAAC,UAAU,CAAC,CAAC;QACpE;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,MAAMlE,aAAa,GAAG,IAAI,CAACgE,aAAa,CAAClE,SAAS,CAACjY,GAAG,CAAC,SAAS,CAAC;QACjE,IAAImY,aAAa,IAAIuD,OAAO,CAACtD,KAAK,EAAE;UAChCD,aAAa,CAACC,KAAK,GAAG;YAAE,GAAGsD,OAAO,CAACtD;UAAM,CAAC;QAC9C;QACA;AAChB;AACA;AACA;QACgB,IAAI,CAAC+D,aAAa,CAAC5D,WAAW,GAAG,CAAC,CAAC;QACnC,IAAImD,OAAO,CAACtD,KAAK,EAAE;UACf,MAAMkE,eAAe,GAAGZ,OAAO,CAACtD,KAAK,CAACE,QAAQ;UAC9C,IAAI,CAAC6D,aAAa,CAAC5D,WAAW,CAACpP,WAAW,GAAGmT,eAAe,CAACnT,WAAW;UACxE,IAAI,CAACgT,aAAa,CAAC5D,WAAW,CAACnP,QAAQ,GAAGkT,eAAe,CAAClT,QAAQ;QACtE;MACJ;MACA,MAAMQ,CAAC,GAAG,IAAI,CAAC4B,SAAS;MACxB,IAAI,CAAC2Q,aAAa,GAAG,IAAI;MACzB,IAAI,CAACjC,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC4B,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACS,gBAAgB,CAACC,IAAI,CAAC5S,CAAC,CAAC;IACjC;EACJ;EACA+R,YAAYA,CAACtH,cAAc,EAAE1J,mBAAmB,EAAE;IAAA,IAAA8R,kBAAA;IAC9C,IAAI,IAAI,CAACb,WAAW,EAAE;MAClB,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;IAClE;IACA,IAAI,CAACC,eAAe,GAAGzH,cAAc;IACrC,IAAIqI,MAAM;IACV,IAAIhI,YAAY,GAAG,IAAI,CAAC8F,SAAS,CAACxE,eAAe,CAAC3B,cAAc,CAAC;IACjE,IAAIK,YAAY,EAAE;MACdgI,MAAM,GAAG,IAAI,CAACxC,SAAS,GAAGxF,YAAY,CAACa,GAAG;MAC1C,MAAMoH,KAAK,GAAGjI,YAAY,CAACuD,SAAS;MACpC,IAAI0E,KAAK,EAAE;QACP;QACA;QACA,MAAMjB,OAAO,GAAG,IAAI,CAACH,UAAU,CAAC,CAAC;QACjCG,OAAO,CAACW,QAAQ,CAAC,UAAU,CAAC,GAAGM,KAAK;MACxC;MACA;MACA,IAAI,CAACC,yBAAyB,CAACF,MAAM,CAAC/P,QAAQ,EAAE0H,cAAc,CAAC;IACnE,CAAC,MACI;MAAA,IAAAwI,qBAAA;MACD,MAAMvE,QAAQ,GAAGjE,cAAc,CAACyI,eAAe;MAC/C;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMC,aAAa,GAAG,IAAI,CAAC9B,cAAc,CAAC+B,kBAAkB,CAAC,IAAI,CAAC5c,IAAI,CAAC,CAACic,QAAQ;MAChF;MACA;MACA,MAAMY,UAAU,GAAG,IAAIte,eAAe,CAAC,IAAI,CAAC;MAC5C,MAAMue,mBAAmB,GAAG,IAAI,CAACC,yBAAyB,CAACF,UAAU,EAAE5I,cAAc,CAAC;MACtF,MAAMzJ,QAAQ,GAAG,IAAIwS,cAAc,CAACF,mBAAmB,EAAEH,aAAa,EAAE,IAAI,CAACtY,QAAQ,CAACmG,QAAQ,CAAC;MAC/F;MACA,MAAMY,SAAS,IAAAqR,qBAAA,GAAGvE,QAAQ,CAAC+E,WAAW,CAAC7R,SAAS,cAAAqR,qBAAA,cAAAA,qBAAA,GAAIvE,QAAQ,CAAC9M,SAAS;MACtE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACYkR,MAAM,GAAG,IAAI,CAACxC,SAAS,GAAG,IAAI,CAACoD,aAAa,CAAC5gB,eAAe,CAAC8O,SAAS,EAAE;QACpE2I,KAAK,EAAE,IAAI,CAACmJ,aAAa,CAACrI,MAAM;QAChCrK,QAAQ;QACRD,mBAAmB,EAAEA,mBAAmB,aAAnBA,mBAAmB,cAAnBA,mBAAmB,GAAI,IAAI,CAACA;MACrD,CAAC,CAAC;MACF;MACAsS,UAAU,CAACpW,IAAI,CAAC6V,MAAM,CAAC/P,QAAQ,CAAC;MAChC;MACA;MACA;AACZ;AACA;AACA;MACY;MACA+H,YAAY,GAAG,IAAI,CAAC8F,SAAS,CAAC3E,UAAU,CAAC,IAAI,CAACqE,SAAS,EAAE7F,cAAc,CAAC;MACxE;MACA,IAAI,CAACkJ,QAAQ,CAAC1P,GAAG,CAAC6O,MAAM,CAAC/P,QAAQ,EAAEuQ,mBAAmB,CAAC;MACvD,IAAI,CAACM,sBAAsB,CAAC3W,IAAI,CAAC;QAAE2E,SAAS,EAAEkR,MAAM,CAAC/P,QAAQ;QAAE0H;MAAe,CAAC,CAAC;IACpF;IACA,CAAAoI,kBAAA,OAAI,CAACpB,WAAW,cAAAoB,kBAAA,eAAhBA,kBAAA,CAAkBgB,mCAAmC,CAAC,IAAI,CAAC;IAC3D,IAAI,CAACtB,aAAa,GAAGzH,YAAY;IACjC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkB,OAAO,CAAChN,YAAY,CAAC,IAAI,CAAC;IAC/B,MAAM+L,WAAW,GAAG,IAAI,CAAC6F,SAAS,CAACzB,aAAa,CAAC,CAAC;IAClD,IAAI,CAAC2E,eAAe,CAAClB,IAAI,CAAC;MACtB9H,YAAY;MACZiC,SAAS,EAAElC,WAAW,CAACC,YAAY,EAAEC,WAAW;IACpD,CAAC,CAAC;IACF,IAAI,CAAC6F,SAAS,CAAClE,SAAS,CAAC5B,YAAY,CAAC,CAACiD,IAAI,CAAEtN,IAAI,IAAK;MAClD,IAAI,CAACsT,cAAc,CAACnB,IAAI,CAACE,MAAM,CAAC/P,QAAQ,CAAC;MACzC,IAAI,CAACiR,cAAc,CAACpB,IAAI,CAACnS,IAAI,CAAC;IAClC,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIqN,SAASA,CAACG,IAAI,GAAG,CAAC,EAAE5D,OAAO,EAAE;IACzB,OAAO,IAAI,CAACuG,SAAS,CAAC9C,SAAS,CAACG,IAAI,EAAE5D,OAAO,CAAC;EAClD;EACA;AACJ;AACA;EACItM,GAAGA,CAACkQ,IAAI,GAAG,CAAC,EAAE5D,OAAO,EAAE;IACnB,OAAO,IAAI,CAACuG,SAAS,CAAC7S,GAAG,CAACkQ,IAAI,EAAE5D,OAAO,CAAC;EAC5C;EACA;AACJ;AACA;EACI4E,UAAUA,CAAC5E,OAAO,EAAE;IAChB,MAAM4J,MAAM,GAAG,IAAI,CAACrD,SAAS,CAAC3B,UAAU,CAAC5E,OAAO,CAAC;IACjD,OAAO4J,MAAM,GAAGA,MAAM,CAACrY,GAAG,GAAGQ,SAAS;EAC1C;EACA;AACJ;AACA;AACA;EACI8X,gBAAgBA,CAAC7J,OAAO,EAAE;IACtB,OAAO,IAAI,CAACuG,SAAS,CAAC3B,UAAU,CAAC5E,OAAO,CAAC;EAC7C;EACA;AACJ;AACA;AACA;EACI8J,WAAWA,CAAC9J,OAAO,EAAE;IACjB,OAAO,IAAI,CAACuG,SAAS,CAAC1B,UAAU,CAAC7E,OAAO,CAAC;EAC7C;EACA;AACJ;AACA;EACI6D,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC0C,SAAS,CAAC1C,gBAAgB,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;AACA;EACIqF,yBAAyBA,CAACF,UAAU,EAAE5I,cAAc,EAAE;IAClD,MAAM2J,KAAK,GAAG,IAAIngB,cAAc,CAAC,CAAC;IAClCmgB,KAAK,CAAClB,eAAe,GAAGzI,cAAc,CAACyI,eAAe;IACtDkB,KAAK,CAAC3F,YAAY,GAAGhE,cAAc,CAACgE,YAAY;IAChD2F,KAAK,CAAC1F,QAAQ,GAAGjE,cAAc,CAACiE,QAAQ;IACxC0F,KAAK,CAAC1V,MAAM,GAAG+L,cAAc,CAAC/L,MAAM;IACpC0V,KAAK,CAACxS,SAAS,GAAG6I,cAAc,CAAC7I,SAAS;IAC1C;IACAwS,KAAK,CAACC,SAAS,GAAG,IAAI,CAACC,eAAe,CAACjB,UAAU,EAAE,UAAU,CAAC;IAC9De,KAAK,CAACG,cAAc,GAAG,IAAI,CAACD,eAAe,CAACjB,UAAU,EAAE,eAAe,CAAC;IACxEe,KAAK,CAACxY,GAAG,GAAG,IAAI,CAAC0Y,eAAe,CAACjB,UAAU,EAAE,KAAK,CAAC;IACnDe,KAAK,CAACvS,MAAM,GAAG,IAAI,CAACyS,eAAe,CAACjB,UAAU,EAAE,QAAQ,CAAC;IACzDe,KAAK,CAAC7U,WAAW,GAAG,IAAI,CAAC+U,eAAe,CAACjB,UAAU,EAAE,aAAa,CAAC;IACnEe,KAAK,CAAC5U,QAAQ,GAAG,IAAI,CAAC8U,eAAe,CAACjB,UAAU,EAAE,UAAU,CAAC;IAC7De,KAAK,CAAC3T,IAAI,GAAG,IAAI,CAAC6T,eAAe,CAACjB,UAAU,EAAE,MAAM,CAAC;IACrD,OAAOe,KAAK;EAChB;EACA;AACJ;AACA;EACIE,eAAeA,CAACjB,UAAU,EAAE/H,IAAI,EAAE;IAC9B,OAAO+H,UAAU,CAACmB,IAAI;IACtB;IACArf,MAAM,CAAEyM,SAAS,IAAK,CAAC,CAACA,SAAS,CAAC,EAAExM,SAAS,CAAEwM,SAAS,IAAK,IAAI,CAACgS,sBAAsB,CAACY,IAAI,CAACrf,MAAM,CAAEsf,OAAO,IAAKA,OAAO,KAAK,IAAI,IAAIA,OAAO,CAAC7S,SAAS,KAAKA,SAAS,CAAC,EAAExM,SAAS,CAAEqf,OAAO,IAAKA,OAAO,IAAIA,OAAO,CAAChK,cAAc,CAACa,IAAI,CAAC,CAAC,EAAEjW,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;EACtQ;EACA;AACJ;AACA;EACI2d,yBAAyBA,CAACpR,SAAS,EAAE6I,cAAc,EAAE;IACjD,MAAM2J,KAAK,GAAG,IAAI,CAACT,QAAQ,CAACvd,GAAG,CAACwL,SAAS,CAAC;IAC1C,IAAI,CAACwS,KAAK,EAAE;MACR,MAAM,IAAInC,KAAK,CAAC,+CAA+C,CAAC;IACpE;IACAmC,KAAK,CAAClB,eAAe,GAAGzI,cAAc,CAACyI,eAAe;IACtDkB,KAAK,CAAC3F,YAAY,GAAGhE,cAAc,CAACgE,YAAY;IAChD2F,KAAK,CAAC1F,QAAQ,GAAGjE,cAAc,CAACiE,QAAQ;IACxC0F,KAAK,CAAC1V,MAAM,GAAG+L,cAAc,CAAC/L,MAAM;IACpC0V,KAAK,CAACxS,SAAS,GAAG6I,cAAc,CAAC7I,SAAS;IAC1C,IAAI,CAACgS,sBAAsB,CAAC3W,IAAI,CAAC;MAAE2E,SAAS;MAAE6I;IAAe,CAAC,CAAC;EACnE;EACA;AAEJ;AAACiK,gBAAA,GAvUKtE,eAAe;AAAA1a,eAAA,CAAf0a,eAAe,wBAAAuE,yBAAA/c,iBAAA;EAAA,YAAAA,iBAAA,IAqUsGwY,gBAAe,EA5nDxD/d,EAAE,CAAAuiB,iBAAA,CA4nDwE,MAAM,GA5nDhFviB,EAAE,CAAAuiB,iBAAA,CA4nD4G,MAAM,GA5nDpHviB,EAAE,CAAAwU,iBAAA,CA4nDgK1S,EAAE,CAACyL,QAAQ,GA5nD7KvN,EAAE,CAAAwU,iBAAA,CA4nDwLxU,EAAE,CAACsB,UAAU,GA5nDvMtB,EAAE,CAAAwU,iBAAA,CA4nDkNhT,EAAE,CAACK,MAAM,GA5nD7N7B,EAAE,CAAAwU,iBAAA,CA4nDwOxU,EAAE,CAACM,MAAM,GA5nDnPN,EAAE,CAAAwU,iBAAA,CA4nD8PhT,EAAE,CAACI,cAAc,GA5nDjR5B,EAAE,CAAAwU,iBAAA,CA4nD4RuJ,gBAAe;AAAA;AAC3X;AAAA1a,eAAA,CAtUE0a,eAAe,8BAvzC6D/d,EAAE,CAAA0U,iBAAA;EAAAlQ,IAAA,EA6nD2BuZ,gBAAe;EAAApJ,SAAA;EAAA9B,MAAA;IAAA9N,QAAA;IAAAX,SAAA;IAAA6R,IAAA;IAAArS,YAAA;IAAAO,IAAA;EAAA;EAAAqe,OAAA;IAAAf,eAAA;IAAAE,cAAA;IAAAD,cAAA;IAAApB,gBAAA;EAAA;EAAAmC,QAAA;EAAAhM,UAAA;AAAA;AAE9H;EAAA,QAAA5Q,SAAA,oBAAAA,SAAA,KA/nDkF7F,EAAE,CAAA8F,iBAAA,CA+nDQiY,eAAe,EAAc,CAAC;IAC9GvZ,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC2Q,QAAQ,EAAE,mBAAmB;MAC7B+L,QAAQ,EAAE,QAAQ;MAClB;MACA5P,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc;IAC5D,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErO,IAAI,EAAEuF,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DxF,IAAI,EAAExD,SAAS;QACf+E,IAAI,EAAE,CAAC,MAAM;MACjB,CAAC;IAAE,CAAC,EAAE;MAAEvB,IAAI,EAAEuF,SAAS;MAAEC,UAAU,EAAE,CAAC;QAClCxF,IAAI,EAAErE;MACV,CAAC,EAAE;QACCqE,IAAI,EAAExD,SAAS;QACf+E,IAAI,EAAE,CAAC,MAAM;MACjB,CAAC;IAAE,CAAC,EAAE;MAAEvB,IAAI,EAAE1C,EAAE,CAACyL;IAAS,CAAC,EAAE;MAAE/I,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAEhD,EAAE,CAACK;IAAO,CAAC,EAAE;MAAE2C,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,EAAE;MAAEkE,IAAI,EAAEhD,EAAE,CAACI;IAAe,CAAC,EAAE;MAAE4C,IAAI,EAAEuZ,eAAe;MAAE/T,UAAU,EAAE,CAAC;QAC/JxF,IAAI,EAAEvD;MACV,CAAC,EAAE;QACCuD,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEgE,IAAI,EAAE,CAAC;MACnCK,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAEugB,eAAe,EAAE,CAAC;MAClBjd,IAAI,EAAErD;IACV,CAAC,CAAC;IAAEwgB,cAAc,EAAE,CAAC;MACjBnd,IAAI,EAAErD;IACV,CAAC,CAAC;IAAEugB,cAAc,EAAE,CAAC;MACjBld,IAAI,EAAErD,MAAM;MACZ4E,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAEua,gBAAgB,EAAE,CAAC;MACnB9b,IAAI,EAAErD,MAAM;MACZ4E,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMob,cAAc,CAAC;EAIjBhe,WAAWA,CAACgZ,KAAK,EAAE2E,aAAa,EAAEtQ,MAAM,EAAE;IAAAnN,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACtC,IAAI,CAAC8Y,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC2E,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACtQ,MAAM,GAAGA,MAAM;EACxB;EACAzM,GAAGA,CAAC0B,KAAK,EAAEid,aAAa,EAAE;IACtB,IAAIjd,KAAK,KAAK7D,cAAc,EAAE;MAC1B,OAAO,IAAI,CAACua,KAAK;IACrB;IACA,IAAI1W,KAAK,KAAK9D,sBAAsB,EAAE;MAClC,OAAO,IAAI,CAACmf,aAAa;IAC7B;IACA,OAAO,IAAI,CAACtQ,MAAM,CAACzM,GAAG,CAAC0B,KAAK,EAAEid,aAAa,CAAC;EAChD;AACJ;AACA;AACA,MAAM5D,YAAY,GAAG,IAAI1e,cAAc,CAAC,EAAE,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuiB,0BAA0B,CAAC;EAAAxf,YAAA;IAAAE,eAAA,kCACH,IAAI8c,GAAG,CAAC,CAAC;EAAA;EACnCqB,mCAAmCA,CAACnV,MAAM,EAAE;IACxC,IAAI,CAACgT,wBAAwB,CAAChT,MAAM,CAAC;IACrC,IAAI,CAACuW,oBAAoB,CAACvW,MAAM,CAAC;EACrC;EACAgT,wBAAwBA,CAAChT,MAAM,EAAE;IAAA,IAAAwW,qBAAA;IAC7B,CAAAA,qBAAA,OAAI,CAACC,uBAAuB,CAAC/e,GAAG,CAACsI,MAAM,CAAC,cAAAwW,qBAAA,eAAxCA,qBAAA,CAA0CE,WAAW,CAAC,CAAC;IACvD,IAAI,CAACD,uBAAuB,CAAC3S,MAAM,CAAC9D,MAAM,CAAC;EAC/C;EACAuW,oBAAoBA,CAACvW,MAAM,EAAE;IACzB,MAAM;MAAE+L;IAAe,CAAC,GAAG/L,MAAM;IACjC,MAAM2W,gBAAgB,GAAGrgB,aAAa,CAAC,CAACyV,cAAc,CAAClL,WAAW,EAAEkL,cAAc,CAAC5I,MAAM,EAAE4I,cAAc,CAAChK,IAAI,CAAC,CAAC,CAC3G+T,IAAI,CAACpf,SAAS,CAAC,CAAC,CAACmK,WAAW,EAAEsC,MAAM,EAAEpB,IAAI,CAAC,EAAE8J,KAAK,KAAK;MACxD9J,IAAI,GAAG;QAAE,GAAGlB,WAAW;QAAE,GAAGsC,MAAM;QAAE,GAAGpB;MAAK,CAAC;MAC7C;MACA;MACA,IAAI8J,KAAK,KAAK,CAAC,EAAE;QACb,OAAOtV,EAAE,CAACwL,IAAI,CAAC;MACnB;MACA;MACA;MACA;MACA,OAAOzG,OAAO,CAAC+H,OAAO,CAACtB,IAAI,CAAC;IAChC,CAAC,CAAC,CAAC,CACErH,SAAS,CAAEqH,IAAI,IAAK;MACrB;MACA;MACA,IAAI,CAAC/B,MAAM,CAACsT,WAAW,IACnB,CAACtT,MAAM,CAAC2R,qBAAqB,IAC7B3R,MAAM,CAAC+L,cAAc,KAAKA,cAAc,IACxCA,cAAc,CAAC7I,SAAS,KAAK,IAAI,EAAE;QACnC,IAAI,CAAC8P,wBAAwB,CAAChT,MAAM,CAAC;QACrC;MACJ;MACA,MAAM4W,MAAM,GAAG7hB,oBAAoB,CAACgX,cAAc,CAAC7I,SAAS,CAAC;MAC7D,IAAI,CAAC0T,MAAM,EAAE;QACT,IAAI,CAAC5D,wBAAwB,CAAChT,MAAM,CAAC;QACrC;MACJ;MACA,KAAK,MAAM;QAAE6W;MAAa,CAAC,IAAID,MAAM,CAACpQ,MAAM,EAAE;QAC1CxG,MAAM,CAAC2R,qBAAqB,CAAChN,QAAQ,CAACkS,YAAY,EAAE9U,IAAI,CAAC8U,YAAY,CAAC,CAAC;MAC3E;IACJ,CAAC,CAAC;IACF,IAAI,CAACJ,uBAAuB,CAAClR,GAAG,CAACvF,MAAM,EAAE2W,gBAAgB,CAAC;EAC9D;EACA;AAEJ;AAACG,2BAAA,GAhDKR,0BAA0B;AAAAtf,eAAA,CAA1Bsf,0BAA0B,wBAAAS,oCAAA7d,iBAAA;EAAA,YAAAA,iBAAA,IA8C2Fod,2BAA0B;AAAA;AACjJ;AAAAtf,eAAA,CA/CEsf,0BAA0B,+BAnsDkD3iB,EAAE,CAAAwF,kBAAA;EAAAC,KAAA,EAkvD2Ckd,2BAA0B;EAAAjd,OAAA,EAA1Bid,2BAA0B,CAAAhd;AAAA;AAEzJ;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KApvDkF7F,EAAE,CAAA8F,iBAAA,CAovDQ6c,0BAA0B,EAAc,CAAC;IACzHne,IAAI,EAAEvE;EACV,CAAC,CAAC;AAAA;AACV,MAAMojB,4BAA4B,GAAGA,CAAA,KAAM;EACvC,OAAO;IACH/Q,OAAO,EAAEwM,YAAY;IACrBtM,UAAU,EAAE8Q,4BAA4B;IACxC5Q,IAAI,EAAE,CAAC7Q,MAAM;EACjB,CAAC;AACL,CAAC;AACD,SAASyhB,4BAA4BA,CAACtY,MAAM,EAAE;EAC1C;AACJ;AACA;AACA;EACI,IAAIA,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEuY,4BAA4B,EAAE;IACtC,OAAO,IAAIZ,0BAA0B,CAAC,CAAC;EAC3C;EACA,OAAO,IAAI;AACf;AAEA,MAAMa,kBAAkB,GAAG,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,MAAM,CAAC;AAClH,IAAIC,aAAa,IAAAC,cAAA,GAAG,MAAMD,aAAa,CAAC;EAOpCtgB,WAAWA,CAACwgB,YAAY,EAAEhK,OAAO,EAAEzL,MAAM,EAAEkG,CAAC,EAAEjB,CAAC,EAAExF,CAAC,EAAE;IAAAtK,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAChD,IAAI,CAACsgB,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAChK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACzL,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACkG,CAAC,GAAGA,CAAC;IACV,IAAI,CAACjB,CAAC,GAAGA,CAAC;IACVxF,CAAC,CAAC4N,MAAM,CAAC,CAAC;IACV,IAAI,CAAC/Q,EAAE,GAAG,IAAI,CAAC4J,CAAC,CAACxD,aAAa;EAClC;EACA;AACJ;AACA;EACIgT,OAAOA,CAAC5c,EAAE,EAAE;IAAA,IAAA6c,kBAAA;IACR,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,IAAI,CAAC5V,MAAM,CAACnK,GAAG,CAAC,uBAAuB,CAAC;IAChF,KAAA8f,kBAAA,GAAI,IAAI,CAACF,YAAY,cAAAE,kBAAA,eAAjBA,kBAAA,CAAmBpI,SAAS,CAAC,CAAC,EAAE;MAChC,IAAI,CAAC9B,OAAO,CAAC9N,YAAY,CAAC,MAAM,EAAE9B,SAAS,EAAEA,SAAS,EAAE,IAAI,CAACga,eAAe,CAAC;MAC7E,IAAI,CAACJ,YAAY,CAACjY,GAAG,CAAC,CAAC;MACvB1E,EAAE,CAACgd,cAAc,CAAC,CAAC;IACvB,CAAC,MACI,IAAIF,WAAW,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACnK,OAAO,CAAC3N,YAAY,CAAC8X,WAAW,EAAE;QAAE1f,SAAS,EAAE,IAAI,CAAC2f;MAAgB,CAAC,CAAC;MAC3E/c,EAAE,CAACgd,cAAc,CAAC,CAAC;IACvB;EACJ;EACA;AAEJ,CAAC,EAAA3gB,eAAA,CAAAqgB,cAAA,wBAAAO,uBAAA1e,iBAAA;EAAA,YAAAA,iBAAA,IAF0Hke,cAAa,EAzyDtDzjB,EAAE,CAAAwU,iBAAA,CAyyDsEuJ,eAAe,MAzyDvF/d,EAAE,CAAAwU,iBAAA,CAyyDkH3J,aAAa,GAzyDjI7K,EAAE,CAAAwU,iBAAA,CAyyD4I/G,MAAM,GAzyDpJzN,EAAE,CAAAwU,iBAAA,CAyyD+JxU,EAAE,CAACsB,UAAU,GAzyD9KtB,EAAE,CAAAwU,iBAAA,CAyyDyLxU,EAAE,CAACM,MAAM,GAzyDpMN,EAAE,CAAAwU,iBAAA,CAyyD+MxU,EAAE,CAACyU,iBAAiB;AAAA,IAAApR,eAAA,CAAAqgB,cAAA,8BAzyDrO1jB,EAAE,CAAA0U,iBAAA;EAAAlQ,IAAA,EA0yD2Bif,cAAa;EAAAS,YAAA,WAAAC,4BAAArP,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA1yD1C9U,EAAE,CAAAokB,UAAA,mBAAAC,wCAAAC,MAAA;QAAA,OA0yD2BvP,GAAA,CAAA6O,OAAA,CAAAU,MAAc,CAAC;MAAA,CAAH,CAAC;IAAA;EAAA;EAAAzR,MAAA;IAAA0R,KAAA;IAAAT,WAAA;IAAAU,QAAA;IAAAC,IAAA;IAAAxO,IAAA;IAAA8N,eAAA;IAAAW,IAAA;IAAAlgB,IAAA;EAAA;EAAAiS,UAAA;AAAA,KAAAiN,cAAA,CAC3H;AACDD,aAAa,GAAG5gB,UAAU,CAAC,CACvB8Q,QAAQ,CAAC;EACLd,MAAM,EAAE2Q;AACZ,CAAC,CAAC,CACL,EAAEC,aAAa,CAAC;AACjB;EAAA,QAAA5d,SAAA,oBAAAA,SAAA,KAjzDkF7F,EAAE,CAAA8F,iBAAA,CAizDQ2d,aAAa,EAAc,CAAC;IAC5Gjf,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC;MACA8M,MAAM,EAAE2Q;IACZ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEhf,IAAI,EAAEuZ,eAAe;MAAE/T,UAAU,EAAE,CAAC;QACpExF,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,EAAE;MAAEqE,IAAI,EAAEqG;IAAc,CAAC,EAAE;MAAErG,IAAI,EAAEiJ;IAAO,CAAC,EAAE;MAAEjJ,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,EAAE;MAAEkE,IAAI,EAAExE,EAAE,CAACyU;IAAkB,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEmP,OAAO,EAAE,CAAC;MAC/Jpf,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4e,2BAA2B,CAAC;EAQ9BxhB,WAAWA,CAACyhB,gBAAgB,EAAEjL,OAAO,EAAEkF,UAAU,EAAE7T,MAAM,EAAE6Z,UAAU,EAAE;IAAAxhB,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,0BAFrD,SAAS;IAAAA,eAAA;IAGvB,IAAI,CAACuhB,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACjL,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACkF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC7T,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC6Z,UAAU,GAAGA,UAAU;EAChC;EACAtF,QAAQA,CAAA,EAAG;IACP,IAAI,CAACuF,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACC,cAAc,CAAC,CAAC;EACzB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,sBAAsB,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,cAAcA,CAAA,EAAG;IACb;IACA,MAAME,eAAe,GAAG,CACpB,iBAAiB,EACjB,gBAAgB,EAChB,YAAY,EACZ,UAAU,EACV,gBAAgB,EAChB,UAAU,EACV,iBAAiB,EACjB,iBAAiB,EACjB,oBAAoB,EACpB,gBAAgB,CACnB;IACD,MAAMtU,WAAW,GAAG,IAAI,CAACkO,UAAU,CAACjO,aAAa;IACjD,IAAIqU,eAAe,CAAC7J,QAAQ,CAACzK,WAAW,CAACG,OAAO,CAAC,EAAE;MAC/C,IAAIH,WAAW,CAACuU,YAAY,CAAC,UAAU,CAAC,KAAK,GAAG,EAAE;QAC9CvU,WAAW,CAACwU,eAAe,CAAC,UAAU,CAAC;MAC3C;IACJ;EACJ;EACAL,sBAAsBA,CAAA,EAAG;IAAA,IAAAM,gBAAA;IACrB,KAAAA,gBAAA,GAAI,IAAI,CAACP,UAAU,cAAAO,gBAAA,eAAfA,gBAAA,CAAiBrY,OAAO,EAAE;MAC1B,MAAMtE,IAAI,GAAG,IAAI,CAACmc,gBAAgB,CAACS,kBAAkB,CAAC,IAAI,CAACra,MAAM,CAACuN,YAAY,CAAC,IAAI,CAACsM,UAAU,CAAC9X,OAAO,CAAC,CAAC;MACxG,IAAI,CAAC8R,UAAU,CAACjO,aAAa,CAACnI,IAAI,GAAGA,IAAI;IAC7C;EACJ;EACA;AACJ;AACA;EACImb,OAAOA,CAAC5c,EAAE,EAAE;IACR,IAAI,CAAC2S,OAAO,CAAC9N,YAAY,CAAC,IAAI,CAACyZ,eAAe,EAAEvb,SAAS,EAAEA,SAAS,EAAE,IAAI,CAACga,eAAe,CAAC;IAC3F;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ/c,EAAE,CAACgd,cAAc,CAAC,CAAC;EACvB;EACA;AAEJ;AAACuB,4BAAA,GA3EKZ,2BAA2B;AAAAthB,eAAA,CAA3BshB,2BAA2B,wBAAAa,qCAAAjgB,iBAAA;EAAA,YAAAA,iBAAA,IAyE0Fof,4BAA2B,EA74DpE3kB,EAAE,CAAAwU,iBAAA,CA64DoF1S,EAAE,CAAC2jB,gBAAgB,GA74DzGzlB,EAAE,CAAAwU,iBAAA,CA64DoH3J,aAAa,GA74DnI7K,EAAE,CAAAwU,iBAAA,CA64D8IxU,EAAE,CAACsB,UAAU,GA74D7JtB,EAAE,CAAAwU,iBAAA,CA64DwKhT,EAAE,CAACK,MAAM,GA74DnL7B,EAAE,CAAAwU,iBAAA,CA64D8LhT,EAAE,CAACkkB,UAAU;AAAA;AAC3R;AAAAriB,eAAA,CA1EEshB,2BAA2B,8BAp0DiD3kB,EAAE,CAAA0U,iBAAA;EAAAlQ,IAAA,EA84D2BmgB,4BAA2B;EAAAhQ,SAAA;EAAAuP,YAAA,WAAAyB,0CAAA7Q,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA94DxD9U,EAAE,CAAAokB,UAAA,mBAAAwB,sDAAAtB,MAAA;QAAA,OA84D2BvP,GAAA,CAAA6O,OAAA,CAAAU,MAAc,CAAC;MAAA,CAAW,CAAC;IAAA;EAAA;EAAAzR,MAAA;IAAAyS,eAAA;IAAAvB,eAAA;EAAA;EAAAtN,UAAA;EAAAoP,QAAA,GA94DxD7lB,EAAE,CAAA8lB,oBAAA;AAAA;AAg5DpF;EAAA,QAAAjgB,SAAA,oBAAAA,SAAA,KAh5DkF7F,EAAE,CAAA8F,iBAAA,CAg5DQ6e,2BAA2B,EAAc,CAAC;IAC1HngB,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC2Q,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElS,IAAI,EAAE1C,EAAE,CAAC2jB;IAAiB,CAAC,EAAE;MAAEjhB,IAAI,EAAEqG;IAAc,CAAC,EAAE;MAAErG,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAEhD,EAAE,CAACK;IAAO,CAAC,EAAE;MAAE2C,IAAI,EAAEhD,EAAE,CAACkkB,UAAU;MAAE1b,UAAU,EAAE,CAAC;QACxKxF,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEmlB,eAAe,EAAE,CAAC;MAC9C9gB,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAE6iB,eAAe,EAAE,CAAC;MAClBvf,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAE0iB,OAAO,EAAE,CAAC;MACVpf,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMggB,mCAAmC,CAAC;EAQtC5iB,WAAWA,CAACyhB,gBAAgB,EAAEjL,OAAO,EAAEkF,UAAU,EAAE7T,MAAM,EAAE6Z,UAAU,EAAE;IAAAxhB,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,0BAFrD,SAAS;IAAAA,eAAA;IAGvB,IAAI,CAACuhB,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACjL,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACkF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC7T,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC6Z,UAAU,GAAGA,UAAU;EAChC;EACAtF,QAAQA,CAAA,EAAG;IACP,IAAI,CAACuF,sBAAsB,CAAC,CAAC;EACjC;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,sBAAsB,CAAC,CAAC;EACjC;EACAA,sBAAsBA,CAAA,EAAG;IAAA,IAAAkB,iBAAA;IACrB,KAAAA,iBAAA,GAAI,IAAI,CAACnB,UAAU,cAAAmB,iBAAA,eAAfA,iBAAA,CAAiBjZ,OAAO,EAAE;MAC1B,MAAMtE,IAAI,GAAG,IAAI,CAACmc,gBAAgB,CAACS,kBAAkB,CAAC,IAAI,CAACra,MAAM,CAACuN,YAAY,CAAC,IAAI,CAACsM,UAAU,CAAC9X,OAAO,CAAC,CAAC;MACxG,IAAI,CAAC8R,UAAU,CAACjO,aAAa,CAACnI,IAAI,GAAGA,IAAI;IAC7C;EACJ;EACA;AACJ;AACA;EACImb,OAAOA,CAAA,EAAG;IACN,IAAI,CAACjK,OAAO,CAAC9N,YAAY,CAAC,IAAI,CAACyZ,eAAe,EAAEvb,SAAS,EAAEA,SAAS,EAAE,IAAI,CAACga,eAAe,CAAC;EAC/F;EACA;AAEJ;AAACkC,oCAAA,GAnCKF,mCAAmC;AAAA1iB,eAAA,CAAnC0iB,mCAAmC,wBAAAG,6CAAA3gB,iBAAA;EAAA,YAAAA,iBAAA,IAiCkFwgB,oCAAmC,EAh8D5E/lB,EAAE,CAAAwU,iBAAA,CAg8D4F1S,EAAE,CAAC2jB,gBAAgB,GAh8DjHzlB,EAAE,CAAAwU,iBAAA,CAg8D4H3J,aAAa,GAh8D3I7K,EAAE,CAAAwU,iBAAA,CAg8DsJxU,EAAE,CAACsB,UAAU,GAh8DrKtB,EAAE,CAAAwU,iBAAA,CAg8DgLhT,EAAE,CAACK,MAAM,GAh8D3L7B,EAAE,CAAAwU,iBAAA,CAg8DsMhT,EAAE,CAACkkB,UAAU;AAAA;AACnS;AAAAriB,eAAA,CAlCE0iB,mCAAmC,8BA/5DyC/lB,EAAE,CAAA0U,iBAAA;EAAAlQ,IAAA,EAi8D2BuhB,oCAAmC;EAAApR,SAAA;EAAAuP,YAAA,WAAAiC,kDAAArR,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAj8DhE9U,EAAE,CAAAokB,UAAA,mBAAAgC,8DAAA;QAAA,OAi8D2BrR,GAAA,CAAA6O,OAAA,CAAQ,CAAC;MAAA,CAAyB,CAAC;IAAA;EAAA;EAAA/Q,MAAA;IAAAyS,eAAA;IAAAvB,eAAA;EAAA;EAAAtN,UAAA;EAAAoP,QAAA,GAj8DhE7lB,EAAE,CAAA8lB,oBAAA;AAAA;AAm8DpF;EAAA,QAAAjgB,SAAA,oBAAAA,SAAA,KAn8DkF7F,EAAE,CAAA8F,iBAAA,CAm8DQigB,mCAAmC,EAAc,CAAC;IAClIvhB,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC2Q,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElS,IAAI,EAAE1C,EAAE,CAAC2jB;IAAiB,CAAC,EAAE;MAAEjhB,IAAI,EAAEqG;IAAc,CAAC,EAAE;MAAErG,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAEhD,EAAE,CAACK;IAAO,CAAC,EAAE;MAAE2C,IAAI,EAAEhD,EAAE,CAACkkB,UAAU;MAAE1b,UAAU,EAAE,CAAC;QACxKxF,IAAI,EAAErE;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEmlB,eAAe,EAAE,CAAC;MAC9C9gB,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAE6iB,eAAe,EAAE,CAAC;MAClBvf,IAAI,EAAEtD;IACV,CAAC,CAAC;IAAE0iB,OAAO,EAAE,CAAC;MACVpf,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsgB,UAAU,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,CAAC;AAClF,MAAMC,WAAW,GAAG,CAChB,MAAM,EACN,QAAQ,EACR,aAAa,EACb,KAAK,EACL,OAAO,EACP,WAAW,EACX,aAAa,EACb,SAAS,EACT,UAAU,EACV,WAAW,EACX,YAAY,EACZ,WAAW,EACX,aAAa,CAChB;AACD,IAAIC,MAAM,IAAAC,OAAA,GAAG,MAAMD,MAAM,CAAC;EAGtBpjB,WAAWA,CAACmW,GAAG,EAAE5K,mBAAmB,EAAEC,QAAQ,EAAE8X,eAAe,EAAEtT,CAAC,EAAExF,CAAC,EAAE;IAAAtK,eAAA;IAAAA,eAAA;IACnE,IAAI,CAAC8P,CAAC,GAAGA,CAAC;IACVxF,CAAC,CAAC4N,MAAM,CAAC,CAAC;IACV,IAAI,CAAC/Q,EAAE,GAAG8O,GAAG,CAAC1I,aAAa;IAC3B0I,GAAG,CAAC1I,aAAa,CAAC8V,QAAQ,GAAGD,eAAe,CAAChY,MAAM,CAACC,mBAAmB,EAAEC,QAAQ,CAAC;IAClF+E,YAAY,CAAC,IAAI,EAAE,IAAI,CAAClJ,EAAE,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAC;EACxE;EACA;AAEJ,CAAC,EAAAnH,eAAA,CAAAmjB,OAAA,wBAAAG,gBAAAphB,iBAAA;EAAA,YAAAA,iBAAA,IAF0HghB,OAAM,EA7+D/CvmB,EAAE,CAAAwU,iBAAA,CA6+D+DxU,EAAE,CAACsB,UAAU,GA7+D9EtB,EAAE,CAAAwU,iBAAA,CA6+DyFxU,EAAE,CAACe,mBAAmB,GA7+DjHf,EAAE,CAAAwU,iBAAA,CA6+D4HxU,EAAE,CAACQ,QAAQ,GA7+DzIR,EAAE,CAAAwU,iBAAA,CA6+DoJhG,eAAe,GA7+DrKxO,EAAE,CAAAwU,iBAAA,CA6+DgLxU,EAAE,CAACM,MAAM,GA7+D3LN,EAAE,CAAAwU,iBAAA,CA6+DsMxU,EAAE,CAACyU,iBAAiB;AAAA,IAAApR,eAAA,CAAAmjB,OAAA,8BA7+D5NxmB,EAAE,CAAA0U,iBAAA;EAAAlQ,IAAA,EA8+D2B+hB,OAAM;EAAA1T,MAAA;IAAA9N,QAAA;IAAAX,SAAA;IAAAwiB,IAAA;IAAAC,UAAA;IAAAjjB,YAAA;EAAA;EAAA6S,UAAA;AAAA,KAAA+P,OAAA,CACpH;AACDD,MAAM,GAAG1jB,UAAU,CAAC,CAChB8Q,QAAQ,CAAC;EACLd,MAAM,EAAEwT,UAAU;EAClB/S,OAAO,EAAEgT;AACb,CAAC,CAAC,CACL,EAAEC,MAAM,CAAC;AACV;EAAA,QAAA1gB,SAAA,oBAAAA,SAAA,KAt/DkF7F,EAAE,CAAA8F,iBAAA,CAs/DQygB,MAAM,EAAc,CAAC;IACrG/hB,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC;MACA8M,MAAM,EAAEwT;IACZ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE7hB,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,EAAE;MAAEkD,IAAI,EAAExE,EAAE,CAACe;IAAoB,CAAC,EAAE;MAAEyD,IAAI,EAAExE,EAAE,CAACQ;IAAS,CAAC,EAAE;MAAEgE,IAAI,EAAEgK;IAAgB,CAAC,EAAE;MAAEhK,IAAI,EAAExE,EAAE,CAACM;IAAO,CAAC,EAAE;MAAEkE,IAAI,EAAExE,EAAE,CAACyU;IAAkB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAEtN;AACA,MAAMqS,OAAO,CAAC;EAeV3jB,WAAWA,CAACwW,OAAO,EAAE;IAAAtW,eAAA;IAAAA,eAAA;IAZrB;AACJ;AACA;IAFIA,eAAA,4BAGoB,IAAIxC,YAAY,CAAC,CAAC;IACtC;AACJ;AACA;IAFIwC,eAAA,2BAGmB,IAAIxC,YAAY,CAAC,CAAC;IAAAwC,eAAA,qBACxB,QAAQ;IAAAA,eAAA,iBACZ,KAAK;IAAAA,eAAA;IAAAA,eAAA;IAIV,IAAI,CAACsW,OAAO,GAAGA,OAAO;EAC1B;EACAoN,eAAeA,CAAA,EAAG;IACd;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMC,QAAQ,GAAG,IAAI,CAACrI,IAAI,CAAC3F,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC2F,IAAI,CAACrJ,KAAK,GAAGvL,SAAS;IACnE,IAAIid,QAAQ,EAAE;MACV,IAAI,CAACC,MAAM,GAAG,IAAI;MAClB,IAAI,CAACC,YAAY,CAACF,QAAQ,CAACG,GAAG,CAAC;MAC/B,IAAI,CAACzM,SAAS,CAAC,CAAC;IACpB;EACJ;EACA0M,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAC5B;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACD,iBAAiB,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;EACIE,iBAAiBA,CAAC;IAAE9O,YAAY;IAAEiC;EAAU,CAAC,EAAE;IAC3C,MAAM1C,OAAO,GAAGS,YAAY,CAACT,OAAO;IACpC,IAAI0C,SAAS,IAAI1C,OAAO,KAAKjO,SAAS,EAAE;MACpC,IAAI,CAACyd,iBAAiB,CAACjH,IAAI,CAAC;QAAE4G,GAAG,EAAEnP;MAAQ,CAAC,CAAC;IACjD;EACJ;EACA;AACJ;AACA;EACIyP,gBAAgBA,CAAC;IAAEhP,YAAY;IAAEiC;EAAU,CAAC,EAAE;IAC1C,MAAM1C,OAAO,GAAGS,YAAY,CAACT,OAAO;IACpC,IAAI0C,SAAS,IAAI1C,OAAO,KAAKjO,SAAS,EAAE;MACpC,IAAI,IAAI,CAAC2d,MAAM,EAAE;QACb,IAAI,CAACA,MAAM,CAACC,WAAW,GAAG3P,OAAO;MACrC;MACA,IAAI,CAAC4P,gBAAgB,CAACrH,IAAI,CAAC;QAAE4G,GAAG,EAAEnP;MAAQ,CAAC,CAAC;IAChD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI6P,MAAMA,CAACC,UAAU,EAAE;IACf,MAAMC,WAAW,GAAG,OAAOD,UAAU,KAAK,QAAQ;IAClD,MAAMX,GAAG,GAAGY,WAAW,GAAGD,UAAU,GAAGA,UAAU,CAACnd,MAAM,CAACwc,GAAG;IAC5D;AACR;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAACF,MAAM,EAAE;MACb,IAAI,CAACC,YAAY,CAACC,GAAG,CAAC;MACtB,IAAI,CAACzM,SAAS,CAAC,CAAC;MAChB;IACJ;IACA,MAAMsN,eAAe,GAAG,IAAI,CAAC3b,MAAM,CAACwP,gBAAgB,CAAC,CAAC,KAAKsL,GAAG;IAC9D,MAAMc,UAAU,GAAG,GAAG,IAAI,CAAC5b,MAAM,CAACoN,UAAU,IAAI0N,GAAG,EAAE;IACrD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACY,WAAW,EAAE;MACdD,UAAU,CAACI,eAAe,CAAC,CAAC;IAChC;IACA,IAAIF,eAAe,EAAE;MACjB,MAAMG,aAAa,GAAG,IAAI,CAAC9b,MAAM,CAACwP,gBAAgB,CAAC,CAAC;MACpD,MAAMpB,UAAU,GAAG,IAAI,CAACpO,MAAM,CAACwV,gBAAgB,CAACsG,aAAa,CAAC;MAC9D;MACA,IAAI,CAAA1N,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAElR,GAAG,MAAK0e,UAAU,EAAE;QAChC;MACJ;MACA,MAAMG,QAAQ,GAAG,IAAI,CAAC/b,MAAM,CAACyV,WAAW,CAACqF,GAAG,CAAC;MAC7C,MAAMkB,gBAAgB,GAAGD,QAAQ,IAAIH,UAAU,KAAKG,QAAQ,CAAC7e,GAAG,IAAI6e,QAAQ,CAAC9L,WAAW;MACxF,OAAO,IAAI,CAAC3C,OAAO,CAAC1N,YAAY,CAACgc,UAAU,EAAE;QACzC,GAAGI,gBAAgB;QACnBtjB,QAAQ,EAAE,IAAI;QACd+G,kBAAkB,EAAE;MACxB,CAAC,CAAC;IACN,CAAC,MACI;MACD,MAAMwc,SAAS,GAAG,IAAI,CAACjc,MAAM,CAACwV,gBAAgB,CAACsF,GAAG,CAAC;MACnD;AACZ;AACA;AACA;MACY,MAAM5d,GAAG,GAAG,CAAA+e,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE/e,GAAG,KAAI0e,UAAU;MACxC,MAAMI,gBAAgB,GAAGC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEhM,WAAW;MAC/C,OAAO,IAAI,CAAC3C,OAAO,CAAC1N,YAAY,CAAC1C,GAAG,EAAE;QAClC,GAAG8e,gBAAgB;QACnBtjB,QAAQ,EAAE,IAAI;QACd+G,kBAAkB,EAAE;MACxB,CAAC,CAAC;IACN;EACJ;EACAob,YAAYA,CAACC,GAAG,EAAE;IACd,MAAMxI,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMgJ,WAAW,GAAGhJ,IAAI,CAAC1E,IAAI,CAAEsO,CAAC,IAAKA,CAAC,CAACpB,GAAG,KAAKA,GAAG,CAAC;IACnD,IAAI,CAACQ,WAAW,EAAE;MACdtZ,OAAO,CAACwC,KAAK,CAAC,gCAAgCsW,GAAG,kBAAkB,CAAC;MACpE;IACJ;IACA,IAAI,CAACqB,UAAU,GAAG,IAAI,CAACb,WAAW;IAClC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACH,iBAAiB,CAACjH,IAAI,CAAC;MAAE4G;IAAI,CAAC,CAAC;IACpCQ,WAAW,CAACnd,EAAE,CAACoX,MAAM,GAAG,IAAI;EAChC;EACAlH,SAASA,CAAA,EAAG;IACR,MAAM;MAAEiN,WAAW;MAAEa;IAAW,CAAC,GAAG,IAAI;IACxC,IAAI,IAAI,CAACd,MAAM,IAAIC,WAAW,EAAE;MAC5B,IAAI,CAACD,MAAM,CAACC,WAAW,GAAGA,WAAW,CAACR,GAAG;IAC7C;IACA,IAAI,CAAAqB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAErB,GAAG,OAAKQ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAER,GAAG,GAAE;MACtC,IAAIqB,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEhe,EAAE,EAAE;QAChBge,UAAU,CAAChe,EAAE,CAACoX,MAAM,GAAG,KAAK;MAChC;IACJ;IACA,IAAI+F,WAAW,EAAE;MACb,IAAI,CAACC,gBAAgB,CAACrH,IAAI,CAAC;QAAE4G,GAAG,EAAEQ,WAAW,CAACR;MAAI,CAAC,CAAC;IACxD;EACJ;EACAsB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACxB,MAAM,EAAE;MAAA,IAAAyB,iBAAA;MACb,QAAAA,iBAAA,GAAO,IAAI,CAACf,WAAW,cAAAe,iBAAA,uBAAhBA,iBAAA,CAAkBvB,GAAG;IAChC;IACA,OAAO,IAAI,CAAC9a,MAAM,CAACwP,gBAAgB,CAAC,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwL,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACsB,OAAO,CAACxW,OAAO,CAAEuV,MAAM,IAAK;MAC7B;MACA,MAAMkB,WAAW,GAAGlB,MAAM,CAACld,EAAE,CAAC0a,YAAY,CAAC,MAAM,CAAC;MAClD,IAAI0D,WAAW,KAAK,IAAI,CAACC,UAAU,EAAE;QACjC,IAAI,CAACA,UAAU,GAAGD,WAAW;QAC7B,IAAI,CAACE,cAAc,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIA,cAAcA,CAAA,EAAG;IACb;AACR;AACA;AACA;AACA;IACQ,MAAMpB,MAAM,GAAG,IAAI,CAACA,MAAM,CAACld,EAAE;IAC7B,IAAI,IAAI,CAACqe,UAAU,KAAK,KAAK,EAAE;MAC3B;AACZ;AACA;AACA;MACY,IAAI,CAACE,SAAS,CAACnY,aAAa,CAACoY,MAAM,CAACtB,MAAM,CAAC;IAC/C,CAAC,MACI;MACD;AACZ;AACA;AACA;MACY,IAAI,CAACqB,SAAS,CAACnY,aAAa,CAACqY,KAAK,CAACvB,MAAM,CAAC;IAC9C;EACJ;EACA;AAEJ;AAACwB,QAAA,GAjNKpC,OAAO;AAAAzjB,eAAA,CAAPyjB,OAAO,wBAAAqC,iBAAA5jB,iBAAA;EAAA,YAAAA,iBAAA,IA+M8GuhB,QAAO,EA9sEhD9mB,EAAE,CAAAwU,iBAAA,CA8sEgE3J,aAAa;AAAA;AAC7J;AAAAxH,eAAA,CAhNEyjB,OAAO,8BA//DqE9mB,EAAE,CAAA0U,iBAAA;EAAAlQ,IAAA,EA+sE2BsiB,QAAO;EAAAnS,SAAA;EAAAyU,SAAA,WAAAC,eAAAvU,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA/sEpC9U,EAAE,CAAAspB,WAAA,CAAAC,GAAA,KA+sEgUjoB,UAAU;IAAA;IAAA,IAAAwT,EAAA;MAAA,IAAAI,EAAA;MA/sE5UlV,EAAE,CAAAmV,cAAA,CAAAD,EAAA,GAAFlV,EAAE,CAAAoV,WAAA,QAAAL,GAAA,CAAAgU,SAAA,GAAA7T,EAAA,CAAAI,KAAA;IAAA;EAAA;EAAA4O,YAAA,WAAAsF,sBAAA1U,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MAAF9U,EAAE,CAAAokB,UAAA,+BAAAqF,8CAAAnF,MAAA;QAAA,OA+sE2BvP,GAAA,CAAA8S,MAAA,CAAAvD,MAAa,CAAC;MAAA,CAAR,CAAC;IAAA;EAAA;EAAA9B,OAAA;IAAAgF,iBAAA;IAAAI,gBAAA;EAAA;EAAAnR,UAAA;AAAA;AAEtH;EAAA,QAAA5Q,SAAA,oBAAAA,SAAA,KAjtEkF7F,EAAE,CAAA8F,iBAAA,CAitEQghB,OAAO,EAAc,CAAC;IACtGtiB,IAAI,EAAE7D,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC2Q,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAElS,IAAI,EAAEqG;IAAc,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAEke,SAAS,EAAE,CAAC;MAC7FvkB,IAAI,EAAEjD,SAAS;MACfwE,IAAI,EAAE,CAAC,WAAW,EAAE;QAAEd,IAAI,EAAE3D,UAAU;QAAEqV,MAAM,EAAE;MAAK,CAAC;IAC1D,CAAC,CAAC;IAAE6Q,iBAAiB,EAAE,CAAC;MACpBhjB,IAAI,EAAErD;IACV,CAAC,CAAC;IAAEymB,gBAAgB,EAAE,CAAC;MACnBpjB,IAAI,EAAErD;IACV,CAAC,CAAC;IAAE0mB,MAAM,EAAE,CAAC;MACTrjB,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,mBAAmB,EAAE,CAAC,QAAQ,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2jB,GAAG,GAAIC,CAAC,IAAK;EACf,IAAI,OAAOC,oCAAoC,KAAK,UAAU,EAAE;IAC5D,OAAOA,oCAAoC,CAACD,CAAC,CAAC;EAClD;EACA,IAAI,OAAOvjB,qBAAqB,KAAK,UAAU,EAAE;IAC7C,OAAOA,qBAAqB,CAACujB,CAAC,CAAC;EACnC;EACA,OAAOE,UAAU,CAACF,CAAC,CAAC;AACxB,CAAC;;AAED;AACA,MAAMG,aAAa,CAAC;EAWhB3mB,WAAWA,CAACwL,QAAQ,EAAEkQ,UAAU,EAAE;IAAAxb,eAAA;IAAAA,eAAA;IAAAA,eAAA,mBARvB,MAAM;MACb;IAAA,CACH;IAAAA,eAAA,oBACW,MAAM;MACd;IAAA,CACH;IAAAA,eAAA;IAAAA,eAAA;IAIG,IAAI,CAACsL,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACkQ,UAAU,GAAGA,UAAU;EAChC;EACAkL,UAAUA,CAACrf,KAAK,EAAE;IACd,IAAI,CAACmU,UAAU,CAACjO,aAAa,CAAClG,KAAK,GAAG,IAAI,CAACsf,SAAS,GAAGtf,KAAK;IAC5Duf,eAAe,CAAC,IAAI,CAACpL,UAAU,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIqL,iBAAiBA,CAAC1f,EAAE,EAAEE,KAAK,EAAE;IACzB,IAAIF,EAAE,KAAK,IAAI,CAACqU,UAAU,CAACjO,aAAa,EAAE;MACtC,IAAIlG,KAAK,KAAK,IAAI,CAACsf,SAAS,EAAE;QAC1B,IAAI,CAACA,SAAS,GAAGtf,KAAK;QACtB,IAAI,CAACyf,QAAQ,CAACzf,KAAK,CAAC;MACxB;MACAuf,eAAe,CAAC,IAAI,CAACpL,UAAU,CAAC;IACpC;EACJ;EACAuL,gBAAgBA,CAAC5f,EAAE,EAAE;IACjB,IAAIA,EAAE,KAAK,IAAI,CAACqU,UAAU,CAACjO,aAAa,EAAE;MACtC,IAAI,CAACyZ,SAAS,CAAC,CAAC;MAChBJ,eAAe,CAAC,IAAI,CAACpL,UAAU,CAAC;IACpC;EACJ;EACAyL,gBAAgBA,CAAClY,EAAE,EAAE;IACjB,IAAI,CAAC+X,QAAQ,GAAG/X,EAAE;EACtB;EACAmY,iBAAiBA,CAACnY,EAAE,EAAE;IAClB,IAAI,CAACiY,SAAS,GAAGjY,EAAE;EACvB;EACAoY,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAC5L,UAAU,CAACjO,aAAa,CAAC4T,QAAQ,GAAGiG,UAAU;EACvD;EACAvL,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACwL,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC3H,WAAW,CAAC,CAAC;IACpC;EACJ;EACAgE,eAAeA,CAAA,EAAG;IACd,IAAI4D,SAAS;IACb,IAAI;MACAA,SAAS,GAAG,IAAI,CAAChc,QAAQ,CAAC5K,GAAG,CAACd,SAAS,CAAC;IAC5C,CAAC,CACD,MAAM;MACF;IAAA;IAEJ,IAAI,CAAC0nB,SAAS,EAAE;MACZ;IACJ;IACA;IACA,IAAIA,SAAS,CAACD,aAAa,EAAE;MACzB,IAAI,CAACA,aAAa,GAAGC,SAAS,CAACD,aAAa,CAAC3jB,SAAS,CAAC,MAAMkjB,eAAe,CAAC,IAAI,CAACpL,UAAU,CAAC,CAAC;IAClG;IACA;AACR;AACA;AACA;IACQ,MAAM+L,WAAW,GAAGD,SAAS,CAACE,OAAO;IACrC,IAAID,WAAW,EAAE;MACb,MAAME,cAAc,GAAG,CAAC,eAAe,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,aAAa,EAAE,gBAAgB,CAAC;MAChHA,cAAc,CAAC3Y,OAAO,CAAE4Y,MAAM,IAAK;QAC/B,IAAI,OAAOH,WAAW,CAACG,MAAM,CAAC,KAAK,WAAW,EAAE;UAC5C,MAAMC,KAAK,GAAGJ,WAAW,CAACG,MAAM,CAAC,CAACE,IAAI,CAACL,WAAW,CAAC;UACnDA,WAAW,CAACG,MAAM,CAAC,GAAG,CAAC,GAAGvb,MAAM,KAAK;YACjCwb,KAAK,CAAC,GAAGxb,MAAM,CAAC;YAChBya,eAAe,CAAC,IAAI,CAACpL,UAAU,CAAC;UACpC,CAAC;QACL;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AAEJ;AAACqM,cAAA,GAhGKpB,aAAa;AAAAzmB,eAAA,CAAbymB,aAAa,wBAAAqB,uBAAA5lB,iBAAA;EAAA,YAAAA,iBAAA,IA8FwGukB,cAAa,EA30EtD9pB,EAAE,CAAAwU,iBAAA,CA20EsExU,EAAE,CAACQ,QAAQ,GA30EnFR,EAAE,CAAAwU,iBAAA,CA20E8FxU,EAAE,CAACsB,UAAU;AAAA;AAC3L;AAAA+B,eAAA,CA/FEymB,aAAa,8BA7uE+D9pB,EAAE,CAAA0U,iBAAA;EAAAlQ,IAAA,EA40E2BslB,cAAa;EAAA5F,YAAA,WAAAkH,4BAAAtW,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MA50E1C9U,EAAE,CAAAokB,UAAA,qBAAAiH,0CAAA/G,MAAA;QAAA,OA40E2BvP,GAAA,CAAAqV,gBAAA,CAAA9F,MAAA,CAAAgH,MAA8B,CAAC;MAAA,CAAnB,CAAC;IAAA;EAAA;EAAA7U,UAAA;AAAA;AAE5H;EAAA,QAAA5Q,SAAA,oBAAAA,SAAA,KA90EkF7F,EAAE,CAAA8F,iBAAA,CA80EQgkB,aAAa,EAAc,CAAC;IAC5GtlB,IAAI,EAAE7D;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE6D,IAAI,EAAExE,EAAE,CAACQ;IAAS,CAAC,EAAE;MAAEgE,IAAI,EAAExE,EAAE,CAACsB;IAAW,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE8oB,gBAAgB,EAAE,CAAC;MAC3H5lB,IAAI,EAAEnD,YAAY;MAClB0E,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,eAAe,CAAC;IACvC,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMkkB,eAAe,GAAInY,OAAO,IAAK;EACjC4X,GAAG,CAAC,MAAM;IACN,MAAM6B,KAAK,GAAGzZ,OAAO,CAAClB,aAAa;IACnC,MAAM4a,QAAQ,GAAGD,KAAK,CAAC7gB,KAAK,IAAI,IAAI,IAAI6gB,KAAK,CAAC7gB,KAAK,CAACuC,QAAQ,CAAC,CAAC,CAAC+L,MAAM,GAAG,CAAC;IACzE,MAAMyS,OAAO,GAAGC,UAAU,CAACH,KAAK,CAAC;IACjCI,UAAU,CAACJ,KAAK,EAAEE,OAAO,CAAC;IAC1B,MAAMzY,IAAI,GAAGuY,KAAK,CAACK,OAAO,CAAC,UAAU,CAAC;IACtC,IAAI5Y,IAAI,EAAE;MACN,IAAIwY,QAAQ,EAAE;QACVG,UAAU,CAAC3Y,IAAI,EAAE,CAAC,GAAGyY,OAAO,EAAE,gBAAgB,CAAC,CAAC;MACpD,CAAC,MACI;QACDE,UAAU,CAAC3Y,IAAI,EAAEyY,OAAO,CAAC;MAC7B;IACJ;EACJ,CAAC,CAAC;AACN,CAAC;AACD,MAAMC,UAAU,GAAI5Z,OAAO,IAAK;EAC5B,MAAMP,SAAS,GAAGO,OAAO,CAACP,SAAS;EACnC,MAAMka,OAAO,GAAG,EAAE;EAClB,KAAK,IAAI1S,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxH,SAAS,CAACyH,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,MAAM/F,IAAI,GAAGzB,SAAS,CAACyB,IAAI,CAAC+F,CAAC,CAAC;IAC9B,IAAI/F,IAAI,KAAK,IAAI,IAAI6Y,UAAU,CAAC7Y,IAAI,EAAE,KAAK,CAAC,EAAE;MAC1CyY,OAAO,CAACxT,IAAI,CAAC,OAAOjF,IAAI,CAAC8Y,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;IAC5C;EACJ;EACA,OAAOL,OAAO;AAClB,CAAC;AACD,MAAME,UAAU,GAAGA,CAAC7Z,OAAO,EAAE2Z,OAAO,KAAK;EACrC,MAAMla,SAAS,GAAGO,OAAO,CAACP,SAAS;EACnCA,SAAS,CAACwa,MAAM,CAAC,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,WAAW,EAAE,cAAc,CAAC;EACzGxa,SAAS,CAACC,GAAG,CAAC,GAAGia,OAAO,CAAC;AAC7B,CAAC;AACD,MAAMI,UAAU,GAAGA,CAACN,KAAK,EAAES,MAAM,KAAK;EAClC,OAAOT,KAAK,CAACO,SAAS,CAAC,CAAC,EAAEE,MAAM,CAAChT,MAAM,CAAC,KAAKgT,MAAM;AACvD,CAAC;;AAED;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrB;AACJ;AACA;EACIC,YAAYA,CAACC,MAAM,EAAE;IACjB,OAAO,KAAK;EAChB;EACA;AACJ;AACA;EACIC,YAAYA,CAACD,MAAM,EAAE;IACjB,OAAO,KAAK;EAChB;EACA;AACJ;AACA;EACIE,KAAKA,CAACF,MAAM,EAAEG,aAAa,EAAE;IACzB;EACJ;EACA;AACJ;AACA;EACIC,QAAQA,CAACJ,MAAM,EAAE;IACb,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIK,gBAAgBA,CAACC,MAAM,EAAEC,IAAI,EAAE;IAC3B,IAAID,MAAM,CAACrL,WAAW,KAAKsL,IAAI,CAACtL,WAAW,EAAE;MACzC,OAAO,KAAK;IAChB;IACA;IACA,MAAMuL,YAAY,GAAGF,MAAM,CAACjd,MAAM;IAClC,MAAMod,aAAa,GAAGF,IAAI,CAACld,MAAM;IACjC,MAAMqd,KAAK,GAAGzb,MAAM,CAAC0b,IAAI,CAACH,YAAY,CAAC;IACvC,MAAMI,KAAK,GAAG3b,MAAM,CAAC0b,IAAI,CAACF,aAAa,CAAC;IACxC,IAAIC,KAAK,CAAC7T,MAAM,KAAK+T,KAAK,CAAC/T,MAAM,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA;IACA,KAAK,MAAM1Q,GAAG,IAAIukB,KAAK,EAAE;MACrB,IAAID,aAAa,CAACtkB,GAAG,CAAC,KAAKqkB,YAAY,CAACrkB,GAAG,CAAC,EAAE;QAC1C,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf;AACJ;;AAEA;AACA,MAAM0kB,qBAAqB,CAAC;EAExB7pB,WAAWA,CAAC8pB,IAAI,EAAE;IAAA5pB,eAAA;IACd,IAAI,CAAC4pB,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;EACIxe,MAAMA,CAACmF,IAAI,EAAE;IACT,OAAO,IAAI,CAACqZ,IAAI,CAACxe,MAAM,CAAEmF,IAAI,IAAI,CAAC,CAAE,CAAC;EACzC;EACA;AACJ;AACA;EACIsZ,OAAOA,CAAC9e,IAAI,EAAE+e,IAAI,EAAE/hB,EAAE,EAAE;IACpB,OAAO,IAAI,CAAC6hB,IAAI,CAACC,OAAO,CAAC9e,IAAI,EAAE+e,IAAI,EAAE/hB,EAAE,CAAC;EAC5C;EACA;AACJ;AACA;EACIgiB,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACH,IAAI,CAACG,MAAM,CAAC,CAAC;EAC7B;AACJ;;AAEA;AACA;AACA;;AAEA,SAAS5e,eAAe,EAAEf,MAAM,EAAEQ,WAAW,EAAEjJ,aAAa,EAAEye,aAAa,EAAE3M,QAAQ,EAAEyP,MAAM,EAAErS,UAAU,EAAE6J,eAAe,EAAE+I,OAAO,EAAEmF,kBAAkB,EAAE/oB,cAAc,EAAE2H,aAAa,EAAEsD,SAAS,EAAE6e,qBAAqB,EAAE3mB,QAAQ,EAAEsN,QAAQ,EAAEgR,2BAA2B,EAAEoB,mCAAmC,EAAE+D,aAAa,EAAErY,mBAAmB,EAAE4R,4BAA4B,EAAEqG,GAAG,EAAEO,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}