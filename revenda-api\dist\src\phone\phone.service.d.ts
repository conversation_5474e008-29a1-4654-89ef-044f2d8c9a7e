import { Repository } from 'typeorm';
import { Phone } from './phone.entity';
import { CreatePhoneDto } from './dto/create-phone.dto';
import { UpdatePhoneDto } from './dto/update-phone.dto';
export declare class PhoneService {
    private phoneRepository;
    constructor(phoneRepository: Repository<Phone>);
    create(createPhoneDto: CreatePhoneDto): Promise<Phone>;
    findAll(): Promise<Phone[]>;
    findOne(id: number): Promise<Phone | null>;
    findByBrand(brandId: number): Promise<Phone[]>;
    update(id: number, updatePhoneDto: UpdatePhoneDto): Promise<import("typeorm").UpdateResult>;
    remove(id: number): Promise<import("typeorm").DeleteResult>;
}
