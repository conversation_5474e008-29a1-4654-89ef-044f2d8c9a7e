{"ast": null, "code": "function getContentEditableSelection(element) {\n  const {\n    anchorOffset = 0,\n    focusOffset = 0\n  } = element.ownerDocument.getSelection() || {};\n  const from = Math.min(anchorOffset, focusOffset);\n  const to = Math.max(anchorOffset, focusOffset);\n  return [from, to];\n}\nfunction setContentEditableSelection(element, [from, to]) {\n  var _a, _b, _c, _d;\n  const document = element.ownerDocument;\n  const range = document.createRange();\n  range.setStart(element.firstChild || element, Math.min(from, (_b = (_a = element.textContent) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0));\n  range.setEnd(element.lastChild || element, Math.min(to, (_d = (_c = element.textContent) === null || _c === void 0 ? void 0 : _c.length) !== null && _d !== void 0 ? _d : 0));\n  const selection = document.getSelection();\n  if (selection) {\n    selection.removeAllRanges();\n    selection.addRange(range);\n  }\n}\nclass ContentEditableAdapter {\n  constructor(element) {\n    this.element = element;\n    this.maxLength = Infinity;\n  }\n  get value() {\n    return this.element.innerText.replace(/\\n\\n$/, '\\n');\n  }\n  set value(value) {\n    // Setting into innerHTML of element with `white-space: pre;` style\n    this.element.innerHTML = value.replace(/\\n$/, '\\n\\n');\n  }\n  get selectionStart() {\n    return getContentEditableSelection(this.element)[0];\n  }\n  get selectionEnd() {\n    return getContentEditableSelection(this.element)[1];\n  }\n  setSelectionRange(from, to) {\n    setContentEditableSelection(this.element, [from !== null && from !== void 0 ? from : 0, to !== null && to !== void 0 ? to : 0]);\n  }\n  select() {\n    this.setSelectionRange(0, this.value.length);\n  }\n}\nfunction maskitoAdaptContentEditable(element) {\n  const adapter = new ContentEditableAdapter(element);\n  return new Proxy(element, {\n    get(target, prop) {\n      if (prop in adapter) {\n        return adapter[prop];\n      }\n      const nativeProperty = target[prop];\n      return typeof nativeProperty === 'function' ? nativeProperty.bind(target) : nativeProperty;\n    },\n    // eslint-disable-next-line @typescript-eslint/max-params\n    set(target, prop, val, receiver) {\n      return Reflect.set(prop in adapter ? adapter : target, prop, val, receiver);\n    }\n  });\n}\nconst MASKITO_DEFAULT_ELEMENT_PREDICATE = e => e.isContentEditable ? maskitoAdaptContentEditable(e) : e.querySelector('input,textarea') || e;\nconst MASKITO_DEFAULT_OPTIONS = {\n  mask: /^.*$/,\n  preprocessors: [],\n  postprocessors: [],\n  plugins: [],\n  overwriteMode: 'shift'\n};\nclass MaskHistory {\n  constructor() {\n    this.now = null;\n    this.past = [];\n    this.future = [];\n  }\n  undo() {\n    const state = this.past.pop();\n    if (state && this.now) {\n      this.future.push(this.now);\n      this.updateElement(state, 'historyUndo');\n    }\n  }\n  redo() {\n    const state = this.future.pop();\n    if (state && this.now) {\n      this.past.push(this.now);\n      this.updateElement(state, 'historyRedo');\n    }\n  }\n  updateHistory(state) {\n    if (!this.now) {\n      this.now = state;\n      return;\n    }\n    const isValueChanged = this.now.value !== state.value;\n    const isSelectionChanged = this.now.selection.some((item, index) => item !== state.selection[index]);\n    if (!isValueChanged && !isSelectionChanged) {\n      return;\n    }\n    if (isValueChanged) {\n      this.past.push(this.now);\n      this.future = [];\n    }\n    this.now = state;\n  }\n  updateElement(state, inputType) {\n    this.now = state;\n    this.updateElementState(state, {\n      inputType,\n      data: null\n    });\n  }\n}\nfunction areElementValuesEqual(sampleState, ...states) {\n  return states.every(({\n    value\n  }) => value === sampleState.value);\n}\nfunction areElementStatesEqual(sampleState, ...states) {\n  return states.every(({\n    value,\n    selection\n  }) => value === sampleState.value && selection[0] === sampleState.selection[0] && selection[1] === sampleState.selection[1]);\n}\nfunction applyOverwriteMode({\n  value,\n  selection\n}, newCharacters, mode) {\n  const [from, to] = selection;\n  const computedMode = typeof mode === 'function' ? mode({\n    value,\n    selection\n  }) : mode;\n  return {\n    value,\n    selection: computedMode === 'replace' ? [from, Math.max(from + newCharacters.length, to)] : [from, to]\n  };\n}\nfunction isFixedCharacter(char) {\n  return typeof char === 'string';\n}\n\n// eslint-disable-next-line @typescript-eslint/max-params\nfunction getLeadingFixedCharacters(mask, validatedValuePart, newCharacter, initialElementState) {\n  let leadingFixedCharacters = '';\n  for (let i = validatedValuePart.length; i < mask.length; i++) {\n    const charConstraint = mask[i] || '';\n    const isInitiallyExisted = (initialElementState === null || initialElementState === void 0 ? void 0 : initialElementState.value[i]) === charConstraint;\n    if (!isFixedCharacter(charConstraint) || charConstraint === newCharacter && !isInitiallyExisted) {\n      return leadingFixedCharacters;\n    }\n    leadingFixedCharacters += charConstraint;\n  }\n  return leadingFixedCharacters;\n}\nfunction validateValueWithMask(value, maskExpression) {\n  if (Array.isArray(maskExpression)) {\n    return value.length === maskExpression.length && Array.from(value).every((char, i) => {\n      const charConstraint = maskExpression[i] || '';\n      return isFixedCharacter(charConstraint) ? char === charConstraint : char.match(charConstraint);\n    });\n  }\n  return maskExpression.test(value);\n}\nfunction guessValidValueByPattern(elementState, mask, initialElementState) {\n  let maskedFrom = null;\n  let maskedTo = null;\n  const maskedValue = Array.from(elementState.value).reduce((validatedCharacters, char, charIndex) => {\n    const leadingCharacters = getLeadingFixedCharacters(mask, validatedCharacters, char, initialElementState);\n    const newValidatedChars = validatedCharacters + leadingCharacters;\n    const charConstraint = mask[newValidatedChars.length] || '';\n    if (maskedFrom === null && charIndex >= elementState.selection[0]) {\n      maskedFrom = newValidatedChars.length;\n    }\n    if (maskedTo === null && charIndex >= elementState.selection[1]) {\n      maskedTo = newValidatedChars.length;\n    }\n    if (isFixedCharacter(charConstraint)) {\n      return newValidatedChars + charConstraint;\n    }\n    return char.match(charConstraint) ? newValidatedChars + char : newValidatedChars;\n  }, '');\n  const trailingFixedCharacters = getLeadingFixedCharacters(mask, maskedValue, '', initialElementState);\n  return {\n    value: validateValueWithMask(maskedValue + trailingFixedCharacters, mask) ? maskedValue + trailingFixedCharacters : maskedValue,\n    selection: [maskedFrom !== null && maskedFrom !== void 0 ? maskedFrom : maskedValue.length, maskedTo !== null && maskedTo !== void 0 ? maskedTo : maskedValue.length]\n  };\n}\nfunction guessValidValueByRegExp({\n  value,\n  selection\n}, maskRegExp) {\n  const [from, to] = selection;\n  let newFrom = from;\n  let newTo = to;\n  const validatedValue = Array.from(value).reduce((validatedValuePart, char, i) => {\n    const newPossibleValue = validatedValuePart + char;\n    if (from === i) {\n      newFrom = validatedValuePart.length;\n    }\n    if (to === i) {\n      newTo = validatedValuePart.length;\n    }\n    return newPossibleValue.match(maskRegExp) ? newPossibleValue : validatedValuePart;\n  }, '');\n  return {\n    value: validatedValue,\n    selection: [Math.min(newFrom, validatedValue.length), Math.min(newTo, validatedValue.length)]\n  };\n}\nfunction calibrateValueByMask(elementState, mask, initialElementState = null) {\n  if (validateValueWithMask(elementState.value, mask)) {\n    return elementState;\n  }\n  const {\n    value,\n    selection\n  } = Array.isArray(mask) ? guessValidValueByPattern(elementState, mask, initialElementState) : guessValidValueByRegExp(elementState, mask);\n  return {\n    selection,\n    value: Array.isArray(mask) ? value.slice(0, mask.length) : value\n  };\n}\nfunction removeFixedMaskCharacters(initialElementState, mask) {\n  if (!Array.isArray(mask)) {\n    return initialElementState;\n  }\n  const [from, to] = initialElementState.selection;\n  const selection = [];\n  const unmaskedValue = Array.from(initialElementState.value).reduce((rawValue, char, i) => {\n    const charConstraint = mask[i] || '';\n    if (i === from) {\n      selection.push(rawValue.length);\n    }\n    if (i === to) {\n      selection.push(rawValue.length);\n    }\n    return isFixedCharacter(charConstraint) && charConstraint === char ? rawValue : rawValue + char;\n  }, '');\n  if (selection.length < 2) {\n    selection.push(...new Array(2 - selection.length).fill(unmaskedValue.length));\n  }\n  return {\n    value: unmaskedValue,\n    selection: [selection[0], selection[1]]\n  };\n}\nclass MaskModel {\n  constructor(initialElementState, maskOptions) {\n    this.initialElementState = initialElementState;\n    this.maskOptions = maskOptions;\n    this.value = '';\n    this.selection = [0, 0];\n    const {\n      value,\n      selection\n    } = calibrateValueByMask(this.initialElementState, this.getMaskExpression(this.initialElementState));\n    this.value = value;\n    this.selection = selection;\n  }\n  addCharacters([from, to], newCharacters) {\n    const {\n      value,\n      maskOptions\n    } = this;\n    const maskExpression = this.getMaskExpression({\n      value: value.slice(0, from) + newCharacters + value.slice(to),\n      selection: [from + newCharacters.length, from + newCharacters.length]\n    });\n    const initialElementState = {\n      value,\n      selection: [from, to]\n    };\n    const unmaskedElementState = removeFixedMaskCharacters(initialElementState, maskExpression);\n    const [unmaskedFrom, unmaskedTo] = applyOverwriteMode(unmaskedElementState, newCharacters, maskOptions.overwriteMode).selection;\n    const newUnmaskedLeadingValuePart = unmaskedElementState.value.slice(0, unmaskedFrom) + newCharacters;\n    const newCaretIndex = newUnmaskedLeadingValuePart.length;\n    const maskedElementState = calibrateValueByMask({\n      value: newUnmaskedLeadingValuePart + unmaskedElementState.value.slice(unmaskedTo),\n      selection: [newCaretIndex, newCaretIndex]\n    }, maskExpression, initialElementState);\n    const isInvalidCharsInsertion = value.slice(0, from) === calibrateValueByMask({\n      value: newUnmaskedLeadingValuePart,\n      selection: [newCaretIndex, newCaretIndex]\n    }, maskExpression, initialElementState).value;\n    if (isInvalidCharsInsertion || areElementStatesEqual(this, maskedElementState) // If typing new characters does not change value\n    ) {\n      throw new Error('Invalid mask value');\n    }\n    this.value = maskedElementState.value;\n    this.selection = maskedElementState.selection;\n  }\n  deleteCharacters([from, to]) {\n    if (from === to || !to) {\n      return;\n    }\n    const {\n      value\n    } = this;\n    const maskExpression = this.getMaskExpression({\n      value: value.slice(0, from) + value.slice(to),\n      selection: [from, from]\n    });\n    const initialElementState = {\n      value,\n      selection: [from, to]\n    };\n    const unmaskedElementState = removeFixedMaskCharacters(initialElementState, maskExpression);\n    const [unmaskedFrom, unmaskedTo] = unmaskedElementState.selection;\n    const newUnmaskedValue = unmaskedElementState.value.slice(0, unmaskedFrom) + unmaskedElementState.value.slice(unmaskedTo);\n    const maskedElementState = calibrateValueByMask({\n      value: newUnmaskedValue,\n      selection: [unmaskedFrom, unmaskedFrom]\n    }, maskExpression, initialElementState);\n    this.value = maskedElementState.value;\n    this.selection = maskedElementState.selection;\n  }\n  getMaskExpression(elementState) {\n    const {\n      mask\n    } = this.maskOptions;\n    return typeof mask === 'function' ? mask(elementState) : mask;\n  }\n}\nfunction maskitoChangeEventPlugin() {\n  return element => {\n    if (element.isContentEditable) {\n      return;\n    }\n    let value = element.value;\n    const valueListener = () => {\n      value = element.value;\n    };\n    const blurListener = () => {\n      if (element.value !== value) {\n        element.dispatchEvent(new Event('change', {\n          bubbles: true\n        }));\n      }\n    };\n    element.addEventListener('focus', valueListener);\n    element.addEventListener('change', valueListener);\n    element.addEventListener('blur', blurListener);\n    return () => {\n      element.removeEventListener('focus', valueListener);\n      element.removeEventListener('change', valueListener);\n      element.removeEventListener('blur', blurListener);\n    };\n  };\n}\nclass EventListener {\n  constructor(element) {\n    this.element = element;\n    this.listeners = [];\n  }\n  listen(eventType, fn, options) {\n    const untypedFn = fn;\n    this.element.addEventListener(eventType, untypedFn, options);\n    this.listeners.push(() => this.element.removeEventListener(eventType, untypedFn, options));\n  }\n  destroy() {\n    this.listeners.forEach(stopListen => stopListen());\n  }\n}\nconst HotkeyModifier = {\n  CTRL: 1 << 0,\n  ALT: 1 << 1,\n  SHIFT: 1 << 2,\n  META: 1 << 3\n};\n// TODO add variants that can be processed correctly\nconst HotkeyCode = {\n  Y: 89,\n  Z: 90\n};\n/**\n * Checks if the passed keyboard event match the required hotkey.\n *\n * @example\n * input.addEventListener('keydown', (event) => {\n *     if (isHotkey(event, HotkeyModifier.CTRL | HotkeyModifier.SHIFT, HotkeyCode.Z)) {\n *         // redo hotkey pressed\n *     }\n * })\n *\n * @return will return `true` only if the {@link HotkeyCode} matches and only the necessary\n * {@link HotkeyModifier modifiers} have been pressed\n */\nfunction isHotkey(event, modifiers, hotkeyCode) {\n  return event.ctrlKey === !!(modifiers & HotkeyModifier.CTRL) && event.altKey === !!(modifiers & HotkeyModifier.ALT) && event.shiftKey === !!(modifiers & HotkeyModifier.SHIFT) && event.metaKey === !!(modifiers & HotkeyModifier.META) &&\n  /**\n   * We intentionally use legacy {@link KeyboardEvent#keyCode `keyCode`} property. It is more\n   * \"keyboard-layout\"-independent than {@link KeyboardEvent#key `key`} or {@link KeyboardEvent#code `code`} properties.\n   * @see {@link https://github.com/taiga-family/maskito/issues/315 `KeyboardEvent#code` issue}\n   */\n  event.keyCode === hotkeyCode;\n}\nfunction isRedo(event) {\n  return isHotkey(event, HotkeyModifier.CTRL, HotkeyCode.Y) ||\n  // Windows\n  isHotkey(event, HotkeyModifier.CTRL | HotkeyModifier.SHIFT, HotkeyCode.Z) ||\n  // Windows & Android\n  isHotkey(event, HotkeyModifier.META | HotkeyModifier.SHIFT, HotkeyCode.Z) // macOS & iOS\n  ;\n}\nfunction isUndo(event) {\n  return isHotkey(event, HotkeyModifier.CTRL, HotkeyCode.Z) ||\n  // Windows & Android\n  isHotkey(event, HotkeyModifier.META, HotkeyCode.Z) // macOS & iOS\n  ;\n}\n\n/**\n * Sets value to element, and dispatches input event\n * if you passed ELementState, it also sets selection range\n *\n * @example\n * maskitoUpdateElement(input, newValue);\n * maskitoUpdateElement(input, elementState);\n *\n * @see {@link https://github.com/taiga-family/maskito/issues/804 issue}\n *\n * @return void\n */\nfunction maskitoUpdateElement(element, valueOrElementState) {\n  var _a;\n  const initialValue = element.value;\n  if (typeof valueOrElementState === 'string') {\n    element.value = valueOrElementState;\n  } else {\n    const [from, to] = valueOrElementState.selection;\n    element.value = valueOrElementState.value;\n    if (element.matches(':focus')) {\n      (_a = element.setSelectionRange) === null || _a === void 0 ? void 0 : _a.call(element, from, to);\n    }\n  }\n  if (element.value !== initialValue) {\n    element.dispatchEvent(new Event('input',\n    /**\n     * React handles this event only on bubbling phase\n     *\n     * here is the list of events that are processed in the capture stage, others are processed in the bubbling stage\n     * https://github.com/facebook/react/blob/cb2439624f43c510007f65aea5c50a8bb97917e4/packages/react-dom-bindings/src/events/DOMPluginEventSystem.js#L222\n     */\n    {\n      bubbles: true\n    }));\n  }\n}\nfunction getLineSelection({\n  value,\n  selection\n}, isForward) {\n  const [from, to] = selection;\n  if (from !== to) {\n    return [from, to];\n  }\n  const nearestBreak = isForward ? value.slice(from).indexOf('\\n') + 1 || value.length : value.slice(0, to).lastIndexOf('\\n') + 1;\n  const selectFrom = isForward ? from : nearestBreak;\n  const selectTo = isForward ? nearestBreak : to;\n  return [selectFrom, selectTo];\n}\nfunction getNotEmptySelection({\n  value,\n  selection\n}, isForward) {\n  const [from, to] = selection;\n  if (from !== to) {\n    return [from, to];\n  }\n  const notEmptySelection = isForward ? [from, to + 1] : [from - 1, to];\n  return notEmptySelection.map(x => Math.min(Math.max(x, 0), value.length));\n}\nconst TRAILING_SPACES_REG = /\\s+$/g;\nconst LEADING_SPACES_REG = /^\\s+/g;\nconst SPACE_REG = /\\s/;\nfunction getWordSelection({\n  value,\n  selection\n}, isForward) {\n  const [from, to] = selection;\n  if (from !== to) {\n    return [from, to];\n  }\n  if (isForward) {\n    const valueAfterSelectionStart = value.slice(from);\n    const [leadingSpaces] = valueAfterSelectionStart.match(LEADING_SPACES_REG) || [''];\n    const nearestWordEndIndex = valueAfterSelectionStart.trimStart().search(SPACE_REG);\n    return [from, nearestWordEndIndex !== -1 ? from + leadingSpaces.length + nearestWordEndIndex : value.length];\n  }\n  const valueBeforeSelectionEnd = value.slice(0, to);\n  const [trailingSpaces] = valueBeforeSelectionEnd.match(TRAILING_SPACES_REG) || [''];\n  const selectedWordLength = valueBeforeSelectionEnd.trimEnd().split('').reverse().findIndex(char => SPACE_REG.exec(char));\n  return [selectedWordLength !== -1 ? to - trailingSpaces.length - selectedWordLength : 0, to];\n}\n\n/* eslint-disable @typescript-eslint/no-restricted-types */\n/**\n * @internal\n */\nfunction maskitoPipe(processors = []) {\n  return (initialData, ...readonlyArgs) => processors.reduce((data, fn) => Object.assign(Object.assign({}, data), fn(data, ...readonlyArgs)), initialData);\n}\nfunction maskitoTransform(valueOrState, maskitoOptions) {\n  const options = Object.assign(Object.assign({}, MASKITO_DEFAULT_OPTIONS), maskitoOptions);\n  const preprocessor = maskitoPipe(options.preprocessors);\n  const postprocessor = maskitoPipe(options.postprocessors);\n  const initialElementState = typeof valueOrState === 'string' ? {\n    value: valueOrState,\n    selection: [0, 0]\n  } : valueOrState;\n  const {\n    elementState\n  } = preprocessor({\n    elementState: initialElementState,\n    data: ''\n  }, 'validation');\n  const maskModel = new MaskModel(elementState, options);\n  const {\n    value,\n    selection\n  } = postprocessor(maskModel, initialElementState);\n  return typeof valueOrState === 'string' ? value : {\n    value,\n    selection\n  };\n}\nconst SPACE = ' ';\n/**\n * 1. Android user (with G-board keyboard or similar) presses 1st space\n * ```\n * {type: \"beforeinput\", data: \" \", inputType: \"insertText\"}\n * ```\n * 2. User presses 2nd space\n * ```\n * // Android tries to delete previously inserted space\n * {type: \"beforeinput\", inputType: \"deleteContentBackward\"}\n * {type: \"beforeinput\", data: \". \", inputType: \"insertText\"}\n * ```\n * ---------\n * 1. MacOS user presses 1st space\n * ```\n * {type: \"beforeinput\", data: \" \", inputType: \"insertText\"}\n * ```\n * 2. User presses 2nd space\n * ```\n * // MacOS automatically run `element.setSelectionRange(indexBeforeSpace, indexAfterSpace)` and then\n * {type: \"beforeinput\", data: \". \", inputType: \"insertText\"}\n * ```\n * ---------\n * @see https://github.com/taiga-family/maskito/issues/2023\n */\nfunction createDoubleSpacePlugin() {\n  let prevValue = '';\n  let prevCaretIndex = 0;\n  let prevEvent = null;\n  let prevRejectedSpace = false;\n  return element => {\n    const eventListener = new EventListener(element);\n    eventListener.listen('beforeinput', event => {\n      var _a, _b;\n      const {\n        value,\n        selectionStart,\n        selectionEnd\n      } = element;\n      const rejectedSpace = (prevEvent === null || prevEvent === void 0 ? void 0 : prevEvent.inputType) === 'insertText' && (prevEvent === null || prevEvent === void 0 ? void 0 : prevEvent.data) === SPACE && !value.slice(0, Number(selectionEnd)).endsWith(SPACE);\n      if (event.inputType === 'insertText' && event.data === `.${SPACE}`) {\n        if ((prevEvent === null || prevEvent === void 0 ? void 0 : prevEvent.inputType) === 'deleteContentBackward' && prevRejectedSpace) {\n          // Android\n          element.value = prevValue;\n          (_a = element.setSelectionRange) === null || _a === void 0 ? void 0 : _a.call(element, prevCaretIndex, prevCaretIndex);\n        } else if (rejectedSpace) {\n          // Mac OS\n          (_b = element.setSelectionRange) === null || _b === void 0 ? void 0 : _b.call(element, selectionStart, selectionStart);\n        }\n      }\n      prevRejectedSpace = rejectedSpace;\n      prevEvent = event;\n      prevValue = value;\n      prevCaretIndex = Number((rejectedSpace ? prevCaretIndex : selectionEnd) === value.length ? selectionEnd : selectionStart);\n    });\n    return () => eventListener.destroy();\n  };\n}\nfunction maskitoInitialCalibrationPlugin(customOptions) {\n  return (element, options) => {\n    var _a, _b;\n    const from = (_a = element.selectionStart) !== null && _a !== void 0 ? _a : 0;\n    const to = (_b = element.selectionEnd) !== null && _b !== void 0 ? _b : 0;\n    maskitoUpdateElement(element, {\n      value: maskitoTransform(element.value, customOptions || options),\n      selection: [from, to]\n    });\n  };\n}\nfunction maskitoStrictCompositionPlugin() {\n  return (element, maskitoOptions) => {\n    const listener = event => {\n      var _a, _b;\n      if (event.inputType !== 'insertCompositionText') {\n        return;\n      }\n      const selection = [(_a = element.selectionStart) !== null && _a !== void 0 ? _a : 0, (_b = element.selectionEnd) !== null && _b !== void 0 ? _b : 0];\n      const elementState = {\n        selection,\n        value: element.value\n      };\n      const validatedState = maskitoTransform(elementState, maskitoOptions);\n      if (!areElementStatesEqual(elementState, validatedState)) {\n        event.preventDefault();\n        maskitoUpdateElement(element, validatedState);\n      }\n    };\n    element.addEventListener('input', listener);\n    return () => element.removeEventListener('input', listener);\n  };\n}\nconst BUILT_IN_PLUGINS = [createDoubleSpacePlugin()];\nclass Maskito extends MaskHistory {\n  constructor(element, maskitoOptions) {\n    super();\n    this.element = element;\n    this.maskitoOptions = maskitoOptions;\n    this.isTextArea = this.element.nodeName === 'TEXTAREA';\n    this.eventListener = new EventListener(this.element);\n    this.options = Object.assign(Object.assign({}, MASKITO_DEFAULT_OPTIONS), this.maskitoOptions);\n    this.upcomingElementState = null;\n    this.preprocessor = maskitoPipe(this.options.preprocessors);\n    this.postprocessor = maskitoPipe(this.options.postprocessors);\n    this.teardowns = this.options.plugins.concat(BUILT_IN_PLUGINS).map(plugin => plugin(this.element, this.options));\n    this.updateHistory(this.elementState);\n    this.eventListener.listen('keydown', event => {\n      if (isRedo(event)) {\n        event.preventDefault();\n        return this.redo();\n      }\n      if (isUndo(event)) {\n        event.preventDefault();\n        return this.undo();\n      }\n    });\n    this.eventListener.listen('beforeinput', event => {\n      var _a, _b, _c;\n      const isForward = event.inputType.includes('Forward');\n      this.updateHistory(this.elementState);\n      switch (event.inputType) {\n        case 'deleteByCut':\n        case 'deleteContentBackward':\n        case 'deleteContentForward':\n          return this.handleDelete({\n            event,\n            isForward,\n            selection: getNotEmptySelection(this.elementState, isForward)\n          });\n        case 'deleteHardLineBackward':\n        case 'deleteHardLineForward':\n        case 'deleteSoftLineBackward':\n        case 'deleteSoftLineForward':\n          return this.handleDelete({\n            event,\n            isForward,\n            selection: getLineSelection(this.elementState, isForward),\n            force: true\n          });\n        case 'deleteWordBackward':\n        case 'deleteWordForward':\n          return this.handleDelete({\n            event,\n            isForward,\n            selection: getWordSelection(this.elementState, isForward),\n            force: true\n          });\n        case 'historyRedo':\n          event.preventDefault();\n          return this.redo();\n        // historyUndo/historyRedo will not be triggered if value was modified programmatically\n        case 'historyUndo':\n          event.preventDefault();\n          return this.undo();\n        case 'insertCompositionText':\n          return;\n        // will be handled inside `compositionend` event\n        case 'insertLineBreak':\n        case 'insertParagraph':\n          return this.handleEnter(event);\n        case 'insertReplacementText':\n          /**\n           * According {@link https://www.w3.org/TR/input-events-2 W3C specification}:\n           * > `insertReplacementText` – insert or replace existing text by means of a spell checker,\n           * > auto-correct, writing suggestions or similar.\n           * ___\n           * Firefox emits `insertReplacementText` event for its suggestion/autofill and for spell checker.\n           * However, it is impossible to detect which part of the textfield value is going to be replaced\n           * (`selectionStart` and `selectionEnd` just equal to the last caret position).\n           * ___\n           * Chrome does not fire `beforeinput` event for its suggestion/autofill.\n           * It emits only `input` event with `inputType` and `data` set to `undefined`.\n           * ___\n           * All these browser limitations make us to validate the result value later in `input` event.\n           */\n          return;\n        case 'insertFromDrop':\n        case 'insertFromPaste':\n        case 'insertText':\n        default:\n          return this.handleInsert(event, (_c = (_a = event.data) !== null && _a !== void 0 ? _a :\n          // `event.data` for `contentEditable` is always `null` for paste/drop events\n          (_b = event.dataTransfer) === null || _b === void 0 ? void 0 : _b.getData('text/plain')) !== null && _c !== void 0 ? _c : '');\n      }\n    });\n    this.eventListener.listen('input', () => {\n      if (this.upcomingElementState) {\n        this.updateElementState(this.upcomingElementState);\n        this.upcomingElementState = null;\n      }\n    }, {\n      capture: true\n    });\n    this.eventListener.listen('input', ({\n      inputType\n    }) => {\n      if (inputType === 'insertCompositionText') {\n        return; // will be handled inside `compositionend` event\n      }\n      this.ensureValueFitsMask();\n      this.updateHistory(this.elementState);\n    });\n    this.eventListener.listen('compositionend', () => {\n      this.ensureValueFitsMask();\n      this.updateHistory(this.elementState);\n    });\n  }\n  destroy() {\n    this.eventListener.destroy();\n    this.teardowns.forEach(teardown => teardown === null || teardown === void 0 ? void 0 : teardown());\n  }\n  updateElementState({\n    value,\n    selection\n  }, eventInit) {\n    const initialValue = this.elementState.value;\n    this.updateValue(value);\n    this.updateSelectionRange(selection);\n    if (eventInit && initialValue !== value) {\n      this.dispatchInputEvent(eventInit);\n    }\n  }\n  get elementState() {\n    const {\n      value,\n      selectionStart,\n      selectionEnd\n    } = this.element;\n    return {\n      value,\n      selection: [selectionStart !== null && selectionStart !== void 0 ? selectionStart : 0, selectionEnd !== null && selectionEnd !== void 0 ? selectionEnd : 0]\n    };\n  }\n  get maxLength() {\n    const {\n      maxLength\n    } = this.element;\n    return maxLength === -1 ? Infinity : maxLength;\n  }\n  updateSelectionRange([from, to]) {\n    var _a;\n    const {\n      element\n    } = this;\n    if (element.matches(':focus') && (element.selectionStart !== from || element.selectionEnd !== to)) {\n      (_a = element.setSelectionRange) === null || _a === void 0 ? void 0 : _a.call(element, from, to);\n    }\n  }\n  updateValue(value) {\n    this.element.value = value;\n  }\n  ensureValueFitsMask() {\n    this.updateElementState(maskitoTransform(this.elementState, this.options), {\n      inputType: 'insertText',\n      data: null\n    });\n  }\n  dispatchInputEvent(eventInit = {\n    inputType: 'insertText',\n    data: null\n  }) {\n    if (globalThis.InputEvent) {\n      this.element.dispatchEvent(new InputEvent('input', Object.assign(Object.assign({}, eventInit), {\n        bubbles: true,\n        cancelable: false\n      })));\n    }\n  }\n  handleDelete({\n    event,\n    selection,\n    isForward,\n    force = false\n  }) {\n    const initialState = {\n      value: this.elementState.value,\n      selection\n    };\n    const [initialFrom, initialTo] = initialState.selection;\n    const {\n      elementState\n    } = this.preprocessor({\n      elementState: initialState,\n      data: ''\n    }, isForward ? 'deleteForward' : 'deleteBackward');\n    const maskModel = new MaskModel(elementState, this.options);\n    const [from, to] = elementState.selection;\n    maskModel.deleteCharacters([from, to]);\n    const newElementState = this.postprocessor(maskModel, initialState);\n    const newPossibleState = {\n      value: initialState.value.slice(0, initialFrom) + initialState.value.slice(initialTo),\n      selection: [initialFrom, initialFrom]\n    };\n    if (areElementStatesEqual(newPossibleState, newElementState) && !force && !this.element.isContentEditable) {\n      return;\n    }\n    if (areElementValuesEqual(initialState, elementState, maskModel, newElementState)) {\n      event.preventDefault();\n      // User presses Backspace/Delete for the fixed value\n      return this.updateSelectionRange(isForward ? [to, to] : [from, from]);\n    }\n    this.upcomingElementState = newElementState;\n  }\n  handleInsert(event, data) {\n    const {\n      options,\n      maxLength,\n      element,\n      elementState: initialElementState\n    } = this;\n    const {\n      elementState,\n      data: insertedText = data\n    } = this.preprocessor({\n      data,\n      elementState: initialElementState\n    }, 'insert');\n    const maskModel = new MaskModel(elementState, options);\n    try {\n      maskModel.addCharacters(elementState.selection, insertedText);\n    } catch (_a) {\n      return event.preventDefault();\n    }\n    const [from, to] = initialElementState.selection;\n    const newPossibleState = {\n      value: initialElementState.value.slice(0, from) + data + initialElementState.value.slice(to),\n      selection: [from + data.length, from + data.length]\n    };\n    const newElementState = this.clampState(this.postprocessor(maskModel, initialElementState));\n    if (!areElementStatesEqual(this.clampState(newPossibleState), newElementState) || element.isContentEditable) {\n      this.upcomingElementState = newElementState;\n      if (options.overwriteMode === 'replace' && newPossibleState.value.length > maxLength) {\n        /**\n         * Browsers know nothing about Maskito and its `overwriteMode`.\n         * When textfield value length is already equal to attribute `maxlength`,\n         * pressing any key (even with valid value) does not emit `input` event.\n         */\n        this.dispatchInputEvent({\n          inputType: 'insertText',\n          data\n        });\n      }\n    }\n  }\n  handleEnter(event) {\n    if (this.isTextArea || this.element.isContentEditable) {\n      this.handleInsert(event, '\\n');\n    }\n  }\n  clampState({\n    value,\n    selection\n  }) {\n    const [from, to] = selection;\n    const max = this.maxLength;\n    return {\n      value: value.slice(0, max),\n      selection: [Math.min(from, max), Math.min(to, max)]\n    };\n  }\n}\nexport { MASKITO_DEFAULT_ELEMENT_PREDICATE, MASKITO_DEFAULT_OPTIONS, Maskito, maskitoAdaptContentEditable, maskitoChangeEventPlugin, maskitoInitialCalibrationPlugin, maskitoPipe, maskitoStrictCompositionPlugin, maskitoTransform, maskitoUpdateElement };", "map": {"version": 3, "names": ["getContentEditableSelection", "element", "anchorOffset", "focusOffset", "ownerDocument", "getSelection", "from", "Math", "min", "to", "max", "setContentEditableSelection", "_a", "_b", "_c", "_d", "document", "range", "createRange", "setStart", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "length", "setEnd", "<PERSON><PERSON><PERSON><PERSON>", "selection", "removeAllRanges", "addRange", "ContentEditableAdapter", "constructor", "max<PERSON><PERSON><PERSON>", "Infinity", "value", "innerText", "replace", "innerHTML", "selectionStart", "selectionEnd", "setSelectionRange", "select", "maskitoAdaptContentEditable", "adapter", "Proxy", "get", "target", "prop", "nativeProperty", "bind", "set", "val", "receiver", "Reflect", "MASKITO_DEFAULT_ELEMENT_PREDICATE", "e", "isContentEditable", "querySelector", "MASKITO_DEFAULT_OPTIONS", "mask", "preprocessors", "postprocessors", "plugins", "overwriteMode", "MaskHistory", "now", "past", "future", "undo", "state", "pop", "push", "updateElement", "redo", "updateHistory", "isValueChanged", "isSelectionChanged", "some", "item", "index", "inputType", "updateElementState", "data", "areElementValuesEqual", "sampleState", "states", "every", "areElementStatesEqual", "applyOverwriteMode", "newCharacters", "mode", "computedMode", "isFixedCharacter", "char", "getLeadingFixedCharacters", "validated<PERSON><PERSON>uePart", "newCharacter", "initialElementState", "leadingFixedCharacters", "i", "charConstraint", "isInitiallyExisted", "validateValueWithMask", "maskExpression", "Array", "isArray", "match", "test", "guessValidValueByPattern", "elementState", "maskedFrom", "maskedTo", "maskedValue", "reduce", "validatedCharacters", "charIndex", "leadingCharacters", "newValidatedChars", "trailingFixedCharacters", "guessValidValueByRegExp", "maskRegExp", "newFrom", "newTo", "validatedV<PERSON>ue", "newPossibleValue", "calibrateValueByMask", "slice", "removeFixedMaskCharacters", "unmasked<PERSON><PERSON>ue", "rawValue", "fill", "MaskModel", "maskOptions", "getMaskExpression", "addCharacters", "unmaskedElementState", "unmaskedFrom", "unmaskedTo", "newUnmaskedLeadingValuePart", "newCaretIndex", "maskedElementState", "isInvalidCharsInsertion", "Error", "deleteCharacters", "newUnmaskedValue", "maskitoChangeEventPlugin", "valueListener", "blurListener", "dispatchEvent", "Event", "bubbles", "addEventListener", "removeEventListener", "EventListener", "listeners", "listen", "eventType", "fn", "options", "untypedFn", "destroy", "for<PERSON>ach", "stopListen", "HotkeyModifier", "CTRL", "ALT", "SHIFT", "META", "HotkeyCode", "Y", "Z", "isHotkey", "event", "modifiers", "hotkeyCode", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "metaKey", "keyCode", "isRedo", "isUndo", "maskitoUpdateElement", "valueOrElementState", "initialValue", "matches", "call", "getLineSelection", "isForward", "nearestBreak", "indexOf", "lastIndexOf", "selectFrom", "selectTo", "getNotEmptySelection", "notEmptySelection", "map", "x", "TRAILING_SPACES_REG", "LEADING_SPACES_REG", "SPACE_REG", "getWordSelection", "valueAfterSelectionStart", "leadingSpaces", "nearestWordEndIndex", "trimStart", "search", "valueBeforeSelectionEnd", "trailingSpaces", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trimEnd", "split", "reverse", "findIndex", "exec", "maskitoPipe", "processors", "initialData", "readon<PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "maskitoTransform", "valueOrState", "maskitoOptions", "preprocessor", "postprocessor", "maskModel", "SPACE", "createDoubleSpacePlugin", "prevValue", "prevCaretIndex", "prevEvent", "prevRejectedSpace", "eventListener", "rejectedSpace", "Number", "endsWith", "maskitoInitialCalibrationPlugin", "customOptions", "maskitoStrictCompositionPlugin", "listener", "validatedState", "preventDefault", "BUILT_IN_PLUGINS", "<PERSON><PERSON>", "isTextArea", "nodeName", "upcomingElementState", "teardowns", "concat", "plugin", "includes", "handleDelete", "force", "handleEnter", "handleInsert", "dataTransfer", "getData", "capture", "ensureValueFitsMask", "teardown", "eventInit", "updateValue", "updateSelectionRange", "dispatchInputEvent", "globalThis", "InputEvent", "cancelable", "initialState", "initialFrom", "initialTo", "newElementState", "newPossibleState", "insertedText", "clampState"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@maskito/core/index.esm.js"], "sourcesContent": ["function getContentEditableSelection(element) {\n    const { anchorOffset = 0, focusOffset = 0 } = element.ownerDocument.getSelection() || {};\n    const from = Math.min(anchorOffset, focusOffset);\n    const to = Math.max(anchorOffset, focusOffset);\n    return [from, to];\n}\n\nfunction setContentEditableSelection(element, [from, to]) {\n    var _a, _b, _c, _d;\n    const document = element.ownerDocument;\n    const range = document.createRange();\n    range.setStart(element.firstChild || element, Math.min(from, (_b = (_a = element.textContent) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0));\n    range.setEnd(element.lastChild || element, Math.min(to, (_d = (_c = element.textContent) === null || _c === void 0 ? void 0 : _c.length) !== null && _d !== void 0 ? _d : 0));\n    const selection = document.getSelection();\n    if (selection) {\n        selection.removeAllRanges();\n        selection.addRange(range);\n    }\n}\n\nclass ContentEditableAdapter {\n    constructor(element) {\n        this.element = element;\n        this.maxLength = Infinity;\n    }\n    get value() {\n        return this.element.innerText.replace(/\\n\\n$/, '\\n');\n    }\n    set value(value) {\n        // Setting into innerHTML of element with `white-space: pre;` style\n        this.element.innerHTML = value.replace(/\\n$/, '\\n\\n');\n    }\n    get selectionStart() {\n        return getContentEditableSelection(this.element)[0];\n    }\n    get selectionEnd() {\n        return getContentEditableSelection(this.element)[1];\n    }\n    setSelectionRange(from, to) {\n        setContentEditableSelection(this.element, [from !== null && from !== void 0 ? from : 0, to !== null && to !== void 0 ? to : 0]);\n    }\n    select() {\n        this.setSelectionRange(0, this.value.length);\n    }\n}\nfunction maskitoAdaptContentEditable(element) {\n    const adapter = new ContentEditableAdapter(element);\n    return new Proxy(element, {\n        get(target, prop) {\n            if (prop in adapter) {\n                return adapter[prop];\n            }\n            const nativeProperty = target[prop];\n            return typeof nativeProperty === 'function'\n                ? nativeProperty.bind(target)\n                : nativeProperty;\n        },\n        // eslint-disable-next-line @typescript-eslint/max-params\n        set(target, prop, val, receiver) {\n            return Reflect.set(prop in adapter ? adapter : target, prop, val, receiver);\n        },\n    });\n}\n\nconst MASKITO_DEFAULT_ELEMENT_PREDICATE = (e) => e.isContentEditable\n    ? maskitoAdaptContentEditable(e)\n    : e.querySelector('input,textarea') ||\n        e;\n\nconst MASKITO_DEFAULT_OPTIONS = {\n    mask: /^.*$/,\n    preprocessors: [],\n    postprocessors: [],\n    plugins: [],\n    overwriteMode: 'shift',\n};\n\nclass MaskHistory {\n    constructor() {\n        this.now = null;\n        this.past = [];\n        this.future = [];\n    }\n    undo() {\n        const state = this.past.pop();\n        if (state && this.now) {\n            this.future.push(this.now);\n            this.updateElement(state, 'historyUndo');\n        }\n    }\n    redo() {\n        const state = this.future.pop();\n        if (state && this.now) {\n            this.past.push(this.now);\n            this.updateElement(state, 'historyRedo');\n        }\n    }\n    updateHistory(state) {\n        if (!this.now) {\n            this.now = state;\n            return;\n        }\n        const isValueChanged = this.now.value !== state.value;\n        const isSelectionChanged = this.now.selection.some((item, index) => item !== state.selection[index]);\n        if (!isValueChanged && !isSelectionChanged) {\n            return;\n        }\n        if (isValueChanged) {\n            this.past.push(this.now);\n            this.future = [];\n        }\n        this.now = state;\n    }\n    updateElement(state, inputType) {\n        this.now = state;\n        this.updateElementState(state, { inputType, data: null });\n    }\n}\n\nfunction areElementValuesEqual(sampleState, ...states) {\n    return states.every(({ value }) => value === sampleState.value);\n}\nfunction areElementStatesEqual(sampleState, ...states) {\n    return states.every(({ value, selection }) => value === sampleState.value &&\n        selection[0] === sampleState.selection[0] &&\n        selection[1] === sampleState.selection[1]);\n}\n\nfunction applyOverwriteMode({ value, selection }, newCharacters, mode) {\n    const [from, to] = selection;\n    const computedMode = typeof mode === 'function' ? mode({ value, selection }) : mode;\n    return {\n        value,\n        selection: computedMode === 'replace'\n            ? [from, Math.max(from + newCharacters.length, to)]\n            : [from, to],\n    };\n}\n\nfunction isFixedCharacter(char) {\n    return typeof char === 'string';\n}\n\n// eslint-disable-next-line @typescript-eslint/max-params\nfunction getLeadingFixedCharacters(mask, validatedValuePart, newCharacter, initialElementState) {\n    let leadingFixedCharacters = '';\n    for (let i = validatedValuePart.length; i < mask.length; i++) {\n        const charConstraint = mask[i] || '';\n        const isInitiallyExisted = (initialElementState === null || initialElementState === void 0 ? void 0 : initialElementState.value[i]) === charConstraint;\n        if (!isFixedCharacter(charConstraint) ||\n            (charConstraint === newCharacter && !isInitiallyExisted)) {\n            return leadingFixedCharacters;\n        }\n        leadingFixedCharacters += charConstraint;\n    }\n    return leadingFixedCharacters;\n}\n\nfunction validateValueWithMask(value, maskExpression) {\n    if (Array.isArray(maskExpression)) {\n        return (value.length === maskExpression.length &&\n            Array.from(value).every((char, i) => {\n                const charConstraint = maskExpression[i] || '';\n                return isFixedCharacter(charConstraint)\n                    ? char === charConstraint\n                    : char.match(charConstraint);\n            }));\n    }\n    return maskExpression.test(value);\n}\n\nfunction guessValidValueByPattern(elementState, mask, initialElementState) {\n    let maskedFrom = null;\n    let maskedTo = null;\n    const maskedValue = Array.from(elementState.value).reduce((validatedCharacters, char, charIndex) => {\n        const leadingCharacters = getLeadingFixedCharacters(mask, validatedCharacters, char, initialElementState);\n        const newValidatedChars = validatedCharacters + leadingCharacters;\n        const charConstraint = mask[newValidatedChars.length] || '';\n        if (maskedFrom === null && charIndex >= elementState.selection[0]) {\n            maskedFrom = newValidatedChars.length;\n        }\n        if (maskedTo === null && charIndex >= elementState.selection[1]) {\n            maskedTo = newValidatedChars.length;\n        }\n        if (isFixedCharacter(charConstraint)) {\n            return newValidatedChars + charConstraint;\n        }\n        return char.match(charConstraint)\n            ? newValidatedChars + char\n            : newValidatedChars;\n    }, '');\n    const trailingFixedCharacters = getLeadingFixedCharacters(mask, maskedValue, '', initialElementState);\n    return {\n        value: validateValueWithMask(maskedValue + trailingFixedCharacters, mask)\n            ? maskedValue + trailingFixedCharacters\n            : maskedValue,\n        selection: [maskedFrom !== null && maskedFrom !== void 0 ? maskedFrom : maskedValue.length, maskedTo !== null && maskedTo !== void 0 ? maskedTo : maskedValue.length],\n    };\n}\n\nfunction guessValidValueByRegExp({ value, selection }, maskRegExp) {\n    const [from, to] = selection;\n    let newFrom = from;\n    let newTo = to;\n    const validatedValue = Array.from(value).reduce((validatedValuePart, char, i) => {\n        const newPossibleValue = validatedValuePart + char;\n        if (from === i) {\n            newFrom = validatedValuePart.length;\n        }\n        if (to === i) {\n            newTo = validatedValuePart.length;\n        }\n        return newPossibleValue.match(maskRegExp) ? newPossibleValue : validatedValuePart;\n    }, '');\n    return {\n        value: validatedValue,\n        selection: [\n            Math.min(newFrom, validatedValue.length),\n            Math.min(newTo, validatedValue.length),\n        ],\n    };\n}\n\nfunction calibrateValueByMask(elementState, mask, initialElementState = null) {\n    if (validateValueWithMask(elementState.value, mask)) {\n        return elementState;\n    }\n    const { value, selection } = Array.isArray(mask)\n        ? guessValidValueByPattern(elementState, mask, initialElementState)\n        : guessValidValueByRegExp(elementState, mask);\n    return {\n        selection,\n        value: Array.isArray(mask) ? value.slice(0, mask.length) : value,\n    };\n}\n\nfunction removeFixedMaskCharacters(initialElementState, mask) {\n    if (!Array.isArray(mask)) {\n        return initialElementState;\n    }\n    const [from, to] = initialElementState.selection;\n    const selection = [];\n    const unmaskedValue = Array.from(initialElementState.value).reduce((rawValue, char, i) => {\n        const charConstraint = mask[i] || '';\n        if (i === from) {\n            selection.push(rawValue.length);\n        }\n        if (i === to) {\n            selection.push(rawValue.length);\n        }\n        return isFixedCharacter(charConstraint) && charConstraint === char\n            ? rawValue\n            : rawValue + char;\n    }, '');\n    if (selection.length < 2) {\n        selection.push(...new Array(2 - selection.length).fill(unmaskedValue.length));\n    }\n    return {\n        value: unmaskedValue,\n        selection: [selection[0], selection[1]],\n    };\n}\n\nclass MaskModel {\n    constructor(initialElementState, maskOptions) {\n        this.initialElementState = initialElementState;\n        this.maskOptions = maskOptions;\n        this.value = '';\n        this.selection = [0, 0];\n        const { value, selection } = calibrateValueByMask(this.initialElementState, this.getMaskExpression(this.initialElementState));\n        this.value = value;\n        this.selection = selection;\n    }\n    addCharacters([from, to], newCharacters) {\n        const { value, maskOptions } = this;\n        const maskExpression = this.getMaskExpression({\n            value: value.slice(0, from) + newCharacters + value.slice(to),\n            selection: [from + newCharacters.length, from + newCharacters.length],\n        });\n        const initialElementState = { value, selection: [from, to] };\n        const unmaskedElementState = removeFixedMaskCharacters(initialElementState, maskExpression);\n        const [unmaskedFrom, unmaskedTo] = applyOverwriteMode(unmaskedElementState, newCharacters, maskOptions.overwriteMode).selection;\n        const newUnmaskedLeadingValuePart = unmaskedElementState.value.slice(0, unmaskedFrom) + newCharacters;\n        const newCaretIndex = newUnmaskedLeadingValuePart.length;\n        const maskedElementState = calibrateValueByMask({\n            value: newUnmaskedLeadingValuePart +\n                unmaskedElementState.value.slice(unmaskedTo),\n            selection: [newCaretIndex, newCaretIndex],\n        }, maskExpression, initialElementState);\n        const isInvalidCharsInsertion = value.slice(0, from) ===\n            calibrateValueByMask({\n                value: newUnmaskedLeadingValuePart,\n                selection: [newCaretIndex, newCaretIndex],\n            }, maskExpression, initialElementState).value;\n        if (isInvalidCharsInsertion ||\n            areElementStatesEqual(this, maskedElementState) // If typing new characters does not change value\n        ) {\n            throw new Error('Invalid mask value');\n        }\n        this.value = maskedElementState.value;\n        this.selection = maskedElementState.selection;\n    }\n    deleteCharacters([from, to]) {\n        if (from === to || !to) {\n            return;\n        }\n        const { value } = this;\n        const maskExpression = this.getMaskExpression({\n            value: value.slice(0, from) + value.slice(to),\n            selection: [from, from],\n        });\n        const initialElementState = { value, selection: [from, to] };\n        const unmaskedElementState = removeFixedMaskCharacters(initialElementState, maskExpression);\n        const [unmaskedFrom, unmaskedTo] = unmaskedElementState.selection;\n        const newUnmaskedValue = unmaskedElementState.value.slice(0, unmaskedFrom) +\n            unmaskedElementState.value.slice(unmaskedTo);\n        const maskedElementState = calibrateValueByMask({ value: newUnmaskedValue, selection: [unmaskedFrom, unmaskedFrom] }, maskExpression, initialElementState);\n        this.value = maskedElementState.value;\n        this.selection = maskedElementState.selection;\n    }\n    getMaskExpression(elementState) {\n        const { mask } = this.maskOptions;\n        return typeof mask === 'function' ? mask(elementState) : mask;\n    }\n}\n\nfunction maskitoChangeEventPlugin() {\n    return (element) => {\n        if (element.isContentEditable) {\n            return;\n        }\n        let value = element.value;\n        const valueListener = () => {\n            value = element.value;\n        };\n        const blurListener = () => {\n            if (element.value !== value) {\n                element.dispatchEvent(new Event('change', { bubbles: true }));\n            }\n        };\n        element.addEventListener('focus', valueListener);\n        element.addEventListener('change', valueListener);\n        element.addEventListener('blur', blurListener);\n        return () => {\n            element.removeEventListener('focus', valueListener);\n            element.removeEventListener('change', valueListener);\n            element.removeEventListener('blur', blurListener);\n        };\n    };\n}\n\nclass EventListener {\n    constructor(element) {\n        this.element = element;\n        this.listeners = [];\n    }\n    listen(eventType, fn, options) {\n        const untypedFn = fn;\n        this.element.addEventListener(eventType, untypedFn, options);\n        this.listeners.push(() => this.element.removeEventListener(eventType, untypedFn, options));\n    }\n    destroy() {\n        this.listeners.forEach((stopListen) => stopListen());\n    }\n}\n\nconst HotkeyModifier = {\n    CTRL: 1 << 0,\n    ALT: 1 << 1,\n    SHIFT: 1 << 2,\n    META: 1 << 3,\n};\n// TODO add variants that can be processed correctly\nconst HotkeyCode = {\n    Y: 89,\n    Z: 90,\n};\n/**\n * Checks if the passed keyboard event match the required hotkey.\n *\n * @example\n * input.addEventListener('keydown', (event) => {\n *     if (isHotkey(event, HotkeyModifier.CTRL | HotkeyModifier.SHIFT, HotkeyCode.Z)) {\n *         // redo hotkey pressed\n *     }\n * })\n *\n * @return will return `true` only if the {@link HotkeyCode} matches and only the necessary\n * {@link HotkeyModifier modifiers} have been pressed\n */\nfunction isHotkey(event, modifiers, hotkeyCode) {\n    return (event.ctrlKey === !!(modifiers & HotkeyModifier.CTRL) &&\n        event.altKey === !!(modifiers & HotkeyModifier.ALT) &&\n        event.shiftKey === !!(modifiers & HotkeyModifier.SHIFT) &&\n        event.metaKey === !!(modifiers & HotkeyModifier.META) &&\n        /**\n         * We intentionally use legacy {@link KeyboardEvent#keyCode `keyCode`} property. It is more\n         * \"keyboard-layout\"-independent than {@link KeyboardEvent#key `key`} or {@link KeyboardEvent#code `code`} properties.\n         * @see {@link https://github.com/taiga-family/maskito/issues/315 `KeyboardEvent#code` issue}\n         */\n        event.keyCode === hotkeyCode);\n}\n\nfunction isRedo(event) {\n    return (isHotkey(event, HotkeyModifier.CTRL, HotkeyCode.Y) || // Windows\n        isHotkey(event, HotkeyModifier.CTRL | HotkeyModifier.SHIFT, HotkeyCode.Z) || // Windows & Android\n        isHotkey(event, HotkeyModifier.META | HotkeyModifier.SHIFT, HotkeyCode.Z) // macOS & iOS\n    );\n}\nfunction isUndo(event) {\n    return (isHotkey(event, HotkeyModifier.CTRL, HotkeyCode.Z) || // Windows & Android\n        isHotkey(event, HotkeyModifier.META, HotkeyCode.Z) // macOS & iOS\n    );\n}\n\n/**\n * Sets value to element, and dispatches input event\n * if you passed ELementState, it also sets selection range\n *\n * @example\n * maskitoUpdateElement(input, newValue);\n * maskitoUpdateElement(input, elementState);\n *\n * @see {@link https://github.com/taiga-family/maskito/issues/804 issue}\n *\n * @return void\n */\nfunction maskitoUpdateElement(element, valueOrElementState) {\n    var _a;\n    const initialValue = element.value;\n    if (typeof valueOrElementState === 'string') {\n        element.value = valueOrElementState;\n    }\n    else {\n        const [from, to] = valueOrElementState.selection;\n        element.value = valueOrElementState.value;\n        if (element.matches(':focus')) {\n            (_a = element.setSelectionRange) === null || _a === void 0 ? void 0 : _a.call(element, from, to);\n        }\n    }\n    if (element.value !== initialValue) {\n        element.dispatchEvent(new Event('input', \n        /**\n         * React handles this event only on bubbling phase\n         *\n         * here is the list of events that are processed in the capture stage, others are processed in the bubbling stage\n         * https://github.com/facebook/react/blob/cb2439624f43c510007f65aea5c50a8bb97917e4/packages/react-dom-bindings/src/events/DOMPluginEventSystem.js#L222\n         */\n        { bubbles: true }));\n    }\n}\n\nfunction getLineSelection({ value, selection }, isForward) {\n    const [from, to] = selection;\n    if (from !== to) {\n        return [from, to];\n    }\n    const nearestBreak = isForward\n        ? value.slice(from).indexOf('\\n') + 1 || value.length\n        : value.slice(0, to).lastIndexOf('\\n') + 1;\n    const selectFrom = isForward ? from : nearestBreak;\n    const selectTo = isForward ? nearestBreak : to;\n    return [selectFrom, selectTo];\n}\n\nfunction getNotEmptySelection({ value, selection }, isForward) {\n    const [from, to] = selection;\n    if (from !== to) {\n        return [from, to];\n    }\n    const notEmptySelection = isForward ? [from, to + 1] : [from - 1, to];\n    return notEmptySelection.map((x) => Math.min(Math.max(x, 0), value.length));\n}\n\nconst TRAILING_SPACES_REG = /\\s+$/g;\nconst LEADING_SPACES_REG = /^\\s+/g;\nconst SPACE_REG = /\\s/;\nfunction getWordSelection({ value, selection }, isForward) {\n    const [from, to] = selection;\n    if (from !== to) {\n        return [from, to];\n    }\n    if (isForward) {\n        const valueAfterSelectionStart = value.slice(from);\n        const [leadingSpaces] = valueAfterSelectionStart.match(LEADING_SPACES_REG) || [\n            '',\n        ];\n        const nearestWordEndIndex = valueAfterSelectionStart\n            .trimStart()\n            .search(SPACE_REG);\n        return [\n            from,\n            nearestWordEndIndex !== -1\n                ? from + leadingSpaces.length + nearestWordEndIndex\n                : value.length,\n        ];\n    }\n    const valueBeforeSelectionEnd = value.slice(0, to);\n    const [trailingSpaces] = valueBeforeSelectionEnd.match(TRAILING_SPACES_REG) || [''];\n    const selectedWordLength = valueBeforeSelectionEnd\n        .trimEnd()\n        .split('')\n        .reverse()\n        .findIndex((char) => SPACE_REG.exec(char));\n    return [\n        selectedWordLength !== -1 ? to - trailingSpaces.length - selectedWordLength : 0,\n        to,\n    ];\n}\n\n/* eslint-disable @typescript-eslint/no-restricted-types */\n/**\n * @internal\n */\nfunction maskitoPipe(processors = []) {\n    return (initialData, ...readonlyArgs) => processors.reduce((data, fn) => (Object.assign(Object.assign({}, data), fn(data, ...readonlyArgs))), initialData);\n}\n\nfunction maskitoTransform(valueOrState, maskitoOptions) {\n    const options = Object.assign(Object.assign({}, MASKITO_DEFAULT_OPTIONS), maskitoOptions);\n    const preprocessor = maskitoPipe(options.preprocessors);\n    const postprocessor = maskitoPipe(options.postprocessors);\n    const initialElementState = typeof valueOrState === 'string'\n        ? { value: valueOrState, selection: [0, 0] }\n        : valueOrState;\n    const { elementState } = preprocessor({ elementState: initialElementState, data: '' }, 'validation');\n    const maskModel = new MaskModel(elementState, options);\n    const { value, selection } = postprocessor(maskModel, initialElementState);\n    return typeof valueOrState === 'string' ? value : { value, selection };\n}\n\nconst SPACE = ' ';\n/**\n * 1. Android user (with G-board keyboard or similar) presses 1st space\n * ```\n * {type: \"beforeinput\", data: \" \", inputType: \"insertText\"}\n * ```\n * 2. User presses 2nd space\n * ```\n * // Android tries to delete previously inserted space\n * {type: \"beforeinput\", inputType: \"deleteContentBackward\"}\n * {type: \"beforeinput\", data: \". \", inputType: \"insertText\"}\n * ```\n * ---------\n * 1. MacOS user presses 1st space\n * ```\n * {type: \"beforeinput\", data: \" \", inputType: \"insertText\"}\n * ```\n * 2. User presses 2nd space\n * ```\n * // MacOS automatically run `element.setSelectionRange(indexBeforeSpace, indexAfterSpace)` and then\n * {type: \"beforeinput\", data: \". \", inputType: \"insertText\"}\n * ```\n * ---------\n * @see https://github.com/taiga-family/maskito/issues/2023\n */\nfunction createDoubleSpacePlugin() {\n    let prevValue = '';\n    let prevCaretIndex = 0;\n    let prevEvent = null;\n    let prevRejectedSpace = false;\n    return (element) => {\n        const eventListener = new EventListener(element);\n        eventListener.listen('beforeinput', (event) => {\n            var _a, _b;\n            const { value, selectionStart, selectionEnd } = element;\n            const rejectedSpace = (prevEvent === null || prevEvent === void 0 ? void 0 : prevEvent.inputType) === 'insertText' &&\n                (prevEvent === null || prevEvent === void 0 ? void 0 : prevEvent.data) === SPACE &&\n                !value.slice(0, Number(selectionEnd)).endsWith(SPACE);\n            if (event.inputType === 'insertText' && event.data === `.${SPACE}`) {\n                if ((prevEvent === null || prevEvent === void 0 ? void 0 : prevEvent.inputType) === 'deleteContentBackward' &&\n                    prevRejectedSpace) {\n                    // Android\n                    element.value = prevValue;\n                    (_a = element.setSelectionRange) === null || _a === void 0 ? void 0 : _a.call(element, prevCaretIndex, prevCaretIndex);\n                }\n                else if (rejectedSpace) {\n                    // Mac OS\n                    (_b = element.setSelectionRange) === null || _b === void 0 ? void 0 : _b.call(element, selectionStart, selectionStart);\n                }\n            }\n            prevRejectedSpace = rejectedSpace;\n            prevEvent = event;\n            prevValue = value;\n            prevCaretIndex = Number((rejectedSpace ? prevCaretIndex : selectionEnd) === value.length\n                ? selectionEnd\n                : selectionStart);\n        });\n        return () => eventListener.destroy();\n    };\n}\n\nfunction maskitoInitialCalibrationPlugin(customOptions) {\n    return (element, options) => {\n        var _a, _b;\n        const from = (_a = element.selectionStart) !== null && _a !== void 0 ? _a : 0;\n        const to = (_b = element.selectionEnd) !== null && _b !== void 0 ? _b : 0;\n        maskitoUpdateElement(element, {\n            value: maskitoTransform(element.value, customOptions || options),\n            selection: [from, to],\n        });\n    };\n}\n\nfunction maskitoStrictCompositionPlugin() {\n    return (element, maskitoOptions) => {\n        const listener = (event) => {\n            var _a, _b;\n            if (event.inputType !== 'insertCompositionText') {\n                return;\n            }\n            const selection = [\n                (_a = element.selectionStart) !== null && _a !== void 0 ? _a : 0,\n                (_b = element.selectionEnd) !== null && _b !== void 0 ? _b : 0,\n            ];\n            const elementState = {\n                selection,\n                value: element.value,\n            };\n            const validatedState = maskitoTransform(elementState, maskitoOptions);\n            if (!areElementStatesEqual(elementState, validatedState)) {\n                event.preventDefault();\n                maskitoUpdateElement(element, validatedState);\n            }\n        };\n        element.addEventListener('input', listener);\n        return () => element.removeEventListener('input', listener);\n    };\n}\n\nconst BUILT_IN_PLUGINS = [createDoubleSpacePlugin()];\nclass Maskito extends MaskHistory {\n    constructor(element, maskitoOptions) {\n        super();\n        this.element = element;\n        this.maskitoOptions = maskitoOptions;\n        this.isTextArea = this.element.nodeName === 'TEXTAREA';\n        this.eventListener = new EventListener(this.element);\n        this.options = Object.assign(Object.assign({}, MASKITO_DEFAULT_OPTIONS), this.maskitoOptions);\n        this.upcomingElementState = null;\n        this.preprocessor = maskitoPipe(this.options.preprocessors);\n        this.postprocessor = maskitoPipe(this.options.postprocessors);\n        this.teardowns = this.options.plugins\n            .concat(BUILT_IN_PLUGINS)\n            .map((plugin) => plugin(this.element, this.options));\n        this.updateHistory(this.elementState);\n        this.eventListener.listen('keydown', (event) => {\n            if (isRedo(event)) {\n                event.preventDefault();\n                return this.redo();\n            }\n            if (isUndo(event)) {\n                event.preventDefault();\n                return this.undo();\n            }\n        });\n        this.eventListener.listen('beforeinput', (event) => {\n            var _a, _b, _c;\n            const isForward = event.inputType.includes('Forward');\n            this.updateHistory(this.elementState);\n            switch (event.inputType) {\n                case 'deleteByCut':\n                case 'deleteContentBackward':\n                case 'deleteContentForward':\n                    return this.handleDelete({\n                        event,\n                        isForward,\n                        selection: getNotEmptySelection(this.elementState, isForward),\n                    });\n                case 'deleteHardLineBackward':\n                case 'deleteHardLineForward':\n                case 'deleteSoftLineBackward':\n                case 'deleteSoftLineForward':\n                    return this.handleDelete({\n                        event,\n                        isForward,\n                        selection: getLineSelection(this.elementState, isForward),\n                        force: true,\n                    });\n                case 'deleteWordBackward':\n                case 'deleteWordForward':\n                    return this.handleDelete({\n                        event,\n                        isForward,\n                        selection: getWordSelection(this.elementState, isForward),\n                        force: true,\n                    });\n                case 'historyRedo':\n                    event.preventDefault();\n                    return this.redo();\n                // historyUndo/historyRedo will not be triggered if value was modified programmatically\n                case 'historyUndo':\n                    event.preventDefault();\n                    return this.undo();\n                case 'insertCompositionText':\n                    return; // will be handled inside `compositionend` event\n                case 'insertLineBreak':\n                case 'insertParagraph':\n                    return this.handleEnter(event);\n                case 'insertReplacementText':\n                    /**\n                     * According {@link https://www.w3.org/TR/input-events-2 W3C specification}:\n                     * > `insertReplacementText` – insert or replace existing text by means of a spell checker,\n                     * > auto-correct, writing suggestions or similar.\n                     * ___\n                     * Firefox emits `insertReplacementText` event for its suggestion/autofill and for spell checker.\n                     * However, it is impossible to detect which part of the textfield value is going to be replaced\n                     * (`selectionStart` and `selectionEnd` just equal to the last caret position).\n                     * ___\n                     * Chrome does not fire `beforeinput` event for its suggestion/autofill.\n                     * It emits only `input` event with `inputType` and `data` set to `undefined`.\n                     * ___\n                     * All these browser limitations make us to validate the result value later in `input` event.\n                     */\n                    return;\n                case 'insertFromDrop':\n                case 'insertFromPaste':\n                case 'insertText':\n                default:\n                    return this.handleInsert(event, (_c = (_a = event.data) !== null && _a !== void 0 ? _a : \n                    // `event.data` for `contentEditable` is always `null` for paste/drop events\n                    (_b = event.dataTransfer) === null || _b === void 0 ? void 0 : _b.getData('text/plain')) !== null && _c !== void 0 ? _c : '');\n            }\n        });\n        this.eventListener.listen('input', () => {\n            if (this.upcomingElementState) {\n                this.updateElementState(this.upcomingElementState);\n                this.upcomingElementState = null;\n            }\n        }, { capture: true });\n        this.eventListener.listen('input', ({ inputType }) => {\n            if (inputType === 'insertCompositionText') {\n                return; // will be handled inside `compositionend` event\n            }\n            this.ensureValueFitsMask();\n            this.updateHistory(this.elementState);\n        });\n        this.eventListener.listen('compositionend', () => {\n            this.ensureValueFitsMask();\n            this.updateHistory(this.elementState);\n        });\n    }\n    destroy() {\n        this.eventListener.destroy();\n        this.teardowns.forEach((teardown) => teardown === null || teardown === void 0 ? void 0 : teardown());\n    }\n    updateElementState({ value, selection }, eventInit) {\n        const initialValue = this.elementState.value;\n        this.updateValue(value);\n        this.updateSelectionRange(selection);\n        if (eventInit && initialValue !== value) {\n            this.dispatchInputEvent(eventInit);\n        }\n    }\n    get elementState() {\n        const { value, selectionStart, selectionEnd } = this.element;\n        return {\n            value,\n            selection: [selectionStart !== null && selectionStart !== void 0 ? selectionStart : 0, selectionEnd !== null && selectionEnd !== void 0 ? selectionEnd : 0],\n        };\n    }\n    get maxLength() {\n        const { maxLength } = this.element;\n        return maxLength === -1 ? Infinity : maxLength;\n    }\n    updateSelectionRange([from, to]) {\n        var _a;\n        const { element } = this;\n        if (element.matches(':focus') &&\n            (element.selectionStart !== from || element.selectionEnd !== to)) {\n            (_a = element.setSelectionRange) === null || _a === void 0 ? void 0 : _a.call(element, from, to);\n        }\n    }\n    updateValue(value) {\n        this.element.value = value;\n    }\n    ensureValueFitsMask() {\n        this.updateElementState(maskitoTransform(this.elementState, this.options), {\n            inputType: 'insertText',\n            data: null,\n        });\n    }\n    dispatchInputEvent(eventInit = {\n        inputType: 'insertText',\n        data: null,\n    }) {\n        if (globalThis.InputEvent) {\n            this.element.dispatchEvent(new InputEvent('input', Object.assign(Object.assign({}, eventInit), { bubbles: true, cancelable: false })));\n        }\n    }\n    handleDelete({ event, selection, isForward, force = false, }) {\n        const initialState = {\n            value: this.elementState.value,\n            selection,\n        };\n        const [initialFrom, initialTo] = initialState.selection;\n        const { elementState } = this.preprocessor({\n            elementState: initialState,\n            data: '',\n        }, isForward ? 'deleteForward' : 'deleteBackward');\n        const maskModel = new MaskModel(elementState, this.options);\n        const [from, to] = elementState.selection;\n        maskModel.deleteCharacters([from, to]);\n        const newElementState = this.postprocessor(maskModel, initialState);\n        const newPossibleState = {\n            value: initialState.value.slice(0, initialFrom) +\n                initialState.value.slice(initialTo),\n            selection: [initialFrom, initialFrom],\n        };\n        if (areElementStatesEqual(newPossibleState, newElementState) &&\n            !force &&\n            !this.element.isContentEditable) {\n            return;\n        }\n        if (areElementValuesEqual(initialState, elementState, maskModel, newElementState)) {\n            event.preventDefault();\n            // User presses Backspace/Delete for the fixed value\n            return this.updateSelectionRange(isForward ? [to, to] : [from, from]);\n        }\n        this.upcomingElementState = newElementState;\n    }\n    handleInsert(event, data) {\n        const { options, maxLength, element, elementState: initialElementState } = this;\n        const { elementState, data: insertedText = data } = this.preprocessor({\n            data,\n            elementState: initialElementState,\n        }, 'insert');\n        const maskModel = new MaskModel(elementState, options);\n        try {\n            maskModel.addCharacters(elementState.selection, insertedText);\n        }\n        catch (_a) {\n            return event.preventDefault();\n        }\n        const [from, to] = initialElementState.selection;\n        const newPossibleState = {\n            value: initialElementState.value.slice(0, from) +\n                data +\n                initialElementState.value.slice(to),\n            selection: [from + data.length, from + data.length],\n        };\n        const newElementState = this.clampState(this.postprocessor(maskModel, initialElementState));\n        if (!areElementStatesEqual(this.clampState(newPossibleState), newElementState) ||\n            element.isContentEditable) {\n            this.upcomingElementState = newElementState;\n            if (options.overwriteMode === 'replace' &&\n                newPossibleState.value.length > maxLength) {\n                /**\n                 * Browsers know nothing about Maskito and its `overwriteMode`.\n                 * When textfield value length is already equal to attribute `maxlength`,\n                 * pressing any key (even with valid value) does not emit `input` event.\n                 */\n                this.dispatchInputEvent({ inputType: 'insertText', data });\n            }\n        }\n    }\n    handleEnter(event) {\n        if (this.isTextArea || this.element.isContentEditable) {\n            this.handleInsert(event, '\\n');\n        }\n    }\n    clampState({ value, selection }) {\n        const [from, to] = selection;\n        const max = this.maxLength;\n        return {\n            value: value.slice(0, max),\n            selection: [Math.min(from, max), Math.min(to, max)],\n        };\n    }\n}\n\nexport { MASKITO_DEFAULT_ELEMENT_PREDICATE, MASKITO_DEFAULT_OPTIONS, Maskito, maskitoAdaptContentEditable, maskitoChangeEventPlugin, maskitoInitialCalibrationPlugin, maskitoPipe, maskitoStrictCompositionPlugin, maskitoTransform, maskitoUpdateElement };\n"], "mappings": "AAAA,SAASA,2BAA2BA,CAACC,OAAO,EAAE;EAC1C,MAAM;IAAEC,YAAY,GAAG,CAAC;IAAEC,WAAW,GAAG;EAAE,CAAC,GAAGF,OAAO,CAACG,aAAa,CAACC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC;EACxF,MAAMC,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACN,YAAY,EAAEC,WAAW,CAAC;EAChD,MAAMM,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACR,YAAY,EAAEC,WAAW,CAAC;EAC9C,OAAO,CAACG,IAAI,EAAEG,EAAE,CAAC;AACrB;AAEA,SAASE,2BAA2BA,CAACV,OAAO,EAAE,CAACK,IAAI,EAAEG,EAAE,CAAC,EAAE;EACtD,IAAIG,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAClB,MAAMC,QAAQ,GAAGf,OAAO,CAACG,aAAa;EACtC,MAAMa,KAAK,GAAGD,QAAQ,CAACE,WAAW,CAAC,CAAC;EACpCD,KAAK,CAACE,QAAQ,CAAClB,OAAO,CAACmB,UAAU,IAAInB,OAAO,EAAEM,IAAI,CAACC,GAAG,CAACF,IAAI,EAAE,CAACO,EAAE,GAAG,CAACD,EAAE,GAAGX,OAAO,CAACoB,WAAW,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,MAAM,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC,CAAC;EAClLI,KAAK,CAACM,MAAM,CAACtB,OAAO,CAACuB,SAAS,IAAIvB,OAAO,EAAEM,IAAI,CAACC,GAAG,CAACC,EAAE,EAAE,CAACM,EAAE,GAAG,CAACD,EAAE,GAAGb,OAAO,CAACoB,WAAW,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACQ,MAAM,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC,CAAC;EAC7K,MAAMU,SAAS,GAAGT,QAAQ,CAACX,YAAY,CAAC,CAAC;EACzC,IAAIoB,SAAS,EAAE;IACXA,SAAS,CAACC,eAAe,CAAC,CAAC;IAC3BD,SAAS,CAACE,QAAQ,CAACV,KAAK,CAAC;EAC7B;AACJ;AAEA,MAAMW,sBAAsB,CAAC;EACzBC,WAAWA,CAAC5B,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC6B,SAAS,GAAGC,QAAQ;EAC7B;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC/B,OAAO,CAACgC,SAAS,CAACC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;EACxD;EACA,IAAIF,KAAKA,CAACA,KAAK,EAAE;IACb;IACA,IAAI,CAAC/B,OAAO,CAACkC,SAAS,GAAGH,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;EACzD;EACA,IAAIE,cAAcA,CAAA,EAAG;IACjB,OAAOpC,2BAA2B,CAAC,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;EACvD;EACA,IAAIoC,YAAYA,CAAA,EAAG;IACf,OAAOrC,2BAA2B,CAAC,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;EACvD;EACAqC,iBAAiBA,CAAChC,IAAI,EAAEG,EAAE,EAAE;IACxBE,2BAA2B,CAAC,IAAI,CAACV,OAAO,EAAE,CAACK,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC,EAAEG,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC,CAAC;EACnI;EACA8B,MAAMA,CAAA,EAAG;IACL,IAAI,CAACD,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACN,KAAK,CAACV,MAAM,CAAC;EAChD;AACJ;AACA,SAASkB,2BAA2BA,CAACvC,OAAO,EAAE;EAC1C,MAAMwC,OAAO,GAAG,IAAIb,sBAAsB,CAAC3B,OAAO,CAAC;EACnD,OAAO,IAAIyC,KAAK,CAACzC,OAAO,EAAE;IACtB0C,GAAGA,CAACC,MAAM,EAAEC,IAAI,EAAE;MACd,IAAIA,IAAI,IAAIJ,OAAO,EAAE;QACjB,OAAOA,OAAO,CAACI,IAAI,CAAC;MACxB;MACA,MAAMC,cAAc,GAAGF,MAAM,CAACC,IAAI,CAAC;MACnC,OAAO,OAAOC,cAAc,KAAK,UAAU,GACrCA,cAAc,CAACC,IAAI,CAACH,MAAM,CAAC,GAC3BE,cAAc;IACxB,CAAC;IACD;IACAE,GAAGA,CAACJ,MAAM,EAAEC,IAAI,EAAEI,GAAG,EAAEC,QAAQ,EAAE;MAC7B,OAAOC,OAAO,CAACH,GAAG,CAACH,IAAI,IAAIJ,OAAO,GAAGA,OAAO,GAAGG,MAAM,EAAEC,IAAI,EAAEI,GAAG,EAAEC,QAAQ,CAAC;IAC/E;EACJ,CAAC,CAAC;AACN;AAEA,MAAME,iCAAiC,GAAIC,CAAC,IAAKA,CAAC,CAACC,iBAAiB,GAC9Dd,2BAA2B,CAACa,CAAC,CAAC,GAC9BA,CAAC,CAACE,aAAa,CAAC,gBAAgB,CAAC,IAC/BF,CAAC;AAET,MAAMG,uBAAuB,GAAG;EAC5BC,IAAI,EAAE,MAAM;EACZC,aAAa,EAAE,EAAE;EACjBC,cAAc,EAAE,EAAE;EAClBC,OAAO,EAAE,EAAE;EACXC,aAAa,EAAE;AACnB,CAAC;AAED,MAAMC,WAAW,CAAC;EACdjC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkC,GAAG,GAAG,IAAI;IACf,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,MAAM,GAAG,EAAE;EACpB;EACAC,IAAIA,CAAA,EAAG;IACH,MAAMC,KAAK,GAAG,IAAI,CAACH,IAAI,CAACI,GAAG,CAAC,CAAC;IAC7B,IAAID,KAAK,IAAI,IAAI,CAACJ,GAAG,EAAE;MACnB,IAAI,CAACE,MAAM,CAACI,IAAI,CAAC,IAAI,CAACN,GAAG,CAAC;MAC1B,IAAI,CAACO,aAAa,CAACH,KAAK,EAAE,aAAa,CAAC;IAC5C;EACJ;EACAI,IAAIA,CAAA,EAAG;IACH,MAAMJ,KAAK,GAAG,IAAI,CAACF,MAAM,CAACG,GAAG,CAAC,CAAC;IAC/B,IAAID,KAAK,IAAI,IAAI,CAACJ,GAAG,EAAE;MACnB,IAAI,CAACC,IAAI,CAACK,IAAI,CAAC,IAAI,CAACN,GAAG,CAAC;MACxB,IAAI,CAACO,aAAa,CAACH,KAAK,EAAE,aAAa,CAAC;IAC5C;EACJ;EACAK,aAAaA,CAACL,KAAK,EAAE;IACjB,IAAI,CAAC,IAAI,CAACJ,GAAG,EAAE;MACX,IAAI,CAACA,GAAG,GAAGI,KAAK;MAChB;IACJ;IACA,MAAMM,cAAc,GAAG,IAAI,CAACV,GAAG,CAAC/B,KAAK,KAAKmC,KAAK,CAACnC,KAAK;IACrD,MAAM0C,kBAAkB,GAAG,IAAI,CAACX,GAAG,CAACtC,SAAS,CAACkD,IAAI,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAKD,IAAI,KAAKT,KAAK,CAAC1C,SAAS,CAACoD,KAAK,CAAC,CAAC;IACpG,IAAI,CAACJ,cAAc,IAAI,CAACC,kBAAkB,EAAE;MACxC;IACJ;IACA,IAAID,cAAc,EAAE;MAChB,IAAI,CAACT,IAAI,CAACK,IAAI,CAAC,IAAI,CAACN,GAAG,CAAC;MACxB,IAAI,CAACE,MAAM,GAAG,EAAE;IACpB;IACA,IAAI,CAACF,GAAG,GAAGI,KAAK;EACpB;EACAG,aAAaA,CAACH,KAAK,EAAEW,SAAS,EAAE;IAC5B,IAAI,CAACf,GAAG,GAAGI,KAAK;IAChB,IAAI,CAACY,kBAAkB,CAACZ,KAAK,EAAE;MAAEW,SAAS;MAAEE,IAAI,EAAE;IAAK,CAAC,CAAC;EAC7D;AACJ;AAEA,SAASC,qBAAqBA,CAACC,WAAW,EAAE,GAAGC,MAAM,EAAE;EACnD,OAAOA,MAAM,CAACC,KAAK,CAAC,CAAC;IAAEpD;EAAM,CAAC,KAAKA,KAAK,KAAKkD,WAAW,CAAClD,KAAK,CAAC;AACnE;AACA,SAASqD,qBAAqBA,CAACH,WAAW,EAAE,GAAGC,MAAM,EAAE;EACnD,OAAOA,MAAM,CAACC,KAAK,CAAC,CAAC;IAAEpD,KAAK;IAAEP;EAAU,CAAC,KAAKO,KAAK,KAAKkD,WAAW,CAAClD,KAAK,IACrEP,SAAS,CAAC,CAAC,CAAC,KAAKyD,WAAW,CAACzD,SAAS,CAAC,CAAC,CAAC,IACzCA,SAAS,CAAC,CAAC,CAAC,KAAKyD,WAAW,CAACzD,SAAS,CAAC,CAAC,CAAC,CAAC;AAClD;AAEA,SAAS6D,kBAAkBA,CAAC;EAAEtD,KAAK;EAAEP;AAAU,CAAC,EAAE8D,aAAa,EAAEC,IAAI,EAAE;EACnE,MAAM,CAAClF,IAAI,EAAEG,EAAE,CAAC,GAAGgB,SAAS;EAC5B,MAAMgE,YAAY,GAAG,OAAOD,IAAI,KAAK,UAAU,GAAGA,IAAI,CAAC;IAAExD,KAAK;IAAEP;EAAU,CAAC,CAAC,GAAG+D,IAAI;EACnF,OAAO;IACHxD,KAAK;IACLP,SAAS,EAAEgE,YAAY,KAAK,SAAS,GAC/B,CAACnF,IAAI,EAAEC,IAAI,CAACG,GAAG,CAACJ,IAAI,GAAGiF,aAAa,CAACjE,MAAM,EAAEb,EAAE,CAAC,CAAC,GACjD,CAACH,IAAI,EAAEG,EAAE;EACnB,CAAC;AACL;AAEA,SAASiF,gBAAgBA,CAACC,IAAI,EAAE;EAC5B,OAAO,OAAOA,IAAI,KAAK,QAAQ;AACnC;;AAEA;AACA,SAASC,yBAAyBA,CAACnC,IAAI,EAAEoC,kBAAkB,EAAEC,YAAY,EAAEC,mBAAmB,EAAE;EAC5F,IAAIC,sBAAsB,GAAG,EAAE;EAC/B,KAAK,IAAIC,CAAC,GAAGJ,kBAAkB,CAACvE,MAAM,EAAE2E,CAAC,GAAGxC,IAAI,CAACnC,MAAM,EAAE2E,CAAC,EAAE,EAAE;IAC1D,MAAMC,cAAc,GAAGzC,IAAI,CAACwC,CAAC,CAAC,IAAI,EAAE;IACpC,MAAME,kBAAkB,GAAG,CAACJ,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAAC/D,KAAK,CAACiE,CAAC,CAAC,MAAMC,cAAc;IACtJ,IAAI,CAACR,gBAAgB,CAACQ,cAAc,CAAC,IAChCA,cAAc,KAAKJ,YAAY,IAAI,CAACK,kBAAmB,EAAE;MAC1D,OAAOH,sBAAsB;IACjC;IACAA,sBAAsB,IAAIE,cAAc;EAC5C;EACA,OAAOF,sBAAsB;AACjC;AAEA,SAASI,qBAAqBA,CAACpE,KAAK,EAAEqE,cAAc,EAAE;EAClD,IAAIC,KAAK,CAACC,OAAO,CAACF,cAAc,CAAC,EAAE;IAC/B,OAAQrE,KAAK,CAACV,MAAM,KAAK+E,cAAc,CAAC/E,MAAM,IAC1CgF,KAAK,CAAChG,IAAI,CAAC0B,KAAK,CAAC,CAACoD,KAAK,CAAC,CAACO,IAAI,EAAEM,CAAC,KAAK;MACjC,MAAMC,cAAc,GAAGG,cAAc,CAACJ,CAAC,CAAC,IAAI,EAAE;MAC9C,OAAOP,gBAAgB,CAACQ,cAAc,CAAC,GACjCP,IAAI,KAAKO,cAAc,GACvBP,IAAI,CAACa,KAAK,CAACN,cAAc,CAAC;IACpC,CAAC,CAAC;EACV;EACA,OAAOG,cAAc,CAACI,IAAI,CAACzE,KAAK,CAAC;AACrC;AAEA,SAAS0E,wBAAwBA,CAACC,YAAY,EAAElD,IAAI,EAAEsC,mBAAmB,EAAE;EACvE,IAAIa,UAAU,GAAG,IAAI;EACrB,IAAIC,QAAQ,GAAG,IAAI;EACnB,MAAMC,WAAW,GAAGR,KAAK,CAAChG,IAAI,CAACqG,YAAY,CAAC3E,KAAK,CAAC,CAAC+E,MAAM,CAAC,CAACC,mBAAmB,EAAErB,IAAI,EAAEsB,SAAS,KAAK;IAChG,MAAMC,iBAAiB,GAAGtB,yBAAyB,CAACnC,IAAI,EAAEuD,mBAAmB,EAAErB,IAAI,EAAEI,mBAAmB,CAAC;IACzG,MAAMoB,iBAAiB,GAAGH,mBAAmB,GAAGE,iBAAiB;IACjE,MAAMhB,cAAc,GAAGzC,IAAI,CAAC0D,iBAAiB,CAAC7F,MAAM,CAAC,IAAI,EAAE;IAC3D,IAAIsF,UAAU,KAAK,IAAI,IAAIK,SAAS,IAAIN,YAAY,CAAClF,SAAS,CAAC,CAAC,CAAC,EAAE;MAC/DmF,UAAU,GAAGO,iBAAiB,CAAC7F,MAAM;IACzC;IACA,IAAIuF,QAAQ,KAAK,IAAI,IAAII,SAAS,IAAIN,YAAY,CAAClF,SAAS,CAAC,CAAC,CAAC,EAAE;MAC7DoF,QAAQ,GAAGM,iBAAiB,CAAC7F,MAAM;IACvC;IACA,IAAIoE,gBAAgB,CAACQ,cAAc,CAAC,EAAE;MAClC,OAAOiB,iBAAiB,GAAGjB,cAAc;IAC7C;IACA,OAAOP,IAAI,CAACa,KAAK,CAACN,cAAc,CAAC,GAC3BiB,iBAAiB,GAAGxB,IAAI,GACxBwB,iBAAiB;EAC3B,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,uBAAuB,GAAGxB,yBAAyB,CAACnC,IAAI,EAAEqD,WAAW,EAAE,EAAE,EAAEf,mBAAmB,CAAC;EACrG,OAAO;IACH/D,KAAK,EAAEoE,qBAAqB,CAACU,WAAW,GAAGM,uBAAuB,EAAE3D,IAAI,CAAC,GACnEqD,WAAW,GAAGM,uBAAuB,GACrCN,WAAW;IACjBrF,SAAS,EAAE,CAACmF,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGE,WAAW,CAACxF,MAAM,EAAEuF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGC,WAAW,CAACxF,MAAM;EACxK,CAAC;AACL;AAEA,SAAS+F,uBAAuBA,CAAC;EAAErF,KAAK;EAAEP;AAAU,CAAC,EAAE6F,UAAU,EAAE;EAC/D,MAAM,CAAChH,IAAI,EAAEG,EAAE,CAAC,GAAGgB,SAAS;EAC5B,IAAI8F,OAAO,GAAGjH,IAAI;EAClB,IAAIkH,KAAK,GAAG/G,EAAE;EACd,MAAMgH,cAAc,GAAGnB,KAAK,CAAChG,IAAI,CAAC0B,KAAK,CAAC,CAAC+E,MAAM,CAAC,CAAClB,kBAAkB,EAAEF,IAAI,EAAEM,CAAC,KAAK;IAC7E,MAAMyB,gBAAgB,GAAG7B,kBAAkB,GAAGF,IAAI;IAClD,IAAIrF,IAAI,KAAK2F,CAAC,EAAE;MACZsB,OAAO,GAAG1B,kBAAkB,CAACvE,MAAM;IACvC;IACA,IAAIb,EAAE,KAAKwF,CAAC,EAAE;MACVuB,KAAK,GAAG3B,kBAAkB,CAACvE,MAAM;IACrC;IACA,OAAOoG,gBAAgB,CAAClB,KAAK,CAACc,UAAU,CAAC,GAAGI,gBAAgB,GAAG7B,kBAAkB;EACrF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO;IACH7D,KAAK,EAAEyF,cAAc;IACrBhG,SAAS,EAAE,CACPlB,IAAI,CAACC,GAAG,CAAC+G,OAAO,EAAEE,cAAc,CAACnG,MAAM,CAAC,EACxCf,IAAI,CAACC,GAAG,CAACgH,KAAK,EAAEC,cAAc,CAACnG,MAAM,CAAC;EAE9C,CAAC;AACL;AAEA,SAASqG,oBAAoBA,CAAChB,YAAY,EAAElD,IAAI,EAAEsC,mBAAmB,GAAG,IAAI,EAAE;EAC1E,IAAIK,qBAAqB,CAACO,YAAY,CAAC3E,KAAK,EAAEyB,IAAI,CAAC,EAAE;IACjD,OAAOkD,YAAY;EACvB;EACA,MAAM;IAAE3E,KAAK;IAAEP;EAAU,CAAC,GAAG6E,KAAK,CAACC,OAAO,CAAC9C,IAAI,CAAC,GAC1CiD,wBAAwB,CAACC,YAAY,EAAElD,IAAI,EAAEsC,mBAAmB,CAAC,GACjEsB,uBAAuB,CAACV,YAAY,EAAElD,IAAI,CAAC;EACjD,OAAO;IACHhC,SAAS;IACTO,KAAK,EAAEsE,KAAK,CAACC,OAAO,CAAC9C,IAAI,CAAC,GAAGzB,KAAK,CAAC4F,KAAK,CAAC,CAAC,EAAEnE,IAAI,CAACnC,MAAM,CAAC,GAAGU;EAC/D,CAAC;AACL;AAEA,SAAS6F,yBAAyBA,CAAC9B,mBAAmB,EAAEtC,IAAI,EAAE;EAC1D,IAAI,CAAC6C,KAAK,CAACC,OAAO,CAAC9C,IAAI,CAAC,EAAE;IACtB,OAAOsC,mBAAmB;EAC9B;EACA,MAAM,CAACzF,IAAI,EAAEG,EAAE,CAAC,GAAGsF,mBAAmB,CAACtE,SAAS;EAChD,MAAMA,SAAS,GAAG,EAAE;EACpB,MAAMqG,aAAa,GAAGxB,KAAK,CAAChG,IAAI,CAACyF,mBAAmB,CAAC/D,KAAK,CAAC,CAAC+E,MAAM,CAAC,CAACgB,QAAQ,EAAEpC,IAAI,EAAEM,CAAC,KAAK;IACtF,MAAMC,cAAc,GAAGzC,IAAI,CAACwC,CAAC,CAAC,IAAI,EAAE;IACpC,IAAIA,CAAC,KAAK3F,IAAI,EAAE;MACZmB,SAAS,CAAC4C,IAAI,CAAC0D,QAAQ,CAACzG,MAAM,CAAC;IACnC;IACA,IAAI2E,CAAC,KAAKxF,EAAE,EAAE;MACVgB,SAAS,CAAC4C,IAAI,CAAC0D,QAAQ,CAACzG,MAAM,CAAC;IACnC;IACA,OAAOoE,gBAAgB,CAACQ,cAAc,CAAC,IAAIA,cAAc,KAAKP,IAAI,GAC5DoC,QAAQ,GACRA,QAAQ,GAAGpC,IAAI;EACzB,CAAC,EAAE,EAAE,CAAC;EACN,IAAIlE,SAAS,CAACH,MAAM,GAAG,CAAC,EAAE;IACtBG,SAAS,CAAC4C,IAAI,CAAC,GAAG,IAAIiC,KAAK,CAAC,CAAC,GAAG7E,SAAS,CAACH,MAAM,CAAC,CAAC0G,IAAI,CAACF,aAAa,CAACxG,MAAM,CAAC,CAAC;EACjF;EACA,OAAO;IACHU,KAAK,EAAE8F,aAAa;IACpBrG,SAAS,EAAE,CAACA,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC;EAC1C,CAAC;AACL;AAEA,MAAMwG,SAAS,CAAC;EACZpG,WAAWA,CAACkE,mBAAmB,EAAEmC,WAAW,EAAE;IAC1C,IAAI,CAACnC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACmC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAClG,KAAK,GAAG,EAAE;IACf,IAAI,CAACP,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACvB,MAAM;MAAEO,KAAK;MAAEP;IAAU,CAAC,GAAGkG,oBAAoB,CAAC,IAAI,CAAC5B,mBAAmB,EAAE,IAAI,CAACoC,iBAAiB,CAAC,IAAI,CAACpC,mBAAmB,CAAC,CAAC;IAC7H,IAAI,CAAC/D,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACP,SAAS,GAAGA,SAAS;EAC9B;EACA2G,aAAaA,CAAC,CAAC9H,IAAI,EAAEG,EAAE,CAAC,EAAE8E,aAAa,EAAE;IACrC,MAAM;MAAEvD,KAAK;MAAEkG;IAAY,CAAC,GAAG,IAAI;IACnC,MAAM7B,cAAc,GAAG,IAAI,CAAC8B,iBAAiB,CAAC;MAC1CnG,KAAK,EAAEA,KAAK,CAAC4F,KAAK,CAAC,CAAC,EAAEtH,IAAI,CAAC,GAAGiF,aAAa,GAAGvD,KAAK,CAAC4F,KAAK,CAACnH,EAAE,CAAC;MAC7DgB,SAAS,EAAE,CAACnB,IAAI,GAAGiF,aAAa,CAACjE,MAAM,EAAEhB,IAAI,GAAGiF,aAAa,CAACjE,MAAM;IACxE,CAAC,CAAC;IACF,MAAMyE,mBAAmB,GAAG;MAAE/D,KAAK;MAAEP,SAAS,EAAE,CAACnB,IAAI,EAAEG,EAAE;IAAE,CAAC;IAC5D,MAAM4H,oBAAoB,GAAGR,yBAAyB,CAAC9B,mBAAmB,EAAEM,cAAc,CAAC;IAC3F,MAAM,CAACiC,YAAY,EAAEC,UAAU,CAAC,GAAGjD,kBAAkB,CAAC+C,oBAAoB,EAAE9C,aAAa,EAAE2C,WAAW,CAACrE,aAAa,CAAC,CAACpC,SAAS;IAC/H,MAAM+G,2BAA2B,GAAGH,oBAAoB,CAACrG,KAAK,CAAC4F,KAAK,CAAC,CAAC,EAAEU,YAAY,CAAC,GAAG/C,aAAa;IACrG,MAAMkD,aAAa,GAAGD,2BAA2B,CAAClH,MAAM;IACxD,MAAMoH,kBAAkB,GAAGf,oBAAoB,CAAC;MAC5C3F,KAAK,EAAEwG,2BAA2B,GAC9BH,oBAAoB,CAACrG,KAAK,CAAC4F,KAAK,CAACW,UAAU,CAAC;MAChD9G,SAAS,EAAE,CAACgH,aAAa,EAAEA,aAAa;IAC5C,CAAC,EAAEpC,cAAc,EAAEN,mBAAmB,CAAC;IACvC,MAAM4C,uBAAuB,GAAG3G,KAAK,CAAC4F,KAAK,CAAC,CAAC,EAAEtH,IAAI,CAAC,KAChDqH,oBAAoB,CAAC;MACjB3F,KAAK,EAAEwG,2BAA2B;MAClC/G,SAAS,EAAE,CAACgH,aAAa,EAAEA,aAAa;IAC5C,CAAC,EAAEpC,cAAc,EAAEN,mBAAmB,CAAC,CAAC/D,KAAK;IACjD,IAAI2G,uBAAuB,IACvBtD,qBAAqB,CAAC,IAAI,EAAEqD,kBAAkB,CAAC,CAAC;IAAA,EAClD;MACE,MAAM,IAAIE,KAAK,CAAC,oBAAoB,CAAC;IACzC;IACA,IAAI,CAAC5G,KAAK,GAAG0G,kBAAkB,CAAC1G,KAAK;IACrC,IAAI,CAACP,SAAS,GAAGiH,kBAAkB,CAACjH,SAAS;EACjD;EACAoH,gBAAgBA,CAAC,CAACvI,IAAI,EAAEG,EAAE,CAAC,EAAE;IACzB,IAAIH,IAAI,KAAKG,EAAE,IAAI,CAACA,EAAE,EAAE;MACpB;IACJ;IACA,MAAM;MAAEuB;IAAM,CAAC,GAAG,IAAI;IACtB,MAAMqE,cAAc,GAAG,IAAI,CAAC8B,iBAAiB,CAAC;MAC1CnG,KAAK,EAAEA,KAAK,CAAC4F,KAAK,CAAC,CAAC,EAAEtH,IAAI,CAAC,GAAG0B,KAAK,CAAC4F,KAAK,CAACnH,EAAE,CAAC;MAC7CgB,SAAS,EAAE,CAACnB,IAAI,EAAEA,IAAI;IAC1B,CAAC,CAAC;IACF,MAAMyF,mBAAmB,GAAG;MAAE/D,KAAK;MAAEP,SAAS,EAAE,CAACnB,IAAI,EAAEG,EAAE;IAAE,CAAC;IAC5D,MAAM4H,oBAAoB,GAAGR,yBAAyB,CAAC9B,mBAAmB,EAAEM,cAAc,CAAC;IAC3F,MAAM,CAACiC,YAAY,EAAEC,UAAU,CAAC,GAAGF,oBAAoB,CAAC5G,SAAS;IACjE,MAAMqH,gBAAgB,GAAGT,oBAAoB,CAACrG,KAAK,CAAC4F,KAAK,CAAC,CAAC,EAAEU,YAAY,CAAC,GACtED,oBAAoB,CAACrG,KAAK,CAAC4F,KAAK,CAACW,UAAU,CAAC;IAChD,MAAMG,kBAAkB,GAAGf,oBAAoB,CAAC;MAAE3F,KAAK,EAAE8G,gBAAgB;MAAErH,SAAS,EAAE,CAAC6G,YAAY,EAAEA,YAAY;IAAE,CAAC,EAAEjC,cAAc,EAAEN,mBAAmB,CAAC;IAC1J,IAAI,CAAC/D,KAAK,GAAG0G,kBAAkB,CAAC1G,KAAK;IACrC,IAAI,CAACP,SAAS,GAAGiH,kBAAkB,CAACjH,SAAS;EACjD;EACA0G,iBAAiBA,CAACxB,YAAY,EAAE;IAC5B,MAAM;MAAElD;IAAK,CAAC,GAAG,IAAI,CAACyE,WAAW;IACjC,OAAO,OAAOzE,IAAI,KAAK,UAAU,GAAGA,IAAI,CAACkD,YAAY,CAAC,GAAGlD,IAAI;EACjE;AACJ;AAEA,SAASsF,wBAAwBA,CAAA,EAAG;EAChC,OAAQ9I,OAAO,IAAK;IAChB,IAAIA,OAAO,CAACqD,iBAAiB,EAAE;MAC3B;IACJ;IACA,IAAItB,KAAK,GAAG/B,OAAO,CAAC+B,KAAK;IACzB,MAAMgH,aAAa,GAAGA,CAAA,KAAM;MACxBhH,KAAK,GAAG/B,OAAO,CAAC+B,KAAK;IACzB,CAAC;IACD,MAAMiH,YAAY,GAAGA,CAAA,KAAM;MACvB,IAAIhJ,OAAO,CAAC+B,KAAK,KAAKA,KAAK,EAAE;QACzB/B,OAAO,CAACiJ,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,EAAE;UAAEC,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC;MACjE;IACJ,CAAC;IACDnJ,OAAO,CAACoJ,gBAAgB,CAAC,OAAO,EAAEL,aAAa,CAAC;IAChD/I,OAAO,CAACoJ,gBAAgB,CAAC,QAAQ,EAAEL,aAAa,CAAC;IACjD/I,OAAO,CAACoJ,gBAAgB,CAAC,MAAM,EAAEJ,YAAY,CAAC;IAC9C,OAAO,MAAM;MACThJ,OAAO,CAACqJ,mBAAmB,CAAC,OAAO,EAAEN,aAAa,CAAC;MACnD/I,OAAO,CAACqJ,mBAAmB,CAAC,QAAQ,EAAEN,aAAa,CAAC;MACpD/I,OAAO,CAACqJ,mBAAmB,CAAC,MAAM,EAAEL,YAAY,CAAC;IACrD,CAAC;EACL,CAAC;AACL;AAEA,MAAMM,aAAa,CAAC;EAChB1H,WAAWA,CAAC5B,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACuJ,SAAS,GAAG,EAAE;EACvB;EACAC,MAAMA,CAACC,SAAS,EAAEC,EAAE,EAAEC,OAAO,EAAE;IAC3B,MAAMC,SAAS,GAAGF,EAAE;IACpB,IAAI,CAAC1J,OAAO,CAACoJ,gBAAgB,CAACK,SAAS,EAAEG,SAAS,EAAED,OAAO,CAAC;IAC5D,IAAI,CAACJ,SAAS,CAACnF,IAAI,CAAC,MAAM,IAAI,CAACpE,OAAO,CAACqJ,mBAAmB,CAACI,SAAS,EAAEG,SAAS,EAAED,OAAO,CAAC,CAAC;EAC9F;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,CAACN,SAAS,CAACO,OAAO,CAAEC,UAAU,IAAKA,UAAU,CAAC,CAAC,CAAC;EACxD;AACJ;AAEA,MAAMC,cAAc,GAAG;EACnBC,IAAI,EAAE,CAAC,IAAI,CAAC;EACZC,GAAG,EAAE,CAAC,IAAI,CAAC;EACXC,KAAK,EAAE,CAAC,IAAI,CAAC;EACbC,IAAI,EAAE,CAAC,IAAI;AACf,CAAC;AACD;AACA,MAAMC,UAAU,GAAG;EACfC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE;AACP,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAEC,SAAS,EAAEC,UAAU,EAAE;EAC5C,OAAQF,KAAK,CAACG,OAAO,KAAK,CAAC,EAAEF,SAAS,GAAGV,cAAc,CAACC,IAAI,CAAC,IACzDQ,KAAK,CAACI,MAAM,KAAK,CAAC,EAAEH,SAAS,GAAGV,cAAc,CAACE,GAAG,CAAC,IACnDO,KAAK,CAACK,QAAQ,KAAK,CAAC,EAAEJ,SAAS,GAAGV,cAAc,CAACG,KAAK,CAAC,IACvDM,KAAK,CAACM,OAAO,KAAK,CAAC,EAAEL,SAAS,GAAGV,cAAc,CAACI,IAAI,CAAC;EACrD;AACR;AACA;AACA;AACA;EACQK,KAAK,CAACO,OAAO,KAAKL,UAAU;AACpC;AAEA,SAASM,MAAMA,CAACR,KAAK,EAAE;EACnB,OAAQD,QAAQ,CAACC,KAAK,EAAET,cAAc,CAACC,IAAI,EAAEI,UAAU,CAACC,CAAC,CAAC;EAAI;EAC1DE,QAAQ,CAACC,KAAK,EAAET,cAAc,CAACC,IAAI,GAAGD,cAAc,CAACG,KAAK,EAAEE,UAAU,CAACE,CAAC,CAAC;EAAI;EAC7EC,QAAQ,CAACC,KAAK,EAAET,cAAc,CAACI,IAAI,GAAGJ,cAAc,CAACG,KAAK,EAAEE,UAAU,CAACE,CAAC,CAAC,CAAC;EAAA;AAElF;AACA,SAASW,MAAMA,CAACT,KAAK,EAAE;EACnB,OAAQD,QAAQ,CAACC,KAAK,EAAET,cAAc,CAACC,IAAI,EAAEI,UAAU,CAACE,CAAC,CAAC;EAAI;EAC1DC,QAAQ,CAACC,KAAK,EAAET,cAAc,CAACI,IAAI,EAAEC,UAAU,CAACE,CAAC,CAAC,CAAC;EAAA;AAE3D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,oBAAoBA,CAACnL,OAAO,EAAEoL,mBAAmB,EAAE;EACxD,IAAIzK,EAAE;EACN,MAAM0K,YAAY,GAAGrL,OAAO,CAAC+B,KAAK;EAClC,IAAI,OAAOqJ,mBAAmB,KAAK,QAAQ,EAAE;IACzCpL,OAAO,CAAC+B,KAAK,GAAGqJ,mBAAmB;EACvC,CAAC,MACI;IACD,MAAM,CAAC/K,IAAI,EAAEG,EAAE,CAAC,GAAG4K,mBAAmB,CAAC5J,SAAS;IAChDxB,OAAO,CAAC+B,KAAK,GAAGqJ,mBAAmB,CAACrJ,KAAK;IACzC,IAAI/B,OAAO,CAACsL,OAAO,CAAC,QAAQ,CAAC,EAAE;MAC3B,CAAC3K,EAAE,GAAGX,OAAO,CAACqC,iBAAiB,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4K,IAAI,CAACvL,OAAO,EAAEK,IAAI,EAAEG,EAAE,CAAC;IACpG;EACJ;EACA,IAAIR,OAAO,CAAC+B,KAAK,KAAKsJ,YAAY,EAAE;IAChCrL,OAAO,CAACiJ,aAAa,CAAC,IAAIC,KAAK,CAAC,OAAO;IACvC;AACR;AACA;AACA;AACA;AACA;IACQ;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC;EACvB;AACJ;AAEA,SAASqC,gBAAgBA,CAAC;EAAEzJ,KAAK;EAAEP;AAAU,CAAC,EAAEiK,SAAS,EAAE;EACvD,MAAM,CAACpL,IAAI,EAAEG,EAAE,CAAC,GAAGgB,SAAS;EAC5B,IAAInB,IAAI,KAAKG,EAAE,EAAE;IACb,OAAO,CAACH,IAAI,EAAEG,EAAE,CAAC;EACrB;EACA,MAAMkL,YAAY,GAAGD,SAAS,GACxB1J,KAAK,CAAC4F,KAAK,CAACtH,IAAI,CAAC,CAACsL,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI5J,KAAK,CAACV,MAAM,GACnDU,KAAK,CAAC4F,KAAK,CAAC,CAAC,EAAEnH,EAAE,CAAC,CAACoL,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC;EAC9C,MAAMC,UAAU,GAAGJ,SAAS,GAAGpL,IAAI,GAAGqL,YAAY;EAClD,MAAMI,QAAQ,GAAGL,SAAS,GAAGC,YAAY,GAAGlL,EAAE;EAC9C,OAAO,CAACqL,UAAU,EAAEC,QAAQ,CAAC;AACjC;AAEA,SAASC,oBAAoBA,CAAC;EAAEhK,KAAK;EAAEP;AAAU,CAAC,EAAEiK,SAAS,EAAE;EAC3D,MAAM,CAACpL,IAAI,EAAEG,EAAE,CAAC,GAAGgB,SAAS;EAC5B,IAAInB,IAAI,KAAKG,EAAE,EAAE;IACb,OAAO,CAACH,IAAI,EAAEG,EAAE,CAAC;EACrB;EACA,MAAMwL,iBAAiB,GAAGP,SAAS,GAAG,CAACpL,IAAI,EAAEG,EAAE,GAAG,CAAC,CAAC,GAAG,CAACH,IAAI,GAAG,CAAC,EAAEG,EAAE,CAAC;EACrE,OAAOwL,iBAAiB,CAACC,GAAG,CAAEC,CAAC,IAAK5L,IAAI,CAACC,GAAG,CAACD,IAAI,CAACG,GAAG,CAACyL,CAAC,EAAE,CAAC,CAAC,EAAEnK,KAAK,CAACV,MAAM,CAAC,CAAC;AAC/E;AAEA,MAAM8K,mBAAmB,GAAG,OAAO;AACnC,MAAMC,kBAAkB,GAAG,OAAO;AAClC,MAAMC,SAAS,GAAG,IAAI;AACtB,SAASC,gBAAgBA,CAAC;EAAEvK,KAAK;EAAEP;AAAU,CAAC,EAAEiK,SAAS,EAAE;EACvD,MAAM,CAACpL,IAAI,EAAEG,EAAE,CAAC,GAAGgB,SAAS;EAC5B,IAAInB,IAAI,KAAKG,EAAE,EAAE;IACb,OAAO,CAACH,IAAI,EAAEG,EAAE,CAAC;EACrB;EACA,IAAIiL,SAAS,EAAE;IACX,MAAMc,wBAAwB,GAAGxK,KAAK,CAAC4F,KAAK,CAACtH,IAAI,CAAC;IAClD,MAAM,CAACmM,aAAa,CAAC,GAAGD,wBAAwB,CAAChG,KAAK,CAAC6F,kBAAkB,CAAC,IAAI,CAC1E,EAAE,CACL;IACD,MAAMK,mBAAmB,GAAGF,wBAAwB,CAC/CG,SAAS,CAAC,CAAC,CACXC,MAAM,CAACN,SAAS,CAAC;IACtB,OAAO,CACHhM,IAAI,EACJoM,mBAAmB,KAAK,CAAC,CAAC,GACpBpM,IAAI,GAAGmM,aAAa,CAACnL,MAAM,GAAGoL,mBAAmB,GACjD1K,KAAK,CAACV,MAAM,CACrB;EACL;EACA,MAAMuL,uBAAuB,GAAG7K,KAAK,CAAC4F,KAAK,CAAC,CAAC,EAAEnH,EAAE,CAAC;EAClD,MAAM,CAACqM,cAAc,CAAC,GAAGD,uBAAuB,CAACrG,KAAK,CAAC4F,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;EACnF,MAAMW,kBAAkB,GAAGF,uBAAuB,CAC7CG,OAAO,CAAC,CAAC,CACTC,KAAK,CAAC,EAAE,CAAC,CACTC,OAAO,CAAC,CAAC,CACTC,SAAS,CAAExH,IAAI,IAAK2G,SAAS,CAACc,IAAI,CAACzH,IAAI,CAAC,CAAC;EAC9C,OAAO,CACHoH,kBAAkB,KAAK,CAAC,CAAC,GAAGtM,EAAE,GAAGqM,cAAc,CAACxL,MAAM,GAAGyL,kBAAkB,GAAG,CAAC,EAC/EtM,EAAE,CACL;AACL;;AAEA;AACA;AACA;AACA;AACA,SAAS4M,WAAWA,CAACC,UAAU,GAAG,EAAE,EAAE;EAClC,OAAO,CAACC,WAAW,EAAE,GAAGC,YAAY,KAAKF,UAAU,CAACvG,MAAM,CAAC,CAAC/B,IAAI,EAAE2E,EAAE,KAAM8D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE1I,IAAI,CAAC,EAAE2E,EAAE,CAAC3E,IAAI,EAAE,GAAGwI,YAAY,CAAC,CAAE,EAAED,WAAW,CAAC;AAC9J;AAEA,SAASI,gBAAgBA,CAACC,YAAY,EAAEC,cAAc,EAAE;EACpD,MAAMjE,OAAO,GAAG6D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElK,uBAAuB,CAAC,EAAEqK,cAAc,CAAC;EACzF,MAAMC,YAAY,GAAGT,WAAW,CAACzD,OAAO,CAAClG,aAAa,CAAC;EACvD,MAAMqK,aAAa,GAAGV,WAAW,CAACzD,OAAO,CAACjG,cAAc,CAAC;EACzD,MAAMoC,mBAAmB,GAAG,OAAO6H,YAAY,KAAK,QAAQ,GACtD;IAAE5L,KAAK,EAAE4L,YAAY;IAAEnM,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;EAAE,CAAC,GAC1CmM,YAAY;EAClB,MAAM;IAAEjH;EAAa,CAAC,GAAGmH,YAAY,CAAC;IAAEnH,YAAY,EAAEZ,mBAAmB;IAAEf,IAAI,EAAE;EAAG,CAAC,EAAE,YAAY,CAAC;EACpG,MAAMgJ,SAAS,GAAG,IAAI/F,SAAS,CAACtB,YAAY,EAAEiD,OAAO,CAAC;EACtD,MAAM;IAAE5H,KAAK;IAAEP;EAAU,CAAC,GAAGsM,aAAa,CAACC,SAAS,EAAEjI,mBAAmB,CAAC;EAC1E,OAAO,OAAO6H,YAAY,KAAK,QAAQ,GAAG5L,KAAK,GAAG;IAAEA,KAAK;IAAEP;EAAU,CAAC;AAC1E;AAEA,MAAMwM,KAAK,GAAG,GAAG;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,uBAAuBA,CAAA,EAAG;EAC/B,IAAIC,SAAS,GAAG,EAAE;EAClB,IAAIC,cAAc,GAAG,CAAC;EACtB,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,OAAQrO,OAAO,IAAK;IAChB,MAAMsO,aAAa,GAAG,IAAIhF,aAAa,CAACtJ,OAAO,CAAC;IAChDsO,aAAa,CAAC9E,MAAM,CAAC,aAAa,EAAGiB,KAAK,IAAK;MAC3C,IAAI9J,EAAE,EAAEC,EAAE;MACV,MAAM;QAAEmB,KAAK;QAAEI,cAAc;QAAEC;MAAa,CAAC,GAAGpC,OAAO;MACvD,MAAMuO,aAAa,GAAG,CAACH,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACvJ,SAAS,MAAM,YAAY,IAC9G,CAACuJ,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACrJ,IAAI,MAAMiJ,KAAK,IAChF,CAACjM,KAAK,CAAC4F,KAAK,CAAC,CAAC,EAAE6G,MAAM,CAACpM,YAAY,CAAC,CAAC,CAACqM,QAAQ,CAACT,KAAK,CAAC;MACzD,IAAIvD,KAAK,CAAC5F,SAAS,KAAK,YAAY,IAAI4F,KAAK,CAAC1F,IAAI,KAAK,IAAIiJ,KAAK,EAAE,EAAE;QAChE,IAAI,CAACI,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACvJ,SAAS,MAAM,uBAAuB,IACvGwJ,iBAAiB,EAAE;UACnB;UACArO,OAAO,CAAC+B,KAAK,GAAGmM,SAAS;UACzB,CAACvN,EAAE,GAAGX,OAAO,CAACqC,iBAAiB,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4K,IAAI,CAACvL,OAAO,EAAEmO,cAAc,EAAEA,cAAc,CAAC;QAC1H,CAAC,MACI,IAAII,aAAa,EAAE;UACpB;UACA,CAAC3N,EAAE,GAAGZ,OAAO,CAACqC,iBAAiB,MAAM,IAAI,IAAIzB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2K,IAAI,CAACvL,OAAO,EAAEmC,cAAc,EAAEA,cAAc,CAAC;QAC1H;MACJ;MACAkM,iBAAiB,GAAGE,aAAa;MACjCH,SAAS,GAAG3D,KAAK;MACjByD,SAAS,GAAGnM,KAAK;MACjBoM,cAAc,GAAGK,MAAM,CAAC,CAACD,aAAa,GAAGJ,cAAc,GAAG/L,YAAY,MAAML,KAAK,CAACV,MAAM,GAClFe,YAAY,GACZD,cAAc,CAAC;IACzB,CAAC,CAAC;IACF,OAAO,MAAMmM,aAAa,CAACzE,OAAO,CAAC,CAAC;EACxC,CAAC;AACL;AAEA,SAAS6E,+BAA+BA,CAACC,aAAa,EAAE;EACpD,OAAO,CAAC3O,OAAO,EAAE2J,OAAO,KAAK;IACzB,IAAIhJ,EAAE,EAAEC,EAAE;IACV,MAAMP,IAAI,GAAG,CAACM,EAAE,GAAGX,OAAO,CAACmC,cAAc,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAC7E,MAAMH,EAAE,GAAG,CAACI,EAAE,GAAGZ,OAAO,CAACoC,YAAY,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IACzEuK,oBAAoB,CAACnL,OAAO,EAAE;MAC1B+B,KAAK,EAAE2L,gBAAgB,CAAC1N,OAAO,CAAC+B,KAAK,EAAE4M,aAAa,IAAIhF,OAAO,CAAC;MAChEnI,SAAS,EAAE,CAACnB,IAAI,EAAEG,EAAE;IACxB,CAAC,CAAC;EACN,CAAC;AACL;AAEA,SAASoO,8BAA8BA,CAAA,EAAG;EACtC,OAAO,CAAC5O,OAAO,EAAE4N,cAAc,KAAK;IAChC,MAAMiB,QAAQ,GAAIpE,KAAK,IAAK;MACxB,IAAI9J,EAAE,EAAEC,EAAE;MACV,IAAI6J,KAAK,CAAC5F,SAAS,KAAK,uBAAuB,EAAE;QAC7C;MACJ;MACA,MAAMrD,SAAS,GAAG,CACd,CAACb,EAAE,GAAGX,OAAO,CAACmC,cAAc,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,EAChE,CAACC,EAAE,GAAGZ,OAAO,CAACoC,YAAY,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CACjE;MACD,MAAM8F,YAAY,GAAG;QACjBlF,SAAS;QACTO,KAAK,EAAE/B,OAAO,CAAC+B;MACnB,CAAC;MACD,MAAM+M,cAAc,GAAGpB,gBAAgB,CAAChH,YAAY,EAAEkH,cAAc,CAAC;MACrE,IAAI,CAACxI,qBAAqB,CAACsB,YAAY,EAAEoI,cAAc,CAAC,EAAE;QACtDrE,KAAK,CAACsE,cAAc,CAAC,CAAC;QACtB5D,oBAAoB,CAACnL,OAAO,EAAE8O,cAAc,CAAC;MACjD;IACJ,CAAC;IACD9O,OAAO,CAACoJ,gBAAgB,CAAC,OAAO,EAAEyF,QAAQ,CAAC;IAC3C,OAAO,MAAM7O,OAAO,CAACqJ,mBAAmB,CAAC,OAAO,EAAEwF,QAAQ,CAAC;EAC/D,CAAC;AACL;AAEA,MAAMG,gBAAgB,GAAG,CAACf,uBAAuB,CAAC,CAAC,CAAC;AACpD,MAAMgB,OAAO,SAASpL,WAAW,CAAC;EAC9BjC,WAAWA,CAAC5B,OAAO,EAAE4N,cAAc,EAAE;IACjC,KAAK,CAAC,CAAC;IACP,IAAI,CAAC5N,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC4N,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACsB,UAAU,GAAG,IAAI,CAAClP,OAAO,CAACmP,QAAQ,KAAK,UAAU;IACtD,IAAI,CAACb,aAAa,GAAG,IAAIhF,aAAa,CAAC,IAAI,CAACtJ,OAAO,CAAC;IACpD,IAAI,CAAC2J,OAAO,GAAG6D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElK,uBAAuB,CAAC,EAAE,IAAI,CAACqK,cAAc,CAAC;IAC7F,IAAI,CAACwB,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACvB,YAAY,GAAGT,WAAW,CAAC,IAAI,CAACzD,OAAO,CAAClG,aAAa,CAAC;IAC3D,IAAI,CAACqK,aAAa,GAAGV,WAAW,CAAC,IAAI,CAACzD,OAAO,CAACjG,cAAc,CAAC;IAC7D,IAAI,CAAC2L,SAAS,GAAG,IAAI,CAAC1F,OAAO,CAAChG,OAAO,CAChC2L,MAAM,CAACN,gBAAgB,CAAC,CACxB/C,GAAG,CAAEsD,MAAM,IAAKA,MAAM,CAAC,IAAI,CAACvP,OAAO,EAAE,IAAI,CAAC2J,OAAO,CAAC,CAAC;IACxD,IAAI,CAACpF,aAAa,CAAC,IAAI,CAACmC,YAAY,CAAC;IACrC,IAAI,CAAC4H,aAAa,CAAC9E,MAAM,CAAC,SAAS,EAAGiB,KAAK,IAAK;MAC5C,IAAIQ,MAAM,CAACR,KAAK,CAAC,EAAE;QACfA,KAAK,CAACsE,cAAc,CAAC,CAAC;QACtB,OAAO,IAAI,CAACzK,IAAI,CAAC,CAAC;MACtB;MACA,IAAI4G,MAAM,CAACT,KAAK,CAAC,EAAE;QACfA,KAAK,CAACsE,cAAc,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC9K,IAAI,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC;IACF,IAAI,CAACqK,aAAa,CAAC9E,MAAM,CAAC,aAAa,EAAGiB,KAAK,IAAK;MAChD,IAAI9J,EAAE,EAAEC,EAAE,EAAEC,EAAE;MACd,MAAM4K,SAAS,GAAGhB,KAAK,CAAC5F,SAAS,CAAC2K,QAAQ,CAAC,SAAS,CAAC;MACrD,IAAI,CAACjL,aAAa,CAAC,IAAI,CAACmC,YAAY,CAAC;MACrC,QAAQ+D,KAAK,CAAC5F,SAAS;QACnB,KAAK,aAAa;QAClB,KAAK,uBAAuB;QAC5B,KAAK,sBAAsB;UACvB,OAAO,IAAI,CAAC4K,YAAY,CAAC;YACrBhF,KAAK;YACLgB,SAAS;YACTjK,SAAS,EAAEuK,oBAAoB,CAAC,IAAI,CAACrF,YAAY,EAAE+E,SAAS;UAChE,CAAC,CAAC;QACN,KAAK,wBAAwB;QAC7B,KAAK,uBAAuB;QAC5B,KAAK,wBAAwB;QAC7B,KAAK,uBAAuB;UACxB,OAAO,IAAI,CAACgE,YAAY,CAAC;YACrBhF,KAAK;YACLgB,SAAS;YACTjK,SAAS,EAAEgK,gBAAgB,CAAC,IAAI,CAAC9E,YAAY,EAAE+E,SAAS,CAAC;YACzDiE,KAAK,EAAE;UACX,CAAC,CAAC;QACN,KAAK,oBAAoB;QACzB,KAAK,mBAAmB;UACpB,OAAO,IAAI,CAACD,YAAY,CAAC;YACrBhF,KAAK;YACLgB,SAAS;YACTjK,SAAS,EAAE8K,gBAAgB,CAAC,IAAI,CAAC5F,YAAY,EAAE+E,SAAS,CAAC;YACzDiE,KAAK,EAAE;UACX,CAAC,CAAC;QACN,KAAK,aAAa;UACdjF,KAAK,CAACsE,cAAc,CAAC,CAAC;UACtB,OAAO,IAAI,CAACzK,IAAI,CAAC,CAAC;QACtB;QACA,KAAK,aAAa;UACdmG,KAAK,CAACsE,cAAc,CAAC,CAAC;UACtB,OAAO,IAAI,CAAC9K,IAAI,CAAC,CAAC;QACtB,KAAK,uBAAuB;UACxB;QAAQ;QACZ,KAAK,iBAAiB;QACtB,KAAK,iBAAiB;UAClB,OAAO,IAAI,CAAC0L,WAAW,CAAClF,KAAK,CAAC;QAClC,KAAK,uBAAuB;UACxB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB;QACJ,KAAK,gBAAgB;QACrB,KAAK,iBAAiB;QACtB,KAAK,YAAY;QACjB;UACI,OAAO,IAAI,CAACmF,YAAY,CAACnF,KAAK,EAAE,CAAC5J,EAAE,GAAG,CAACF,EAAE,GAAG8J,KAAK,CAAC1F,IAAI,MAAM,IAAI,IAAIpE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE;UACtF;UACA,CAACC,EAAE,GAAG6J,KAAK,CAACoF,YAAY,MAAM,IAAI,IAAIjP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkP,OAAO,CAAC,YAAY,CAAC,MAAM,IAAI,IAAIjP,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC;MACrI;IACJ,CAAC,CAAC;IACF,IAAI,CAACyN,aAAa,CAAC9E,MAAM,CAAC,OAAO,EAAE,MAAM;MACrC,IAAI,IAAI,CAAC4F,oBAAoB,EAAE;QAC3B,IAAI,CAACtK,kBAAkB,CAAC,IAAI,CAACsK,oBAAoB,CAAC;QAClD,IAAI,CAACA,oBAAoB,GAAG,IAAI;MACpC;IACJ,CAAC,EAAE;MAAEW,OAAO,EAAE;IAAK,CAAC,CAAC;IACrB,IAAI,CAACzB,aAAa,CAAC9E,MAAM,CAAC,OAAO,EAAE,CAAC;MAAE3E;IAAU,CAAC,KAAK;MAClD,IAAIA,SAAS,KAAK,uBAAuB,EAAE;QACvC,OAAO,CAAC;MACZ;MACA,IAAI,CAACmL,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACzL,aAAa,CAAC,IAAI,CAACmC,YAAY,CAAC;IACzC,CAAC,CAAC;IACF,IAAI,CAAC4H,aAAa,CAAC9E,MAAM,CAAC,gBAAgB,EAAE,MAAM;MAC9C,IAAI,CAACwG,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACzL,aAAa,CAAC,IAAI,CAACmC,YAAY,CAAC;IACzC,CAAC,CAAC;EACN;EACAmD,OAAOA,CAAA,EAAG;IACN,IAAI,CAACyE,aAAa,CAACzE,OAAO,CAAC,CAAC;IAC5B,IAAI,CAACwF,SAAS,CAACvF,OAAO,CAAEmG,QAAQ,IAAKA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;EACxG;EACAnL,kBAAkBA,CAAC;IAAE/C,KAAK;IAAEP;EAAU,CAAC,EAAE0O,SAAS,EAAE;IAChD,MAAM7E,YAAY,GAAG,IAAI,CAAC3E,YAAY,CAAC3E,KAAK;IAC5C,IAAI,CAACoO,WAAW,CAACpO,KAAK,CAAC;IACvB,IAAI,CAACqO,oBAAoB,CAAC5O,SAAS,CAAC;IACpC,IAAI0O,SAAS,IAAI7E,YAAY,KAAKtJ,KAAK,EAAE;MACrC,IAAI,CAACsO,kBAAkB,CAACH,SAAS,CAAC;IACtC;EACJ;EACA,IAAIxJ,YAAYA,CAAA,EAAG;IACf,MAAM;MAAE3E,KAAK;MAAEI,cAAc;MAAEC;IAAa,CAAC,GAAG,IAAI,CAACpC,OAAO;IAC5D,OAAO;MACH+B,KAAK;MACLP,SAAS,EAAE,CAACW,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG,CAAC,EAAEC,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,CAAC;IAC9J,CAAC;EACL;EACA,IAAIP,SAASA,CAAA,EAAG;IACZ,MAAM;MAAEA;IAAU,CAAC,GAAG,IAAI,CAAC7B,OAAO;IAClC,OAAO6B,SAAS,KAAK,CAAC,CAAC,GAAGC,QAAQ,GAAGD,SAAS;EAClD;EACAuO,oBAAoBA,CAAC,CAAC/P,IAAI,EAAEG,EAAE,CAAC,EAAE;IAC7B,IAAIG,EAAE;IACN,MAAM;MAAEX;IAAQ,CAAC,GAAG,IAAI;IACxB,IAAIA,OAAO,CAACsL,OAAO,CAAC,QAAQ,CAAC,KACxBtL,OAAO,CAACmC,cAAc,KAAK9B,IAAI,IAAIL,OAAO,CAACoC,YAAY,KAAK5B,EAAE,CAAC,EAAE;MAClE,CAACG,EAAE,GAAGX,OAAO,CAACqC,iBAAiB,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4K,IAAI,CAACvL,OAAO,EAAEK,IAAI,EAAEG,EAAE,CAAC;IACpG;EACJ;EACA2P,WAAWA,CAACpO,KAAK,EAAE;IACf,IAAI,CAAC/B,OAAO,CAAC+B,KAAK,GAAGA,KAAK;EAC9B;EACAiO,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAClL,kBAAkB,CAAC4I,gBAAgB,CAAC,IAAI,CAAChH,YAAY,EAAE,IAAI,CAACiD,OAAO,CAAC,EAAE;MACvE9E,SAAS,EAAE,YAAY;MACvBE,IAAI,EAAE;IACV,CAAC,CAAC;EACN;EACAsL,kBAAkBA,CAACH,SAAS,GAAG;IAC3BrL,SAAS,EAAE,YAAY;IACvBE,IAAI,EAAE;EACV,CAAC,EAAE;IACC,IAAIuL,UAAU,CAACC,UAAU,EAAE;MACvB,IAAI,CAACvQ,OAAO,CAACiJ,aAAa,CAAC,IAAIsH,UAAU,CAAC,OAAO,EAAE/C,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyC,SAAS,CAAC,EAAE;QAAE/G,OAAO,EAAE,IAAI;QAAEqH,UAAU,EAAE;MAAM,CAAC,CAAC,CAAC,CAAC;IAC1I;EACJ;EACAf,YAAYA,CAAC;IAAEhF,KAAK;IAAEjJ,SAAS;IAAEiK,SAAS;IAAEiE,KAAK,GAAG;EAAO,CAAC,EAAE;IAC1D,MAAMe,YAAY,GAAG;MACjB1O,KAAK,EAAE,IAAI,CAAC2E,YAAY,CAAC3E,KAAK;MAC9BP;IACJ,CAAC;IACD,MAAM,CAACkP,WAAW,EAAEC,SAAS,CAAC,GAAGF,YAAY,CAACjP,SAAS;IACvD,MAAM;MAAEkF;IAAa,CAAC,GAAG,IAAI,CAACmH,YAAY,CAAC;MACvCnH,YAAY,EAAE+J,YAAY;MAC1B1L,IAAI,EAAE;IACV,CAAC,EAAE0G,SAAS,GAAG,eAAe,GAAG,gBAAgB,CAAC;IAClD,MAAMsC,SAAS,GAAG,IAAI/F,SAAS,CAACtB,YAAY,EAAE,IAAI,CAACiD,OAAO,CAAC;IAC3D,MAAM,CAACtJ,IAAI,EAAEG,EAAE,CAAC,GAAGkG,YAAY,CAAClF,SAAS;IACzCuM,SAAS,CAACnF,gBAAgB,CAAC,CAACvI,IAAI,EAAEG,EAAE,CAAC,CAAC;IACtC,MAAMoQ,eAAe,GAAG,IAAI,CAAC9C,aAAa,CAACC,SAAS,EAAE0C,YAAY,CAAC;IACnE,MAAMI,gBAAgB,GAAG;MACrB9O,KAAK,EAAE0O,YAAY,CAAC1O,KAAK,CAAC4F,KAAK,CAAC,CAAC,EAAE+I,WAAW,CAAC,GAC3CD,YAAY,CAAC1O,KAAK,CAAC4F,KAAK,CAACgJ,SAAS,CAAC;MACvCnP,SAAS,EAAE,CAACkP,WAAW,EAAEA,WAAW;IACxC,CAAC;IACD,IAAItL,qBAAqB,CAACyL,gBAAgB,EAAED,eAAe,CAAC,IACxD,CAAClB,KAAK,IACN,CAAC,IAAI,CAAC1P,OAAO,CAACqD,iBAAiB,EAAE;MACjC;IACJ;IACA,IAAI2B,qBAAqB,CAACyL,YAAY,EAAE/J,YAAY,EAAEqH,SAAS,EAAE6C,eAAe,CAAC,EAAE;MAC/EnG,KAAK,CAACsE,cAAc,CAAC,CAAC;MACtB;MACA,OAAO,IAAI,CAACqB,oBAAoB,CAAC3E,SAAS,GAAG,CAACjL,EAAE,EAAEA,EAAE,CAAC,GAAG,CAACH,IAAI,EAAEA,IAAI,CAAC,CAAC;IACzE;IACA,IAAI,CAAC+O,oBAAoB,GAAGwB,eAAe;EAC/C;EACAhB,YAAYA,CAACnF,KAAK,EAAE1F,IAAI,EAAE;IACtB,MAAM;MAAE4E,OAAO;MAAE9H,SAAS;MAAE7B,OAAO;MAAE0G,YAAY,EAAEZ;IAAoB,CAAC,GAAG,IAAI;IAC/E,MAAM;MAAEY,YAAY;MAAE3B,IAAI,EAAE+L,YAAY,GAAG/L;IAAK,CAAC,GAAG,IAAI,CAAC8I,YAAY,CAAC;MAClE9I,IAAI;MACJ2B,YAAY,EAAEZ;IAClB,CAAC,EAAE,QAAQ,CAAC;IACZ,MAAMiI,SAAS,GAAG,IAAI/F,SAAS,CAACtB,YAAY,EAAEiD,OAAO,CAAC;IACtD,IAAI;MACAoE,SAAS,CAAC5F,aAAa,CAACzB,YAAY,CAAClF,SAAS,EAAEsP,YAAY,CAAC;IACjE,CAAC,CACD,OAAOnQ,EAAE,EAAE;MACP,OAAO8J,KAAK,CAACsE,cAAc,CAAC,CAAC;IACjC;IACA,MAAM,CAAC1O,IAAI,EAAEG,EAAE,CAAC,GAAGsF,mBAAmB,CAACtE,SAAS;IAChD,MAAMqP,gBAAgB,GAAG;MACrB9O,KAAK,EAAE+D,mBAAmB,CAAC/D,KAAK,CAAC4F,KAAK,CAAC,CAAC,EAAEtH,IAAI,CAAC,GAC3C0E,IAAI,GACJe,mBAAmB,CAAC/D,KAAK,CAAC4F,KAAK,CAACnH,EAAE,CAAC;MACvCgB,SAAS,EAAE,CAACnB,IAAI,GAAG0E,IAAI,CAAC1D,MAAM,EAAEhB,IAAI,GAAG0E,IAAI,CAAC1D,MAAM;IACtD,CAAC;IACD,MAAMuP,eAAe,GAAG,IAAI,CAACG,UAAU,CAAC,IAAI,CAACjD,aAAa,CAACC,SAAS,EAAEjI,mBAAmB,CAAC,CAAC;IAC3F,IAAI,CAACV,qBAAqB,CAAC,IAAI,CAAC2L,UAAU,CAACF,gBAAgB,CAAC,EAAED,eAAe,CAAC,IAC1E5Q,OAAO,CAACqD,iBAAiB,EAAE;MAC3B,IAAI,CAAC+L,oBAAoB,GAAGwB,eAAe;MAC3C,IAAIjH,OAAO,CAAC/F,aAAa,KAAK,SAAS,IACnCiN,gBAAgB,CAAC9O,KAAK,CAACV,MAAM,GAAGQ,SAAS,EAAE;QAC3C;AAChB;AACA;AACA;AACA;QACgB,IAAI,CAACwO,kBAAkB,CAAC;UAAExL,SAAS,EAAE,YAAY;UAAEE;QAAK,CAAC,CAAC;MAC9D;IACJ;EACJ;EACA4K,WAAWA,CAAClF,KAAK,EAAE;IACf,IAAI,IAAI,CAACyE,UAAU,IAAI,IAAI,CAAClP,OAAO,CAACqD,iBAAiB,EAAE;MACnD,IAAI,CAACuM,YAAY,CAACnF,KAAK,EAAE,IAAI,CAAC;IAClC;EACJ;EACAsG,UAAUA,CAAC;IAAEhP,KAAK;IAAEP;EAAU,CAAC,EAAE;IAC7B,MAAM,CAACnB,IAAI,EAAEG,EAAE,CAAC,GAAGgB,SAAS;IAC5B,MAAMf,GAAG,GAAG,IAAI,CAACoB,SAAS;IAC1B,OAAO;MACHE,KAAK,EAAEA,KAAK,CAAC4F,KAAK,CAAC,CAAC,EAAElH,GAAG,CAAC;MAC1Be,SAAS,EAAE,CAAClB,IAAI,CAACC,GAAG,CAACF,IAAI,EAAEI,GAAG,CAAC,EAAEH,IAAI,CAACC,GAAG,CAACC,EAAE,EAAEC,GAAG,CAAC;IACtD,CAAC;EACL;AACJ;AAEA,SAAS0C,iCAAiC,EAAEI,uBAAuB,EAAE0L,OAAO,EAAE1M,2BAA2B,EAAEuG,wBAAwB,EAAE4F,+BAA+B,EAAEtB,WAAW,EAAEwB,8BAA8B,EAAElB,gBAAgB,EAAEvC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}