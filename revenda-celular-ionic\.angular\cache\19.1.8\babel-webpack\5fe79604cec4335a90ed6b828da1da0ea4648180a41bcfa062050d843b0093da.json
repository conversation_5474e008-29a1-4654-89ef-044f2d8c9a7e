{"ast": null, "code": "var _SaleService;\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class SaleService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.baseUrl}/sales`;\n  }\n  getAll(status) {\n    const params = status ? `?status=${status}` : '';\n    return this.http.get(`${this.apiUrl}${params}`);\n  }\n  getList() {\n    return this.getAll();\n  }\n  getById(id) {\n    return this.http.get(`${this.apiUrl}/${id}`);\n  }\n  getByCustomer(customerId) {\n    return this.http.get(`${this.apiUrl}/customer/${customerId}`);\n  }\n  getByStore(storeId) {\n    return this.http.get(`${this.apiUrl}/store/${storeId}`);\n  }\n  add(sale) {\n    return this.http.post(this.apiUrl, sale);\n  }\n  update(id, sale) {\n    return this.http.patch(`${this.apiUrl}/${id}`, sale);\n  }\n  save(sale) {\n    if (sale.id) {\n      const updateData = {\n        customerId: sale.customerId,\n        storeId: sale.storeId,\n        paymentMethod: sale.paymentMethod,\n        status: sale.status,\n        seller: sale.seller,\n        items: sale.items.map(item => ({\n          productId: item.productId,\n          productType: item.productType,\n          quantity: item.quantity,\n          unitPrice: item.unitPrice,\n          subtotal: item.subtotal\n        }))\n      };\n      return this.update(sale.id, updateData);\n    } else {\n      const createData = {\n        customerId: sale.customerId,\n        storeId: sale.storeId,\n        paymentMethod: sale.paymentMethod,\n        seller: sale.seller,\n        items: sale.items.map(item => ({\n          productId: item.productId,\n          productType: item.productType,\n          quantity: item.quantity,\n          unitPrice: item.unitPrice,\n          subtotal: item.subtotal\n        }))\n      };\n      return this.add(createData);\n    }\n  }\n  updateStatus(id, status) {\n    return this.http.patch(`${this.apiUrl}/${id}/status`, {\n      status\n    });\n  }\n  remove(sale) {\n    return this.http.delete(`${this.apiUrl}/${sale.id}`);\n  }\n  delete(id) {\n    return this.http.delete(`${this.apiUrl}/${id}`);\n  }\n  // Dashboard methods\n  getDashboardStats() {\n    return this.http.get(`${this.apiUrl}/dashboard/stats`);\n  }\n  getSalesByMonth() {\n    return this.http.get(`${this.apiUrl}/dashboard/monthly`);\n  }\n  getTopProducts() {\n    return this.http.get(`${this.apiUrl}/dashboard/top-products`);\n  }\n  getRecentSales(limit = 10) {\n    return this.http.get(`${this.apiUrl}/dashboard/recent?limit=${limit}`);\n  }\n}\n_SaleService = SaleService;\n_SaleService.ɵfac = function SaleService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SaleService)(i0.ɵɵinject(i1.HttpClient));\n};\n_SaleService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _SaleService,\n  factory: _SaleService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["environment", "SaleService", "constructor", "http", "apiUrl", "baseUrl", "getAll", "status", "params", "get", "getList", "getById", "id", "getByCustomer", "customerId", "getByStore", "storeId", "add", "sale", "post", "update", "patch", "save", "updateData", "paymentMethod", "seller", "items", "map", "item", "productId", "productType", "quantity", "unitPrice", "subtotal", "createData", "updateStatus", "remove", "delete", "getDashboardStats", "getSalesByMonth", "getTopProducts", "getRecentSales", "limit", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\sales\\services\\sale.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { Sale, CreateSaleDto, UpdateSaleDto, DashboardStats } from '../models/sale.type';\r\nimport { environment } from '../../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class SaleService {\r\n  private apiUrl = `${environment.baseUrl}/sales`;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getAll(status?: string): Observable<Sale[]> {\r\n    const params = status ? `?status=${status}` : '';\r\n    return this.http.get<Sale[]>(`${this.apiUrl}${params}`);\r\n  }\r\n\r\n  getList(): Observable<Sale[]> {\r\n    return this.getAll();\r\n  }\r\n\r\n  getById(id: number): Observable<Sale> {\r\n    return this.http.get<Sale>(`${this.apiUrl}/${id}`);\r\n  }\r\n\r\n  getByCustomer(customerId: number): Observable<Sale[]> {\r\n    return this.http.get<Sale[]>(`${this.apiUrl}/customer/${customerId}`);\r\n  }\r\n\r\n  getByStore(storeId: number): Observable<Sale[]> {\r\n    return this.http.get<Sale[]>(`${this.apiUrl}/store/${storeId}`);\r\n  }\r\n\r\n  private add(sale: CreateSaleDto): Observable<Sale> {\r\n    return this.http.post<Sale>(this.apiUrl, sale);\r\n  }\r\n\r\n  private update(id: number, sale: UpdateSaleDto): Observable<any> {\r\n    return this.http.patch(`${this.apiUrl}/${id}`, sale);\r\n  }\r\n\r\n  save(sale: Sale): Observable<any> {\r\n    if (sale.id) {\r\n      const updateData: UpdateSaleDto = {\r\n        customerId: sale.customerId,\r\n        storeId: sale.storeId,\r\n        paymentMethod: sale.paymentMethod,\r\n        status: sale.status,\r\n        seller: sale.seller,\r\n        items: sale.items.map(item => ({\r\n          productId: item.productId,\r\n          productType: item.productType,\r\n          quantity: item.quantity,\r\n          unitPrice: item.unitPrice,\r\n          subtotal: item.subtotal\r\n        }))\r\n      };\r\n      return this.update(sale.id, updateData);\r\n    } else {\r\n      const createData: CreateSaleDto = {\r\n        customerId: sale.customerId,\r\n        storeId: sale.storeId,\r\n        paymentMethod: sale.paymentMethod,\r\n        seller: sale.seller,\r\n        items: sale.items.map(item => ({\r\n          productId: item.productId,\r\n          productType: item.productType,\r\n          quantity: item.quantity,\r\n          unitPrice: item.unitPrice,\r\n          subtotal: item.subtotal\r\n        }))\r\n      };\r\n      return this.add(createData);\r\n    }\r\n  }\r\n\r\n  updateStatus(id: number, status: string): Observable<any> {\r\n    return this.http.patch(`${this.apiUrl}/${id}/status`, { status });\r\n  }\r\n\r\n  remove(sale: Sale): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/${sale.id}`);\r\n  }\r\n\r\n  delete(id: number): Observable<void> {\r\n    return this.http.delete<void>(`${this.apiUrl}/${id}`);\r\n  }\r\n\r\n  // Dashboard methods\r\n  getDashboardStats(): Observable<DashboardStats> {\r\n    return this.http.get<DashboardStats>(`${this.apiUrl}/dashboard/stats`);\r\n  }\r\n\r\n  getSalesByMonth(): Observable<any[]> {\r\n    return this.http.get<any[]>(`${this.apiUrl}/dashboard/monthly`);\r\n  }\r\n\r\n  getTopProducts(): Observable<any[]> {\r\n    return this.http.get<any[]>(`${this.apiUrl}/dashboard/top-products`);\r\n  }\r\n\r\n  getRecentSales(limit: number = 10): Observable<Sale[]> {\r\n    return this.http.get<Sale[]>(`${this.apiUrl}/dashboard/recent?limit=${limit}`);\r\n  }\r\n}\r\n"], "mappings": ";AAIA,SAASA,WAAW,QAAQ,mCAAmC;;;AAK/D,OAAM,MAAOC,WAAW;EAGtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,OAAO,QAAQ;EAEP;EAExCC,MAAMA,CAACC,MAAe;IACpB,MAAMC,MAAM,GAAGD,MAAM,GAAG,WAAWA,MAAM,EAAE,GAAG,EAAE;IAChD,OAAO,IAAI,CAACJ,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,GAAGI,MAAM,EAAE,CAAC;EACzD;EAEAE,OAAOA,CAAA;IACL,OAAO,IAAI,CAACJ,MAAM,EAAE;EACtB;EAEAK,OAAOA,CAACC,EAAU;IAChB,OAAO,IAAI,CAACT,IAAI,CAACM,GAAG,CAAO,GAAG,IAAI,CAACL,MAAM,IAAIQ,EAAE,EAAE,CAAC;EACpD;EAEAC,aAAaA,CAACC,UAAkB;IAC9B,OAAO,IAAI,CAACX,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,aAAaU,UAAU,EAAE,CAAC;EACvE;EAEAC,UAAUA,CAACC,OAAe;IACxB,OAAO,IAAI,CAACb,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,UAAUY,OAAO,EAAE,CAAC;EACjE;EAEQC,GAAGA,CAACC,IAAmB;IAC7B,OAAO,IAAI,CAACf,IAAI,CAACgB,IAAI,CAAO,IAAI,CAACf,MAAM,EAAEc,IAAI,CAAC;EAChD;EAEQE,MAAMA,CAACR,EAAU,EAAEM,IAAmB;IAC5C,OAAO,IAAI,CAACf,IAAI,CAACkB,KAAK,CAAC,GAAG,IAAI,CAACjB,MAAM,IAAIQ,EAAE,EAAE,EAAEM,IAAI,CAAC;EACtD;EAEAI,IAAIA,CAACJ,IAAU;IACb,IAAIA,IAAI,CAACN,EAAE,EAAE;MACX,MAAMW,UAAU,GAAkB;QAChCT,UAAU,EAAEI,IAAI,CAACJ,UAAU;QAC3BE,OAAO,EAAEE,IAAI,CAACF,OAAO;QACrBQ,aAAa,EAAEN,IAAI,CAACM,aAAa;QACjCjB,MAAM,EAAEW,IAAI,CAACX,MAAM;QACnBkB,MAAM,EAAEP,IAAI,CAACO,MAAM;QACnBC,KAAK,EAAER,IAAI,CAACQ,KAAK,CAACC,GAAG,CAACC,IAAI,KAAK;UAC7BC,SAAS,EAAED,IAAI,CAACC,SAAS;UACzBC,WAAW,EAAEF,IAAI,CAACE,WAAW;UAC7BC,QAAQ,EAAEH,IAAI,CAACG,QAAQ;UACvBC,SAAS,EAAEJ,IAAI,CAACI,SAAS;UACzBC,QAAQ,EAAEL,IAAI,CAACK;SAChB,CAAC;OACH;MACD,OAAO,IAAI,CAACb,MAAM,CAACF,IAAI,CAACN,EAAE,EAAEW,UAAU,CAAC;IACzC,CAAC,MAAM;MACL,MAAMW,UAAU,GAAkB;QAChCpB,UAAU,EAAEI,IAAI,CAACJ,UAAU;QAC3BE,OAAO,EAAEE,IAAI,CAACF,OAAO;QACrBQ,aAAa,EAAEN,IAAI,CAACM,aAAa;QACjCC,MAAM,EAAEP,IAAI,CAACO,MAAM;QACnBC,KAAK,EAAER,IAAI,CAACQ,KAAK,CAACC,GAAG,CAACC,IAAI,KAAK;UAC7BC,SAAS,EAAED,IAAI,CAACC,SAAS;UACzBC,WAAW,EAAEF,IAAI,CAACE,WAAW;UAC7BC,QAAQ,EAAEH,IAAI,CAACG,QAAQ;UACvBC,SAAS,EAAEJ,IAAI,CAACI,SAAS;UACzBC,QAAQ,EAAEL,IAAI,CAACK;SAChB,CAAC;OACH;MACD,OAAO,IAAI,CAAChB,GAAG,CAACiB,UAAU,CAAC;IAC7B;EACF;EAEAC,YAAYA,CAACvB,EAAU,EAAEL,MAAc;IACrC,OAAO,IAAI,CAACJ,IAAI,CAACkB,KAAK,CAAC,GAAG,IAAI,CAACjB,MAAM,IAAIQ,EAAE,SAAS,EAAE;MAAEL;IAAM,CAAE,CAAC;EACnE;EAEA6B,MAAMA,CAAClB,IAAU;IACf,OAAO,IAAI,CAACf,IAAI,CAACkC,MAAM,CAAC,GAAG,IAAI,CAACjC,MAAM,IAAIc,IAAI,CAACN,EAAE,EAAE,CAAC;EACtD;EAEAyB,MAAMA,CAACzB,EAAU;IACf,OAAO,IAAI,CAACT,IAAI,CAACkC,MAAM,CAAO,GAAG,IAAI,CAACjC,MAAM,IAAIQ,EAAE,EAAE,CAAC;EACvD;EAEA;EACA0B,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACnC,IAAI,CAACM,GAAG,CAAiB,GAAG,IAAI,CAACL,MAAM,kBAAkB,CAAC;EACxE;EAEAmC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACpC,IAAI,CAACM,GAAG,CAAQ,GAAG,IAAI,CAACL,MAAM,oBAAoB,CAAC;EACjE;EAEAoC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACrC,IAAI,CAACM,GAAG,CAAQ,GAAG,IAAI,CAACL,MAAM,yBAAyB,CAAC;EACtE;EAEAqC,cAAcA,CAACC,KAAA,GAAgB,EAAE;IAC/B,OAAO,IAAI,CAACvC,IAAI,CAACM,GAAG,CAAS,GAAG,IAAI,CAACL,MAAM,2BAA2BsC,KAAK,EAAE,CAAC;EAChF;;eAhGWzC,WAAW;;mCAAXA,YAAW,EAAA0C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAX7C,YAAW;EAAA8C,OAAA,EAAX9C,YAAW,CAAA+C,IAAA;EAAAC,UAAA,EAFV;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}