"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateStoresTable1703000005000 = void 0;
const typeorm_1 = require("typeorm");
class CreateStoresTable1703000005000 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'stores',
            columns: [
                {
                    name: 'id',
                    type: 'int',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '200',
                },
                {
                    name: 'address',
                    type: 'varchar',
                    length: '500',
                },
                {
                    name: 'city',
                    type: 'varchar',
                    length: '100',
                },
                {
                    name: 'state',
                    type: 'varchar',
                    length: '50',
                },
                {
                    name: 'phone',
                    type: 'varchar',
                    length: '20',
                },
                {
                    name: 'manager',
                    type: 'varchar',
                    length: '200',
                },
                {
                    name: 'isHeadquarters',
                    type: 'boolean',
                    default: false,
                },
                {
                    name: 'status',
                    type: 'enum',
                    enum: ['active', 'inactive', 'underMaintenance'],
                    default: "'active'",
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('stores');
    }
}
exports.CreateStoresTable1703000005000 = CreateStoresTable1703000005000;
//# sourceMappingURL=1703000005000-CreateStoresTable.js.map