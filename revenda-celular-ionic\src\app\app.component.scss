ion-menu ion-content {
  --background: var(--ion-item-background, var(--ion-background-color, #fff));
}

ion-menu ion-toolbar {
  --background: var(--ion-color-secondary);
  --color: var(--ion-color-secondary-contrast);
}

ion-menu.md ion-content {
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 20px;
  --padding-bottom: 20px;
}

ion-menu.md ion-list {
  padding: 20px 0;
}

ion-menu.md ion-list#inbox-list {
  border-bottom: 1px solid rgba(var(--ion-color-secondary-rgb), 0.1);
}

ion-menu.md ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
  margin-bottom: 4px;
  border-radius: 8px;
  --min-height: 50px;
}

ion-menu.md ion-item.selected {
  --background: rgba(var(--ion-color-secondary-rgb), 0.15);
}

ion-menu.md ion-item.selected ion-icon {
  color: var(--ion-color-secondary);
}

ion-menu.md ion-item ion-icon {
  color: var(--ion-color-medium);
  margin-right: 16px;
  font-size: 20px;
}

ion-menu.md ion-item ion-label {
  font-weight: 500;
}

ion-menu.ios ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 50px;
  margin-bottom: 4px;
  border-radius: 8px;
}

ion-menu.ios ion-item.selected ion-icon {
  color: var(--ion-color-secondary);
}

ion-menu.ios ion-item ion-icon {
  font-size: 20px;
  margin-right: 16px;
  color: var(--ion-color-medium);
}

ion-item.selected {
  --color: var(--ion-color-secondary);
  font-weight: 600;
}
