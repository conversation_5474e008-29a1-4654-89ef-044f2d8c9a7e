{"ast": null, "code": "import { Subject } from './Subject';\nexport class AsyncSubject extends Subject {\n  constructor() {\n    super(...arguments);\n    this._value = null;\n    this._hasValue = false;\n    this._isComplete = false;\n  }\n  _checkFinalizedStatuses(subscriber) {\n    const {\n      hasError,\n      _hasValue,\n      _value,\n      thrownError,\n      isStopped,\n      _isComplete\n    } = this;\n    if (hasError) {\n      subscriber.error(thrownError);\n    } else if (isStopped || _isComplete) {\n      _hasValue && subscriber.next(_value);\n      subscriber.complete();\n    }\n  }\n  next(value) {\n    if (!this.isStopped) {\n      this._value = value;\n      this._hasValue = true;\n    }\n  }\n  complete() {\n    const {\n      _hasValue,\n      _value,\n      _isComplete\n    } = this;\n    if (!_isComplete) {\n      this._isComplete = true;\n      _hasValue && super.next(_value);\n      super.complete();\n    }\n  }\n}", "map": {"version": 3, "names": ["Subject", "AsyncSubject", "constructor", "arguments", "_value", "_hasValue", "_isComplete", "_checkFinalizedStatuses", "subscriber", "<PERSON><PERSON><PERSON><PERSON>", "thrownError", "isStopped", "error", "next", "complete", "value"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/rxjs/dist/esm/internal/AsyncSubject.js"], "sourcesContent": ["import { Subject } from './Subject';\nexport class AsyncSubject extends Subject {\n    constructor() {\n        super(...arguments);\n        this._value = null;\n        this._hasValue = false;\n        this._isComplete = false;\n    }\n    _checkFinalizedStatuses(subscriber) {\n        const { hasError, _hasValue, _value, thrownError, isStopped, _isComplete } = this;\n        if (hasError) {\n            subscriber.error(thrownError);\n        }\n        else if (isStopped || _isComplete) {\n            _hasValue && subscriber.next(_value);\n            subscriber.complete();\n        }\n    }\n    next(value) {\n        if (!this.isStopped) {\n            this._value = value;\n            this._hasValue = true;\n        }\n    }\n    complete() {\n        const { _hasValue, _value, _isComplete } = this;\n        if (!_isComplete) {\n            this._isComplete = true;\n            _hasValue && super.next(_value);\n            super.complete();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,WAAW;AACnC,OAAO,MAAMC,YAAY,SAASD,OAAO,CAAC;EACtCE,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,WAAW,GAAG,KAAK;EAC5B;EACAC,uBAAuBA,CAACC,UAAU,EAAE;IAChC,MAAM;MAAEC,QAAQ;MAAEJ,SAAS;MAAED,MAAM;MAAEM,WAAW;MAAEC,SAAS;MAAEL;IAAY,CAAC,GAAG,IAAI;IACjF,IAAIG,QAAQ,EAAE;MACVD,UAAU,CAACI,KAAK,CAACF,WAAW,CAAC;IACjC,CAAC,MACI,IAAIC,SAAS,IAAIL,WAAW,EAAE;MAC/BD,SAAS,IAAIG,UAAU,CAACK,IAAI,CAACT,MAAM,CAAC;MACpCI,UAAU,CAACM,QAAQ,CAAC,CAAC;IACzB;EACJ;EACAD,IAAIA,CAACE,KAAK,EAAE;IACR,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE;MACjB,IAAI,CAACP,MAAM,GAAGW,KAAK;MACnB,IAAI,CAACV,SAAS,GAAG,IAAI;IACzB;EACJ;EACAS,QAAQA,CAAA,EAAG;IACP,MAAM;MAAET,SAAS;MAAED,MAAM;MAAEE;IAAY,CAAC,GAAG,IAAI;IAC/C,IAAI,CAACA,WAAW,EAAE;MACd,IAAI,CAACA,WAAW,GAAG,IAAI;MACvBD,SAAS,IAAI,KAAK,CAACQ,IAAI,CAACT,MAAM,CAAC;MAC/B,KAAK,CAACU,QAAQ,CAAC,CAAC;IACpB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}