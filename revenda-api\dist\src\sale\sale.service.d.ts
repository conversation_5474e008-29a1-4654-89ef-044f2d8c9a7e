import { Repository } from 'typeorm';
import { Sale } from './sale.entity';
import { SaleItem } from './sale-item.entity';
import { CreateSaleDto } from './dto/create-sale.dto';
import { UpdateSaleDto } from './dto/update-sale.dto';
export declare class SaleService {
    private saleRepository;
    private saleItemRepository;
    constructor(saleRepository: Repository<Sale>, saleItemRepository: Repository<SaleItem>);
    create(createSaleDto: CreateSaleDto): Promise<Sale | null>;
    findAll(status?: string): Promise<Sale[]>;
    findOne(id: number): Promise<Sale | null>;
    findByCustomer(customerId: number): Promise<Sale[]>;
    findByStore(storeId: number): Promise<Sale[]>;
    update(id: number, updateSaleDto: UpdateSaleDto): Promise<Sale | null>;
    updateStatus(id: number, status: string): Promise<import("typeorm").UpdateResult>;
    remove(id: number): Promise<import("typeorm").DeleteResult>;
}
