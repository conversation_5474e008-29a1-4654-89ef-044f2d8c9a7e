{"ast": null, "code": "\"use strict\";\n\n/**\n * Prevents Angular change detection from\n * running with certain Web Component callbacks\n */\n// eslint-disable-next-line no-underscore-dangle\nwindow.__Zone_disable_customElements = true;", "map": {"version": 3, "names": ["window", "__Zone_disable_customElements"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\zone-flags.ts"], "sourcesContent": ["/**\r\n * Prevents Angular change detection from\r\n * running with certain Web Component callbacks\r\n */\r\n// eslint-disable-next-line no-underscore-dangle\r\n(window as any).__Zone_disable_customElements = true;\r\n"], "mappings": ";;AAAA;;;;AAIA;AACCA,MAAc,CAACC,6BAA6B,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}