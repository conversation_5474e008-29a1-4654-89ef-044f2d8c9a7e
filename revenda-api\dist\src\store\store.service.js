"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoreService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const store_entity_1 = require("./store.entity");
let StoreService = class StoreService {
    storeRepository;
    constructor(storeRepository) {
        this.storeRepository = storeRepository;
    }
    create(createStoreDto) {
        const store = this.storeRepository.create(createStoreDto);
        return this.storeRepository.save(store);
    }
    findAll() {
        return this.storeRepository.find({
            relations: ['sales'],
        });
    }
    findOne(id) {
        return this.storeRepository.findOne({
            where: { id },
            relations: ['sales'],
        });
    }
    findActive() {
        return this.storeRepository.find({
            where: { status: 'active' },
        });
    }
    update(id, updateStoreDto) {
        return this.storeRepository.update(id, updateStoreDto);
    }
    async remove(id) {
        const store = await this.storeRepository.findOne({
            where: { id },
            relations: ['sales'],
        });
        if (!store) {
            throw new common_1.NotFoundException(`Loja com ID ${id} não encontrada`);
        }
        if (store.sales && store.sales.length > 0) {
            throw new common_1.BadRequestException(`Não é possível deletar a loja '${store.name}' pois existem ${store.sales.length} venda(s) associada(s) a ela`);
        }
        return this.storeRepository.delete(id);
    }
};
exports.StoreService = StoreService;
exports.StoreService = StoreService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(store_entity_1.Store)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], StoreService);
//# sourceMappingURL=store.service.js.map