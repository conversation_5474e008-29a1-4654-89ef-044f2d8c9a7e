{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar _SalesPage;\nimport { SaleStatus, PaymentMethods } from './models/sale.type';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/sale.service\";\nimport * as i2 from \"../stores/services/store.service\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nconst _c0 = () => [\"new\"];\nconst _c1 = a0 => [\"details\", a0];\nconst _c2 = a0 => [\"edit\", a0];\nconst _forTrack0 = ($index, $item) => $item.id;\nconst _forTrack1 = ($index, $item) => $item.value;\nfunction SalesPage_For_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const store_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", store_r1.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(store_r1.name);\n  }\n}\nfunction SalesPage_For_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", status_r2.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(status_r2.label);\n  }\n}\nfunction SalesPage_For_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-item\")(1, \"ion-text\", 17)(2, \"h2\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Data:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"h4\")(10, \"strong\");\n    i0.ɵɵtext(11, \"Cliente:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"h4\")(14, \"strong\");\n    i0.ɵɵtext(15, \"Loja:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"h4\")(18, \"strong\");\n    i0.ɵɵtext(19, \"Valor Total:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"h4\")(23, \"ion-badge\", 18);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"ion-badge\", 19);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"ion-button\", 20);\n    i0.ɵɵelement(28, \"ion-icon\", 21);\n    i0.ɵɵtext(29, \" Detalhes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"ion-button\", 22);\n    i0.ɵɵelement(31, \"ion-icon\", 23);\n    i0.ɵɵtext(32, \" Editar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"ion-button\", 24);\n    i0.ɵɵlistener(\"click\", function SalesPage_For_43_Template_ion_button_click_33_listener() {\n      const sale_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.cancelSale(sale_r4));\n    });\n    i0.ɵɵelement(34, \"ion-icon\", 25);\n    i0.ɵɵtext(35, \" Cancelar \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const sale_r4 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Venda #\", sale_r4.id, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(8, 12, sale_r4.date, \"dd/MM/yyyy\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", sale_r4.customer.name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", sale_r4.store.name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(21, 15, sale_r4.totalValue, \"BRL\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"color\", ctx_r4.getStatusColor(sale_r4.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r4.getStatusLabel(sale_r4.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getPaymentMethodLabel(sale_r4.paymentMethod));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(18, _c1, sale_r4.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(20, _c2, sale_r4.id))(\"disabled\", sale_r4.status === \"canceled\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", sale_r4.status === \"canceled\");\n  }\n}\nfunction SalesPage_ForEmpty_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\");\n    i0.ɵɵtext(1, \"Nenhuma venda encontrada com os filtros selecionados.\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class SalesPage {\n  constructor(saleService, storeService, alertController) {\n    this.saleService = saleService;\n    this.storeService = storeService;\n    this.alertController = alertController;\n    this.salesList = [];\n    this.storesList = [];\n    this.saleStatusOptions = SaleStatus;\n    this.paymentMethodOptions = PaymentMethods;\n    this.filters = {\n      storeId: null,\n      status: null\n    };\n  }\n  ngOnInit() {\n    this.loadSales();\n    this.loadStores();\n  }\n  ionViewWillEnter() {\n    this.loadSales();\n  }\n  loadSales() {\n    this.saleService.getAll().subscribe({\n      next: sales => {\n        this.salesList = sales;\n      },\n      error: error => {\n        console.error('Erro ao carregar vendas', error);\n      }\n    });\n  }\n  loadStores() {\n    this.storeService.getList().subscribe({\n      next: stores => {\n        this.storesList = stores;\n      },\n      error: error => {\n        console.error('Erro ao carregar lojas', error);\n      }\n    });\n  }\n  applyFilters() {\n    this.saleService.filterSales(this.filters).subscribe({\n      next: sales => {\n        this.salesList = sales;\n      },\n      error: error => {\n        console.error('Erro ao filtrar vendas', error);\n      }\n    });\n  }\n  clearFilters() {\n    this.filters = {\n      storeId: null,\n      status: null\n    };\n    this.loadSales();\n  }\n  getStatusLabel(status) {\n    const statusObj = this.saleStatusOptions.find(s => s.value === status);\n    return statusObj ? statusObj.label : status;\n  }\n  getPaymentMethodLabel(method) {\n    const methodObj = this.paymentMethodOptions.find(m => m.value === method);\n    return methodObj ? methodObj.label : method;\n  }\n  getStatusColor(status) {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'canceled':\n        return 'danger';\n      default:\n        return 'medium';\n    }\n  }\n  cancelSale(sale) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const alert = yield _this.alertController.create({\n        header: 'Confirmar Cancelamento',\n        message: `Tem certeza que deseja cancelar a venda #${sale.id}?`,\n        buttons: [{\n          text: 'Cancelar',\n          role: 'cancel'\n        }, {\n          text: 'Confirmar',\n          handler: () => {\n            const updatedSale = {\n              ...sale,\n              status: 'canceled'\n            };\n            _this.saleService.save(updatedSale).subscribe({\n              next: () => {\n                _this.loadSales();\n              },\n              error: error => {\n                console.error('Erro ao cancelar venda', error);\n              }\n            });\n          }\n        }]\n      });\n      yield alert.present();\n    })();\n  }\n}\n_SalesPage = SalesPage;\n_SalesPage.ɵfac = function SalesPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SalesPage)(i0.ɵɵdirectiveInject(i1.SaleService), i0.ɵɵdirectiveInject(i2.StoreService), i0.ɵɵdirectiveInject(i3.AlertController));\n};\n_SalesPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _SalesPage,\n  selectors: [[\"app-sales\"]],\n  standalone: false,\n  decls: 48,\n  vars: 9,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [3, \"fullscreen\"], [\"collapse\", \"condense\"], [\"size\", \"large\"], [\"size\", \"12\", \"size-md\", \"6\"], [\"label\", \"Loja\", \"labelPlacement\", \"floating\", 3, \"ngModelChange\", \"ngModel\"], [3, \"value\"], [\"label\", \"Status\", \"labelPlacement\", \"floating\", 3, \"ngModelChange\", \"ngModel\"], [\"expand\", \"block\", 3, \"click\"], [\"name\", \"search\", \"slot\", \"start\"], [\"expand\", \"block\", \"fill\", \"outline\", 3, \"click\"], [\"name\", \"refresh\", \"slot\", \"start\"], [\"vertical\", \"bottom\", \"horizontal\", \"end\", \"slot\", \"fixed\"], [3, \"routerLink\"], [\"name\", \"add\"], [1, \"sale-info\"], [3, \"color\"], [\"color\", \"primary\"], [\"size\", \"small\", 3, \"routerLink\"], [\"name\", \"eye\", \"slot\", \"start\"], [\"size\", \"small\", 3, \"routerLink\", \"disabled\"], [\"name\", \"create\", \"slot\", \"start\"], [\"size\", \"small\", \"color\", \"danger\", 3, \"click\", \"disabled\"], [\"name\", \"close-circle\", \"slot\", \"end\"]],\n  template: function SalesPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-menu-button\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5, \"Vendas\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 3)(7, \"ion-header\", 4)(8, \"ion-toolbar\")(9, \"ion-title\", 5);\n      i0.ɵɵtext(10, \"Vendas\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(11, \"ion-card\")(12, \"ion-card-header\")(13, \"ion-card-title\");\n      i0.ɵɵtext(14, \"Filtros\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(15, \"ion-card-content\")(16, \"ion-grid\")(17, \"ion-row\")(18, \"ion-col\", 6)(19, \"ion-item\")(20, \"ion-select\", 7);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesPage_Template_ion_select_ngModelChange_20_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.filters.storeId, $event) || (ctx.filters.storeId = $event);\n        return $event;\n      });\n      i0.ɵɵelementStart(21, \"ion-select-option\", 8);\n      i0.ɵɵtext(22, \"Todas\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵrepeaterCreate(23, SalesPage_For_24_Template, 2, 2, \"ion-select-option\", 8, _forTrack0);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(25, \"ion-col\", 6)(26, \"ion-item\")(27, \"ion-select\", 9);\n      i0.ɵɵtwoWayListener(\"ngModelChange\", function SalesPage_Template_ion_select_ngModelChange_27_listener($event) {\n        i0.ɵɵtwoWayBindingSet(ctx.filters.status, $event) || (ctx.filters.status = $event);\n        return $event;\n      });\n      i0.ɵɵelementStart(28, \"ion-select-option\", 8);\n      i0.ɵɵtext(29, \"Todos\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵrepeaterCreate(30, SalesPage_For_31_Template, 2, 2, \"ion-select-option\", 8, _forTrack1);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(32, \"ion-row\")(33, \"ion-col\")(34, \"ion-button\", 10);\n      i0.ɵɵlistener(\"click\", function SalesPage_Template_ion_button_click_34_listener() {\n        return ctx.applyFilters();\n      });\n      i0.ɵɵelement(35, \"ion-icon\", 11);\n      i0.ɵɵtext(36, \" Filtrar \");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(37, \"ion-col\")(38, \"ion-button\", 12);\n      i0.ɵɵlistener(\"click\", function SalesPage_Template_ion_button_click_38_listener() {\n        return ctx.clearFilters();\n      });\n      i0.ɵɵelement(39, \"ion-icon\", 13);\n      i0.ɵɵtext(40, \" Limpar \");\n      i0.ɵɵelementEnd()()()()()();\n      i0.ɵɵelementStart(41, \"ion-list\");\n      i0.ɵɵrepeaterCreate(42, SalesPage_For_43_Template, 36, 22, \"ion-item\", null, _forTrack0, false, SalesPage_ForEmpty_44_Template, 2, 0, \"ion-item\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(45, \"ion-fab\", 14)(46, \"ion-fab-button\", 15);\n      i0.ɵɵelement(47, \"ion-icon\", 16);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(14);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.filters.storeId);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"value\", null);\n      i0.ɵɵadvance(2);\n      i0.ɵɵrepeater(ctx.storesList);\n      i0.ɵɵadvance(4);\n      i0.ɵɵtwoWayProperty(\"ngModel\", ctx.filters.status);\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"value\", null);\n      i0.ɵɵadvance(2);\n      i0.ɵɵrepeater(ctx.saleStatusOptions);\n      i0.ɵɵadvance(12);\n      i0.ɵɵrepeater(ctx.salesList);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(8, _c0));\n    }\n  },\n  dependencies: [i4.NgControlStatus, i4.NgModel, i3.IonBadge, i3.IonButton, i3.IonButtons, i3.IonCard, i3.IonCardContent, i3.IonCardHeader, i3.IonCardTitle, i3.IonCol, i3.IonContent, i3.IonFab, i3.IonFabButton, i3.IonGrid, i3.IonHeader, i3.IonIcon, i3.IonItem, i3.IonList, i3.IonMenuButton, i3.IonRow, i3.IonSelect, i3.IonSelectOption, i3.IonText, i3.IonTitle, i3.IonToolbar, i3.SelectValueAccessor, i3.RouterLinkDelegate, i5.RouterLink, i6.CurrencyPipe, i6.DatePipe],\n  styles: [\".sale-info[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.sale-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n  margin-bottom: 6px;\\n  color: var(--ion-color-secondary);\\n}\\n.sale-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .sale-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin: 4px 0;\\n  font-weight: normal;\\n  display: flex;\\n  align-items: center;\\n}\\n.sale-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .sale-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  min-width: 70px;\\n  font-weight: 600;\\n}\\n.sale-info[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 12px;\\n  letter-spacing: 0.5px;\\n}\\n.sale-info[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  --padding-start: 12px;\\n  --padding-end: 12px;\\n}\\n.sale-info[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  margin-right: 4px;\\n}\\n\\nion-item[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2FsZXMvc2FsZXMucGFnZS5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsV0FBQTtBQUNGO0FBQ0U7RUFDRSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLGlDQUFBO0FBQ0o7QUFFRTtFQUNFLGVBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7QUFBSjtBQUVJO0VBQ0UsZUFBQTtFQUNBLGdCQUFBO0FBQU47QUFJRTtFQUNFLGlCQUFBO0VBQ0EsZUFBQTtFQUNBLHFCQUFBO0FBRko7QUFLRTtFQUNFLGdCQUFBO0VBQ0EscUJBQUE7RUFDQSxtQkFBQTtBQUhKO0FBS0k7RUFDRSxpQkFBQTtBQUhOOztBQVFBO0VBQ0Usa0NBQUE7QUFMRjs7QUFRQTtFQUNFO0lBQU8sVUFBQTtJQUFZLDJCQUFBO0VBSG5CO0VBSUE7SUFBSyxVQUFBO0lBQVksd0JBQUE7RUFBakI7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5zYWxlLWluZm8ge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIFxyXG4gIGgyIHtcclxuICAgIGZvbnQtc2l6ZTogMThweDtcclxuICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogNnB4O1xyXG4gICAgY29sb3I6IHZhcigtLWlvbi1jb2xvci1zZWNvbmRhcnkpO1xyXG4gIH1cclxuICBcclxuICBoMywgaDQge1xyXG4gICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgbWFyZ2luOiA0cHggMDtcclxuICAgIGZvbnQtd2VpZ2h0OiBub3JtYWw7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIFxyXG4gICAgc3Ryb25nIHtcclxuICAgICAgbWluLXdpZHRoOiA3MHB4O1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgfVxyXG4gIH1cclxuICBcclxuICBpb24tYmFkZ2Uge1xyXG4gICAgbWFyZ2luLXJpZ2h0OiA4cHg7XHJcbiAgICBmb250LXNpemU6IDEycHg7XHJcbiAgICBsZXR0ZXItc3BhY2luZzogMC41cHg7XHJcbiAgfVxyXG4gIFxyXG4gIGlvbi1idXR0b24ge1xyXG4gICAgbWFyZ2luLXRvcDogMTBweDtcclxuICAgIC0tcGFkZGluZy1zdGFydDogMTJweDtcclxuICAgIC0tcGFkZGluZy1lbmQ6IDEycHg7XHJcbiAgICBcclxuICAgIGlvbi1pY29uIHtcclxuICAgICAgbWFyZ2luLXJpZ2h0OiA0cHg7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG5pb24taXRlbSB7XHJcbiAgYW5pbWF0aW9uOiBmYWRlSW4gMC4zcyBlYXNlLWluLW91dDtcclxufVxyXG5cclxuQGtleWZyYW1lcyBmYWRlSW4ge1xyXG4gIGZyb20geyBvcGFjaXR5OiAwOyB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMTBweCk7IH1cclxuICB0byB7IG9wYWNpdHk6IDE7IHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTsgfVxyXG59XHJcblxyXG5cclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuXHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n});", "map": {"version": 3, "names": ["SaleStatus", "PaymentMethods", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "store_r1", "id", "ɵɵadvance", "ɵɵtextInterpolate", "name", "status_r2", "value", "label", "ɵɵelement", "ɵɵlistener", "SalesPage_For_43_Template_ion_button_click_33_listener", "sale_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "cancelSale", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "date", "customer", "store", "totalValue", "getStatusColor", "status", "getStatusLabel", "getPaymentMethodLabel", "paymentMethod", "ɵɵpureFunction1", "_c1", "_c2", "SalesPage", "constructor", "saleService", "storeService", "alertController", "salesList", "storesList", "saleStatusOptions", "paymentMethodOptions", "filters", "storeId", "ngOnInit", "loadSales", "loadStores", "ionViewWillEnter", "getAll", "subscribe", "next", "sales", "error", "console", "getList", "stores", "applyFilters", "filterSales", "clearFilters", "statusObj", "find", "s", "method", "methodObj", "m", "sale", "_this", "_asyncToGenerator", "alert", "create", "header", "message", "buttons", "text", "role", "handler", "updatedSale", "save", "present", "ɵɵdirectiveInject", "i1", "SaleService", "i2", "StoreService", "i3", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectors", "standalone", "decls", "vars", "consts", "template", "SalesPage_Template", "rf", "ctx", "ɵɵtwoWayListener", "SalesPage_Template_ion_select_ngModelChange_20_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵrepeaterCreate", "SalesPage_For_24_Template", "_forTrack0", "SalesPage_Template_ion_select_ngModelChange_27_listener", "SalesPage_For_31_Template", "_forTrack1", "SalesPage_Template_ion_button_click_34_listener", "SalesPage_Template_ion_button_click_38_listener", "SalesPage_For_43_Template", "SalesPage_ForEmpty_44_Template", "ɵɵtwoWayProperty", "ɵɵrepeater", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\sales\\sales.page.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\sales\\sales.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AlertController } from '@ionic/angular';\r\nimport { Sale, SaleStatus, PaymentMethods } from './models/sale.type';\r\nimport { SaleService } from './services/sale.service';\r\nimport { StoreService } from '../stores/services/store.service';\r\nimport { Store } from '../stores/models/store.type';\r\n\r\n@Component({\r\n  selector: 'app-sales',\r\n  templateUrl: './sales.page.html',\r\n  styleUrls: ['./sales.page.scss'],\r\n  standalone: false,\r\n})\r\nexport class SalesPage implements OnInit {\r\n  salesList: Sale[] = [];\r\n  storesList: Store[] = [];\r\n  saleStatusOptions = SaleStatus;\r\n  paymentMethodOptions = PaymentMethods;\r\n  \r\n  filters = {\r\n    storeId: null as number | null,\r\n    status: null as string | null\r\n  };\r\n\r\n  constructor(\r\n    private saleService: SaleService,\r\n    private storeService: StoreService,\r\n    private alertController: AlertController\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.loadSales();\r\n    this.loadStores();\r\n  }\r\n\r\n  ionViewWillEnter() {\r\n    this.loadSales();\r\n  }\r\n\r\n  loadSales() {\r\n    this.saleService.getAll().subscribe({\r\n      next: (sales) => {\r\n        this.salesList = sales;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar vendas', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadStores() {\r\n    this.storeService.getList().subscribe({\r\n      next: (stores) => {\r\n        this.storesList = stores;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar lojas', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  applyFilters() {\r\n    this.saleService.filterSales(this.filters).subscribe({\r\n      next: (sales) => {\r\n        this.salesList = sales;\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao filtrar vendas', error);\r\n      }\r\n    });\r\n  }\r\n\r\n  clearFilters() {\r\n    this.filters = {\r\n      storeId: null,\r\n      status: null\r\n    };\r\n    this.loadSales();\r\n  }\r\n\r\n  getStatusLabel(status: string): string {\r\n    const statusObj = this.saleStatusOptions.find(s => s.value === status);\r\n    return statusObj ? statusObj.label : status;\r\n  }\r\n\r\n  getPaymentMethodLabel(method: string): string {\r\n    const methodObj = this.paymentMethodOptions.find(m => m.value === method);\r\n    return methodObj ? methodObj.label : method;\r\n  }\r\n\r\n  getStatusColor(status: string): string {\r\n    switch (status) {\r\n      case 'completed': return 'success';\r\n      case 'pending': return 'warning';\r\n      case 'canceled': return 'danger';\r\n      default: return 'medium';\r\n    }\r\n  }\r\n\r\n  async cancelSale(sale: Sale) {\r\n    const alert = await this.alertController.create({\r\n      header: 'Confirmar Cancelamento',\r\n      message: `Tem certeza que deseja cancelar a venda #${sale.id}?`,\r\n      buttons: [\r\n        {\r\n          text: 'Cancelar',\r\n          role: 'cancel'\r\n        },\r\n        {\r\n          text: 'Confirmar',\r\n          handler: () => {\r\n            const updatedSale = { ...sale, status: 'canceled' as 'canceled' };\r\n            this.saleService.save(updatedSale).subscribe({\r\n              next: () => {\r\n                this.loadSales();\r\n              },\r\n              error: (error) => {\r\n                console.error('Erro ao cancelar venda', error);\r\n              }\r\n            });\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    await alert.present();\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-menu-button></ion-menu-button>\r\n    </ion-buttons>\r\n    <ion-title>Vendas</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <ion-header collapse=\"condense\">\r\n    <ion-toolbar>\r\n      <ion-title size=\"large\">Vendas</ion-title>\r\n    </ion-toolbar>\r\n  </ion-header>\r\n\r\n  <ion-card>\r\n    <ion-card-header>\r\n      <ion-card-title>Filtros</ion-card-title>\r\n    </ion-card-header>\r\n    <ion-card-content>\r\n      <ion-grid>\r\n        <ion-row>\r\n          <ion-col size=\"12\" size-md=\"6\">\r\n            <ion-item>\r\n              <ion-select label=\"Loja\" labelPlacement=\"floating\" [(ngModel)]=\"filters.storeId\">\r\n                <ion-select-option [value]=\"null\">Todas</ion-select-option>\r\n                @for(store of storesList; track store.id) {\r\n                  <ion-select-option [value]=\"store.id\">{{ store.name }}</ion-select-option>\r\n                }\r\n              </ion-select>\r\n            </ion-item>\r\n          </ion-col>\r\n          <ion-col size=\"12\" size-md=\"6\">\r\n            <ion-item>\r\n              <ion-select label=\"Status\" labelPlacement=\"floating\" [(ngModel)]=\"filters.status\">\r\n                <ion-select-option [value]=\"null\">Todos</ion-select-option>\r\n                @for(status of saleStatusOptions; track status.value) {\r\n                  <ion-select-option [value]=\"status.value\">{{ status.label }}</ion-select-option>\r\n                }\r\n              </ion-select>\r\n            </ion-item>\r\n          </ion-col>\r\n        </ion-row>\r\n        <ion-row>\r\n          <ion-col>\r\n            <ion-button expand=\"block\" (click)=\"applyFilters()\">\r\n              <ion-icon name=\"search\" slot=\"start\"></ion-icon>\r\n              Filtrar\r\n            </ion-button>\r\n          </ion-col>\r\n          <ion-col>\r\n            <ion-button expand=\"block\" fill=\"outline\" (click)=\"clearFilters()\">\r\n              <ion-icon name=\"refresh\" slot=\"start\"></ion-icon>\r\n              Limpar\r\n            </ion-button>\r\n          </ion-col>\r\n        </ion-row>\r\n      </ion-grid>\r\n    </ion-card-content>\r\n  </ion-card>\r\n\r\n  <ion-list>\r\n    @for(sale of salesList; track sale.id) {\r\n    <ion-item>\r\n      <ion-text class=\"sale-info\">\r\n        <h2>Venda #{{ sale.id }}</h2>\r\n        <h3><strong>Data:</strong> {{ sale.date | date: 'dd/MM/yyyy' }}</h3>\r\n        <h4><strong>Cliente:</strong> {{ sale.customer.name }}</h4>\r\n        <h4><strong>Loja:</strong> {{ sale.store.name }}</h4>\r\n        <h4><strong>Valor Total:</strong> {{ sale.totalValue | currency: 'BRL' }}</h4>\r\n        <h4>\r\n          <ion-badge [color]=\"getStatusColor(sale.status)\">{{ getStatusLabel(sale.status) }}</ion-badge>\r\n          <ion-badge color=\"primary\">{{ getPaymentMethodLabel(sale.paymentMethod) }}</ion-badge>\r\n        </h4>\r\n        <ion-button size=\"small\" [routerLink]=\"['details', sale.id]\">\r\n          <ion-icon name=\"eye\" slot=\"start\"></ion-icon>\r\n          Detalhes\r\n        </ion-button>\r\n        <ion-button size=\"small\" [routerLink]=\"['edit', sale.id]\" [disabled]=\"sale.status === 'canceled'\">\r\n          <ion-icon name=\"create\" slot=\"start\"></ion-icon>\r\n          Editar\r\n        </ion-button>\r\n        <ion-button size=\"small\" (click)=\"cancelSale(sale)\" color=\"danger\" \r\n                   [disabled]=\"sale.status === 'canceled'\">\r\n          <ion-icon name=\"close-circle\" slot=\"end\"></ion-icon>\r\n          Cancelar\r\n        </ion-button>\r\n      </ion-text>\r\n    </ion-item>\r\n    }\r\n    @empty {\r\n    <ion-item>Nenhuma venda encontrada com os filtros selecionados.</ion-item>\r\n    }\r\n  </ion-list>\r\n\r\n  <ion-fab vertical=\"bottom\" horizontal=\"end\" slot=\"fixed\">\r\n    <ion-fab-button [routerLink]=\"['new']\">\r\n      <ion-icon name=\"add\"></ion-icon>\r\n    </ion-fab-button>\r\n  </ion-fab>\r\n</ion-content>\r\n"], "mappings": ";;AAEA,SAAeA,UAAU,EAAEC,cAAc,QAAQ,oBAAoB;;;;;;;;;;;;;;;IC0BnDC,EAAA,CAAAC,cAAA,2BAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAoB;;;;IAAvDH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAAC,EAAA,CAAkB;IAACN,EAAA,CAAAO,SAAA,EAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,QAAA,CAAAI,IAAA,CAAgB;;;;;IAUtDT,EAAA,CAAAC,cAAA,2BAA0C;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAoB;;;;IAA7DH,EAAA,CAAAI,UAAA,UAAAM,SAAA,CAAAC,KAAA,CAAsB;IAACX,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAQ,iBAAA,CAAAE,SAAA,CAAAE,KAAA,CAAkB;;;;;;IA4BtEZ,EAFJ,CAAAC,cAAA,eAAU,mBACoB,SACtB;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAJ,CAAAC,cAAA,SAAI,aAAQ;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAAoC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAJ,CAAAC,cAAA,SAAI,cAAQ;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvDH,EAAJ,CAAAC,cAAA,UAAI,cAAQ;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAJ,CAAAC,cAAA,UAAI,cAAQ;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAuC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5EH,EADF,CAAAC,cAAA,UAAI,qBAC+C;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC9FH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA+C;IAC5EF,EAD4E,CAAAG,YAAA,EAAY,EACnF;IACLH,EAAA,CAAAC,cAAA,sBAA6D;IAC3DD,EAAA,CAAAa,SAAA,oBAA6C;IAC7Cb,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAkG;IAChGD,EAAA,CAAAa,SAAA,oBAAgD;IAChDb,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBACmD;IAD1BD,EAAA,CAAAc,UAAA,mBAAAC,uDAAA;MAAA,MAAAC,OAAA,GAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,OAAA,CAAgB;IAAA,EAAC;IAEjDhB,EAAA,CAAAa,SAAA,oBAAoD;IACpDb,EAAA,CAAAE,MAAA,kBACF;IAEJF,EAFI,CAAAG,YAAA,EAAa,EACJ,EACF;;;;;IAvBHH,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAwB,kBAAA,YAAAR,OAAA,CAAAV,EAAA,KAAoB;IACGN,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAwB,kBAAA,MAAAxB,EAAA,CAAAyB,WAAA,QAAAT,OAAA,CAAAU,IAAA,oBAAoC;IACjC1B,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAwB,kBAAA,MAAAR,OAAA,CAAAW,QAAA,CAAAlB,IAAA,KAAwB;IAC3BT,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAwB,kBAAA,MAAAR,OAAA,CAAAY,KAAA,CAAAnB,IAAA,KAAqB;IACdT,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAwB,kBAAA,MAAAxB,EAAA,CAAAyB,WAAA,SAAAT,OAAA,CAAAa,UAAA,aAAuC;IAE5D7B,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAI,UAAA,UAAAgB,MAAA,CAAAU,cAAA,CAAAd,OAAA,CAAAe,MAAA,EAAqC;IAAC/B,EAAA,CAAAO,SAAA,EAAiC;IAAjCP,EAAA,CAAAQ,iBAAA,CAAAY,MAAA,CAAAY,cAAA,CAAAhB,OAAA,CAAAe,MAAA,EAAiC;IACvD/B,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAQ,iBAAA,CAAAY,MAAA,CAAAa,qBAAA,CAAAjB,OAAA,CAAAkB,aAAA,EAA+C;IAEnDlC,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAmC,eAAA,KAAAC,GAAA,EAAApB,OAAA,CAAAV,EAAA,EAAmC;IAInCN,EAAA,CAAAO,SAAA,GAAgC;IAACP,EAAjC,CAAAI,UAAA,eAAAJ,EAAA,CAAAmC,eAAA,KAAAE,GAAA,EAAArB,OAAA,CAAAV,EAAA,EAAgC,aAAAU,OAAA,CAAAe,MAAA,gBAAwC;IAKtF/B,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAI,UAAA,aAAAY,OAAA,CAAAe,MAAA,gBAAuC;;;;;IAQtD/B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,4DAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;AD/E9E,OAAM,MAAOmC,SAAS;EAWpBC,YACUC,WAAwB,EACxBC,YAA0B,EAC1BC,eAAgC;IAFhC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IAbzB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,UAAU,GAAY,EAAE;IACxB,KAAAC,iBAAiB,GAAG/C,UAAU;IAC9B,KAAAgD,oBAAoB,GAAG/C,cAAc;IAErC,KAAAgD,OAAO,GAAG;MACRC,OAAO,EAAE,IAAqB;MAC9BjB,MAAM,EAAE;KACT;EAMG;EAEJkB,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACF,SAAS,EAAE;EAClB;EAEAA,SAASA,CAAA;IACP,IAAI,CAACV,WAAW,CAACa,MAAM,EAAE,CAACC,SAAS,CAAC;MAClCC,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAACb,SAAS,GAAGa,KAAK;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;KACD,CAAC;EACJ;EAEAN,UAAUA,CAAA;IACR,IAAI,CAACV,YAAY,CAACkB,OAAO,EAAE,CAACL,SAAS,CAAC;MACpCC,IAAI,EAAGK,MAAM,IAAI;QACf,IAAI,CAAChB,UAAU,GAAGgB,MAAM;MAC1B,CAAC;MACDH,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAI,YAAYA,CAAA;IACV,IAAI,CAACrB,WAAW,CAACsB,WAAW,CAAC,IAAI,CAACf,OAAO,CAAC,CAACO,SAAS,CAAC;MACnDC,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAACb,SAAS,GAAGa,KAAK;MACxB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD;KACD,CAAC;EACJ;EAEAM,YAAYA,CAAA;IACV,IAAI,CAAChB,OAAO,GAAG;MACbC,OAAO,EAAE,IAAI;MACbjB,MAAM,EAAE;KACT;IACD,IAAI,CAACmB,SAAS,EAAE;EAClB;EAEAlB,cAAcA,CAACD,MAAc;IAC3B,MAAMiC,SAAS,GAAG,IAAI,CAACnB,iBAAiB,CAACoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvD,KAAK,KAAKoB,MAAM,CAAC;IACtE,OAAOiC,SAAS,GAAGA,SAAS,CAACpD,KAAK,GAAGmB,MAAM;EAC7C;EAEAE,qBAAqBA,CAACkC,MAAc;IAClC,MAAMC,SAAS,GAAG,IAAI,CAACtB,oBAAoB,CAACmB,IAAI,CAACI,CAAC,IAAIA,CAAC,CAAC1D,KAAK,KAAKwD,MAAM,CAAC;IACzE,OAAOC,SAAS,GAAGA,SAAS,CAACxD,KAAK,GAAGuD,MAAM;EAC7C;EAEArC,cAAcA,CAACC,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,UAAU;QAAE,OAAO,QAAQ;MAChC;QAAS,OAAO,QAAQ;IAC1B;EACF;EAEMR,UAAUA,CAAC+C,IAAU;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACzB,MAAMC,KAAK,SAASF,KAAI,CAAC7B,eAAe,CAACgC,MAAM,CAAC;QAC9CC,MAAM,EAAE,wBAAwB;QAChCC,OAAO,EAAE,4CAA4CN,IAAI,CAAChE,EAAE,GAAG;QAC/DuE,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAE;SACP,EACD;UACED,IAAI,EAAE,WAAW;UACjBE,OAAO,EAAEA,CAAA,KAAK;YACZ,MAAMC,WAAW,GAAG;cAAE,GAAGX,IAAI;cAAEvC,MAAM,EAAE;YAAwB,CAAE;YACjEwC,KAAI,CAAC/B,WAAW,CAAC0C,IAAI,CAACD,WAAW,CAAC,CAAC3B,SAAS,CAAC;cAC3CC,IAAI,EAAEA,CAAA,KAAK;gBACTgB,KAAI,CAACrB,SAAS,EAAE;cAClB,CAAC;cACDO,KAAK,EAAGA,KAAK,IAAI;gBACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;cAChD;aACD,CAAC;UACJ;SACD;OAEJ,CAAC;MAEF,MAAMgB,KAAK,CAACU,OAAO,EAAE;IAAC;EACxB;;aAjHW7C,SAAS;;mCAATA,UAAS,EAAAtC,EAAA,CAAAoF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtF,EAAA,CAAAoF,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAxF,EAAA,CAAAoF,iBAAA,CAAAK,EAAA,CAAAC,eAAA;AAAA;;QAATpD,UAAS;EAAAqD,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,mBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCXlBlG,EAFJ,CAAAC,cAAA,oBAAiC,qBACA,qBACH;MACxBD,EAAA,CAAAa,SAAA,sBAAmC;MACrCb,EAAA,CAAAG,YAAA,EAAc;MACdH,EAAA,CAAAC,cAAA,gBAAW;MAAAD,EAAA,CAAAE,MAAA,aAAM;MAErBF,EAFqB,CAAAG,YAAA,EAAY,EACjB,EACH;MAKPH,EAHN,CAAAC,cAAA,qBAAiC,oBACC,kBACjB,mBACa;MAAAD,EAAA,CAAAE,MAAA,cAAM;MAElCF,EAFkC,CAAAG,YAAA,EAAY,EAC9B,EACH;MAITH,EAFJ,CAAAC,cAAA,gBAAU,uBACS,sBACC;MAAAD,EAAA,CAAAE,MAAA,eAAO;MACzBF,EADyB,CAAAG,YAAA,EAAiB,EACxB;MAMRH,EALV,CAAAC,cAAA,wBAAkB,gBACN,eACC,kBACwB,gBACnB,qBACyE;MAA9BD,EAAA,CAAAoG,gBAAA,2BAAAC,wDAAAC,MAAA;QAAAtG,EAAA,CAAAuG,kBAAA,CAAAJ,GAAA,CAAApD,OAAA,CAAAC,OAAA,EAAAsD,MAAA,MAAAH,GAAA,CAAApD,OAAA,CAAAC,OAAA,GAAAsD,MAAA;QAAA,OAAAA,MAAA;MAAA,EAA6B;MAC9EtG,EAAA,CAAAC,cAAA,4BAAkC;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAoB;MAC3DH,EAAA,CAAAwG,gBAAA,KAAAC,yBAAA,gCAAAC,UAAA,CAEC;MAGP1G,EAFI,CAAAG,YAAA,EAAa,EACJ,EACH;MAGNH,EAFJ,CAAAC,cAAA,kBAA+B,gBACnB,qBAC0E;MAA7BD,EAAA,CAAAoG,gBAAA,2BAAAO,wDAAAL,MAAA;QAAAtG,EAAA,CAAAuG,kBAAA,CAAAJ,GAAA,CAAApD,OAAA,CAAAhB,MAAA,EAAAuE,MAAA,MAAAH,GAAA,CAAApD,OAAA,CAAAhB,MAAA,GAAAuE,MAAA;QAAA,OAAAA,MAAA;MAAA,EAA4B;MAC/EtG,EAAA,CAAAC,cAAA,4BAAkC;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAAAF,EAAA,CAAAG,YAAA,EAAoB;MAC3DH,EAAA,CAAAwG,gBAAA,KAAAI,yBAAA,gCAAAC,UAAA,CAEC;MAIT7G,EAHM,CAAAG,YAAA,EAAa,EACJ,EACH,EACF;MAGNH,EAFJ,CAAAC,cAAA,eAAS,eACE,sBAC6C;MAAzBD,EAAA,CAAAc,UAAA,mBAAAgG,gDAAA;QAAA,OAASX,GAAA,CAAAtC,YAAA,EAAc;MAAA,EAAC;MACjD7D,EAAA,CAAAa,SAAA,oBAAgD;MAChDb,EAAA,CAAAE,MAAA,iBACF;MACFF,EADE,CAAAG,YAAA,EAAa,EACL;MAERH,EADF,CAAAC,cAAA,eAAS,sBAC4D;MAAzBD,EAAA,CAAAc,UAAA,mBAAAiG,gDAAA;QAAA,OAASZ,GAAA,CAAApC,YAAA,EAAc;MAAA,EAAC;MAChE/D,EAAA,CAAAa,SAAA,oBAAiD;MACjDb,EAAA,CAAAE,MAAA,gBACF;MAKVF,EALU,CAAAG,YAAA,EAAa,EACL,EACF,EACD,EACM,EACV;MAEXH,EAAA,CAAAC,cAAA,gBAAU;MACRD,EAAA,CAAAwG,gBAAA,KAAAQ,yBAAA,4BAAAN,UAAA,SAAAO,8BAAA,mBA8BC;MACHjH,EAAA,CAAAG,YAAA,EAAW;MAGTH,EADF,CAAAC,cAAA,mBAAyD,0BAChB;MACrCD,EAAA,CAAAa,SAAA,oBAAgC;MAGtCb,EAFI,CAAAG,YAAA,EAAiB,EACT,EACE;;;MArGFH,EAAA,CAAAI,UAAA,qBAAoB;MASnBJ,EAAA,CAAAO,SAAA,GAAmB;MAAnBP,EAAA,CAAAI,UAAA,oBAAmB;MAgBiCJ,EAAA,CAAAO,SAAA,IAA6B;MAA7BP,EAAA,CAAAkH,gBAAA,YAAAf,GAAA,CAAApD,OAAA,CAAAC,OAAA,CAA6B;MAC3DhD,EAAA,CAAAO,SAAA,EAAc;MAAdP,EAAA,CAAAI,UAAA,eAAc;MACjCJ,EAAA,CAAAO,SAAA,GAEC;MAFDP,EAAA,CAAAmH,UAAA,CAAAhB,GAAA,CAAAvD,UAAA,CAEC;MAMkD5C,EAAA,CAAAO,SAAA,GAA4B;MAA5BP,EAAA,CAAAkH,gBAAA,YAAAf,GAAA,CAAApD,OAAA,CAAAhB,MAAA,CAA4B;MAC5D/B,EAAA,CAAAO,SAAA,EAAc;MAAdP,EAAA,CAAAI,UAAA,eAAc;MACjCJ,EAAA,CAAAO,SAAA,GAEC;MAFDP,EAAA,CAAAmH,UAAA,CAAAhB,GAAA,CAAAtD,iBAAA,CAEC;MAwBb7C,EAAA,CAAAO,SAAA,IA8BC;MA9BDP,EAAA,CAAAmH,UAAA,CAAAhB,GAAA,CAAAxD,SAAA,CA8BC;MAIe3C,EAAA,CAAAO,SAAA,GAAsB;MAAtBP,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAoH,eAAA,IAAAC,GAAA,EAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}