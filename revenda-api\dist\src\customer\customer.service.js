"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const customer_entity_1 = require("./customer.entity");
let CustomerService = class CustomerService {
    customerRepository;
    constructor(customerRepository) {
        this.customerRepository = customerRepository;
    }
    async create(createCustomerDto) {
        const existingCustomer = await this.customerRepository.findOne({
            where: { email: createCustomerDto.email }
        });
        if (existingCustomer) {
            throw new common_1.ConflictException(`Email '${createCustomerDto.email}' já está em uso`);
        }
        const customer = this.customerRepository.create(createCustomerDto);
        return this.customerRepository.save(customer);
    }
    findAll() {
        return this.customerRepository.find({
            relations: ['sales'],
        });
    }
    findOne(id) {
        return this.customerRepository.findOne({
            where: { id },
            relations: ['sales'],
        });
    }
    findByEmail(email) {
        return this.customerRepository.findOne({
            where: { email },
        });
    }
    async update(id, updateCustomerDto) {
        if (updateCustomerDto.email) {
            const existingCustomer = await this.customerRepository.findOne({
                where: { email: updateCustomerDto.email }
            });
            if (existingCustomer && existingCustomer.id !== id) {
                throw new common_1.ConflictException(`Email '${updateCustomerDto.email}' já está em uso por outro cliente`);
            }
        }
        if (updateCustomerDto.customerType) {
            const existingCustomer = await this.customerRepository.findOne({ where: { id } });
            if (!existingCustomer) {
                throw new common_1.NotFoundException(`Cliente com ID ${id} não encontrado`);
            }
            if (existingCustomer.customerType === 'vip' && updateCustomerDto.customerType === 'regular') {
                throw new common_1.BadRequestException('Não é permitido fazer downgrade de cliente VIP para regular. Entre em contato com o suporte.');
            }
        }
        return this.customerRepository.update(id, updateCustomerDto);
    }
    async remove(id) {
        const customer = await this.customerRepository.findOne({
            where: { id },
            relations: ['sales']
        });
        if (!customer) {
            throw new common_1.NotFoundException(`Cliente com ID ${id} não encontrado`);
        }
        if (customer.sales && customer.sales.length > 0) {
            throw new common_1.BadRequestException(`Não é possível deletar cliente com vendas associadas. Cliente possui ${customer.sales.length} venda(s).`);
        }
        return this.customerRepository.delete(id);
    }
};
exports.CustomerService = CustomerService;
exports.CustomerService = CustomerService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(customer_entity_1.Customer)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CustomerService);
//# sourceMappingURL=customer.service.js.map