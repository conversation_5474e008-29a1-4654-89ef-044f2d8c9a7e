{"ast": null, "code": "var _CustomerService;\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CustomerService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.baseUrl}/customers`;\n  }\n  getAll() {\n    return this.http.get(this.apiUrl);\n  }\n  getList() {\n    return this.getAll();\n  }\n  getById(id) {\n    return this.http.get(`${this.apiUrl}/${id}`);\n  }\n  getByType(customerType) {\n    return this.http.get(`${this.apiUrl}/type/${customerType}`);\n  }\n  add(customer) {\n    return this.http.post(this.apiUrl, customer);\n  }\n  update(id, customer) {\n    return this.http.patch(`${this.apiUrl}/${id}`, customer);\n  }\n  save(customer) {\n    if (customer.id) {\n      const updateData = {\n        name: customer.name,\n        email: customer.email,\n        phone: customer.phone,\n        birthDate: customer.birthDate instanceof Date ? customer.birthDate : new Date(customer.birthDate),\n        address: customer.address,\n        customerType: customer.customerType,\n        active: customer.active\n      };\n      return this.update(customer.id, updateData);\n    } else {\n      const createData = {\n        name: customer.name,\n        email: customer.email,\n        phone: customer.phone,\n        birthDate: customer.birthDate instanceof Date ? customer.birthDate : new Date(customer.birthDate),\n        address: customer.address,\n        customerType: customer.customerType,\n        active: customer.active\n      };\n      return this.add(createData);\n    }\n  }\n  remove(customer) {\n    return this.http.delete(`${this.apiUrl}/${customer.id}`);\n  }\n  delete(id) {\n    return this.http.delete(`${this.apiUrl}/${id}`);\n  }\n}\n_CustomerService = CustomerService;\n_CustomerService.ɵfac = function CustomerService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CustomerService)(i0.ɵɵinject(i1.HttpClient));\n};\n_CustomerService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _CustomerService,\n  factory: _CustomerService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["environment", "CustomerService", "constructor", "http", "apiUrl", "baseUrl", "getAll", "get", "getList", "getById", "id", "getByType", "customerType", "add", "customer", "post", "update", "patch", "save", "updateData", "name", "email", "phone", "birthDate", "Date", "address", "active", "createData", "remove", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\customers\\services\\customer.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { Customer, CreateCustomerDto, UpdateCustomerDto } from '../models/customer.type';\r\nimport { environment } from '../../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CustomerService {\r\n  private apiUrl = `${environment.baseUrl}/customers`;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getAll(): Observable<Customer[]> {\r\n    return this.http.get<Customer[]>(this.apiUrl);\r\n  }\r\n\r\n  getList(): Observable<Customer[]> {\r\n    return this.getAll();\r\n  }\r\n\r\n  getById(id: number): Observable<Customer> {\r\n    return this.http.get<Customer>(`${this.apiUrl}/${id}`);\r\n  }\r\n\r\n  getByType(customerType: string): Observable<Customer[]> {\r\n    return this.http.get<Customer[]>(`${this.apiUrl}/type/${customerType}`);\r\n  }\r\n\r\n  private add(customer: CreateCustomerDto): Observable<Customer> {\r\n    return this.http.post<Customer>(this.apiUrl, customer);\r\n  }\r\n\r\n  private update(id: number, customer: UpdateCustomerDto): Observable<any> {\r\n    return this.http.patch(`${this.apiUrl}/${id}`, customer);\r\n  }\r\n\r\n  save(customer: Customer): Observable<any> {\r\n    if (customer.id) {\r\n      const updateData: UpdateCustomerDto = {\r\n        name: customer.name,\r\n        email: customer.email,\r\n        phone: customer.phone,\r\n        birthDate: customer.birthDate instanceof Date ? customer.birthDate : new Date(customer.birthDate),\r\n        address: customer.address,\r\n        customerType: customer.customerType,\r\n        active: customer.active\r\n      };\r\n      return this.update(customer.id, updateData);\r\n    } else {\r\n      const createData: CreateCustomerDto = {\r\n        name: customer.name,\r\n        email: customer.email,\r\n        phone: customer.phone,\r\n        birthDate: customer.birthDate instanceof Date ? customer.birthDate : new Date(customer.birthDate),\r\n        address: customer.address,\r\n        customerType: customer.customerType,\r\n        active: customer.active\r\n      };\r\n      return this.add(createData);\r\n    }\r\n  }\r\n\r\n  remove(customer: Customer): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/${customer.id}`);\r\n  }\r\n\r\n  delete(id: number): Observable<void> {\r\n    return this.http.delete<void>(`${this.apiUrl}/${id}`);\r\n  }\r\n}\r\n"], "mappings": ";AAIA,SAASA,WAAW,QAAQ,mCAAmC;;;AAK/D,OAAM,MAAOC,eAAe;EAG1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,OAAO,YAAY;EAEX;EAExCC,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAa,IAAI,CAACH,MAAM,CAAC;EAC/C;EAEAI,OAAOA,CAAA;IACL,OAAO,IAAI,CAACF,MAAM,EAAE;EACtB;EAEAG,OAAOA,CAACC,EAAU;IAChB,OAAO,IAAI,CAACP,IAAI,CAACI,GAAG,CAAW,GAAG,IAAI,CAACH,MAAM,IAAIM,EAAE,EAAE,CAAC;EACxD;EAEAC,SAASA,CAACC,YAAoB;IAC5B,OAAO,IAAI,CAACT,IAAI,CAACI,GAAG,CAAa,GAAG,IAAI,CAACH,MAAM,SAASQ,YAAY,EAAE,CAAC;EACzE;EAEQC,GAAGA,CAACC,QAA2B;IACrC,OAAO,IAAI,CAACX,IAAI,CAACY,IAAI,CAAW,IAAI,CAACX,MAAM,EAAEU,QAAQ,CAAC;EACxD;EAEQE,MAAMA,CAACN,EAAU,EAAEI,QAA2B;IACpD,OAAO,IAAI,CAACX,IAAI,CAACc,KAAK,CAAC,GAAG,IAAI,CAACb,MAAM,IAAIM,EAAE,EAAE,EAAEI,QAAQ,CAAC;EAC1D;EAEAI,IAAIA,CAACJ,QAAkB;IACrB,IAAIA,QAAQ,CAACJ,EAAE,EAAE;MACf,MAAMS,UAAU,GAAsB;QACpCC,IAAI,EAAEN,QAAQ,CAACM,IAAI;QACnBC,KAAK,EAAEP,QAAQ,CAACO,KAAK;QACrBC,KAAK,EAAER,QAAQ,CAACQ,KAAK;QACrBC,SAAS,EAAET,QAAQ,CAACS,SAAS,YAAYC,IAAI,GAAGV,QAAQ,CAACS,SAAS,GAAG,IAAIC,IAAI,CAACV,QAAQ,CAACS,SAAS,CAAC;QACjGE,OAAO,EAAEX,QAAQ,CAACW,OAAO;QACzBb,YAAY,EAAEE,QAAQ,CAACF,YAAY;QACnCc,MAAM,EAAEZ,QAAQ,CAACY;OAClB;MACD,OAAO,IAAI,CAACV,MAAM,CAACF,QAAQ,CAACJ,EAAE,EAAES,UAAU,CAAC;IAC7C,CAAC,MAAM;MACL,MAAMQ,UAAU,GAAsB;QACpCP,IAAI,EAAEN,QAAQ,CAACM,IAAI;QACnBC,KAAK,EAAEP,QAAQ,CAACO,KAAK;QACrBC,KAAK,EAAER,QAAQ,CAACQ,KAAK;QACrBC,SAAS,EAAET,QAAQ,CAACS,SAAS,YAAYC,IAAI,GAAGV,QAAQ,CAACS,SAAS,GAAG,IAAIC,IAAI,CAACV,QAAQ,CAACS,SAAS,CAAC;QACjGE,OAAO,EAAEX,QAAQ,CAACW,OAAO;QACzBb,YAAY,EAAEE,QAAQ,CAACF,YAAY;QACnCc,MAAM,EAAEZ,QAAQ,CAACY;OAClB;MACD,OAAO,IAAI,CAACb,GAAG,CAACc,UAAU,CAAC;IAC7B;EACF;EAEAC,MAAMA,CAACd,QAAkB;IACvB,OAAO,IAAI,CAACX,IAAI,CAAC0B,MAAM,CAAC,GAAG,IAAI,CAACzB,MAAM,IAAIU,QAAQ,CAACJ,EAAE,EAAE,CAAC;EAC1D;EAEAmB,MAAMA,CAACnB,EAAU;IACf,OAAO,IAAI,CAACP,IAAI,CAAC0B,MAAM,CAAO,GAAG,IAAI,CAACzB,MAAM,IAAIM,EAAE,EAAE,CAAC;EACvD;;mBA7DWT,eAAe;;mCAAfA,gBAAe,EAAA6B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAfhC,gBAAe;EAAAiC,OAAA,EAAfjC,gBAAe,CAAAkC,IAAA;EAAAC,UAAA,EAFd;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}