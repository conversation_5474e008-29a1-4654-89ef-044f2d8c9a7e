{"ast": null, "code": "var _BrandsPageModule;\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { BrandsPageRoutingModule } from './brands-routing.module';\nimport { HttpClientModule } from '@angular/common/http';\nimport { BrandsPage } from './brands.page';\nimport { BrandFormComponent } from './brand-form/brand-form.component';\nimport * as i0 from \"@angular/core\";\nexport class BrandsPageModule {}\n_BrandsPageModule = BrandsPageModule;\n_BrandsPageModule.ɵfac = function BrandsPageModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BrandsPageModule)();\n};\n_BrandsPageModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _BrandsPageModule\n});\n_BrandsPageModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [CommonModule, FormsModule, IonicModule, BrandsPageRoutingModule, ReactiveFormsModule, HttpClientModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(BrandsPageModule, {\n    declarations: [BrandsPage, BrandFormComponent],\n    imports: [CommonModule, FormsModule, IonicModule, BrandsPageRoutingModule, ReactiveFormsModule, HttpClientModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "IonicModule", "BrandsPageRoutingModule", "HttpClientModule", "BrandsPage", "BrandFormComponent", "BrandsPageModule", "declarations", "imports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\brands\\brands.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { BrandsPageRoutingModule } from './brands-routing.module';\r\nimport { HttpClientModule } from '@angular/common/http';\r\nimport { BrandsPage } from './brands.page';\r\nimport { BrandFormComponent } from './brand-form/brand-form.component';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    BrandsPageRoutingModule,\r\n    ReactiveFormsModule,\r\n    HttpClientModule,\r\n  ],\r\n  declarations: [\r\n    BrandsPage, \r\n    BrandFormComponent\r\n  ]\r\n})\r\nexport class BrandsPageModule {}\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,uBAAuB,QAAQ,yBAAyB;AACjE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,kBAAkB,QAAQ,mCAAmC;;AAgBtE,OAAM,MAAOC,gBAAgB;oBAAhBA,gBAAgB;;mCAAhBA,iBAAgB;AAAA;;QAAhBA;AAAgB;;YAZzBR,YAAY,EACZC,WAAW,EACXE,WAAW,EACXC,uBAAuB,EACvBF,mBAAmB,EACnBG,gBAAgB;AAAA;;2EAOPG,gBAAgB;IAAAC,YAAA,GAJzBH,UAAU,EACVC,kBAAkB;IAAAG,OAAA,GATlBV,YAAY,EACZC,WAAW,EACXE,WAAW,EACXC,uBAAuB,EACvBF,mBAAmB,EACnBG,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}