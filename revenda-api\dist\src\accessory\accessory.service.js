"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccessoryService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const accessory_entity_1 = require("./accessory.entity");
const phone_entity_1 = require("../phone/phone.entity");
let AccessoryService = class AccessoryService {
    accessoryRepository;
    phoneRepository;
    constructor(accessoryRepository, phoneRepository) {
        this.accessoryRepository = accessoryRepository;
        this.phoneRepository = phoneRepository;
    }
    async create(createAccessoryDto) {
        const existingAccessory = await this.accessoryRepository.findOne({
            where: { name: createAccessoryDto.name }
        });
        if (existingAccessory) {
            throw new common_1.ConflictException(`Acessório '${createAccessoryDto.name}' já existe no sistema`);
        }
        if (createAccessoryDto.price < 0) {
            throw new common_1.BadRequestException(`Preço não pode ser negativo. Valor informado: R$ ${createAccessoryDto.price}`);
        }
        const { compatiblePhoneIds, ...accessoryData } = createAccessoryDto;
        const accessory = this.accessoryRepository.create(accessoryData);
        if (compatiblePhoneIds && compatiblePhoneIds.length > 0) {
            const phones = await this.phoneRepository.find({
                where: { id: (0, typeorm_2.In)(compatiblePhoneIds) }
            });
            accessory.compatiblePhones = phones;
        }
        return this.accessoryRepository.save(accessory);
    }
    async findAllInStock() {
        const accessories = await this.accessoryRepository.find({
            where: { stock: (0, typeorm_2.MoreThan)(0) },
            relations: ['compatiblePhones'],
        });
        if (accessories.length === 0) {
            throw new common_1.BadRequestException('Nenhum acessório disponível em estoque no momento');
        }
        return accessories;
    }
    findAll() {
        return this.accessoryRepository.find({
            relations: ['compatiblePhones'],
        });
    }
    findOne(id) {
        return this.accessoryRepository.findOne({
            where: { id },
            relations: ['compatiblePhones'],
        });
    }
    findByCategory(category) {
        return this.accessoryRepository.find({
            where: { category },
            relations: ['compatiblePhones'],
        });
    }
    findInStock() {
        return this.accessoryRepository
            .createQueryBuilder('accessory')
            .where('accessory.stock > 0')
            .getMany();
    }
    async update(id, updateAccessoryDto) {
        const { compatiblePhoneIds, ...accessoryData } = updateAccessoryDto;
        if (accessoryData.name) {
            const existingAccessory = await this.accessoryRepository.findOne({
                where: { name: accessoryData.name }
            });
            if (existingAccessory && existingAccessory.id !== id) {
                throw new common_1.ConflictException(`Acessório '${accessoryData.name}' já existe no sistema`);
            }
        }
        await this.accessoryRepository.update(id, accessoryData);
        if (compatiblePhoneIds !== undefined) {
            const accessory = await this.accessoryRepository.findOne({
                where: { id },
                relations: ['compatiblePhones']
            });
            if (accessory) {
                if (compatiblePhoneIds.length > 0) {
                    const phones = await this.phoneRepository.find({
                        where: { id: (0, typeorm_2.In)(compatiblePhoneIds) }
                    });
                    accessory.compatiblePhones = phones;
                }
                else {
                    accessory.compatiblePhones = [];
                }
                await this.accessoryRepository.save(accessory);
            }
        }
        return { message: 'Acessório atualizado com sucesso' };
    }
    async updateStock(id, quantity) {
        const accessory = await this.accessoryRepository.findOne({ where: { id } });
        if (!accessory) {
            throw new common_1.BadRequestException(`Acessório com ID ${id} não encontrado`);
        }
        const newStock = accessory.stock + quantity;
        if (newStock < 0) {
            throw new common_1.BadRequestException(`Operação resultaria em estoque negativo. Estoque atual: ${accessory.stock}, Quantidade solicitada: ${quantity}, Resultado: ${newStock}`);
        }
        return this.accessoryRepository
            .createQueryBuilder()
            .update(accessory_entity_1.Accessory)
            .set({ stock: () => `stock + ${quantity}` })
            .where('id = :id', { id })
            .execute();
    }
    async remove(id) {
        const accessory = await this.accessoryRepository.findOne({ where: { id } });
        if (!accessory) {
            throw new common_1.NotFoundException(`Acessório com ID ${id} não encontrado`);
        }
        const accessoryInSales = await this.accessoryRepository
            .createQueryBuilder('accessory')
            .innerJoin('sale_items', 'si', 'si.product_id = accessory.id AND si.productType = :type', { type: 'accessory' })
            .where('accessory.id = :id', { id })
            .getOne();
        if (accessoryInSales) {
            throw new common_1.BadRequestException(`Não é possível deletar acessório que está associado a vendas. Nome: ${accessory.name}`);
        }
        return this.accessoryRepository.delete(id);
    }
};
exports.AccessoryService = AccessoryService;
exports.AccessoryService = AccessoryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(accessory_entity_1.Accessory)),
    __param(1, (0, typeorm_1.InjectRepository)(phone_entity_1.Phone)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], AccessoryService);
//# sourceMappingURL=accessory.service.js.map