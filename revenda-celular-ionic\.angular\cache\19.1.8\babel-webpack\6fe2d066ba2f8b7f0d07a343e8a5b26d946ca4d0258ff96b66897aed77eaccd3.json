{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-28849c61.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { o as getPresentedOverlay, B as BACKDROP, n as focusFirstDescendant, q as focusLastDescendant, G as GESTURE } from './overlays-e7b9d6d9.js';\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-314a54f6.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-06ef3c3e.js';\nimport { n as isEndSide, i as inheritAriaAttributes, m as assert, j as clamp } from './helpers-da915de8.js';\nimport { m as menuController } from './index-24b48b06.js';\nimport { c as config, b as getIonMode, a as isPlatform } from './ionic-global-c81d82ab.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { u as menuOutline, v as menuSharp } from './index-e2cf2ceb.js';\nimport './index-a5d50daf.js';\nimport './framework-delegate-63d1a679.js';\nimport './index-9b0d46f4.js';\nimport './animation-eab5a4ca.js';\nconst menuIosCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width, var(--width));min-width:var(--side-min-width, var(--min-width));max-width:var(--side-max-width, var(--max-width))}:host(.menu-pane-visible.split-pane-side){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.menu-pane-visible.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}:host(.menu-pane-visible.split-pane-side){-ms-flex-order:-1;order:-1}:host(.menu-pane-visible.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-pane-visible.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-pane-visible.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-type-push){z-index:1000}:host(.menu-type-push) .show-backdrop{display:block}\";\nconst IonMenuIosStyle0 = menuIosCss;\nconst menuMdCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width, var(--width));min-width:var(--side-min-width, var(--min-width));max-width:var(--side-max-width, var(--max-width))}:host(.menu-pane-visible.split-pane-side){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.menu-pane-visible.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}:host(.menu-pane-visible.split-pane-side){-ms-flex-order:-1;order:-1}:host(.menu-pane-visible.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-pane-visible.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-pane-visible.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-type-overlay) .menu-inner{-webkit-box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18);box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18)}\";\nconst IonMenuMdStyle0 = menuMdCss;\nconst iosEasing = 'cubic-bezier(0.32,0.72,0,1)';\nconst mdEasing = 'cubic-bezier(0.0,0.0,0.2,1)';\nconst iosEasingReverse = 'cubic-bezier(1, 0, 0.68, 0.28)';\nconst mdEasingReverse = 'cubic-bezier(0.4, 0, 0.6, 1)';\nconst Menu = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionWillOpen = createEvent(this, \"ionWillOpen\", 7);\n    this.ionWillClose = createEvent(this, \"ionWillClose\", 7);\n    this.ionDidOpen = createEvent(this, \"ionDidOpen\", 7);\n    this.ionDidClose = createEvent(this, \"ionDidClose\", 7);\n    this.ionMenuChange = createEvent(this, \"ionMenuChange\", 7);\n    this.lastOnEnd = 0;\n    this.blocker = GESTURE_CONTROLLER.createBlocker({\n      disableScroll: true\n    });\n    this.didLoad = false;\n    /**\n     * Flag used to determine if an open/close\n     * operation was cancelled. For example, if\n     * an app calls \"menu.open\" then disables the menu\n     * part way through the animation, then this would\n     * be considered a cancelled operation.\n     */\n    this.operationCancelled = false;\n    this.isAnimating = false;\n    this._isOpen = false;\n    this.inheritedAttributes = {};\n    this.handleFocus = ev => {\n      /**\n       * Overlays have their own focus trapping listener\n       * so we do not want the two listeners to conflict\n       * with each other. If the top-most overlay that is\n       * open does not contain this ion-menu, then ion-menu's\n       * focus trapping should not run.\n       */\n      const lastOverlay = getPresentedOverlay(document);\n      if (lastOverlay && !lastOverlay.contains(this.el)) {\n        return;\n      }\n      this.trapKeyboardFocus(ev, document);\n    };\n    this.isPaneVisible = false;\n    this.isEndSide = false;\n    this.contentId = undefined;\n    this.menuId = undefined;\n    this.type = undefined;\n    this.disabled = false;\n    this.side = 'start';\n    this.swipeGesture = true;\n    this.maxEdgeStart = 50;\n  }\n  typeChanged(type, oldType) {\n    const contentEl = this.contentEl;\n    if (contentEl) {\n      if (oldType !== undefined) {\n        contentEl.classList.remove(`menu-content-${oldType}`);\n      }\n      contentEl.classList.add(`menu-content-${type}`);\n      contentEl.removeAttribute('style');\n    }\n    if (this.menuInnerEl) {\n      // Remove effects of previous animations\n      this.menuInnerEl.removeAttribute('style');\n    }\n    this.animation = undefined;\n  }\n  disabledChanged() {\n    this.updateState();\n    this.ionMenuChange.emit({\n      disabled: this.disabled,\n      open: this._isOpen\n    });\n  }\n  sideChanged() {\n    this.isEndSide = isEndSide(this.side);\n    /**\n     * Menu direction animation is calculated based on the document direction.\n     * If the document direction changes, we need to create a new animation.\n     */\n    this.animation = undefined;\n  }\n  swipeGestureChanged() {\n    this.updateState();\n  }\n  connectedCallback() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // TODO: connectedCallback is fired in CE build\n      // before WC is defined. This needs to be fixed in Stencil.\n      if (typeof customElements !== 'undefined' && customElements != null) {\n        yield customElements.whenDefined('ion-menu');\n      }\n      if (_this.type === undefined) {\n        _this.type = config.get('menuType', 'overlay');\n      }\n      const content = _this.contentId !== undefined ? document.getElementById(_this.contentId) : null;\n      if (content === null) {\n        console.error('Menu: must have a \"content\" element to listen for drag events on.');\n        return;\n      }\n      if (_this.el.contains(content)) {\n        console.error(`Menu: \"contentId\" should refer to the main view's ion-content, not the ion-content inside of the ion-menu.`);\n      }\n      _this.contentEl = content;\n      // add menu's content classes\n      content.classList.add('menu-content');\n      _this.typeChanged(_this.type, undefined);\n      _this.sideChanged();\n      // register this menu with the app's menu controller\n      menuController._register(_this);\n      _this.menuChanged();\n      _this.gesture = (yield import('./index-39782642.js')).createGesture({\n        el: document,\n        gestureName: 'menu-swipe',\n        gesturePriority: 30,\n        threshold: 10,\n        blurOnStart: true,\n        canStart: ev => _this.canStart(ev),\n        onWillStart: () => _this.onWillStart(),\n        onStart: () => _this.onStart(),\n        onMove: ev => _this.onMove(ev),\n        onEnd: ev => _this.onEnd(ev)\n      });\n      _this.updateState();\n    })();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  componentDidLoad() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.didLoad = true;\n      /**\n       * A menu inside of a split pane is assumed\n       * to be a side pane.\n       *\n       * When the menu is loaded it needs to\n       * see if it should be considered visible inside\n       * of the split pane. If the split pane is\n       * hidden then the menu should be too.\n       */\n      const splitPane = _this2.el.closest('ion-split-pane');\n      if (splitPane !== null) {\n        _this2.isPaneVisible = yield splitPane.isVisible();\n      }\n      _this2.menuChanged();\n      _this2.updateState();\n    })();\n  }\n  menuChanged() {\n    /**\n     * Inform dependent components such as ion-menu-button\n     * that the menu is ready. Note that we only want to do this\n     * once the menu has been rendered which is why we check for didLoad.\n     */\n    if (this.didLoad) {\n      this.ionMenuChange.emit({\n        disabled: this.disabled,\n        open: this._isOpen\n      });\n    }\n  }\n  disconnectedCallback() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      /**\n       * The menu should be closed when it is\n       * unmounted from the DOM.\n       * This is an async call, so we need to wait for\n       * this to finish otherwise contentEl\n       * will not have MENU_CONTENT_OPEN removed.\n       */\n      yield _this3.close(false);\n      _this3.blocker.destroy();\n      menuController._unregister(_this3);\n      if (_this3.animation) {\n        _this3.animation.destroy();\n      }\n      if (_this3.gesture) {\n        _this3.gesture.destroy();\n        _this3.gesture = undefined;\n      }\n      _this3.animation = undefined;\n      _this3.contentEl = undefined;\n    })();\n  }\n  onSplitPaneChanged(ev) {\n    const closestSplitPane = this.el.closest('ion-split-pane');\n    if (closestSplitPane !== null && closestSplitPane === ev.target) {\n      this.isPaneVisible = ev.detail.visible;\n      this.updateState();\n    }\n  }\n  onBackdropClick(ev) {\n    // TODO(FW-2832): type (CustomEvent triggers errors which should be sorted)\n    if (this._isOpen && this.lastOnEnd < ev.timeStamp - 100) {\n      const shouldClose = ev.composedPath ? !ev.composedPath().includes(this.menuInnerEl) : false;\n      if (shouldClose) {\n        ev.preventDefault();\n        ev.stopPropagation();\n        this.close(undefined, BACKDROP);\n      }\n    }\n  }\n  onKeydown(ev) {\n    if (ev.key === 'Escape') {\n      this.close(undefined, BACKDROP);\n    }\n  }\n  /**\n   * Returns `true` is the menu is open.\n   */\n  isOpen() {\n    return Promise.resolve(this._isOpen);\n  }\n  /**\n   * Returns `true` is the menu is active.\n   *\n   * A menu is active when it can be opened or closed, meaning it's enabled\n   * and it's not part of a `ion-split-pane`.\n   */\n  isActive() {\n    return Promise.resolve(this._isActive());\n  }\n  /**\n   * Opens the menu. If the menu is already open or it can't be opened,\n   * it returns `false`.\n   */\n  open(animated = true) {\n    return this.setOpen(true, animated);\n  }\n  /**\n   * Closes the menu. If the menu is already closed or it can't be closed,\n   * it returns `false`.\n   */\n  close(animated = true, role) {\n    return this.setOpen(false, animated, role);\n  }\n  /**\n   * Toggles the menu. If the menu is already open, it will try to close, otherwise it will try to open it.\n   * If the operation can't be completed successfully, it returns `false`.\n   */\n  toggle(animated = true) {\n    return this.setOpen(!this._isOpen, animated);\n  }\n  /**\n   * Opens or closes the button.\n   * If the operation can't be completed successfully, it returns `false`.\n   */\n  setOpen(shouldOpen, animated = true, role) {\n    return menuController._setOpen(this, shouldOpen, animated, role);\n  }\n  trapKeyboardFocus(ev, doc) {\n    const target = ev.target;\n    if (!target) {\n      return;\n    }\n    /**\n     * If the target is inside the menu contents, let the browser\n     * focus as normal and keep a log of the last focused element.\n     */\n    if (this.el.contains(target)) {\n      this.lastFocus = target;\n    } else {\n      /**\n       * Otherwise, we are about to have focus go out of the menu.\n       * Wrap the focus to either the first or last element.\n       */\n      const {\n        el\n      } = this;\n      /**\n       * Once we call `focusFirstDescendant`, another focus event\n       * will fire, which will cause `lastFocus` to be updated\n       * before we can run the code after that. We cache the value\n       * here to avoid that.\n       */\n      focusFirstDescendant(el);\n      /**\n       * If the cached last focused element is the same as the now-\n       * active element, that means the user was on the first element\n       * already and pressed Shift + Tab, so we need to wrap to the\n       * last descendant.\n       */\n      if (this.lastFocus === doc.activeElement) {\n        focusLastDescendant(el);\n      }\n    }\n  }\n  _setOpen(shouldOpen, animated = true, role) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      // If the menu is disabled or it is currently being animated, let's do nothing\n      if (!_this4._isActive() || _this4.isAnimating || shouldOpen === _this4._isOpen) {\n        return false;\n      }\n      _this4.beforeAnimation(shouldOpen, role);\n      yield _this4.loadAnimation();\n      yield _this4.startAnimation(shouldOpen, animated);\n      /**\n       * If the animation was cancelled then\n       * return false because the operation\n       * did not succeed.\n       */\n      if (_this4.operationCancelled) {\n        _this4.operationCancelled = false;\n        return false;\n      }\n      _this4.afterAnimation(shouldOpen, role);\n      return true;\n    })();\n  }\n  loadAnimation() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      // Menu swipe animation takes the menu's inner width as parameter,\n      // If `offsetWidth` changes, we need to create a new animation.\n      const width = _this5.menuInnerEl.offsetWidth;\n      /**\n       * Menu direction animation is calculated based on the document direction.\n       * If the document direction changes, we need to create a new animation.\n       */\n      const isEndSide$1 = isEndSide(_this5.side);\n      if (width === _this5.width && _this5.animation !== undefined && isEndSide$1 === _this5.isEndSide) {\n        return;\n      }\n      _this5.width = width;\n      _this5.isEndSide = isEndSide$1;\n      // Destroy existing animation\n      if (_this5.animation) {\n        _this5.animation.destroy();\n        _this5.animation = undefined;\n      }\n      // Create new animation\n      const animation = _this5.animation = yield menuController._createAnimation(_this5.type, _this5);\n      if (!config.getBoolean('animated', true)) {\n        animation.duration(0);\n      }\n      animation.fill('both');\n    })();\n  }\n  startAnimation(shouldOpen, animated) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const isReversed = !shouldOpen;\n      const mode = getIonMode(_this6);\n      const easing = mode === 'ios' ? iosEasing : mdEasing;\n      const easingReverse = mode === 'ios' ? iosEasingReverse : mdEasingReverse;\n      const ani = _this6.animation.direction(isReversed ? 'reverse' : 'normal').easing(isReversed ? easingReverse : easing);\n      if (animated) {\n        yield ani.play();\n      } else {\n        ani.play({\n          sync: true\n        });\n      }\n      /**\n       * We run this after the play invocation\n       * instead of using ani.onFinish so that\n       * multiple onFinish callbacks do not get\n       * run if an animation is played, stopped,\n       * and then played again.\n       */\n      if (ani.getDirection() === 'reverse') {\n        ani.direction('normal');\n      }\n    })();\n  }\n  _isActive() {\n    return !this.disabled && !this.isPaneVisible;\n  }\n  canSwipe() {\n    return this.swipeGesture && !this.isAnimating && this._isActive();\n  }\n  canStart(detail) {\n    // Do not allow swipe gesture if a modal is open\n    const isModalPresented = !!document.querySelector('ion-modal.show-modal');\n    if (isModalPresented || !this.canSwipe()) {\n      return false;\n    }\n    if (this._isOpen) {\n      return true;\n    } else if (menuController._getOpenSync()) {\n      return false;\n    }\n    return checkEdgeSide(window, detail.currentX, this.isEndSide, this.maxEdgeStart);\n  }\n  onWillStart() {\n    this.beforeAnimation(!this._isOpen, GESTURE);\n    return this.loadAnimation();\n  }\n  onStart() {\n    if (!this.isAnimating || !this.animation) {\n      assert(false, 'isAnimating has to be true');\n      return;\n    }\n    // the cloned animation should not use an easing curve during seek\n    this.animation.progressStart(true, this._isOpen ? 1 : 0);\n  }\n  onMove(detail) {\n    if (!this.isAnimating || !this.animation) {\n      assert(false, 'isAnimating has to be true');\n      return;\n    }\n    const delta = computeDelta(detail.deltaX, this._isOpen, this.isEndSide);\n    const stepValue = delta / this.width;\n    this.animation.progressStep(this._isOpen ? 1 - stepValue : stepValue);\n  }\n  onEnd(detail) {\n    if (!this.isAnimating || !this.animation) {\n      assert(false, 'isAnimating has to be true');\n      return;\n    }\n    const isOpen = this._isOpen;\n    const isEndSide = this.isEndSide;\n    const delta = computeDelta(detail.deltaX, isOpen, isEndSide);\n    const width = this.width;\n    const stepValue = delta / width;\n    const velocity = detail.velocityX;\n    const z = width / 2.0;\n    const shouldCompleteRight = velocity >= 0 && (velocity > 0.2 || detail.deltaX > z);\n    const shouldCompleteLeft = velocity <= 0 && (velocity < -0.2 || detail.deltaX < -z);\n    const shouldComplete = isOpen ? isEndSide ? shouldCompleteRight : shouldCompleteLeft : isEndSide ? shouldCompleteLeft : shouldCompleteRight;\n    let shouldOpen = !isOpen && shouldComplete;\n    if (isOpen && !shouldComplete) {\n      shouldOpen = true;\n    }\n    this.lastOnEnd = detail.currentTime;\n    // Account for rounding errors in JS\n    let newStepValue = shouldComplete ? 0.001 : -0.001;\n    /**\n     * stepValue can sometimes return a negative\n     * value, but you can't have a negative time value\n     * for the cubic bezier curve (at least with web animations)\n     */\n    const adjustedStepValue = stepValue < 0 ? 0.01 : stepValue;\n    /**\n     * Animation will be reversed here, so need to\n     * reverse the easing curve as well\n     *\n     * Additionally, we need to account for the time relative\n     * to the new easing curve, as `stepValue` is going to be given\n     * in terms of a linear curve.\n     */\n    newStepValue += getTimeGivenProgression([0, 0], [0.4, 0], [0.6, 1], [1, 1], clamp(0, adjustedStepValue, 0.9999))[0] || 0;\n    const playTo = this._isOpen ? !shouldComplete : shouldComplete;\n    this.animation.easing('cubic-bezier(0.4, 0.0, 0.6, 1)').onFinish(() => this.afterAnimation(shouldOpen, GESTURE), {\n      oneTimeCallback: true\n    }).progressEnd(playTo ? 1 : 0, this._isOpen ? 1 - newStepValue : newStepValue, 300);\n  }\n  beforeAnimation(shouldOpen, role) {\n    assert(!this.isAnimating, '_before() should not be called while animating');\n    /**\n     * When the menu is presented on an Android device, TalkBack's focus rings\n     * may appear in the wrong position due to the transition (specifically\n     * `transform` styles). This occurs because the focus rings are initially\n     * displayed at the starting position of the elements before the transition\n     * begins. This workaround ensures the focus rings do not appear in the\n     * incorrect location.\n     *\n     * If this solution is applied to iOS devices, then it leads to a bug where\n     * the overlays cannot be accessed by screen readers. This is due to\n     * VoiceOver not being able to update the accessibility tree when the\n     * `aria-hidden` is removed.\n     */\n    if (isPlatform('android')) {\n      this.el.setAttribute('aria-hidden', 'true');\n    }\n    // this places the menu into the correct location before it animates in\n    // this css class doesn't actually kick off any animations\n    this.el.classList.add(SHOW_MENU);\n    /**\n     * We add a tabindex here so that focus trapping\n     * still works even if the menu does not have\n     * any focusable elements slotted inside. The\n     * focus trapping utility will fallback to focusing\n     * the menu so focus does not leave when the menu\n     * is open.\n     */\n    this.el.setAttribute('tabindex', '0');\n    if (this.backdropEl) {\n      this.backdropEl.classList.add(SHOW_BACKDROP);\n    }\n    // add css class and hide content behind menu from screen readers\n    if (this.contentEl) {\n      this.contentEl.classList.add(MENU_CONTENT_OPEN);\n      /**\n       * When the menu is open and overlaying the main\n       * content, the main content should not be announced\n       * by the screenreader as the menu is the main\n       * focus. This is useful with screenreaders that have\n       * \"read from top\" gestures that read the entire\n       * page from top to bottom when activated.\n       * This should be done before the animation starts\n       * so that users cannot accidentally scroll\n       * the content while dragging a menu open.\n       */\n      this.contentEl.setAttribute('aria-hidden', 'true');\n    }\n    this.blocker.block();\n    this.isAnimating = true;\n    if (shouldOpen) {\n      this.ionWillOpen.emit();\n    } else {\n      this.ionWillClose.emit({\n        role\n      });\n    }\n  }\n  afterAnimation(isOpen, role) {\n    var _a;\n    // keep opening/closing the menu disabled for a touch more yet\n    // only add listeners/css if it's enabled and isOpen\n    // and only remove listeners/css if it's not open\n    // emit opened/closed events\n    this._isOpen = isOpen;\n    this.isAnimating = false;\n    if (!this._isOpen) {\n      this.blocker.unblock();\n    }\n    if (isOpen) {\n      /**\n       * When the menu is presented on an Android device, TalkBack's focus rings\n       * may appear in the wrong position due to the transition (specifically\n       * `transform` styles). The menu is hidden from screen readers during the\n       * transition to prevent this. Once the transition is complete, the menu\n       * is shown again.\n       */\n      if (isPlatform('android')) {\n        this.el.removeAttribute('aria-hidden');\n      }\n      // emit open event\n      this.ionDidOpen.emit();\n      /**\n       * Move focus to the menu to prepare focus trapping, as long as\n       * it isn't already focused. Use the host element instead of the\n       * first descendant to avoid the scroll position jumping around.\n       */\n      const focusedMenu = (_a = document.activeElement) === null || _a === void 0 ? void 0 : _a.closest('ion-menu');\n      if (focusedMenu !== this.el) {\n        this.el.focus();\n      }\n      // start focus trapping\n      document.addEventListener('focus', this.handleFocus, true);\n    } else {\n      this.el.removeAttribute('aria-hidden');\n      // remove css classes and unhide content from screen readers\n      this.el.classList.remove(SHOW_MENU);\n      /**\n       * Remove tabindex from the menu component\n       * so that is cannot be tabbed to.\n       */\n      this.el.removeAttribute('tabindex');\n      if (this.contentEl) {\n        this.contentEl.classList.remove(MENU_CONTENT_OPEN);\n        /**\n         * Remove aria-hidden so screen readers\n         * can announce the main content again\n         * now that the menu is not the main focus.\n         */\n        this.contentEl.removeAttribute('aria-hidden');\n      }\n      if (this.backdropEl) {\n        this.backdropEl.classList.remove(SHOW_BACKDROP);\n      }\n      if (this.animation) {\n        this.animation.stop();\n      }\n      // emit close event\n      this.ionDidClose.emit({\n        role\n      });\n      // undo focus trapping so multiple menus don't collide\n      document.removeEventListener('focus', this.handleFocus, true);\n    }\n  }\n  updateState() {\n    const isActive = this._isActive();\n    if (this.gesture) {\n      this.gesture.enable(isActive && this.swipeGesture);\n    }\n    /**\n     * If the menu is disabled but it is still open\n     * then we should close the menu immediately.\n     * Additionally, if the menu is in the process\n     * of animating {open, close} and the menu is disabled\n     * then it should still be closed immediately.\n     */\n    if (!isActive) {\n      /**\n       * It is possible to disable the menu while\n       * it is mid-animation. When this happens, we\n       * need to set the operationCancelled flag\n       * so that this._setOpen knows to return false\n       * and not run the \"afterAnimation\" callback.\n       */\n      if (this.isAnimating) {\n        this.operationCancelled = true;\n      }\n      /**\n       * If the menu is disabled then we should\n       * forcibly close the menu even if it is open.\n       */\n      this.afterAnimation(false, GESTURE);\n    }\n  }\n  render() {\n    const {\n      type,\n      disabled,\n      el,\n      isPaneVisible,\n      inheritedAttributes,\n      side\n    } = this;\n    const mode = getIonMode(this);\n    /**\n     * If the Close Watcher is enabled then\n     * the ionBackButton listener in the menu controller\n     * will handle closing the menu when Escape is pressed.\n     */\n    return h(Host, {\n      key: 'da96fdb4c5ddf60e615cc4cdda7ccdb3fd7e089b',\n      onKeyDown: shouldUseCloseWatcher() ? null : this.onKeydown,\n      role: \"navigation\",\n      \"aria-label\": inheritedAttributes['aria-label'] || 'menu',\n      class: {\n        [mode]: true,\n        [`menu-type-${type}`]: true,\n        'menu-enabled': !disabled,\n        [`menu-side-${side}`]: true,\n        'menu-pane-visible': isPaneVisible,\n        'split-pane-side': hostContext('ion-split-pane', el)\n      }\n    }, h(\"div\", {\n      key: '894e680fe227534711128c7aca980964ddb5a08a',\n      class: \"menu-inner\",\n      part: \"container\",\n      ref: el => this.menuInnerEl = el\n    }, h(\"slot\", {\n      key: 'e9f5934518dc0cceaeadf1f2820614595fec6bc9'\n    })), h(\"ion-backdrop\", {\n      key: '7282077817657b1bb6c155f1404c0a519fece993',\n      ref: el => this.backdropEl = el,\n      class: \"menu-backdrop\",\n      tappable: false,\n      stopPropagation: false,\n      part: \"backdrop\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"type\": [\"typeChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"side\": [\"sideChanged\"],\n      \"swipeGesture\": [\"swipeGestureChanged\"]\n    };\n  }\n};\nconst computeDelta = (deltaX, isOpen, isEndSide) => {\n  return Math.max(0, isOpen !== isEndSide ? -deltaX : deltaX);\n};\nconst checkEdgeSide = (win, posX, isEndSide, maxEdgeStart) => {\n  if (isEndSide) {\n    return posX >= win.innerWidth - maxEdgeStart;\n  } else {\n    return posX <= maxEdgeStart;\n  }\n};\nconst SHOW_MENU = 'show-menu';\nconst SHOW_BACKDROP = 'show-backdrop';\nconst MENU_CONTENT_OPEN = 'menu-content-open';\nMenu.style = {\n  ios: IonMenuIosStyle0,\n  md: IonMenuMdStyle0\n};\n\n// Given a menu, return whether or not the menu toggle should be visible\nconst updateVisibility = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (menu) {\n    const menuEl = yield menuController.get(menu);\n    return !!(menuEl && (yield menuEl.isActive()));\n  });\n  return function updateVisibility(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst menuButtonIosCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #0054e9);--padding-start:5px;--padding-end:5px;min-height:32px;font-size:clamp(31px, 1.9375rem, 38.13px)}:host(.ion-activated){opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}\";\nconst IonMenuButtonIosStyle0 = menuButtonIosCss;\nconst menuButtonMdCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:.04;--border-radius:50%;--color:initial;--padding-start:8px;--padding-end:8px;width:3rem;height:3rem;font-size:1.5rem}:host(.ion-color.ion-focused)::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}\";\nconst IonMenuButtonMdStyle0 = menuButtonMdCss;\nconst MenuButton = class {\n  constructor(hostRef) {\n    var _this7 = this;\n    registerInstance(this, hostRef);\n    this.inheritedAttributes = {};\n    this.onClick = /*#__PURE__*/_asyncToGenerator(function* () {\n      return menuController.toggle(_this7.menu);\n    });\n    this.visible = false;\n    this.color = undefined;\n    this.disabled = false;\n    this.menu = undefined;\n    this.autoHide = true;\n    this.type = 'button';\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  componentDidLoad() {\n    this.visibilityChanged();\n  }\n  visibilityChanged() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      _this8.visible = yield updateVisibility(_this8.menu);\n    })();\n  }\n  render() {\n    const {\n      color,\n      disabled,\n      inheritedAttributes\n    } = this;\n    const mode = getIonMode(this);\n    const menuIcon = config.get('menuIcon', mode === 'ios' ? menuOutline : menuSharp);\n    const hidden = this.autoHide && !this.visible;\n    const attrs = {\n      type: this.type\n    };\n    const ariaLabel = inheritedAttributes['aria-label'] || 'menu';\n    return h(Host, {\n      key: '7ec29715ce7926b7c2b08f3d9cac8aaa16b3dc28',\n      onClick: this.onClick,\n      \"aria-disabled\": disabled ? 'true' : null,\n      \"aria-hidden\": hidden ? 'true' : null,\n      class: createColorClasses(color, {\n        [mode]: true,\n        button: true,\n        // ion-buttons target .button\n        'menu-button-hidden': hidden,\n        'menu-button-disabled': disabled,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'ion-activatable': true,\n        'ion-focusable': true\n      })\n    }, h(\"button\", Object.assign({\n      key: 'd4c5929264af3ba0328118bcc27d2ab7ef5d3809'\n    }, attrs, {\n      disabled: disabled,\n      class: \"button-native\",\n      part: \"native\",\n      \"aria-label\": ariaLabel\n    }), h(\"span\", {\n      key: '7bfa6e9a93105486623d044861e879ec79ff64f1',\n      class: \"button-inner\"\n    }, h(\"slot\", {\n      key: '071ab58e285832fc188706166f5547d45d501ac5'\n    }, h(\"ion-icon\", {\n      key: '918ec5d791921de9821c347af4f65f97dd94aabf',\n      part: \"icon\",\n      icon: menuIcon,\n      mode: mode,\n      lazy: false,\n      \"aria-hidden\": \"true\"\n    }))), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '00ffdd53f635e706c1dbd01b8e7944498650fe81',\n      type: \"unbounded\"\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nMenuButton.style = {\n  ios: IonMenuButtonIosStyle0,\n  md: IonMenuButtonMdStyle0\n};\nconst menuToggleCss = \":host(.menu-toggle-hidden){display:none}\";\nconst IonMenuToggleStyle0 = menuToggleCss;\nconst MenuToggle = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onClick = () => {\n      return menuController.toggle(this.menu);\n    };\n    this.visible = false;\n    this.menu = undefined;\n    this.autoHide = true;\n  }\n  connectedCallback() {\n    this.visibilityChanged();\n  }\n  visibilityChanged() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      _this9.visible = yield updateVisibility(_this9.menu);\n    })();\n  }\n  render() {\n    const mode = getIonMode(this);\n    const hidden = this.autoHide && !this.visible;\n    return h(Host, {\n      key: '7c27ea5b0795676bf5cb33e1f83aa142c197f64e',\n      onClick: this.onClick,\n      \"aria-hidden\": hidden ? 'true' : null,\n      class: {\n        [mode]: true,\n        'menu-toggle-hidden': hidden\n      }\n    }, h(\"slot\", {\n      key: '69f187becedc0fe34603d41d279f043cf0fdf776'\n    }));\n  }\n};\nMenuToggle.style = IonMenuToggleStyle0;\nexport { Menu as ion_menu, MenuButton as ion_menu_button, MenuToggle as ion_menu_toggle };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "g", "getTimeGivenProgression", "o", "getPresentedOverlay", "B", "BACKDROP", "n", "focusFirstDescendant", "q", "focusLastDescendant", "G", "GESTURE", "GESTURE_CONTROLLER", "shouldUseCloseWatcher", "isEndSide", "inheritAriaAttributes", "m", "assert", "j", "clamp", "menuController", "c", "config", "b", "getIonMode", "a", "isPlatform", "hostContext", "createColorClasses", "u", "menuOutline", "v", "menuSharp", "menuIosCss", "IonMenuIosStyle0", "menuMdCss", "IonMenuMdStyle0", "iosEasing", "mdEasing", "iosEasingReverse", "mdEasingReverse", "<PERSON><PERSON>", "constructor", "hostRef", "ionWillOpen", "ionWillClose", "ionDidOpen", "ionDidClose", "ionMenuChange", "lastOnEnd", "blocker", "createBlocker", "disableScroll", "didLoad", "operationCancelled", "isAnimating", "_isOpen", "inheritedAttributes", "handleFocus", "ev", "lastOverlay", "document", "contains", "el", "trapKeyboardFocus", "isPaneVisible", "contentId", "undefined", "menuId", "type", "disabled", "side", "swipeGesture", "maxEdgeStart", "typeChanged", "oldType", "contentEl", "classList", "remove", "add", "removeAttribute", "menuInnerEl", "animation", "disabled<PERSON><PERSON>ed", "updateState", "emit", "open", "sideChanged", "swipeGestureChanged", "connectedCallback", "_this", "_asyncToGenerator", "customElements", "whenDefined", "get", "content", "getElementById", "console", "error", "_register", "menuChanged", "gesture", "createGesture", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "blurOnStart", "canStart", "onWillStart", "onStart", "onMove", "onEnd", "componentWillLoad", "componentDidLoad", "_this2", "splitPane", "closest", "isVisible", "disconnectedCallback", "_this3", "close", "destroy", "_unregister", "onSplitPaneChanged", "closestSplitPane", "target", "detail", "visible", "onBackdropClick", "timeStamp", "shouldClose", "<PERSON><PERSON><PERSON>", "includes", "preventDefault", "stopPropagation", "onKeydown", "key", "isOpen", "Promise", "resolve", "isActive", "_isActive", "animated", "<PERSON><PERSON><PERSON>", "role", "toggle", "shouldOpen", "_setOpen", "doc", "lastFocus", "activeElement", "_this4", "beforeAnimation", "loadAnimation", "startAnimation", "afterAnimation", "_this5", "width", "offsetWidth", "isEndSide$1", "_createAnimation", "getBoolean", "duration", "fill", "_this6", "isReversed", "mode", "easing", "easingReverse", "ani", "direction", "play", "sync", "getDirection", "canSwipe", "isModalPresented", "querySelector", "_getOpenSync", "checkEdgeSide", "window", "currentX", "progressStart", "delta", "computeDelta", "deltaX", "<PERSON><PERSON><PERSON><PERSON>", "progressStep", "velocity", "velocityX", "z", "shouldCompleteRight", "shouldCompleteLeft", "shouldComplete", "currentTime", "newStepValue", "adjustedStepValue", "playTo", "onFinish", "oneTimeCallback", "progressEnd", "setAttribute", "SHOW_MENU", "backdropEl", "SHOW_BACKDROP", "MENU_CONTENT_OPEN", "block", "_a", "unblock", "focusedMenu", "focus", "addEventListener", "stop", "removeEventListener", "enable", "render", "onKeyDown", "class", "part", "ref", "tappable", "watchers", "Math", "max", "win", "posX", "innerWidth", "style", "ios", "md", "updateVisibility", "_ref", "menu", "menuEl", "_x", "apply", "arguments", "menuButtonIosCss", "IonMenuButtonIosStyle0", "menuButtonMdCss", "IonMenuButtonMdStyle0", "MenuButton", "_this7", "onClick", "color", "autoHide", "visibilityChanged", "_this8", "menuIcon", "hidden", "attrs", "aria<PERSON><PERSON><PERSON>", "button", "Object", "assign", "icon", "lazy", "menuToggleCss", "IonMenuToggleStyle0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this9", "ion_menu", "ion_menu_button", "ion_menu_toggle"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/ion-menu_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-28849c61.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { o as getPresentedOverlay, B as BACKDROP, n as focusFirstDescendant, q as focusLastDescendant, G as GESTURE } from './overlays-e7b9d6d9.js';\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-314a54f6.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-06ef3c3e.js';\nimport { n as isEndSide, i as inheritAriaAttributes, m as assert, j as clamp } from './helpers-da915de8.js';\nimport { m as menuController } from './index-24b48b06.js';\nimport { c as config, b as getIonMode, a as isPlatform } from './ionic-global-c81d82ab.js';\nimport { h as hostContext, c as createColorClasses } from './theme-01f3f29c.js';\nimport { u as menuOutline, v as menuSharp } from './index-e2cf2ceb.js';\nimport './index-a5d50daf.js';\nimport './framework-delegate-63d1a679.js';\nimport './index-9b0d46f4.js';\nimport './animation-eab5a4ca.js';\n\nconst menuIosCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width, var(--width));min-width:var(--side-min-width, var(--min-width));max-width:var(--side-max-width, var(--max-width))}:host(.menu-pane-visible.split-pane-side){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.menu-pane-visible.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}:host(.menu-pane-visible.split-pane-side){-ms-flex-order:-1;order:-1}:host(.menu-pane-visible.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-pane-visible.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-pane-visible.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-type-push){z-index:1000}:host(.menu-type-push) .show-backdrop{display:block}\";\nconst IonMenuIosStyle0 = menuIosCss;\n\nconst menuMdCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){-ms-flex:0 1 auto;flex:0 1 auto;width:var(--side-width, var(--width));min-width:var(--side-min-width, var(--min-width));max-width:var(--side-max-width, var(--max-width))}:host(.menu-pane-visible.split-pane-side){left:0;right:0;top:0;bottom:0;position:relative;-webkit-box-shadow:none;box-shadow:none;z-index:0}:host(.menu-pane-visible.split-pane-side.menu-enabled){display:-ms-flexbox;display:flex;-ms-flex-negative:0;flex-shrink:0}:host(.menu-pane-visible.split-pane-side){-ms-flex-order:-1;order:-1}:host(.menu-pane-visible.split-pane-side[side=end]){-ms-flex-order:1;order:1}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-pane-visible.split-pane-side){-webkit-border-start:0;border-inline-start:0;-webkit-border-end:var(--border);border-inline-end:var(--border);border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-pane-visible.split-pane-side[side=end]){-webkit-border-start:var(--border);border-inline-start:var(--border);-webkit-border-end:0;border-inline-end:0;border-top:0;border-bottom:0;min-width:var(--side-min-width);max-width:var(--side-max-width)}:host(.menu-type-overlay) .menu-inner{-webkit-box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18);box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18)}\";\nconst IonMenuMdStyle0 = menuMdCss;\n\nconst iosEasing = 'cubic-bezier(0.32,0.72,0,1)';\nconst mdEasing = 'cubic-bezier(0.0,0.0,0.2,1)';\nconst iosEasingReverse = 'cubic-bezier(1, 0, 0.68, 0.28)';\nconst mdEasingReverse = 'cubic-bezier(0.4, 0, 0.6, 1)';\nconst Menu = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionWillOpen = createEvent(this, \"ionWillOpen\", 7);\n        this.ionWillClose = createEvent(this, \"ionWillClose\", 7);\n        this.ionDidOpen = createEvent(this, \"ionDidOpen\", 7);\n        this.ionDidClose = createEvent(this, \"ionDidClose\", 7);\n        this.ionMenuChange = createEvent(this, \"ionMenuChange\", 7);\n        this.lastOnEnd = 0;\n        this.blocker = GESTURE_CONTROLLER.createBlocker({ disableScroll: true });\n        this.didLoad = false;\n        /**\n         * Flag used to determine if an open/close\n         * operation was cancelled. For example, if\n         * an app calls \"menu.open\" then disables the menu\n         * part way through the animation, then this would\n         * be considered a cancelled operation.\n         */\n        this.operationCancelled = false;\n        this.isAnimating = false;\n        this._isOpen = false;\n        this.inheritedAttributes = {};\n        this.handleFocus = (ev) => {\n            /**\n             * Overlays have their own focus trapping listener\n             * so we do not want the two listeners to conflict\n             * with each other. If the top-most overlay that is\n             * open does not contain this ion-menu, then ion-menu's\n             * focus trapping should not run.\n             */\n            const lastOverlay = getPresentedOverlay(document);\n            if (lastOverlay && !lastOverlay.contains(this.el)) {\n                return;\n            }\n            this.trapKeyboardFocus(ev, document);\n        };\n        this.isPaneVisible = false;\n        this.isEndSide = false;\n        this.contentId = undefined;\n        this.menuId = undefined;\n        this.type = undefined;\n        this.disabled = false;\n        this.side = 'start';\n        this.swipeGesture = true;\n        this.maxEdgeStart = 50;\n    }\n    typeChanged(type, oldType) {\n        const contentEl = this.contentEl;\n        if (contentEl) {\n            if (oldType !== undefined) {\n                contentEl.classList.remove(`menu-content-${oldType}`);\n            }\n            contentEl.classList.add(`menu-content-${type}`);\n            contentEl.removeAttribute('style');\n        }\n        if (this.menuInnerEl) {\n            // Remove effects of previous animations\n            this.menuInnerEl.removeAttribute('style');\n        }\n        this.animation = undefined;\n    }\n    disabledChanged() {\n        this.updateState();\n        this.ionMenuChange.emit({\n            disabled: this.disabled,\n            open: this._isOpen,\n        });\n    }\n    sideChanged() {\n        this.isEndSide = isEndSide(this.side);\n        /**\n         * Menu direction animation is calculated based on the document direction.\n         * If the document direction changes, we need to create a new animation.\n         */\n        this.animation = undefined;\n    }\n    swipeGestureChanged() {\n        this.updateState();\n    }\n    async connectedCallback() {\n        // TODO: connectedCallback is fired in CE build\n        // before WC is defined. This needs to be fixed in Stencil.\n        if (typeof customElements !== 'undefined' && customElements != null) {\n            await customElements.whenDefined('ion-menu');\n        }\n        if (this.type === undefined) {\n            this.type = config.get('menuType', 'overlay');\n        }\n        const content = this.contentId !== undefined ? document.getElementById(this.contentId) : null;\n        if (content === null) {\n            console.error('Menu: must have a \"content\" element to listen for drag events on.');\n            return;\n        }\n        if (this.el.contains(content)) {\n            console.error(`Menu: \"contentId\" should refer to the main view's ion-content, not the ion-content inside of the ion-menu.`);\n        }\n        this.contentEl = content;\n        // add menu's content classes\n        content.classList.add('menu-content');\n        this.typeChanged(this.type, undefined);\n        this.sideChanged();\n        // register this menu with the app's menu controller\n        menuController._register(this);\n        this.menuChanged();\n        this.gesture = (await import('./index-39782642.js')).createGesture({\n            el: document,\n            gestureName: 'menu-swipe',\n            gesturePriority: 30,\n            threshold: 10,\n            blurOnStart: true,\n            canStart: (ev) => this.canStart(ev),\n            onWillStart: () => this.onWillStart(),\n            onStart: () => this.onStart(),\n            onMove: (ev) => this.onMove(ev),\n            onEnd: (ev) => this.onEnd(ev),\n        });\n        this.updateState();\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    async componentDidLoad() {\n        this.didLoad = true;\n        /**\n         * A menu inside of a split pane is assumed\n         * to be a side pane.\n         *\n         * When the menu is loaded it needs to\n         * see if it should be considered visible inside\n         * of the split pane. If the split pane is\n         * hidden then the menu should be too.\n         */\n        const splitPane = this.el.closest('ion-split-pane');\n        if (splitPane !== null) {\n            this.isPaneVisible = await splitPane.isVisible();\n        }\n        this.menuChanged();\n        this.updateState();\n    }\n    menuChanged() {\n        /**\n         * Inform dependent components such as ion-menu-button\n         * that the menu is ready. Note that we only want to do this\n         * once the menu has been rendered which is why we check for didLoad.\n         */\n        if (this.didLoad) {\n            this.ionMenuChange.emit({ disabled: this.disabled, open: this._isOpen });\n        }\n    }\n    async disconnectedCallback() {\n        /**\n         * The menu should be closed when it is\n         * unmounted from the DOM.\n         * This is an async call, so we need to wait for\n         * this to finish otherwise contentEl\n         * will not have MENU_CONTENT_OPEN removed.\n         */\n        await this.close(false);\n        this.blocker.destroy();\n        menuController._unregister(this);\n        if (this.animation) {\n            this.animation.destroy();\n        }\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        this.animation = undefined;\n        this.contentEl = undefined;\n    }\n    onSplitPaneChanged(ev) {\n        const closestSplitPane = this.el.closest('ion-split-pane');\n        if (closestSplitPane !== null && closestSplitPane === ev.target) {\n            this.isPaneVisible = ev.detail.visible;\n            this.updateState();\n        }\n    }\n    onBackdropClick(ev) {\n        // TODO(FW-2832): type (CustomEvent triggers errors which should be sorted)\n        if (this._isOpen && this.lastOnEnd < ev.timeStamp - 100) {\n            const shouldClose = ev.composedPath ? !ev.composedPath().includes(this.menuInnerEl) : false;\n            if (shouldClose) {\n                ev.preventDefault();\n                ev.stopPropagation();\n                this.close(undefined, BACKDROP);\n            }\n        }\n    }\n    onKeydown(ev) {\n        if (ev.key === 'Escape') {\n            this.close(undefined, BACKDROP);\n        }\n    }\n    /**\n     * Returns `true` is the menu is open.\n     */\n    isOpen() {\n        return Promise.resolve(this._isOpen);\n    }\n    /**\n     * Returns `true` is the menu is active.\n     *\n     * A menu is active when it can be opened or closed, meaning it's enabled\n     * and it's not part of a `ion-split-pane`.\n     */\n    isActive() {\n        return Promise.resolve(this._isActive());\n    }\n    /**\n     * Opens the menu. If the menu is already open or it can't be opened,\n     * it returns `false`.\n     */\n    open(animated = true) {\n        return this.setOpen(true, animated);\n    }\n    /**\n     * Closes the menu. If the menu is already closed or it can't be closed,\n     * it returns `false`.\n     */\n    close(animated = true, role) {\n        return this.setOpen(false, animated, role);\n    }\n    /**\n     * Toggles the menu. If the menu is already open, it will try to close, otherwise it will try to open it.\n     * If the operation can't be completed successfully, it returns `false`.\n     */\n    toggle(animated = true) {\n        return this.setOpen(!this._isOpen, animated);\n    }\n    /**\n     * Opens or closes the button.\n     * If the operation can't be completed successfully, it returns `false`.\n     */\n    setOpen(shouldOpen, animated = true, role) {\n        return menuController._setOpen(this, shouldOpen, animated, role);\n    }\n    trapKeyboardFocus(ev, doc) {\n        const target = ev.target;\n        if (!target) {\n            return;\n        }\n        /**\n         * If the target is inside the menu contents, let the browser\n         * focus as normal and keep a log of the last focused element.\n         */\n        if (this.el.contains(target)) {\n            this.lastFocus = target;\n        }\n        else {\n            /**\n             * Otherwise, we are about to have focus go out of the menu.\n             * Wrap the focus to either the first or last element.\n             */\n            const { el } = this;\n            /**\n             * Once we call `focusFirstDescendant`, another focus event\n             * will fire, which will cause `lastFocus` to be updated\n             * before we can run the code after that. We cache the value\n             * here to avoid that.\n             */\n            focusFirstDescendant(el);\n            /**\n             * If the cached last focused element is the same as the now-\n             * active element, that means the user was on the first element\n             * already and pressed Shift + Tab, so we need to wrap to the\n             * last descendant.\n             */\n            if (this.lastFocus === doc.activeElement) {\n                focusLastDescendant(el);\n            }\n        }\n    }\n    async _setOpen(shouldOpen, animated = true, role) {\n        // If the menu is disabled or it is currently being animated, let's do nothing\n        if (!this._isActive() || this.isAnimating || shouldOpen === this._isOpen) {\n            return false;\n        }\n        this.beforeAnimation(shouldOpen, role);\n        await this.loadAnimation();\n        await this.startAnimation(shouldOpen, animated);\n        /**\n         * If the animation was cancelled then\n         * return false because the operation\n         * did not succeed.\n         */\n        if (this.operationCancelled) {\n            this.operationCancelled = false;\n            return false;\n        }\n        this.afterAnimation(shouldOpen, role);\n        return true;\n    }\n    async loadAnimation() {\n        // Menu swipe animation takes the menu's inner width as parameter,\n        // If `offsetWidth` changes, we need to create a new animation.\n        const width = this.menuInnerEl.offsetWidth;\n        /**\n         * Menu direction animation is calculated based on the document direction.\n         * If the document direction changes, we need to create a new animation.\n         */\n        const isEndSide$1 = isEndSide(this.side);\n        if (width === this.width && this.animation !== undefined && isEndSide$1 === this.isEndSide) {\n            return;\n        }\n        this.width = width;\n        this.isEndSide = isEndSide$1;\n        // Destroy existing animation\n        if (this.animation) {\n            this.animation.destroy();\n            this.animation = undefined;\n        }\n        // Create new animation\n        const animation = (this.animation = await menuController._createAnimation(this.type, this));\n        if (!config.getBoolean('animated', true)) {\n            animation.duration(0);\n        }\n        animation.fill('both');\n    }\n    async startAnimation(shouldOpen, animated) {\n        const isReversed = !shouldOpen;\n        const mode = getIonMode(this);\n        const easing = mode === 'ios' ? iosEasing : mdEasing;\n        const easingReverse = mode === 'ios' ? iosEasingReverse : mdEasingReverse;\n        const ani = this.animation\n            .direction(isReversed ? 'reverse' : 'normal')\n            .easing(isReversed ? easingReverse : easing);\n        if (animated) {\n            await ani.play();\n        }\n        else {\n            ani.play({ sync: true });\n        }\n        /**\n         * We run this after the play invocation\n         * instead of using ani.onFinish so that\n         * multiple onFinish callbacks do not get\n         * run if an animation is played, stopped,\n         * and then played again.\n         */\n        if (ani.getDirection() === 'reverse') {\n            ani.direction('normal');\n        }\n    }\n    _isActive() {\n        return !this.disabled && !this.isPaneVisible;\n    }\n    canSwipe() {\n        return this.swipeGesture && !this.isAnimating && this._isActive();\n    }\n    canStart(detail) {\n        // Do not allow swipe gesture if a modal is open\n        const isModalPresented = !!document.querySelector('ion-modal.show-modal');\n        if (isModalPresented || !this.canSwipe()) {\n            return false;\n        }\n        if (this._isOpen) {\n            return true;\n        }\n        else if (menuController._getOpenSync()) {\n            return false;\n        }\n        return checkEdgeSide(window, detail.currentX, this.isEndSide, this.maxEdgeStart);\n    }\n    onWillStart() {\n        this.beforeAnimation(!this._isOpen, GESTURE);\n        return this.loadAnimation();\n    }\n    onStart() {\n        if (!this.isAnimating || !this.animation) {\n            assert(false, 'isAnimating has to be true');\n            return;\n        }\n        // the cloned animation should not use an easing curve during seek\n        this.animation.progressStart(true, this._isOpen ? 1 : 0);\n    }\n    onMove(detail) {\n        if (!this.isAnimating || !this.animation) {\n            assert(false, 'isAnimating has to be true');\n            return;\n        }\n        const delta = computeDelta(detail.deltaX, this._isOpen, this.isEndSide);\n        const stepValue = delta / this.width;\n        this.animation.progressStep(this._isOpen ? 1 - stepValue : stepValue);\n    }\n    onEnd(detail) {\n        if (!this.isAnimating || !this.animation) {\n            assert(false, 'isAnimating has to be true');\n            return;\n        }\n        const isOpen = this._isOpen;\n        const isEndSide = this.isEndSide;\n        const delta = computeDelta(detail.deltaX, isOpen, isEndSide);\n        const width = this.width;\n        const stepValue = delta / width;\n        const velocity = detail.velocityX;\n        const z = width / 2.0;\n        const shouldCompleteRight = velocity >= 0 && (velocity > 0.2 || detail.deltaX > z);\n        const shouldCompleteLeft = velocity <= 0 && (velocity < -0.2 || detail.deltaX < -z);\n        const shouldComplete = isOpen\n            ? isEndSide\n                ? shouldCompleteRight\n                : shouldCompleteLeft\n            : isEndSide\n                ? shouldCompleteLeft\n                : shouldCompleteRight;\n        let shouldOpen = !isOpen && shouldComplete;\n        if (isOpen && !shouldComplete) {\n            shouldOpen = true;\n        }\n        this.lastOnEnd = detail.currentTime;\n        // Account for rounding errors in JS\n        let newStepValue = shouldComplete ? 0.001 : -0.001;\n        /**\n         * stepValue can sometimes return a negative\n         * value, but you can't have a negative time value\n         * for the cubic bezier curve (at least with web animations)\n         */\n        const adjustedStepValue = stepValue < 0 ? 0.01 : stepValue;\n        /**\n         * Animation will be reversed here, so need to\n         * reverse the easing curve as well\n         *\n         * Additionally, we need to account for the time relative\n         * to the new easing curve, as `stepValue` is going to be given\n         * in terms of a linear curve.\n         */\n        newStepValue +=\n            getTimeGivenProgression([0, 0], [0.4, 0], [0.6, 1], [1, 1], clamp(0, adjustedStepValue, 0.9999))[0] || 0;\n        const playTo = this._isOpen ? !shouldComplete : shouldComplete;\n        this.animation\n            .easing('cubic-bezier(0.4, 0.0, 0.6, 1)')\n            .onFinish(() => this.afterAnimation(shouldOpen, GESTURE), { oneTimeCallback: true })\n            .progressEnd(playTo ? 1 : 0, this._isOpen ? 1 - newStepValue : newStepValue, 300);\n    }\n    beforeAnimation(shouldOpen, role) {\n        assert(!this.isAnimating, '_before() should not be called while animating');\n        /**\n         * When the menu is presented on an Android device, TalkBack's focus rings\n         * may appear in the wrong position due to the transition (specifically\n         * `transform` styles). This occurs because the focus rings are initially\n         * displayed at the starting position of the elements before the transition\n         * begins. This workaround ensures the focus rings do not appear in the\n         * incorrect location.\n         *\n         * If this solution is applied to iOS devices, then it leads to a bug where\n         * the overlays cannot be accessed by screen readers. This is due to\n         * VoiceOver not being able to update the accessibility tree when the\n         * `aria-hidden` is removed.\n         */\n        if (isPlatform('android')) {\n            this.el.setAttribute('aria-hidden', 'true');\n        }\n        // this places the menu into the correct location before it animates in\n        // this css class doesn't actually kick off any animations\n        this.el.classList.add(SHOW_MENU);\n        /**\n         * We add a tabindex here so that focus trapping\n         * still works even if the menu does not have\n         * any focusable elements slotted inside. The\n         * focus trapping utility will fallback to focusing\n         * the menu so focus does not leave when the menu\n         * is open.\n         */\n        this.el.setAttribute('tabindex', '0');\n        if (this.backdropEl) {\n            this.backdropEl.classList.add(SHOW_BACKDROP);\n        }\n        // add css class and hide content behind menu from screen readers\n        if (this.contentEl) {\n            this.contentEl.classList.add(MENU_CONTENT_OPEN);\n            /**\n             * When the menu is open and overlaying the main\n             * content, the main content should not be announced\n             * by the screenreader as the menu is the main\n             * focus. This is useful with screenreaders that have\n             * \"read from top\" gestures that read the entire\n             * page from top to bottom when activated.\n             * This should be done before the animation starts\n             * so that users cannot accidentally scroll\n             * the content while dragging a menu open.\n             */\n            this.contentEl.setAttribute('aria-hidden', 'true');\n        }\n        this.blocker.block();\n        this.isAnimating = true;\n        if (shouldOpen) {\n            this.ionWillOpen.emit();\n        }\n        else {\n            this.ionWillClose.emit({ role });\n        }\n    }\n    afterAnimation(isOpen, role) {\n        var _a;\n        // keep opening/closing the menu disabled for a touch more yet\n        // only add listeners/css if it's enabled and isOpen\n        // and only remove listeners/css if it's not open\n        // emit opened/closed events\n        this._isOpen = isOpen;\n        this.isAnimating = false;\n        if (!this._isOpen) {\n            this.blocker.unblock();\n        }\n        if (isOpen) {\n            /**\n             * When the menu is presented on an Android device, TalkBack's focus rings\n             * may appear in the wrong position due to the transition (specifically\n             * `transform` styles). The menu is hidden from screen readers during the\n             * transition to prevent this. Once the transition is complete, the menu\n             * is shown again.\n             */\n            if (isPlatform('android')) {\n                this.el.removeAttribute('aria-hidden');\n            }\n            // emit open event\n            this.ionDidOpen.emit();\n            /**\n             * Move focus to the menu to prepare focus trapping, as long as\n             * it isn't already focused. Use the host element instead of the\n             * first descendant to avoid the scroll position jumping around.\n             */\n            const focusedMenu = (_a = document.activeElement) === null || _a === void 0 ? void 0 : _a.closest('ion-menu');\n            if (focusedMenu !== this.el) {\n                this.el.focus();\n            }\n            // start focus trapping\n            document.addEventListener('focus', this.handleFocus, true);\n        }\n        else {\n            this.el.removeAttribute('aria-hidden');\n            // remove css classes and unhide content from screen readers\n            this.el.classList.remove(SHOW_MENU);\n            /**\n             * Remove tabindex from the menu component\n             * so that is cannot be tabbed to.\n             */\n            this.el.removeAttribute('tabindex');\n            if (this.contentEl) {\n                this.contentEl.classList.remove(MENU_CONTENT_OPEN);\n                /**\n                 * Remove aria-hidden so screen readers\n                 * can announce the main content again\n                 * now that the menu is not the main focus.\n                 */\n                this.contentEl.removeAttribute('aria-hidden');\n            }\n            if (this.backdropEl) {\n                this.backdropEl.classList.remove(SHOW_BACKDROP);\n            }\n            if (this.animation) {\n                this.animation.stop();\n            }\n            // emit close event\n            this.ionDidClose.emit({ role });\n            // undo focus trapping so multiple menus don't collide\n            document.removeEventListener('focus', this.handleFocus, true);\n        }\n    }\n    updateState() {\n        const isActive = this._isActive();\n        if (this.gesture) {\n            this.gesture.enable(isActive && this.swipeGesture);\n        }\n        /**\n         * If the menu is disabled but it is still open\n         * then we should close the menu immediately.\n         * Additionally, if the menu is in the process\n         * of animating {open, close} and the menu is disabled\n         * then it should still be closed immediately.\n         */\n        if (!isActive) {\n            /**\n             * It is possible to disable the menu while\n             * it is mid-animation. When this happens, we\n             * need to set the operationCancelled flag\n             * so that this._setOpen knows to return false\n             * and not run the \"afterAnimation\" callback.\n             */\n            if (this.isAnimating) {\n                this.operationCancelled = true;\n            }\n            /**\n             * If the menu is disabled then we should\n             * forcibly close the menu even if it is open.\n             */\n            this.afterAnimation(false, GESTURE);\n        }\n    }\n    render() {\n        const { type, disabled, el, isPaneVisible, inheritedAttributes, side } = this;\n        const mode = getIonMode(this);\n        /**\n         * If the Close Watcher is enabled then\n         * the ionBackButton listener in the menu controller\n         * will handle closing the menu when Escape is pressed.\n         */\n        return (h(Host, { key: 'da96fdb4c5ddf60e615cc4cdda7ccdb3fd7e089b', onKeyDown: shouldUseCloseWatcher() ? null : this.onKeydown, role: \"navigation\", \"aria-label\": inheritedAttributes['aria-label'] || 'menu', class: {\n                [mode]: true,\n                [`menu-type-${type}`]: true,\n                'menu-enabled': !disabled,\n                [`menu-side-${side}`]: true,\n                'menu-pane-visible': isPaneVisible,\n                'split-pane-side': hostContext('ion-split-pane', el),\n            } }, h(\"div\", { key: '894e680fe227534711128c7aca980964ddb5a08a', class: \"menu-inner\", part: \"container\", ref: (el) => (this.menuInnerEl = el) }, h(\"slot\", { key: 'e9f5934518dc0cceaeadf1f2820614595fec6bc9' })), h(\"ion-backdrop\", { key: '7282077817657b1bb6c155f1404c0a519fece993', ref: (el) => (this.backdropEl = el), class: \"menu-backdrop\", tappable: false, stopPropagation: false, part: \"backdrop\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"type\": [\"typeChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"side\": [\"sideChanged\"],\n        \"swipeGesture\": [\"swipeGestureChanged\"]\n    }; }\n};\nconst computeDelta = (deltaX, isOpen, isEndSide) => {\n    return Math.max(0, isOpen !== isEndSide ? -deltaX : deltaX);\n};\nconst checkEdgeSide = (win, posX, isEndSide, maxEdgeStart) => {\n    if (isEndSide) {\n        return posX >= win.innerWidth - maxEdgeStart;\n    }\n    else {\n        return posX <= maxEdgeStart;\n    }\n};\nconst SHOW_MENU = 'show-menu';\nconst SHOW_BACKDROP = 'show-backdrop';\nconst MENU_CONTENT_OPEN = 'menu-content-open';\nMenu.style = {\n    ios: IonMenuIosStyle0,\n    md: IonMenuMdStyle0\n};\n\n// Given a menu, return whether or not the menu toggle should be visible\nconst updateVisibility = async (menu) => {\n    const menuEl = await menuController.get(menu);\n    return !!(menuEl && (await menuEl.isActive()));\n};\n\nconst menuButtonIosCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #0054e9);--padding-start:5px;--padding-end:5px;min-height:32px;font-size:clamp(31px, 1.9375rem, 38.13px)}:host(.ion-activated){opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}\";\nconst IonMenuButtonIosStyle0 = menuButtonIosCss;\n\nconst menuButtonMdCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:.04;--border-radius:50%;--color:initial;--padding-start:8px;--padding-end:8px;width:3rem;height:3rem;font-size:1.5rem}:host(.ion-color.ion-focused)::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}\";\nconst IonMenuButtonMdStyle0 = menuButtonMdCss;\n\nconst MenuButton = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inheritedAttributes = {};\n        this.onClick = async () => {\n            return menuController.toggle(this.menu);\n        };\n        this.visible = false;\n        this.color = undefined;\n        this.disabled = false;\n        this.menu = undefined;\n        this.autoHide = true;\n        this.type = 'button';\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    componentDidLoad() {\n        this.visibilityChanged();\n    }\n    async visibilityChanged() {\n        this.visible = await updateVisibility(this.menu);\n    }\n    render() {\n        const { color, disabled, inheritedAttributes } = this;\n        const mode = getIonMode(this);\n        const menuIcon = config.get('menuIcon', mode === 'ios' ? menuOutline : menuSharp);\n        const hidden = this.autoHide && !this.visible;\n        const attrs = {\n            type: this.type,\n        };\n        const ariaLabel = inheritedAttributes['aria-label'] || 'menu';\n        return (h(Host, { key: '7ec29715ce7926b7c2b08f3d9cac8aaa16b3dc28', onClick: this.onClick, \"aria-disabled\": disabled ? 'true' : null, \"aria-hidden\": hidden ? 'true' : null, class: createColorClasses(color, {\n                [mode]: true,\n                button: true, // ion-buttons target .button\n                'menu-button-hidden': hidden,\n                'menu-button-disabled': disabled,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'ion-activatable': true,\n                'ion-focusable': true,\n            }) }, h(\"button\", Object.assign({ key: 'd4c5929264af3ba0328118bcc27d2ab7ef5d3809' }, attrs, { disabled: disabled, class: \"button-native\", part: \"native\", \"aria-label\": ariaLabel }), h(\"span\", { key: '7bfa6e9a93105486623d044861e879ec79ff64f1', class: \"button-inner\" }, h(\"slot\", { key: '071ab58e285832fc188706166f5547d45d501ac5' }, h(\"ion-icon\", { key: '918ec5d791921de9821c347af4f65f97dd94aabf', part: \"icon\", icon: menuIcon, mode: mode, lazy: false, \"aria-hidden\": \"true\" }))), mode === 'md' && h(\"ion-ripple-effect\", { key: '00ffdd53f635e706c1dbd01b8e7944498650fe81', type: \"unbounded\" }))));\n    }\n    get el() { return getElement(this); }\n};\nMenuButton.style = {\n    ios: IonMenuButtonIosStyle0,\n    md: IonMenuButtonMdStyle0\n};\n\nconst menuToggleCss = \":host(.menu-toggle-hidden){display:none}\";\nconst IonMenuToggleStyle0 = menuToggleCss;\n\nconst MenuToggle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.onClick = () => {\n            return menuController.toggle(this.menu);\n        };\n        this.visible = false;\n        this.menu = undefined;\n        this.autoHide = true;\n    }\n    connectedCallback() {\n        this.visibilityChanged();\n    }\n    async visibilityChanged() {\n        this.visible = await updateVisibility(this.menu);\n    }\n    render() {\n        const mode = getIonMode(this);\n        const hidden = this.autoHide && !this.visible;\n        return (h(Host, { key: '7c27ea5b0795676bf5cb33e1f83aa142c197f64e', onClick: this.onClick, \"aria-hidden\": hidden ? 'true' : null, class: {\n                [mode]: true,\n                'menu-toggle-hidden': hidden,\n            } }, h(\"slot\", { key: '69f187becedc0fe34603d41d279f043cf0fdf776' })));\n    }\n};\nMenuToggle.style = IonMenuToggleStyle0;\n\nexport { Menu as ion_menu, MenuButton as ion_menu_button, MenuToggle as ion_menu_toggle };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC5G,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,4BAA4B;AACzE,SAASC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,OAAO,QAAQ,wBAAwB;AACnJ,SAASD,CAAC,IAAIE,kBAAkB,QAAQ,kCAAkC;AAC1E,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASP,CAAC,IAAIQ,SAAS,EAAEhB,CAAC,IAAIiB,qBAAqB,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,KAAK,QAAQ,uBAAuB;AAC3G,SAASH,CAAC,IAAII,cAAc,QAAQ,qBAAqB;AACzD,SAASC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC1F,SAAS/B,CAAC,IAAIgC,WAAW,EAAEN,CAAC,IAAIO,kBAAkB,QAAQ,qBAAqB;AAC/E,SAASC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,SAAS,QAAQ,qBAAqB;AACtE,OAAO,qBAAqB;AAC5B,OAAO,kCAAkC;AACzC,OAAO,qBAAqB;AAC5B,OAAO,yBAAyB;AAEhC,MAAMC,UAAU,GAAG,kxGAAkxG;AACryG,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,SAAS,GAAG,g0GAAg0G;AACl1G,MAAMC,eAAe,GAAGD,SAAS;AAEjC,MAAME,SAAS,GAAG,6BAA6B;AAC/C,MAAMC,QAAQ,GAAG,6BAA6B;AAC9C,MAAMC,gBAAgB,GAAG,gCAAgC;AACzD,MAAMC,eAAe,GAAG,8BAA8B;AACtD,MAAMC,IAAI,GAAG,MAAM;EACfC,WAAWA,CAACC,OAAO,EAAE;IACjBnD,gBAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAGlD,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAACmD,YAAY,GAAGnD,WAAW,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;IACxD,IAAI,CAACoD,UAAU,GAAGpD,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACpD,IAAI,CAACqD,WAAW,GAAGrD,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAACsD,aAAa,GAAGtD,WAAW,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;IAC1D,IAAI,CAACuD,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,OAAO,GAAGtC,kBAAkB,CAACuC,aAAa,CAAC;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC;IACxE,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,WAAW,GAAIC,EAAE,IAAK;MACvB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,WAAW,GAAGzD,mBAAmB,CAAC0D,QAAQ,CAAC;MACjD,IAAID,WAAW,IAAI,CAACA,WAAW,CAACE,QAAQ,CAAC,IAAI,CAACC,EAAE,CAAC,EAAE;QAC/C;MACJ;MACA,IAAI,CAACC,iBAAiB,CAACL,EAAE,EAAEE,QAAQ,CAAC;IACxC,CAAC;IACD,IAAI,CAACI,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACnD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACoD,SAAS,GAAGC,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGD,SAAS;IACvB,IAAI,CAACE,IAAI,GAAGF,SAAS;IACrB,IAAI,CAACG,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAG,OAAO;IACnB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,YAAY,GAAG,EAAE;EAC1B;EACAC,WAAWA,CAACL,IAAI,EAAEM,OAAO,EAAE;IACvB,MAAMC,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAIA,SAAS,EAAE;MACX,IAAID,OAAO,KAAKR,SAAS,EAAE;QACvBS,SAAS,CAACC,SAAS,CAACC,MAAM,CAAC,gBAAgBH,OAAO,EAAE,CAAC;MACzD;MACAC,SAAS,CAACC,SAAS,CAACE,GAAG,CAAC,gBAAgBV,IAAI,EAAE,CAAC;MAC/CO,SAAS,CAACI,eAAe,CAAC,OAAO,CAAC;IACtC;IACA,IAAI,IAAI,CAACC,WAAW,EAAE;MAClB;MACA,IAAI,CAACA,WAAW,CAACD,eAAe,CAAC,OAAO,CAAC;IAC7C;IACA,IAAI,CAACE,SAAS,GAAGf,SAAS;EAC9B;EACAgB,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACpC,aAAa,CAACqC,IAAI,CAAC;MACpBf,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBgB,IAAI,EAAE,IAAI,CAAC9B;IACf,CAAC,CAAC;EACN;EACA+B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACzE,SAAS,GAAGA,SAAS,CAAC,IAAI,CAACyD,IAAI,CAAC;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACW,SAAS,GAAGf,SAAS;EAC9B;EACAqB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACJ,WAAW,CAAC,CAAC;EACtB;EACMK,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB;MACA;MACA,IAAI,OAAOC,cAAc,KAAK,WAAW,IAAIA,cAAc,IAAI,IAAI,EAAE;QACjE,MAAMA,cAAc,CAACC,WAAW,CAAC,UAAU,CAAC;MAChD;MACA,IAAIH,KAAI,CAACrB,IAAI,KAAKF,SAAS,EAAE;QACzBuB,KAAI,CAACrB,IAAI,GAAG/C,MAAM,CAACwE,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC;MACjD;MACA,MAAMC,OAAO,GAAGL,KAAI,CAACxB,SAAS,KAAKC,SAAS,GAAGN,QAAQ,CAACmC,cAAc,CAACN,KAAI,CAACxB,SAAS,CAAC,GAAG,IAAI;MAC7F,IAAI6B,OAAO,KAAK,IAAI,EAAE;QAClBE,OAAO,CAACC,KAAK,CAAC,mEAAmE,CAAC;QAClF;MACJ;MACA,IAAIR,KAAI,CAAC3B,EAAE,CAACD,QAAQ,CAACiC,OAAO,CAAC,EAAE;QAC3BE,OAAO,CAACC,KAAK,CAAC,4GAA4G,CAAC;MAC/H;MACAR,KAAI,CAACd,SAAS,GAAGmB,OAAO;MACxB;MACAA,OAAO,CAAClB,SAAS,CAACE,GAAG,CAAC,cAAc,CAAC;MACrCW,KAAI,CAAChB,WAAW,CAACgB,KAAI,CAACrB,IAAI,EAAEF,SAAS,CAAC;MACtCuB,KAAI,CAACH,WAAW,CAAC,CAAC;MAClB;MACAnE,cAAc,CAAC+E,SAAS,CAACT,KAAI,CAAC;MAC9BA,KAAI,CAACU,WAAW,CAAC,CAAC;MAClBV,KAAI,CAACW,OAAO,GAAG,OAAO,MAAM,CAAC,qBAAqB,CAAC,EAAEC,aAAa,CAAC;QAC/DvC,EAAE,EAAEF,QAAQ;QACZ0C,WAAW,EAAE,YAAY;QACzBC,eAAe,EAAE,EAAE;QACnBC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAGhD,EAAE,IAAK+B,KAAI,CAACiB,QAAQ,CAAChD,EAAE,CAAC;QACnCiD,WAAW,EAAEA,CAAA,KAAMlB,KAAI,CAACkB,WAAW,CAAC,CAAC;QACrCC,OAAO,EAAEA,CAAA,KAAMnB,KAAI,CAACmB,OAAO,CAAC,CAAC;QAC7BC,MAAM,EAAGnD,EAAE,IAAK+B,KAAI,CAACoB,MAAM,CAACnD,EAAE,CAAC;QAC/BoD,KAAK,EAAGpD,EAAE,IAAK+B,KAAI,CAACqB,KAAK,CAACpD,EAAE;MAChC,CAAC,CAAC;MACF+B,KAAI,CAACN,WAAW,CAAC,CAAC;IAAC;EACvB;EACA4B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvD,mBAAmB,GAAG1C,qBAAqB,CAAC,IAAI,CAACgD,EAAE,CAAC;EAC7D;EACMkD,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAvB,iBAAA;MACrBuB,MAAI,CAAC7D,OAAO,GAAG,IAAI;MACnB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,MAAM8D,SAAS,GAAGD,MAAI,CAACnD,EAAE,CAACqD,OAAO,CAAC,gBAAgB,CAAC;MACnD,IAAID,SAAS,KAAK,IAAI,EAAE;QACpBD,MAAI,CAACjD,aAAa,SAASkD,SAAS,CAACE,SAAS,CAAC,CAAC;MACpD;MACAH,MAAI,CAACd,WAAW,CAAC,CAAC;MAClBc,MAAI,CAAC9B,WAAW,CAAC,CAAC;IAAC;EACvB;EACAgB,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAAC/C,OAAO,EAAE;MACd,IAAI,CAACL,aAAa,CAACqC,IAAI,CAAC;QAAEf,QAAQ,EAAE,IAAI,CAACA,QAAQ;QAAEgB,IAAI,EAAE,IAAI,CAAC9B;MAAQ,CAAC,CAAC;IAC5E;EACJ;EACM8D,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA5B,iBAAA;MACzB;AACR;AACA;AACA;AACA;AACA;AACA;MACQ,MAAM4B,MAAI,CAACC,KAAK,CAAC,KAAK,CAAC;MACvBD,MAAI,CAACrE,OAAO,CAACuE,OAAO,CAAC,CAAC;MACtBrG,cAAc,CAACsG,WAAW,CAACH,MAAI,CAAC;MAChC,IAAIA,MAAI,CAACrC,SAAS,EAAE;QAChBqC,MAAI,CAACrC,SAAS,CAACuC,OAAO,CAAC,CAAC;MAC5B;MACA,IAAIF,MAAI,CAAClB,OAAO,EAAE;QACdkB,MAAI,CAAClB,OAAO,CAACoB,OAAO,CAAC,CAAC;QACtBF,MAAI,CAAClB,OAAO,GAAGlC,SAAS;MAC5B;MACAoD,MAAI,CAACrC,SAAS,GAAGf,SAAS;MAC1BoD,MAAI,CAAC3C,SAAS,GAAGT,SAAS;IAAC;EAC/B;EACAwD,kBAAkBA,CAAChE,EAAE,EAAE;IACnB,MAAMiE,gBAAgB,GAAG,IAAI,CAAC7D,EAAE,CAACqD,OAAO,CAAC,gBAAgB,CAAC;IAC1D,IAAIQ,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAKjE,EAAE,CAACkE,MAAM,EAAE;MAC7D,IAAI,CAAC5D,aAAa,GAAGN,EAAE,CAACmE,MAAM,CAACC,OAAO;MACtC,IAAI,CAAC3C,WAAW,CAAC,CAAC;IACtB;EACJ;EACA4C,eAAeA,CAACrE,EAAE,EAAE;IAChB;IACA,IAAI,IAAI,CAACH,OAAO,IAAI,IAAI,CAACP,SAAS,GAAGU,EAAE,CAACsE,SAAS,GAAG,GAAG,EAAE;MACrD,MAAMC,WAAW,GAAGvE,EAAE,CAACwE,YAAY,GAAG,CAACxE,EAAE,CAACwE,YAAY,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACnD,WAAW,CAAC,GAAG,KAAK;MAC3F,IAAIiD,WAAW,EAAE;QACbvE,EAAE,CAAC0E,cAAc,CAAC,CAAC;QACnB1E,EAAE,CAAC2E,eAAe,CAAC,CAAC;QACpB,IAAI,CAACd,KAAK,CAACrD,SAAS,EAAE9D,QAAQ,CAAC;MACnC;IACJ;EACJ;EACAkI,SAASA,CAAC5E,EAAE,EAAE;IACV,IAAIA,EAAE,CAAC6E,GAAG,KAAK,QAAQ,EAAE;MACrB,IAAI,CAAChB,KAAK,CAACrD,SAAS,EAAE9D,QAAQ,CAAC;IACnC;EACJ;EACA;AACJ;AACA;EACIoI,MAAMA,CAAA,EAAG;IACL,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAACnF,OAAO,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIoF,QAAQA,CAAA,EAAG;IACP,OAAOF,OAAO,CAACC,OAAO,CAAC,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;AACA;EACIvD,IAAIA,CAACwD,QAAQ,GAAG,IAAI,EAAE;IAClB,OAAO,IAAI,CAACC,OAAO,CAAC,IAAI,EAAED,QAAQ,CAAC;EACvC;EACA;AACJ;AACA;AACA;EACItB,KAAKA,CAACsB,QAAQ,GAAG,IAAI,EAAEE,IAAI,EAAE;IACzB,OAAO,IAAI,CAACD,OAAO,CAAC,KAAK,EAAED,QAAQ,EAAEE,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;EACIC,MAAMA,CAACH,QAAQ,GAAG,IAAI,EAAE;IACpB,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,IAAI,CAACvF,OAAO,EAAEsF,QAAQ,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACIC,OAAOA,CAACG,UAAU,EAAEJ,QAAQ,GAAG,IAAI,EAAEE,IAAI,EAAE;IACvC,OAAO5H,cAAc,CAAC+H,QAAQ,CAAC,IAAI,EAAED,UAAU,EAAEJ,QAAQ,EAAEE,IAAI,CAAC;EACpE;EACAhF,iBAAiBA,CAACL,EAAE,EAAEyF,GAAG,EAAE;IACvB,MAAMvB,MAAM,GAAGlE,EAAE,CAACkE,MAAM;IACxB,IAAI,CAACA,MAAM,EAAE;MACT;IACJ;IACA;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAAC9D,EAAE,CAACD,QAAQ,CAAC+D,MAAM,CAAC,EAAE;MAC1B,IAAI,CAACwB,SAAS,GAAGxB,MAAM;IAC3B,CAAC,MACI;MACD;AACZ;AACA;AACA;MACY,MAAM;QAAE9D;MAAG,CAAC,GAAG,IAAI;MACnB;AACZ;AACA;AACA;AACA;AACA;MACYxD,oBAAoB,CAACwD,EAAE,CAAC;MACxB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACsF,SAAS,KAAKD,GAAG,CAACE,aAAa,EAAE;QACtC7I,mBAAmB,CAACsD,EAAE,CAAC;MAC3B;IACJ;EACJ;EACMoF,QAAQA,CAACD,UAAU,EAAEJ,QAAQ,GAAG,IAAI,EAAEE,IAAI,EAAE;IAAA,IAAAO,MAAA;IAAA,OAAA5D,iBAAA;MAC9C;MACA,IAAI,CAAC4D,MAAI,CAACV,SAAS,CAAC,CAAC,IAAIU,MAAI,CAAChG,WAAW,IAAI2F,UAAU,KAAKK,MAAI,CAAC/F,OAAO,EAAE;QACtE,OAAO,KAAK;MAChB;MACA+F,MAAI,CAACC,eAAe,CAACN,UAAU,EAAEF,IAAI,CAAC;MACtC,MAAMO,MAAI,CAACE,aAAa,CAAC,CAAC;MAC1B,MAAMF,MAAI,CAACG,cAAc,CAACR,UAAU,EAAEJ,QAAQ,CAAC;MAC/C;AACR;AACA;AACA;AACA;MACQ,IAAIS,MAAI,CAACjG,kBAAkB,EAAE;QACzBiG,MAAI,CAACjG,kBAAkB,GAAG,KAAK;QAC/B,OAAO,KAAK;MAChB;MACAiG,MAAI,CAACI,cAAc,CAACT,UAAU,EAAEF,IAAI,CAAC;MACrC,OAAO,IAAI;IAAC;EAChB;EACMS,aAAaA,CAAA,EAAG;IAAA,IAAAG,MAAA;IAAA,OAAAjE,iBAAA;MAClB;MACA;MACA,MAAMkE,KAAK,GAAGD,MAAI,CAAC3E,WAAW,CAAC6E,WAAW;MAC1C;AACR;AACA;AACA;MACQ,MAAMC,WAAW,GAAGjJ,SAAS,CAAC8I,MAAI,CAACrF,IAAI,CAAC;MACxC,IAAIsF,KAAK,KAAKD,MAAI,CAACC,KAAK,IAAID,MAAI,CAAC1E,SAAS,KAAKf,SAAS,IAAI4F,WAAW,KAAKH,MAAI,CAAC9I,SAAS,EAAE;QACxF;MACJ;MACA8I,MAAI,CAACC,KAAK,GAAGA,KAAK;MAClBD,MAAI,CAAC9I,SAAS,GAAGiJ,WAAW;MAC5B;MACA,IAAIH,MAAI,CAAC1E,SAAS,EAAE;QAChB0E,MAAI,CAAC1E,SAAS,CAACuC,OAAO,CAAC,CAAC;QACxBmC,MAAI,CAAC1E,SAAS,GAAGf,SAAS;MAC9B;MACA;MACA,MAAMe,SAAS,GAAI0E,MAAI,CAAC1E,SAAS,SAAS9D,cAAc,CAAC4I,gBAAgB,CAACJ,MAAI,CAACvF,IAAI,EAAEuF,MAAI,CAAE;MAC3F,IAAI,CAACtI,MAAM,CAAC2I,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE;QACtC/E,SAAS,CAACgF,QAAQ,CAAC,CAAC,CAAC;MACzB;MACAhF,SAAS,CAACiF,IAAI,CAAC,MAAM,CAAC;IAAC;EAC3B;EACMT,cAAcA,CAACR,UAAU,EAAEJ,QAAQ,EAAE;IAAA,IAAAsB,MAAA;IAAA,OAAAzE,iBAAA;MACvC,MAAM0E,UAAU,GAAG,CAACnB,UAAU;MAC9B,MAAMoB,IAAI,GAAG9I,UAAU,CAAC4I,MAAI,CAAC;MAC7B,MAAMG,MAAM,GAAGD,IAAI,KAAK,KAAK,GAAGjI,SAAS,GAAGC,QAAQ;MACpD,MAAMkI,aAAa,GAAGF,IAAI,KAAK,KAAK,GAAG/H,gBAAgB,GAAGC,eAAe;MACzE,MAAMiI,GAAG,GAAGL,MAAI,CAAClF,SAAS,CACrBwF,SAAS,CAACL,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC,CAC5CE,MAAM,CAACF,UAAU,GAAGG,aAAa,GAAGD,MAAM,CAAC;MAChD,IAAIzB,QAAQ,EAAE;QACV,MAAM2B,GAAG,CAACE,IAAI,CAAC,CAAC;MACpB,CAAC,MACI;QACDF,GAAG,CAACE,IAAI,CAAC;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MAC5B;MACA;AACR;AACA;AACA;AACA;AACA;AACA;MACQ,IAAIH,GAAG,CAACI,YAAY,CAAC,CAAC,KAAK,SAAS,EAAE;QAClCJ,GAAG,CAACC,SAAS,CAAC,QAAQ,CAAC;MAC3B;IAAC;EACL;EACA7B,SAASA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACvE,QAAQ,IAAI,CAAC,IAAI,CAACL,aAAa;EAChD;EACA6G,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACtG,YAAY,IAAI,CAAC,IAAI,CAACjB,WAAW,IAAI,IAAI,CAACsF,SAAS,CAAC,CAAC;EACrE;EACAlC,QAAQA,CAACmB,MAAM,EAAE;IACb;IACA,MAAMiD,gBAAgB,GAAG,CAAC,CAAClH,QAAQ,CAACmH,aAAa,CAAC,sBAAsB,CAAC;IACzE,IAAID,gBAAgB,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACtH,OAAO,EAAE;MACd,OAAO,IAAI;IACf,CAAC,MACI,IAAIpC,cAAc,CAAC6J,YAAY,CAAC,CAAC,EAAE;MACpC,OAAO,KAAK;IAChB;IACA,OAAOC,aAAa,CAACC,MAAM,EAAErD,MAAM,CAACsD,QAAQ,EAAE,IAAI,CAACtK,SAAS,EAAE,IAAI,CAAC2D,YAAY,CAAC;EACpF;EACAmC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4C,eAAe,CAAC,CAAC,IAAI,CAAChG,OAAO,EAAE7C,OAAO,CAAC;IAC5C,OAAO,IAAI,CAAC8I,aAAa,CAAC,CAAC;EAC/B;EACA5C,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACtD,WAAW,IAAI,CAAC,IAAI,CAAC2B,SAAS,EAAE;MACtCjE,MAAM,CAAC,KAAK,EAAE,4BAA4B,CAAC;MAC3C;IACJ;IACA;IACA,IAAI,CAACiE,SAAS,CAACmG,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC7H,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5D;EACAsD,MAAMA,CAACgB,MAAM,EAAE;IACX,IAAI,CAAC,IAAI,CAACvE,WAAW,IAAI,CAAC,IAAI,CAAC2B,SAAS,EAAE;MACtCjE,MAAM,CAAC,KAAK,EAAE,4BAA4B,CAAC;MAC3C;IACJ;IACA,MAAMqK,KAAK,GAAGC,YAAY,CAACzD,MAAM,CAAC0D,MAAM,EAAE,IAAI,CAAChI,OAAO,EAAE,IAAI,CAAC1C,SAAS,CAAC;IACvE,MAAM2K,SAAS,GAAGH,KAAK,GAAG,IAAI,CAACzB,KAAK;IACpC,IAAI,CAAC3E,SAAS,CAACwG,YAAY,CAAC,IAAI,CAAClI,OAAO,GAAG,CAAC,GAAGiI,SAAS,GAAGA,SAAS,CAAC;EACzE;EACA1E,KAAKA,CAACe,MAAM,EAAE;IACV,IAAI,CAAC,IAAI,CAACvE,WAAW,IAAI,CAAC,IAAI,CAAC2B,SAAS,EAAE;MACtCjE,MAAM,CAAC,KAAK,EAAE,4BAA4B,CAAC;MAC3C;IACJ;IACA,MAAMwH,MAAM,GAAG,IAAI,CAACjF,OAAO;IAC3B,MAAM1C,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMwK,KAAK,GAAGC,YAAY,CAACzD,MAAM,CAAC0D,MAAM,EAAE/C,MAAM,EAAE3H,SAAS,CAAC;IAC5D,MAAM+I,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAM4B,SAAS,GAAGH,KAAK,GAAGzB,KAAK;IAC/B,MAAM8B,QAAQ,GAAG7D,MAAM,CAAC8D,SAAS;IACjC,MAAMC,CAAC,GAAGhC,KAAK,GAAG,GAAG;IACrB,MAAMiC,mBAAmB,GAAGH,QAAQ,IAAI,CAAC,KAAKA,QAAQ,GAAG,GAAG,IAAI7D,MAAM,CAAC0D,MAAM,GAAGK,CAAC,CAAC;IAClF,MAAME,kBAAkB,GAAGJ,QAAQ,IAAI,CAAC,KAAKA,QAAQ,GAAG,CAAC,GAAG,IAAI7D,MAAM,CAAC0D,MAAM,GAAG,CAACK,CAAC,CAAC;IACnF,MAAMG,cAAc,GAAGvD,MAAM,GACvB3H,SAAS,GACLgL,mBAAmB,GACnBC,kBAAkB,GACtBjL,SAAS,GACLiL,kBAAkB,GAClBD,mBAAmB;IAC7B,IAAI5C,UAAU,GAAG,CAACT,MAAM,IAAIuD,cAAc;IAC1C,IAAIvD,MAAM,IAAI,CAACuD,cAAc,EAAE;MAC3B9C,UAAU,GAAG,IAAI;IACrB;IACA,IAAI,CAACjG,SAAS,GAAG6E,MAAM,CAACmE,WAAW;IACnC;IACA,IAAIC,YAAY,GAAGF,cAAc,GAAG,KAAK,GAAG,CAAC,KAAK;IAClD;AACR;AACA;AACA;AACA;IACQ,MAAMG,iBAAiB,GAAGV,SAAS,GAAG,CAAC,GAAG,IAAI,GAAGA,SAAS;IAC1D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQS,YAAY,IACRjM,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEkB,KAAK,CAAC,CAAC,EAAEgL,iBAAiB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5G,MAAMC,MAAM,GAAG,IAAI,CAAC5I,OAAO,GAAG,CAACwI,cAAc,GAAGA,cAAc;IAC9D,IAAI,CAAC9G,SAAS,CACTqF,MAAM,CAAC,gCAAgC,CAAC,CACxC8B,QAAQ,CAAC,MAAM,IAAI,CAAC1C,cAAc,CAACT,UAAU,EAAEvI,OAAO,CAAC,EAAE;MAAE2L,eAAe,EAAE;IAAK,CAAC,CAAC,CACnFC,WAAW,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC5I,OAAO,GAAG,CAAC,GAAG0I,YAAY,GAAGA,YAAY,EAAE,GAAG,CAAC;EACzF;EACA1C,eAAeA,CAACN,UAAU,EAAEF,IAAI,EAAE;IAC9B/H,MAAM,CAAC,CAAC,IAAI,CAACsC,WAAW,EAAE,gDAAgD,CAAC;IAC3E;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI7B,UAAU,CAAC,SAAS,CAAC,EAAE;MACvB,IAAI,CAACqC,EAAE,CAACyI,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC/C;IACA;IACA;IACA,IAAI,CAACzI,EAAE,CAACc,SAAS,CAACE,GAAG,CAAC0H,SAAS,CAAC;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC1I,EAAE,CAACyI,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC;IACrC,IAAI,IAAI,CAACE,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAAC7H,SAAS,CAACE,GAAG,CAAC4H,aAAa,CAAC;IAChD;IACA;IACA,IAAI,IAAI,CAAC/H,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACC,SAAS,CAACE,GAAG,CAAC6H,iBAAiB,CAAC;MAC/C;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAAChI,SAAS,CAAC4H,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACtD;IACA,IAAI,CAACtJ,OAAO,CAAC2J,KAAK,CAAC,CAAC;IACpB,IAAI,CAACtJ,WAAW,GAAG,IAAI;IACvB,IAAI2F,UAAU,EAAE;MACZ,IAAI,CAACtG,WAAW,CAACyC,IAAI,CAAC,CAAC;IAC3B,CAAC,MACI;MACD,IAAI,CAACxC,YAAY,CAACwC,IAAI,CAAC;QAAE2D;MAAK,CAAC,CAAC;IACpC;EACJ;EACAW,cAAcA,CAAClB,MAAM,EAAEO,IAAI,EAAE;IACzB,IAAI8D,EAAE;IACN;IACA;IACA;IACA;IACA,IAAI,CAACtJ,OAAO,GAAGiF,MAAM;IACrB,IAAI,CAAClF,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACf,IAAI,CAACN,OAAO,CAAC6J,OAAO,CAAC,CAAC;IAC1B;IACA,IAAItE,MAAM,EAAE;MACR;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI/G,UAAU,CAAC,SAAS,CAAC,EAAE;QACvB,IAAI,CAACqC,EAAE,CAACiB,eAAe,CAAC,aAAa,CAAC;MAC1C;MACA;MACA,IAAI,CAAClC,UAAU,CAACuC,IAAI,CAAC,CAAC;MACtB;AACZ;AACA;AACA;AACA;MACY,MAAM2H,WAAW,GAAG,CAACF,EAAE,GAAGjJ,QAAQ,CAACyF,aAAa,MAAM,IAAI,IAAIwD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC1F,OAAO,CAAC,UAAU,CAAC;MAC7G,IAAI4F,WAAW,KAAK,IAAI,CAACjJ,EAAE,EAAE;QACzB,IAAI,CAACA,EAAE,CAACkJ,KAAK,CAAC,CAAC;MACnB;MACA;MACApJ,QAAQ,CAACqJ,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACxJ,WAAW,EAAE,IAAI,CAAC;IAC9D,CAAC,MACI;MACD,IAAI,CAACK,EAAE,CAACiB,eAAe,CAAC,aAAa,CAAC;MACtC;MACA,IAAI,CAACjB,EAAE,CAACc,SAAS,CAACC,MAAM,CAAC2H,SAAS,CAAC;MACnC;AACZ;AACA;AACA;MACY,IAAI,CAAC1I,EAAE,CAACiB,eAAe,CAAC,UAAU,CAAC;MACnC,IAAI,IAAI,CAACJ,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAACC,SAAS,CAACC,MAAM,CAAC8H,iBAAiB,CAAC;QAClD;AAChB;AACA;AACA;AACA;QACgB,IAAI,CAAChI,SAAS,CAACI,eAAe,CAAC,aAAa,CAAC;MACjD;MACA,IAAI,IAAI,CAAC0H,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAAC7H,SAAS,CAACC,MAAM,CAAC6H,aAAa,CAAC;MACnD;MACA,IAAI,IAAI,CAACzH,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAACiI,IAAI,CAAC,CAAC;MACzB;MACA;MACA,IAAI,CAACpK,WAAW,CAACsC,IAAI,CAAC;QAAE2D;MAAK,CAAC,CAAC;MAC/B;MACAnF,QAAQ,CAACuJ,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC1J,WAAW,EAAE,IAAI,CAAC;IACjE;EACJ;EACA0B,WAAWA,CAAA,EAAG;IACV,MAAMwD,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IACjC,IAAI,IAAI,CAACxC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACgH,MAAM,CAACzE,QAAQ,IAAI,IAAI,CAACpE,YAAY,CAAC;IACtD;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACoE,QAAQ,EAAE;MACX;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACrF,WAAW,EAAE;QAClB,IAAI,CAACD,kBAAkB,GAAG,IAAI;MAClC;MACA;AACZ;AACA;AACA;MACY,IAAI,CAACqG,cAAc,CAAC,KAAK,EAAEhJ,OAAO,CAAC;IACvC;EACJ;EACA2M,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEjJ,IAAI;MAAEC,QAAQ;MAAEP,EAAE;MAAEE,aAAa;MAAER,mBAAmB;MAAEc;IAAK,CAAC,GAAG,IAAI;IAC7E,MAAM+F,IAAI,GAAG9I,UAAU,CAAC,IAAI,CAAC;IAC7B;AACR;AACA;AACA;AACA;IACQ,OAAQ7B,CAAC,CAACE,IAAI,EAAE;MAAE2I,GAAG,EAAE,0CAA0C;MAAE+E,SAAS,EAAE1M,qBAAqB,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC0H,SAAS;MAAES,IAAI,EAAE,YAAY;MAAE,YAAY,EAAEvF,mBAAmB,CAAC,YAAY,CAAC,IAAI,MAAM;MAAE+J,KAAK,EAAE;QAC7M,CAAClD,IAAI,GAAG,IAAI;QACZ,CAAC,aAAajG,IAAI,EAAE,GAAG,IAAI;QAC3B,cAAc,EAAE,CAACC,QAAQ;QACzB,CAAC,aAAaC,IAAI,EAAE,GAAG,IAAI;QAC3B,mBAAmB,EAAEN,aAAa;QAClC,iBAAiB,EAAEtC,WAAW,CAAC,gBAAgB,EAAEoC,EAAE;MACvD;IAAE,CAAC,EAAEpE,CAAC,CAAC,KAAK,EAAE;MAAE6I,GAAG,EAAE,0CAA0C;MAAEgF,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE,WAAW;MAAEC,GAAG,EAAG3J,EAAE,IAAM,IAAI,CAACkB,WAAW,GAAGlB;IAAI,CAAC,EAAEpE,CAAC,CAAC,MAAM,EAAE;MAAE6I,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAE7I,CAAC,CAAC,cAAc,EAAE;MAAE6I,GAAG,EAAE,0CAA0C;MAAEkF,GAAG,EAAG3J,EAAE,IAAM,IAAI,CAAC2I,UAAU,GAAG3I,EAAG;MAAEyJ,KAAK,EAAE,eAAe;MAAEG,QAAQ,EAAE,KAAK;MAAErF,eAAe,EAAE,KAAK;MAAEmF,IAAI,EAAE;IAAW,CAAC,CAAC,CAAC;EACzZ;EACA,IAAI1J,EAAEA,CAAA,EAAG;IAAE,OAAOhE,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW6N,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,aAAa,CAAC;MACvB,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,MAAM,EAAE,CAAC,aAAa,CAAC;MACvB,cAAc,EAAE,CAAC,qBAAqB;IAC1C,CAAC;EAAE;AACP,CAAC;AACD,MAAMrC,YAAY,GAAGA,CAACC,MAAM,EAAE/C,MAAM,EAAE3H,SAAS,KAAK;EAChD,OAAO+M,IAAI,CAACC,GAAG,CAAC,CAAC,EAAErF,MAAM,KAAK3H,SAAS,GAAG,CAAC0K,MAAM,GAAGA,MAAM,CAAC;AAC/D,CAAC;AACD,MAAMN,aAAa,GAAGA,CAAC6C,GAAG,EAAEC,IAAI,EAAElN,SAAS,EAAE2D,YAAY,KAAK;EAC1D,IAAI3D,SAAS,EAAE;IACX,OAAOkN,IAAI,IAAID,GAAG,CAACE,UAAU,GAAGxJ,YAAY;EAChD,CAAC,MACI;IACD,OAAOuJ,IAAI,IAAIvJ,YAAY;EAC/B;AACJ,CAAC;AACD,MAAMgI,SAAS,GAAG,WAAW;AAC7B,MAAME,aAAa,GAAG,eAAe;AACrC,MAAMC,iBAAiB,GAAG,mBAAmB;AAC7CnK,IAAI,CAACyL,KAAK,GAAG;EACTC,GAAG,EAAEjM,gBAAgB;EACrBkM,EAAE,EAAEhM;AACR,CAAC;;AAED;AACA,MAAMiM,gBAAgB;EAAA,IAAAC,IAAA,GAAA3I,iBAAA,CAAG,WAAO4I,IAAI,EAAK;IACrC,MAAMC,MAAM,SAASpN,cAAc,CAAC0E,GAAG,CAACyI,IAAI,CAAC;IAC7C,OAAO,CAAC,EAAEC,MAAM,WAAWA,MAAM,CAAC5F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC;EAAA,gBAHKyF,gBAAgBA,CAAAI,EAAA;IAAA,OAAAH,IAAA,CAAAI,KAAA,OAAAC,SAAA;EAAA;AAAA,GAGrB;AAED,MAAMC,gBAAgB,GAAG,+2FAA+2F;AACx4F,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,eAAe,GAAG,49FAA49F;AACp/F,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,UAAU,GAAG,MAAM;EACrBtM,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAsM,MAAA;IACjBzP,gBAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B,IAAI,CAACc,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACyL,OAAO,gBAAAvJ,iBAAA,CAAG,aAAY;MACvB,OAAOvE,cAAc,CAAC6H,MAAM,CAACgG,MAAI,CAACV,IAAI,CAAC;IAC3C,CAAC;IACD,IAAI,CAACxG,OAAO,GAAG,KAAK;IACpB,IAAI,CAACoH,KAAK,GAAGhL,SAAS;IACtB,IAAI,CAACG,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACiK,IAAI,GAAGpK,SAAS;IACrB,IAAI,CAACiL,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC/K,IAAI,GAAG,QAAQ;EACxB;EACA2C,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvD,mBAAmB,GAAG1C,qBAAqB,CAAC,IAAI,CAACgD,EAAE,CAAC;EAC7D;EACAkD,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACoI,iBAAiB,CAAC,CAAC;EAC5B;EACMA,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA3J,iBAAA;MACtB2J,MAAI,CAACvH,OAAO,SAASsG,gBAAgB,CAACiB,MAAI,CAACf,IAAI,CAAC;IAAC;EACrD;EACAjB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE6B,KAAK;MAAE7K,QAAQ;MAAEb;IAAoB,CAAC,GAAG,IAAI;IACrD,MAAM6G,IAAI,GAAG9I,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM+N,QAAQ,GAAGjO,MAAM,CAACwE,GAAG,CAAC,UAAU,EAAEwE,IAAI,KAAK,KAAK,GAAGxI,WAAW,GAAGE,SAAS,CAAC;IACjF,MAAMwN,MAAM,GAAG,IAAI,CAACJ,QAAQ,IAAI,CAAC,IAAI,CAACrH,OAAO;IAC7C,MAAM0H,KAAK,GAAG;MACVpL,IAAI,EAAE,IAAI,CAACA;IACf,CAAC;IACD,MAAMqL,SAAS,GAAGjM,mBAAmB,CAAC,YAAY,CAAC,IAAI,MAAM;IAC7D,OAAQ9D,CAAC,CAACE,IAAI,EAAE;MAAE2I,GAAG,EAAE,0CAA0C;MAAE0G,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE,eAAe,EAAE5K,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE,aAAa,EAAEkL,MAAM,GAAG,MAAM,GAAG,IAAI;MAAEhC,KAAK,EAAE5L,kBAAkB,CAACuN,KAAK,EAAE;QACrM,CAAC7E,IAAI,GAAG,IAAI;QACZqF,MAAM,EAAE,IAAI;QAAE;QACd,oBAAoB,EAAEH,MAAM;QAC5B,sBAAsB,EAAElL,QAAQ;QAChC,YAAY,EAAE3C,WAAW,CAAC,aAAa,EAAE,IAAI,CAACoC,EAAE,CAAC;QACjD,kBAAkB,EAAEpC,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAACoC,EAAE,CAAC;QAC9D,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE;MACrB,CAAC;IAAE,CAAC,EAAEpE,CAAC,CAAC,QAAQ,EAAEiQ,MAAM,CAACC,MAAM,CAAC;MAAErH,GAAG,EAAE;IAA2C,CAAC,EAAEiH,KAAK,EAAE;MAAEnL,QAAQ,EAAEA,QAAQ;MAAEkJ,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE,QAAQ;MAAE,YAAY,EAAEiC;IAAU,CAAC,CAAC,EAAE/P,CAAC,CAAC,MAAM,EAAE;MAAE6I,GAAG,EAAE,0CAA0C;MAAEgF,KAAK,EAAE;IAAe,CAAC,EAAE7N,CAAC,CAAC,MAAM,EAAE;MAAE6I,GAAG,EAAE;IAA2C,CAAC,EAAE7I,CAAC,CAAC,UAAU,EAAE;MAAE6I,GAAG,EAAE,0CAA0C;MAAEiF,IAAI,EAAE,MAAM;MAAEqC,IAAI,EAAEP,QAAQ;MAAEjF,IAAI,EAAEA,IAAI;MAAEyF,IAAI,EAAE,KAAK;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAC,CAAC,EAAEzF,IAAI,KAAK,IAAI,IAAI3K,CAAC,CAAC,mBAAmB,EAAE;MAAE6I,GAAG,EAAE,0CAA0C;MAAEnE,IAAI,EAAE;IAAY,CAAC,CAAC,CAAC,CAAC;EACxlB;EACA,IAAIN,EAAEA,CAAA,EAAG;IAAE,OAAOhE,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDiP,UAAU,CAACd,KAAK,GAAG;EACfC,GAAG,EAAEU,sBAAsB;EAC3BT,EAAE,EAAEW;AACR,CAAC;AAED,MAAMiB,aAAa,GAAG,0CAA0C;AAChE,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,UAAU,GAAG,MAAM;EACrBxN,WAAWA,CAACC,OAAO,EAAE;IACjBnD,gBAAgB,CAAC,IAAI,EAAEmD,OAAO,CAAC;IAC/B,IAAI,CAACuM,OAAO,GAAG,MAAM;MACjB,OAAO9N,cAAc,CAAC6H,MAAM,CAAC,IAAI,CAACsF,IAAI,CAAC;IAC3C,CAAC;IACD,IAAI,CAACxG,OAAO,GAAG,KAAK;IACpB,IAAI,CAACwG,IAAI,GAAGpK,SAAS;IACrB,IAAI,CAACiL,QAAQ,GAAG,IAAI;EACxB;EACA3J,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC4J,iBAAiB,CAAC,CAAC;EAC5B;EACMA,iBAAiBA,CAAA,EAAG;IAAA,IAAAc,MAAA;IAAA,OAAAxK,iBAAA;MACtBwK,MAAI,CAACpI,OAAO,SAASsG,gBAAgB,CAAC8B,MAAI,CAAC5B,IAAI,CAAC;IAAC;EACrD;EACAjB,MAAMA,CAAA,EAAG;IACL,MAAMhD,IAAI,GAAG9I,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMgO,MAAM,GAAG,IAAI,CAACJ,QAAQ,IAAI,CAAC,IAAI,CAACrH,OAAO;IAC7C,OAAQpI,CAAC,CAACE,IAAI,EAAE;MAAE2I,GAAG,EAAE,0CAA0C;MAAE0G,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE,aAAa,EAAEM,MAAM,GAAG,MAAM,GAAG,IAAI;MAAEhC,KAAK,EAAE;QAChI,CAAClD,IAAI,GAAG,IAAI;QACZ,oBAAoB,EAAEkF;MAC1B;IAAE,CAAC,EAAE7P,CAAC,CAAC,MAAM,EAAE;MAAE6I,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;AACJ,CAAC;AACD0H,UAAU,CAAChC,KAAK,GAAG+B,mBAAmB;AAEtC,SAASxN,IAAI,IAAI2N,QAAQ,EAAEpB,UAAU,IAAIqB,eAAe,EAAEH,UAAU,IAAII,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}