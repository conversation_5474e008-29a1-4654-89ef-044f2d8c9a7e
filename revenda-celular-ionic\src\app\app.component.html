<ion-app>
  <ion-menu contentId="main-content" color="secondary">
    <ion-header>
      <ion-toolbar color="secondary">
        <ion-title>Revenda de Celulares</ion-title>
      </ion-toolbar>
    </ion-header>
    <ion-content >
      <ion-list id="inbox-list">

        @for (p of appPages; track p; let i = $index) {
        <ion-menu-toggle auto-hide="false">
          <ion-item routerDirection="root" [routerLink]="[p.url]" lines="none" detail="false"
            routerLinkActive="selected">
            <ion-icon aria-hidden="true" slot="start" [ios]="p.icon + '-outline'" [md]="p.icon + '-sharp'"></ion-icon>
            <ion-title>{{ p.title }}</ion-title>
          </ion-item>
        </ion-menu-toggle>
        }
      </ion-list>
    </ion-content>
  </ion-menu>
  <ion-router-outlet id="main-content"></ion-router-outlet>
</ion-app>
