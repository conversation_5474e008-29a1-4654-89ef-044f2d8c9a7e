{"ast": null, "code": "var _StoreService;\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class StoreService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.baseUrl}/stores`;\n  }\n  getList() {\n    return this.http.get(this.apiUrl);\n  }\n  getById(storeId) {\n    return this.http.get(`${this.apiUrl}/${storeId}`);\n  }\n  add(store) {\n    return this.http.post(this.apiUrl, store);\n  }\n  update(store) {\n    return this.http.put(`${this.apiUrl}/${store.id}`, store);\n  }\n  save(store) {\n    return store.id ? this.update(store) : this.add(store);\n  }\n  remove(store) {\n    return this.http.delete(`${this.apiUrl}/${store.id}`);\n  }\n}\n_StoreService = StoreService;\n_StoreService.ɵfac = function StoreService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _StoreService)(i0.ɵɵinject(i1.HttpClient));\n};\n_StoreService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _StoreService,\n  factory: _StoreService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["environment", "StoreService", "constructor", "http", "apiUrl", "baseUrl", "getList", "get", "getById", "storeId", "add", "store", "post", "update", "put", "id", "save", "remove", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\stores\\services\\store.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { Store } from '../models/store.type';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class StoreService {\r\n  private readonly apiUrl = `${environment.baseUrl}/stores`;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getList(): Observable<Store[]> {\r\n    return this.http.get<Store[]>(this.apiUrl);\r\n  }\r\n\r\n  getById(storeId: number): Observable<Store> {\r\n    return this.http.get<Store>(`${this.apiUrl}/${storeId}`);\r\n  }\r\n\r\n  private add(store: Store): Observable<Store> {\r\n    return this.http.post<Store>(this.apiUrl, store);\r\n  }\r\n\r\n  private update(store: Store): Observable<Store> {\r\n    return this.http.put<Store>(`${this.apiUrl}/${store.id}`, store);\r\n  }\r\n\r\n  save(store: Store): Observable<Store> {\r\n    return store.id ? this.update(store) : this.add(store);\r\n  }\r\n\r\n  remove(store: Store): Observable<Store> {\r\n    return this.http.delete<Store>(`${this.apiUrl}/${store.id}`);\r\n  }\r\n}\r\n"], "mappings": ";AAIA,SAASA,WAAW,QAAQ,8BAA8B;;;AAK1D,OAAM,MAAOC,YAAY;EAGvBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,OAAO,SAAS;EAElB;EAEvCC,OAAOA,CAAA;IACL,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAU,IAAI,CAACH,MAAM,CAAC;EAC5C;EAEAI,OAAOA,CAACC,OAAe;IACrB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAQ,GAAG,IAAI,CAACH,MAAM,IAAIK,OAAO,EAAE,CAAC;EAC1D;EAEQC,GAAGA,CAACC,KAAY;IACtB,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAQ,IAAI,CAACR,MAAM,EAAEO,KAAK,CAAC;EAClD;EAEQE,MAAMA,CAACF,KAAY;IACzB,OAAO,IAAI,CAACR,IAAI,CAACW,GAAG,CAAQ,GAAG,IAAI,CAACV,MAAM,IAAIO,KAAK,CAACI,EAAE,EAAE,EAAEJ,KAAK,CAAC;EAClE;EAEAK,IAAIA,CAACL,KAAY;IACf,OAAOA,KAAK,CAACI,EAAE,GAAG,IAAI,CAACF,MAAM,CAACF,KAAK,CAAC,GAAG,IAAI,CAACD,GAAG,CAACC,KAAK,CAAC;EACxD;EAEAM,MAAMA,CAACN,KAAY;IACjB,OAAO,IAAI,CAACR,IAAI,CAACe,MAAM,CAAQ,GAAG,IAAI,CAACd,MAAM,IAAIO,KAAK,CAACI,EAAE,EAAE,CAAC;EAC9D;;gBA3BWd,YAAY;;mCAAZA,aAAY,EAAAkB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAZrB,aAAY;EAAAsB,OAAA,EAAZtB,aAAY,CAAAuB,IAAA;EAAAC,UAAA,EAFX;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}