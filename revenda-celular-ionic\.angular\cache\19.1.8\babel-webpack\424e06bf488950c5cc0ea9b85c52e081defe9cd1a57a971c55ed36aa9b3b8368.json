{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { maskitoDateOptionsGenerator, maskitoNumberOptionsGenerator, maskitoParseDate, maskitoStringifyDate, maskitoStringifyNumber } from \"@maskito/kit\";\nconst dateMask = maskitoDateOptionsGenerator({\n  mode: 'dd/mm/yyyy',\n  separator: '/'\n});\nconst priceMask = maskitoNumberOptionsGenerator({\n  decimalSeparator: '.',\n  min: 0,\n  precision: 2,\n  thousandSeparator: ','\n});\n// Máscara de telefone brasileira simples (sem dependência do @maskito/phone)\nconst phoneMask = {\n  mask: ['(', /\\d/, /\\d/, ')', ' ', /\\d/, /\\d/, /\\d/, /\\d/, /\\d/, '-', /\\d/, /\\d/, /\\d/, /\\d/]\n};\nconst maskitoElement = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (el) {\n    return el.getInputElement();\n  });\n  return function maskitoElement(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst parseDateMask = (date, mode = 'dd/mm/yyyy') => {\n  return maskitoParseDate(date, {\n    mode\n  });\n};\nconst formatDateMask = date => {\n  return maskitoStringifyDate(date, {\n    mode: 'dd/mm/yyyy',\n    separator: '/'\n  });\n};\nconst parseNumberMask = value => {\n  if (!value) return 0;\n  const cleanValue = value.replace(/\\./g, '').replace(',', '.');\n  return parseFloat(cleanValue);\n};\nconst formatNumberMask = value => {\n  return maskitoStringifyNumber(value, {\n    decimalSeparator: ',',\n    precision: 2,\n    thousandSeparator: '.'\n  });\n};\nexport { dateMask, priceMask, phoneMask, maskitoElement, parseDateMask, formatDateMask, parseNumberMask, formatNumberMask };", "map": {"version": 3, "names": ["maskitoDateOptionsGenerator", "maskitoNumberOptionsGenerator", "maskitoParseDate", "maskitoStringifyDate", "maskitoStringifyNumber", "dateMask", "mode", "separator", "priceMask", "decimalSeparator", "min", "precision", "thousandSeparator", "phoneMask", "mask", "maskitoElement", "_ref", "_asyncToGenerator", "el", "getInputElement", "_x", "apply", "arguments", "parseDateMask", "date", "formatDateMask", "parseNumberMask", "value", "cleanValue", "replace", "parseFloat", "formatNumberMask"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\core\\constants\\mask.constants.ts"], "sourcesContent": ["import { MaskitoElementPredicate, MaskitoOptions } from \"@maskito/core\";\r\nimport {\r\n  maskitoDateOptionsGenerator, maskitoNumberOptionsGenerator,\r\n  maskitoParseDate, maskitoStringifyDate,\r\n  maskitoParseNumber, maskitoStringifyNumber,\r\n  MaskitoDateMode,\r\n} from \"@maskito/kit\";\r\n\r\nconst dateMask = maskitoDateOptionsGenerator({ mode: 'dd/mm/yyyy', separator: '/' });\r\nconst priceMask = maskitoNumberOptionsGenerator({\r\n  decimalSeparator: '.',\r\n  min: 0,\r\n  precision: 2,\r\n  thousandSeparator: ','\r\n});\r\n\r\n// Máscara de telefone brasileira simples (sem dependência do @maskito/phone)\r\nconst phoneMask: MaskitoOptions = {\r\n  mask: [\r\n    '(',\r\n    /\\d/,\r\n    /\\d/,\r\n    ')',\r\n    ' ',\r\n    /\\d/,\r\n    /\\d/,\r\n    /\\d/,\r\n    /\\d/,\r\n    /\\d/,\r\n    '-',\r\n    /\\d/,\r\n    /\\d/,\r\n    /\\d/,\r\n    /\\d/\r\n  ]\r\n};\r\n\r\nconst maskitoElement: MaskitoElementPredicate = async (el) =>\r\n  (el as HTMLIonInputElement).getInputElement();\r\n\r\nconst parseDateMask = (date: string, mode: MaskitoDateMode = 'dd/mm/yyyy') => {\r\n  return maskitoParseDate(date, { mode })\r\n}\r\n\r\nconst formatDateMask = (date: Date): string => {\r\n  return maskitoStringifyDate(date, { mode: 'dd/mm/yyyy', separator: '/' });\r\n}\r\n\r\nconst parseNumberMask = (value: string): number => {\r\n  if (!value) return 0;\r\n  const cleanValue = value.replace(/\\./g, '').replace(',', '.');\r\n  return parseFloat(cleanValue);\r\n}\r\n\r\nconst formatNumberMask = (value: number): string => {\r\n  return maskitoStringifyNumber(value, {\r\n    decimalSeparator: ',',\r\n    precision: 2,\r\n    thousandSeparator: '.'\r\n  });\r\n}\r\n\r\nexport {\r\n  dateMask,\r\n  priceMask,\r\n  phoneMask,\r\n  maskitoElement,\r\n  parseDateMask,\r\n  formatDateMask,\r\n  parseNumberMask,\r\n  formatNumberMask\r\n}\r\n"], "mappings": ";AACA,SACEA,2BAA2B,EAAEC,6BAA6B,EAC1DC,gBAAgB,EAAEC,oBAAoB,EAClBC,sBAAsB,QAErC,cAAc;AAErB,MAAMC,QAAQ,GAAGL,2BAA2B,CAAC;EAAEM,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAE;AAAG,CAAE,CAAC;AACpF,MAAMC,SAAS,GAAGP,6BAA6B,CAAC;EAC9CQ,gBAAgB,EAAE,GAAG;EACrBC,GAAG,EAAE,CAAC;EACNC,SAAS,EAAE,CAAC;EACZC,iBAAiB,EAAE;CACpB,CAAC;AAEF;AACA,MAAMC,SAAS,GAAmB;EAChCC,IAAI,EAAE,CACJ,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI;CAEP;AAED,MAAMC,cAAc;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAA4B,WAAOC,EAAE;IAAA,OACtDA,EAA0B,CAACC,eAAe,EAAE;EAAA;EAAA,gBADzCJ,cAAcA,CAAAK,EAAA;IAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;EAAA;AAAA,GAC2B;AAE/C,MAAMC,aAAa,GAAGA,CAACC,IAAY,EAAElB,IAAA,GAAwB,YAAY,KAAI;EAC3E,OAAOJ,gBAAgB,CAACsB,IAAI,EAAE;IAAElB;EAAI,CAAE,CAAC;AACzC,CAAC;AAED,MAAMmB,cAAc,GAAID,IAAU,IAAY;EAC5C,OAAOrB,oBAAoB,CAACqB,IAAI,EAAE;IAAElB,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAE;EAAG,CAAE,CAAC;AAC3E,CAAC;AAED,MAAMmB,eAAe,GAAIC,KAAa,IAAY;EAChD,IAAI,CAACA,KAAK,EAAE,OAAO,CAAC;EACpB,MAAMC,UAAU,GAAGD,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7D,OAAOC,UAAU,CAACF,UAAU,CAAC;AAC/B,CAAC;AAED,MAAMG,gBAAgB,GAAIJ,KAAa,IAAY;EACjD,OAAOvB,sBAAsB,CAACuB,KAAK,EAAE;IACnClB,gBAAgB,EAAE,GAAG;IACrBE,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE;GACpB,CAAC;AACJ,CAAC;AAED,SACEP,QAAQ,EACRG,SAAS,EACTK,SAAS,EACTE,cAAc,EACdQ,aAAa,EACbE,cAAc,EACdC,eAAe,EACfK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}