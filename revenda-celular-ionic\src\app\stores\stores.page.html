<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Lojas</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Lojas</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-list>
    @for(store of storesList; track store.id) {
    <ion-item>
      <ion-text class="store-info">
        <h2>{{ store.name }}</h2>
        <h3><strong>Endereço:</strong> {{ store.address }}, {{ store.city }}/{{ store.state }}</h3>
        <h4><strong>Contato:</strong> {{ store.phone }}</h4>
        <h4><strong>Gerente:</strong> {{ store.manager }}</h4>
        <h4>
          <ion-badge [color]="getStatusColor(store.status)">{{ getStatusLabel(store.status) }}</ion-badge>
          @if(store.isHeadquarters) {
            <ion-badge color="primary">Matriz</ion-badge>
          }
        </h4>
        <ion-button size="small" [routerLink]="['edit', store.id]">
          <ion-icon name="create" slot="start"></ion-icon>
          Editar
        </ion-button>
        <ion-button size="small" (click)="remove(store)">
          <ion-icon name="trash" slot="end"></ion-icon>
          Excluir
        </ion-button>
      </ion-text>
    </ion-item>
    }
    @empty {
    <ion-item>Lista de lojas vazia, cadastre uma nova loja!</ion-item>
    }
  </ion-list>
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button [routerLink]="['new']">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
