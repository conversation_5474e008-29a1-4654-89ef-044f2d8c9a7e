<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Lojas</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Lojas</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-list>
    @for(store of storesList; track store.id) {
    <ion-item>
      <ion-avatar slot="start">
        <div class="store-avatar">
          <ion-icon name="storefront-outline"></ion-icon>
        </div>
      </ion-avatar>
      <ion-label>
        <h2>{{ store.name }}</h2>
        <p><strong>Endereço:</strong> {{ store.address }}, {{ store.city }}/{{ store.state }}</p>
        <p><strong>Contato:</strong> {{ store.phone }}</p>
        <p><strong><PERSON><PERSON><PERSON>:</strong> {{ store.manager }}</p>
        <div class="badges">
          <ion-badge [color]="getStatusColor(store.status)">{{ getStatusLabel(store.status) }}</ion-badge>
          @if(store.isHeadquarters) {
            <ion-badge color="primary">Matriz</ion-badge>
          }
        </div>
      </ion-label>
      <ion-button slot="end" size="small" [routerLink]="['edit', store.id]">
        <ion-icon name="create" slot="icon-only"></ion-icon>
      </ion-button>
      <ion-button slot="end" size="small" color="danger" (click)="remove(store)">
        <ion-icon name="trash" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-item>
    }
    @empty {
    <ion-item>Lista de lojas vazia, cadastre uma nova loja!</ion-item>
    }
  </ion-list>
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button [routerLink]="['new']">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
