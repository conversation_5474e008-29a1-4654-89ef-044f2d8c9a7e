{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst ION_FOCUSED = 'ion-focused';\nconst ION_FOCUSABLE = 'ion-focusable';\nconst FOCUS_KEYS = ['Tab', 'ArrowDown', 'Space', 'Escape', ' ', 'Shift', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'Home', 'End'];\nconst startFocusVisible = rootEl => {\n  let currentFocus = [];\n  let keyboardMode = true;\n  const ref = rootEl ? rootEl.shadowRoot : document;\n  const root = rootEl ? rootEl : document.body;\n  const setFocus = elements => {\n    currentFocus.forEach(el => el.classList.remove(ION_FOCUSED));\n    elements.forEach(el => el.classList.add(ION_FOCUSED));\n    currentFocus = elements;\n  };\n  const pointerDown = () => {\n    keyboardMode = false;\n    setFocus([]);\n  };\n  const onKeydown = ev => {\n    keyboardMode = FOCUS_KEYS.includes(ev.key);\n    if (!keyboardMode) {\n      setFocus([]);\n    }\n  };\n  const onFocusin = ev => {\n    if (keyboardMode && ev.composedPath !== undefined) {\n      const toFocus = ev.composedPath().filter(el => {\n        // TODO(FW-2832): type\n        if (el.classList) {\n          return el.classList.contains(ION_FOCUSABLE);\n        }\n        return false;\n      });\n      setFocus(toFocus);\n    }\n  };\n  const onFocusout = () => {\n    if (ref.activeElement === root) {\n      setFocus([]);\n    }\n  };\n  ref.addEventListener('keydown', onKeydown);\n  ref.addEventListener('focusin', onFocusin);\n  ref.addEventListener('focusout', onFocusout);\n  ref.addEventListener('touchstart', pointerDown, {\n    passive: true\n  });\n  ref.addEventListener('mousedown', pointerDown);\n  const destroy = () => {\n    ref.removeEventListener('keydown', onKeydown);\n    ref.removeEventListener('focusin', onFocusin);\n    ref.removeEventListener('focusout', onFocusout);\n    ref.removeEventListener('touchstart', pointerDown);\n    ref.removeEventListener('mousedown', pointerDown);\n  };\n  return {\n    destroy,\n    setFocus\n  };\n};\nexport { startFocusVisible };", "map": {"version": 3, "names": ["ION_FOCUSED", "ION_FOCUSABLE", "FOCUS_KEYS", "startFocusVisible", "rootEl", "currentFocus", "keyboardMode", "ref", "shadowRoot", "document", "root", "body", "setFocus", "elements", "for<PERSON>ach", "el", "classList", "remove", "add", "pointerDown", "onKeydown", "ev", "includes", "key", "onFocusin", "<PERSON><PERSON><PERSON>", "undefined", "toFocus", "filter", "contains", "onFocusout", "activeElement", "addEventListener", "passive", "destroy", "removeEventListener"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/focus-visible-dd40d69f.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst ION_FOCUSED = 'ion-focused';\nconst ION_FOCUSABLE = 'ion-focusable';\nconst FOCUS_KEYS = [\n    'Tab',\n    'ArrowDown',\n    'Space',\n    'Escape',\n    ' ',\n    'Shift',\n    'Enter',\n    'ArrowLeft',\n    'ArrowRight',\n    'ArrowUp',\n    'Home',\n    'End',\n];\nconst startFocusVisible = (rootEl) => {\n    let currentFocus = [];\n    let keyboardMode = true;\n    const ref = rootEl ? rootEl.shadowRoot : document;\n    const root = rootEl ? rootEl : document.body;\n    const setFocus = (elements) => {\n        currentFocus.forEach((el) => el.classList.remove(ION_FOCUSED));\n        elements.forEach((el) => el.classList.add(ION_FOCUSED));\n        currentFocus = elements;\n    };\n    const pointerDown = () => {\n        keyboardMode = false;\n        setFocus([]);\n    };\n    const onKeydown = (ev) => {\n        keyboardMode = FOCUS_KEYS.includes(ev.key);\n        if (!keyboardMode) {\n            setFocus([]);\n        }\n    };\n    const onFocusin = (ev) => {\n        if (keyboardMode && ev.composedPath !== undefined) {\n            const toFocus = ev.composedPath().filter((el) => {\n                // TODO(FW-2832): type\n                if (el.classList) {\n                    return el.classList.contains(ION_FOCUSABLE);\n                }\n                return false;\n            });\n            setFocus(toFocus);\n        }\n    };\n    const onFocusout = () => {\n        if (ref.activeElement === root) {\n            setFocus([]);\n        }\n    };\n    ref.addEventListener('keydown', onKeydown);\n    ref.addEventListener('focusin', onFocusin);\n    ref.addEventListener('focusout', onFocusout);\n    ref.addEventListener('touchstart', pointerDown, { passive: true });\n    ref.addEventListener('mousedown', pointerDown);\n    const destroy = () => {\n        ref.removeEventListener('keydown', onKeydown);\n        ref.removeEventListener('focusin', onFocusin);\n        ref.removeEventListener('focusout', onFocusout);\n        ref.removeEventListener('touchstart', pointerDown);\n        ref.removeEventListener('mousedown', pointerDown);\n    };\n    return {\n        destroy,\n        setFocus,\n    };\n};\n\nexport { startFocusVisible };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,WAAW,GAAG,aAAa;AACjC,MAAMC,aAAa,GAAG,eAAe;AACrC,MAAMC,UAAU,GAAG,CACf,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,GAAG,EACH,OAAO,EACP,OAAO,EACP,WAAW,EACX,YAAY,EACZ,SAAS,EACT,MAAM,EACN,KAAK,CACR;AACD,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;EAClC,IAAIC,YAAY,GAAG,EAAE;EACrB,IAAIC,YAAY,GAAG,IAAI;EACvB,MAAMC,GAAG,GAAGH,MAAM,GAAGA,MAAM,CAACI,UAAU,GAAGC,QAAQ;EACjD,MAAMC,IAAI,GAAGN,MAAM,GAAGA,MAAM,GAAGK,QAAQ,CAACE,IAAI;EAC5C,MAAMC,QAAQ,GAAIC,QAAQ,IAAK;IAC3BR,YAAY,CAACS,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACC,SAAS,CAACC,MAAM,CAACjB,WAAW,CAAC,CAAC;IAC9Da,QAAQ,CAACC,OAAO,CAAEC,EAAE,IAAKA,EAAE,CAACC,SAAS,CAACE,GAAG,CAAClB,WAAW,CAAC,CAAC;IACvDK,YAAY,GAAGQ,QAAQ;EAC3B,CAAC;EACD,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACtBb,YAAY,GAAG,KAAK;IACpBM,QAAQ,CAAC,EAAE,CAAC;EAChB,CAAC;EACD,MAAMQ,SAAS,GAAIC,EAAE,IAAK;IACtBf,YAAY,GAAGJ,UAAU,CAACoB,QAAQ,CAACD,EAAE,CAACE,GAAG,CAAC;IAC1C,IAAI,CAACjB,YAAY,EAAE;MACfM,QAAQ,CAAC,EAAE,CAAC;IAChB;EACJ,CAAC;EACD,MAAMY,SAAS,GAAIH,EAAE,IAAK;IACtB,IAAIf,YAAY,IAAIe,EAAE,CAACI,YAAY,KAAKC,SAAS,EAAE;MAC/C,MAAMC,OAAO,GAAGN,EAAE,CAACI,YAAY,CAAC,CAAC,CAACG,MAAM,CAAEb,EAAE,IAAK;QAC7C;QACA,IAAIA,EAAE,CAACC,SAAS,EAAE;UACd,OAAOD,EAAE,CAACC,SAAS,CAACa,QAAQ,CAAC5B,aAAa,CAAC;QAC/C;QACA,OAAO,KAAK;MAChB,CAAC,CAAC;MACFW,QAAQ,CAACe,OAAO,CAAC;IACrB;EACJ,CAAC;EACD,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACrB,IAAIvB,GAAG,CAACwB,aAAa,KAAKrB,IAAI,EAAE;MAC5BE,QAAQ,CAAC,EAAE,CAAC;IAChB;EACJ,CAAC;EACDL,GAAG,CAACyB,gBAAgB,CAAC,SAAS,EAAEZ,SAAS,CAAC;EAC1Cb,GAAG,CAACyB,gBAAgB,CAAC,SAAS,EAAER,SAAS,CAAC;EAC1CjB,GAAG,CAACyB,gBAAgB,CAAC,UAAU,EAAEF,UAAU,CAAC;EAC5CvB,GAAG,CAACyB,gBAAgB,CAAC,YAAY,EAAEb,WAAW,EAAE;IAAEc,OAAO,EAAE;EAAK,CAAC,CAAC;EAClE1B,GAAG,CAACyB,gBAAgB,CAAC,WAAW,EAAEb,WAAW,CAAC;EAC9C,MAAMe,OAAO,GAAGA,CAAA,KAAM;IAClB3B,GAAG,CAAC4B,mBAAmB,CAAC,SAAS,EAAEf,SAAS,CAAC;IAC7Cb,GAAG,CAAC4B,mBAAmB,CAAC,SAAS,EAAEX,SAAS,CAAC;IAC7CjB,GAAG,CAAC4B,mBAAmB,CAAC,UAAU,EAAEL,UAAU,CAAC;IAC/CvB,GAAG,CAAC4B,mBAAmB,CAAC,YAAY,EAAEhB,WAAW,CAAC;IAClDZ,GAAG,CAAC4B,mBAAmB,CAAC,WAAW,EAAEhB,WAAW,CAAC;EACrD,CAAC;EACD,OAAO;IACHe,OAAO;IACPtB;EACJ,CAAC;AACL,CAAC;AAED,SAAST,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}