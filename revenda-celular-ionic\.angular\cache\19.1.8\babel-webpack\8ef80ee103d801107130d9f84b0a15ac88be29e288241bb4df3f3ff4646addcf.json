{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { e as readTask, w as writeTask } from './index-28849c61.js';\nimport { f as findClosestIonContent, s as scrollToTop } from './index-5cc724f3.js';\nimport { c as componentOnReady } from './helpers-da915de8.js';\nimport './index-9b0d46f4.js';\nconst startStatusTap = () => {\n  const win = window;\n  win.addEventListener('statusTap', () => {\n    readTask(() => {\n      const width = win.innerWidth;\n      const height = win.innerHeight;\n      const el = document.elementFromPoint(width / 2, height / 2);\n      if (!el) {\n        return;\n      }\n      const contentEl = findClosestIonContent(el);\n      if (contentEl) {\n        new Promise(resolve => componentOnReady(contentEl, resolve)).then(() => {\n          writeTask(/*#__PURE__*/_asyncToGenerator(function* () {\n            /**\n             * If scrolling and user taps status bar,\n             * only calling scrollToTop is not enough\n             * as engines like WebKit will jump the\n             * scroll position back down and complete\n             * any in-progress momentum scrolling.\n             */\n            contentEl.style.setProperty('--overflow', 'hidden');\n            yield scrollToTop(contentEl, 300);\n            contentEl.style.removeProperty('--overflow');\n          }));\n        });\n      }\n    });\n  });\n};\nexport { startStatusTap };", "map": {"version": 3, "names": ["e", "readTask", "w", "writeTask", "f", "findClosestIonContent", "s", "scrollToTop", "c", "componentOnReady", "startStatusTap", "win", "window", "addEventListener", "width", "innerWidth", "height", "innerHeight", "el", "document", "elementFromPoint", "contentEl", "Promise", "resolve", "then", "_asyncToGenerator", "style", "setProperty", "removeProperty"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/status-tap-f472b09f.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { e as readTask, w as writeTask } from './index-28849c61.js';\nimport { f as findClosestIonContent, s as scrollToTop } from './index-5cc724f3.js';\nimport { c as componentOnReady } from './helpers-da915de8.js';\nimport './index-9b0d46f4.js';\n\nconst startStatusTap = () => {\n    const win = window;\n    win.addEventListener('statusTap', () => {\n        readTask(() => {\n            const width = win.innerWidth;\n            const height = win.innerHeight;\n            const el = document.elementFromPoint(width / 2, height / 2);\n            if (!el) {\n                return;\n            }\n            const contentEl = findClosestIonContent(el);\n            if (contentEl) {\n                new Promise((resolve) => componentOnReady(contentEl, resolve)).then(() => {\n                    writeTask(async () => {\n                        /**\n                         * If scrolling and user taps status bar,\n                         * only calling scrollToTop is not enough\n                         * as engines like WebKit will jump the\n                         * scroll position back down and complete\n                         * any in-progress momentum scrolling.\n                         */\n                        contentEl.style.setProperty('--overflow', 'hidden');\n                        await scrollToTop(contentEl, 300);\n                        contentEl.style.removeProperty('--overflow');\n                    });\n                });\n            }\n        });\n    });\n};\n\nexport { startStatusTap };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,SAAS,QAAQ,qBAAqB;AACnE,SAASC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,WAAW,QAAQ,qBAAqB;AAClF,SAASC,CAAC,IAAIC,gBAAgB,QAAQ,uBAAuB;AAC7D,OAAO,qBAAqB;AAE5B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EACzB,MAAMC,GAAG,GAAGC,MAAM;EAClBD,GAAG,CAACE,gBAAgB,CAAC,WAAW,EAAE,MAAM;IACpCZ,QAAQ,CAAC,MAAM;MACX,MAAMa,KAAK,GAAGH,GAAG,CAACI,UAAU;MAC5B,MAAMC,MAAM,GAAGL,GAAG,CAACM,WAAW;MAC9B,MAAMC,EAAE,GAAGC,QAAQ,CAACC,gBAAgB,CAACN,KAAK,GAAG,CAAC,EAAEE,MAAM,GAAG,CAAC,CAAC;MAC3D,IAAI,CAACE,EAAE,EAAE;QACL;MACJ;MACA,MAAMG,SAAS,GAAGhB,qBAAqB,CAACa,EAAE,CAAC;MAC3C,IAAIG,SAAS,EAAE;QACX,IAAIC,OAAO,CAAEC,OAAO,IAAKd,gBAAgB,CAACY,SAAS,EAAEE,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACtErB,SAAS,cAAAsB,iBAAA,CAAC,aAAY;YAClB;AACxB;AACA;AACA;AACA;AACA;AACA;YACwBJ,SAAS,CAACK,KAAK,CAACC,WAAW,CAAC,YAAY,EAAE,QAAQ,CAAC;YACnD,MAAMpB,WAAW,CAACc,SAAS,EAAE,GAAG,CAAC;YACjCA,SAAS,CAACK,KAAK,CAACE,cAAc,CAAC,YAAY,CAAC;UAChD,CAAC,EAAC;QACN,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AAED,SAASlB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}