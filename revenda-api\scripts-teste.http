### 🧪 SCRIPTS DE TESTE - API REST
### Use este arquivo no VS Code com a extensão "REST Client" ou importe no Postman

@baseUrl = http://localhost:3000
@contentType = application/json

### ========================================
### 🏢 TESTES DE MARCAS
### ========================================

### 1. Criar marca válida
POST {{baseUrl}}/brands
Content-Type: {{contentType}}

{
  "name": "Apple",
  "country": "EUA"
}

### 2. Tentar criar marca duplicada (deve falhar)
POST {{baseUrl}}/brands
Content-Type: {{contentType}}

{
  "name": "Apple",
  "country": "EUA"
}

### 3. Listar todas as marcas
GET {{baseUrl}}/brands

### 4. Buscar marca por ID
GET {{baseUrl}}/brands/1

### ========================================
### 👤 TESTES DE CLIENTES
### ========================================

### 1. Criar cliente válido
POST {{baseUrl}}/customers
Content-Type: {{contentType}}

{
  "name": "João Silva",
  "email": "<EMAIL>",
  "phone": "11987654321",
  "birthDate": "1990-05-15",
  "address": "Rua das Flores, 123, Centro",
  "customerType": "regular",
  "active": true
}

### 2. Tentar criar cliente com email duplicado (deve falhar)
POST {{baseUrl}}/customers
Content-Type: {{contentType}}

{
  "name": "Maria Santos",
  "email": "<EMAIL>",
  "phone": "11987654322",
  "birthDate": "1985-03-20",
  "address": "Av. Paulista, 456",
  "customerType": "premium",
  "active": true
}

### 3. Criar cliente VIP
POST {{baseUrl}}/customers
Content-Type: {{contentType}}

{
  "name": "Cliente VIP",
  "email": "<EMAIL>",
  "phone": "11987654323",
  "birthDate": "1980-12-10",
  "address": "Rua Luxo, 789",
  "customerType": "vip",
  "active": true
}

### 4. Tentar fazer downgrade VIP → Regular (deve falhar)
PATCH {{baseUrl}}/customers/2
Content-Type: {{contentType}}

{
  "customerType": "regular"
}

### 5. Listar todos os clientes
GET {{baseUrl}}/customers

### ========================================
### 🏪 TESTES DE LOJAS
### ========================================

### 1. Criar loja válida
POST {{baseUrl}}/stores
Content-Type: {{contentType}}

{
  "name": "Loja Tech Center",
  "address": "Av. Paulista, 1000, Bela Vista",
  "city": "São Paulo",
  "state": "SP",
  "phone": "1133334444",
  "manager": "Maria Santos",
  "isHeadquarters": false,
  "status": "active"
}

### 2. Criar loja matriz
POST {{baseUrl}}/stores
Content-Type: {{contentType}}

{
  "name": "Store Principal",
  "address": "Rua Matriz, 100",
  "city": "São Paulo",
  "state": "SP",
  "phone": "11999887766",
  "manager": "Carlos Silva",
  "isHeadquarters": true,
  "status": "active"
}

### 3. Listar todas as lojas
GET {{baseUrl}}/stores

### 4. Listar apenas lojas ativas
GET {{baseUrl}}/stores/active/list

### ========================================
### 📱 TESTES DE CELULARES
### ========================================

### 1. Criar celular válido
POST {{baseUrl}}/phones
Content-Type: {{contentType}}

{
  "model": "iPhone 15 Pro",
  "image": "https://example.com/iphone15.jpg",
  "releaseDate": "2023-09-15",
  "price": 5999.99,
  "category": "Smartphone",
  "brandId": 1,
  "stock": 50
}

### 2. Tentar criar modelo duplicado na mesma marca (deve falhar)
POST {{baseUrl}}/phones
Content-Type: {{contentType}}

{
  "model": "iPhone 15 Pro",
  "image": "https://example.com/iphone15-2.jpg",
  "releaseDate": "2023-09-15",
  "price": 6999.99,
  "category": "Smartphone",
  "brandId": 1,
  "stock": 30
}

### 3. Tentar criar celular com preço muito baixo (deve falhar)
POST {{baseUrl}}/phones
Content-Type: {{contentType}}

{
  "model": "Celular Básico",
  "image": "https://example.com/basico.jpg",
  "releaseDate": "2023-01-01",
  "price": 5.00,
  "category": "Básico",
  "brandId": 1,
  "stock": 100
}

### 4. Criar celular com estoque limitado
POST {{baseUrl}}/phones
Content-Type: {{contentType}}

{
  "model": "Galaxy S23",
  "image": "https://example.com/galaxy.jpg",
  "releaseDate": "2023-02-01",
  "price": 3999.99,
  "category": "Smartphone",
  "brandId": 1,
  "stock": 5
}

### 5. Listar todos os celulares
GET {{baseUrl}}/phones

### ========================================
### 🔌 TESTES DE ACESSÓRIOS
### ========================================

### 1. Criar acessório válido
POST {{baseUrl}}/accessories
Content-Type: {{contentType}}

{
  "name": "Capa Protetora Premium",
  "description": "Capa de alta qualidade com proteção total",
  "price": 89.90,
  "category": "Capa",
  "image": "https://example.com/capa.jpg",
  "stock": 200,
  "compatiblePhoneIds": [1]
}

### 2. Tentar criar acessório duplicado (deve falhar)
POST {{baseUrl}}/accessories
Content-Type: {{contentType}}

{
  "name": "Capa Protetora Premium",
  "description": "Outra capa similar",
  "price": 99.90,
  "category": "Capa",
  "image": "https://example.com/capa2.jpg",
  "stock": 150,
  "compatiblePhoneIds": [1]
}

### 3. Criar carregador com estoque
POST {{baseUrl}}/accessories
Content-Type: {{contentType}}

{
  "name": "Carregador USB-C Rápido",
  "description": "Carregador de alta velocidade",
  "price": 129.90,
  "category": "Carregador",
  "image": "https://example.com/carregador.jpg",
  "stock": 10,
  "compatiblePhoneIds": [1, 2]
}

### 4. Tentar reduzir estoque além do disponível (deve falhar)
PATCH {{baseUrl}}/accessories/2/stock
Content-Type: {{contentType}}

{
  "quantity": -15
}

### 5. Reduzir estoque corretamente
PATCH {{baseUrl}}/accessories/2/stock
Content-Type: {{contentType}}

{
  "quantity": -3
}

### 6. Listar todos os acessórios
GET {{baseUrl}}/accessories

### 7. Listar acessórios em estoque
GET {{baseUrl}}/accessories/stock/available

### ========================================
### 🧾 TESTES DE VENDAS
### ========================================

### 1. Criar venda válida
POST {{baseUrl}}/sales
Content-Type: {{contentType}}

{
  "customerId": 1,
  "storeId": 1,
  "paymentMethod": "credit_card",
  "status": "pending",
  "seller": "João Vendedor",
  "items": [
    {
      "productId": 1,
      "productType": "phone",
      "quantity": 1,
      "unitPrice": 5999.99,
      "subtotal": 5999.99
    },
    {
      "productId": 1,
      "productType": "accessory",
      "quantity": 2,
      "unitPrice": 89.90,
      "subtotal": 179.80
    }
  ]
}

### 2. Tentar vender quantidade maior que estoque (deve falhar)
POST {{baseUrl}}/sales
Content-Type: {{contentType}}

{
  "customerId": 1,
  "storeId": 1,
  "paymentMethod": "credit_card",
  "status": "pending",
  "seller": "Maria Vendedora",
  "items": [
    {
      "productId": 2,
      "productType": "phone",
      "quantity": 10,
      "unitPrice": 3999.99,
      "subtotal": 39999.90
    }
  ]
}

### 3. Tentar criar venda sem itens (deve falhar)
POST {{baseUrl}}/sales
Content-Type: {{contentType}}

{
  "customerId": 1,
  "storeId": 1,
  "paymentMethod": "credit_card",
  "status": "pending",
  "seller": "Vendedor Teste",
  "items": []
}

### 4. Listar todas as vendas
GET {{baseUrl}}/sales

### 5. Buscar venda por ID
GET {{baseUrl}}/sales/1

### 6. Atualizar status da venda
PATCH {{baseUrl}}/sales/1/status
Content-Type: {{contentType}}

{
  "status": "completed"
}

### 7. Buscar vendas por cliente
GET {{baseUrl}}/sales/customer/1

### 8. Buscar vendas por loja
GET {{baseUrl}}/sales/store/1

### ========================================
### 📊 TESTES DE DASHBOARD
### ========================================

### 1. Estatísticas do dashboard
GET {{baseUrl}}/sales/dashboard/stats

### 2. Vendas por mês
GET {{baseUrl}}/sales/dashboard/monthly

### 3. Produtos mais vendidos
GET {{baseUrl}}/sales/dashboard/top-products

### 4. Vendas recentes
GET {{baseUrl}}/sales/dashboard/recent?limit=5

### ========================================
### 🗑️ TESTES DE EXCLUSÃO (REGRAS DE NEGÓCIO)
### ========================================

### 1. Tentar deletar marca com celulares (deve falhar)
DELETE {{baseUrl}}/brands/1

### 2. Tentar deletar cliente com vendas (deve falhar)
DELETE {{baseUrl}}/customers/1

### 3. Tentar deletar celular em vendas (deve falhar)
DELETE {{baseUrl}}/phones/1

### 4. Deletar acessório sem vendas (deve funcionar)
DELETE {{baseUrl}}/accessories/2
