{"ast": null, "code": "/**\n * @license Angular v19.1.7\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The default equality function used for `signal` and `computed`, which uses referential equality.\n */\nfunction defaultEquals(a, b) {\n  return Object.is(a, b);\n}\n\n/**\n * The currently active consumer `ReactiveNode`, if running code in a reactive context.\n *\n * Change this via `setActiveConsumer`.\n */\nlet activeConsumer = null;\nlet inNotificationPhase = false;\n/**\n * Global epoch counter. Incremented whenever a source signal is set.\n */\nlet epoch = 1;\n/**\n * Symbol used to tell `Signal`s apart from other functions.\n *\n * This can be used to auto-unwrap signals in various cases, or to auto-wrap non-signal values.\n */\nconst SIGNAL = /* @__PURE__ */Symbol('SIGNAL');\nfunction setActiveConsumer(consumer) {\n  const prev = activeConsumer;\n  activeConsumer = consumer;\n  return prev;\n}\nfunction getActiveConsumer() {\n  return activeConsumer;\n}\nfunction isInNotificationPhase() {\n  return inNotificationPhase;\n}\nfunction isReactive(value) {\n  return value[SIGNAL] !== undefined;\n}\nconst REACTIVE_NODE = {\n  version: 0,\n  lastCleanEpoch: 0,\n  dirty: false,\n  producerNode: undefined,\n  producerLastReadVersion: undefined,\n  producerIndexOfThis: undefined,\n  nextProducerIndex: 0,\n  liveConsumerNode: undefined,\n  liveConsumerIndexOfThis: undefined,\n  consumerAllowSignalWrites: false,\n  consumerIsAlwaysLive: false,\n  kind: 'unknown',\n  producerMustRecompute: () => false,\n  producerRecomputeValue: () => {},\n  consumerMarkedDirty: () => {},\n  consumerOnSignalRead: () => {}\n};\n/**\n * Called by implementations when a producer's signal is read.\n */\nfunction producerAccessed(node) {\n  if (inNotificationPhase) {\n    throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ? `Assertion error: signal read during notification phase` : '');\n  }\n  if (activeConsumer === null) {\n    // Accessed outside of a reactive context, so nothing to record.\n    return;\n  }\n  activeConsumer.consumerOnSignalRead(node);\n  // This producer is the `idx`th dependency of `activeConsumer`.\n  const idx = activeConsumer.nextProducerIndex++;\n  assertConsumerNode(activeConsumer);\n  if (idx < activeConsumer.producerNode.length && activeConsumer.producerNode[idx] !== node) {\n    // There's been a change in producers since the last execution of `activeConsumer`.\n    // `activeConsumer.producerNode[idx]` holds a stale dependency which will be be removed and\n    // replaced with `this`.\n    //\n    // If `activeConsumer` isn't live, then this is a no-op, since we can replace the producer in\n    // `activeConsumer.producerNode` directly. However, if `activeConsumer` is live, then we need\n    // to remove it from the stale producer's `liveConsumer`s.\n    if (consumerIsLive(activeConsumer)) {\n      const staleProducer = activeConsumer.producerNode[idx];\n      producerRemoveLiveConsumerAtIndex(staleProducer, activeConsumer.producerIndexOfThis[idx]);\n      // At this point, the only record of `staleProducer` is the reference at\n      // `activeConsumer.producerNode[idx]` which will be overwritten below.\n    }\n  }\n  if (activeConsumer.producerNode[idx] !== node) {\n    // We're a new dependency of the consumer (at `idx`).\n    activeConsumer.producerNode[idx] = node;\n    // If the active consumer is live, then add it as a live consumer. If not, then use 0 as a\n    // placeholder value.\n    activeConsumer.producerIndexOfThis[idx] = consumerIsLive(activeConsumer) ? producerAddLiveConsumer(node, activeConsumer, idx) : 0;\n  }\n  activeConsumer.producerLastReadVersion[idx] = node.version;\n}\n/**\n * Increment the global epoch counter.\n *\n * Called by source producers (that is, not computeds) whenever their values change.\n */\nfunction producerIncrementEpoch() {\n  epoch++;\n}\n/**\n * Ensure this producer's `version` is up-to-date.\n */\nfunction producerUpdateValueVersion(node) {\n  if (consumerIsLive(node) && !node.dirty) {\n    // A live consumer will be marked dirty by producers, so a clean state means that its version\n    // is guaranteed to be up-to-date.\n    return;\n  }\n  if (!node.dirty && node.lastCleanEpoch === epoch) {\n    // Even non-live consumers can skip polling if they previously found themselves to be clean at\n    // the current epoch, since their dependencies could not possibly have changed (such a change\n    // would've increased the epoch).\n    return;\n  }\n  if (!node.producerMustRecompute(node) && !consumerPollProducersForChange(node)) {\n    // None of our producers report a change since the last time they were read, so no\n    // recomputation of our value is necessary, and we can consider ourselves clean.\n    producerMarkClean(node);\n    return;\n  }\n  node.producerRecomputeValue(node);\n  // After recomputing the value, we're no longer dirty.\n  producerMarkClean(node);\n}\n/**\n * Propagate a dirty notification to live consumers of this producer.\n */\nfunction producerNotifyConsumers(node) {\n  if (node.liveConsumerNode === undefined) {\n    return;\n  }\n  // Prevent signal reads when we're updating the graph\n  const prev = inNotificationPhase;\n  inNotificationPhase = true;\n  try {\n    for (const consumer of node.liveConsumerNode) {\n      if (!consumer.dirty) {\n        consumerMarkDirty(consumer);\n      }\n    }\n  } finally {\n    inNotificationPhase = prev;\n  }\n}\n/**\n * Whether this `ReactiveNode` in its producer capacity is currently allowed to initiate updates,\n * based on the current consumer context.\n */\nfunction producerUpdatesAllowed() {\n  var _activeConsumer;\n  return ((_activeConsumer = activeConsumer) === null || _activeConsumer === void 0 ? void 0 : _activeConsumer.consumerAllowSignalWrites) !== false;\n}\nfunction consumerMarkDirty(node) {\n  var _node$consumerMarkedD;\n  node.dirty = true;\n  producerNotifyConsumers(node);\n  (_node$consumerMarkedD = node.consumerMarkedDirty) === null || _node$consumerMarkedD === void 0 || _node$consumerMarkedD.call(node, node);\n}\nfunction producerMarkClean(node) {\n  node.dirty = false;\n  node.lastCleanEpoch = epoch;\n}\n/**\n * Prepare this consumer to run a computation in its reactive context.\n *\n * Must be called by subclasses which represent reactive computations, before those computations\n * begin.\n */\nfunction consumerBeforeComputation(node) {\n  node && (node.nextProducerIndex = 0);\n  return setActiveConsumer(node);\n}\n/**\n * Finalize this consumer's state after a reactive computation has run.\n *\n * Must be called by subclasses which represent reactive computations, after those computations\n * have finished.\n */\nfunction consumerAfterComputation(node, prevConsumer) {\n  setActiveConsumer(prevConsumer);\n  if (!node || node.producerNode === undefined || node.producerIndexOfThis === undefined || node.producerLastReadVersion === undefined) {\n    return;\n  }\n  if (consumerIsLive(node)) {\n    // For live consumers, we need to remove the producer -> consumer edge for any stale producers\n    // which weren't dependencies after the recomputation.\n    for (let i = node.nextProducerIndex; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n  // Truncate the producer tracking arrays.\n  // Perf note: this is essentially truncating the length to `node.nextProducerIndex`, but\n  // benchmarking has shown that individual pop operations are faster.\n  while (node.producerNode.length > node.nextProducerIndex) {\n    node.producerNode.pop();\n    node.producerLastReadVersion.pop();\n    node.producerIndexOfThis.pop();\n  }\n}\n/**\n * Determine whether this consumer has any dependencies which have changed since the last time\n * they were read.\n */\nfunction consumerPollProducersForChange(node) {\n  assertConsumerNode(node);\n  // Poll producers for change.\n  for (let i = 0; i < node.producerNode.length; i++) {\n    const producer = node.producerNode[i];\n    const seenVersion = node.producerLastReadVersion[i];\n    // First check the versions. A mismatch means that the producer's value is known to have\n    // changed since the last time we read it.\n    if (seenVersion !== producer.version) {\n      return true;\n    }\n    // The producer's version is the same as the last time we read it, but it might itself be\n    // stale. Force the producer to recompute its version (calculating a new value if necessary).\n    producerUpdateValueVersion(producer);\n    // Now when we do this check, `producer.version` is guaranteed to be up to date, so if the\n    // versions still match then it has not changed since the last time we read it.\n    if (seenVersion !== producer.version) {\n      return true;\n    }\n  }\n  return false;\n}\n/**\n * Disconnect this consumer from the graph.\n */\nfunction consumerDestroy(node) {\n  assertConsumerNode(node);\n  if (consumerIsLive(node)) {\n    // Drop all connections from the graph to this node.\n    for (let i = 0; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n  // Truncate all the arrays to drop all connection from this node to the graph.\n  node.producerNode.length = node.producerLastReadVersion.length = node.producerIndexOfThis.length = 0;\n  if (node.liveConsumerNode) {\n    node.liveConsumerNode.length = node.liveConsumerIndexOfThis.length = 0;\n  }\n}\n/**\n * Add `consumer` as a live consumer of this node.\n *\n * Note that this operation is potentially transitive. If this node becomes live, then it becomes\n * a live consumer of all of its current producers.\n */\nfunction producerAddLiveConsumer(node, consumer, indexOfThis) {\n  assertProducerNode(node);\n  if (node.liveConsumerNode.length === 0 && isConsumerNode(node)) {\n    // When going from 0 to 1 live consumers, we become a live consumer to our producers.\n    for (let i = 0; i < node.producerNode.length; i++) {\n      node.producerIndexOfThis[i] = producerAddLiveConsumer(node.producerNode[i], node, i);\n    }\n  }\n  node.liveConsumerIndexOfThis.push(indexOfThis);\n  return node.liveConsumerNode.push(consumer) - 1;\n}\n/**\n * Remove the live consumer at `idx`.\n */\nfunction producerRemoveLiveConsumerAtIndex(node, idx) {\n  assertProducerNode(node);\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && idx >= node.liveConsumerNode.length) {\n    throw new Error(`Assertion error: active consumer index ${idx} is out of bounds of ${node.liveConsumerNode.length} consumers)`);\n  }\n  if (node.liveConsumerNode.length === 1 && isConsumerNode(node)) {\n    // When removing the last live consumer, we will no longer be live. We need to remove\n    // ourselves from our producers' tracking (which may cause consumer-producers to lose\n    // liveness as well).\n    for (let i = 0; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n  // Move the last value of `liveConsumers` into `idx`. Note that if there's only a single\n  // live consumer, this is a no-op.\n  const lastIdx = node.liveConsumerNode.length - 1;\n  node.liveConsumerNode[idx] = node.liveConsumerNode[lastIdx];\n  node.liveConsumerIndexOfThis[idx] = node.liveConsumerIndexOfThis[lastIdx];\n  // Truncate the array.\n  node.liveConsumerNode.length--;\n  node.liveConsumerIndexOfThis.length--;\n  // If the index is still valid, then we need to fix the index pointer from the producer to this\n  // consumer, and update it from `lastIdx` to `idx` (accounting for the move above).\n  if (idx < node.liveConsumerNode.length) {\n    const idxProducer = node.liveConsumerIndexOfThis[idx];\n    const consumer = node.liveConsumerNode[idx];\n    assertConsumerNode(consumer);\n    consumer.producerIndexOfThis[idxProducer] = idx;\n  }\n}\nfunction consumerIsLive(node) {\n  var _node$liveConsumerNod, _node$liveConsumerNod2;\n  return node.consumerIsAlwaysLive || ((_node$liveConsumerNod = node === null || node === void 0 || (_node$liveConsumerNod2 = node.liveConsumerNode) === null || _node$liveConsumerNod2 === void 0 ? void 0 : _node$liveConsumerNod2.length) !== null && _node$liveConsumerNod !== void 0 ? _node$liveConsumerNod : 0) > 0;\n}\nfunction assertConsumerNode(node) {\n  var _node$producerNode, _node$producerIndexOf, _node$producerLastRea;\n  (_node$producerNode = node.producerNode) !== null && _node$producerNode !== void 0 ? _node$producerNode : node.producerNode = [];\n  (_node$producerIndexOf = node.producerIndexOfThis) !== null && _node$producerIndexOf !== void 0 ? _node$producerIndexOf : node.producerIndexOfThis = [];\n  (_node$producerLastRea = node.producerLastReadVersion) !== null && _node$producerLastRea !== void 0 ? _node$producerLastRea : node.producerLastReadVersion = [];\n}\nfunction assertProducerNode(node) {\n  var _node$liveConsumerNod3, _node$liveConsumerInd;\n  (_node$liveConsumerNod3 = node.liveConsumerNode) !== null && _node$liveConsumerNod3 !== void 0 ? _node$liveConsumerNod3 : node.liveConsumerNode = [];\n  (_node$liveConsumerInd = node.liveConsumerIndexOfThis) !== null && _node$liveConsumerInd !== void 0 ? _node$liveConsumerInd : node.liveConsumerIndexOfThis = [];\n}\nfunction isConsumerNode(node) {\n  return node.producerNode !== undefined;\n}\n\n/**\n * Create a computed signal which derives a reactive value from an expression.\n */\nfunction createComputed(computation) {\n  const node = Object.create(COMPUTED_NODE);\n  node.computation = computation;\n  const computed = () => {\n    // Check if the value needs updating before returning it.\n    producerUpdateValueVersion(node);\n    // Record that someone looked at this signal.\n    producerAccessed(node);\n    if (node.value === ERRORED) {\n      throw node.error;\n    }\n    return node.value;\n  };\n  computed[SIGNAL] = node;\n  return computed;\n}\n/**\n * A dedicated symbol used before a computed value has been calculated for the first time.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst UNSET = /* @__PURE__ */Symbol('UNSET');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * is in progress. Used to detect cycles in computation chains.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst COMPUTING = /* @__PURE__ */Symbol('COMPUTING');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * failed. The thrown error is cached until the computation gets dirty again.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst ERRORED = /* @__PURE__ */Symbol('ERRORED');\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst COMPUTED_NODE = /* @__PURE__ */(() => {\n  return {\n    ...REACTIVE_NODE,\n    value: UNSET,\n    dirty: true,\n    error: null,\n    equal: defaultEquals,\n    kind: 'computed',\n    producerMustRecompute(node) {\n      // Force a recomputation if there's no current value, or if the current value is in the\n      // process of being calculated (which should throw an error).\n      return node.value === UNSET || node.value === COMPUTING;\n    },\n    producerRecomputeValue(node) {\n      if (node.value === COMPUTING) {\n        // Our computation somehow led to a cyclic read of itself.\n        throw new Error('Detected cycle in computations.');\n      }\n      const oldValue = node.value;\n      node.value = COMPUTING;\n      const prevConsumer = consumerBeforeComputation(node);\n      let newValue;\n      let wasEqual = false;\n      try {\n        newValue = node.computation();\n        // We want to mark this node as errored if calling `equal` throws; however, we don't want\n        // to track any reactive reads inside `equal`.\n        setActiveConsumer(null);\n        wasEqual = oldValue !== UNSET && oldValue !== ERRORED && newValue !== ERRORED && node.equal(oldValue, newValue);\n      } catch (err) {\n        newValue = ERRORED;\n        node.error = err;\n      } finally {\n        consumerAfterComputation(node, prevConsumer);\n      }\n      if (wasEqual) {\n        // No change to `valueVersion` - old and new values are\n        // semantically equivalent.\n        node.value = oldValue;\n        return;\n      }\n      node.value = newValue;\n      node.version++;\n    }\n  };\n})();\nfunction defaultThrowError() {\n  throw new Error();\n}\nlet throwInvalidWriteToSignalErrorFn = defaultThrowError;\nfunction throwInvalidWriteToSignalError() {\n  throwInvalidWriteToSignalErrorFn();\n}\nfunction setThrowInvalidWriteToSignalError(fn) {\n  throwInvalidWriteToSignalErrorFn = fn;\n}\n\n/**\n * If set, called after `WritableSignal`s are updated.\n *\n * This hook can be used to achieve various effects, such as running effects synchronously as part\n * of setting a signal.\n */\nlet postSignalSetFn = null;\n/**\n * Create a `Signal` that can be set or updated directly.\n */\nfunction createSignal(initialValue) {\n  const node = Object.create(SIGNAL_NODE);\n  node.value = initialValue;\n  const getter = () => {\n    producerAccessed(node);\n    return node.value;\n  };\n  getter[SIGNAL] = node;\n  return getter;\n}\nfunction setPostSignalSetFn(fn) {\n  const prev = postSignalSetFn;\n  postSignalSetFn = fn;\n  return prev;\n}\nfunction signalGetFn() {\n  producerAccessed(this);\n  return this.value;\n}\nfunction signalSetFn(node, newValue) {\n  if (!producerUpdatesAllowed()) {\n    throwInvalidWriteToSignalError();\n  }\n  if (!node.equal(node.value, newValue)) {\n    node.value = newValue;\n    signalValueChanged(node);\n  }\n}\nfunction signalUpdateFn(node, updater) {\n  if (!producerUpdatesAllowed()) {\n    throwInvalidWriteToSignalError();\n  }\n  signalSetFn(node, updater(node.value));\n}\nfunction runPostSignalSetFn() {\n  var _postSignalSetFn;\n  (_postSignalSetFn = postSignalSetFn) === null || _postSignalSetFn === void 0 || _postSignalSetFn();\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst SIGNAL_NODE = /* @__PURE__ */(() => {\n  return {\n    ...REACTIVE_NODE,\n    equal: defaultEquals,\n    value: undefined,\n    kind: 'signal'\n  };\n})();\nfunction signalValueChanged(node) {\n  var _postSignalSetFn2;\n  node.version++;\n  producerIncrementEpoch();\n  producerNotifyConsumers(node);\n  (_postSignalSetFn2 = postSignalSetFn) === null || _postSignalSetFn2 === void 0 || _postSignalSetFn2();\n}\nfunction createLinkedSignal(sourceFn, computationFn, equalityFn) {\n  const node = Object.create(LINKED_SIGNAL_NODE);\n  node.source = sourceFn;\n  node.computation = computationFn;\n  if (equalityFn != undefined) {\n    node.equal = equalityFn;\n  }\n  const linkedSignalGetter = () => {\n    // Check if the value needs updating before returning it.\n    producerUpdateValueVersion(node);\n    // Record that someone looked at this signal.\n    producerAccessed(node);\n    if (node.value === ERRORED) {\n      throw node.error;\n    }\n    return node.value;\n  };\n  const getter = linkedSignalGetter;\n  getter[SIGNAL] = node;\n  return getter;\n}\nfunction linkedSignalSetFn(node, newValue) {\n  producerUpdateValueVersion(node);\n  signalSetFn(node, newValue);\n  producerMarkClean(node);\n}\nfunction linkedSignalUpdateFn(node, updater) {\n  producerUpdateValueVersion(node);\n  signalUpdateFn(node, updater);\n  producerMarkClean(node);\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `LINKED_SIGNAL_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst LINKED_SIGNAL_NODE = /* @__PURE__ */(() => {\n  return {\n    ...REACTIVE_NODE,\n    value: UNSET,\n    dirty: true,\n    error: null,\n    equal: defaultEquals,\n    producerMustRecompute(node) {\n      // Force a recomputation if there's no current value, or if the current value is in the\n      // process of being calculated (which should throw an error).\n      return node.value === UNSET || node.value === COMPUTING;\n    },\n    producerRecomputeValue(node) {\n      if (node.value === COMPUTING) {\n        // Our computation somehow led to a cyclic read of itself.\n        throw new Error('Detected cycle in computations.');\n      }\n      const oldValue = node.value;\n      node.value = COMPUTING;\n      const prevConsumer = consumerBeforeComputation(node);\n      let newValue;\n      try {\n        const newSourceValue = node.source();\n        const prev = oldValue === UNSET || oldValue === ERRORED ? undefined : {\n          source: node.sourceValue,\n          value: oldValue\n        };\n        newValue = node.computation(newSourceValue, prev);\n        node.sourceValue = newSourceValue;\n      } catch (err) {\n        newValue = ERRORED;\n        node.error = err;\n      } finally {\n        consumerAfterComputation(node, prevConsumer);\n      }\n      if (oldValue !== UNSET && newValue !== ERRORED && node.equal(oldValue, newValue)) {\n        // No change to `valueVersion` - old and new values are\n        // semantically equivalent.\n        node.value = oldValue;\n        return;\n      }\n      node.value = newValue;\n      node.version++;\n    }\n  };\n})();\nfunction createWatch(fn, schedule, allowSignalWrites) {\n  const node = Object.create(WATCH_NODE);\n  if (allowSignalWrites) {\n    node.consumerAllowSignalWrites = true;\n  }\n  node.fn = fn;\n  node.schedule = schedule;\n  const registerOnCleanup = cleanupFn => {\n    node.cleanupFn = cleanupFn;\n  };\n  function isWatchNodeDestroyed(node) {\n    return node.fn === null && node.schedule === null;\n  }\n  function destroyWatchNode(node) {\n    if (!isWatchNodeDestroyed(node)) {\n      consumerDestroy(node); // disconnect watcher from the reactive graph\n      node.cleanupFn();\n      // nullify references to the integration functions to mark node as destroyed\n      node.fn = null;\n      node.schedule = null;\n      node.cleanupFn = NOOP_CLEANUP_FN;\n    }\n  }\n  const run = () => {\n    if (node.fn === null) {\n      // trying to run a destroyed watch is noop\n      return;\n    }\n    if (isInNotificationPhase()) {\n      throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n    }\n    node.dirty = false;\n    if (node.hasRun && !consumerPollProducersForChange(node)) {\n      return;\n    }\n    node.hasRun = true;\n    const prevConsumer = consumerBeforeComputation(node);\n    try {\n      node.cleanupFn();\n      node.cleanupFn = NOOP_CLEANUP_FN;\n      node.fn(registerOnCleanup);\n    } finally {\n      consumerAfterComputation(node, prevConsumer);\n    }\n  };\n  node.ref = {\n    notify: () => consumerMarkDirty(node),\n    run,\n    cleanup: () => node.cleanupFn(),\n    destroy: () => destroyWatchNode(node),\n    [SIGNAL]: node\n  };\n  return node.ref;\n}\nconst NOOP_CLEANUP_FN = () => {};\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst WATCH_NODE = /* @__PURE__ */(() => {\n  return {\n    ...REACTIVE_NODE,\n    consumerIsAlwaysLive: true,\n    consumerAllowSignalWrites: false,\n    consumerMarkedDirty: node => {\n      if (node.schedule !== null) {\n        node.schedule(node.ref);\n      }\n    },\n    hasRun: false,\n    cleanupFn: NOOP_CLEANUP_FN\n  };\n})();\nfunction setAlternateWeakRefImpl(impl) {\n  // TODO: remove this function\n}\nexport { REACTIVE_NODE, SIGNAL, SIGNAL_NODE, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, createComputed, createLinkedSignal, createSignal, createWatch, defaultEquals, getActiveConsumer, isInNotificationPhase, isReactive, linkedSignalSetFn, linkedSignalUpdateFn, producerAccessed, producerIncrementEpoch, producerMarkClean, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, runPostSignalSetFn, setActiveConsumer, setAlternateWeakRefImpl, setPostSignalSetFn, setThrowInvalidWriteToSignalError, signalSetFn, signalUpdateFn };", "map": {"version": 3, "names": ["defaultEquals", "a", "b", "Object", "is", "activeConsumer", "inNotificationPhase", "epoch", "SIGNAL", "Symbol", "setActiveConsumer", "consumer", "prev", "getActiveConsumer", "isInNotificationPhase", "isReactive", "value", "undefined", "REACTIVE_NODE", "version", "lastCleanEpoch", "dirty", "producerNode", "producerLastReadVersion", "producerIndexOfThis", "nextProducerIndex", "liveConsumerNode", "liveConsumerIndexOfThis", "consumerAllowSignalWrites", "consumerIsAlwaysLive", "kind", "producerMustRecompute", "producerRecomputeValue", "consumerMarkedDirty", "consumerOnSignalRead", "producerAccessed", "node", "Error", "ngDevMode", "idx", "assertConsumerNode", "length", "consumerIsLive", "staleProducer", "producerRemoveLiveConsumerAtIndex", "producerAddLiveConsumer", "producerIncrementEpoch", "producerUpdateValueVersion", "consumerPollProducersForChange", "producerMark<PERSON><PERSON>", "producerNotifyConsumers", "consumerMarkDirty", "producer<PERSON><PERSON>datesAllowed", "_activeConsumer", "_node$consumerMarkedD", "call", "consumerBeforeComputation", "consumerAfterComputation", "prevConsumer", "i", "pop", "producer", "seenVersion", "consumerDestroy", "indexOfThis", "assertProducerNode", "isConsumerNode", "push", "lastIdx", "idxProducer", "_node$liveConsumerNod", "_node$liveConsumerNod2", "_node$producerNode", "_node$producerIndexOf", "_node$producerLastRea", "_node$liveConsumerNod3", "_node$liveConsumerInd", "createComputed", "computation", "create", "COMPUTED_NODE", "computed", "ERRORED", "error", "UNSET", "COMPUTING", "equal", "oldValue", "newValue", "wasEqual", "err", "defaultThrowError", "throwInvalidWriteToSignalErrorFn", "throwInvalidWriteToSignalError", "setThrowInvalidWriteToSignalError", "fn", "postSignalSetFn", "createSignal", "initialValue", "SIGNAL_NODE", "getter", "setPostSignalSetFn", "signalGetFn", "signalSetFn", "signalValueChanged", "signalUpdateFn", "updater", "runPostSignalSetFn", "_postSignalSetFn", "_postSignalSetFn2", "createLinkedSignal", "sourceFn", "computationFn", "equalityFn", "LINKED_SIGNAL_NODE", "source", "linkedSignalGetter", "linkedSignalSetFn", "linkedSignalUpdateFn", "newSourceValue", "sourceValue", "createWatch", "schedule", "allowSignalWrites", "WATCH_NODE", "registerOnCleanup", "cleanupFn", "isWatchNodeDestroyed", "destroyWatchNode", "NOOP_CLEANUP_FN", "run", "<PERSON><PERSON>un", "ref", "notify", "cleanup", "destroy", "setAlternateWeakRefImpl", "impl"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@angular/core/fesm2022/primitives/signals.mjs"], "sourcesContent": ["/**\n * @license Angular v19.1.7\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The default equality function used for `signal` and `computed`, which uses referential equality.\n */\nfunction defaultEquals(a, b) {\n    return Object.is(a, b);\n}\n\n/**\n * The currently active consumer `ReactiveNode`, if running code in a reactive context.\n *\n * Change this via `setActiveConsumer`.\n */\nlet activeConsumer = null;\nlet inNotificationPhase = false;\n/**\n * Global epoch counter. Incremented whenever a source signal is set.\n */\nlet epoch = 1;\n/**\n * Symbol used to tell `Signal`s apart from other functions.\n *\n * This can be used to auto-unwrap signals in various cases, or to auto-wrap non-signal values.\n */\nconst SIGNAL = /* @__PURE__ */ Symbol('SIGNAL');\nfunction setActiveConsumer(consumer) {\n    const prev = activeConsumer;\n    activeConsumer = consumer;\n    return prev;\n}\nfunction getActiveConsumer() {\n    return activeConsumer;\n}\nfunction isInNotificationPhase() {\n    return inNotificationPhase;\n}\nfunction isReactive(value) {\n    return value[SIGNAL] !== undefined;\n}\nconst REACTIVE_NODE = {\n    version: 0,\n    lastCleanEpoch: 0,\n    dirty: false,\n    producerNode: undefined,\n    producerLastReadVersion: undefined,\n    producerIndexOfThis: undefined,\n    nextProducerIndex: 0,\n    liveConsumerNode: undefined,\n    liveConsumerIndexOfThis: undefined,\n    consumerAllowSignalWrites: false,\n    consumerIsAlwaysLive: false,\n    kind: 'unknown',\n    producerMustRecompute: () => false,\n    producerRecomputeValue: () => { },\n    consumerMarkedDirty: () => { },\n    consumerOnSignalRead: () => { },\n};\n/**\n * Called by implementations when a producer's signal is read.\n */\nfunction producerAccessed(node) {\n    if (inNotificationPhase) {\n        throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode\n            ? `Assertion error: signal read during notification phase`\n            : '');\n    }\n    if (activeConsumer === null) {\n        // Accessed outside of a reactive context, so nothing to record.\n        return;\n    }\n    activeConsumer.consumerOnSignalRead(node);\n    // This producer is the `idx`th dependency of `activeConsumer`.\n    const idx = activeConsumer.nextProducerIndex++;\n    assertConsumerNode(activeConsumer);\n    if (idx < activeConsumer.producerNode.length && activeConsumer.producerNode[idx] !== node) {\n        // There's been a change in producers since the last execution of `activeConsumer`.\n        // `activeConsumer.producerNode[idx]` holds a stale dependency which will be be removed and\n        // replaced with `this`.\n        //\n        // If `activeConsumer` isn't live, then this is a no-op, since we can replace the producer in\n        // `activeConsumer.producerNode` directly. However, if `activeConsumer` is live, then we need\n        // to remove it from the stale producer's `liveConsumer`s.\n        if (consumerIsLive(activeConsumer)) {\n            const staleProducer = activeConsumer.producerNode[idx];\n            producerRemoveLiveConsumerAtIndex(staleProducer, activeConsumer.producerIndexOfThis[idx]);\n            // At this point, the only record of `staleProducer` is the reference at\n            // `activeConsumer.producerNode[idx]` which will be overwritten below.\n        }\n    }\n    if (activeConsumer.producerNode[idx] !== node) {\n        // We're a new dependency of the consumer (at `idx`).\n        activeConsumer.producerNode[idx] = node;\n        // If the active consumer is live, then add it as a live consumer. If not, then use 0 as a\n        // placeholder value.\n        activeConsumer.producerIndexOfThis[idx] = consumerIsLive(activeConsumer)\n            ? producerAddLiveConsumer(node, activeConsumer, idx)\n            : 0;\n    }\n    activeConsumer.producerLastReadVersion[idx] = node.version;\n}\n/**\n * Increment the global epoch counter.\n *\n * Called by source producers (that is, not computeds) whenever their values change.\n */\nfunction producerIncrementEpoch() {\n    epoch++;\n}\n/**\n * Ensure this producer's `version` is up-to-date.\n */\nfunction producerUpdateValueVersion(node) {\n    if (consumerIsLive(node) && !node.dirty) {\n        // A live consumer will be marked dirty by producers, so a clean state means that its version\n        // is guaranteed to be up-to-date.\n        return;\n    }\n    if (!node.dirty && node.lastCleanEpoch === epoch) {\n        // Even non-live consumers can skip polling if they previously found themselves to be clean at\n        // the current epoch, since their dependencies could not possibly have changed (such a change\n        // would've increased the epoch).\n        return;\n    }\n    if (!node.producerMustRecompute(node) && !consumerPollProducersForChange(node)) {\n        // None of our producers report a change since the last time they were read, so no\n        // recomputation of our value is necessary, and we can consider ourselves clean.\n        producerMarkClean(node);\n        return;\n    }\n    node.producerRecomputeValue(node);\n    // After recomputing the value, we're no longer dirty.\n    producerMarkClean(node);\n}\n/**\n * Propagate a dirty notification to live consumers of this producer.\n */\nfunction producerNotifyConsumers(node) {\n    if (node.liveConsumerNode === undefined) {\n        return;\n    }\n    // Prevent signal reads when we're updating the graph\n    const prev = inNotificationPhase;\n    inNotificationPhase = true;\n    try {\n        for (const consumer of node.liveConsumerNode) {\n            if (!consumer.dirty) {\n                consumerMarkDirty(consumer);\n            }\n        }\n    }\n    finally {\n        inNotificationPhase = prev;\n    }\n}\n/**\n * Whether this `ReactiveNode` in its producer capacity is currently allowed to initiate updates,\n * based on the current consumer context.\n */\nfunction producerUpdatesAllowed() {\n    return activeConsumer?.consumerAllowSignalWrites !== false;\n}\nfunction consumerMarkDirty(node) {\n    node.dirty = true;\n    producerNotifyConsumers(node);\n    node.consumerMarkedDirty?.(node);\n}\nfunction producerMarkClean(node) {\n    node.dirty = false;\n    node.lastCleanEpoch = epoch;\n}\n/**\n * Prepare this consumer to run a computation in its reactive context.\n *\n * Must be called by subclasses which represent reactive computations, before those computations\n * begin.\n */\nfunction consumerBeforeComputation(node) {\n    node && (node.nextProducerIndex = 0);\n    return setActiveConsumer(node);\n}\n/**\n * Finalize this consumer's state after a reactive computation has run.\n *\n * Must be called by subclasses which represent reactive computations, after those computations\n * have finished.\n */\nfunction consumerAfterComputation(node, prevConsumer) {\n    setActiveConsumer(prevConsumer);\n    if (!node ||\n        node.producerNode === undefined ||\n        node.producerIndexOfThis === undefined ||\n        node.producerLastReadVersion === undefined) {\n        return;\n    }\n    if (consumerIsLive(node)) {\n        // For live consumers, we need to remove the producer -> consumer edge for any stale producers\n        // which weren't dependencies after the recomputation.\n        for (let i = node.nextProducerIndex; i < node.producerNode.length; i++) {\n            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n        }\n    }\n    // Truncate the producer tracking arrays.\n    // Perf note: this is essentially truncating the length to `node.nextProducerIndex`, but\n    // benchmarking has shown that individual pop operations are faster.\n    while (node.producerNode.length > node.nextProducerIndex) {\n        node.producerNode.pop();\n        node.producerLastReadVersion.pop();\n        node.producerIndexOfThis.pop();\n    }\n}\n/**\n * Determine whether this consumer has any dependencies which have changed since the last time\n * they were read.\n */\nfunction consumerPollProducersForChange(node) {\n    assertConsumerNode(node);\n    // Poll producers for change.\n    for (let i = 0; i < node.producerNode.length; i++) {\n        const producer = node.producerNode[i];\n        const seenVersion = node.producerLastReadVersion[i];\n        // First check the versions. A mismatch means that the producer's value is known to have\n        // changed since the last time we read it.\n        if (seenVersion !== producer.version) {\n            return true;\n        }\n        // The producer's version is the same as the last time we read it, but it might itself be\n        // stale. Force the producer to recompute its version (calculating a new value if necessary).\n        producerUpdateValueVersion(producer);\n        // Now when we do this check, `producer.version` is guaranteed to be up to date, so if the\n        // versions still match then it has not changed since the last time we read it.\n        if (seenVersion !== producer.version) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * Disconnect this consumer from the graph.\n */\nfunction consumerDestroy(node) {\n    assertConsumerNode(node);\n    if (consumerIsLive(node)) {\n        // Drop all connections from the graph to this node.\n        for (let i = 0; i < node.producerNode.length; i++) {\n            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n        }\n    }\n    // Truncate all the arrays to drop all connection from this node to the graph.\n    node.producerNode.length =\n        node.producerLastReadVersion.length =\n            node.producerIndexOfThis.length =\n                0;\n    if (node.liveConsumerNode) {\n        node.liveConsumerNode.length = node.liveConsumerIndexOfThis.length = 0;\n    }\n}\n/**\n * Add `consumer` as a live consumer of this node.\n *\n * Note that this operation is potentially transitive. If this node becomes live, then it becomes\n * a live consumer of all of its current producers.\n */\nfunction producerAddLiveConsumer(node, consumer, indexOfThis) {\n    assertProducerNode(node);\n    if (node.liveConsumerNode.length === 0 && isConsumerNode(node)) {\n        // When going from 0 to 1 live consumers, we become a live consumer to our producers.\n        for (let i = 0; i < node.producerNode.length; i++) {\n            node.producerIndexOfThis[i] = producerAddLiveConsumer(node.producerNode[i], node, i);\n        }\n    }\n    node.liveConsumerIndexOfThis.push(indexOfThis);\n    return node.liveConsumerNode.push(consumer) - 1;\n}\n/**\n * Remove the live consumer at `idx`.\n */\nfunction producerRemoveLiveConsumerAtIndex(node, idx) {\n    assertProducerNode(node);\n    if (typeof ngDevMode !== 'undefined' && ngDevMode && idx >= node.liveConsumerNode.length) {\n        throw new Error(`Assertion error: active consumer index ${idx} is out of bounds of ${node.liveConsumerNode.length} consumers)`);\n    }\n    if (node.liveConsumerNode.length === 1 && isConsumerNode(node)) {\n        // When removing the last live consumer, we will no longer be live. We need to remove\n        // ourselves from our producers' tracking (which may cause consumer-producers to lose\n        // liveness as well).\n        for (let i = 0; i < node.producerNode.length; i++) {\n            producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n        }\n    }\n    // Move the last value of `liveConsumers` into `idx`. Note that if there's only a single\n    // live consumer, this is a no-op.\n    const lastIdx = node.liveConsumerNode.length - 1;\n    node.liveConsumerNode[idx] = node.liveConsumerNode[lastIdx];\n    node.liveConsumerIndexOfThis[idx] = node.liveConsumerIndexOfThis[lastIdx];\n    // Truncate the array.\n    node.liveConsumerNode.length--;\n    node.liveConsumerIndexOfThis.length--;\n    // If the index is still valid, then we need to fix the index pointer from the producer to this\n    // consumer, and update it from `lastIdx` to `idx` (accounting for the move above).\n    if (idx < node.liveConsumerNode.length) {\n        const idxProducer = node.liveConsumerIndexOfThis[idx];\n        const consumer = node.liveConsumerNode[idx];\n        assertConsumerNode(consumer);\n        consumer.producerIndexOfThis[idxProducer] = idx;\n    }\n}\nfunction consumerIsLive(node) {\n    return node.consumerIsAlwaysLive || (node?.liveConsumerNode?.length ?? 0) > 0;\n}\nfunction assertConsumerNode(node) {\n    node.producerNode ??= [];\n    node.producerIndexOfThis ??= [];\n    node.producerLastReadVersion ??= [];\n}\nfunction assertProducerNode(node) {\n    node.liveConsumerNode ??= [];\n    node.liveConsumerIndexOfThis ??= [];\n}\nfunction isConsumerNode(node) {\n    return node.producerNode !== undefined;\n}\n\n/**\n * Create a computed signal which derives a reactive value from an expression.\n */\nfunction createComputed(computation) {\n    const node = Object.create(COMPUTED_NODE);\n    node.computation = computation;\n    const computed = () => {\n        // Check if the value needs updating before returning it.\n        producerUpdateValueVersion(node);\n        // Record that someone looked at this signal.\n        producerAccessed(node);\n        if (node.value === ERRORED) {\n            throw node.error;\n        }\n        return node.value;\n    };\n    computed[SIGNAL] = node;\n    return computed;\n}\n/**\n * A dedicated symbol used before a computed value has been calculated for the first time.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst UNSET = /* @__PURE__ */ Symbol('UNSET');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * is in progress. Used to detect cycles in computation chains.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst COMPUTING = /* @__PURE__ */ Symbol('COMPUTING');\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * failed. The thrown error is cached until the computation gets dirty again.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst ERRORED = /* @__PURE__ */ Symbol('ERRORED');\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst COMPUTED_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        value: UNSET,\n        dirty: true,\n        error: null,\n        equal: defaultEquals,\n        kind: 'computed',\n        producerMustRecompute(node) {\n            // Force a recomputation if there's no current value, or if the current value is in the\n            // process of being calculated (which should throw an error).\n            return node.value === UNSET || node.value === COMPUTING;\n        },\n        producerRecomputeValue(node) {\n            if (node.value === COMPUTING) {\n                // Our computation somehow led to a cyclic read of itself.\n                throw new Error('Detected cycle in computations.');\n            }\n            const oldValue = node.value;\n            node.value = COMPUTING;\n            const prevConsumer = consumerBeforeComputation(node);\n            let newValue;\n            let wasEqual = false;\n            try {\n                newValue = node.computation();\n                // We want to mark this node as errored if calling `equal` throws; however, we don't want\n                // to track any reactive reads inside `equal`.\n                setActiveConsumer(null);\n                wasEqual =\n                    oldValue !== UNSET &&\n                        oldValue !== ERRORED &&\n                        newValue !== ERRORED &&\n                        node.equal(oldValue, newValue);\n            }\n            catch (err) {\n                newValue = ERRORED;\n                node.error = err;\n            }\n            finally {\n                consumerAfterComputation(node, prevConsumer);\n            }\n            if (wasEqual) {\n                // No change to `valueVersion` - old and new values are\n                // semantically equivalent.\n                node.value = oldValue;\n                return;\n            }\n            node.value = newValue;\n            node.version++;\n        },\n    };\n})();\n\nfunction defaultThrowError() {\n    throw new Error();\n}\nlet throwInvalidWriteToSignalErrorFn = defaultThrowError;\nfunction throwInvalidWriteToSignalError() {\n    throwInvalidWriteToSignalErrorFn();\n}\nfunction setThrowInvalidWriteToSignalError(fn) {\n    throwInvalidWriteToSignalErrorFn = fn;\n}\n\n/**\n * If set, called after `WritableSignal`s are updated.\n *\n * This hook can be used to achieve various effects, such as running effects synchronously as part\n * of setting a signal.\n */\nlet postSignalSetFn = null;\n/**\n * Create a `Signal` that can be set or updated directly.\n */\nfunction createSignal(initialValue) {\n    const node = Object.create(SIGNAL_NODE);\n    node.value = initialValue;\n    const getter = (() => {\n        producerAccessed(node);\n        return node.value;\n    });\n    getter[SIGNAL] = node;\n    return getter;\n}\nfunction setPostSignalSetFn(fn) {\n    const prev = postSignalSetFn;\n    postSignalSetFn = fn;\n    return prev;\n}\nfunction signalGetFn() {\n    producerAccessed(this);\n    return this.value;\n}\nfunction signalSetFn(node, newValue) {\n    if (!producerUpdatesAllowed()) {\n        throwInvalidWriteToSignalError();\n    }\n    if (!node.equal(node.value, newValue)) {\n        node.value = newValue;\n        signalValueChanged(node);\n    }\n}\nfunction signalUpdateFn(node, updater) {\n    if (!producerUpdatesAllowed()) {\n        throwInvalidWriteToSignalError();\n    }\n    signalSetFn(node, updater(node.value));\n}\nfunction runPostSignalSetFn() {\n    postSignalSetFn?.();\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst SIGNAL_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        equal: defaultEquals,\n        value: undefined,\n        kind: 'signal',\n    };\n})();\nfunction signalValueChanged(node) {\n    node.version++;\n    producerIncrementEpoch();\n    producerNotifyConsumers(node);\n    postSignalSetFn?.();\n}\n\nfunction createLinkedSignal(sourceFn, computationFn, equalityFn) {\n    const node = Object.create(LINKED_SIGNAL_NODE);\n    node.source = sourceFn;\n    node.computation = computationFn;\n    if (equalityFn != undefined) {\n        node.equal = equalityFn;\n    }\n    const linkedSignalGetter = () => {\n        // Check if the value needs updating before returning it.\n        producerUpdateValueVersion(node);\n        // Record that someone looked at this signal.\n        producerAccessed(node);\n        if (node.value === ERRORED) {\n            throw node.error;\n        }\n        return node.value;\n    };\n    const getter = linkedSignalGetter;\n    getter[SIGNAL] = node;\n    return getter;\n}\nfunction linkedSignalSetFn(node, newValue) {\n    producerUpdateValueVersion(node);\n    signalSetFn(node, newValue);\n    producerMarkClean(node);\n}\nfunction linkedSignalUpdateFn(node, updater) {\n    producerUpdateValueVersion(node);\n    signalUpdateFn(node, updater);\n    producerMarkClean(node);\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `LINKED_SIGNAL_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst LINKED_SIGNAL_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        value: UNSET,\n        dirty: true,\n        error: null,\n        equal: defaultEquals,\n        producerMustRecompute(node) {\n            // Force a recomputation if there's no current value, or if the current value is in the\n            // process of being calculated (which should throw an error).\n            return node.value === UNSET || node.value === COMPUTING;\n        },\n        producerRecomputeValue(node) {\n            if (node.value === COMPUTING) {\n                // Our computation somehow led to a cyclic read of itself.\n                throw new Error('Detected cycle in computations.');\n            }\n            const oldValue = node.value;\n            node.value = COMPUTING;\n            const prevConsumer = consumerBeforeComputation(node);\n            let newValue;\n            try {\n                const newSourceValue = node.source();\n                const prev = oldValue === UNSET || oldValue === ERRORED\n                    ? undefined\n                    : {\n                        source: node.sourceValue,\n                        value: oldValue,\n                    };\n                newValue = node.computation(newSourceValue, prev);\n                node.sourceValue = newSourceValue;\n            }\n            catch (err) {\n                newValue = ERRORED;\n                node.error = err;\n            }\n            finally {\n                consumerAfterComputation(node, prevConsumer);\n            }\n            if (oldValue !== UNSET && newValue !== ERRORED && node.equal(oldValue, newValue)) {\n                // No change to `valueVersion` - old and new values are\n                // semantically equivalent.\n                node.value = oldValue;\n                return;\n            }\n            node.value = newValue;\n            node.version++;\n        },\n    };\n})();\n\nfunction createWatch(fn, schedule, allowSignalWrites) {\n    const node = Object.create(WATCH_NODE);\n    if (allowSignalWrites) {\n        node.consumerAllowSignalWrites = true;\n    }\n    node.fn = fn;\n    node.schedule = schedule;\n    const registerOnCleanup = (cleanupFn) => {\n        node.cleanupFn = cleanupFn;\n    };\n    function isWatchNodeDestroyed(node) {\n        return node.fn === null && node.schedule === null;\n    }\n    function destroyWatchNode(node) {\n        if (!isWatchNodeDestroyed(node)) {\n            consumerDestroy(node); // disconnect watcher from the reactive graph\n            node.cleanupFn();\n            // nullify references to the integration functions to mark node as destroyed\n            node.fn = null;\n            node.schedule = null;\n            node.cleanupFn = NOOP_CLEANUP_FN;\n        }\n    }\n    const run = () => {\n        if (node.fn === null) {\n            // trying to run a destroyed watch is noop\n            return;\n        }\n        if (isInNotificationPhase()) {\n            throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n        }\n        node.dirty = false;\n        if (node.hasRun && !consumerPollProducersForChange(node)) {\n            return;\n        }\n        node.hasRun = true;\n        const prevConsumer = consumerBeforeComputation(node);\n        try {\n            node.cleanupFn();\n            node.cleanupFn = NOOP_CLEANUP_FN;\n            node.fn(registerOnCleanup);\n        }\n        finally {\n            consumerAfterComputation(node, prevConsumer);\n        }\n    };\n    node.ref = {\n        notify: () => consumerMarkDirty(node),\n        run,\n        cleanup: () => node.cleanupFn(),\n        destroy: () => destroyWatchNode(node),\n        [SIGNAL]: node,\n    };\n    return node.ref;\n}\nconst NOOP_CLEANUP_FN = () => { };\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst WATCH_NODE = /* @__PURE__ */ (() => {\n    return {\n        ...REACTIVE_NODE,\n        consumerIsAlwaysLive: true,\n        consumerAllowSignalWrites: false,\n        consumerMarkedDirty: (node) => {\n            if (node.schedule !== null) {\n                node.schedule(node.ref);\n            }\n        },\n        hasRun: false,\n        cleanupFn: NOOP_CLEANUP_FN,\n    };\n})();\n\nfunction setAlternateWeakRefImpl(impl) {\n    // TODO: remove this function\n}\n\nexport { REACTIVE_NODE, SIGNAL, SIGNAL_NODE, consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, createComputed, createLinkedSignal, createSignal, createWatch, defaultEquals, getActiveConsumer, isInNotificationPhase, isReactive, linkedSignalSetFn, linkedSignalUpdateFn, producerAccessed, producerIncrementEpoch, producerMarkClean, producerNotifyConsumers, producerUpdateValueVersion, producerUpdatesAllowed, runPostSignalSetFn, setActiveConsumer, setAlternateWeakRefImpl, setPostSignalSetFn, setThrowInvalidWriteToSignalError, signalSetFn, signalUpdateFn };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAASA,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOC,MAAM,CAACC,EAAE,CAACH,CAAC,EAAEC,CAAC,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIG,cAAc,GAAG,IAAI;AACzB,IAAIC,mBAAmB,GAAG,KAAK;AAC/B;AACA;AACA;AACA,IAAIC,KAAK,GAAG,CAAC;AACb;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,eAAgBC,MAAM,CAAC,QAAQ,CAAC;AAC/C,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EACjC,MAAMC,IAAI,GAAGP,cAAc;EAC3BA,cAAc,GAAGM,QAAQ;EACzB,OAAOC,IAAI;AACf;AACA,SAASC,iBAAiBA,CAAA,EAAG;EACzB,OAAOR,cAAc;AACzB;AACA,SAASS,qBAAqBA,CAAA,EAAG;EAC7B,OAAOR,mBAAmB;AAC9B;AACA,SAASS,UAAUA,CAACC,KAAK,EAAE;EACvB,OAAOA,KAAK,CAACR,MAAM,CAAC,KAAKS,SAAS;AACtC;AACA,MAAMC,aAAa,GAAG;EAClBC,OAAO,EAAE,CAAC;EACVC,cAAc,EAAE,CAAC;EACjBC,KAAK,EAAE,KAAK;EACZC,YAAY,EAAEL,SAAS;EACvBM,uBAAuB,EAAEN,SAAS;EAClCO,mBAAmB,EAAEP,SAAS;EAC9BQ,iBAAiB,EAAE,CAAC;EACpBC,gBAAgB,EAAET,SAAS;EAC3BU,uBAAuB,EAAEV,SAAS;EAClCW,yBAAyB,EAAE,KAAK;EAChCC,oBAAoB,EAAE,KAAK;EAC3BC,IAAI,EAAE,SAAS;EACfC,qBAAqB,EAAEA,CAAA,KAAM,KAAK;EAClCC,sBAAsB,EAAEA,CAAA,KAAM,CAAE,CAAC;EACjCC,mBAAmB,EAAEA,CAAA,KAAM,CAAE,CAAC;EAC9BC,oBAAoB,EAAEA,CAAA,KAAM,CAAE;AAClC,CAAC;AACD;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC5B,IAAI9B,mBAAmB,EAAE;IACrB,MAAM,IAAI+B,KAAK,CAAC,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvD,wDAAwD,GACxD,EAAE,CAAC;EACb;EACA,IAAIjC,cAAc,KAAK,IAAI,EAAE;IACzB;IACA;EACJ;EACAA,cAAc,CAAC6B,oBAAoB,CAACE,IAAI,CAAC;EACzC;EACA,MAAMG,GAAG,GAAGlC,cAAc,CAACoB,iBAAiB,EAAE;EAC9Ce,kBAAkB,CAACnC,cAAc,CAAC;EAClC,IAAIkC,GAAG,GAAGlC,cAAc,CAACiB,YAAY,CAACmB,MAAM,IAAIpC,cAAc,CAACiB,YAAY,CAACiB,GAAG,CAAC,KAAKH,IAAI,EAAE;IACvF;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIM,cAAc,CAACrC,cAAc,CAAC,EAAE;MAChC,MAAMsC,aAAa,GAAGtC,cAAc,CAACiB,YAAY,CAACiB,GAAG,CAAC;MACtDK,iCAAiC,CAACD,aAAa,EAAEtC,cAAc,CAACmB,mBAAmB,CAACe,GAAG,CAAC,CAAC;MACzF;MACA;IACJ;EACJ;EACA,IAAIlC,cAAc,CAACiB,YAAY,CAACiB,GAAG,CAAC,KAAKH,IAAI,EAAE;IAC3C;IACA/B,cAAc,CAACiB,YAAY,CAACiB,GAAG,CAAC,GAAGH,IAAI;IACvC;IACA;IACA/B,cAAc,CAACmB,mBAAmB,CAACe,GAAG,CAAC,GAAGG,cAAc,CAACrC,cAAc,CAAC,GAClEwC,uBAAuB,CAACT,IAAI,EAAE/B,cAAc,EAAEkC,GAAG,CAAC,GAClD,CAAC;EACX;EACAlC,cAAc,CAACkB,uBAAuB,CAACgB,GAAG,CAAC,GAAGH,IAAI,CAACjB,OAAO;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2B,sBAAsBA,CAAA,EAAG;EAC9BvC,KAAK,EAAE;AACX;AACA;AACA;AACA;AACA,SAASwC,0BAA0BA,CAACX,IAAI,EAAE;EACtC,IAAIM,cAAc,CAACN,IAAI,CAAC,IAAI,CAACA,IAAI,CAACf,KAAK,EAAE;IACrC;IACA;IACA;EACJ;EACA,IAAI,CAACe,IAAI,CAACf,KAAK,IAAIe,IAAI,CAAChB,cAAc,KAAKb,KAAK,EAAE;IAC9C;IACA;IACA;IACA;EACJ;EACA,IAAI,CAAC6B,IAAI,CAACL,qBAAqB,CAACK,IAAI,CAAC,IAAI,CAACY,8BAA8B,CAACZ,IAAI,CAAC,EAAE;IAC5E;IACA;IACAa,iBAAiB,CAACb,IAAI,CAAC;IACvB;EACJ;EACAA,IAAI,CAACJ,sBAAsB,CAACI,IAAI,CAAC;EACjC;EACAa,iBAAiB,CAACb,IAAI,CAAC;AAC3B;AACA;AACA;AACA;AACA,SAASc,uBAAuBA,CAACd,IAAI,EAAE;EACnC,IAAIA,IAAI,CAACV,gBAAgB,KAAKT,SAAS,EAAE;IACrC;EACJ;EACA;EACA,MAAML,IAAI,GAAGN,mBAAmB;EAChCA,mBAAmB,GAAG,IAAI;EAC1B,IAAI;IACA,KAAK,MAAMK,QAAQ,IAAIyB,IAAI,CAACV,gBAAgB,EAAE;MAC1C,IAAI,CAACf,QAAQ,CAACU,KAAK,EAAE;QACjB8B,iBAAiB,CAACxC,QAAQ,CAAC;MAC/B;IACJ;EACJ,CAAC,SACO;IACJL,mBAAmB,GAAGM,IAAI;EAC9B;AACJ;AACA;AACA;AACA;AACA;AACA,SAASwC,sBAAsBA,CAAA,EAAG;EAAA,IAAAC,eAAA;EAC9B,OAAO,EAAAA,eAAA,GAAAhD,cAAc,cAAAgD,eAAA,uBAAdA,eAAA,CAAgBzB,yBAAyB,MAAK,KAAK;AAC9D;AACA,SAASuB,iBAAiBA,CAACf,IAAI,EAAE;EAAA,IAAAkB,qBAAA;EAC7BlB,IAAI,CAACf,KAAK,GAAG,IAAI;EACjB6B,uBAAuB,CAACd,IAAI,CAAC;EAC7B,CAAAkB,qBAAA,GAAAlB,IAAI,CAACH,mBAAmB,cAAAqB,qBAAA,eAAxBA,qBAAA,CAAAC,IAAA,CAAAnB,IAAI,EAAuBA,IAAI,CAAC;AACpC;AACA,SAASa,iBAAiBA,CAACb,IAAI,EAAE;EAC7BA,IAAI,CAACf,KAAK,GAAG,KAAK;EAClBe,IAAI,CAAChB,cAAc,GAAGb,KAAK;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiD,yBAAyBA,CAACpB,IAAI,EAAE;EACrCA,IAAI,KAAKA,IAAI,CAACX,iBAAiB,GAAG,CAAC,CAAC;EACpC,OAAOf,iBAAiB,CAAC0B,IAAI,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,wBAAwBA,CAACrB,IAAI,EAAEsB,YAAY,EAAE;EAClDhD,iBAAiB,CAACgD,YAAY,CAAC;EAC/B,IAAI,CAACtB,IAAI,IACLA,IAAI,CAACd,YAAY,KAAKL,SAAS,IAC/BmB,IAAI,CAACZ,mBAAmB,KAAKP,SAAS,IACtCmB,IAAI,CAACb,uBAAuB,KAAKN,SAAS,EAAE;IAC5C;EACJ;EACA,IAAIyB,cAAc,CAACN,IAAI,CAAC,EAAE;IACtB;IACA;IACA,KAAK,IAAIuB,CAAC,GAAGvB,IAAI,CAACX,iBAAiB,EAAEkC,CAAC,GAAGvB,IAAI,CAACd,YAAY,CAACmB,MAAM,EAAEkB,CAAC,EAAE,EAAE;MACpEf,iCAAiC,CAACR,IAAI,CAACd,YAAY,CAACqC,CAAC,CAAC,EAAEvB,IAAI,CAACZ,mBAAmB,CAACmC,CAAC,CAAC,CAAC;IACxF;EACJ;EACA;EACA;EACA;EACA,OAAOvB,IAAI,CAACd,YAAY,CAACmB,MAAM,GAAGL,IAAI,CAACX,iBAAiB,EAAE;IACtDW,IAAI,CAACd,YAAY,CAACsC,GAAG,CAAC,CAAC;IACvBxB,IAAI,CAACb,uBAAuB,CAACqC,GAAG,CAAC,CAAC;IAClCxB,IAAI,CAACZ,mBAAmB,CAACoC,GAAG,CAAC,CAAC;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA,SAASZ,8BAA8BA,CAACZ,IAAI,EAAE;EAC1CI,kBAAkB,CAACJ,IAAI,CAAC;EACxB;EACA,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,IAAI,CAACd,YAAY,CAACmB,MAAM,EAAEkB,CAAC,EAAE,EAAE;IAC/C,MAAME,QAAQ,GAAGzB,IAAI,CAACd,YAAY,CAACqC,CAAC,CAAC;IACrC,MAAMG,WAAW,GAAG1B,IAAI,CAACb,uBAAuB,CAACoC,CAAC,CAAC;IACnD;IACA;IACA,IAAIG,WAAW,KAAKD,QAAQ,CAAC1C,OAAO,EAAE;MAClC,OAAO,IAAI;IACf;IACA;IACA;IACA4B,0BAA0B,CAACc,QAAQ,CAAC;IACpC;IACA;IACA,IAAIC,WAAW,KAAKD,QAAQ,CAAC1C,OAAO,EAAE;MAClC,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA,SAAS4C,eAAeA,CAAC3B,IAAI,EAAE;EAC3BI,kBAAkB,CAACJ,IAAI,CAAC;EACxB,IAAIM,cAAc,CAACN,IAAI,CAAC,EAAE;IACtB;IACA,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,IAAI,CAACd,YAAY,CAACmB,MAAM,EAAEkB,CAAC,EAAE,EAAE;MAC/Cf,iCAAiC,CAACR,IAAI,CAACd,YAAY,CAACqC,CAAC,CAAC,EAAEvB,IAAI,CAACZ,mBAAmB,CAACmC,CAAC,CAAC,CAAC;IACxF;EACJ;EACA;EACAvB,IAAI,CAACd,YAAY,CAACmB,MAAM,GACpBL,IAAI,CAACb,uBAAuB,CAACkB,MAAM,GAC/BL,IAAI,CAACZ,mBAAmB,CAACiB,MAAM,GAC3B,CAAC;EACb,IAAIL,IAAI,CAACV,gBAAgB,EAAE;IACvBU,IAAI,CAACV,gBAAgB,CAACe,MAAM,GAAGL,IAAI,CAACT,uBAAuB,CAACc,MAAM,GAAG,CAAC;EAC1E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,uBAAuBA,CAACT,IAAI,EAAEzB,QAAQ,EAAEqD,WAAW,EAAE;EAC1DC,kBAAkB,CAAC7B,IAAI,CAAC;EACxB,IAAIA,IAAI,CAACV,gBAAgB,CAACe,MAAM,KAAK,CAAC,IAAIyB,cAAc,CAAC9B,IAAI,CAAC,EAAE;IAC5D;IACA,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,IAAI,CAACd,YAAY,CAACmB,MAAM,EAAEkB,CAAC,EAAE,EAAE;MAC/CvB,IAAI,CAACZ,mBAAmB,CAACmC,CAAC,CAAC,GAAGd,uBAAuB,CAACT,IAAI,CAACd,YAAY,CAACqC,CAAC,CAAC,EAAEvB,IAAI,EAAEuB,CAAC,CAAC;IACxF;EACJ;EACAvB,IAAI,CAACT,uBAAuB,CAACwC,IAAI,CAACH,WAAW,CAAC;EAC9C,OAAO5B,IAAI,CAACV,gBAAgB,CAACyC,IAAI,CAACxD,QAAQ,CAAC,GAAG,CAAC;AACnD;AACA;AACA;AACA;AACA,SAASiC,iCAAiCA,CAACR,IAAI,EAAEG,GAAG,EAAE;EAClD0B,kBAAkB,CAAC7B,IAAI,CAAC;EACxB,IAAI,OAAOE,SAAS,KAAK,WAAW,IAAIA,SAAS,IAAIC,GAAG,IAAIH,IAAI,CAACV,gBAAgB,CAACe,MAAM,EAAE;IACtF,MAAM,IAAIJ,KAAK,CAAC,0CAA0CE,GAAG,wBAAwBH,IAAI,CAACV,gBAAgB,CAACe,MAAM,aAAa,CAAC;EACnI;EACA,IAAIL,IAAI,CAACV,gBAAgB,CAACe,MAAM,KAAK,CAAC,IAAIyB,cAAc,CAAC9B,IAAI,CAAC,EAAE;IAC5D;IACA;IACA;IACA,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,IAAI,CAACd,YAAY,CAACmB,MAAM,EAAEkB,CAAC,EAAE,EAAE;MAC/Cf,iCAAiC,CAACR,IAAI,CAACd,YAAY,CAACqC,CAAC,CAAC,EAAEvB,IAAI,CAACZ,mBAAmB,CAACmC,CAAC,CAAC,CAAC;IACxF;EACJ;EACA;EACA;EACA,MAAMS,OAAO,GAAGhC,IAAI,CAACV,gBAAgB,CAACe,MAAM,GAAG,CAAC;EAChDL,IAAI,CAACV,gBAAgB,CAACa,GAAG,CAAC,GAAGH,IAAI,CAACV,gBAAgB,CAAC0C,OAAO,CAAC;EAC3DhC,IAAI,CAACT,uBAAuB,CAACY,GAAG,CAAC,GAAGH,IAAI,CAACT,uBAAuB,CAACyC,OAAO,CAAC;EACzE;EACAhC,IAAI,CAACV,gBAAgB,CAACe,MAAM,EAAE;EAC9BL,IAAI,CAACT,uBAAuB,CAACc,MAAM,EAAE;EACrC;EACA;EACA,IAAIF,GAAG,GAAGH,IAAI,CAACV,gBAAgB,CAACe,MAAM,EAAE;IACpC,MAAM4B,WAAW,GAAGjC,IAAI,CAACT,uBAAuB,CAACY,GAAG,CAAC;IACrD,MAAM5B,QAAQ,GAAGyB,IAAI,CAACV,gBAAgB,CAACa,GAAG,CAAC;IAC3CC,kBAAkB,CAAC7B,QAAQ,CAAC;IAC5BA,QAAQ,CAACa,mBAAmB,CAAC6C,WAAW,CAAC,GAAG9B,GAAG;EACnD;AACJ;AACA,SAASG,cAAcA,CAACN,IAAI,EAAE;EAAA,IAAAkC,qBAAA,EAAAC,sBAAA;EAC1B,OAAOnC,IAAI,CAACP,oBAAoB,IAAI,EAAAyC,qBAAA,GAAClC,IAAI,aAAJA,IAAI,gBAAAmC,sBAAA,GAAJnC,IAAI,CAAEV,gBAAgB,cAAA6C,sBAAA,uBAAtBA,sBAAA,CAAwB9B,MAAM,cAAA6B,qBAAA,cAAAA,qBAAA,GAAI,CAAC,IAAI,CAAC;AACjF;AACA,SAAS9B,kBAAkBA,CAACJ,IAAI,EAAE;EAAA,IAAAoC,kBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC9B,CAAAF,kBAAA,GAAApC,IAAI,CAACd,YAAY,cAAAkD,kBAAA,cAAAA,kBAAA,GAAjBpC,IAAI,CAACd,YAAY,GAAK,EAAE;EACxB,CAAAmD,qBAAA,GAAArC,IAAI,CAACZ,mBAAmB,cAAAiD,qBAAA,cAAAA,qBAAA,GAAxBrC,IAAI,CAACZ,mBAAmB,GAAK,EAAE;EAC/B,CAAAkD,qBAAA,GAAAtC,IAAI,CAACb,uBAAuB,cAAAmD,qBAAA,cAAAA,qBAAA,GAA5BtC,IAAI,CAACb,uBAAuB,GAAK,EAAE;AACvC;AACA,SAAS0C,kBAAkBA,CAAC7B,IAAI,EAAE;EAAA,IAAAuC,sBAAA,EAAAC,qBAAA;EAC9B,CAAAD,sBAAA,GAAAvC,IAAI,CAACV,gBAAgB,cAAAiD,sBAAA,cAAAA,sBAAA,GAArBvC,IAAI,CAACV,gBAAgB,GAAK,EAAE;EAC5B,CAAAkD,qBAAA,GAAAxC,IAAI,CAACT,uBAAuB,cAAAiD,qBAAA,cAAAA,qBAAA,GAA5BxC,IAAI,CAACT,uBAAuB,GAAK,EAAE;AACvC;AACA,SAASuC,cAAcA,CAAC9B,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACd,YAAY,KAAKL,SAAS;AAC1C;;AAEA;AACA;AACA;AACA,SAAS4D,cAAcA,CAACC,WAAW,EAAE;EACjC,MAAM1C,IAAI,GAAGjC,MAAM,CAAC4E,MAAM,CAACC,aAAa,CAAC;EACzC5C,IAAI,CAAC0C,WAAW,GAAGA,WAAW;EAC9B,MAAMG,QAAQ,GAAGA,CAAA,KAAM;IACnB;IACAlC,0BAA0B,CAACX,IAAI,CAAC;IAChC;IACAD,gBAAgB,CAACC,IAAI,CAAC;IACtB,IAAIA,IAAI,CAACpB,KAAK,KAAKkE,OAAO,EAAE;MACxB,MAAM9C,IAAI,CAAC+C,KAAK;IACpB;IACA,OAAO/C,IAAI,CAACpB,KAAK;EACrB,CAAC;EACDiE,QAAQ,CAACzE,MAAM,CAAC,GAAG4B,IAAI;EACvB,OAAO6C,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA,MAAMG,KAAK,GAAG,eAAgB3E,MAAM,CAAC,OAAO,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA,MAAM4E,SAAS,GAAG,eAAgB5E,MAAM,CAAC,WAAW,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA,MAAMyE,OAAO,GAAG,eAAgBzE,MAAM,CAAC,SAAS,CAAC;AACjD;AACA;AACA;AACA,MAAMuE,aAAa,GAAG,eAAgB,CAAC,MAAM;EACzC,OAAO;IACH,GAAG9D,aAAa;IAChBF,KAAK,EAAEoE,KAAK;IACZ/D,KAAK,EAAE,IAAI;IACX8D,KAAK,EAAE,IAAI;IACXG,KAAK,EAAEtF,aAAa;IACpB8B,IAAI,EAAE,UAAU;IAChBC,qBAAqBA,CAACK,IAAI,EAAE;MACxB;MACA;MACA,OAAOA,IAAI,CAACpB,KAAK,KAAKoE,KAAK,IAAIhD,IAAI,CAACpB,KAAK,KAAKqE,SAAS;IAC3D,CAAC;IACDrD,sBAAsBA,CAACI,IAAI,EAAE;MACzB,IAAIA,IAAI,CAACpB,KAAK,KAAKqE,SAAS,EAAE;QAC1B;QACA,MAAM,IAAIhD,KAAK,CAAC,iCAAiC,CAAC;MACtD;MACA,MAAMkD,QAAQ,GAAGnD,IAAI,CAACpB,KAAK;MAC3BoB,IAAI,CAACpB,KAAK,GAAGqE,SAAS;MACtB,MAAM3B,YAAY,GAAGF,yBAAyB,CAACpB,IAAI,CAAC;MACpD,IAAIoD,QAAQ;MACZ,IAAIC,QAAQ,GAAG,KAAK;MACpB,IAAI;QACAD,QAAQ,GAAGpD,IAAI,CAAC0C,WAAW,CAAC,CAAC;QAC7B;QACA;QACApE,iBAAiB,CAAC,IAAI,CAAC;QACvB+E,QAAQ,GACJF,QAAQ,KAAKH,KAAK,IACdG,QAAQ,KAAKL,OAAO,IACpBM,QAAQ,KAAKN,OAAO,IACpB9C,IAAI,CAACkD,KAAK,CAACC,QAAQ,EAAEC,QAAQ,CAAC;MAC1C,CAAC,CACD,OAAOE,GAAG,EAAE;QACRF,QAAQ,GAAGN,OAAO;QAClB9C,IAAI,CAAC+C,KAAK,GAAGO,GAAG;MACpB,CAAC,SACO;QACJjC,wBAAwB,CAACrB,IAAI,EAAEsB,YAAY,CAAC;MAChD;MACA,IAAI+B,QAAQ,EAAE;QACV;QACA;QACArD,IAAI,CAACpB,KAAK,GAAGuE,QAAQ;QACrB;MACJ;MACAnD,IAAI,CAACpB,KAAK,GAAGwE,QAAQ;MACrBpD,IAAI,CAACjB,OAAO,EAAE;IAClB;EACJ,CAAC;AACL,CAAC,EAAE,CAAC;AAEJ,SAASwE,iBAAiBA,CAAA,EAAG;EACzB,MAAM,IAAItD,KAAK,CAAC,CAAC;AACrB;AACA,IAAIuD,gCAAgC,GAAGD,iBAAiB;AACxD,SAASE,8BAA8BA,CAAA,EAAG;EACtCD,gCAAgC,CAAC,CAAC;AACtC;AACA,SAASE,iCAAiCA,CAACC,EAAE,EAAE;EAC3CH,gCAAgC,GAAGG,EAAE;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,IAAI;AAC1B;AACA;AACA;AACA,SAASC,YAAYA,CAACC,YAAY,EAAE;EAChC,MAAM9D,IAAI,GAAGjC,MAAM,CAAC4E,MAAM,CAACoB,WAAW,CAAC;EACvC/D,IAAI,CAACpB,KAAK,GAAGkF,YAAY;EACzB,MAAME,MAAM,GAAIA,CAAA,KAAM;IAClBjE,gBAAgB,CAACC,IAAI,CAAC;IACtB,OAAOA,IAAI,CAACpB,KAAK;EACrB,CAAE;EACFoF,MAAM,CAAC5F,MAAM,CAAC,GAAG4B,IAAI;EACrB,OAAOgE,MAAM;AACjB;AACA,SAASC,kBAAkBA,CAACN,EAAE,EAAE;EAC5B,MAAMnF,IAAI,GAAGoF,eAAe;EAC5BA,eAAe,GAAGD,EAAE;EACpB,OAAOnF,IAAI;AACf;AACA,SAAS0F,WAAWA,CAAA,EAAG;EACnBnE,gBAAgB,CAAC,IAAI,CAAC;EACtB,OAAO,IAAI,CAACnB,KAAK;AACrB;AACA,SAASuF,WAAWA,CAACnE,IAAI,EAAEoD,QAAQ,EAAE;EACjC,IAAI,CAACpC,sBAAsB,CAAC,CAAC,EAAE;IAC3ByC,8BAA8B,CAAC,CAAC;EACpC;EACA,IAAI,CAACzD,IAAI,CAACkD,KAAK,CAAClD,IAAI,CAACpB,KAAK,EAAEwE,QAAQ,CAAC,EAAE;IACnCpD,IAAI,CAACpB,KAAK,GAAGwE,QAAQ;IACrBgB,kBAAkB,CAACpE,IAAI,CAAC;EAC5B;AACJ;AACA,SAASqE,cAAcA,CAACrE,IAAI,EAAEsE,OAAO,EAAE;EACnC,IAAI,CAACtD,sBAAsB,CAAC,CAAC,EAAE;IAC3ByC,8BAA8B,CAAC,CAAC;EACpC;EACAU,WAAW,CAACnE,IAAI,EAAEsE,OAAO,CAACtE,IAAI,CAACpB,KAAK,CAAC,CAAC;AAC1C;AACA,SAAS2F,kBAAkBA,CAAA,EAAG;EAAA,IAAAC,gBAAA;EAC1B,CAAAA,gBAAA,GAAAZ,eAAe,cAAAY,gBAAA,eAAfA,gBAAA,CAAkB,CAAC;AACvB;AACA;AACA;AACA;AACA,MAAMT,WAAW,GAAG,eAAgB,CAAC,MAAM;EACvC,OAAO;IACH,GAAGjF,aAAa;IAChBoE,KAAK,EAAEtF,aAAa;IACpBgB,KAAK,EAAEC,SAAS;IAChBa,IAAI,EAAE;EACV,CAAC;AACL,CAAC,EAAE,CAAC;AACJ,SAAS0E,kBAAkBA,CAACpE,IAAI,EAAE;EAAA,IAAAyE,iBAAA;EAC9BzE,IAAI,CAACjB,OAAO,EAAE;EACd2B,sBAAsB,CAAC,CAAC;EACxBI,uBAAuB,CAACd,IAAI,CAAC;EAC7B,CAAAyE,iBAAA,GAAAb,eAAe,cAAAa,iBAAA,eAAfA,iBAAA,CAAkB,CAAC;AACvB;AAEA,SAASC,kBAAkBA,CAACC,QAAQ,EAAEC,aAAa,EAAEC,UAAU,EAAE;EAC7D,MAAM7E,IAAI,GAAGjC,MAAM,CAAC4E,MAAM,CAACmC,kBAAkB,CAAC;EAC9C9E,IAAI,CAAC+E,MAAM,GAAGJ,QAAQ;EACtB3E,IAAI,CAAC0C,WAAW,GAAGkC,aAAa;EAChC,IAAIC,UAAU,IAAIhG,SAAS,EAAE;IACzBmB,IAAI,CAACkD,KAAK,GAAG2B,UAAU;EAC3B;EACA,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC7B;IACArE,0BAA0B,CAACX,IAAI,CAAC;IAChC;IACAD,gBAAgB,CAACC,IAAI,CAAC;IACtB,IAAIA,IAAI,CAACpB,KAAK,KAAKkE,OAAO,EAAE;MACxB,MAAM9C,IAAI,CAAC+C,KAAK;IACpB;IACA,OAAO/C,IAAI,CAACpB,KAAK;EACrB,CAAC;EACD,MAAMoF,MAAM,GAAGgB,kBAAkB;EACjChB,MAAM,CAAC5F,MAAM,CAAC,GAAG4B,IAAI;EACrB,OAAOgE,MAAM;AACjB;AACA,SAASiB,iBAAiBA,CAACjF,IAAI,EAAEoD,QAAQ,EAAE;EACvCzC,0BAA0B,CAACX,IAAI,CAAC;EAChCmE,WAAW,CAACnE,IAAI,EAAEoD,QAAQ,CAAC;EAC3BvC,iBAAiB,CAACb,IAAI,CAAC;AAC3B;AACA,SAASkF,oBAAoBA,CAAClF,IAAI,EAAEsE,OAAO,EAAE;EACzC3D,0BAA0B,CAACX,IAAI,CAAC;EAChCqE,cAAc,CAACrE,IAAI,EAAEsE,OAAO,CAAC;EAC7BzD,iBAAiB,CAACb,IAAI,CAAC;AAC3B;AACA;AACA;AACA;AACA,MAAM8E,kBAAkB,GAAG,eAAgB,CAAC,MAAM;EAC9C,OAAO;IACH,GAAGhG,aAAa;IAChBF,KAAK,EAAEoE,KAAK;IACZ/D,KAAK,EAAE,IAAI;IACX8D,KAAK,EAAE,IAAI;IACXG,KAAK,EAAEtF,aAAa;IACpB+B,qBAAqBA,CAACK,IAAI,EAAE;MACxB;MACA;MACA,OAAOA,IAAI,CAACpB,KAAK,KAAKoE,KAAK,IAAIhD,IAAI,CAACpB,KAAK,KAAKqE,SAAS;IAC3D,CAAC;IACDrD,sBAAsBA,CAACI,IAAI,EAAE;MACzB,IAAIA,IAAI,CAACpB,KAAK,KAAKqE,SAAS,EAAE;QAC1B;QACA,MAAM,IAAIhD,KAAK,CAAC,iCAAiC,CAAC;MACtD;MACA,MAAMkD,QAAQ,GAAGnD,IAAI,CAACpB,KAAK;MAC3BoB,IAAI,CAACpB,KAAK,GAAGqE,SAAS;MACtB,MAAM3B,YAAY,GAAGF,yBAAyB,CAACpB,IAAI,CAAC;MACpD,IAAIoD,QAAQ;MACZ,IAAI;QACA,MAAM+B,cAAc,GAAGnF,IAAI,CAAC+E,MAAM,CAAC,CAAC;QACpC,MAAMvG,IAAI,GAAG2E,QAAQ,KAAKH,KAAK,IAAIG,QAAQ,KAAKL,OAAO,GACjDjE,SAAS,GACT;UACEkG,MAAM,EAAE/E,IAAI,CAACoF,WAAW;UACxBxG,KAAK,EAAEuE;QACX,CAAC;QACLC,QAAQ,GAAGpD,IAAI,CAAC0C,WAAW,CAACyC,cAAc,EAAE3G,IAAI,CAAC;QACjDwB,IAAI,CAACoF,WAAW,GAAGD,cAAc;MACrC,CAAC,CACD,OAAO7B,GAAG,EAAE;QACRF,QAAQ,GAAGN,OAAO;QAClB9C,IAAI,CAAC+C,KAAK,GAAGO,GAAG;MACpB,CAAC,SACO;QACJjC,wBAAwB,CAACrB,IAAI,EAAEsB,YAAY,CAAC;MAChD;MACA,IAAI6B,QAAQ,KAAKH,KAAK,IAAII,QAAQ,KAAKN,OAAO,IAAI9C,IAAI,CAACkD,KAAK,CAACC,QAAQ,EAAEC,QAAQ,CAAC,EAAE;QAC9E;QACA;QACApD,IAAI,CAACpB,KAAK,GAAGuE,QAAQ;QACrB;MACJ;MACAnD,IAAI,CAACpB,KAAK,GAAGwE,QAAQ;MACrBpD,IAAI,CAACjB,OAAO,EAAE;IAClB;EACJ,CAAC;AACL,CAAC,EAAE,CAAC;AAEJ,SAASsG,WAAWA,CAAC1B,EAAE,EAAE2B,QAAQ,EAAEC,iBAAiB,EAAE;EAClD,MAAMvF,IAAI,GAAGjC,MAAM,CAAC4E,MAAM,CAAC6C,UAAU,CAAC;EACtC,IAAID,iBAAiB,EAAE;IACnBvF,IAAI,CAACR,yBAAyB,GAAG,IAAI;EACzC;EACAQ,IAAI,CAAC2D,EAAE,GAAGA,EAAE;EACZ3D,IAAI,CAACsF,QAAQ,GAAGA,QAAQ;EACxB,MAAMG,iBAAiB,GAAIC,SAAS,IAAK;IACrC1F,IAAI,CAAC0F,SAAS,GAAGA,SAAS;EAC9B,CAAC;EACD,SAASC,oBAAoBA,CAAC3F,IAAI,EAAE;IAChC,OAAOA,IAAI,CAAC2D,EAAE,KAAK,IAAI,IAAI3D,IAAI,CAACsF,QAAQ,KAAK,IAAI;EACrD;EACA,SAASM,gBAAgBA,CAAC5F,IAAI,EAAE;IAC5B,IAAI,CAAC2F,oBAAoB,CAAC3F,IAAI,CAAC,EAAE;MAC7B2B,eAAe,CAAC3B,IAAI,CAAC,CAAC,CAAC;MACvBA,IAAI,CAAC0F,SAAS,CAAC,CAAC;MAChB;MACA1F,IAAI,CAAC2D,EAAE,GAAG,IAAI;MACd3D,IAAI,CAACsF,QAAQ,GAAG,IAAI;MACpBtF,IAAI,CAAC0F,SAAS,GAAGG,eAAe;IACpC;EACJ;EACA,MAAMC,GAAG,GAAGA,CAAA,KAAM;IACd,IAAI9F,IAAI,CAAC2D,EAAE,KAAK,IAAI,EAAE;MAClB;MACA;IACJ;IACA,IAAIjF,qBAAqB,CAAC,CAAC,EAAE;MACzB,MAAM,IAAIuB,KAAK,CAAC,mEAAmE,CAAC;IACxF;IACAD,IAAI,CAACf,KAAK,GAAG,KAAK;IAClB,IAAIe,IAAI,CAAC+F,MAAM,IAAI,CAACnF,8BAA8B,CAACZ,IAAI,CAAC,EAAE;MACtD;IACJ;IACAA,IAAI,CAAC+F,MAAM,GAAG,IAAI;IAClB,MAAMzE,YAAY,GAAGF,yBAAyB,CAACpB,IAAI,CAAC;IACpD,IAAI;MACAA,IAAI,CAAC0F,SAAS,CAAC,CAAC;MAChB1F,IAAI,CAAC0F,SAAS,GAAGG,eAAe;MAChC7F,IAAI,CAAC2D,EAAE,CAAC8B,iBAAiB,CAAC;IAC9B,CAAC,SACO;MACJpE,wBAAwB,CAACrB,IAAI,EAAEsB,YAAY,CAAC;IAChD;EACJ,CAAC;EACDtB,IAAI,CAACgG,GAAG,GAAG;IACPC,MAAM,EAAEA,CAAA,KAAMlF,iBAAiB,CAACf,IAAI,CAAC;IACrC8F,GAAG;IACHI,OAAO,EAAEA,CAAA,KAAMlG,IAAI,CAAC0F,SAAS,CAAC,CAAC;IAC/BS,OAAO,EAAEA,CAAA,KAAMP,gBAAgB,CAAC5F,IAAI,CAAC;IACrC,CAAC5B,MAAM,GAAG4B;EACd,CAAC;EACD,OAAOA,IAAI,CAACgG,GAAG;AACnB;AACA,MAAMH,eAAe,GAAGA,CAAA,KAAM,CAAE,CAAC;AACjC;AACA;AACA;AACA,MAAML,UAAU,GAAG,eAAgB,CAAC,MAAM;EACtC,OAAO;IACH,GAAG1G,aAAa;IAChBW,oBAAoB,EAAE,IAAI;IAC1BD,yBAAyB,EAAE,KAAK;IAChCK,mBAAmB,EAAGG,IAAI,IAAK;MAC3B,IAAIA,IAAI,CAACsF,QAAQ,KAAK,IAAI,EAAE;QACxBtF,IAAI,CAACsF,QAAQ,CAACtF,IAAI,CAACgG,GAAG,CAAC;MAC3B;IACJ,CAAC;IACDD,MAAM,EAAE,KAAK;IACbL,SAAS,EAAEG;EACf,CAAC;AACL,CAAC,EAAE,CAAC;AAEJ,SAASO,uBAAuBA,CAACC,IAAI,EAAE;EACnC;AAAA;AAGJ,SAASvH,aAAa,EAAEV,MAAM,EAAE2F,WAAW,EAAE1C,wBAAwB,EAAED,yBAAyB,EAAEO,eAAe,EAAEZ,iBAAiB,EAAEH,8BAA8B,EAAE6B,cAAc,EAAEiC,kBAAkB,EAAEb,YAAY,EAAEwB,WAAW,EAAEzH,aAAa,EAAEa,iBAAiB,EAAEC,qBAAqB,EAAEC,UAAU,EAAEsG,iBAAiB,EAAEC,oBAAoB,EAAEnF,gBAAgB,EAAEW,sBAAsB,EAAEG,iBAAiB,EAAEC,uBAAuB,EAAEH,0BAA0B,EAAEK,sBAAsB,EAAEuD,kBAAkB,EAAEjG,iBAAiB,EAAE8H,uBAAuB,EAAEnC,kBAAkB,EAAEP,iCAAiC,EAAES,WAAW,EAAEE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}