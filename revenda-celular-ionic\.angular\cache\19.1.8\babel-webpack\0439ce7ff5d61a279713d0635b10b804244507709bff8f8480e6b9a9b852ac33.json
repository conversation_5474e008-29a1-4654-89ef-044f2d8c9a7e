{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host } from './index-28849c61.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-c81d82ab.js';\nconst textCss = \":host(.ion-color){color:var(--ion-color-base)}\";\nconst IonTextStyle0 = textCss;\nconst Text = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'e134d70c04344b708a2ecf6253722781ad2ca826',\n      class: createColorClasses(this.color, {\n        [mode]: true\n      })\n    }, h(\"slot\", {\n      key: 'da79c760f7ebbcd007ce110439f05a62cb22eac8'\n    }));\n  }\n};\nText.style = IonTextStyle0;\nexport { Text as ion_text };", "map": {"version": 3, "names": ["r", "registerInstance", "h", "f", "Host", "c", "createColorClasses", "b", "getIonMode", "textCss", "IonTextStyle0", "Text", "constructor", "hostRef", "color", "undefined", "render", "mode", "key", "class", "style", "ion_text"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/ion-text.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host } from './index-28849c61.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-c81d82ab.js';\n\nconst textCss = \":host(.ion-color){color:var(--ion-color-base)}\";\nconst IonTextStyle0 = textCss;\n\nconst Text = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.color = undefined;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'e134d70c04344b708a2ecf6253722781ad2ca826', class: createColorClasses(this.color, {\n                [mode]: true,\n            }) }, h(\"slot\", { key: 'da79c760f7ebbcd007ce110439f05a62cb22eac8' })));\n    }\n};\nText.style = IonTextStyle0;\n\nexport { Text as ion_text };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,QAAQ,qBAAqB;AACzE,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,qBAAqB;AAC7D,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAE5D,MAAMC,OAAO,GAAG,gDAAgD;AAChE,MAAMC,aAAa,GAAGD,OAAO;AAE7B,MAAME,IAAI,GAAG,MAAM;EACfC,WAAWA,CAACC,OAAO,EAAE;IACjBZ,gBAAgB,CAAC,IAAI,EAAEY,OAAO,CAAC;IAC/B,IAAI,CAACC,KAAK,GAAGC,SAAS;EAC1B;EACAC,MAAMA,CAAA,EAAG;IACL,MAAMC,IAAI,GAAGT,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQN,CAAC,CAACE,IAAI,EAAE;MAAEc,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEb,kBAAkB,CAAC,IAAI,CAACQ,KAAK,EAAE;QACjG,CAACG,IAAI,GAAG;MACZ,CAAC;IAAE,CAAC,EAAEf,CAAC,CAAC,MAAM,EAAE;MAAEgB,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;AACJ,CAAC;AACDP,IAAI,CAACS,KAAK,GAAGV,aAAa;AAE1B,SAASC,IAAI,IAAIU,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}