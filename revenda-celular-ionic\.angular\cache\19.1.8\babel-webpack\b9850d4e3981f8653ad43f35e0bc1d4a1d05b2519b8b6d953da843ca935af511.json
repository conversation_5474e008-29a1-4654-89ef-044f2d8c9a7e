{"ast": null, "code": "var _AccessoryService;\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AccessoryService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.baseUrl}/accessories`;\n  }\n  getById(accessoryId) {\n    return this.http.get(`${this.apiUrl}/${accessoryId}`);\n  }\n  getList() {\n    return this.http.get(this.apiUrl);\n  }\n  add(accessory) {\n    return this.http.post(this.apiUrl, accessory);\n  }\n  update(accessory) {\n    return this.http.put(`${this.apiUrl}/${accessory.id}`, accessory);\n  }\n  save(accessory) {\n    return accessory.id ? this.update(accessory) : this.add(accessory);\n  }\n  remove(accessory) {\n    return this.http.delete(`${this.apiUrl}/${accessory.id}`);\n  }\n}\n_AccessoryService = AccessoryService;\n_AccessoryService.ɵfac = function AccessoryService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AccessoryService)(i0.ɵɵinject(i1.HttpClient));\n};\n_AccessoryService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _AccessoryService,\n  factory: _AccessoryService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["environment", "AccessoryService", "constructor", "http", "apiUrl", "baseUrl", "getById", "accessoryId", "get", "getList", "add", "accessory", "post", "update", "put", "id", "save", "remove", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\accessories\\services\\accessory.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { Accessory } from '../models/accessory.type';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AccessoryService {\r\n  private readonly apiUrl = `${environment.baseUrl}/accessories`;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getById(accessoryId: string): Observable<Accessory> {\r\n    return this.http.get<Accessory>(`${this.apiUrl}/${accessoryId}`);\r\n  }\r\n\r\n  getList(): Observable<Accessory[]> {\r\n    return this.http.get<Accessory[]>(this.apiUrl);\r\n  }\r\n\r\n  private add(accessory: Accessory): Observable<Accessory> {\r\n    return this.http.post<Accessory>(this.apiUrl, accessory);\r\n  }\r\n\r\n  private update(accessory: Accessory): Observable<Accessory> {\r\n    return this.http.put<Accessory>(`${this.apiUrl}/${accessory.id}`, accessory);\r\n  }\r\n\r\n  save(accessory: Accessory): Observable<Accessory> {\r\n    return accessory.id ? this.update(accessory) : this.add(accessory);\r\n  }\r\n\r\n  remove(accessory: Accessory): Observable<Accessory> {\r\n    return this.http.delete<Accessory>(`${this.apiUrl}/${accessory.id}`);\r\n  }\r\n}\r\n"], "mappings": ";AAIA,SAASA,WAAW,QAAQ,8BAA8B;;;AAK1D,OAAM,MAAOC,gBAAgB;EAG3BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,OAAO,cAAc;EAEtB;EAExCC,OAAOA,CAACC,WAAmB;IACzB,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAY,GAAG,IAAI,CAACJ,MAAM,IAAIG,WAAW,EAAE,CAAC;EAClE;EAEAE,OAAOA,CAAA;IACL,OAAO,IAAI,CAACN,IAAI,CAACK,GAAG,CAAc,IAAI,CAACJ,MAAM,CAAC;EAChD;EAEQM,GAAGA,CAACC,SAAoB;IAC9B,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAY,IAAI,CAACR,MAAM,EAAEO,SAAS,CAAC;EAC1D;EAEQE,MAAMA,CAACF,SAAoB;IACjC,OAAO,IAAI,CAACR,IAAI,CAACW,GAAG,CAAY,GAAG,IAAI,CAACV,MAAM,IAAIO,SAAS,CAACI,EAAE,EAAE,EAAEJ,SAAS,CAAC;EAC9E;EAEAK,IAAIA,CAACL,SAAoB;IACvB,OAAOA,SAAS,CAACI,EAAE,GAAG,IAAI,CAACF,MAAM,CAACF,SAAS,CAAC,GAAG,IAAI,CAACD,GAAG,CAACC,SAAS,CAAC;EACpE;EAEAM,MAAMA,CAACN,SAAoB;IACzB,OAAO,IAAI,CAACR,IAAI,CAACe,MAAM,CAAY,GAAG,IAAI,CAACd,MAAM,IAAIO,SAAS,CAACI,EAAE,EAAE,CAAC;EACtE;;oBA3BWd,gBAAgB;;mCAAhBA,iBAAgB,EAAAkB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAhBrB,iBAAgB;EAAAsB,OAAA,EAAhBtB,iBAAgB,CAAAuB,IAAA;EAAAC,UAAA,EAFf;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}