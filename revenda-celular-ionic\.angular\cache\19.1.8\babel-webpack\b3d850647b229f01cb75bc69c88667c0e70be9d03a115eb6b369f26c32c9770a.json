{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { i as initialize } from './ionic-global-c81d82ab.js';\nconst globalScripts = initialize;\nexport { globalScripts as g };", "map": {"version": 3, "names": ["i", "initialize", "globalScripts", "g"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/app-globals-5dbb61a5.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { i as initialize } from './ionic-global-c81d82ab.js';\n\nconst globalScripts = initialize;\n\nexport { globalScripts as g };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAE5D,MAAMC,aAAa,GAAGD,UAAU;AAEhC,SAASC,aAAa,IAAIC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}