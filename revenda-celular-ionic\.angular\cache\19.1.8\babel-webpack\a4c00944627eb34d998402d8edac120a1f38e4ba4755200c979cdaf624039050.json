{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host, i as getElement } from './index-28849c61.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { x as eye, y as eyeOff } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-c81d82ab.js';\nconst iosInputPasswordToggleCss = \"\";\nconst IonInputPasswordToggleIosStyle0 = iosInputPasswordToggleCss;\nconst mdInputPasswordToggleCss = \"\";\nconst IonInputPasswordToggleMdStyle0 = mdInputPasswordToggleCss;\nconst InputPasswordToggle = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.togglePasswordVisibility = () => {\n      const {\n        inputElRef\n      } = this;\n      if (!inputElRef) {\n        return;\n      }\n      inputElRef.type = inputElRef.type === 'text' ? 'password' : 'text';\n    };\n    this.color = undefined;\n    this.showIcon = undefined;\n    this.hideIcon = undefined;\n    this.type = 'password';\n  }\n  /**\n   * Whenever the input type changes we need to re-run validation to ensure the password\n   * toggle is being used with the correct input type. If the application changes the type\n   * outside of this component we also need to re-render so the correct icon is shown.\n   */\n  onTypeChange(newValue) {\n    if (newValue !== 'text' && newValue !== 'password') {\n      printIonWarning(`ion-input-password-toggle only supports inputs of type \"text\" or \"password\". Input of type \"${newValue}\" is not compatible.`, this.el);\n      return;\n    }\n  }\n  connectedCallback() {\n    const {\n      el\n    } = this;\n    const inputElRef = this.inputElRef = el.closest('ion-input');\n    if (!inputElRef) {\n      printIonWarning('No ancestor ion-input found for ion-input-password-toggle. This component must be slotted inside of an ion-input.', el);\n      return;\n    }\n    /**\n     * Important: Set the type in connectedCallback because the default value\n     * of this.type may not always be accurate. Usually inputs have the \"password\" type\n     * but it is possible to have the input to initially have the \"text\" type. In that scenario\n     * the wrong icon will show briefly before switching to the correct icon. Setting the\n     * type here allows us to avoid that flicker.\n     */\n    this.type = inputElRef.type;\n  }\n  disconnectedCallback() {\n    this.inputElRef = null;\n  }\n  render() {\n    var _a, _b;\n    const {\n      color,\n      type\n    } = this;\n    const mode = getIonMode(this);\n    const showPasswordIcon = (_a = this.showIcon) !== null && _a !== void 0 ? _a : eye;\n    const hidePasswordIcon = (_b = this.hideIcon) !== null && _b !== void 0 ? _b : eyeOff;\n    const isPasswordVisible = type === 'text';\n    return h(Host, {\n      key: 'd9811e25bfeb2aa197352bb9be852e9e420739d5',\n      class: createColorClasses(color, {\n        [mode]: true\n      })\n    }, h(\"ion-button\", {\n      key: '1eaea1442b248fb2b8d61538b27274e647a07804',\n      mode: mode,\n      color: color,\n      fill: \"clear\",\n      shape: \"round\",\n      \"aria-checked\": isPasswordVisible ? 'true' : 'false',\n      \"aria-label\": \"show password\",\n      role: \"switch\",\n      type: \"button\",\n      onPointerDown: ev => {\n        /**\n         * This prevents mobile browsers from\n         * blurring the input when the password toggle\n         * button is activated.\n         */\n        ev.preventDefault();\n      },\n      onClick: this.togglePasswordVisibility\n    }, h(\"ion-icon\", {\n      key: '9c88de8f4631d9bde222ce2edf6950d639e04773',\n      slot: \"icon-only\",\n      \"aria-hidden\": \"true\",\n      icon: isPasswordVisible ? hidePasswordIcon : showPasswordIcon\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"type\": [\"onTypeChange\"]\n    };\n  }\n};\nInputPasswordToggle.style = {\n  ios: IonInputPasswordToggleIosStyle0,\n  md: IonInputPasswordToggleMdStyle0\n};\nexport { InputPasswordToggle as ion_input_password_toggle };", "map": {"version": 3, "names": ["r", "registerInstance", "h", "f", "Host", "i", "getElement", "p", "printIonWarning", "c", "createColorClasses", "x", "eye", "y", "eyeOff", "b", "getIonMode", "iosInputPasswordToggleCss", "IonInputPasswordToggleIosStyle0", "mdInputPasswordToggleCss", "IonInputPasswordToggleMdStyle0", "InputPasswordToggle", "constructor", "hostRef", "togglePasswordVisibility", "inputElRef", "type", "color", "undefined", "showIcon", "hideIcon", "onTypeChange", "newValue", "el", "connectedCallback", "closest", "disconnectedCallback", "render", "_a", "_b", "mode", "showPasswordIcon", "hidePasswordIcon", "isPasswordVisible", "key", "class", "fill", "shape", "role", "onPointerDown", "ev", "preventDefault", "onClick", "slot", "icon", "watchers", "style", "ios", "md", "ion_input_password_toggle"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/ion-input-password-toggle.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host, i as getElement } from './index-28849c61.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { x as eye, y as eyeOff } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-c81d82ab.js';\n\nconst iosInputPasswordToggleCss = \"\";\nconst IonInputPasswordToggleIosStyle0 = iosInputPasswordToggleCss;\n\nconst mdInputPasswordToggleCss = \"\";\nconst IonInputPasswordToggleMdStyle0 = mdInputPasswordToggleCss;\n\nconst InputPasswordToggle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.togglePasswordVisibility = () => {\n            const { inputElRef } = this;\n            if (!inputElRef) {\n                return;\n            }\n            inputElRef.type = inputElRef.type === 'text' ? 'password' : 'text';\n        };\n        this.color = undefined;\n        this.showIcon = undefined;\n        this.hideIcon = undefined;\n        this.type = 'password';\n    }\n    /**\n     * Whenever the input type changes we need to re-run validation to ensure the password\n     * toggle is being used with the correct input type. If the application changes the type\n     * outside of this component we also need to re-render so the correct icon is shown.\n     */\n    onTypeChange(newValue) {\n        if (newValue !== 'text' && newValue !== 'password') {\n            printIonWarning(`ion-input-password-toggle only supports inputs of type \"text\" or \"password\". Input of type \"${newValue}\" is not compatible.`, this.el);\n            return;\n        }\n    }\n    connectedCallback() {\n        const { el } = this;\n        const inputElRef = (this.inputElRef = el.closest('ion-input'));\n        if (!inputElRef) {\n            printIonWarning('No ancestor ion-input found for ion-input-password-toggle. This component must be slotted inside of an ion-input.', el);\n            return;\n        }\n        /**\n         * Important: Set the type in connectedCallback because the default value\n         * of this.type may not always be accurate. Usually inputs have the \"password\" type\n         * but it is possible to have the input to initially have the \"text\" type. In that scenario\n         * the wrong icon will show briefly before switching to the correct icon. Setting the\n         * type here allows us to avoid that flicker.\n         */\n        this.type = inputElRef.type;\n    }\n    disconnectedCallback() {\n        this.inputElRef = null;\n    }\n    render() {\n        var _a, _b;\n        const { color, type } = this;\n        const mode = getIonMode(this);\n        const showPasswordIcon = (_a = this.showIcon) !== null && _a !== void 0 ? _a : eye;\n        const hidePasswordIcon = (_b = this.hideIcon) !== null && _b !== void 0 ? _b : eyeOff;\n        const isPasswordVisible = type === 'text';\n        return (h(Host, { key: 'd9811e25bfeb2aa197352bb9be852e9e420739d5', class: createColorClasses(color, {\n                [mode]: true,\n            }) }, h(\"ion-button\", { key: '1eaea1442b248fb2b8d61538b27274e647a07804', mode: mode, color: color, fill: \"clear\", shape: \"round\", \"aria-checked\": isPasswordVisible ? 'true' : 'false', \"aria-label\": \"show password\", role: \"switch\", type: \"button\", onPointerDown: (ev) => {\n                /**\n                 * This prevents mobile browsers from\n                 * blurring the input when the password toggle\n                 * button is activated.\n                 */\n                ev.preventDefault();\n            }, onClick: this.togglePasswordVisibility }, h(\"ion-icon\", { key: '9c88de8f4631d9bde222ce2edf6950d639e04773', slot: \"icon-only\", \"aria-hidden\": \"true\", icon: isPasswordVisible ? hidePasswordIcon : showPasswordIcon }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"type\": [\"onTypeChange\"]\n    }; }\n};\nInputPasswordToggle.style = {\n    ios: IonInputPasswordToggleIosStyle0,\n    md: IonInputPasswordToggleMdStyle0\n};\n\nexport { InputPasswordToggle as ion_input_password_toggle };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC1F,SAASC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAC1D,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,qBAAqB;AAC7D,SAASC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,MAAM,QAAQ,qBAAqB;AAC3D,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAE5D,MAAMC,yBAAyB,GAAG,EAAE;AACpC,MAAMC,+BAA+B,GAAGD,yBAAyB;AAEjE,MAAME,wBAAwB,GAAG,EAAE;AACnC,MAAMC,8BAA8B,GAAGD,wBAAwB;AAE/D,MAAME,mBAAmB,GAAG,MAAM;EAC9BC,WAAWA,CAACC,OAAO,EAAE;IACjBtB,gBAAgB,CAAC,IAAI,EAAEsB,OAAO,CAAC;IAC/B,IAAI,CAACC,wBAAwB,GAAG,MAAM;MAClC,MAAM;QAAEC;MAAW,CAAC,GAAG,IAAI;MAC3B,IAAI,CAACA,UAAU,EAAE;QACb;MACJ;MACAA,UAAU,CAACC,IAAI,GAAGD,UAAU,CAACC,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,MAAM;IACtE,CAAC;IACD,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,QAAQ,GAAGD,SAAS;IACzB,IAAI,CAACE,QAAQ,GAAGF,SAAS;IACzB,IAAI,CAACF,IAAI,GAAG,UAAU;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIK,YAAYA,CAACC,QAAQ,EAAE;IACnB,IAAIA,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,UAAU,EAAE;MAChDxB,eAAe,CAAC,+FAA+FwB,QAAQ,sBAAsB,EAAE,IAAI,CAACC,EAAE,CAAC;MACvJ;IACJ;EACJ;EACAC,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAED;IAAG,CAAC,GAAG,IAAI;IACnB,MAAMR,UAAU,GAAI,IAAI,CAACA,UAAU,GAAGQ,EAAE,CAACE,OAAO,CAAC,WAAW,CAAE;IAC9D,IAAI,CAACV,UAAU,EAAE;MACbjB,eAAe,CAAC,mHAAmH,EAAEyB,EAAE,CAAC;MACxI;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACP,IAAI,GAAGD,UAAU,CAACC,IAAI;EAC/B;EACAU,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACX,UAAU,GAAG,IAAI;EAC1B;EACAY,MAAMA,CAAA,EAAG;IACL,IAAIC,EAAE,EAAEC,EAAE;IACV,MAAM;MAAEZ,KAAK;MAAED;IAAK,CAAC,GAAG,IAAI;IAC5B,MAAMc,IAAI,GAAGxB,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMyB,gBAAgB,GAAG,CAACH,EAAE,GAAG,IAAI,CAACT,QAAQ,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG1B,GAAG;IAClF,MAAM8B,gBAAgB,GAAG,CAACH,EAAE,GAAG,IAAI,CAACT,QAAQ,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGzB,MAAM;IACrF,MAAM6B,iBAAiB,GAAGjB,IAAI,KAAK,MAAM;IACzC,OAAQxB,CAAC,CAACE,IAAI,EAAE;MAAEwC,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEnC,kBAAkB,CAACiB,KAAK,EAAE;QAC5F,CAACa,IAAI,GAAG;MACZ,CAAC;IAAE,CAAC,EAAEtC,CAAC,CAAC,YAAY,EAAE;MAAE0C,GAAG,EAAE,0CAA0C;MAAEJ,IAAI,EAAEA,IAAI;MAAEb,KAAK,EAAEA,KAAK;MAAEmB,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE,OAAO;MAAE,cAAc,EAAEJ,iBAAiB,GAAG,MAAM,GAAG,OAAO;MAAE,YAAY,EAAE,eAAe;MAAEK,IAAI,EAAE,QAAQ;MAAEtB,IAAI,EAAE,QAAQ;MAAEuB,aAAa,EAAGC,EAAE,IAAK;QAC1Q;AAChB;AACA;AACA;AACA;QACgBA,EAAE,CAACC,cAAc,CAAC,CAAC;MACvB,CAAC;MAAEC,OAAO,EAAE,IAAI,CAAC5B;IAAyB,CAAC,EAAEtB,CAAC,CAAC,UAAU,EAAE;MAAE0C,GAAG,EAAE,0CAA0C;MAAES,IAAI,EAAE,WAAW;MAAE,aAAa,EAAE,MAAM;MAAEC,IAAI,EAAEX,iBAAiB,GAAGD,gBAAgB,GAAGD;IAAiB,CAAC,CAAC,CAAC,CAAC;EAClO;EACA,IAAIR,EAAEA,CAAA,EAAG;IAAE,OAAO3B,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiD,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,cAAc;IAC3B,CAAC;EAAE;AACP,CAAC;AACDlC,mBAAmB,CAACmC,KAAK,GAAG;EACxBC,GAAG,EAAEvC,+BAA+B;EACpCwC,EAAE,EAAEtC;AACR,CAAC;AAED,SAASC,mBAAmB,IAAIsC,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}