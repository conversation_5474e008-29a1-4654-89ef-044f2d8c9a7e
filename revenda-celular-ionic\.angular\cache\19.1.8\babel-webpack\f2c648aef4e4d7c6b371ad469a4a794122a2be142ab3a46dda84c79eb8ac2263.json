{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-a5d50daf.js';\nimport { p as pointerCoord } from './helpers-da915de8.js';\nconst startTapClick = config => {\n  if (doc === undefined) {\n    return;\n  }\n  let lastActivated = 0;\n  let activatableEle;\n  let activeRipple;\n  let activeDefer;\n  const useRippleEffect = config.getBoolean('animated', true) && config.getBoolean('rippleEffect', true);\n  const clearDefers = new WeakMap();\n  const cancelActive = () => {\n    if (activeDefer) clearTimeout(activeDefer);\n    activeDefer = undefined;\n    if (activatableEle) {\n      removeActivated(false);\n      activatableEle = undefined;\n    }\n  };\n  const pointerDown = ev => {\n    // Ignore right clicks\n    if (activatableEle || ev.button === 2) {\n      return;\n    }\n    setActivatedElement(getActivatableTarget(ev), ev);\n  };\n  const pointerUp = ev => {\n    setActivatedElement(undefined, ev);\n  };\n  const setActivatedElement = (el, ev) => {\n    // do nothing\n    if (el && el === activatableEle) {\n      return;\n    }\n    if (activeDefer) clearTimeout(activeDefer);\n    activeDefer = undefined;\n    const {\n      x,\n      y\n    } = pointerCoord(ev);\n    // deactivate selected\n    if (activatableEle) {\n      if (clearDefers.has(activatableEle)) {\n        throw new Error('internal error');\n      }\n      if (!activatableEle.classList.contains(ACTIVATED)) {\n        addActivated(activatableEle, x, y);\n      }\n      removeActivated(true);\n    }\n    // activate\n    if (el) {\n      const deferId = clearDefers.get(el);\n      if (deferId) {\n        clearTimeout(deferId);\n        clearDefers.delete(el);\n      }\n      el.classList.remove(ACTIVATED);\n      const callback = () => {\n        addActivated(el, x, y);\n        activeDefer = undefined;\n      };\n      if (isInstant(el)) {\n        callback();\n      } else {\n        activeDefer = setTimeout(callback, ADD_ACTIVATED_DEFERS);\n      }\n    }\n    activatableEle = el;\n  };\n  const addActivated = (el, x, y) => {\n    lastActivated = Date.now();\n    el.classList.add(ACTIVATED);\n    if (!useRippleEffect) return;\n    const rippleEffect = getRippleEffect(el);\n    if (rippleEffect !== null) {\n      removeRipple();\n      activeRipple = rippleEffect.addRipple(x, y);\n    }\n  };\n  const removeRipple = () => {\n    if (activeRipple !== undefined) {\n      activeRipple.then(remove => remove());\n      activeRipple = undefined;\n    }\n  };\n  const removeActivated = smooth => {\n    removeRipple();\n    const active = activatableEle;\n    if (!active) {\n      return;\n    }\n    const time = CLEAR_STATE_DEFERS - Date.now() + lastActivated;\n    if (smooth && time > 0 && !isInstant(active)) {\n      const deferId = setTimeout(() => {\n        active.classList.remove(ACTIVATED);\n        clearDefers.delete(active);\n      }, CLEAR_STATE_DEFERS);\n      clearDefers.set(active, deferId);\n    } else {\n      active.classList.remove(ACTIVATED);\n    }\n  };\n  doc.addEventListener('ionGestureCaptured', cancelActive);\n  doc.addEventListener('pointerdown', pointerDown, true);\n  doc.addEventListener('pointerup', pointerUp, true);\n  /**\n   * Tap click effects such as the ripple effect should\n   * not happen when scrolling. For example, if a user scrolls\n   * the page but also happens to do a touchstart on a button\n   * as part of the scroll, the ripple effect should not\n   * be dispatched. The ripple effect should only happen\n   * if the button is activated and the page is not scrolling.\n   *\n   * pointercancel is dispatched on a gesture when scrolling\n   * starts, so this lets us avoid having to listen for\n   * ion-content's scroll events.\n   */\n  doc.addEventListener('pointercancel', cancelActive, true);\n};\n// TODO(FW-2832): type\nconst getActivatableTarget = ev => {\n  if (ev.composedPath !== undefined) {\n    /**\n     * composedPath returns EventTarget[]. However,\n     * objects other than Element can be targets too.\n     * For example, AudioContext can be a target. In this\n     * case, we know that the event is a UIEvent so we\n     * can assume that the path will contain either Element\n     * or ShadowRoot.\n     */\n    const path = ev.composedPath();\n    for (let i = 0; i < path.length - 2; i++) {\n      const el = path[i];\n      if (!(el instanceof ShadowRoot) && el.classList.contains('ion-activatable')) {\n        return el;\n      }\n    }\n  } else {\n    return ev.target.closest('.ion-activatable');\n  }\n};\nconst isInstant = el => {\n  return el.classList.contains('ion-activatable-instant');\n};\nconst getRippleEffect = el => {\n  if (el.shadowRoot) {\n    const ripple = el.shadowRoot.querySelector('ion-ripple-effect');\n    if (ripple) {\n      return ripple;\n    }\n  }\n  return el.querySelector('ion-ripple-effect');\n};\nconst ACTIVATED = 'ion-activated';\nconst ADD_ACTIVATED_DEFERS = 100;\nconst CLEAR_STATE_DEFERS = 150;\nexport { startTapClick };", "map": {"version": 3, "names": ["d", "doc", "p", "pointerCoord", "startTapClick", "config", "undefined", "lastActivated", "activatableEle", "activeRipple", "activeDefer", "useRippleEffect", "getBoolean", "clearDefers", "WeakMap", "cancelActive", "clearTimeout", "removeActivated", "pointerDown", "ev", "button", "setActivatedElement", "getActivatableTarget", "pointerUp", "el", "x", "y", "has", "Error", "classList", "contains", "ACTIVATED", "addActivated", "deferId", "get", "delete", "remove", "callback", "isInstant", "setTimeout", "ADD_ACTIVATED_DEFERS", "Date", "now", "add", "rippleEffect", "getRippleEffect", "removeRipple", "addRipple", "then", "smooth", "active", "time", "CLEAR_STATE_DEFERS", "set", "addEventListener", "<PERSON><PERSON><PERSON>", "path", "i", "length", "ShadowRoot", "target", "closest", "shadowRoot", "ripple", "querySelector"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/index-79b30591.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-a5d50daf.js';\nimport { p as pointerCoord } from './helpers-da915de8.js';\n\nconst startTapClick = (config) => {\n    if (doc === undefined) {\n        return;\n    }\n    let lastActivated = 0;\n    let activatableEle;\n    let activeRipple;\n    let activeDefer;\n    const useRippleEffect = config.getBoolean('animated', true) && config.getBoolean('rippleEffect', true);\n    const clearDefers = new WeakMap();\n    const cancelActive = () => {\n        if (activeDefer)\n            clearTimeout(activeDefer);\n        activeDefer = undefined;\n        if (activatableEle) {\n            removeActivated(false);\n            activatableEle = undefined;\n        }\n    };\n    const pointerDown = (ev) => {\n        // Ignore right clicks\n        if (activatableEle || ev.button === 2) {\n            return;\n        }\n        setActivatedElement(getActivatableTarget(ev), ev);\n    };\n    const pointerUp = (ev) => {\n        setActivatedElement(undefined, ev);\n    };\n    const setActivatedElement = (el, ev) => {\n        // do nothing\n        if (el && el === activatableEle) {\n            return;\n        }\n        if (activeDefer)\n            clearTimeout(activeDefer);\n        activeDefer = undefined;\n        const { x, y } = pointerCoord(ev);\n        // deactivate selected\n        if (activatableEle) {\n            if (clearDefers.has(activatableEle)) {\n                throw new Error('internal error');\n            }\n            if (!activatableEle.classList.contains(ACTIVATED)) {\n                addActivated(activatableEle, x, y);\n            }\n            removeActivated(true);\n        }\n        // activate\n        if (el) {\n            const deferId = clearDefers.get(el);\n            if (deferId) {\n                clearTimeout(deferId);\n                clearDefers.delete(el);\n            }\n            el.classList.remove(ACTIVATED);\n            const callback = () => {\n                addActivated(el, x, y);\n                activeDefer = undefined;\n            };\n            if (isInstant(el)) {\n                callback();\n            }\n            else {\n                activeDefer = setTimeout(callback, ADD_ACTIVATED_DEFERS);\n            }\n        }\n        activatableEle = el;\n    };\n    const addActivated = (el, x, y) => {\n        lastActivated = Date.now();\n        el.classList.add(ACTIVATED);\n        if (!useRippleEffect)\n            return;\n        const rippleEffect = getRippleEffect(el);\n        if (rippleEffect !== null) {\n            removeRipple();\n            activeRipple = rippleEffect.addRipple(x, y);\n        }\n    };\n    const removeRipple = () => {\n        if (activeRipple !== undefined) {\n            activeRipple.then((remove) => remove());\n            activeRipple = undefined;\n        }\n    };\n    const removeActivated = (smooth) => {\n        removeRipple();\n        const active = activatableEle;\n        if (!active) {\n            return;\n        }\n        const time = CLEAR_STATE_DEFERS - Date.now() + lastActivated;\n        if (smooth && time > 0 && !isInstant(active)) {\n            const deferId = setTimeout(() => {\n                active.classList.remove(ACTIVATED);\n                clearDefers.delete(active);\n            }, CLEAR_STATE_DEFERS);\n            clearDefers.set(active, deferId);\n        }\n        else {\n            active.classList.remove(ACTIVATED);\n        }\n    };\n    doc.addEventListener('ionGestureCaptured', cancelActive);\n    doc.addEventListener('pointerdown', pointerDown, true);\n    doc.addEventListener('pointerup', pointerUp, true);\n    /**\n     * Tap click effects such as the ripple effect should\n     * not happen when scrolling. For example, if a user scrolls\n     * the page but also happens to do a touchstart on a button\n     * as part of the scroll, the ripple effect should not\n     * be dispatched. The ripple effect should only happen\n     * if the button is activated and the page is not scrolling.\n     *\n     * pointercancel is dispatched on a gesture when scrolling\n     * starts, so this lets us avoid having to listen for\n     * ion-content's scroll events.\n     */\n    doc.addEventListener('pointercancel', cancelActive, true);\n};\n// TODO(FW-2832): type\nconst getActivatableTarget = (ev) => {\n    if (ev.composedPath !== undefined) {\n        /**\n         * composedPath returns EventTarget[]. However,\n         * objects other than Element can be targets too.\n         * For example, AudioContext can be a target. In this\n         * case, we know that the event is a UIEvent so we\n         * can assume that the path will contain either Element\n         * or ShadowRoot.\n         */\n        const path = ev.composedPath();\n        for (let i = 0; i < path.length - 2; i++) {\n            const el = path[i];\n            if (!(el instanceof ShadowRoot) && el.classList.contains('ion-activatable')) {\n                return el;\n            }\n        }\n    }\n    else {\n        return ev.target.closest('.ion-activatable');\n    }\n};\nconst isInstant = (el) => {\n    return el.classList.contains('ion-activatable-instant');\n};\nconst getRippleEffect = (el) => {\n    if (el.shadowRoot) {\n        const ripple = el.shadowRoot.querySelector('ion-ripple-effect');\n        if (ripple) {\n            return ripple;\n        }\n    }\n    return el.querySelector('ion-ripple-effect');\n};\nconst ACTIVATED = 'ion-activated';\nconst ADD_ACTIVATED_DEFERS = 100;\nconst CLEAR_STATE_DEFERS = 150;\n\nexport { startTapClick };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,GAAG,QAAQ,qBAAqB;AAC9C,SAASC,CAAC,IAAIC,YAAY,QAAQ,uBAAuB;AAEzD,MAAMC,aAAa,GAAIC,MAAM,IAAK;EAC9B,IAAIJ,GAAG,KAAKK,SAAS,EAAE;IACnB;EACJ;EACA,IAAIC,aAAa,GAAG,CAAC;EACrB,IAAIC,cAAc;EAClB,IAAIC,YAAY;EAChB,IAAIC,WAAW;EACf,MAAMC,eAAe,GAAGN,MAAM,CAACO,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,IAAIP,MAAM,CAACO,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC;EACtG,MAAMC,WAAW,GAAG,IAAIC,OAAO,CAAC,CAAC;EACjC,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAIL,WAAW,EACXM,YAAY,CAACN,WAAW,CAAC;IAC7BA,WAAW,GAAGJ,SAAS;IACvB,IAAIE,cAAc,EAAE;MAChBS,eAAe,CAAC,KAAK,CAAC;MACtBT,cAAc,GAAGF,SAAS;IAC9B;EACJ,CAAC;EACD,MAAMY,WAAW,GAAIC,EAAE,IAAK;IACxB;IACA,IAAIX,cAAc,IAAIW,EAAE,CAACC,MAAM,KAAK,CAAC,EAAE;MACnC;IACJ;IACAC,mBAAmB,CAACC,oBAAoB,CAACH,EAAE,CAAC,EAAEA,EAAE,CAAC;EACrD,CAAC;EACD,MAAMI,SAAS,GAAIJ,EAAE,IAAK;IACtBE,mBAAmB,CAACf,SAAS,EAAEa,EAAE,CAAC;EACtC,CAAC;EACD,MAAME,mBAAmB,GAAGA,CAACG,EAAE,EAAEL,EAAE,KAAK;IACpC;IACA,IAAIK,EAAE,IAAIA,EAAE,KAAKhB,cAAc,EAAE;MAC7B;IACJ;IACA,IAAIE,WAAW,EACXM,YAAY,CAACN,WAAW,CAAC;IAC7BA,WAAW,GAAGJ,SAAS;IACvB,MAAM;MAAEmB,CAAC;MAAEC;IAAE,CAAC,GAAGvB,YAAY,CAACgB,EAAE,CAAC;IACjC;IACA,IAAIX,cAAc,EAAE;MAChB,IAAIK,WAAW,CAACc,GAAG,CAACnB,cAAc,CAAC,EAAE;QACjC,MAAM,IAAIoB,KAAK,CAAC,gBAAgB,CAAC;MACrC;MACA,IAAI,CAACpB,cAAc,CAACqB,SAAS,CAACC,QAAQ,CAACC,SAAS,CAAC,EAAE;QAC/CC,YAAY,CAACxB,cAAc,EAAEiB,CAAC,EAAEC,CAAC,CAAC;MACtC;MACAT,eAAe,CAAC,IAAI,CAAC;IACzB;IACA;IACA,IAAIO,EAAE,EAAE;MACJ,MAAMS,OAAO,GAAGpB,WAAW,CAACqB,GAAG,CAACV,EAAE,CAAC;MACnC,IAAIS,OAAO,EAAE;QACTjB,YAAY,CAACiB,OAAO,CAAC;QACrBpB,WAAW,CAACsB,MAAM,CAACX,EAAE,CAAC;MAC1B;MACAA,EAAE,CAACK,SAAS,CAACO,MAAM,CAACL,SAAS,CAAC;MAC9B,MAAMM,QAAQ,GAAGA,CAAA,KAAM;QACnBL,YAAY,CAACR,EAAE,EAAEC,CAAC,EAAEC,CAAC,CAAC;QACtBhB,WAAW,GAAGJ,SAAS;MAC3B,CAAC;MACD,IAAIgC,SAAS,CAACd,EAAE,CAAC,EAAE;QACfa,QAAQ,CAAC,CAAC;MACd,CAAC,MACI;QACD3B,WAAW,GAAG6B,UAAU,CAACF,QAAQ,EAAEG,oBAAoB,CAAC;MAC5D;IACJ;IACAhC,cAAc,GAAGgB,EAAE;EACvB,CAAC;EACD,MAAMQ,YAAY,GAAGA,CAACR,EAAE,EAAEC,CAAC,EAAEC,CAAC,KAAK;IAC/BnB,aAAa,GAAGkC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC1BlB,EAAE,CAACK,SAAS,CAACc,GAAG,CAACZ,SAAS,CAAC;IAC3B,IAAI,CAACpB,eAAe,EAChB;IACJ,MAAMiC,YAAY,GAAGC,eAAe,CAACrB,EAAE,CAAC;IACxC,IAAIoB,YAAY,KAAK,IAAI,EAAE;MACvBE,YAAY,CAAC,CAAC;MACdrC,YAAY,GAAGmC,YAAY,CAACG,SAAS,CAACtB,CAAC,EAAEC,CAAC,CAAC;IAC/C;EACJ,CAAC;EACD,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAIrC,YAAY,KAAKH,SAAS,EAAE;MAC5BG,YAAY,CAACuC,IAAI,CAAEZ,MAAM,IAAKA,MAAM,CAAC,CAAC,CAAC;MACvC3B,YAAY,GAAGH,SAAS;IAC5B;EACJ,CAAC;EACD,MAAMW,eAAe,GAAIgC,MAAM,IAAK;IAChCH,YAAY,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG1C,cAAc;IAC7B,IAAI,CAAC0C,MAAM,EAAE;MACT;IACJ;IACA,MAAMC,IAAI,GAAGC,kBAAkB,GAAGX,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGnC,aAAa;IAC5D,IAAI0C,MAAM,IAAIE,IAAI,GAAG,CAAC,IAAI,CAACb,SAAS,CAACY,MAAM,CAAC,EAAE;MAC1C,MAAMjB,OAAO,GAAGM,UAAU,CAAC,MAAM;QAC7BW,MAAM,CAACrB,SAAS,CAACO,MAAM,CAACL,SAAS,CAAC;QAClClB,WAAW,CAACsB,MAAM,CAACe,MAAM,CAAC;MAC9B,CAAC,EAAEE,kBAAkB,CAAC;MACtBvC,WAAW,CAACwC,GAAG,CAACH,MAAM,EAAEjB,OAAO,CAAC;IACpC,CAAC,MACI;MACDiB,MAAM,CAACrB,SAAS,CAACO,MAAM,CAACL,SAAS,CAAC;IACtC;EACJ,CAAC;EACD9B,GAAG,CAACqD,gBAAgB,CAAC,oBAAoB,EAAEvC,YAAY,CAAC;EACxDd,GAAG,CAACqD,gBAAgB,CAAC,aAAa,EAAEpC,WAAW,EAAE,IAAI,CAAC;EACtDjB,GAAG,CAACqD,gBAAgB,CAAC,WAAW,EAAE/B,SAAS,EAAE,IAAI,CAAC;EAClD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACItB,GAAG,CAACqD,gBAAgB,CAAC,eAAe,EAAEvC,YAAY,EAAE,IAAI,CAAC;AAC7D,CAAC;AACD;AACA,MAAMO,oBAAoB,GAAIH,EAAE,IAAK;EACjC,IAAIA,EAAE,CAACoC,YAAY,KAAKjD,SAAS,EAAE;IAC/B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMkD,IAAI,GAAGrC,EAAE,CAACoC,YAAY,CAAC,CAAC;IAC9B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACE,MAAM,GAAG,CAAC,EAAED,CAAC,EAAE,EAAE;MACtC,MAAMjC,EAAE,GAAGgC,IAAI,CAACC,CAAC,CAAC;MAClB,IAAI,EAAEjC,EAAE,YAAYmC,UAAU,CAAC,IAAInC,EAAE,CAACK,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;QACzE,OAAON,EAAE;MACb;IACJ;EACJ,CAAC,MACI;IACD,OAAOL,EAAE,CAACyC,MAAM,CAACC,OAAO,CAAC,kBAAkB,CAAC;EAChD;AACJ,CAAC;AACD,MAAMvB,SAAS,GAAId,EAAE,IAAK;EACtB,OAAOA,EAAE,CAACK,SAAS,CAACC,QAAQ,CAAC,yBAAyB,CAAC;AAC3D,CAAC;AACD,MAAMe,eAAe,GAAIrB,EAAE,IAAK;EAC5B,IAAIA,EAAE,CAACsC,UAAU,EAAE;IACf,MAAMC,MAAM,GAAGvC,EAAE,CAACsC,UAAU,CAACE,aAAa,CAAC,mBAAmB,CAAC;IAC/D,IAAID,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;EACJ;EACA,OAAOvC,EAAE,CAACwC,aAAa,CAAC,mBAAmB,CAAC;AAChD,CAAC;AACD,MAAMjC,SAAS,GAAG,eAAe;AACjC,MAAMS,oBAAoB,GAAG,GAAG;AAChC,MAAMY,kBAAkB,GAAG,GAAG;AAE9B,SAAShD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}