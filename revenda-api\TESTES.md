# 📋 GUIA COMPLETO DE TESTES - SISTEMA DE REVENDA DE CELULARES

## 🎯 **OBJETIVO**
Este documento contém todos os cenários de teste para validar formulários, validators customizados e regras de negócio do sistema.

---

## 📱 **1. TESTES DE FORMULÁRIOS - FRONTEND**

### **🏢 1.1 FORMULÁRIO DE MARCAS**

#### **Campos Obrigatórios:**
- ✅ **Nome**: Deixar vazio → Deve mostrar "O campo é obrigatório"
- ✅ **País**: Deixar vazio → Deve mostrar "O campo é obrigatório"

#### **Validações de Tamanho:**
- ✅ **Nome muito curto**: "AB" → Deve mostrar "O campo deve ter no mínimo 3 caracteres"
- ✅ **Nome muito longo**: String com 101 caracteres → Deve mostrar "O campo deve ter no máximo 100 caracteres"

#### **Casos de Sucesso:**
- ✅ **Dados válidos**: Nome: "Samsung", País: "Coreia do Sul" → Deve salvar com sucesso

---

### **👤 1.2 FORMULÁRIO DE CLIENTES**

#### **Validators Customizados:**
- ✅ **Email Domain**: 
  - ❌ "<EMAIL>" → "Apenas domínios permitidos (gmail, hotmail, outlook, yahoo)"
  - ✅ "<EMAIL>" → Deve aceitar
- ✅ **Telefone**: 
  - ❌ "123456789" → "Telefone deve ter 11 dígitos"
  - ✅ "11987654321" → Deve aceitar
- ✅ **Idade**: 
  - ❌ Data nascimento: 2010-01-01 → "Idade mínima é 16 anos"
  - ❌ Data nascimento: 1900-01-01 → "Idade máxima é 120 anos"
  - ✅ Data nascimento: 1990-01-01 → Deve aceitar
- ✅ **Nome**: 
  - ❌ "João123" → "Nome deve conter apenas letras e espaços"
  - ✅ "João Silva" → Deve aceitar
- ✅ **Endereço**: 
  - ❌ "Rua A" → "Endereço deve ter no mínimo 10 caracteres"
  - ✅ "Rua das Flores, 123" → Deve aceitar

#### **Casos de Sucesso:**
```
Nome: João Silva
Email: <EMAIL>
Telefone: 11987654321
Data Nascimento: 1990-05-15
Endereço: Rua das Flores, 123, Centro
Tipo: regular
Ativo: true
```

---

### **🏪 1.3 FORMULÁRIO DE LOJAS**

#### **Validators Customizados:**
- ✅ **Nome da Loja**: 
  - ❌ "Estabelecimento Central" → "Nome deve conter palavra identificadora (loja, store, shop)"
  - ✅ "Loja Central" → Deve aceitar
- ✅ **Telefone**: 
  - ❌ "123456789" → "Telefone deve ter 10 ou 11 dígitos"
  - ✅ "1133334444" → Deve aceitar (10 dígitos)
  - ✅ "11987654321" → Deve aceitar (11 dígitos)
- ✅ **Cidade**: 
  - ❌ "São Paulo123" → "Cidade deve conter apenas letras e espaços"
  - ✅ "São Paulo" → Deve aceitar
- ✅ **Endereço**: 
  - ❌ "Rua A" → "Endereço deve ter no mínimo 10 caracteres"
  - ✅ "Av. Paulista, 1000" → Deve aceitar
- ✅ **Gerente**: 
  - ❌ "João123" → "Gerente deve conter apenas letras e espaços"
  - ✅ "Maria Santos" → Deve aceitar

#### **Casos de Sucesso:**
```
Nome: Loja Tech Center
Endereço: Av. Paulista, 1000, Bela Vista
Cidade: São Paulo
Estado: SP
Telefone: 1133334444
Gerente: Maria Santos
Matriz: false
Status: active
```

---

### **📱 1.4 FORMULÁRIO DE CELULARES**

#### **Validators Customizados:**
- ✅ **Modelo**: 
  - ❌ "iPhone teste" → "Modelo não pode conter palavras como 'teste' ou 'exemplo'"
  - ✅ "iPhone 15 Pro" → Deve aceitar
- ✅ **Data de Lançamento**: 
  - ❌ "2025-12-31" → "Data de lançamento não pode ser no futuro"
  - ❌ "1999-01-01" → "Data de lançamento deve ser posterior ao ano 2000"
  - ✅ "2023-09-15" → Deve aceitar
- ✅ **Preço**: 
  - ❌ "60000" → "Preço não pode ser superior a R$ 50.000"
  - ✅ "3500" → Deve aceitar
- ✅ **Estoque**: 
  - ❌ "15000" → "Estoque não pode ser superior a 10.000 unidades"
  - ✅ "100" → Deve aceitar

#### **Casos de Sucesso:**
```
Modelo: iPhone 15 Pro
Imagem: https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSlcjdKmqzWBxpEzIa7ICY4cBQ5pcXzMngNOg&s
Data Lançamento: 2023-09-15
Preço: 5999.99
Categoria: Smartphone
Marca: Apple (ID)
Estoque: 50
```

---

### **🔌 1.5 FORMULÁRIO DE ACESSÓRIOS**

#### **Validators Customizados:**
- ✅ **Nome**: 
  - ❌ "Produto Universal" → "Nome deve conter palavra identificadora (case, capa, carregador, fone, película, suporte)"
  - ✅ "Capa Protetora" → Deve aceitar
- ✅ **Descrição**: 
  - ❌ "Produto ruim de qualidade" → "Descrição não pode conter palavras negativas"
  - ✅ "Capa de alta qualidade" → Deve aceitar
- ✅ **Preço**: 
  - ❌ "6000" → "Preço não pode ser superior a R$ 5.000"
  - ✅ "89.90" → Deve aceitar
- ✅ **Estoque**: 
  - ❌ "15000" → "Estoque não pode ser superior a 10.000 unidades"
  - ✅ "200" → Deve aceitar

#### **Casos de Sucesso:**
```
Nome: Capa Protetora Premium
Descrição: Capa de alta qualidade com proteção total
Preço: 89.90
Categoria: Capa
Imagem: https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRxyUhUfm80Kx6JZkcKUM4t6mkej300Dgw8lw&s
Estoque: 200
Celulares Compatíveis: [iPhone 15, Samsung Galaxy S23]
```

---

## 🧾 **2. TESTES DE FORMULÁRIO DE VENDAS**

### **2.1 Controle de Estoque**

#### **Cenários de Teste:**
- ✅ **Produto sem estoque definido**: 
  - Selecionar produto antigo sem campo stock → Deve mostrar "1/∞"
- ✅ **Produto com estoque limitado**: 
  - Produto com estoque = 5, quantidade = 3 → Deve mostrar "3/5" em verde
- ✅ **Produto no limite**: 
  - Produto com estoque = 5, quantidade = 5 → Deve mostrar "5/5" em amarelo
- ✅ **Produto acima do estoque**: 
  - Produto com estoque = 5, quantidade = 7 → Deve mostrar "7/5" em vermelho + erro
- ✅ **Produto sem estoque**: 
  - Produto com estoque = 0 → Deve bloquear venda com erro "Produto sem estoque"

### **2.2 Reset de Campos**

#### **Cenários de Teste:**
- ✅ **Alterar tipo de produto**: 
  - Selecionar "Phone" → "Accessory" → Todos os campos devem ser resetados
- ✅ **Alterar produto**: 
  - Selecionar "iPhone" → "Samsung" → Quantidade deve voltar para 1, preço deve atualizar

---

## ⚙️ **3. TESTES DE REGRAS DE NEGÓCIO - BACKEND**

### **🏢 3.1 MARCAS**

#### **RN-001: Nome único**
```bash
# Teste 1: Criar marca
POST /brands
{
  "name": "Apple",
  "country": "EUA"
}
# Resultado esperado: 201 Created

# Teste 2: Tentar criar marca duplicada
POST /brands
{
  "name": "Apple",
  "country": "EUA"
}
# Resultado esperado: 409 Conflict - "Marca 'Apple' já existe"
```

#### **RN-002: Não deletar marca com celulares**
```bash
# Teste 1: Criar celular vinculado à marca
POST /phones
{
  "model": "iPhone 15",
  "brandId": 1,
  ...
}

# Teste 2: Tentar deletar marca
DELETE /brands/1
# Resultado esperado: 400 Bad Request - "Não é possível deletar marca que possui celulares associados"
```

---

### **👤 3.2 CLIENTES**

#### **RN-003: Email único**
```bash
# Teste 1: Criar cliente
POST /customers
{
  "name": "João Silva",
  "email": "<EMAIL>",
  ...
}
# Resultado esperado: 201 Created

# Teste 2: Tentar criar cliente com email duplicado
POST /customers
{
  "name": "Maria Santos",
  "email": "<EMAIL>",
  ...
}
# Resultado esperado: 409 Conflict - "Email '<EMAIL>' já está em uso"
```

#### **RN-008: Não fazer downgrade VIP → Regular**
```bash
# Teste 1: Criar cliente VIP
POST /customers
{
  "name": "Cliente VIP",
  "email": "<EMAIL>",
  "customerType": "vip",
  ...
}

# Teste 2: Tentar fazer downgrade
PATCH /customers/1
{
  "customerType": "regular"
}
# Resultado esperado: 400 Bad Request - "Não é permitido fazer downgrade de cliente VIP para regular"
```

#### **RN-009: Não deletar cliente com vendas**
```bash
# Teste 1: Criar venda para cliente
POST /sales
{
  "customerId": 1,
  ...
}

# Teste 2: Tentar deletar cliente
DELETE /customers/1
# Resultado esperado: 400 Bad Request - "Não é possível deletar cliente com vendas associadas"
```

---

### **📱 3.3 CELULARES**

#### **RN-004: Modelo único por marca**
```bash
# Teste 1: Criar celular
POST /phones
{
  "model": "iPhone 15",
  "brandId": 1,
  "price": 5999.99,
  ...
}
# Resultado esperado: 201 Created

# Teste 2: Tentar criar modelo duplicado na mesma marca
POST /phones
{
  "model": "iPhone 15",
  "brandId": 1,
  "price": 6999.99,
  ...
}
# Resultado esperado: 409 Conflict - "Modelo 'iPhone 15' já existe para esta marca"
```

#### **RN-005: Preço mínimo**
```bash
# Teste: Tentar criar celular com preço muito baixo
POST /phones
{
  "model": "Celular Básico",
  "brandId": 1,
  "price": 5.00,
  ...
}
# Resultado esperado: 400 Bad Request - "Preço deve ser no mínimo R$ 10,00"
```

#### **RN-006: Não deletar celular em vendas**
```bash
# Teste 1: Criar venda com celular
POST /sales
{
  "items": [
    {
      "productId": 1,
      "productType": "phone",
      "quantity": 1,
      ...
    }
  ],
  ...
}

# Teste 2: Tentar deletar celular
DELETE /phones/1
# Resultado esperado: 400 Bad Request - "Não é possível deletar celular que está associado a vendas"
```

---

### **🔌 3.4 ACESSÓRIOS**

#### **RN-007: Nome único**
```bash
# Teste 1: Criar acessório
POST /accessories
{
  "name": "Capa iPhone 15",
  "price": 89.90,
  ...
}
# Resultado esperado: 201 Created

# Teste 2: Tentar criar acessório duplicado
POST /accessories
{
  "name": "Capa iPhone 15",
  "price": 99.90,
  ...
}
# Resultado esperado: 409 Conflict - "Acessório 'Capa iPhone 15' já existe"
```

#### **RN-008: Não reduzir estoque abaixo de 0**
```bash
# Teste 1: Criar acessório com estoque
POST /accessories
{
  "name": "Carregador USB-C",
  "stock": 10,
  ...
}

# Teste 2: Tentar reduzir estoque além do disponível
PATCH /accessories/1/stock
{
  "quantity": -15
}
# Resultado esperado: 400 Bad Request - "Operação resultaria em estoque negativo"
```

---

### **🧾 3.5 VENDAS**

#### **RN-010: Validar estoque antes da venda**
```bash
# Teste 1: Criar produto com estoque limitado
POST /phones
{
  "model": "iPhone 15",
  "stock": 2,
  ...
}

# Teste 2: Tentar vender quantidade maior que estoque
POST /sales
{
  "items": [
    {
      "productId": 1,
      "productType": "phone",
      "quantity": 5,
      ...
    }
  ],
  ...
}
# Resultado esperado: 400 Bad Request - "Quantidade solicitada (5) excede estoque disponível (2)"
```

#### **RN-011: Calcular valor total automaticamente**
```bash
# Teste: Criar venda com múltiplos itens
POST /sales
{
  "customerId": 1,
  "storeId": 1,
  "items": [
    {
      "productId": 1,
      "productType": "phone",
      "quantity": 1,
      "unitPrice": 5999.99,
      "subtotal": 5999.99
    },
    {
      "productId": 2,
      "productType": "accessory",
      "quantity": 2,
      "unitPrice": 89.90,
      "subtotal": 179.80
    }
  ],
  ...
}
# Resultado esperado: totalValue = 6179.79 (calculado automaticamente)
```

#### **RN-012: Não permitir venda sem itens**
```bash
# Teste: Tentar criar venda sem itens
POST /sales
{
  "customerId": 1,
  "storeId": 1,
  "items": [],
  ...
}
# Resultado esperado: 400 Bad Request - "Venda deve conter pelo menos um item"
```

---

## 🧪 **4. CENÁRIOS DE TESTE INTEGRADOS**

### **4.1 Fluxo Completo de Venda**

#### **Cenário: Venda com Controle de Estoque**
```bash
# Passo 1: Criar marca
POST /brands
{
  "name": "Samsung",
  "country": "Coreia do Sul"
}

# Passo 2: Criar celular com estoque
POST /phones
{
  "model": "Galaxy S23",
  "brandId": 1,
  "price": 3999.99,
  "stock": 5,
  ...
}

# Passo 3: Criar acessório com estoque
POST /accessories
{
  "name": "Capa Galaxy S23",
  "price": 79.90,
  "stock": 10,
  "compatiblePhoneIds": [1],
  ...
}

# Passo 4: Criar cliente
POST /customers
{
  "name": "Ana Silva",
  "email": "<EMAIL>",
  ...
}

# Passo 5: Criar loja
POST /stores
{
  "name": "Loja Tech Center",
  "address": "Av. Paulista, 1000",
  ...
}

# Passo 6: Criar venda
POST /sales
{
  "customerId": 1,
  "storeId": 1,
  "items": [
    {
      "productId": 1,
      "productType": "phone",
      "quantity": 2,
      "unitPrice": 3999.99,
      "subtotal": 7999.98
    },
    {
      "productId": 1,
      "productType": "accessory",
      "quantity": 2,
      "unitPrice": 79.90,
      "subtotal": 159.80
    }
  ],
  "paymentMethod": "credit_card",
  "seller": "João Vendedor"
}
# Resultado esperado:
# - Venda criada com totalValue = 8159.78
# - Estoque do celular reduzido para 3
# - Estoque do acessório reduzido para 8
```

### **4.2 Cenário de Erro: Estoque Insuficiente**
```bash
# Continuando do cenário anterior...
# Passo 7: Tentar vender mais que o estoque restante
POST /sales
{
  "customerId": 1,
  "storeId": 1,
  "items": [
    {
      "productId": 1,
      "productType": "phone",
      "quantity": 5,
      "unitPrice": 3999.99,
      "subtotal": 19999.95
    }
  ],
  ...
}
# Resultado esperado: 400 Bad Request - "Estoque insuficiente para Galaxy S23. Disponível: 3, Solicitado: 5"
```

---

## 📊 **5. CHECKLIST DE TESTES**

### **✅ Frontend - Formulários**
- [ ] Marcas: Validações básicas e salvamento
- [ ] Clientes: Todos os validators customizados
- [ ] Lojas: Todos os validators customizados
- [ ] Celulares: Todos os validators customizados + estoque
- [ ] Acessórios: Todos os validators customizados + estoque
- [ ] Vendas: Controle de estoque e reset de campos

### **✅ Backend - Regras de Negócio**
- [ ] RN-001: Marca nome único
- [ ] RN-002: Não deletar marca com celulares
- [ ] RN-003: Cliente email único
- [ ] RN-004: Celular modelo único por marca
- [ ] RN-005: Celular preço mínimo
- [ ] RN-006: Não deletar celular em vendas
- [ ] RN-007: Acessório nome único
- [ ] RN-008: Não reduzir estoque negativo / Não downgrade VIP
- [ ] RN-009: Não deletar cliente com vendas
- [ ] RN-010: Validar estoque antes da venda
- [ ] RN-011: Calcular valor total automaticamente
- [ ] RN-012: Não permitir venda sem itens

### **✅ Integração**
- [ ] Fluxo completo de venda com estoque
- [ ] Cenários de erro com estoque insuficiente
- [ ] Exibição de imagens nas listas
- [ ] Navegação entre todas as telas

---

## 🚀 **6. COMO EXECUTAR OS TESTES**

### **6.1 Preparação do Ambiente**
```bash
# Backend
cd revenda-api
npm install
npm run start:dev

# Frontend
cd revenda-celular-ionic
npm install
ionic serve
```

### **6.2 Ferramentas Recomendadas**
- **API Testing**: Postman, Insomnia ou Thunder Client (VS Code)
- **Frontend Testing**: Navegador com DevTools
- **Database**: pgAdmin ou DBeaver para verificar dados

### **6.3 Ordem de Execução**
1. **Testes de Backend**: Usar Postman/Insomnia para testar APIs
2. **Testes de Frontend**: Usar navegador para testar formulários
3. **Testes Integrados**: Combinar frontend + backend
4. **Testes de Regressão**: Repetir cenários críticos

---

## 📝 **7. RELATÓRIO DE BUGS**

### **Template para Reportar Bugs:**
```
**Título**: [COMPONENTE] Descrição breve do problema

**Passos para Reproduzir**:
1. Passo 1
2. Passo 2
3. Passo 3

**Resultado Esperado**: O que deveria acontecer

**Resultado Atual**: O que realmente acontece

**Evidências**: Screenshots, logs de erro, etc.

**Ambiente**: Frontend/Backend, versão do navegador, etc.
```

---

**🎯 Este guia garante que todos os aspectos do sistema sejam testados de forma completa e sistemática!**
