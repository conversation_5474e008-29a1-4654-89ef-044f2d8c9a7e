{"ast": null, "code": "var _BrandsPage;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/brand.service\";\nimport * as i2 from \"@ionic/angular\";\nimport * as i3 from \"@angular/router\";\nconst _c0 = () => [\"new\"];\nconst _c1 = a0 => [\"edit\", a0];\nconst _forTrack0 = ($index, $item) => $item.id;\nfunction BrandsPage_For_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-item\")(1, \"ion-text\", 9)(2, \"h2\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Pa\\u00EDs:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"ion-button\", 10);\n    i0.ɵɵelement(9, \"ion-icon\", 11);\n    i0.ɵɵtext(10, \" Editar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"ion-button\", 12);\n    i0.ɵɵlistener(\"click\", function BrandsPage_For_13_Template_ion_button_click_11_listener() {\n      const brand_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.remove(brand_r2));\n    });\n    i0.ɵɵelement(12, \"ion-icon\", 13);\n    i0.ɵɵtext(13, \" Excluir \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const brand_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(brand_r2.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", brand_r2.country, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(3, _c1, brand_r2.id));\n  }\n}\nfunction BrandsPage_ForEmpty_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\");\n    i0.ɵɵtext(1, \"Lista de marcas vazia, cadastre uma nova marca!\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class BrandsPage {\n  constructor(brandService, alertController, toastController) {\n    this.brandService = brandService;\n    this.alertController = alertController;\n    this.toastController = toastController;\n    this.brandsList = [];\n  }\n  ionViewDidEnter() {\n    this.loadBrands();\n  }\n  ngOnInit() {}\n  loadBrands() {\n    this.brandService.getBrands().subscribe({\n      next: response => {\n        this.brandsList = response;\n      },\n      error: error => {\n        alert('Erro ao carregar lista de marcas');\n        console.error(error);\n      }\n    });\n  }\n  remove(brand) {\n    this.alertController.create({\n      header: 'Exclusão',\n      message: `Confirma a exclusão da marca ${brand.name}?`,\n      buttons: [{\n        text: 'Sim',\n        handler: () => {\n          this.brandService.remove(brand).subscribe({\n            next: response => {\n              this.brandsList = this.brandsList.filter(b => b.id !== response.id);\n              this.toastController.create({\n                message: `Marca ${brand.name} excluída com sucesso!`,\n                duration: 3000,\n                color: 'secondary',\n                keyboardClose: true\n              }).then(toast => toast.present());\n            },\n            error: error => {\n              alert('Erro ao excluir a marca ' + brand.name);\n              console.error(error);\n            }\n          });\n        }\n      }, 'Não']\n    }).then(alert => alert.present());\n  }\n}\n_BrandsPage = BrandsPage;\n_BrandsPage.ɵfac = function BrandsPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BrandsPage)(i0.ɵɵdirectiveInject(i1.BrandService), i0.ɵɵdirectiveInject(i2.AlertController), i0.ɵɵdirectiveInject(i2.ToastController));\n};\n_BrandsPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _BrandsPage,\n  selectors: [[\"app-brands\"]],\n  standalone: false,\n  decls: 18,\n  vars: 5,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [3, \"fullscreen\"], [\"collapse\", \"condense\"], [\"size\", \"large\"], [\"vertical\", \"bottom\", \"horizontal\", \"end\", \"slot\", \"fixed\"], [3, \"routerLink\"], [\"name\", \"add\"], [1, \"brand-info\"], [\"size\", \"small\", 3, \"routerLink\"], [\"name\", \"create\", \"slot\", \"start\"], [\"size\", \"small\", 3, \"click\"], [\"name\", \"trash\", \"slot\", \"end\"]],\n  template: function BrandsPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-menu-button\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5, \"Marcas\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 3)(7, \"ion-header\", 4)(8, \"ion-toolbar\")(9, \"ion-title\", 5);\n      i0.ɵɵtext(10, \"Marcas\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(11, \"ion-list\");\n      i0.ɵɵrepeaterCreate(12, BrandsPage_For_13_Template, 14, 5, \"ion-item\", null, _forTrack0, false, BrandsPage_ForEmpty_14_Template, 2, 0, \"ion-item\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"ion-fab\", 6)(16, \"ion-fab-button\", 7);\n      i0.ɵɵelement(17, \"ion-icon\", 8);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵrepeater(ctx.brandsList);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(4, _c0));\n    }\n  },\n  dependencies: [i2.IonButton, i2.IonButtons, i2.IonContent, i2.IonFab, i2.IonFabButton, i2.IonHeader, i2.IonIcon, i2.IonItem, i2.IonList, i2.IonMenuButton, i2.IonText, i2.IonTitle, i2.IonToolbar, i2.RouterLinkDelegate, i3.RouterLink],\n  styles: [\".brand-info[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.brand-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n  margin-bottom: 4px;\\n}\\n.brand-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .brand-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin: 4px 0;\\n  font-weight: normal;\\n}\\n.brand-info[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  margin-right: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYnJhbmRzL2JyYW5kcy5wYWdlLnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxXQUFBO0FBQ0Y7QUFDRTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0FBQ0o7QUFFRTtFQUNFLGVBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7QUFBSjtBQUdFO0VBQ0UsZUFBQTtFQUNBLGlCQUFBO0FBREoiLCJzb3VyY2VzQ29udGVudCI6WyIuYnJhbmQtaW5mbyB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgXHJcbiAgaDIge1xyXG4gICAgZm9udC1zaXplOiAxOHB4O1xyXG4gICAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgICBtYXJnaW4tYm90dG9tOiA0cHg7XHJcbiAgfVxyXG4gIFxyXG4gIGgzLCBoNCB7XHJcbiAgICBmb250LXNpemU6IDE0cHg7XHJcbiAgICBtYXJnaW46IDRweCAwO1xyXG4gICAgZm9udC13ZWlnaHQ6IG5vcm1hbDtcclxuICB9XHJcbiAgXHJcbiAgaW9uLWJ1dHRvbiB7XHJcbiAgICBtYXJnaW4tdG9wOiA4cHg7XHJcbiAgICBtYXJnaW4tcmlnaHQ6IDhweDtcclxuICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n});", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "BrandsPage_For_13_Template_ion_button_click_11_listener", "brand_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "remove", "ɵɵadvance", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "country", "ɵɵproperty", "ɵɵpureFunction1", "_c1", "id", "BrandsPage", "constructor", "brandService", "alertController", "toastController", "brandsList", "ionViewDidEnter", "loadBrands", "ngOnInit", "getBrands", "subscribe", "next", "response", "error", "alert", "console", "brand", "create", "header", "message", "buttons", "text", "handler", "filter", "b", "duration", "color", "keyboardClose", "then", "toast", "present", "ɵɵdirectiveInject", "i1", "BrandService", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ToastController", "selectors", "standalone", "decls", "vars", "consts", "template", "BrandsPage_Template", "rf", "ctx", "ɵɵrepeaterCreate", "BrandsPage_For_13_Template", "_forTrack0", "BrandsPage_ForEmpty_14_Template", "ɵɵrepeater", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\brands\\brands.page.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\brands\\brands.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Brand } from './models/brand.type';\r\nimport { BrandService } from './services/brand.service';\r\nimport { AlertController, ToastController, ViewDidEnter } from '@ionic/angular';\r\n\r\n@Component({\r\n  selector: 'app-brands',\r\n  templateUrl: './brands.page.html',\r\n  styleUrls: ['./brands.page.scss'],\r\n  standalone: false,\r\n})\r\nexport class BrandsPage implements OnInit, ViewDidEnter {\r\n\r\n  brandsList: Brand[] = [];\r\n\r\n  constructor(\r\n    private brandService: BrandService,\r\n    private alertController: AlertController,\r\n    private toastController: ToastController,\r\n  ) { }\r\n\r\n  ionViewDidEnter(): void {\r\n    this.loadBrands();\r\n  }\r\n\r\n  ngOnInit() { }\r\n\r\n  loadBrands() {\r\n    this.brandService.getBrands().subscribe({\r\n      next: (response) => {\r\n        this.brandsList = response;\r\n      },\r\n      error: (error) => {\r\n        alert('Erro ao carregar lista de marcas');\r\n        console.error(error);\r\n      }\r\n    });\r\n  }\r\n\r\n  remove(brand: Brand) {\r\n    this.alertController.create({\r\n      header: 'Exclusão',\r\n      message: `Confirma a exclusão da marca ${brand.name}?`,\r\n      buttons: [\r\n        {\r\n          text: 'Sim',\r\n          handler: () => {\r\n            this.brandService.remove(brand).subscribe({\r\n              next: (response) => {\r\n                this.brandsList = this.brandsList.filter(b => b.id !== response.id);\r\n                this.toastController.create({\r\n                  message: `Marca ${brand.name} excluída com sucesso!`,\r\n                  duration: 3000,\r\n                  color: 'secondary',\r\n                  keyboardClose: true,\r\n                }).then(toast => toast.present());\r\n              },\r\n              error: (error) => {\r\n                alert('Erro ao excluir a marca ' + brand.name);\r\n                console.error(error);\r\n              }\r\n            });\r\n          }\r\n        },\r\n        'Não'\r\n      ]\r\n    }).then(alert => alert.present());\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-menu-button></ion-menu-button>\r\n    </ion-buttons>\r\n    <ion-title>Marc<PERSON></ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <ion-header collapse=\"condense\">\r\n    <ion-toolbar>\r\n      <ion-title size=\"large\"><PERSON><PERSON></ion-title>\r\n    </ion-toolbar>\r\n  </ion-header>\r\n\r\n  <ion-list>\r\n    @for(brand of brandsList; track brand.id) {\r\n    <ion-item>\r\n      <ion-text class=\"brand-info\">\r\n        <h2>{{ brand.name }}</h2>\r\n        <h3><strong>País:</strong> {{ brand.country }}</h3>\r\n        <ion-button size=\"small\" [routerLink]=\"['edit', brand.id]\">\r\n          <ion-icon name=\"create\" slot=\"start\"></ion-icon>\r\n          Editar\r\n        </ion-button>\r\n        <ion-button size=\"small\" (click)=\"remove(brand)\">\r\n          <ion-icon name=\"trash\" slot=\"end\"></ion-icon>\r\n          Excluir\r\n        </ion-button>\r\n      </ion-text>\r\n    </ion-item>\r\n    }\r\n    @empty {\r\n    <ion-item>Lista de marcas vazia, cadastre uma nova marca!</ion-item>\r\n    }\r\n  </ion-list>\r\n  <ion-fab vertical=\"bottom\" horizontal=\"end\" slot=\"fixed\">\r\n    <ion-fab-button [routerLink]=\"['new']\">\r\n      <ion-icon name=\"add\"></ion-icon>\r\n    </ion-fab-button>\r\n  </ion-fab>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;;ICoBQA,EAFJ,CAAAC,cAAA,eAAU,kBACqB,SACvB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAJ,CAAAC,cAAA,SAAI,aAAQ;IAAAD,EAAA,CAAAE,MAAA,iBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,qBAA2D;IACzDD,EAAA,CAAAI,SAAA,mBAAgD;IAChDJ,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAAiD;IAAxBD,EAAA,CAAAK,UAAA,mBAAAC,wDAAA;MAAA,MAAAC,QAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,MAAA,CAAAP,QAAA,CAAa;IAAA,EAAC;IAC9CP,EAAA,CAAAI,SAAA,oBAA6C;IAC7CJ,EAAA,CAAAE,MAAA,iBACF;IAEJF,EAFI,CAAAG,YAAA,EAAa,EACJ,EACF;;;;IAXHH,EAAA,CAAAe,SAAA,GAAgB;IAAhBf,EAAA,CAAAgB,iBAAA,CAAAT,QAAA,CAAAU,IAAA,CAAgB;IACOjB,EAAA,CAAAe,SAAA,GAAmB;IAAnBf,EAAA,CAAAkB,kBAAA,MAAAX,QAAA,CAAAY,OAAA,KAAmB;IACrBnB,EAAA,CAAAe,SAAA,EAAiC;IAAjCf,EAAA,CAAAoB,UAAA,eAAApB,EAAA,CAAAqB,eAAA,IAAAC,GAAA,EAAAf,QAAA,CAAAgB,EAAA,EAAiC;;;;;IAY9DvB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,sDAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;ADvBxE,OAAM,MAAOqB,UAAU;EAIrBC,YACUC,YAA0B,EAC1BC,eAAgC,EAChCC,eAAgC;IAFhC,KAAAF,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IALzB,KAAAC,UAAU,GAAY,EAAE;EAMpB;EAEJC,eAAeA,CAAA;IACb,IAAI,CAACC,UAAU,EAAE;EACnB;EAEAC,QAAQA,CAAA,GAAK;EAEbD,UAAUA,CAAA;IACR,IAAI,CAACL,YAAY,CAACO,SAAS,EAAE,CAACC,SAAS,CAAC;MACtCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACP,UAAU,GAAGO,QAAQ;MAC5B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,KAAK,CAAC,kCAAkC,CAAC;QACzCC,OAAO,CAACF,KAAK,CAACA,KAAK,CAAC;MACtB;KACD,CAAC;EACJ;EAEAvB,MAAMA,CAAC0B,KAAY;IACjB,IAAI,CAACb,eAAe,CAACc,MAAM,CAAC;MAC1BC,MAAM,EAAE,UAAU;MAClBC,OAAO,EAAE,gCAAgCH,KAAK,CAACvB,IAAI,GAAG;MACtD2B,OAAO,EAAE,CACP;QACEC,IAAI,EAAE,KAAK;QACXC,OAAO,EAAEA,CAAA,KAAK;UACZ,IAAI,CAACpB,YAAY,CAACZ,MAAM,CAAC0B,KAAK,CAAC,CAACN,SAAS,CAAC;YACxCC,IAAI,EAAGC,QAAQ,IAAI;cACjB,IAAI,CAACP,UAAU,GAAG,IAAI,CAACA,UAAU,CAACkB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,EAAE,KAAKa,QAAQ,CAACb,EAAE,CAAC;cACnE,IAAI,CAACK,eAAe,CAACa,MAAM,CAAC;gBAC1BE,OAAO,EAAE,SAASH,KAAK,CAACvB,IAAI,wBAAwB;gBACpDgC,QAAQ,EAAE,IAAI;gBACdC,KAAK,EAAE,WAAW;gBAClBC,aAAa,EAAE;eAChB,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;YACnC,CAAC;YACDjB,KAAK,EAAGA,KAAK,IAAI;cACfC,KAAK,CAAC,0BAA0B,GAAGE,KAAK,CAACvB,IAAI,CAAC;cAC9CsB,OAAO,CAACF,KAAK,CAACA,KAAK,CAAC;YACtB;WACD,CAAC;QACJ;OACD,EACD,KAAK;KAER,CAAC,CAACe,IAAI,CAACd,KAAK,IAAIA,KAAK,CAACgB,OAAO,EAAE,CAAC;EACnC;;cAxDW9B,UAAU;;mCAAVA,WAAU,EAAAxB,EAAA,CAAAuD,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAzD,EAAA,CAAAuD,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA3D,EAAA,CAAAuD,iBAAA,CAAAG,EAAA,CAAAE,eAAA;AAAA;;QAAVpC,WAAU;EAAAqC,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCTnBpE,EAFJ,CAAAC,cAAA,oBAAiC,qBACA,qBACH;MACxBD,EAAA,CAAAI,SAAA,sBAAmC;MACrCJ,EAAA,CAAAG,YAAA,EAAc;MACdH,EAAA,CAAAC,cAAA,gBAAW;MAAAD,EAAA,CAAAE,MAAA,aAAM;MAErBF,EAFqB,CAAAG,YAAA,EAAY,EACjB,EACH;MAKPH,EAHN,CAAAC,cAAA,qBAAiC,oBACC,kBACjB,mBACa;MAAAD,EAAA,CAAAE,MAAA,cAAM;MAElCF,EAFkC,CAAAG,YAAA,EAAY,EAC9B,EACH;MAEbH,EAAA,CAAAC,cAAA,gBAAU;MACRD,EAAA,CAAAsE,gBAAA,KAAAC,0BAAA,2BAAAC,UAAA,SAAAC,+BAAA,mBAkBC;MACHzE,EAAA,CAAAG,YAAA,EAAW;MAETH,EADF,CAAAC,cAAA,kBAAyD,yBAChB;MACrCD,EAAA,CAAAI,SAAA,mBAAgC;MAGtCJ,EAFI,CAAAG,YAAA,EAAiB,EACT,EACE;;;MA1CFH,EAAA,CAAAoB,UAAA,qBAAoB;MASnBpB,EAAA,CAAAe,SAAA,GAAmB;MAAnBf,EAAA,CAAAoB,UAAA,oBAAmB;MAQ5BpB,EAAA,CAAAe,SAAA,GAkBC;MAlBDf,EAAA,CAAA0E,UAAA,CAAAL,GAAA,CAAAxC,UAAA,CAkBC;MAGe7B,EAAA,CAAAe,SAAA,GAAsB;MAAtBf,EAAA,CAAAoB,UAAA,eAAApB,EAAA,CAAA2E,eAAA,IAAAC,GAAA,EAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}