{"ast": null, "code": "var _PhonesPageModule;\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { PhonesPageRoutingModule } from './phones-routing.module';\nimport { PhonesPage } from './phones.page';\nimport { PhoneFormComponent } from './phone-form/phone-form.component';\nimport { MaskitoDirective } from '@maskito/angular';\nimport { HttpClientModule } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nexport class PhonesPageModule {}\n_PhonesPageModule = PhonesPageModule;\n_PhonesPageModule.ɵfac = function PhonesPageModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _PhonesPageModule)();\n};\n_PhonesPageModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _PhonesPageModule\n});\n_PhonesPageModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [CommonModule, FormsModule, IonicModule, PhonesPageRoutingModule, FormsModule, ReactiveFormsModule, HttpClientModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PhonesPageModule, {\n    declarations: [PhonesPage, PhoneFormComponent],\n    imports: [CommonModule, FormsModule, IonicModule, PhonesPageRoutingModule, MaskitoDirective, FormsModule, ReactiveFormsModule, HttpClientModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "IonicModule", "PhonesPageRoutingModule", "PhonesPage", "PhoneFormComponent", "MaskitoDirective", "HttpClientModule", "PhonesPageModule", "declarations", "imports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\phones\\phones.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\n\r\nimport { IonicModule } from '@ionic/angular';\r\n\r\nimport { PhonesPageRoutingModule } from './phones-routing.module';\r\n\r\nimport { PhonesPage } from './phones.page';\r\nimport { PhoneFormComponent } from './phone-form/phone-form.component';\r\nimport { MaskitoDirective } from '@maskito/angular';\r\nimport { HttpClientModule } from '@angular/common/http';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    IonicModule,\r\n    PhonesPageRoutingModule,\r\n    MaskitoDirective,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    HttpClientModule,\r\n  ],\r\n  declarations: [\r\n    PhonesPage,\r\n    PhoneFormComponent,\r\n  ]\r\n})\r\nexport class PhonesPageModule {}\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,uBAAuB,QAAQ,yBAAyB;AAEjE,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,gBAAgB,QAAQ,sBAAsB;;AAkBvD,OAAM,MAAOC,gBAAgB;oBAAhBA,gBAAgB;;mCAAhBA,iBAAgB;AAAA;;QAAhBA;AAAgB;;YAdzBT,YAAY,EACZC,WAAW,EACXE,WAAW,EACXC,uBAAuB,EAEvBH,WAAW,EACXC,mBAAmB,EACnBM,gBAAgB;AAAA;;2EAOPC,gBAAgB;IAAAC,YAAA,GAJzBL,UAAU,EACVC,kBAAkB;IAAAK,OAAA,GAXlBX,YAAY,EACZC,WAAW,EACXE,WAAW,EACXC,uBAAuB,EACvBG,gBAAgB,EAChBN,WAAW,EACXC,mBAAmB,EACnBM,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}