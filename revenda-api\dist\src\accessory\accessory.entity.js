"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Accessory = void 0;
const typeorm_1 = require("typeorm");
let Accessory = class Accessory {
    id;
    name;
    description;
    price;
    category;
    image;
    stock;
    compatiblePhones;
};
exports.Accessory = Accessory;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Accessory.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 200 }),
    __metadata("design:type", String)
], Accessory.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], Accessory.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Accessory.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], Accessory.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 500 }),
    __metadata("design:type", String)
], Accessory.prototype, "image", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], Accessory.prototype, "stock", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)('Phone', (phone) => phone.accessories),
    (0, typeorm_1.JoinTable)({
        name: 'accessory_phone_compatibility',
        joinColumn: { name: 'accessory_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'phone_id', referencedColumnName: 'id' }
    }),
    __metadata("design:type", Array)
], Accessory.prototype, "compatiblePhones", void 0);
exports.Accessory = Accessory = __decorate([
    (0, typeorm_1.Entity)('accessories')
], Accessory);
//# sourceMappingURL=accessory.entity.js.map