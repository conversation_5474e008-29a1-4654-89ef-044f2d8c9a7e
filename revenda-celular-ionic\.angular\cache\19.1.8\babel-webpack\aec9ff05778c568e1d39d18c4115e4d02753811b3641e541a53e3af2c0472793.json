{"ast": null, "code": "var _StoreService;\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class StoreService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.baseUrl}/stores`;\n  }\n  getList() {\n    return this.http.get(this.apiUrl);\n  }\n  getById(storeId) {\n    return this.http.get(`${this.apiUrl}/${storeId}`);\n  }\n  getActiveStores() {\n    return this.http.get(`${this.apiUrl}/active/list`);\n  }\n  add(store) {\n    return this.http.post(this.apiUrl, store);\n  }\n  update(id, store) {\n    return this.http.patch(`${this.apiUrl}/${id}`, store);\n  }\n  save(store) {\n    if (store.id) {\n      const updateData = {\n        name: store.name,\n        address: store.address,\n        city: store.city,\n        state: store.state,\n        phone: store.phone,\n        manager: store.manager,\n        isHeadquarters: store.isHeadquarters,\n        status: store.status\n      };\n      return this.update(store.id, updateData);\n    } else {\n      const createData = {\n        name: store.name,\n        address: store.address,\n        city: store.city,\n        state: store.state,\n        phone: store.phone,\n        manager: store.manager,\n        isHeadquarters: store.isHeadquarters,\n        status: store.status\n      };\n      return this.add(createData);\n    }\n  }\n  remove(store) {\n    return this.http.delete(`${this.apiUrl}/${store.id}`);\n  }\n}\n_StoreService = StoreService;\n_StoreService.ɵfac = function StoreService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _StoreService)(i0.ɵɵinject(i1.HttpClient));\n};\n_StoreService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _StoreService,\n  factory: _StoreService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["environment", "StoreService", "constructor", "http", "apiUrl", "baseUrl", "getList", "get", "getById", "storeId", "getActiveStores", "add", "store", "post", "update", "id", "patch", "save", "updateData", "name", "address", "city", "state", "phone", "manager", "isHeadquarters", "status", "createData", "remove", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\stores\\services\\store.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { Store, CreateStoreDto, UpdateStoreDto } from '../models/store.type';\r\nimport { environment } from '../../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class StoreService {\r\n  private readonly apiUrl = `${environment.baseUrl}/stores`;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getList(): Observable<Store[]> {\r\n    return this.http.get<Store[]>(this.apiUrl);\r\n  }\r\n\r\n  getById(storeId: number): Observable<Store> {\r\n    return this.http.get<Store>(`${this.apiUrl}/${storeId}`);\r\n  }\r\n\r\n  getActiveStores(): Observable<Store[]> {\r\n    return this.http.get<Store[]>(`${this.apiUrl}/active/list`);\r\n  }\r\n\r\n  private add(store: CreateStoreDto): Observable<Store> {\r\n    return this.http.post<Store>(this.apiUrl, store);\r\n  }\r\n\r\n  private update(id: number, store: UpdateStoreDto): Observable<any> {\r\n    return this.http.patch(`${this.apiUrl}/${id}`, store);\r\n  }\r\n\r\n  save(store: Store): Observable<any> {\r\n    if (store.id) {\r\n      const updateData: UpdateStoreDto = {\r\n        name: store.name,\r\n        address: store.address,\r\n        city: store.city,\r\n        state: store.state,\r\n        phone: store.phone,\r\n        manager: store.manager,\r\n        isHeadquarters: store.isHeadquarters,\r\n        status: store.status\r\n      };\r\n      return this.update(store.id, updateData);\r\n    } else {\r\n      const createData: CreateStoreDto = {\r\n        name: store.name,\r\n        address: store.address,\r\n        city: store.city,\r\n        state: store.state,\r\n        phone: store.phone,\r\n        manager: store.manager,\r\n        isHeadquarters: store.isHeadquarters,\r\n        status: store.status\r\n      };\r\n      return this.add(createData);\r\n    }\r\n  }\r\n\r\n  remove(store: Store): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/${store.id}`);\r\n  }\r\n}\r\n"], "mappings": ";AAIA,SAASA,WAAW,QAAQ,mCAAmC;;;AAK/D,OAAM,MAAOC,YAAY;EAGvBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,OAAO,SAAS;EAElB;EAEvCC,OAAOA,CAAA;IACL,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAU,IAAI,CAACH,MAAM,CAAC;EAC5C;EAEAI,OAAOA,CAACC,OAAe;IACrB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAQ,GAAG,IAAI,CAACH,MAAM,IAAIK,OAAO,EAAE,CAAC;EAC1D;EAEAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACP,IAAI,CAACI,GAAG,CAAU,GAAG,IAAI,CAACH,MAAM,cAAc,CAAC;EAC7D;EAEQO,GAAGA,CAACC,KAAqB;IAC/B,OAAO,IAAI,CAACT,IAAI,CAACU,IAAI,CAAQ,IAAI,CAACT,MAAM,EAAEQ,KAAK,CAAC;EAClD;EAEQE,MAAMA,CAACC,EAAU,EAAEH,KAAqB;IAC9C,OAAO,IAAI,CAACT,IAAI,CAACa,KAAK,CAAC,GAAG,IAAI,CAACZ,MAAM,IAAIW,EAAE,EAAE,EAAEH,KAAK,CAAC;EACvD;EAEAK,IAAIA,CAACL,KAAY;IACf,IAAIA,KAAK,CAACG,EAAE,EAAE;MACZ,MAAMG,UAAU,GAAmB;QACjCC,IAAI,EAAEP,KAAK,CAACO,IAAI;QAChBC,OAAO,EAAER,KAAK,CAACQ,OAAO;QACtBC,IAAI,EAAET,KAAK,CAACS,IAAI;QAChBC,KAAK,EAAEV,KAAK,CAACU,KAAK;QAClBC,KAAK,EAAEX,KAAK,CAACW,KAAK;QAClBC,OAAO,EAAEZ,KAAK,CAACY,OAAO;QACtBC,cAAc,EAAEb,KAAK,CAACa,cAAc;QACpCC,MAAM,EAAEd,KAAK,CAACc;OACf;MACD,OAAO,IAAI,CAACZ,MAAM,CAACF,KAAK,CAACG,EAAE,EAAEG,UAAU,CAAC;IAC1C,CAAC,MAAM;MACL,MAAMS,UAAU,GAAmB;QACjCR,IAAI,EAAEP,KAAK,CAACO,IAAI;QAChBC,OAAO,EAAER,KAAK,CAACQ,OAAO;QACtBC,IAAI,EAAET,KAAK,CAACS,IAAI;QAChBC,KAAK,EAAEV,KAAK,CAACU,KAAK;QAClBC,KAAK,EAAEX,KAAK,CAACW,KAAK;QAClBC,OAAO,EAAEZ,KAAK,CAACY,OAAO;QACtBC,cAAc,EAAEb,KAAK,CAACa,cAAc;QACpCC,MAAM,EAAEd,KAAK,CAACc;OACf;MACD,OAAO,IAAI,CAACf,GAAG,CAACgB,UAAU,CAAC;IAC7B;EACF;EAEAC,MAAMA,CAAChB,KAAY;IACjB,OAAO,IAAI,CAACT,IAAI,CAAC0B,MAAM,CAAC,GAAG,IAAI,CAACzB,MAAM,IAAIQ,KAAK,CAACG,EAAE,EAAE,CAAC;EACvD;;gBAvDWd,YAAY;;mCAAZA,aAAY,EAAA6B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAZhC,aAAY;EAAAiC,OAAA,EAAZjC,aAAY,CAAAkC,IAAA;EAAAC,UAAA,EAFX;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}