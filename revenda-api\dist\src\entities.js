"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaleItem = exports.Sale = exports.Customer = exports.Store = exports.Accessory = exports.Phone = exports.Brand = void 0;
var brand_entity_1 = require("./brand/brand.entity");
Object.defineProperty(exports, "Brand", { enumerable: true, get: function () { return brand_entity_1.Brand; } });
var phone_entity_1 = require("./phone/phone.entity");
Object.defineProperty(exports, "Phone", { enumerable: true, get: function () { return phone_entity_1.Phone; } });
var accessory_entity_1 = require("./accessory/accessory.entity");
Object.defineProperty(exports, "Accessory", { enumerable: true, get: function () { return accessory_entity_1.Accessory; } });
var store_entity_1 = require("./store/store.entity");
Object.defineProperty(exports, "Store", { enumerable: true, get: function () { return store_entity_1.Store; } });
var customer_entity_1 = require("./customer/customer.entity");
Object.defineProperty(exports, "Customer", { enumerable: true, get: function () { return customer_entity_1.Customer; } });
var sale_entity_1 = require("./sale/sale.entity");
Object.defineProperty(exports, "Sale", { enumerable: true, get: function () { return sale_entity_1.Sale; } });
var sale_item_entity_1 = require("./sale/sale-item.entity");
Object.defineProperty(exports, "SaleItem", { enumerable: true, get: function () { return sale_item_entity_1.SaleItem; } });
//# sourceMappingURL=entities.js.map