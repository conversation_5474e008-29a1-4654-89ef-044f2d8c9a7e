{"ast": null, "code": "var _CustomersPageModule;\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\nimport { CustomersPageRoutingModule } from './customers-routing.module';\nimport { CustomersPage } from './customers.page';\nimport { CustomerFormComponent } from './customer-form/customer-form.component';\nimport { MaskitoDirective } from '@maskito/angular';\nimport * as i0 from \"@angular/core\";\nexport class CustomersPageModule {}\n_CustomersPageModule = CustomersPageModule;\n_CustomersPageModule.ɵfac = function CustomersPageModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CustomersPageModule)();\n};\n_CustomersPageModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _CustomersPageModule\n});\n_CustomersPageModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, CustomersPageRoutingModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomersPageModule, {\n    declarations: [CustomersPage, CustomerFormComponent],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, CustomersPageRoutingModule, MaskitoDirective]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "IonicModule", "CustomersPageRoutingModule", "CustomersPage", "CustomerFormComponent", "MaskitoDirective", "CustomersPageModule", "declarations", "imports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\customers\\customers.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { CustomersPageRoutingModule } from './customers-routing.module';\r\nimport { CustomersPage } from './customers.page';\r\nimport { CustomerFormComponent } from './customer-form/customer-form.component';\r\nimport { MaskitoDirective } from '@maskito/angular';\r\n\r\n@NgModule({\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    IonicModule,\r\n    CustomersPageRoutingModule,\r\n    MaskitoDirective\r\n  ],\r\n  declarations: [CustomersPage, CustomerFormComponent]\r\n})\r\nexport class CustomersPageModule {}\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,0BAA0B,QAAQ,4BAA4B;AACvE,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,gBAAgB,QAAQ,kBAAkB;;AAanD,OAAM,MAAOC,mBAAmB;uBAAnBA,mBAAmB;;mCAAnBA,oBAAmB;AAAA;;QAAnBA;AAAmB;;YAT5BR,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW,EACXC,0BAA0B;AAAA;;2EAKjBI,mBAAmB;IAAAC,YAAA,GAFfJ,aAAa,EAAEC,qBAAqB;IAAAI,OAAA,GAPjDV,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW,EACXC,0BAA0B,EAC1BG,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}