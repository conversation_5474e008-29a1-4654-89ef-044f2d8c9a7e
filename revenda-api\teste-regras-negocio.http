### 🧪 TESTE DAS REGRAS DE NEGÓCIO - BACKEND
### Use este arquivo para testar se as regras estão funcionando corretamente

@baseUrl = http://localhost:3000
@contentType = application/json

### ========================================
### 🏢 TESTE: MARCA COM CELULARES VINCULADOS
### ========================================

### 1. Criar marca
POST {{baseUrl}}/brands
Content-Type: {{contentType}}

{
  "name": "Apple Test",
  "country": "EUA"
}

### 2. Criar celular vinculado à marca (assumindo que a marca criada tem ID 1)
POST {{baseUrl}}/phones
Content-Type: {{contentType}}

{
  "model": "iPhone Test",
  "image": "https://example.com/iphone.jpg",
  "releaseDate": "2023-09-15",
  "price": 5999.99,
  "category": "Smartphone",
  "brandId": 1,
  "stock": 10
}

### 3. Tentar deletar marca que tem celular vinculado (deve retornar erro tratado)
DELETE {{baseUrl}}/brands/1

### ========================================
### 👤 TESTE: CLIENTE COM VENDAS
### ========================================

### 1. Criar cliente
POST {{baseUrl}}/customers
Content-Type: {{contentType}}

{
  "name": "João Test",
  "email": "<EMAIL>",
  "phone": "11987654321",
  "birthDate": "1990-05-15",
  "address": "Rua Test, 123",
  "customerType": "regular",
  "active": true
}

### 2. Criar loja
POST {{baseUrl}}/stores
Content-Type: {{contentType}}

{
  "name": "Loja Test",
  "address": "Av. Test, 1000",
  "city": "São Paulo",
  "state": "SP",
  "phone": "1133334444",
  "manager": "Maria Test",
  "isHeadquarters": false,
  "status": "active"
}

### 3. Criar venda para o cliente (assumindo cliente ID 1, loja ID 1, celular ID 1)
POST {{baseUrl}}/sales
Content-Type: {{contentType}}

{
  "customerId": 1,
  "storeId": 1,
  "paymentMethod": "credit_card",
  "status": "pending",
  "seller": "Vendedor Test",
  "items": [
    {
      "productId": 1,
      "productType": "phone",
      "quantity": 1,
      "unitPrice": 5999.99,
      "subtotal": 5999.99
    }
  ]
}

### 4. Tentar deletar cliente que tem venda (deve retornar erro tratado)
DELETE {{baseUrl}}/customers/1

### ========================================
### 📱 TESTE: CELULAR EM VENDAS
### ========================================

### Tentar deletar celular que está em venda (deve retornar erro tratado)
DELETE {{baseUrl}}/phones/1

### ========================================
### 🏪 TESTE: LOJA COM VENDAS
### ========================================

### Tentar deletar loja que tem venda (deve retornar erro tratado)
DELETE {{baseUrl}}/stores/1

### ========================================
### 🔌 TESTE: ACESSÓRIO EM VENDAS
### ========================================

### 1. Criar acessório
POST {{baseUrl}}/accessories
Content-Type: {{contentType}}

{
  "name": "Capa Test",
  "description": "Capa de teste de alta qualidade",
  "price": 89.90,
  "category": "Capa",
  "image": "https://example.com/capa.jpg",
  "stock": 50,
  "compatiblePhoneIds": [1]
}

### 2. Criar venda com acessório (assumindo acessório ID 1)
POST {{baseUrl}}/sales
Content-Type: {{contentType}}

{
  "customerId": 1,
  "storeId": 1,
  "paymentMethod": "credit_card",
  "status": "pending",
  "seller": "Vendedor Test 2",
  "items": [
    {
      "productId": 1,
      "productType": "accessory",
      "quantity": 2,
      "unitPrice": 89.90,
      "subtotal": 179.80
    }
  ]
}

### 3. Tentar deletar acessório que está em venda (deve retornar erro tratado)
DELETE {{baseUrl}}/accessories/1

### ========================================
### 📝 TESTE: NOMES DUPLICADOS
### ========================================

### 1. Tentar criar marca duplicada (deve retornar erro tratado)
POST {{baseUrl}}/brands
Content-Type: {{contentType}}

{
  "name": "Apple Test",
  "country": "Brasil"
}

### 2. Tentar criar acessório duplicado (deve retornar erro tratado)
POST {{baseUrl}}/accessories
Content-Type: {{contentType}}

{
  "name": "Capa Test",
  "description": "Outra capa de teste",
  "price": 99.90,
  "category": "Capa",
  "image": "https://example.com/capa2.jpg",
  "stock": 30,
  "compatiblePhoneIds": [1]
}

### 2.1. Criar segundo acessório para testar update
POST {{baseUrl}}/accessories
Content-Type: {{contentType}}

{
  "name": "Carregador USB-C",
  "description": "Carregador de alta velocidade",
  "price": 129.90,
  "category": "Carregador",
  "image": "https://example.com/carregador.jpg",
  "stock": 20,
  "compatiblePhoneIds": [1]
}

### 3. Tentar atualizar marca com nome duplicado (deve retornar erro tratado)
PATCH {{baseUrl}}/brands/1
Content-Type: {{contentType}}

{
  "name": "Samsung"
}

### 4. Tentar atualizar acessório com nome duplicado (deve retornar erro tratado)
### Tentar alterar "Capa Test" (ID 1) para "Carregador USB-C" (que já existe no ID 2)
PATCH {{baseUrl}}/accessories/1
Content-Type: {{contentType}}

{
  "name": "Carregador USB-C"
}

### 4.1. Teste válido: atualizar acessório com nome único (deve funcionar)
PATCH {{baseUrl}}/accessories/1
Content-Type: {{contentType}}

{
  "name": "Capa Premium Atualizada",
  "description": "Capa atualizada com nova descrição"
}

### ========================================
### ✅ TESTE: OPERAÇÕES VÁLIDAS
### ========================================

### 1. Deletar venda para liberar dependências
DELETE {{baseUrl}}/sales/1

### 2. Deletar segunda venda para liberar dependências
DELETE {{baseUrl}}/sales/2

### 3. Agora deve conseguir deletar acessório (sem vendas)
DELETE {{baseUrl}}/accessories/1

### 4. Agora deve conseguir deletar celular (sem vendas)
DELETE {{baseUrl}}/phones/1

### 5. Agora deve conseguir deletar marca (sem celulares)
DELETE {{baseUrl}}/brands/1

### 6. Agora deve conseguir deletar cliente (sem vendas)
DELETE {{baseUrl}}/customers/1

### 7. Agora deve conseguir deletar loja (sem vendas)
DELETE {{baseUrl}}/stores/1

### ========================================
### 📊 RESULTADOS ESPERADOS
### ========================================

# ❌ DEVEM FALHAR COM MENSAGENS TRATADAS:
# - DELETE marca com celulares: "Não é possível deletar a marca 'Apple Test' pois existem 1 celular(es) associado(s) a ela"
# - DELETE cliente com vendas: "Não é possível deletar cliente com vendas associadas. Cliente possui 1 venda(s)."
# - DELETE celular em vendas: "Não é possível deletar celular que está associado a vendas. Modelo: iPhone Test"
# - DELETE loja com vendas: "Não é possível deletar a loja 'Loja Test' pois existem 2 venda(s) associada(s) a ela"
# - DELETE acessório em vendas: "Não é possível deletar acessório que está associado a vendas. Nome: Capa Test"
# - POST marca duplicada: "Marca 'Apple Test' já existe no sistema"
# - POST acessório duplicado: "Acessório 'Capa Test' já existe no sistema"
# - PATCH marca nome duplicado: "Marca 'Samsung' já existe no sistema"
# - PATCH acessório nome duplicado: "Acessório 'Carregador USB-C' já existe no sistema"

# ✅ DEVEM FUNCIONAR:
# - DELETE operações após remover dependências
# - Todas as operações de criação iniciais
# - Operações de atualização com nomes únicos
# - PATCH acessório com nome único: "Capa Premium Atualizada" (deve funcionar)
# - POST segundo acessório: "Carregador USB-C" (deve funcionar)
