{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\nimport { r as raf } from './helpers-da915de8.js';\nimport { a as printIonError } from './index-9b0d46f4.js';\n\n/**\n * Used to update a scoped component that uses emulated slots. This fires when\n * content is passed into the slot or when the content inside of a slot changes.\n * This is not needed for components using native slots in the Shadow DOM.\n * @internal\n * @param el The host element to observe\n * @param slotName mutationCallback will fire when nodes on these slot(s) change\n * @param mutationCallback The callback to fire whenever the slotted content changes\n */\nconst createSlotMutationController = (el, slotName, mutationCallback) => {\n  let hostMutationObserver;\n  let slottedContentMutationObserver;\n  if (win !== undefined && 'MutationObserver' in win) {\n    const slots = Array.isArray(slotName) ? slotName : [slotName];\n    hostMutationObserver = new MutationObserver(entries => {\n      for (const entry of entries) {\n        for (const node of entry.addedNodes) {\n          /**\n           * Check to see if the added node\n           *  is our slotted content.\n           */\n          if (node.nodeType === Node.ELEMENT_NODE && slots.includes(node.slot)) {\n            /**\n             * If so, we want to watch the slotted\n             * content itself for changes. This lets us\n             * detect when content inside of the slot changes.\n             */\n            mutationCallback();\n            /**\n             * Adding the listener in an raf\n             * waits until Stencil moves the slotted element\n             * into the correct place in the event that\n             * slotted content is being added.\n             */\n            raf(() => watchForSlotChange(node));\n            return;\n          }\n        }\n      }\n    });\n    hostMutationObserver.observe(el, {\n      childList: true,\n      /**\n       * This fixes an issue with the `ion-input` and\n       * `ion-textarea` not re-rendering in some cases\n       * when using the label slot functionality.\n       *\n       * HTML element patches in Stencil that are enabled\n       * by the `experimentalSlotFixes` flag in Stencil v4\n       * result in DOM manipulations that won't trigger\n       * the current mutation observer configuration and\n       * callback.\n       */\n      subtree: true\n    });\n  }\n  /**\n   * Listen for changes inside of the slotted content.\n   * We can listen for subtree changes here to be\n   * informed of text within the slotted content\n   * changing. Doing this on the host is possible\n   * but it is much more expensive to do because\n   * it also listens for changes to the internals\n   * of the component.\n   */\n  const watchForSlotChange = slottedEl => {\n    var _a;\n    if (slottedContentMutationObserver) {\n      slottedContentMutationObserver.disconnect();\n      slottedContentMutationObserver = undefined;\n    }\n    slottedContentMutationObserver = new MutationObserver(entries => {\n      mutationCallback();\n      for (const entry of entries) {\n        for (const node of entry.removedNodes) {\n          /**\n           * If the element was removed then we\n           * need to destroy the MutationObserver\n           * so the element can be garbage collected.\n           */\n          if (node.nodeType === Node.ELEMENT_NODE && node.slot === slotName) {\n            destroySlottedContentObserver();\n          }\n        }\n      }\n    });\n    /**\n     * Listen for changes inside of the element\n     * as well as anything deep in the tree.\n     * We listen on the parentElement so that we can\n     * detect when slotted element itself is removed.\n     */\n    slottedContentMutationObserver.observe((_a = slottedEl.parentElement) !== null && _a !== void 0 ? _a : slottedEl, {\n      subtree: true,\n      childList: true\n    });\n  };\n  const destroy = () => {\n    if (hostMutationObserver) {\n      hostMutationObserver.disconnect();\n      hostMutationObserver = undefined;\n    }\n    destroySlottedContentObserver();\n  };\n  const destroySlottedContentObserver = () => {\n    if (slottedContentMutationObserver) {\n      slottedContentMutationObserver.disconnect();\n      slottedContentMutationObserver = undefined;\n    }\n  };\n  return {\n    destroy\n  };\n};\nconst getCounterText = (value, maxLength, counterFormatter) => {\n  const valueLength = value == null ? 0 : value.toString().length;\n  const defaultCounterText = defaultCounterFormatter(valueLength, maxLength);\n  /**\n   * If developers did not pass a custom formatter,\n   * use the default one.\n   */\n  if (counterFormatter === undefined) {\n    return defaultCounterText;\n  }\n  /**\n   * Otherwise, try to use the custom formatter\n   * and fallback to the default formatter if\n   * there was an error.\n   */\n  try {\n    return counterFormatter(valueLength, maxLength);\n  } catch (e) {\n    printIonError('Exception in provided `counterFormatter`.', e);\n    return defaultCounterText;\n  }\n};\nconst defaultCounterFormatter = (length, maxlength) => {\n  return `${length} / ${maxlength}`;\n};\nexport { createSlotMutationController as c, getCounterText as g };", "map": {"version": 3, "names": ["w", "win", "r", "raf", "a", "printIonError", "createSlotMutationController", "el", "slotName", "mutationCallback", "hostMutationObserver", "slottedContentMutationObserver", "undefined", "slots", "Array", "isArray", "MutationObserver", "entries", "entry", "node", "addedNodes", "nodeType", "Node", "ELEMENT_NODE", "includes", "slot", "watchForSlotChange", "observe", "childList", "subtree", "slottedEl", "_a", "disconnect", "removedNodes", "destroySlottedContentObserver", "parentElement", "destroy", "getCounterText", "value", "max<PERSON><PERSON><PERSON>", "counterFormatter", "valueLength", "toString", "length", "defaultCounterText", "defaultCounterFormatter", "e", "maxlength", "c", "g"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/input.utils-09c71bc7.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\nimport { r as raf } from './helpers-da915de8.js';\nimport { a as printIonError } from './index-9b0d46f4.js';\n\n/**\n * Used to update a scoped component that uses emulated slots. This fires when\n * content is passed into the slot or when the content inside of a slot changes.\n * This is not needed for components using native slots in the Shadow DOM.\n * @internal\n * @param el The host element to observe\n * @param slotName mutationCallback will fire when nodes on these slot(s) change\n * @param mutationCallback The callback to fire whenever the slotted content changes\n */\nconst createSlotMutationController = (el, slotName, mutationCallback) => {\n    let hostMutationObserver;\n    let slottedContentMutationObserver;\n    if (win !== undefined && 'MutationObserver' in win) {\n        const slots = Array.isArray(slotName) ? slotName : [slotName];\n        hostMutationObserver = new MutationObserver((entries) => {\n            for (const entry of entries) {\n                for (const node of entry.addedNodes) {\n                    /**\n                     * Check to see if the added node\n                     *  is our slotted content.\n                     */\n                    if (node.nodeType === Node.ELEMENT_NODE && slots.includes(node.slot)) {\n                        /**\n                         * If so, we want to watch the slotted\n                         * content itself for changes. This lets us\n                         * detect when content inside of the slot changes.\n                         */\n                        mutationCallback();\n                        /**\n                         * Adding the listener in an raf\n                         * waits until Stencil moves the slotted element\n                         * into the correct place in the event that\n                         * slotted content is being added.\n                         */\n                        raf(() => watchForSlotChange(node));\n                        return;\n                    }\n                }\n            }\n        });\n        hostMutationObserver.observe(el, {\n            childList: true,\n            /**\n             * This fixes an issue with the `ion-input` and\n             * `ion-textarea` not re-rendering in some cases\n             * when using the label slot functionality.\n             *\n             * HTML element patches in Stencil that are enabled\n             * by the `experimentalSlotFixes` flag in Stencil v4\n             * result in DOM manipulations that won't trigger\n             * the current mutation observer configuration and\n             * callback.\n             */\n            subtree: true,\n        });\n    }\n    /**\n     * Listen for changes inside of the slotted content.\n     * We can listen for subtree changes here to be\n     * informed of text within the slotted content\n     * changing. Doing this on the host is possible\n     * but it is much more expensive to do because\n     * it also listens for changes to the internals\n     * of the component.\n     */\n    const watchForSlotChange = (slottedEl) => {\n        var _a;\n        if (slottedContentMutationObserver) {\n            slottedContentMutationObserver.disconnect();\n            slottedContentMutationObserver = undefined;\n        }\n        slottedContentMutationObserver = new MutationObserver((entries) => {\n            mutationCallback();\n            for (const entry of entries) {\n                for (const node of entry.removedNodes) {\n                    /**\n                     * If the element was removed then we\n                     * need to destroy the MutationObserver\n                     * so the element can be garbage collected.\n                     */\n                    if (node.nodeType === Node.ELEMENT_NODE && node.slot === slotName) {\n                        destroySlottedContentObserver();\n                    }\n                }\n            }\n        });\n        /**\n         * Listen for changes inside of the element\n         * as well as anything deep in the tree.\n         * We listen on the parentElement so that we can\n         * detect when slotted element itself is removed.\n         */\n        slottedContentMutationObserver.observe((_a = slottedEl.parentElement) !== null && _a !== void 0 ? _a : slottedEl, { subtree: true, childList: true });\n    };\n    const destroy = () => {\n        if (hostMutationObserver) {\n            hostMutationObserver.disconnect();\n            hostMutationObserver = undefined;\n        }\n        destroySlottedContentObserver();\n    };\n    const destroySlottedContentObserver = () => {\n        if (slottedContentMutationObserver) {\n            slottedContentMutationObserver.disconnect();\n            slottedContentMutationObserver = undefined;\n        }\n    };\n    return {\n        destroy,\n    };\n};\n\nconst getCounterText = (value, maxLength, counterFormatter) => {\n    const valueLength = value == null ? 0 : value.toString().length;\n    const defaultCounterText = defaultCounterFormatter(valueLength, maxLength);\n    /**\n     * If developers did not pass a custom formatter,\n     * use the default one.\n     */\n    if (counterFormatter === undefined) {\n        return defaultCounterText;\n    }\n    /**\n     * Otherwise, try to use the custom formatter\n     * and fallback to the default formatter if\n     * there was an error.\n     */\n    try {\n        return counterFormatter(valueLength, maxLength);\n    }\n    catch (e) {\n        printIonError('Exception in provided `counterFormatter`.', e);\n        return defaultCounterText;\n    }\n};\nconst defaultCounterFormatter = (length, maxlength) => {\n    return `${length} / ${maxlength}`;\n};\n\nexport { createSlotMutationController as c, getCounterText as g };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,GAAG,QAAQ,qBAAqB;AAC9C,SAASC,CAAC,IAAIC,GAAG,QAAQ,uBAAuB;AAChD,SAASC,CAAC,IAAIC,aAAa,QAAQ,qBAAqB;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,GAAGA,CAACC,EAAE,EAAEC,QAAQ,EAAEC,gBAAgB,KAAK;EACrE,IAAIC,oBAAoB;EACxB,IAAIC,8BAA8B;EAClC,IAAIV,GAAG,KAAKW,SAAS,IAAI,kBAAkB,IAAIX,GAAG,EAAE;IAChD,MAAMY,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;IAC7DE,oBAAoB,GAAG,IAAIM,gBAAgB,CAAEC,OAAO,IAAK;MACrD,KAAK,MAAMC,KAAK,IAAID,OAAO,EAAE;QACzB,KAAK,MAAME,IAAI,IAAID,KAAK,CAACE,UAAU,EAAE;UACjC;AACpB;AACA;AACA;UACoB,IAAID,IAAI,CAACE,QAAQ,KAAKC,IAAI,CAACC,YAAY,IAAIV,KAAK,CAACW,QAAQ,CAACL,IAAI,CAACM,IAAI,CAAC,EAAE;YAClE;AACxB;AACA;AACA;AACA;YACwBhB,gBAAgB,CAAC,CAAC;YAClB;AACxB;AACA;AACA;AACA;AACA;YACwBN,GAAG,CAAC,MAAMuB,kBAAkB,CAACP,IAAI,CAAC,CAAC;YACnC;UACJ;QACJ;MACJ;IACJ,CAAC,CAAC;IACFT,oBAAoB,CAACiB,OAAO,CAACpB,EAAE,EAAE;MAC7BqB,SAAS,EAAE,IAAI;MACf;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACYC,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMH,kBAAkB,GAAII,SAAS,IAAK;IACtC,IAAIC,EAAE;IACN,IAAIpB,8BAA8B,EAAE;MAChCA,8BAA8B,CAACqB,UAAU,CAAC,CAAC;MAC3CrB,8BAA8B,GAAGC,SAAS;IAC9C;IACAD,8BAA8B,GAAG,IAAIK,gBAAgB,CAAEC,OAAO,IAAK;MAC/DR,gBAAgB,CAAC,CAAC;MAClB,KAAK,MAAMS,KAAK,IAAID,OAAO,EAAE;QACzB,KAAK,MAAME,IAAI,IAAID,KAAK,CAACe,YAAY,EAAE;UACnC;AACpB;AACA;AACA;AACA;UACoB,IAAId,IAAI,CAACE,QAAQ,KAAKC,IAAI,CAACC,YAAY,IAAIJ,IAAI,CAACM,IAAI,KAAKjB,QAAQ,EAAE;YAC/D0B,6BAA6B,CAAC,CAAC;UACnC;QACJ;MACJ;IACJ,CAAC,CAAC;IACF;AACR;AACA;AACA;AACA;AACA;IACQvB,8BAA8B,CAACgB,OAAO,CAAC,CAACI,EAAE,GAAGD,SAAS,CAACK,aAAa,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,SAAS,EAAE;MAAED,OAAO,EAAE,IAAI;MAAED,SAAS,EAAE;IAAK,CAAC,CAAC;EACzJ,CAAC;EACD,MAAMQ,OAAO,GAAGA,CAAA,KAAM;IAClB,IAAI1B,oBAAoB,EAAE;MACtBA,oBAAoB,CAACsB,UAAU,CAAC,CAAC;MACjCtB,oBAAoB,GAAGE,SAAS;IACpC;IACAsB,6BAA6B,CAAC,CAAC;EACnC,CAAC;EACD,MAAMA,6BAA6B,GAAGA,CAAA,KAAM;IACxC,IAAIvB,8BAA8B,EAAE;MAChCA,8BAA8B,CAACqB,UAAU,CAAC,CAAC;MAC3CrB,8BAA8B,GAAGC,SAAS;IAC9C;EACJ,CAAC;EACD,OAAO;IACHwB;EACJ,CAAC;AACL,CAAC;AAED,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,KAAK;EAC3D,MAAMC,WAAW,GAAGH,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACI,QAAQ,CAAC,CAAC,CAACC,MAAM;EAC/D,MAAMC,kBAAkB,GAAGC,uBAAuB,CAACJ,WAAW,EAAEF,SAAS,CAAC;EAC1E;AACJ;AACA;AACA;EACI,IAAIC,gBAAgB,KAAK5B,SAAS,EAAE;IAChC,OAAOgC,kBAAkB;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI;IACA,OAAOJ,gBAAgB,CAACC,WAAW,EAAEF,SAAS,CAAC;EACnD,CAAC,CACD,OAAOO,CAAC,EAAE;IACNzC,aAAa,CAAC,2CAA2C,EAAEyC,CAAC,CAAC;IAC7D,OAAOF,kBAAkB;EAC7B;AACJ,CAAC;AACD,MAAMC,uBAAuB,GAAGA,CAACF,MAAM,EAAEI,SAAS,KAAK;EACnD,OAAO,GAAGJ,MAAM,MAAMI,SAAS,EAAE;AACrC,CAAC;AAED,SAASzC,4BAA4B,IAAI0C,CAAC,EAAEX,cAAc,IAAIY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}