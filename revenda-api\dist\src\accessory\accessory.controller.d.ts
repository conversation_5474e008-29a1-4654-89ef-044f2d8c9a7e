import { AccessoryService } from './accessory.service';
import { CreateAccessoryDto } from './dto/create-accessory.dto';
import { UpdateAccessoryDto } from './dto/update-accessory.dto';
export declare class AccessoryController {
    private readonly accessoryService;
    constructor(accessoryService: AccessoryService);
    create(createAccessoryDto: CreateAccessoryDto): Promise<import("./accessory.entity").Accessory>;
    findAll(): Promise<import("./accessory.entity").Accessory[]>;
    findOne(id: string): Promise<import("./accessory.entity").Accessory | null>;
    findByCategory(category: string): Promise<import("./accessory.entity").Accessory[]>;
    findInStock(): Promise<import("./accessory.entity").Accessory[]>;
    findAllInStock(): Promise<import("./accessory.entity").Accessory[]>;
    update(id: string, updateAccessoryDto: UpdateAccessoryDto): Promise<import("typeorm").UpdateResult>;
    updateStock(id: string, quantity: number): Promise<import("typeorm").UpdateResult>;
    remove(id: string): Promise<import("typeorm").DeleteResult>;
}
