{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host } from './index-28849c61.js';\nconst segmentContentCss = \":host{scroll-snap-align:center;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}\";\nconst IonSegmentContentStyle0 = segmentContentCss;\nconst SegmentContent = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  render() {\n    return h(Host, {\n      key: '03684b2999ac64fe13e376fd7e7f279976e9d4f2'\n    }, h(\"slot\", {\n      key: '143031075bf33ca19e7cfd76fc8a67b83ccaf11c'\n    }));\n  }\n};\nSegmentContent.style = IonSegmentContentStyle0;\nexport { SegmentContent as ion_segment_content };", "map": {"version": 3, "names": ["r", "registerInstance", "h", "f", "Host", "segmentContentCss", "IonSegmentContentStyle0", "SegmentContent", "constructor", "hostRef", "render", "key", "style", "ion_segment_content"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/ion-segment-content.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host } from './index-28849c61.js';\n\nconst segmentContentCss = \":host{scroll-snap-align:center;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%}\";\nconst IonSegmentContentStyle0 = segmentContentCss;\n\nconst SegmentContent = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        return (h(Host, { key: '03684b2999ac64fe13e376fd7e7f279976e9d4f2' }, h(\"slot\", { key: '143031075bf33ca19e7cfd76fc8a67b83ccaf11c' })));\n    }\n};\nSegmentContent.style = IonSegmentContentStyle0;\n\nexport { SegmentContent as ion_segment_content };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,QAAQ,qBAAqB;AAEzE,MAAMC,iBAAiB,GAAG,sGAAsG;AAChI,MAAMC,uBAAuB,GAAGD,iBAAiB;AAEjD,MAAME,cAAc,GAAG,MAAM;EACzBC,WAAWA,CAACC,OAAO,EAAE;IACjBR,gBAAgB,CAAC,IAAI,EAAEQ,OAAO,CAAC;EACnC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAQR,CAAC,CAACE,IAAI,EAAE;MAAEO,GAAG,EAAE;IAA2C,CAAC,EAAET,CAAC,CAAC,MAAM,EAAE;MAAES,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EACxI;AACJ,CAAC;AACDJ,cAAc,CAACK,KAAK,GAAGN,uBAAuB;AAE9C,SAASC,cAAc,IAAIM,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}