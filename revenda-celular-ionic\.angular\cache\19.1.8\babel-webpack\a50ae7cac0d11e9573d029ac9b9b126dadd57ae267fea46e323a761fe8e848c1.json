{"ast": null, "code": "var _AccessoryFormComponent;\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { ApplicationValidators } from '../../core/validators/url.validator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/accessory.service\";\nimport * as i2 from \"../../phones/services/phone.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@ionic/angular\";\nimport * as i5 from \"@angular/forms\";\nconst _forTrack0 = ($index, $item) => $item.id;\nfunction AccessoryFormComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00EDnimo 3 caracteres \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00E1ximo 100 caracteres \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00EDnimo 10 caracteres \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00E1ximo 500 caracteres \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O pre\\u00E7o deve ser maior ou igual a zero \");\n  }\n}\nfunction AccessoryFormComponent_For_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(category_r1);\n  }\n}\nfunction AccessoryFormComponent_Conditional_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo de imagem deve ser uma URL v\\u00E1lida \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O estoque deve ser maior ou igual a zero \");\n  }\n}\nfunction AccessoryFormComponent_Conditional_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O estoque deve ser um n\\u00FAmero inteiro \");\n  }\n}\nfunction AccessoryFormComponent_For_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const phone_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", phone_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(phone_r2.model);\n  }\n}\nexport class AccessoryFormComponent {\n  constructor(accessoryService, phoneService, router, activatedRoute, toastController) {\n    this.accessoryService = accessoryService;\n    this.phoneService = phoneService;\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    this.toastController = toastController;\n    this.accessoryForm = new FormGroup({\n      name: new FormControl('', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]),\n      description: new FormControl('', [Validators.required, Validators.minLength(10), Validators.maxLength(500)]),\n      price: new FormControl(0, [Validators.required, Validators.min(0)]),\n      category: new FormControl('', Validators.required),\n      image: new FormControl('', [Validators.required, ApplicationValidators.urlValidator]),\n      compatiblePhones: new FormControl([]),\n      stock: new FormControl(0, [Validators.required, Validators.min(0), Validators.pattern('^[0-9]*$')])\n    });\n    this.phones = [];\n    this.categories = ['Capa', 'Carregador', 'Fone de Ouvido', 'Película', 'Bateria Externa', 'Outro'];\n  }\n  ngOnInit() {\n    this.loadPhones();\n    const accessoryId = this.activatedRoute.snapshot.params['id'];\n    if (accessoryId) {\n      this.accessoryService.getById(+accessoryId).subscribe({\n        next: accessory => {\n          if (accessory) {\n            this.accessoryId = +accessoryId;\n            // Tratar preço como number\n            let priceValue = 0;\n            if (accessory.price) {\n              if (typeof accessory.price === 'number') {\n                priceValue = accessory.price;\n              } else if (typeof accessory.price === 'string') {\n                priceValue = parseFloat(accessory.price) || 0;\n              }\n            }\n            this.accessoryForm.patchValue({\n              name: accessory.name,\n              description: accessory.description,\n              price: priceValue,\n              category: accessory.category,\n              image: accessory.image,\n              compatiblePhones: accessory.compatiblePhones || [],\n              stock: accessory.stock\n            });\n          }\n        },\n        error: error => {\n          alert('Erro ao carregar o acessório com id ' + accessoryId);\n          console.error(error);\n        }\n      });\n    }\n  }\n  loadPhones() {\n    this.phoneService.getList().subscribe({\n      next: phones => {\n        this.phones = phones;\n      },\n      error: error => {\n        alert('Erro ao carregar lista de celulares');\n        console.error(error);\n      }\n    });\n  }\n  compareWith(o1, o2) {\n    return o1 && o2 ? o1.id === o2.id : o1 === o2;\n  }\n  hasError(field, error) {\n    var _formControl$errors;\n    const formControl = this.accessoryForm.get(field);\n    return !!(formControl !== null && formControl !== void 0 && formControl.touched) && !!(formControl !== null && formControl !== void 0 && (_formControl$errors = formControl.errors) !== null && _formControl$errors !== void 0 && _formControl$errors[error]);\n  }\n  save() {\n    let {\n      value\n    } = this.accessoryForm;\n    // Garantir que o preço seja number\n    value.price = Number(value.price) || 0;\n    // Garantir que o stock seja number\n    value.stock = Number(value.stock) || 0;\n    console.log('Salvando acessório - Dados do formulário:', value);\n    console.log('Celulares compatíveis selecionados:', value.compatiblePhones);\n    this.accessoryService.save({\n      ...value,\n      id: this.accessoryId\n    }).subscribe({\n      next: () => {\n        this.toastController.create({\n          message: 'Acessório salvo com sucesso!',\n          duration: 3000\n        }).then(toast => toast.present());\n        this.router.navigate(['/accessories']);\n      },\n      error: error => {\n        var _error$error;\n        let errorMessage = 'Erro ao salvar o acessório ' + value.name + '!';\n        if ((_error$error = error.error) !== null && _error$error !== void 0 && _error$error.message) {\n          errorMessage = error.error.message;\n        }\n        alert(errorMessage);\n        console.error(error);\n      }\n    });\n  }\n}\n_AccessoryFormComponent = AccessoryFormComponent;\n_AccessoryFormComponent.ɵfac = function AccessoryFormComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AccessoryFormComponent)(i0.ɵɵdirectiveInject(i1.AccessoryService), i0.ɵɵdirectiveInject(i2.PhoneService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.ToastController));\n};\n_AccessoryFormComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _AccessoryFormComponent,\n  selectors: [[\"app-accessory-form\"]],\n  standalone: false,\n  decls: 51,\n  vars: 20,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [\"defaultHref\", \"/accessories\"], [3, \"fullscreen\"], [1, \"form-container\"], [3, \"formGroup\"], [\"formControlName\", \"name\", \"labelPlacement\", \"floating\", \"label\", \"Nome\", \"type\", \"text\"], [\"formControlName\", \"description\", \"labelPlacement\", \"floating\", \"label\", \"Descri\\u00E7\\u00E3o\", \"rows\", \"3\"], [\"formControlName\", \"price\", \"labelPlacement\", \"floating\", \"label\", \"Pre\\u00E7o (R$)\", \"type\", \"number\", \"step\", \"0.01\", \"min\", \"0\"], [\"formControlName\", \"category\", \"labelPlacement\", \"floating\", \"label\", \"Categoria\"], [3, \"value\"], [\"formControlName\", \"image\", \"labelPlacement\", \"floating\", \"label\", \"Imagem (URL)\", \"type\", \"url\"], [\"formControlName\", \"stock\", \"labelPlacement\", \"floating\", \"label\", \"Estoque\", \"type\", \"number\", \"min\", \"0\"], [\"formControlName\", \"compatiblePhones\", \"label\", \"Celulares Compat\\u00EDveis\", \"labelPlacement\", \"floating\", \"multiple\", \"true\", 3, \"compareWith\"], [\"vertical\", \"bottom\", \"horizontal\", \"end\", \"slot\", \"fixed\"], [3, \"click\", \"disabled\"], [\"name\", \"checkmark\"]],\n  template: function AccessoryFormComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-back-button\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 4)(7, \"div\", 5)(8, \"form\", 6)(9, \"ion-list\")(10, \"ion-item\");\n      i0.ɵɵelement(11, \"ion-input\", 7);\n      i0.ɵɵelementStart(12, \"p\");\n      i0.ɵɵtemplate(13, AccessoryFormComponent_Conditional_13_Template, 1, 0)(14, AccessoryFormComponent_Conditional_14_Template, 1, 0)(15, AccessoryFormComponent_Conditional_15_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(16, \"ion-item\");\n      i0.ɵɵelement(17, \"ion-textarea\", 8);\n      i0.ɵɵelementStart(18, \"p\");\n      i0.ɵɵtemplate(19, AccessoryFormComponent_Conditional_19_Template, 1, 0)(20, AccessoryFormComponent_Conditional_20_Template, 1, 0)(21, AccessoryFormComponent_Conditional_21_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(22, \"ion-item\");\n      i0.ɵɵelement(23, \"ion-input\", 9);\n      i0.ɵɵelementStart(24, \"p\");\n      i0.ɵɵtemplate(25, AccessoryFormComponent_Conditional_25_Template, 1, 0)(26, AccessoryFormComponent_Conditional_26_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(27, \"ion-item\")(28, \"ion-select\", 10);\n      i0.ɵɵrepeaterCreate(29, AccessoryFormComponent_For_30_Template, 2, 2, \"ion-select-option\", 11, i0.ɵɵrepeaterTrackByIdentity);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(31, \"p\");\n      i0.ɵɵtemplate(32, AccessoryFormComponent_Conditional_32_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(33, \"ion-item\");\n      i0.ɵɵelement(34, \"ion-input\", 12);\n      i0.ɵɵelementStart(35, \"p\");\n      i0.ɵɵtemplate(36, AccessoryFormComponent_Conditional_36_Template, 1, 0)(37, AccessoryFormComponent_Conditional_37_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(38, \"ion-item\");\n      i0.ɵɵelement(39, \"ion-input\", 13);\n      i0.ɵɵelementStart(40, \"p\");\n      i0.ɵɵtemplate(41, AccessoryFormComponent_Conditional_41_Template, 1, 0)(42, AccessoryFormComponent_Conditional_42_Template, 1, 0)(43, AccessoryFormComponent_Conditional_43_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(44, \"ion-item\")(45, \"ion-select\", 14);\n      i0.ɵɵrepeaterCreate(46, AccessoryFormComponent_For_47_Template, 2, 2, \"ion-select-option\", 11, _forTrack0);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(48, \"ion-fab\", 15)(49, \"ion-fab-button\", 16);\n      i0.ɵɵlistener(\"click\", function AccessoryFormComponent_Template_ion_fab_button_click_49_listener() {\n        return ctx.save();\n      });\n      i0.ɵɵelement(50, \"ion-icon\", 17);\n      i0.ɵɵelementEnd()()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵtextInterpolate1(\"\", ctx.accessoryId ? \"Editar\" : \"Novo\", \" Acess\\u00F3rio\");\n      i0.ɵɵadvance();\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"formGroup\", ctx.accessoryForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"required\") ? 13 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"minlength\") ? 14 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"name\", \"maxlength\") ? 15 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"description\", \"required\") ? 19 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"description\", \"minlength\") ? 20 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"description\", \"maxlength\") ? 21 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"price\", \"required\") ? 25 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"price\", \"min\") ? 26 : -1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵrepeater(ctx.categories);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"category\", \"required\") ? 32 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"image\", \"required\") ? 36 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"image\", \"invalidUrl\") ? 37 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"stock\", \"required\") ? 41 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"stock\", \"min\") ? 42 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"stock\", \"pattern\") ? 43 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"compareWith\", ctx.compareWith);\n      i0.ɵɵadvance();\n      i0.ɵɵrepeater(ctx.phones);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"disabled\", ctx.accessoryForm.invalid);\n    }\n  },\n  dependencies: [i5.ɵNgNoValidate, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, i4.IonButtons, i4.IonContent, i4.IonFab, i4.IonFabButton, i4.IonHeader, i4.IonIcon, i4.IonInput, i4.IonItem, i4.IonList, i4.IonSelect, i4.IonSelectOption, i4.IonTextarea, i4.IonTitle, i4.IonToolbar, i4.NumericValueAccessor, i4.SelectValueAccessor, i4.TextValueAccessor, i4.IonBackButton, i4.IonMinValidator],\n  styles: [\".form-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\nion-item[_ngcontent-%COMP%] {\\n  --padding-start: 0;\\n  margin-bottom: 8px;\\n}\\nion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--ion-color-danger);\\n  padding-left: 16px;\\n  margin: 4px 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWNjZXNzb3JpZXMvYWNjZXNzb3J5LWZvcm0vYWNjZXNzb3J5LWZvcm0uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBQ0Y7O0FBRUE7RUFDRSxrQkFBQTtFQUNBLGtCQUFBO0FBQ0Y7QUFDRTtFQUNFLGVBQUE7RUFDQSw4QkFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsiLmZvcm0tY29udGFpbmVyIHtcclxuICBwYWRkaW5nOiAxNnB4O1xyXG4gIG1heC13aWR0aDogODAwcHg7XHJcbiAgbWFyZ2luOiAwIGF1dG87XHJcbn1cclxuXHJcbmlvbi1pdGVtIHtcclxuICAtLXBhZGRpbmctc3RhcnQ6IDA7XHJcbiAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gIFxyXG4gIHAge1xyXG4gICAgZm9udC1zaXplOiAxMnB4O1xyXG4gICAgY29sb3I6IHZhcigtLWlvbi1jb2xvci1kYW5nZXIpO1xyXG4gICAgcGFkZGluZy1sZWZ0OiAxNnB4O1xyXG4gICAgbWFyZ2luOiA0cHggMDtcclxuICB9XHJcbn1cclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuXHJcblxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n});", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "ApplicationValidators", "i0", "ɵɵtext", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵproperty", "category_r1", "ɵɵadvance", "ɵɵtextInterpolate", "phone_r2", "model", "AccessoryFormComponent", "constructor", "accessoryService", "phoneService", "router", "activatedRoute", "toastController", "accessoryForm", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "description", "price", "min", "category", "image", "urlValidator", "compatiblePhones", "stock", "pattern", "phones", "categories", "ngOnInit", "loadPhones", "accessoryId", "snapshot", "params", "getById", "subscribe", "next", "accessory", "priceValue", "parseFloat", "patchValue", "error", "alert", "console", "getList", "compareWith", "o1", "o2", "id", "<PERSON><PERSON><PERSON><PERSON>", "field", "_formControl$errors", "formControl", "get", "touched", "errors", "save", "value", "Number", "log", "create", "message", "duration", "then", "toast", "present", "navigate", "_error$error", "errorMessage", "ɵɵdirectiveInject", "i1", "AccessoryService", "i2", "PhoneService", "i3", "Router", "ActivatedRoute", "i4", "ToastController", "selectors", "standalone", "decls", "vars", "consts", "template", "AccessoryFormComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "AccessoryFormComponent_Conditional_13_Template", "AccessoryFormComponent_Conditional_14_Template", "AccessoryFormComponent_Conditional_15_Template", "AccessoryFormComponent_Conditional_19_Template", "AccessoryFormComponent_Conditional_20_Template", "AccessoryFormComponent_Conditional_21_Template", "AccessoryFormComponent_Conditional_25_Template", "AccessoryFormComponent_Conditional_26_Template", "ɵɵrepeaterCreate", "AccessoryFormComponent_For_30_Template", "ɵɵrepeaterTrackByIdentity", "AccessoryFormComponent_Conditional_32_Template", "AccessoryFormComponent_Conditional_36_Template", "AccessoryFormComponent_Conditional_37_Template", "AccessoryFormComponent_Conditional_41_Template", "AccessoryFormComponent_Conditional_42_Template", "AccessoryFormComponent_Conditional_43_Template", "AccessoryFormComponent_For_47_Template", "_forTrack0", "ɵɵlistener", "AccessoryFormComponent_Template_ion_fab_button_click_49_listener", "ɵɵtextInterpolate1", "ɵɵconditional", "ɵɵrepeater", "invalid"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\accessories\\accessory-form\\accessory-form.component.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\accessories\\accessory-form\\accessory-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ToastController } from '@ionic/angular';\r\nimport { AccessoryService } from '../services/accessory.service';\r\nimport { PhoneService } from '../../phones/services/phone.service';\r\nimport { Phone } from '../../phones/models/phone.type';\r\nimport { ApplicationValidators } from '../../core/validators/url.validator';\r\n\r\n\r\n@Component({\r\n  selector: 'app-accessory-form',\r\n  templateUrl: './accessory-form.component.html',\r\n  styleUrls: ['./accessory-form.component.scss'],\r\n  standalone: false,\r\n})\r\nexport class AccessoryFormComponent implements OnInit {\r\n\r\n  accessoryForm: FormGroup = new FormGroup({\r\n    name: new FormControl('', [\r\n      Validators.required, Validators.minLength(3), Validators.maxLength(100)\r\n    ]),\r\n    description: new FormControl('', [\r\n      Validators.required, Validators.minLength(10), Validators.maxLength(500)\r\n    ]),\r\n    price: new FormControl(0, [Validators.required, Validators.min(0)]),\r\n    category: new FormControl('', Validators.required),\r\n    image: new FormControl('', [\r\n      Validators.required,\r\n      ApplicationValidators.urlValidator\r\n    ]),\r\n    compatiblePhones: new FormControl([]),\r\n    stock: new FormControl(0, [\r\n      Validators.required, Validators.min(0), Validators.pattern('^[0-9]*$')\r\n    ])\r\n  });\r\n\r\n  accessoryId!: number;\r\n  phones: Phone[] = [];\r\n  categories: string[] = ['Capa', 'Carregador', 'Fone de Ouvido', 'Película', 'Bateria Externa', 'Outro'];\r\n\r\n  constructor(\r\n    private accessoryService: AccessoryService,\r\n    private phoneService: PhoneService,\r\n    private router: Router,\r\n    private activatedRoute: ActivatedRoute,\r\n    private toastController: ToastController\r\n  ) { }\r\n\r\n  ngOnInit() {\r\n    this.loadPhones();\r\n    \r\n    const accessoryId = this.activatedRoute.snapshot.params['id'];\r\n    if (accessoryId) {\r\n      this.accessoryService.getById(+accessoryId).subscribe({\r\n        next: (accessory) => {\r\n          if (accessory) {\r\n            this.accessoryId = +accessoryId;\r\n\r\n            // Tratar preço como number\r\n            let priceValue = 0;\r\n            if (accessory.price) {\r\n              if (typeof accessory.price === 'number') {\r\n                priceValue = accessory.price;\r\n              } else if (typeof accessory.price === 'string') {\r\n                priceValue = parseFloat(accessory.price) || 0;\r\n              }\r\n            }\r\n\r\n            this.accessoryForm.patchValue({\r\n              name: accessory.name,\r\n              description: accessory.description,\r\n              price: priceValue,\r\n              category: accessory.category,\r\n              image: accessory.image,\r\n              compatiblePhones: accessory.compatiblePhones || [],\r\n              stock: accessory.stock\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          alert('Erro ao carregar o acessório com id ' + accessoryId);\r\n          console.error(error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  loadPhones() {\r\n    this.phoneService.getList().subscribe({\r\n      next: (phones) => {\r\n        this.phones = phones;\r\n      },\r\n      error: (error) => {\r\n        alert('Erro ao carregar lista de celulares');\r\n        console.error(error);\r\n      }\r\n    });\r\n  }\r\n\r\n  compareWith(o1: Phone, o2: Phone): boolean {\r\n    return o1 && o2 ? o1.id === o2.id : o1 === o2;\r\n  }\r\n\r\n  hasError(field: string, error: string): boolean {\r\n    const formControl = this.accessoryForm.get(field);\r\n    return !!formControl?.touched && !!formControl?.errors?.[error];\r\n  }\r\n\r\n  save() {\r\n    let { value } = this.accessoryForm;\r\n\r\n    // Garantir que o preço seja number\r\n    value.price = Number(value.price) || 0;\r\n    // Garantir que o stock seja number\r\n    value.stock = Number(value.stock) || 0;\r\n\r\n    console.log('Salvando acessório - Dados do formulário:', value);\r\n    console.log('Celulares compatíveis selecionados:', value.compatiblePhones);\r\n\r\n    this.accessoryService.save({\r\n      ...value,\r\n      id: this.accessoryId\r\n    }).subscribe({\r\n      next: () => {\r\n        this.toastController.create({\r\n          message: 'Acessório salvo com sucesso!',\r\n          duration: 3000,\r\n        }).then(toast => toast.present());\r\n        this.router.navigate(['/accessories']);\r\n      },\r\n      error: (error) => {\r\n        let errorMessage = 'Erro ao salvar o acessório ' + value.name + '!';\r\n        if (error.error?.message) {\r\n          errorMessage = error.error.message;\r\n        }\r\n        alert(errorMessage);\r\n        console.error(error);\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-back-button defaultHref=\"/accessories\"></ion-back-button>\r\n    </ion-buttons>\r\n    <ion-title>{{ accessoryId ? 'Editar' : 'Novo' }} Acessório</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div class=\"form-container\">\r\n    <form [formGroup]=\"accessoryForm\">\r\n      <ion-list>\r\n        <ion-item>\r\n          <ion-input formControlName=\"name\" labelPlacement=\"floating\" label=\"Nome\" type=\"text\"></ion-input>\r\n          <p>\r\n            @if(hasError('name', 'required')) {\r\n              O campo é obrigatório\r\n            }\r\n            @if(hasError('name', 'minlength')) {\r\n              O campo deve ter no mínimo 3 caracteres\r\n            }\r\n            @if(hasError('name', 'maxlength')) {\r\n              O campo deve ter no máximo 100 caracteres\r\n            }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-textarea formControlName=\"description\" labelPlacement=\"floating\" label=\"Descrição\" rows=\"3\"></ion-textarea>\r\n          <p>\r\n            @if(hasError('description', 'required')) {\r\n              O campo é obrigatório\r\n            }\r\n            @if(hasError('description', 'minlength')) {\r\n              O campo deve ter no mínimo 10 caracteres\r\n            }\r\n            @if(hasError('description', 'maxlength')) {\r\n              O campo deve ter no máximo 500 caracteres\r\n            }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"price\" labelPlacement=\"floating\" label=\"Preço (R$)\" type=\"number\" step=\"0.01\" min=\"0\"></ion-input>\r\n          <p>\r\n            @if(hasError('price', 'required')) {\r\n              O campo é obrigatório\r\n            }\r\n            @if(hasError('price', 'min')) {\r\n              O preço deve ser maior ou igual a zero\r\n            }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-select formControlName=\"category\" labelPlacement=\"floating\" label=\"Categoria\">\r\n            @for(category of categories; track category) {\r\n              <ion-select-option [value]=\"category\">{{ category }}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n            @if(hasError('category', 'required')) {\r\n              O campo é obrigatório\r\n            }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"image\" labelPlacement=\"floating\" label=\"Imagem (URL)\" type=\"url\"></ion-input>\r\n          <p>\r\n            @if(hasError('image', 'required')) {\r\n              O campo é obrigatório\r\n            }\r\n            @if(hasError('image', 'invalidUrl')) {\r\n              O campo de imagem deve ser uma URL válida\r\n            }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-input formControlName=\"stock\" labelPlacement=\"floating\" label=\"Estoque\" type=\"number\" min=\"0\"></ion-input>\r\n          <p>\r\n            @if(hasError('stock', 'required')) {\r\n              O campo é obrigatório\r\n            }\r\n            @if(hasError('stock', 'min')) {\r\n              O estoque deve ser maior ou igual a zero\r\n            }\r\n            @if(hasError('stock', 'pattern')) {\r\n              O estoque deve ser um número inteiro\r\n            }\r\n          </p>\r\n        </ion-item>\r\n        \r\n        <ion-item>\r\n          <ion-select formControlName=\"compatiblePhones\" [compareWith]=\"compareWith\" label=\"Celulares Compatíveis\" \r\n            labelPlacement=\"floating\" multiple=\"true\">\r\n            @for(phone of phones; track phone.id) {\r\n              <ion-select-option [value]=\"phone\">{{phone.model}}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n        </ion-item>\r\n      </ion-list>\r\n      \r\n      <ion-fab vertical=\"bottom\" horizontal=\"end\" slot=\"fixed\">\r\n        <ion-fab-button [disabled]=\"accessoryForm.invalid\" (click)=\"save()\">\r\n          <ion-icon name=\"checkmark\"></ion-icon>\r\n        </ion-fab-button>\r\n      </ion-fab>\r\n    </form>\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gBAAgB;AAMnE,SAASC,qBAAqB,QAAQ,qCAAqC;;;;;;;;;;ICU7DC,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,qDACF;;;;;IAEED,EAAA,CAAAC,MAAA,uDACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,sDACF;;;;;IAEED,EAAA,CAAAC,MAAA,uDACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,oDACF;;;;;IAOED,EAAA,CAAAE,cAAA,4BAAsC;IAAAF,EAAA,CAAAC,MAAA,GAAc;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAArDH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAkB;IAACL,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,iBAAA,CAAAF,WAAA,CAAc;;;;;IAKpDL,EAAA,CAAAC,MAAA,wCACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,uDACF;;;;;IAQED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,iDACF;;;;;IAEED,EAAA,CAAAC,MAAA,kDACF;;;;;IAQED,EAAA,CAAAE,cAAA,4BAAmC;IAAAF,EAAA,CAAAC,MAAA,GAAe;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAAnDH,EAAA,CAAAI,UAAA,UAAAI,QAAA,CAAe;IAACR,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAO,iBAAA,CAAAC,QAAA,CAAAC,KAAA,CAAe;;;ADnFhE,OAAM,MAAOC,sBAAsB;EAyBjCC,YACUC,gBAAkC,EAClCC,YAA0B,EAC1BC,MAAc,EACdC,cAA8B,EAC9BC,eAAgC;IAJhC,KAAAJ,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IA5BzB,KAAAC,aAAa,GAAc,IAAIpB,SAAS,CAAC;MACvCqB,IAAI,EAAE,IAAItB,WAAW,CAAC,EAAE,EAAE,CACxBE,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAACsB,SAAS,CAAC,CAAC,CAAC,EAAEtB,UAAU,CAACuB,SAAS,CAAC,GAAG,CAAC,CACxE,CAAC;MACFC,WAAW,EAAE,IAAI1B,WAAW,CAAC,EAAE,EAAE,CAC/BE,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAACsB,SAAS,CAAC,EAAE,CAAC,EAAEtB,UAAU,CAACuB,SAAS,CAAC,GAAG,CAAC,CACzE,CAAC;MACFE,KAAK,EAAE,IAAI3B,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAAC0B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACnEC,QAAQ,EAAE,IAAI7B,WAAW,CAAC,EAAE,EAAEE,UAAU,CAACqB,QAAQ,CAAC;MAClDO,KAAK,EAAE,IAAI9B,WAAW,CAAC,EAAE,EAAE,CACzBE,UAAU,CAACqB,QAAQ,EACnBpB,qBAAqB,CAAC4B,YAAY,CACnC,CAAC;MACFC,gBAAgB,EAAE,IAAIhC,WAAW,CAAC,EAAE,CAAC;MACrCiC,KAAK,EAAE,IAAIjC,WAAW,CAAC,CAAC,EAAE,CACxBE,UAAU,CAACqB,QAAQ,EAAErB,UAAU,CAAC0B,GAAG,CAAC,CAAC,CAAC,EAAE1B,UAAU,CAACgC,OAAO,CAAC,UAAU,CAAC,CACvE;KACF,CAAC;IAGF,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAC,UAAU,GAAa,CAAC,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,UAAU,EAAE,iBAAiB,EAAE,OAAO,CAAC;EAQnG;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;IAEjB,MAAMC,WAAW,GAAG,IAAI,CAACpB,cAAc,CAACqB,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;IAC7D,IAAIF,WAAW,EAAE;MACf,IAAI,CAACvB,gBAAgB,CAAC0B,OAAO,CAAC,CAACH,WAAW,CAAC,CAACI,SAAS,CAAC;QACpDC,IAAI,EAAGC,SAAS,IAAI;UAClB,IAAIA,SAAS,EAAE;YACb,IAAI,CAACN,WAAW,GAAG,CAACA,WAAW;YAE/B;YACA,IAAIO,UAAU,GAAG,CAAC;YAClB,IAAID,SAAS,CAAClB,KAAK,EAAE;cACnB,IAAI,OAAOkB,SAAS,CAAClB,KAAK,KAAK,QAAQ,EAAE;gBACvCmB,UAAU,GAAGD,SAAS,CAAClB,KAAK;cAC9B,CAAC,MAAM,IAAI,OAAOkB,SAAS,CAAClB,KAAK,KAAK,QAAQ,EAAE;gBAC9CmB,UAAU,GAAGC,UAAU,CAACF,SAAS,CAAClB,KAAK,CAAC,IAAI,CAAC;cAC/C;YACF;YAEA,IAAI,CAACN,aAAa,CAAC2B,UAAU,CAAC;cAC5B1B,IAAI,EAAEuB,SAAS,CAACvB,IAAI;cACpBI,WAAW,EAAEmB,SAAS,CAACnB,WAAW;cAClCC,KAAK,EAAEmB,UAAU;cACjBjB,QAAQ,EAAEgB,SAAS,CAAChB,QAAQ;cAC5BC,KAAK,EAAEe,SAAS,CAACf,KAAK;cACtBE,gBAAgB,EAAEa,SAAS,CAACb,gBAAgB,IAAI,EAAE;cAClDC,KAAK,EAAEY,SAAS,CAACZ;aAClB,CAAC;UACJ;QACF,CAAC;QACDgB,KAAK,EAAGA,KAAK,IAAI;UACfC,KAAK,CAAC,sCAAsC,GAAGX,WAAW,CAAC;UAC3DY,OAAO,CAACF,KAAK,CAACA,KAAK,CAAC;QACtB;OACD,CAAC;IACJ;EACF;EAEAX,UAAUA,CAAA;IACR,IAAI,CAACrB,YAAY,CAACmC,OAAO,EAAE,CAACT,SAAS,CAAC;MACpCC,IAAI,EAAGT,MAAM,IAAI;QACf,IAAI,CAACA,MAAM,GAAGA,MAAM;MACtB,CAAC;MACDc,KAAK,EAAGA,KAAK,IAAI;QACfC,KAAK,CAAC,qCAAqC,CAAC;QAC5CC,OAAO,CAACF,KAAK,CAACA,KAAK,CAAC;MACtB;KACD,CAAC;EACJ;EAEAI,WAAWA,CAACC,EAAS,EAAEC,EAAS;IAC9B,OAAOD,EAAE,IAAIC,EAAE,GAAGD,EAAE,CAACE,EAAE,KAAKD,EAAE,CAACC,EAAE,GAAGF,EAAE,KAAKC,EAAE;EAC/C;EAEAE,QAAQA,CAACC,KAAa,EAAET,KAAa;IAAA,IAAAU,mBAAA;IACnC,MAAMC,WAAW,GAAG,IAAI,CAACvC,aAAa,CAACwC,GAAG,CAACH,KAAK,CAAC;IACjD,OAAO,CAAC,EAACE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEE,OAAO,KAAI,CAAC,EAACF,WAAW,aAAXA,WAAW,gBAAAD,mBAAA,GAAXC,WAAW,CAAEG,MAAM,cAAAJ,mBAAA,eAAnBA,mBAAA,CAAsBV,KAAK,CAAC;EACjE;EAEAe,IAAIA,CAAA;IACF,IAAI;MAAEC;IAAK,CAAE,GAAG,IAAI,CAAC5C,aAAa;IAElC;IACA4C,KAAK,CAACtC,KAAK,GAAGuC,MAAM,CAACD,KAAK,CAACtC,KAAK,CAAC,IAAI,CAAC;IACtC;IACAsC,KAAK,CAAChC,KAAK,GAAGiC,MAAM,CAACD,KAAK,CAAChC,KAAK,CAAC,IAAI,CAAC;IAEtCkB,OAAO,CAACgB,GAAG,CAAC,2CAA2C,EAAEF,KAAK,CAAC;IAC/Dd,OAAO,CAACgB,GAAG,CAAC,qCAAqC,EAAEF,KAAK,CAACjC,gBAAgB,CAAC;IAE1E,IAAI,CAAChB,gBAAgB,CAACgD,IAAI,CAAC;MACzB,GAAGC,KAAK;MACRT,EAAE,EAAE,IAAI,CAACjB;KACV,CAAC,CAACI,SAAS,CAAC;MACXC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACxB,eAAe,CAACgD,MAAM,CAAC;UAC1BC,OAAO,EAAE,8BAA8B;UACvCC,QAAQ,EAAE;SACX,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;QACjC,IAAI,CAACvD,MAAM,CAACwD,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;MACxC,CAAC;MACDzB,KAAK,EAAGA,KAAK,IAAI;QAAA,IAAA0B,YAAA;QACf,IAAIC,YAAY,GAAG,6BAA6B,GAAGX,KAAK,CAAC3C,IAAI,GAAG,GAAG;QACnE,KAAAqD,YAAA,GAAI1B,KAAK,CAACA,KAAK,cAAA0B,YAAA,eAAXA,YAAA,CAAaN,OAAO,EAAE;UACxBO,YAAY,GAAG3B,KAAK,CAACA,KAAK,CAACoB,OAAO;QACpC;QACAnB,KAAK,CAAC0B,YAAY,CAAC;QACnBzB,OAAO,CAACF,KAAK,CAACA,KAAK,CAAC;MACtB;KACD,CAAC;EACJ;;0BA5HWnC,sBAAsB;;mCAAtBA,uBAAsB,EAAAV,EAAA,CAAAyE,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA3E,EAAA,CAAAyE,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAA7E,EAAA,CAAAyE,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAA/E,EAAA,CAAAyE,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAAhF,EAAA,CAAAyE,iBAAA,CAAAQ,EAAA,CAAAC,eAAA;AAAA;;QAAtBxE,uBAAsB;EAAAyE,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCd/B1F,EAFJ,CAAAE,cAAA,oBAAiC,qBACA,qBACH;MACxBF,EAAA,CAAA4F,SAAA,yBAA8D;MAChE5F,EAAA,CAAAG,YAAA,EAAc;MACdH,EAAA,CAAAE,cAAA,gBAAW;MAAAF,EAAA,CAAAC,MAAA,GAA+C;MAE9DD,EAF8D,CAAAG,YAAA,EAAY,EAC1D,EACH;MAMLH,EAJR,CAAAE,cAAA,qBAAiC,aACH,cACQ,eACtB,gBACE;MACRF,EAAA,CAAA4F,SAAA,oBAAiG;MACjG5F,EAAA,CAAAE,cAAA,SAAG;MAODF,EANA,CAAA6F,UAAA,KAAAC,8CAAA,OAAmC,KAAAC,8CAAA,OAGC,KAAAC,8CAAA,OAGA;MAIxChG,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA4F,SAAA,uBAAgH;MAChH5F,EAAA,CAAAE,cAAA,SAAG;MAODF,EANA,CAAA6F,UAAA,KAAAI,8CAAA,OAA0C,KAAAC,8CAAA,OAGC,KAAAC,8CAAA,OAGA;MAI/CnG,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA4F,SAAA,oBAA8H;MAC9H5F,EAAA,CAAAE,cAAA,SAAG;MAIDF,EAHA,CAAA6F,UAAA,KAAAO,8CAAA,OAAoC,KAAAC,8CAAA,OAGL;MAInCrG,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,sBAC2E;MACjFF,EAAA,CAAAsG,gBAAA,KAAAC,sCAAA,iCAAAvG,EAAA,CAAAwG,yBAAA,CAEC;MACHxG,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACDF,EAAA,CAAA6F,UAAA,KAAAY,8CAAA,OAAuC;MAI3CzG,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA4F,SAAA,qBAAyG;MACzG5F,EAAA,CAAAE,cAAA,SAAG;MAIDF,EAHA,CAAA6F,UAAA,KAAAa,8CAAA,OAAoC,KAAAC,8CAAA,OAGE;MAI1C3G,EADE,CAAAG,YAAA,EAAI,EACK;MAEXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA4F,SAAA,qBAA+G;MAC/G5F,EAAA,CAAAE,cAAA,SAAG;MAODF,EANA,CAAA6F,UAAA,KAAAe,8CAAA,OAAoC,KAAAC,8CAAA,OAGL,KAAAC,8CAAA,OAGI;MAIvC9G,EADE,CAAAG,YAAA,EAAI,EACK;MAGTH,EADF,CAAAE,cAAA,gBAAU,sBAEoC;MAC1CF,EAAA,CAAAsG,gBAAA,KAAAS,sCAAA,iCAAAC,UAAA,CAEC;MAGPhH,EAFI,CAAAG,YAAA,EAAa,EACJ,EACF;MAGTH,EADF,CAAAE,cAAA,mBAAyD,0BACa;MAAjBF,EAAA,CAAAiH,UAAA,mBAAAC,iEAAA;QAAA,OAASvB,GAAA,CAAA/B,IAAA,EAAM;MAAA,EAAC;MACjE5D,EAAA,CAAA4F,SAAA,oBAAsC;MAKhD5F,EAJQ,CAAAG,YAAA,EAAiB,EACT,EACL,EACH,EACM;;;MAhHFH,EAAA,CAAAI,UAAA,qBAAoB;MAKjBJ,EAAA,CAAAM,SAAA,GAA+C;MAA/CN,EAAA,CAAAmH,kBAAA,KAAAxB,GAAA,CAAAxD,WAAA,wCAA+C;MAIjDnC,EAAA,CAAAM,SAAA,EAAmB;MAAnBN,EAAA,CAAAI,UAAA,oBAAmB;MAEtBJ,EAAA,CAAAM,SAAA,GAA2B;MAA3BN,EAAA,CAAAI,UAAA,cAAAuF,GAAA,CAAA1E,aAAA,CAA2B;MAKzBjB,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,+BAEC;MACDrD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,gCAEC;MACDrD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,gCAEC;MAODrD,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,sCAEC;MACDrD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,uCAEC;MACDrD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,uCAEC;MAODrD,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,gCAEC;MACDrD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,2BAEC;MAMDrD,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAqH,UAAA,CAAA1B,GAAA,CAAA3D,UAAA,CAEC;MAGDhC,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,mCAEC;MAODrD,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,gCAEC;MACDrD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,kCAEC;MAODrD,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,gCAEC;MACDrD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,2BAEC;MACDrD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAoH,aAAA,CAAAzB,GAAA,CAAAtC,QAAA,+BAEC;MAK4CrD,EAAA,CAAAM,SAAA,GAA2B;MAA3BN,EAAA,CAAAI,UAAA,gBAAAuF,GAAA,CAAA1C,WAAA,CAA2B;MAExEjD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAqH,UAAA,CAAA1B,GAAA,CAAA5D,MAAA,CAEC;MAMW/B,EAAA,CAAAM,SAAA,GAAkC;MAAlCN,EAAA,CAAAI,UAAA,aAAAuF,GAAA,CAAA1E,aAAA,CAAAqG,OAAA,CAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}