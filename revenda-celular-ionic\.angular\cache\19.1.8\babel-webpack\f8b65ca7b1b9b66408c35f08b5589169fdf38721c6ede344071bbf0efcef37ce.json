{"ast": null, "code": "var _BrandService;\nimport { environment } from \"../../../environments/environment\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class BrandService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.baseUrl}/brands`;\n  }\n  getBrands() {\n    return this.http.get(this.apiUrl);\n  }\n  getById(brandId) {\n    return this.http.get(`${this.apiUrl}/${brandId}`);\n  }\n  add(brand) {\n    return this.http.post(this.apiUrl, brand);\n  }\n  update(brand) {\n    return this.http.put(`${this.apiUrl}/${brand.id}`, brand);\n  }\n  save(brand) {\n    return brand.id ? this.update(brand) : this.add(brand);\n  }\n  remove(brand) {\n    return this.http.delete(`${this.apiUrl}/${brand.id}`);\n  }\n}\n_BrandService = BrandService;\n_BrandService.ɵfac = function BrandService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BrandService)(i0.ɵɵinject(i1.HttpClient));\n};\n_BrandService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _BrandService,\n  factory: _BrandService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["environment", "BrandService", "constructor", "http", "apiUrl", "baseUrl", "getBrands", "get", "getById", "brandId", "add", "brand", "post", "update", "put", "id", "save", "remove", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\brands\\services\\brand.service.ts"], "sourcesContent": ["import { HttpClient } from \"@angular/common/http\";\r\nimport { Injectable } from \"@angular/core\";\r\nimport { Brand } from \"../models/brand.type\";\r\nimport { Observable } from \"rxjs\";\r\nimport { environment } from \"../../../environments/environment\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class BrandService {\r\n  private readonly apiUrl = `${environment.baseUrl}/brands`;\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getBrands(): Observable<Brand[]> {\r\n    return this.http.get<Brand[]>(this.apiUrl);\r\n  }\r\n\r\n  getById(brandId: number): Observable<Brand> {\r\n    return this.http.get<Brand>(`${this.apiUrl}/${brandId}`);\r\n  }\r\n\r\n  private add(brand: Brand): Observable<Brand> {\r\n    return this.http.post<Brand>(this.apiUrl, brand);\r\n  }\r\n\r\n  private update(brand: Brand): Observable<Brand> {\r\n    return this.http.put<Brand>(`${this.apiUrl}/${brand.id}`, brand);\r\n  }\r\n\r\n  save(brand: Brand): Observable<Brand> {\r\n    return brand.id ? this.update(brand) : this.add(brand);\r\n  }\r\n\r\n  remove(brand: Brand): Observable<Brand> {\r\n    return this.http.delete<Brand>(`${this.apiUrl}/${brand.id}`);\r\n  }\r\n}\r\n"], "mappings": ";AAIA,SAASA,WAAW,QAAQ,mCAAmC;;;AAK/D,OAAM,MAAOC,YAAY;EAGvBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,OAAO,SAAS;EAElB;EAEvCC,SAASA,CAAA;IACP,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAU,IAAI,CAACH,MAAM,CAAC;EAC5C;EAEAI,OAAOA,CAACC,OAAe;IACrB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAQ,GAAG,IAAI,CAACH,MAAM,IAAIK,OAAO,EAAE,CAAC;EAC1D;EAEQC,GAAGA,CAACC,KAAY;IACtB,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAQ,IAAI,CAACR,MAAM,EAAEO,KAAK,CAAC;EAClD;EAEQE,MAAMA,CAACF,KAAY;IACzB,OAAO,IAAI,CAACR,IAAI,CAACW,GAAG,CAAQ,GAAG,IAAI,CAACV,MAAM,IAAIO,KAAK,CAACI,EAAE,EAAE,EAAEJ,KAAK,CAAC;EAClE;EAEAK,IAAIA,CAACL,KAAY;IACf,OAAOA,KAAK,CAACI,EAAE,GAAG,IAAI,CAACF,MAAM,CAACF,KAAK,CAAC,GAAG,IAAI,CAACD,GAAG,CAACC,KAAK,CAAC;EACxD;EAEAM,MAAMA,CAACN,KAAY;IACjB,OAAO,IAAI,CAACR,IAAI,CAACe,MAAM,CAAQ,GAAG,IAAI,CAACd,MAAM,IAAIO,KAAK,CAACI,EAAE,EAAE,CAAC;EAC9D;;gBA3BWd,YAAY;;mCAAZA,aAAY,EAAAkB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAZrB,aAAY;EAAAsB,OAAA,EAAZtB,aAAY,CAAAuB,IAAA;EAAAC,UAAA,EAFX;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}