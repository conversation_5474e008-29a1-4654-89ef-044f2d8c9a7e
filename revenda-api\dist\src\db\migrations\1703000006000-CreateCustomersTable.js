"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCustomersTable1703000006000 = void 0;
const typeorm_1 = require("typeorm");
class CreateCustomersTable1703000006000 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'customers',
            columns: [
                {
                    name: 'id',
                    type: 'int',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '200',
                },
                {
                    name: 'email',
                    type: 'varchar',
                    length: '200',
                    isUnique: true,
                },
                {
                    name: 'phone',
                    type: 'varchar',
                    length: '20',
                },
                {
                    name: 'birthDate',
                    type: 'date',
                },
                {
                    name: 'address',
                    type: 'varchar',
                    length: '500',
                },
                {
                    name: 'customerType',
                    type: 'enum',
                    enum: ['regular', 'premium', 'vip'],
                    default: "'regular'",
                },
                {
                    name: 'active',
                    type: 'boolean',
                    default: true,
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('customers');
    }
}
exports.CreateCustomersTable1703000006000 = CreateCustomersTable1703000006000;
//# sourceMappingURL=1703000006000-CreateCustomersTable.js.map