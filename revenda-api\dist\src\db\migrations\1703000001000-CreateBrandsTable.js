"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateBrandsTable1703000001000 = void 0;
const typeorm_1 = require("typeorm");
class CreateBrandsTable1703000001000 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'brands',
            columns: [
                {
                    name: 'id',
                    type: 'int',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '100',
                },
                {
                    name: 'country',
                    type: 'varchar',
                    length: '100',
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('brands');
    }
}
exports.CreateBrandsTable1703000001000 = CreateBrandsTable1703000001000;
//# sourceMappingURL=1703000001000-CreateBrandsTable.js.map