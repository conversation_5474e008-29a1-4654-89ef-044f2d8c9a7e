{"ast": null, "code": "var _StoresPage;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/store.service\";\nimport * as i2 from \"@ionic/angular\";\nimport * as i3 from \"@angular/router\";\nconst _c0 = () => [\"new\"];\nconst _c1 = a0 => [\"edit\", a0];\nconst _forTrack0 = ($index, $item) => $item.id;\nfunction StoresPage_For_13_Conditional_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-badge\", 13);\n    i0.ɵɵtext(1, \"Matriz\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StoresPage_For_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-item\")(1, \"ion-avatar\", 2)(2, \"div\", 9);\n    i0.ɵɵelement(3, \"ion-icon\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"ion-label\")(5, \"h2\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\")(8, \"strong\");\n    i0.ɵɵtext(9, \"Endere\\u00E7o:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\")(12, \"strong\");\n    i0.ɵɵtext(13, \"Contato:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\")(16, \"strong\");\n    i0.ɵɵtext(17, \"Gerente:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 11)(20, \"ion-badge\", 12);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, StoresPage_For_13_Conditional_22_Template, 2, 0, \"ion-badge\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"ion-button\", 14);\n    i0.ɵɵelement(24, \"ion-icon\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"ion-button\", 16);\n    i0.ɵɵlistener(\"click\", function StoresPage_For_13_Template_ion_button_click_25_listener() {\n      const store_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.remove(store_r2));\n    });\n    i0.ɵɵelement(26, \"ion-icon\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const store_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(store_r2.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \", store_r2.address, \", \", store_r2.city, \"/\", store_r2.state, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", store_r2.phone, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", store_r2.manager, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", ctx_r2.getStatusColor(store_r2.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getStatusLabel(store_r2.status));\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(store_r2.isHeadquarters ? 22 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(10, _c1, store_r2.id));\n  }\n}\nfunction StoresPage_ForEmpty_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\");\n    i0.ɵɵtext(1, \"Lista de lojas vazia, cadastre uma nova loja!\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class StoresPage {\n  constructor(storeService, alertController, toastController) {\n    this.storeService = storeService;\n    this.alertController = alertController;\n    this.toastController = toastController;\n    this.storesList = [];\n  }\n  ionViewDidLeave() {\n    console.log('ionViewDidLeave');\n  }\n  ionViewWillLeave() {\n    console.log('ionViewWillLeave');\n  }\n  ionViewDidEnter() {\n    console.log('ionViewDidEnter');\n  }\n  ionViewWillEnter() {\n    console.log('ionViewWillEnter');\n    this.storeService.getList().subscribe({\n      next: response => {\n        this.storesList = response;\n      },\n      error: error => {\n        alert('Erro ao carregar lista de lojas');\n        console.error(error);\n      }\n    });\n  }\n  ngOnInit() {}\n  getStatusColor(status) {\n    switch (status) {\n      case 'active':\n        return 'success';\n      case 'inactive':\n        return 'danger';\n      case 'underMaintenance':\n        return 'warning';\n      default:\n        return 'medium';\n    }\n  }\n  getStatusLabel(status) {\n    switch (status) {\n      case 'active':\n        return 'Ativa';\n      case 'inactive':\n        return 'Inativa';\n      case 'underMaintenance':\n        return 'Em Manutenção';\n      default:\n        return status;\n    }\n  }\n  remove(store) {\n    this.alertController.create({\n      header: 'Exclusão',\n      message: `Confirma a exclusão da loja ${store.name}?`,\n      buttons: [{\n        text: 'Sim',\n        handler: () => {\n          this.storeService.remove(store).subscribe({\n            next: () => {\n              // Remover da lista usando o ID da loja original\n              this.storesList = this.storesList.filter(s => s.id !== store.id);\n              this.toastController.create({\n                message: `Loja ${store.name} excluída com sucesso!`,\n                duration: 3000,\n                color: 'secondary',\n                keyboardClose: true\n              }).then(toast => toast.present());\n            },\n            error: error => {\n              var _error$error;\n              let errorMessage = 'Erro ao excluir a loja ' + store.name;\n              if ((_error$error = error.error) !== null && _error$error !== void 0 && _error$error.message) {\n                errorMessage = error.error.message;\n              }\n              alert(errorMessage);\n              console.error(error);\n            }\n          });\n        }\n      }, 'Não']\n    }).then(alert => alert.present());\n  }\n}\n_StoresPage = StoresPage;\n_StoresPage.ɵfac = function StoresPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _StoresPage)(i0.ɵɵdirectiveInject(i1.StoreService), i0.ɵɵdirectiveInject(i2.AlertController), i0.ɵɵdirectiveInject(i2.ToastController));\n};\n_StoresPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _StoresPage,\n  selectors: [[\"app-stores\"]],\n  standalone: false,\n  decls: 18,\n  vars: 5,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [3, \"fullscreen\"], [\"collapse\", \"condense\"], [\"size\", \"large\"], [\"vertical\", \"bottom\", \"horizontal\", \"end\", \"slot\", \"fixed\"], [3, \"routerLink\"], [\"name\", \"add\"], [1, \"store-avatar\"], [\"name\", \"storefront-outline\"], [1, \"badges\"], [3, \"color\"], [\"color\", \"primary\"], [\"slot\", \"end\", \"size\", \"small\", 3, \"routerLink\"], [\"name\", \"create\", \"slot\", \"icon-only\"], [\"slot\", \"end\", \"size\", \"small\", \"color\", \"danger\", 3, \"click\"], [\"name\", \"trash\", \"slot\", \"icon-only\"]],\n  template: function StoresPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-menu-button\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5, \"Lojas\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 3)(7, \"ion-header\", 4)(8, \"ion-toolbar\")(9, \"ion-title\", 5);\n      i0.ɵɵtext(10, \"Lojas\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(11, \"ion-list\");\n      i0.ɵɵrepeaterCreate(12, StoresPage_For_13_Template, 27, 12, \"ion-item\", null, _forTrack0, false, StoresPage_ForEmpty_14_Template, 2, 0, \"ion-item\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"ion-fab\", 6)(16, \"ion-fab-button\", 7);\n      i0.ɵɵelement(17, \"ion-icon\", 8);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵrepeater(ctx.storesList);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(4, _c0));\n    }\n  },\n  dependencies: [i2.IonAvatar, i2.IonBadge, i2.IonButton, i2.IonButtons, i2.IonContent, i2.IonFab, i2.IonFabButton, i2.IonHeader, i2.IonIcon, i2.IonItem, i2.IonLabel, i2.IonList, i2.IonMenuButton, i2.IonTitle, i2.IonToolbar, i2.RouterLinkDelegate, i3.RouterLink],\n  styles: [\"ion-item[_ngcontent-%COMP%] {\\n  --padding-start: 16px;\\n  --padding-end: 16px;\\n  --min-height: 90px;\\n  --border-radius: 12px;\\n  --border-width: 1px;\\n  --border-style: solid;\\n  --border-color: var(--ion-color-light-shade);\\n  margin: 8px 16px;\\n  --background: var(--ion-color-light);\\n}\\n\\nion-avatar[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  margin-right: 16px;\\n}\\nion-avatar[_ngcontent-%COMP%]   .store-avatar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 1px solid var(--ion-color-medium-tint);\\n  border-radius: 8px;\\n  background: transparent;\\n}\\nion-avatar[_ngcontent-%COMP%]   .store-avatar[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: var(--ion-color-medium);\\n}\\n\\nion-label[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n  margin-bottom: 4px;\\n  color: var(--ion-color-primary);\\n}\\nion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  margin: 2px 0;\\n  color: var(--ion-color-dark);\\n}\\nion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--ion-color-primary);\\n}\\nion-label[_ngcontent-%COMP%]   .badges[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n}\\nion-label[_ngcontent-%COMP%]   .badges[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  margin-bottom: 4px;\\n}\\n\\nion-button[_ngcontent-%COMP%] {\\n  margin-left: 4px;\\n}\\nion-button[color=danger][_ngcontent-%COMP%] {\\n  --color: var(--ion-color-danger);\\n}\\n\\n.store-info[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.store-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n  margin-bottom: 4px;\\n}\\n.store-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .store-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin: 4px 0;\\n  font-weight: normal;\\n}\\n.store-info[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.store-info[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  margin-right: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "StoresPage_For_13_Conditional_22_Template", "ɵɵlistener", "StoresPage_For_13_Template_ion_button_click_25_listener", "store_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "remove", "ɵɵadvance", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate3", "address", "city", "state", "ɵɵtextInterpolate1", "phone", "manager", "ɵɵproperty", "getStatusColor", "status", "getStatusLabel", "ɵɵconditional", "isHeadquarters", "ɵɵpureFunction1", "_c1", "id", "StoresPage", "constructor", "storeService", "alertController", "toastController", "storesList", "ionViewDidLeave", "console", "log", "ionViewWillLeave", "ionViewDidEnter", "ionViewWillEnter", "getList", "subscribe", "next", "response", "error", "alert", "ngOnInit", "store", "create", "header", "message", "buttons", "text", "handler", "filter", "s", "duration", "color", "keyboardClose", "then", "toast", "present", "_error$error", "errorMessage", "ɵɵdirectiveInject", "i1", "StoreService", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ToastController", "selectors", "standalone", "decls", "vars", "consts", "template", "StoresPage_Template", "rf", "ctx", "ɵɵrepeaterCreate", "StoresPage_For_13_Template", "_forTrack0", "StoresPage_ForEmpty_14_Template", "ɵɵrepeater", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\stores\\stores.page.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\stores\\stores.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AlertController, ToastController, ViewDidEnter, ViewDidLeave, ViewWillEnter, ViewWillLeave } from '@ionic/angular';\r\nimport { Store } from './models/store.type';\r\nimport { StoreService } from './services/store.service';\r\n\r\n@Component({\r\n  selector: 'app-stores',\r\n  templateUrl: './stores.page.html',\r\n  styleUrls: ['./stores.page.scss'],\r\n  standalone: false,\r\n})\r\nexport class StoresPage implements OnInit, ViewWillEnter,\r\n  ViewDidEnter, ViewWillLeave, ViewDidLeave {\r\n\r\n  storesList: Store[] = [];\r\n\r\n  constructor(\r\n    private storeService: StoreService,\r\n    private alertController: AlertController,\r\n    private toastController: ToastController,\r\n  ) { }\r\n\r\n  ionViewDidLeave(): void {\r\n    console.log('ionViewDidLeave');\r\n  }\r\n  \r\n  ionViewWillLeave(): void {\r\n    console.log('ionViewWillLeave');\r\n  }\r\n  \r\n  ionViewDidEnter(): void {\r\n    console.log('ionViewDidEnter');\r\n  }\r\n  \r\n  ionViewWillEnter(): void {\r\n    console.log('ionViewWillEnter');\r\n\r\n    this.storeService.getList().subscribe({\r\n      next: (response) => {\r\n        this.storesList = response;\r\n      },\r\n      error: (error) => {\r\n        alert('Erro ao carregar lista de lojas');\r\n        console.error(error);\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnInit() { }\r\n\r\n  getStatusColor(status: string): string {\r\n    switch (status) {\r\n      case 'active': return 'success';\r\n      case 'inactive': return 'danger';\r\n      case 'underMaintenance': return 'warning';\r\n      default: return 'medium';\r\n    }\r\n  }\r\n\r\n  getStatusLabel(status: string): string {\r\n    switch (status) {\r\n      case 'active': return 'Ativa';\r\n      case 'inactive': return 'Inativa';\r\n      case 'underMaintenance': return 'Em Manutenção';\r\n      default: return status;\r\n    }\r\n  }\r\n\r\n  remove(store: Store) {\r\n    this.alertController.create({\r\n      header: 'Exclusão',\r\n      message: `Confirma a exclusão da loja ${store.name}?`,\r\n      buttons: [\r\n        {\r\n          text: 'Sim',\r\n          handler: () => {\r\n            this.storeService.remove(store).subscribe({\r\n              next: () => {\r\n                // Remover da lista usando o ID da loja original\r\n                this.storesList = this.storesList.filter(s => s.id !== store.id);\r\n                this.toastController.create({\r\n                  message: `Loja ${store.name} excluída com sucesso!`,\r\n                  duration: 3000,\r\n                  color: 'secondary',\r\n                  keyboardClose: true,\r\n                }).then(toast => toast.present());\r\n              },\r\n              error: (error) => {\r\n                let errorMessage = 'Erro ao excluir a loja ' + store.name;\r\n                if (error.error?.message) {\r\n                  errorMessage = error.error.message;\r\n                }\r\n                alert(errorMessage);\r\n                console.error(error);\r\n              }\r\n            });\r\n          }\r\n        },\r\n        'Não'\r\n      ]\r\n    }).then(alert => alert.present());\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-menu-button></ion-menu-button>\r\n    </ion-buttons>\r\n    <ion-title>Lojas</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <ion-header collapse=\"condense\">\r\n    <ion-toolbar>\r\n      <ion-title size=\"large\">Lojas</ion-title>\r\n    </ion-toolbar>\r\n  </ion-header>\r\n\r\n  <ion-list>\r\n    @for(store of storesList; track store.id) {\r\n    <ion-item>\r\n      <ion-avatar slot=\"start\">\r\n        <div class=\"store-avatar\">\r\n          <ion-icon name=\"storefront-outline\"></ion-icon>\r\n        </div>\r\n      </ion-avatar>\r\n      <ion-label>\r\n        <h2>{{ store.name }}</h2>\r\n        <p><strong>Endereço:</strong> {{ store.address }}, {{ store.city }}/{{ store.state }}</p>\r\n        <p><strong>Contato:</strong> {{ store.phone }}</p>\r\n        <p><strong><PERSON><PERSON><PERSON>:</strong> {{ store.manager }}</p>\r\n        <div class=\"badges\">\r\n          <ion-badge [color]=\"getStatusColor(store.status)\">{{ getStatusLabel(store.status) }}</ion-badge>\r\n          @if(store.isHeadquarters) {\r\n            <ion-badge color=\"primary\">Matriz</ion-badge>\r\n          }\r\n        </div>\r\n      </ion-label>\r\n      <ion-button slot=\"end\" size=\"small\" [routerLink]=\"['edit', store.id]\">\r\n        <ion-icon name=\"create\" slot=\"icon-only\"></ion-icon>\r\n      </ion-button>\r\n      <ion-button slot=\"end\" size=\"small\" color=\"danger\" (click)=\"remove(store)\">\r\n        <ion-icon name=\"trash\" slot=\"icon-only\"></ion-icon>\r\n      </ion-button>\r\n    </ion-item>\r\n    }\r\n    @empty {\r\n    <ion-item>Lista de lojas vazia, cadastre uma nova loja!</ion-item>\r\n    }\r\n  </ion-list>\r\n  <ion-fab vertical=\"bottom\" horizontal=\"end\" slot=\"fixed\">\r\n    <ion-fab-button [routerLink]=\"['new']\">\r\n      <ion-icon name=\"add\"></ion-icon>\r\n    </ion-fab-button>\r\n  </ion-fab>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;ICgCYA,EAAA,CAAAC,cAAA,oBAA2B;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;;IAZjDH,EAFJ,CAAAC,cAAA,eAAU,oBACiB,aACG;IACxBD,EAAA,CAAAI,SAAA,mBAA+C;IAEnDJ,EADE,CAAAG,YAAA,EAAM,EACK;IAEXH,EADF,CAAAC,cAAA,gBAAW,SACL;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAH,CAAAC,cAAA,QAAG,aAAQ;IAAAD,EAAA,CAAAE,MAAA,qBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtFH,EAAH,CAAAC,cAAA,SAAG,cAAQ;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/CH,EAAH,CAAAC,cAAA,SAAG,cAAQ;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAElDH,EADF,CAAAC,cAAA,eAAoB,qBACgC;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAChGH,EAAA,CAAAK,UAAA,KAAAC,yCAAA,wBAA2B;IAI/BN,EADE,CAAAG,YAAA,EAAM,EACI;IACZH,EAAA,CAAAC,cAAA,sBAAsE;IACpED,EAAA,CAAAI,SAAA,oBAAoD;IACtDJ,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAC,cAAA,sBAA2E;IAAxBD,EAAA,CAAAO,UAAA,mBAAAC,wDAAA;MAAA,MAAAC,QAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAG,MAAA,CAAAP,QAAA,CAAa;IAAA,EAAC;IACxET,EAAA,CAAAI,SAAA,oBAAmD;IAEvDJ,EADE,CAAAG,YAAA,EAAa,EACJ;;;;;IAjBHH,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAkB,iBAAA,CAAAT,QAAA,CAAAU,IAAA,CAAgB;IACUnB,EAAA,CAAAiB,SAAA,GAAuD;IAAvDjB,EAAA,CAAAoB,kBAAA,MAAAX,QAAA,CAAAY,OAAA,QAAAZ,QAAA,CAAAa,IAAA,OAAAb,QAAA,CAAAc,KAAA,KAAuD;IACxDvB,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAwB,kBAAA,MAAAf,QAAA,CAAAgB,KAAA,KAAiB;IACjBzB,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAwB,kBAAA,MAAAf,QAAA,CAAAiB,OAAA,KAAmB;IAEnC1B,EAAA,CAAAiB,SAAA,GAAsC;IAAtCjB,EAAA,CAAA2B,UAAA,UAAAd,MAAA,CAAAe,cAAA,CAAAnB,QAAA,CAAAoB,MAAA,EAAsC;IAAC7B,EAAA,CAAAiB,SAAA,EAAkC;IAAlCjB,EAAA,CAAAkB,iBAAA,CAAAL,MAAA,CAAAiB,cAAA,CAAArB,QAAA,CAAAoB,MAAA,EAAkC;IACpF7B,EAAA,CAAAiB,SAAA,EAEC;IAFDjB,EAAA,CAAA+B,aAAA,CAAAtB,QAAA,CAAAuB,cAAA,WAEC;IAG+BhC,EAAA,CAAAiB,SAAA,EAAiC;IAAjCjB,EAAA,CAAA2B,UAAA,eAAA3B,EAAA,CAAAiC,eAAA,KAAAC,GAAA,EAAAzB,QAAA,CAAA0B,EAAA,EAAiC;;;;;IASvEnC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,oDAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;ADlCtE,OAAM,MAAOiC,UAAU;EAKrBC,YACUC,YAA0B,EAC1BC,eAAgC,EAChCC,eAAgC;IAFhC,KAAAF,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IALzB,KAAAC,UAAU,GAAY,EAAE;EAMpB;EAEJC,eAAeA,CAAA;IACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;EAChC;EAEAC,gBAAgBA,CAAA;IACdF,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACjC;EAEAE,eAAeA,CAAA;IACbH,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;EAChC;EAEAG,gBAAgBA,CAAA;IACdJ,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAE/B,IAAI,CAACN,YAAY,CAACU,OAAO,EAAE,CAACC,SAAS,CAAC;MACpCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACV,UAAU,GAAGU,QAAQ;MAC5B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,KAAK,CAAC,iCAAiC,CAAC;QACxCV,OAAO,CAACS,KAAK,CAACA,KAAK,CAAC;MACtB;KACD,CAAC;EACJ;EAEAE,QAAQA,CAAA,GAAK;EAEb1B,cAAcA,CAACC,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,UAAU;QAAE,OAAO,QAAQ;MAChC,KAAK,kBAAkB;QAAE,OAAO,SAAS;MACzC;QAAS,OAAO,QAAQ;IAC1B;EACF;EAEAC,cAAcA,CAACD,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,OAAO;MAC7B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,kBAAkB;QAAE,OAAO,eAAe;MAC/C;QAAS,OAAOA,MAAM;IACxB;EACF;EAEAb,MAAMA,CAACuC,KAAY;IACjB,IAAI,CAAChB,eAAe,CAACiB,MAAM,CAAC;MAC1BC,MAAM,EAAE,UAAU;MAClBC,OAAO,EAAE,+BAA+BH,KAAK,CAACpC,IAAI,GAAG;MACrDwC,OAAO,EAAE,CACP;QACEC,IAAI,EAAE,KAAK;QACXC,OAAO,EAAEA,CAAA,KAAK;UACZ,IAAI,CAACvB,YAAY,CAACtB,MAAM,CAACuC,KAAK,CAAC,CAACN,SAAS,CAAC;YACxCC,IAAI,EAAEA,CAAA,KAAK;cACT;cACA,IAAI,CAACT,UAAU,GAAG,IAAI,CAACA,UAAU,CAACqB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAKoB,KAAK,CAACpB,EAAE,CAAC;cAChE,IAAI,CAACK,eAAe,CAACgB,MAAM,CAAC;gBAC1BE,OAAO,EAAE,QAAQH,KAAK,CAACpC,IAAI,wBAAwB;gBACnD6C,QAAQ,EAAE,IAAI;gBACdC,KAAK,EAAE,WAAW;gBAClBC,aAAa,EAAE;eAChB,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;YACnC,CAAC;YACDjB,KAAK,EAAGA,KAAK,IAAI;cAAA,IAAAkB,YAAA;cACf,IAAIC,YAAY,GAAG,yBAAyB,GAAGhB,KAAK,CAACpC,IAAI;cACzD,KAAAmD,YAAA,GAAIlB,KAAK,CAACA,KAAK,cAAAkB,YAAA,eAAXA,YAAA,CAAaZ,OAAO,EAAE;gBACxBa,YAAY,GAAGnB,KAAK,CAACA,KAAK,CAACM,OAAO;cACpC;cACAL,KAAK,CAACkB,YAAY,CAAC;cACnB5B,OAAO,CAACS,KAAK,CAACA,KAAK,CAAC;YACtB;WACD,CAAC;QACJ;OACD,EACD,KAAK;KAER,CAAC,CAACe,IAAI,CAACd,KAAK,IAAIA,KAAK,CAACgB,OAAO,EAAE,CAAC;EACnC;;cA1FWjC,UAAU;;mCAAVA,WAAU,EAAApC,EAAA,CAAAwE,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA1E,EAAA,CAAAwE,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA5E,EAAA,CAAAwE,iBAAA,CAAAG,EAAA,CAAAE,eAAA;AAAA;;QAAVzC,WAAU;EAAA0C,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCTnBrF,EAFJ,CAAAC,cAAA,oBAAiC,qBACA,qBACH;MACxBD,EAAA,CAAAI,SAAA,sBAAmC;MACrCJ,EAAA,CAAAG,YAAA,EAAc;MACdH,EAAA,CAAAC,cAAA,gBAAW;MAAAD,EAAA,CAAAE,MAAA,YAAK;MAEpBF,EAFoB,CAAAG,YAAA,EAAY,EAChB,EACH;MAKPH,EAHN,CAAAC,cAAA,qBAAiC,oBACC,kBACjB,mBACa;MAAAD,EAAA,CAAAE,MAAA,aAAK;MAEjCF,EAFiC,CAAAG,YAAA,EAAY,EAC7B,EACH;MAEbH,EAAA,CAAAC,cAAA,gBAAU;MACRD,EAAA,CAAAuF,gBAAA,KAAAC,0BAAA,4BAAAC,UAAA,SAAAC,+BAAA,mBA6BC;MACH1F,EAAA,CAAAG,YAAA,EAAW;MAETH,EADF,CAAAC,cAAA,kBAAyD,yBAChB;MACrCD,EAAA,CAAAI,SAAA,mBAAgC;MAGtCJ,EAFI,CAAAG,YAAA,EAAiB,EACT,EACE;;;MArDFH,EAAA,CAAA2B,UAAA,qBAAoB;MASnB3B,EAAA,CAAAiB,SAAA,GAAmB;MAAnBjB,EAAA,CAAA2B,UAAA,oBAAmB;MAQ5B3B,EAAA,CAAAiB,SAAA,GA6BC;MA7BDjB,EAAA,CAAA2F,UAAA,CAAAL,GAAA,CAAA7C,UAAA,CA6BC;MAGezC,EAAA,CAAAiB,SAAA,GAAsB;MAAtBjB,EAAA,CAAA2B,UAAA,eAAA3B,EAAA,CAAA4F,eAAA,IAAAC,GAAA,EAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}