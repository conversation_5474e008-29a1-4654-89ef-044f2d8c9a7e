{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Returns `true` if the document or host element\n * has a `dir` set to `rtl`. The host value will always\n * take priority over the root document value.\n */\nconst isRTL = hostEl => {\n  if (hostEl) {\n    if (hostEl.dir !== '') {\n      return hostEl.dir.toLowerCase() === 'rtl';\n    }\n  }\n  return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === 'rtl';\n};\nexport { isRTL as i };", "map": {"version": 3, "names": ["isRTL", "hostEl", "dir", "toLowerCase", "document", "i"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/dir-babeabeb.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Returns `true` if the document or host element\n * has a `dir` set to `rtl`. The host value will always\n * take priority over the root document value.\n */\nconst isRTL = (hostEl) => {\n    if (hostEl) {\n        if (hostEl.dir !== '') {\n            return hostEl.dir.toLowerCase() === 'rtl';\n        }\n    }\n    return (document === null || document === void 0 ? void 0 : document.dir.toLowerCase()) === 'rtl';\n};\n\nexport { isRTL as i };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,KAAK,GAAIC,MAAM,IAAK;EACtB,IAAIA,MAAM,EAAE;IACR,IAAIA,MAAM,CAACC,GAAG,KAAK,EAAE,EAAE;MACnB,OAAOD,MAAM,CAACC,GAAG,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK;IAC7C;EACJ;EACA,OAAO,CAACC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACF,GAAG,CAACC,WAAW,CAAC,CAAC,MAAM,KAAK;AACrG,CAAC;AAED,SAASH,KAAK,IAAIK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}