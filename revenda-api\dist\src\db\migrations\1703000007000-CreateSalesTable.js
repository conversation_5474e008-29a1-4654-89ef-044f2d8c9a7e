"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSalesTable1703000007000 = void 0;
const typeorm_1 = require("typeorm");
class CreateSalesTable1703000007000 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'sales',
            columns: [
                {
                    name: 'id',
                    type: 'int',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'date',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'customer_id',
                    type: 'int',
                },
                {
                    name: 'store_id',
                    type: 'int',
                },
                {
                    name: 'totalValue',
                    type: 'decimal',
                    precision: 10,
                    scale: 2,
                },
                {
                    name: 'paymentMethod',
                    type: 'enum',
                    enum: ['pix', 'debit', 'credit'],
                    default: "'pix'",
                },
                {
                    name: 'status',
                    type: 'enum',
                    enum: ['pending', 'completed', 'canceled'],
                    default: "'pending'",
                },
                {
                    name: 'seller',
                    type: 'varchar',
                    length: '200',
                },
            ],
        }), true);
        await queryRunner.createForeignKey('sales', new typeorm_1.TableForeignKey({
            columnNames: ['customer_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'customers',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        }));
        await queryRunner.createForeignKey('sales', new typeorm_1.TableForeignKey({
            columnNames: ['store_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'stores',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('sales');
    }
}
exports.CreateSalesTable1703000007000 = CreateSalesTable1703000007000;
//# sourceMappingURL=1703000007000-CreateSalesTable.js.map