{"ast": null, "code": "var _AppComponent;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/router\";\nconst _c0 = a0 => [a0];\nfunction AppComponent_For_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-menu-toggle\", 3)(1, \"ion-item\", 5);\n    i0.ɵɵelement(2, \"ion-icon\", 6);\n    i0.ɵɵelementStart(3, \"ion-title\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const p_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(4, _c0, p_r1.url));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ios\", p_r1.icon + \"-outline\")(\"md\", p_r1.icon + \"-sharp\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(p_r1.title);\n  }\n}\nexport class AppComponent {\n  constructor() {\n    this.appPages = [{\n      title: 'Home',\n      url: '/home',\n      icon: 'home'\n    }, {\n      title: 'Celulares',\n      url: '/phones',\n      icon: 'phone-portrait'\n    }, {\n      title: 'Marcas',\n      url: '/brands',\n      icon: 'business'\n    }, {\n      title: 'Acessórios',\n      url: '/accessories',\n      icon: 'hardware-chip'\n    }, {\n      title: 'Lojas',\n      url: '/stores',\n      icon: 'storefront'\n    }, {\n      title: 'Clientes',\n      url: '/customers',\n      icon: 'people'\n    }, {\n      title: 'Vendas',\n      url: '/sales',\n      icon: 'cart'\n    }];\n  }\n}\n_AppComponent = AppComponent;\n_AppComponent.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AppComponent)();\n};\n_AppComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _AppComponent,\n  selectors: [[\"app-root\"]],\n  standalone: false,\n  decls: 11,\n  vars: 0,\n  consts: [[\"contentId\", \"main-content\", \"color\", \"secondary\"], [\"color\", \"secondary\"], [\"id\", \"inbox-list\"], [\"auto-hide\", \"false\"], [\"id\", \"main-content\"], [\"routerDirection\", \"root\", \"lines\", \"none\", \"detail\", \"false\", \"routerLinkActive\", \"selected\", 3, \"routerLink\"], [\"aria-hidden\", \"true\", \"slot\", \"start\", 3, \"ios\", \"md\"]],\n  template: function AppComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-app\")(1, \"ion-menu\", 0)(2, \"ion-header\")(3, \"ion-toolbar\", 1)(4, \"ion-title\");\n      i0.ɵɵtext(5, \"Revenda de Celulares\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\")(7, \"ion-list\", 2);\n      i0.ɵɵrepeaterCreate(8, AppComponent_For_9_Template, 5, 6, \"ion-menu-toggle\", 3, i0.ɵɵrepeaterTrackByIdentity);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelement(10, \"ion-router-outlet\", 4);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(8);\n      i0.ɵɵrepeater(ctx.appPages);\n    }\n  },\n  dependencies: [i1.IonApp, i1.IonContent, i1.IonHeader, i1.IonIcon, i1.IonItem, i1.IonList, i1.IonMenu, i1.IonMenuToggle, i1.IonTitle, i1.IonToolbar, i1.IonRouterOutlet, i1.RouterLinkDelegate, i2.RouterLink, i2.RouterLinkActive],\n  styles: [\"ion-menu[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%] {\\n  --background: var(--ion-item-background, var(--ion-background-color, #fff));\\n}\\n\\nion-menu[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%] {\\n  --background: var(--ion-color-secondary);\\n  --color: var(--ion-color-secondary-contrast);\\n}\\n\\nion-menu.md[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%] {\\n  --padding-start: 8px;\\n  --padding-end: 8px;\\n  --padding-top: 20px;\\n  --padding-bottom: 20px;\\n}\\n\\nion-menu.md[_ngcontent-%COMP%]   ion-list[_ngcontent-%COMP%] {\\n  padding: 20px 0;\\n}\\n\\nion-menu.md[_ngcontent-%COMP%]   ion-list#inbox-list[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid rgba(var(--ion-color-secondary-rgb), 0.1);\\n}\\n\\nion-menu.md[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\\n  --padding-start: 16px;\\n  --padding-end: 16px;\\n  margin-bottom: 4px;\\n  border-radius: 8px;\\n  --min-height: 50px;\\n}\\n\\nion-menu.md[_ngcontent-%COMP%]   ion-item.selected[_ngcontent-%COMP%] {\\n  --background: rgba(var(--ion-color-secondary-rgb), 0.15);\\n}\\n\\nion-menu.md[_ngcontent-%COMP%]   ion-item.selected[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: var(--ion-color-secondary);\\n}\\n\\nion-menu.md[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: var(--ion-color-medium);\\n  margin-right: 16px;\\n  font-size: 20px;\\n}\\n\\nion-menu.md[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\nion-menu.ios[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\\n  --padding-start: 16px;\\n  --padding-end: 16px;\\n  --min-height: 50px;\\n  margin-bottom: 4px;\\n  border-radius: 8px;\\n}\\n\\nion-menu.ios[_ngcontent-%COMP%]   ion-item.selected[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: var(--ion-color-secondary);\\n}\\n\\nion-menu.ios[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-right: 16px;\\n  color: var(--ion-color-medium);\\n}\\n\\nion-item.selected[_ngcontent-%COMP%] {\\n  --color: var(--ion-color-secondary);\\n  font-weight: 600;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "p_r1", "url", "icon", "ɵɵtextInterpolate", "title", "AppComponent", "constructor", "appPages", "selectors", "standalone", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵrepeaterCreate", "AppComponent_For_9_Template", "ɵɵrepeaterTrackByIdentity", "ɵɵrepeater"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\app.component.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: 'app.component.html',\r\n  styleUrls: ['app.component.scss'],\r\n  standalone: false,\r\n})\r\nexport class AppComponent {\r\n\r\n  public appPages = [\r\n    { title: 'Home', url: '/home', icon: 'home' },\r\n    { title: 'Ce<PERSON><PERSON><PERSON>', url: '/phones', icon: 'phone-portrait' },\r\n    { title: 'Marcas', url: '/brands', icon: 'business' },\r\n    { title: 'Acessórios', url: '/accessories', icon: 'hardware-chip' },\r\n    { title: 'Lojas', url: '/stores', icon: 'storefront' },\r\n    { title: 'Clientes', url: '/customers', icon: 'people' },\r\n    { title: 'Vendas', url: '/sales', icon: 'cart' }\r\n  ];\r\n\r\n  constructor() { }\r\n}\r\n", "<ion-app>\r\n  <ion-menu contentId=\"main-content\" color=\"secondary\">\r\n    <ion-header>\r\n      <ion-toolbar color=\"secondary\">\r\n        <ion-title>Revenda de Celulares</ion-title>\r\n      </ion-toolbar>\r\n    </ion-header>\r\n    <ion-content >\r\n      <ion-list id=\"inbox-list\">\r\n\r\n        @for (p of appPages; track p; let i = $index) {\r\n        <ion-menu-toggle auto-hide=\"false\">\r\n          <ion-item routerDirection=\"root\" [routerLink]=\"[p.url]\" lines=\"none\" detail=\"false\"\r\n            routerLinkActive=\"selected\">\r\n            <ion-icon aria-hidden=\"true\" slot=\"start\" [ios]=\"p.icon + '-outline'\" [md]=\"p.icon + '-sharp'\"></ion-icon>\r\n            <ion-title>{{ p.title }}</ion-title>\r\n          </ion-item>\r\n        </ion-menu-toggle>\r\n        }\r\n      </ion-list>\r\n    </ion-content>\r\n  </ion-menu>\r\n  <ion-router-outlet id=\"main-content\"></ion-router-outlet>\r\n</ion-app>\r\n"], "mappings": ";;;;;;;ICYUA,EADF,CAAAC,cAAA,yBAAmC,kBAEH;IAC5BD,EAAA,CAAAE,SAAA,kBAA0G;IAC1GF,EAAA,CAAAC,cAAA,gBAAW;IAAAD,EAAA,CAAAG,MAAA,GAAa;IAE5BH,EAF4B,CAAAI,YAAA,EAAY,EAC3B,EACK;;;;IALiBJ,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAAC,IAAA,CAAAC,GAAA,EAAsB;IAEXV,EAAA,CAAAK,SAAA,EAA2B;IAACL,EAA5B,CAAAM,UAAA,QAAAG,IAAA,CAAAE,IAAA,cAA2B,OAAAF,IAAA,CAAAE,IAAA,YAAyB;IACnFX,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAY,iBAAA,CAAAH,IAAA,CAAAI,KAAA,CAAa;;;ADRpC,OAAM,MAAOC,YAAY;EAYvBC,YAAA;IAVO,KAAAC,QAAQ,GAAG,CAChB;MAAEH,KAAK,EAAE,MAAM;MAAEH,GAAG,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAM,CAAE,EAC7C;MAAEE,KAAK,EAAE,WAAW;MAAEH,GAAG,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAgB,CAAE,EAC9D;MAAEE,KAAK,EAAE,QAAQ;MAAEH,GAAG,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAU,CAAE,EACrD;MAAEE,KAAK,EAAE,YAAY;MAAEH,GAAG,EAAE,cAAc;MAAEC,IAAI,EAAE;IAAe,CAAE,EACnE;MAAEE,KAAK,EAAE,OAAO;MAAEH,GAAG,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAY,CAAE,EACtD;MAAEE,KAAK,EAAE,UAAU;MAAEH,GAAG,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAQ,CAAE,EACxD;MAAEE,KAAK,EAAE,QAAQ;MAAEH,GAAG,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAM,CAAE,CACjD;EAEe;;gBAZLG,YAAY;;mCAAZA,aAAY;AAAA;;QAAZA,aAAY;EAAAG,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCHjBxB,EAJR,CAAAC,cAAA,cAAS,kBAC8C,iBACvC,qBACqB,gBAClB;MAAAD,EAAA,CAAAG,MAAA,2BAAoB;MAEnCH,EAFmC,CAAAI,YAAA,EAAY,EAC/B,EACH;MAEXJ,EADF,CAAAC,cAAA,kBAAc,kBACc;MAExBD,EAAA,CAAA0B,gBAAA,IAAAC,2BAAA,8BAAA3B,EAAA,CAAA4B,yBAAA,CAQC;MAGP5B,EAFI,CAAAI,YAAA,EAAW,EACC,EACL;MACXJ,EAAA,CAAAE,SAAA,4BAAyD;MAC3DF,EAAA,CAAAI,YAAA,EAAU;;;MAbFJ,EAAA,CAAAK,SAAA,GAQC;MARDL,EAAA,CAAA6B,UAAA,CAAAJ,GAAA,CAAAT,QAAA,CAQC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}