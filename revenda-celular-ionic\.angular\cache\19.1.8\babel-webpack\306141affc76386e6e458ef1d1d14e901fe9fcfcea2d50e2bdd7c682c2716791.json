{"ast": null, "code": "var _AccessoriesPageRoutingModule;\nimport { RouterModule } from '@angular/router';\nimport { AccessoriesPage } from './accessories.page';\nimport { AccessoryFormComponent } from './accessory-form/accessory-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: AccessoriesPage\n}, {\n  path: 'new',\n  component: AccessoryFormComponent\n}, {\n  path: 'edit/:id',\n  component: AccessoryFormComponent\n}];\nexport class AccessoriesPageRoutingModule {}\n_AccessoriesPageRoutingModule = AccessoriesPageRoutingModule;\n_AccessoriesPageRoutingModule.ɵfac = function AccessoriesPageRoutingModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AccessoriesPageRoutingModule)();\n};\n_AccessoriesPageRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _AccessoriesPageRoutingModule\n});\n_AccessoriesPageRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [RouterModule.forChild(routes), RouterModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AccessoriesPageRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AccessoriesPage", "AccessoryFormComponent", "routes", "path", "component", "AccessoriesPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\accessories\\accessories-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { AccessoriesPage } from './accessories.page';\r\nimport { AccessoryFormComponent } from './accessory-form/accessory-form.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: AccessoriesPage\r\n  },\r\n  {\r\n    path: 'new',\r\n    component: AccessoryFormComponent\r\n  },\r\n  {\r\n    path: 'edit/:id',\r\n    component: AccessoryFormComponent\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class AccessoriesPageRoutingModule { }\r\n"], "mappings": ";AACA,SAAiBA,YAAY,QAAQ,iBAAiB;AAEtD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,sBAAsB,QAAQ,2CAA2C;;;AAElF,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEH;CACZ,EACD;EACEE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,4BAA4B;gCAA5BA,4BAA4B;;mCAA5BA,6BAA4B;AAAA;;QAA5BA;AAA4B;;YAH7BN,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC,EAC7BH,YAAY;AAAA;;2EAEXM,4BAA4B;IAAAE,OAAA,GAAAC,EAAA,CAAAT,YAAA;IAAAU,OAAA,GAF7BV,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}