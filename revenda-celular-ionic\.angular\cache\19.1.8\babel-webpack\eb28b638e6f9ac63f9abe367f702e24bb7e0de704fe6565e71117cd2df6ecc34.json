{"ast": null, "code": "var _SalesPageRoutingModule;\nimport { RouterModule } from '@angular/router';\nimport { SalesPage } from './sales.page';\nimport { SaleFormComponent } from './sale-form/sale-form.component';\nimport { SaleDetailsComponent } from './sale-details/sale-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SalesPage\n}, {\n  path: 'new',\n  component: SaleFormComponent\n}, {\n  path: 'edit/:id',\n  component: SaleFormComponent\n}, {\n  path: 'details/:id',\n  component: SaleDetailsComponent\n}];\nexport class SalesPageRoutingModule {}\n_SalesPageRoutingModule = SalesPageRoutingModule;\n_SalesPageRoutingModule.ɵfac = function SalesPageRoutingModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SalesPageRoutingModule)();\n};\n_SalesPageRoutingModule.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n  type: _SalesPageRoutingModule\n});\n_SalesPageRoutingModule.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n  imports: [RouterModule.forChild(routes), RouterModule]\n});\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SalesPageRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SalesPage", "SaleFormComponent", "SaleDetailsComponent", "routes", "path", "component", "SalesPageRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\sales\\sales-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { Routes, RouterModule } from '@angular/router';\r\n\r\nimport { SalesPage } from './sales.page';\r\nimport { SaleFormComponent } from './sale-form/sale-form.component';\r\nimport { SaleDetailsComponent } from './sale-details/sale-details.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: SalesPage\r\n  },\r\n  {\r\n    path: 'new',\r\n    component: SaleFormComponent\r\n  },\r\n  {\r\n    path: 'edit/:id',\r\n    component: SaleFormComponent\r\n  },\r\n  {\r\n    path: 'details/:id',\r\n    component: SaleDetailsComponent\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class SalesPageRoutingModule {}\r\n"], "mappings": ";AACA,SAAiBA,YAAY,QAAQ,iBAAiB;AAEtD,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,oBAAoB,QAAQ,uCAAuC;;;AAE5E,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEL;CACZ,EACD;EACEI,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,aAAa;EACnBC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,sBAAsB;0BAAtBA,sBAAsB;;mCAAtBA,uBAAsB;AAAA;;QAAtBA;AAAsB;;YAHvBP,YAAY,CAACQ,QAAQ,CAACJ,MAAM,CAAC,EAC7BJ,YAAY;AAAA;;2EAEXO,sBAAsB;IAAAE,OAAA,GAAAC,EAAA,CAAAV,YAAA;IAAAW,OAAA,GAFvBX,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}