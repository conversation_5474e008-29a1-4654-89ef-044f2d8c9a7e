/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
@import "@ionic/angular/css/palettes/dark.system.css";

/* Estilos modernos para listas */
ion-list {
  padding: 8px;
  
  ion-item {
    --padding-start: 16px;
    --padding-end: 16px;
    --padding-top: 12px;
    --padding-bottom: 12px;
    margin-bottom: 8px;
    --background: var(--ion-background-color);
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    transition: box-shadow 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
    }
    
    &.selected {
      --background: rgba(var(--ion-color-secondary-rgb), 0.1);
      border-left: 4px solid var(--ion-color-secondary);
    }
  }
  
  h2 {
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--ion-color-dark);
  }
  
  h3, h4 {
    margin: 4px 0;
    color: var(--ion-color-medium);
  }
  
  ion-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
    margin-right: 6px;
  }
  
  ion-button {
    --border-radius: 6px;
  }
}

/* Estilo para cards */
ion-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  ion-card-header {
    background-color: rgba(var(--ion-color-secondary-rgb), 0.1);
    
    ion-card-title {
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--ion-color-secondary);
    }
  }
}
