import { BrandService } from './brand.service';
import { CreateBrandDto } from './dto/create-brand.dto';
import { UpdateBrandDto } from './dto/update-brand.dto';
export declare class BrandController {
    private readonly brandService;
    constructor(brandService: BrandService);
    create(createBrandDto: CreateBrandDto): Promise<import("./brand.entity").Brand>;
    findAll(): Promise<import("./brand.entity").Brand[]>;
    findOne(id: string): Promise<import("./brand.entity").Brand | null>;
    update(id: string, updateBrandDto: UpdateBrandDto): Promise<import("typeorm").UpdateResult>;
    remove(id: string): Promise<import("typeorm").DeleteResult>;
}
