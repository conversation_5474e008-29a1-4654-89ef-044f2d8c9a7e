{"version": 3, "sourceRoot": "", "sources": ["../../src/css/palettes/dark.scss"], "names": [], "mappings": "AAuEE,MAEI,6BACA,sCACA,mCACA,0CACA,mCACA,kCALA,+BACA,wCACA,qCACA,4CACA,qCACA,oCALA,8BACA,wCACA,oCACA,2CACA,oCACA,mCALA,6BACA,qCACA,mCACA,0CACA,mCACA,kCALA,6BACA,sCACA,mCACA,0CACA,mCACA,kCALA,4BACA,oCACA,kCACA,yCACA,kCACA,iCALA,2BACA,kCACA,iCACA,8CACA,iCACA,gCALA,4BACA,sCACA,kCACA,yCACA,kCACA,iCALA,0BACA,oCACA,gCACA,uCACA,gCACA,+BAMJ,UACE,gCACA,oCACA,0BACA,oCACA,wCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,kCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,+BACA,+BAGF,oBACE,wFACA,0FACA,4FAKF,SACE,gCACA,uCACA,0BACA,oCACA,wCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,yCACA,kCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,mCACA,+BACA,kCACA,kCACA", "file": "dark.always.css", "sourcesContent": ["@use \"sass:map\";\n@import \"../../themes/ionic.functions.color\";\n\n$primary: #4d8dff;\n$secondary: #46b1ff;\n$tertiary: #8482fb;\n$success: #2dd55b;\n$warning: #ffce31;\n$danger: #f24c58;\n$light: #222428;\n$medium: #989aa2;\n$dark: #f4f5f8;\n\n$colors:  (\n  primary: (\n    base:             $primary,\n    contrast:         #000,\n    shade:            get-color-shade($primary),\n    tint:             get-color-tint($primary)\n  ),\n  secondary: (\n    base:             $secondary,\n    contrast:         #000,\n    shade:            get-color-shade($secondary),\n    tint:             get-color-tint($secondary)\n  ),\n  tertiary: (\n    base:             $tertiary,\n    contrast:         #000,\n    shade:            get-color-shade($tertiary),\n    tint:             get-color-tint($tertiary)\n  ),\n  success: (\n    base:             $success,\n    contrast:         #000,\n    shade:            get-color-shade($success),\n    tint:             get-color-tint($success)\n  ),\n  warning: (\n    base:             $warning,\n    contrast:         #000,\n    shade:            get-color-shade($warning),\n    tint:             get-color-tint($warning)\n  ),\n  danger: (\n    base:             $danger,\n    contrast:         #000,\n    shade:            get-color-shade($danger),\n    tint:             get-color-tint($danger)\n  ),\n  light: (\n    base:             $light,\n    contrast:         #fff,\n    shade:            get-color-shade($light),\n    tint:             get-color-tint($light)\n  ),\n  medium: (\n    base:             $medium,\n    contrast:         #000,\n    shade:            get-color-shade($medium),\n    tint:             get-color-tint($medium)\n  ),\n  dark: (\n    base:             $dark,\n    contrast:         #000,\n    shade:            get-color-shade($dark),\n    tint:             get-color-tint($dark)\n  )\n);\n\n@mixin dark-base-palette() {\n  & {\n    @each $color-name, $value in $colors {\n      --ion-color-#{$color-name}: #{map.get($value, base)};\n      --ion-color-#{$color-name}-rgb: #{color-to-rgb-list(map.get($value, base))};\n      --ion-color-#{$color-name}-contrast: #{map.get($value, contrast)};\n      --ion-color-#{$color-name}-contrast-rgb: #{color-to-rgb-list(map.get($value, contrast))};\n      --ion-color-#{$color-name}-shade: #{map.get($value, shade)};\n      --ion-color-#{$color-name}-tint: #{map.get($value, tint)};\n    }\n  }\n}\n\n@mixin dark-ios-palette() {\n  & {\n    --ion-background-color: #000000;\n    --ion-background-color-rgb: 0, 0, 0;\n    --ion-text-color: #ffffff;\n    --ion-text-color-rgb: 255, 255, 255;\n    --ion-background-color-step-50: #0d0d0d;\n    --ion-background-color-step-100: #1a1a1a;\n    --ion-background-color-step-150: #262626;\n    --ion-background-color-step-200: #333333;\n    --ion-background-color-step-250: #404040;\n    --ion-background-color-step-300: #4d4d4d;\n    --ion-background-color-step-350: #595959;\n    --ion-background-color-step-400: #666666;\n    --ion-background-color-step-450: #737373;\n    --ion-background-color-step-500: #808080;\n    --ion-background-color-step-550: #8c8c8c;\n    --ion-background-color-step-600: #999999;\n    --ion-background-color-step-650: #a6a6a6;\n    --ion-background-color-step-700: #b3b3b3;\n    --ion-background-color-step-750: #bfbfbf;\n    --ion-background-color-step-800: #cccccc;\n    --ion-background-color-step-850: #d9d9d9;\n    --ion-background-color-step-900: #e6e6e6;\n    --ion-background-color-step-950: #f2f2f2;\n    --ion-text-color-step-50: #f2f2f2;\n    --ion-text-color-step-100: #e6e6e6;\n    --ion-text-color-step-150: #d9d9d9;\n    --ion-text-color-step-200: #cccccc;\n    --ion-text-color-step-250: #bfbfbf;\n    --ion-text-color-step-300: #b3b3b3;\n    --ion-text-color-step-350: #a6a6a6;\n    --ion-text-color-step-400: #999999;\n    --ion-text-color-step-450: #8c8c8c;\n    --ion-text-color-step-500: #808080;\n    --ion-text-color-step-550: #737373;\n    --ion-text-color-step-600: #666666;\n    --ion-text-color-step-650: #595959;\n    --ion-text-color-step-700: #4d4d4d;\n    --ion-text-color-step-750: #404040;\n    --ion-text-color-step-800: #333333;\n    --ion-text-color-step-850: #262626;\n    --ion-text-color-step-900: #1a1a1a;\n    --ion-text-color-step-950: #0d0d0d;\n    --ion-item-background: #000000;\n    --ion-card-background: #1c1c1d;\n  }\n\n  & ion-modal {\n    --ion-background-color: var(--ion-color-step-100, var(--ion-background-color-step-100));\n    --ion-toolbar-background: var(--ion-color-step-150, var(--ion-background-color-step-150));\n    --ion-toolbar-border-color: var(--ion-color-step-250, var(--ion-background-color-step-250));\n  }\n}\n\n@mixin dark-md-palette() {\n  & {\n    --ion-background-color: #121212;\n    --ion-background-color-rgb: 18, 18, 18;\n    --ion-text-color: #ffffff;\n    --ion-text-color-rgb: 255, 255, 255;\n    --ion-background-color-step-50: #1e1e1e;\n    --ion-background-color-step-100: #2a2a2a;\n    --ion-background-color-step-150: #363636;\n    --ion-background-color-step-200: #414141;\n    --ion-background-color-step-250: #4d4d4d;\n    --ion-background-color-step-300: #595959;\n    --ion-background-color-step-350: #656565;\n    --ion-background-color-step-400: #717171;\n    --ion-background-color-step-450: #7d7d7d;\n    --ion-background-color-step-500: #898989;\n    --ion-background-color-step-550: #949494;\n    --ion-background-color-step-600: #a0a0a0;\n    --ion-background-color-step-650: #acacac;\n    --ion-background-color-step-700: #b8b8b8;\n    --ion-background-color-step-750: #c4c4c4;\n    --ion-background-color-step-800: #d0d0d0;\n    --ion-background-color-step-850: #dbdbdb;\n    --ion-background-color-step-900: #e7e7e7;\n    --ion-background-color-step-950: #f3f3f3;\n    --ion-text-color-step-50: #f3f3f3;\n    --ion-text-color-step-100: #e7e7e7;\n    --ion-text-color-step-150: #dbdbdb;\n    --ion-text-color-step-200: #d0d0d0;\n    --ion-text-color-step-250: #c4c4c4;\n    --ion-text-color-step-300: #b8b8b8;\n    --ion-text-color-step-350: #acacac;\n    --ion-text-color-step-400: #a0a0a0;\n    --ion-text-color-step-450: #949494;\n    --ion-text-color-step-500: #898989;\n    --ion-text-color-step-550: #7d7d7d;\n    --ion-text-color-step-600: #717171;\n    --ion-text-color-step-650: #656565;\n    --ion-text-color-step-700: #595959;\n    --ion-text-color-step-750: #4d4d4d;\n    --ion-text-color-step-800: #414141;\n    --ion-text-color-step-850: #363636;\n    --ion-text-color-step-900: #2a2a2a;\n    --ion-text-color-step-950: #1e1e1e;\n    --ion-item-background: #1e1e1e;\n    --ion-toolbar-background: #1f1f1f;\n    --ion-tab-bar-background: #1f1f1f;\n    --ion-card-background: #1e1e1e;\n  }\n}\n"]}