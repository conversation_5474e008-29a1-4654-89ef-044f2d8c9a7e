<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Marc<PERSON></ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large"><PERSON><PERSON></ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-list>
    @for(brand of brandsList; track brand.id) {
    <ion-item>
      <ion-avatar slot="start">
        <div class="brand-avatar">
          <ion-icon name="business-outline"></ion-icon>
        </div>
      </ion-avatar>
      <ion-label>
        <h2>{{ brand.name }}</h2>
        <p><strong>País:</strong> {{ brand.country }}</p>
      </ion-label>
      <ion-button slot="end" size="small" [routerLink]="['edit', brand.id]">
        <ion-icon name="create" slot="icon-only"></ion-icon>
      </ion-button>
      <ion-button slot="end" size="small" color="danger" (click)="remove(brand)">
        <ion-icon name="trash" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-item>
    }
    @empty {
    <ion-item>Lista de marcas vazia, cadastre uma nova marca!</ion-item>
    }
  </ion-list>
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button [routerLink]="['new']">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
