{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement, j as forceUpdate } from './index-28849c61.js';\nimport { c as createNotchController } from './notch-controller-55b09e11.js';\nimport { i as isOptionSelected, c as compareOptions } from './compare-with-utils-a96ff2ea.js';\nimport { h as inheritAttributes, f as focusVisibleElement, d as renderHiddenInput } from './helpers-da915de8.js';\nimport { c as popoverController, b as actionSheetController, a as alertController, m as modalController, s as safeCall } from './overlays-e7b9d6d9.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { h as hostContext, c as createColorClasses, g as getClassMap } from './theme-01f3f29c.js';\nimport { w as watchForOptions } from './watch-options-c2911ace.js';\nimport { w as chevronExpand, q as caretDownSharp } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-c81d82ab.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-06ef3c3e.js';\nimport './framework-delegate-63d1a679.js';\nimport './gesture-controller-314a54f6.js';\nimport './index-9b0d46f4.js';\nconst selectIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host{--highlight-height:0px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonSelectIosStyle0 = selectIosCss;\nconst selectMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.ion-focused){--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}:host(.select-fill-solid) .select-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.ion-focused){--border-width:var(--highlight-height);--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-start{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius)}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-end{border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.ion-focused) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.ion-focused) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.select-highlight{inset-inline-start:0}:host(.select-expanded) .select-highlight,:host(.ion-focused) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}:host(.in-item) .select-highlight{inset-inline-start:0}:host(.select-expanded:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.ion-focused) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonSelectMdStyle0 = selectMdCss;\nconst Select = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionCancel = createEvent(this, \"ionCancel\", 7);\n    this.ionDismiss = createEvent(this, \"ionDismiss\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inputId = `ion-sel-${selectIds++}`;\n    this.inheritedAttributes = {};\n    this.onClick = ev => {\n      const target = ev.target;\n      const closestSlot = target.closest('[slot=\"start\"], [slot=\"end\"]');\n      if (target === this.el || closestSlot === null) {\n        this.setFocus();\n        this.open(ev);\n      } else {\n        /**\n         * Prevent clicks to the start/end slots from opening the select.\n         * We ensure the target isn't this element in case the select is slotted\n         * in, for example, an item. This would prevent the select from ever\n         * being opened since the element itself has slot=\"start\"/\"end\".\n         *\n         * Clicking a slotted element also causes a click\n         * on the <label> element (since it wraps the slots).\n         * Clicking <label> dispatches another click event on\n         * the native form control that then bubbles up to this\n         * listener. This additional event targets the host\n         * element, so the select overlay is opened.\n         *\n         * When the slotted elements are clicked (and therefore\n         * the ancestor <label> element) we want to prevent the label\n         * from dispatching another click event.\n         *\n         * Do not call stopPropagation() because this will cause\n         * click handlers on the slotted elements to never fire in React.\n         * When developers do onClick in React a native \"click\" listener\n         * is added on the root element, not the slotted element. When that\n         * native click listener fires, React then dispatches the synthetic\n         * click event on the slotted element. However, if stopPropagation\n         * is called then the native click event will never bubble up\n         * to the root element.\n         */\n        ev.preventDefault();\n      }\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.isExpanded = false;\n    this.cancelText = 'Cancel';\n    this.color = undefined;\n    this.compareWith = undefined;\n    this.disabled = false;\n    this.fill = undefined;\n    this.interface = 'alert';\n    this.interfaceOptions = {};\n    this.justify = undefined;\n    this.label = undefined;\n    this.labelPlacement = 'start';\n    this.multiple = false;\n    this.name = this.inputId;\n    this.okText = 'OK';\n    this.placeholder = undefined;\n    this.selectedText = undefined;\n    this.toggleIcon = undefined;\n    this.expandedIcon = undefined;\n    this.shape = undefined;\n    this.value = undefined;\n  }\n  styleChanged() {\n    this.emitStyle();\n  }\n  setValue(value) {\n    this.value = value;\n    this.ionChange.emit({\n      value\n    });\n  }\n  connectedCallback() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const {\n        el\n      } = _this;\n      _this.notchController = createNotchController(el, () => _this.notchSpacerEl, () => _this.labelSlot);\n      _this.updateOverlayOptions();\n      _this.emitStyle();\n      _this.mutationO = watchForOptions(_this.el, 'ion-select-option', /*#__PURE__*/_asyncToGenerator(function* () {\n        _this.updateOverlayOptions();\n        /**\n         * We need to re-render the component\n         * because one of the new ion-select-option\n         * elements may match the value. In this case,\n         * the rendered selected text should be updated.\n         */\n        forceUpdate(_this);\n      }));\n    })();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n  }\n  componentDidLoad() {\n    /**\n     * If any of the conditions that trigger the styleChanged callback\n     * are met on component load, it is possible the event emitted\n     * prior to a parent web component registering an event listener.\n     *\n     * To ensure the parent web component receives the event, we\n     * emit the style event again after the component has loaded.\n     *\n     * This is often seen in Angular with the `dist` output target.\n     */\n    this.emitStyle();\n  }\n  disconnectedCallback() {\n    if (this.mutationO) {\n      this.mutationO.disconnect();\n      this.mutationO = undefined;\n    }\n    if (this.notchController) {\n      this.notchController.destroy();\n      this.notchController = undefined;\n    }\n  }\n  /**\n   * Open the select overlay. The overlay is either an alert, action sheet, or popover,\n   * depending on the `interface` property on the `ion-select`.\n   *\n   * @param event The user interface event that called the open.\n   */\n  open(event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.disabled || _this2.isExpanded) {\n        return undefined;\n      }\n      _this2.isExpanded = true;\n      const overlay = _this2.overlay = yield _this2.createOverlay(event);\n      overlay.onDidDismiss().then(() => {\n        _this2.overlay = undefined;\n        _this2.isExpanded = false;\n        _this2.ionDismiss.emit();\n        _this2.setFocus();\n      });\n      yield overlay.present();\n      // focus selected option for popovers and modals\n      if (_this2.interface === 'popover' || _this2.interface === 'modal') {\n        const indexOfSelected = _this2.childOpts.findIndex(o => o.value === _this2.value);\n        if (indexOfSelected > -1) {\n          const selectedItem = overlay.querySelector(`.select-interface-option:nth-child(${indexOfSelected + 1})`);\n          if (selectedItem) {\n            /**\n             * Browsers such as Firefox do not\n             * correctly delegate focus when manually\n             * focusing an element with delegatesFocus.\n             * We work around this by manually focusing\n             * the interactive element.\n             * ion-radio and ion-checkbox are the only\n             * elements that ion-select-popover uses, so\n             * we only need to worry about those two components\n             * when focusing.\n             */\n            const interactiveEl = selectedItem.querySelector('ion-radio, ion-checkbox');\n            if (interactiveEl) {\n              // Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n              // and removing `ion-focused` style\n              interactiveEl.setFocus();\n            }\n            focusVisibleElement(selectedItem);\n          }\n        } else {\n          /**\n           * If no value is set then focus the first enabled option.\n           */\n          const firstEnabledOption = overlay.querySelector('ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)');\n          if (firstEnabledOption) {\n            /**\n             * Focus the option for the same reason as we do above.\n             *\n             * Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n             * and removing `ion-focused` style\n             */\n            firstEnabledOption.setFocus();\n            focusVisibleElement(firstEnabledOption.closest('ion-item'));\n          }\n        }\n      }\n      return overlay;\n    })();\n  }\n  createOverlay(ev) {\n    let selectInterface = this.interface;\n    if (selectInterface === 'action-sheet' && this.multiple) {\n      console.warn(`Select interface cannot be \"${selectInterface}\" with a multi-value select. Using the \"alert\" interface instead.`);\n      selectInterface = 'alert';\n    }\n    if (selectInterface === 'popover' && !ev) {\n      console.warn(`Select interface cannot be a \"${selectInterface}\" without passing an event. Using the \"alert\" interface instead.`);\n      selectInterface = 'alert';\n    }\n    if (selectInterface === 'action-sheet') {\n      return this.openActionSheet();\n    }\n    if (selectInterface === 'popover') {\n      return this.openPopover(ev);\n    }\n    if (selectInterface === 'modal') {\n      return this.openModal();\n    }\n    return this.openAlert();\n  }\n  updateOverlayOptions() {\n    const overlay = this.overlay;\n    if (!overlay) {\n      return;\n    }\n    const childOpts = this.childOpts;\n    const value = this.value;\n    switch (this.interface) {\n      case 'action-sheet':\n        overlay.buttons = this.createActionSheetButtons(childOpts, value);\n        break;\n      case 'popover':\n        const popover = overlay.querySelector('ion-select-popover');\n        if (popover) {\n          popover.options = this.createOverlaySelectOptions(childOpts, value);\n        }\n        break;\n      case 'modal':\n        const modal = overlay.querySelector('ion-select-modal');\n        if (modal) {\n          modal.options = this.createOverlaySelectOptions(childOpts, value);\n        }\n        break;\n      case 'alert':\n        const inputType = this.multiple ? 'checkbox' : 'radio';\n        overlay.inputs = this.createAlertInputs(childOpts, inputType, value);\n        break;\n    }\n  }\n  createActionSheetButtons(data, selectValue) {\n    const actionSheetButtons = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        role: isOptionSelected(selectValue, value, this.compareWith) ? 'selected' : '',\n        text: option.textContent,\n        cssClass: optClass,\n        handler: () => {\n          this.setValue(value);\n        }\n      };\n    });\n    // Add \"cancel\" button\n    actionSheetButtons.push({\n      text: this.cancelText,\n      role: 'cancel',\n      handler: () => {\n        this.ionCancel.emit();\n      }\n    });\n    return actionSheetButtons;\n  }\n  createAlertInputs(data, inputType, selectValue) {\n    const alertInputs = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        type: inputType,\n        cssClass: optClass,\n        label: option.textContent || '',\n        value,\n        checked: isOptionSelected(selectValue, value, this.compareWith),\n        disabled: option.disabled\n      };\n    });\n    return alertInputs;\n  }\n  createOverlaySelectOptions(data, selectValue) {\n    const popoverOptions = data.map(option => {\n      const value = getOptionValue(option);\n      // Remove hydrated before copying over classes\n      const copyClasses = Array.from(option.classList).filter(cls => cls !== 'hydrated').join(' ');\n      const optClass = `${OPTION_CLASS} ${copyClasses}`;\n      return {\n        text: option.textContent || '',\n        cssClass: optClass,\n        value,\n        checked: isOptionSelected(selectValue, value, this.compareWith),\n        disabled: option.disabled,\n        handler: selected => {\n          this.setValue(selected);\n          if (!this.multiple) {\n            this.close();\n          }\n        }\n      };\n    });\n    return popoverOptions;\n  }\n  openPopover(ev) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        fill,\n        labelPlacement\n      } = _this3;\n      const interfaceOptions = _this3.interfaceOptions;\n      const mode = getIonMode(_this3);\n      const showBackdrop = mode === 'md' ? false : true;\n      const multiple = _this3.multiple;\n      const value = _this3.value;\n      let event = ev;\n      let size = 'auto';\n      const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n      /**\n       * The popover should take up the full width\n       * when using a fill in MD mode or if the\n       * label is floating/stacked.\n       */\n      if (hasFloatingOrStackedLabel || mode === 'md' && fill !== undefined) {\n        size = 'cover';\n        /**\n         * Otherwise the popover\n         * should be positioned relative\n         * to the native element.\n         */\n      } else {\n        event = Object.assign(Object.assign({}, ev), {\n          detail: {\n            ionShadowTarget: _this3.nativeWrapperEl\n          }\n        });\n      }\n      const popoverOpts = Object.assign(Object.assign({\n        mode,\n        event,\n        alignment: 'center',\n        size,\n        showBackdrop\n      }, interfaceOptions), {\n        component: 'ion-select-popover',\n        cssClass: ['select-popover', interfaceOptions.cssClass],\n        componentProps: {\n          header: interfaceOptions.header,\n          subHeader: interfaceOptions.subHeader,\n          message: interfaceOptions.message,\n          multiple,\n          value,\n          options: _this3.createOverlaySelectOptions(_this3.childOpts, value)\n        }\n      });\n      return popoverController.create(popoverOpts);\n    })();\n  }\n  openActionSheet() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const mode = getIonMode(_this4);\n      const interfaceOptions = _this4.interfaceOptions;\n      const actionSheetOpts = Object.assign(Object.assign({\n        mode\n      }, interfaceOptions), {\n        buttons: _this4.createActionSheetButtons(_this4.childOpts, _this4.value),\n        cssClass: ['select-action-sheet', interfaceOptions.cssClass]\n      });\n      return actionSheetController.create(actionSheetOpts);\n    })();\n  }\n  openAlert() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const interfaceOptions = _this5.interfaceOptions;\n      const inputType = _this5.multiple ? 'checkbox' : 'radio';\n      const mode = getIonMode(_this5);\n      const alertOpts = Object.assign(Object.assign({\n        mode\n      }, interfaceOptions), {\n        header: interfaceOptions.header ? interfaceOptions.header : _this5.labelText,\n        inputs: _this5.createAlertInputs(_this5.childOpts, inputType, _this5.value),\n        buttons: [{\n          text: _this5.cancelText,\n          role: 'cancel',\n          handler: () => {\n            _this5.ionCancel.emit();\n          }\n        }, {\n          text: _this5.okText,\n          handler: selectedValues => {\n            _this5.setValue(selectedValues);\n          }\n        }],\n        cssClass: ['select-alert', interfaceOptions.cssClass, _this5.multiple ? 'multiple-select-alert' : 'single-select-alert']\n      });\n      return alertController.create(alertOpts);\n    })();\n  }\n  openModal() {\n    const {\n      multiple,\n      value,\n      interfaceOptions\n    } = this;\n    const mode = getIonMode(this);\n    const modalOpts = Object.assign(Object.assign({}, interfaceOptions), {\n      mode,\n      cssClass: ['select-modal', interfaceOptions.cssClass],\n      component: 'ion-select-modal',\n      componentProps: {\n        header: interfaceOptions.header,\n        multiple,\n        value,\n        options: this.createOverlaySelectOptions(this.childOpts, value)\n      }\n    });\n    return modalController.create(modalOpts);\n  }\n  /**\n   * Close the select interface.\n   */\n  close() {\n    if (!this.overlay) {\n      return Promise.resolve(false);\n    }\n    return this.overlay.dismiss();\n  }\n  hasValue() {\n    return this.getText() !== '';\n  }\n  get childOpts() {\n    return Array.from(this.el.querySelectorAll('ion-select-option'));\n  }\n  /**\n   * Returns any plaintext associated with\n   * the label (either prop or slot).\n   * Note: This will not return any custom\n   * HTML. Use the `hasLabel` getter if you\n   * want to know if any slotted label content\n   * was passed.\n   */\n  get labelText() {\n    const {\n      label\n    } = this;\n    if (label !== undefined) {\n      return label;\n    }\n    const {\n      labelSlot\n    } = this;\n    if (labelSlot !== null) {\n      return labelSlot.textContent;\n    }\n    return;\n  }\n  getText() {\n    const selectedText = this.selectedText;\n    if (selectedText != null && selectedText !== '') {\n      return selectedText;\n    }\n    return generateText(this.childOpts, this.value, this.compareWith);\n  }\n  setFocus() {\n    if (this.focusEl) {\n      this.focusEl.focus();\n    }\n  }\n  emitStyle() {\n    const {\n      disabled\n    } = this;\n    const style = {\n      'interactive-disabled': disabled\n    };\n    this.ionStyle.emit(style);\n  }\n  renderLabel() {\n    const {\n      label\n    } = this;\n    return h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !this.hasLabel\n      },\n      part: \"label\"\n    }, label === undefined ? h(\"slot\", {\n      name: \"label\"\n    }) : h(\"div\", {\n      class: \"label-text\"\n    }, label));\n  }\n  componentDidRender() {\n    var _a;\n    (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n  }\n  /**\n   * Gets any content passed into the `label` slot,\n   * not the <slot> definition.\n   */\n  get labelSlot() {\n    return this.el.querySelector('[slot=\"label\"]');\n  }\n  /**\n   * Returns `true` if label content is provided\n   * either by a prop or a content. If you want\n   * to get the plaintext value of the label use\n   * the `labelText` getter instead.\n   */\n  get hasLabel() {\n    return this.label !== undefined || this.labelSlot !== null;\n  }\n  /**\n   * Renders the border container\n   * when fill=\"outline\".\n   */\n  renderLabelContainer() {\n    const mode = getIonMode(this);\n    const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n    if (hasOutlineFill) {\n      /**\n       * The outline fill has a special outline\n       * that appears around the select and the label.\n       * Certain stacked and floating label placements cause the\n       * label to translate up and create a \"cut out\"\n       * inside of that border by using the notch-spacer element.\n       */\n      return [h(\"div\", {\n        class: \"select-outline-container\"\n      }, h(\"div\", {\n        class: \"select-outline-start\"\n      }), h(\"div\", {\n        class: {\n          'select-outline-notch': true,\n          'select-outline-notch-hidden': !this.hasLabel\n        }\n      }, h(\"div\", {\n        class: \"notch-spacer\",\n        \"aria-hidden\": \"true\",\n        ref: el => this.notchSpacerEl = el\n      }, this.label)), h(\"div\", {\n        class: \"select-outline-end\"\n      })), this.renderLabel()];\n    }\n    /**\n     * If not using the outline style,\n     * we can render just the label.\n     */\n    return this.renderLabel();\n  }\n  /**\n   * Renders either the placeholder\n   * or the selected values based on\n   * the state of the select.\n   */\n  renderSelectText() {\n    const {\n      placeholder\n    } = this;\n    const displayValue = this.getText();\n    let addPlaceholderClass = false;\n    let selectText = displayValue;\n    if (selectText === '' && placeholder !== undefined) {\n      selectText = placeholder;\n      addPlaceholderClass = true;\n    }\n    const selectTextClasses = {\n      'select-text': true,\n      'select-placeholder': addPlaceholderClass\n    };\n    const textPart = addPlaceholderClass ? 'placeholder' : 'text';\n    return h(\"div\", {\n      \"aria-hidden\": \"true\",\n      class: selectTextClasses,\n      part: textPart\n    }, selectText);\n  }\n  /**\n   * Renders the chevron icon\n   * next to the select text.\n   */\n  renderSelectIcon() {\n    const mode = getIonMode(this);\n    const {\n      isExpanded,\n      toggleIcon,\n      expandedIcon\n    } = this;\n    let icon;\n    if (isExpanded && expandedIcon !== undefined) {\n      icon = expandedIcon;\n    } else {\n      const defaultIcon = mode === 'ios' ? chevronExpand : caretDownSharp;\n      icon = toggleIcon !== null && toggleIcon !== void 0 ? toggleIcon : defaultIcon;\n    }\n    return h(\"ion-icon\", {\n      class: \"select-icon\",\n      part: \"icon\",\n      \"aria-hidden\": \"true\",\n      icon: icon\n    });\n  }\n  get ariaLabel() {\n    var _a;\n    const {\n      placeholder,\n      inheritedAttributes\n    } = this;\n    const displayValue = this.getText();\n    // The aria label should be preferred over visible text if both are specified\n    const definedLabel = (_a = inheritedAttributes['aria-label']) !== null && _a !== void 0 ? _a : this.labelText;\n    /**\n     * If developer has specified a placeholder\n     * and there is nothing selected, the selectText\n     * should have the placeholder value.\n     */\n    let renderedLabel = displayValue;\n    if (renderedLabel === '' && placeholder !== undefined) {\n      renderedLabel = placeholder;\n    }\n    /**\n     * If there is a developer-defined label,\n     * then we need to concatenate the developer label\n     * string with the current current value.\n     * The label for the control should be read\n     * before the values of the control.\n     */\n    if (definedLabel !== undefined) {\n      renderedLabel = renderedLabel === '' ? definedLabel : `${definedLabel}, ${renderedLabel}`;\n    }\n    return renderedLabel;\n  }\n  renderListbox() {\n    const {\n      disabled,\n      inputId,\n      isExpanded\n    } = this;\n    return h(\"button\", {\n      disabled: disabled,\n      id: inputId,\n      \"aria-label\": this.ariaLabel,\n      \"aria-haspopup\": \"dialog\",\n      \"aria-expanded\": `${isExpanded}`,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur,\n      ref: focusEl => this.focusEl = focusEl\n    });\n  }\n  render() {\n    const {\n      disabled,\n      el,\n      isExpanded,\n      expandedIcon,\n      labelPlacement,\n      justify,\n      placeholder,\n      fill,\n      shape,\n      name,\n      value\n    } = this;\n    const mode = getIonMode(this);\n    const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n    const justifyEnabled = !hasFloatingOrStackedLabel && justify !== undefined;\n    const rtl = isRTL(el) ? 'rtl' : 'ltr';\n    const inItem = hostContext('ion-item', this.el);\n    const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n    const hasValue = this.hasValue();\n    const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n    renderHiddenInput(true, el, name, parseValue(value), disabled);\n    /**\n     * If the label is stacked, it should always sit above the select.\n     * For floating labels, the label should move above the select if\n     * the select has a value, is open, or has anything in either\n     * the start or end slot.\n     *\n     * If there is content in the start slot, the label would overlap\n     * it if not forced to float. This is also applied to the end slot\n     * because with the default or solid fills, the select is not\n     * vertically centered in the container, but the label is. This\n     * causes the slots and label to appear vertically offset from each\n     * other when the label isn't floating above the input. This doesn't\n     * apply to the outline fill, but this was not accounted for to keep\n     * things consistent.\n     *\n     * TODO(FW-5592): Remove hasStartEndSlots condition\n     */\n    const labelShouldFloat = labelPlacement === 'stacked' || labelPlacement === 'floating' && (hasValue || isExpanded || hasStartEndSlots);\n    return h(Host, {\n      key: '144dfa5c49549a74fe516c65b9b8104a477ac789',\n      onClick: this.onClick,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-item': inItem,\n        'in-item-color': hostContext('ion-item.ion-color', el),\n        'select-disabled': disabled,\n        'select-expanded': isExpanded,\n        'has-expanded-icon': expandedIcon !== undefined,\n        'has-value': hasValue,\n        'label-floating': labelShouldFloat,\n        'has-placeholder': placeholder !== undefined,\n        'ion-focusable': true,\n        [`select-${rtl}`]: true,\n        [`select-fill-${fill}`]: fill !== undefined,\n        [`select-justify-${justify}`]: justifyEnabled,\n        [`select-shape-${shape}`]: shape !== undefined,\n        [`select-label-placement-${labelPlacement}`]: true\n      })\n    }, h(\"label\", {\n      key: '0edcfcbac575a9dccc77991531b6980d1caebf42',\n      class: \"select-wrapper\",\n      id: \"select-label\"\n    }, this.renderLabelContainer(), h(\"div\", {\n      key: '348151d90cb093f5d21c7d4a834264ac4a312c40',\n      class: \"select-wrapper-inner\"\n    }, h(\"slot\", {\n      key: '8b7708c7f81217435c58276da0c08bba766d9500',\n      name: \"start\"\n    }), h(\"div\", {\n      key: '10c520a335da0a0d1cf40f9365597beb244d3b48',\n      class: \"native-wrapper\",\n      ref: el => this.nativeWrapperEl = el,\n      part: \"container\"\n    }, this.renderSelectText(), this.renderListbox()), h(\"slot\", {\n      key: '0f15c40a5495e98e29d2a21ba21e0bc6f1c0125a',\n      name: \"end\"\n    }), !hasFloatingOrStackedLabel && this.renderSelectIcon()), hasFloatingOrStackedLabel && this.renderSelectIcon(), shouldRenderHighlight && h(\"div\", {\n      key: 'c87faad2e5ebf7f9453397d7ede43abd64d21294',\n      class: \"select-highlight\"\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"disabled\": [\"styleChanged\"],\n      \"isExpanded\": [\"styleChanged\"],\n      \"placeholder\": [\"styleChanged\"],\n      \"value\": [\"styleChanged\"]\n    };\n  }\n};\nconst getOptionValue = el => {\n  const value = el.value;\n  return value === undefined ? el.textContent || '' : value;\n};\nconst parseValue = value => {\n  if (value == null) {\n    return undefined;\n  }\n  if (Array.isArray(value)) {\n    return value.join(',');\n  }\n  return value.toString();\n};\nconst generateText = (opts, value, compareWith) => {\n  if (value === undefined) {\n    return '';\n  }\n  if (Array.isArray(value)) {\n    return value.map(v => textForValue(opts, v, compareWith)).filter(opt => opt !== null).join(', ');\n  } else {\n    return textForValue(opts, value, compareWith) || '';\n  }\n};\nconst textForValue = (opts, value, compareWith) => {\n  const selectOpt = opts.find(opt => {\n    return compareOptions(value, getOptionValue(opt), compareWith);\n  });\n  return selectOpt ? selectOpt.textContent : null;\n};\nlet selectIds = 0;\nconst OPTION_CLASS = 'select-interface-option';\nSelect.style = {\n  ios: IonSelectIosStyle0,\n  md: IonSelectMdStyle0\n};\nconst selectOptionCss = \":host{display:none}\";\nconst IonSelectOptionStyle0 = selectOptionCss;\nconst SelectOption = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.inputId = `ion-selopt-${selectOptionIds++}`;\n    this.disabled = false;\n    this.value = undefined;\n  }\n  render() {\n    return h(Host, {\n      key: '2e6fa159464f04f6d8720f960141f67918bc7534',\n      role: \"option\",\n      id: this.inputId,\n      class: getIonMode(this)\n    });\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nlet selectOptionIds = 0;\nSelectOption.style = IonSelectOptionStyle0;\nconst selectPopoverIosCss = \".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}\";\nconst IonSelectPopoverIosStyle0 = selectPopoverIosCss;\nconst selectPopoverMdCss = \".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){display:none}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\nconst IonSelectPopoverMdStyle0 = selectPopoverMdCss;\nconst SelectPopover = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.header = undefined;\n    this.subHeader = undefined;\n    this.message = undefined;\n    this.multiple = undefined;\n    this.options = [];\n  }\n  findOptionFromEvent(ev) {\n    const {\n      options\n    } = this;\n    return options.find(o => o.value === ev.target.value);\n  }\n  /**\n   * When an option is selected we need to get the value(s)\n   * of the selected option(s) and return it in the option\n   * handler\n   */\n  callOptionHandler(ev) {\n    const option = this.findOptionFromEvent(ev);\n    const values = this.getValues(ev);\n    if (option === null || option === void 0 ? void 0 : option.handler) {\n      safeCall(option.handler, values);\n    }\n  }\n  /**\n   * Dismisses the host popover that the `ion-select-popover`\n   * is rendered within.\n   */\n  dismissParentPopover() {\n    const popover = this.el.closest('ion-popover');\n    if (popover) {\n      popover.dismiss();\n    }\n  }\n  setChecked(ev) {\n    const {\n      multiple\n    } = this;\n    const option = this.findOptionFromEvent(ev);\n    // this is a popover with checkboxes (multiple value select)\n    // we need to set the checked value for this option\n    if (multiple && option) {\n      option.checked = ev.detail.checked;\n    }\n  }\n  getValues(ev) {\n    const {\n      multiple,\n      options\n    } = this;\n    if (multiple) {\n      // this is a popover with checkboxes (multiple value select)\n      // return an array of all the checked values\n      return options.filter(o => o.checked).map(o => o.value);\n    }\n    // this is a popover with radio buttons (single value select)\n    // return the value that was clicked, otherwise undefined\n    const option = this.findOptionFromEvent(ev);\n    return option ? option.value : undefined;\n  }\n  renderOptions(options) {\n    const {\n      multiple\n    } = this;\n    switch (multiple) {\n      case true:\n        return this.renderCheckboxOptions(options);\n      default:\n        return this.renderRadioOptions(options);\n    }\n  }\n  renderCheckboxOptions(options) {\n    return options.map(option => h(\"ion-item\", {\n      class: Object.assign({\n        // TODO FW-4784\n        'item-checkbox-checked': option.checked\n      }, getClassMap(option.cssClass))\n    }, h(\"ion-checkbox\", {\n      value: option.value,\n      disabled: option.disabled,\n      checked: option.checked,\n      justify: \"start\",\n      labelPlacement: \"end\",\n      onIonChange: ev => {\n        this.setChecked(ev);\n        this.callOptionHandler(ev);\n        // TODO FW-4784\n        forceUpdate(this);\n      }\n    }, option.text)));\n  }\n  renderRadioOptions(options) {\n    const checked = options.filter(o => o.checked).map(o => o.value)[0];\n    return h(\"ion-radio-group\", {\n      value: checked,\n      onIonChange: ev => this.callOptionHandler(ev)\n    }, options.map(option => h(\"ion-item\", {\n      class: Object.assign({\n        // TODO FW-4784\n        'item-radio-checked': option.value === checked\n      }, getClassMap(option.cssClass))\n    }, h(\"ion-radio\", {\n      value: option.value,\n      disabled: option.disabled,\n      onClick: () => this.dismissParentPopover(),\n      onKeyUp: ev => {\n        if (ev.key === ' ') {\n          /**\n           * Selecting a radio option with keyboard navigation,\n           * either through the Enter or Space keys, should\n           * dismiss the popover.\n           */\n          this.dismissParentPopover();\n        }\n      }\n    }, option.text))));\n  }\n  render() {\n    const {\n      header,\n      message,\n      options,\n      subHeader\n    } = this;\n    const hasSubHeaderOrMessage = subHeader !== undefined || message !== undefined;\n    return h(Host, {\n      key: 'dd0990db4de4f175b176b27f35501dd1604ca400',\n      class: getIonMode(this)\n    }, h(\"ion-list\", {\n      key: 'cea2bc3687b8b2490a2a9ff4a1e16cd34169558e'\n    }, header !== undefined && h(\"ion-list-header\", {\n      key: '194aebb53453c43f913daae45a1a3066a1708c4c'\n    }, header), hasSubHeaderOrMessage && h(\"ion-item\", {\n      key: 'b706b07a3c63ad8104d2db413e210c735ec1bec9'\n    }, h(\"ion-label\", {\n      key: '6e52b5b4cf6b04ff3dd5671d64264233de4190d5',\n      class: \"ion-text-wrap\"\n    }, subHeader !== undefined && h(\"h3\", {\n      key: '6ef4440d17f5db8c96c63b9aa5073f4fe4b8ad62'\n    }, subHeader), message !== undefined && h(\"p\", {\n      key: 'c7b3b76c30ecd606c0e985a0258c13d3a75756e7'\n    }, message))), this.renderOptions(options)));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nSelectPopover.style = {\n  ios: IonSelectPopoverIosStyle0,\n  md: IonSelectPopoverMdStyle0\n};\nexport { Select as ion_select, SelectOption as ion_select_option, SelectPopover as ion_select_popover };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "j", "forceUpdate", "c", "createNotchController", "isOptionSelected", "compareOptions", "inheritAttributes", "focusVisibleElement", "renderHiddenInput", "popoverController", "b", "actionSheetController", "a", "alertController", "m", "modalController", "s", "safeCall", "isRTL", "hostContext", "createColorClasses", "g", "getClassMap", "w", "watchForOptions", "chevronExpand", "q", "caretDownSharp", "getIonMode", "selectIosCss", "IonSelectIosStyle0", "selectMdCss", "IonSelectMdStyle0", "Select", "constructor", "hostRef", "ionChange", "ionCancel", "ion<PERSON><PERSON><PERSON>", "ionFocus", "ionBlur", "ionStyle", "inputId", "selectIds", "inheritedAttributes", "onClick", "ev", "target", "closestSlot", "closest", "el", "setFocus", "open", "preventDefault", "onFocus", "emit", "onBlur", "isExpanded", "cancelText", "color", "undefined", "compareWith", "disabled", "fill", "interface", "interfaceOptions", "justify", "label", "labelPlacement", "multiple", "name", "okText", "placeholder", "selectedText", "toggleIcon", "expandedIcon", "shape", "value", "styleChanged", "emitStyle", "setValue", "connectedCallback", "_this", "_asyncToGenerator", "notchController", "notchSpacerEl", "labelSlot", "updateOverlayOptions", "mutationO", "componentWillLoad", "componentDidLoad", "disconnectedCallback", "disconnect", "destroy", "event", "_this2", "overlay", "createOverlay", "onDid<PERSON><PERSON><PERSON>", "then", "present", "indexOfSelected", "childOpts", "findIndex", "o", "selectedItem", "querySelector", "interactiveEl", "firstEnabledOption", "selectInterface", "console", "warn", "openActionSheet", "openPopover", "openModal", "openAlert", "buttons", "createActionSheetButtons", "popover", "options", "createOverlaySelectOptions", "modal", "inputType", "inputs", "createAlertInputs", "data", "selectValue", "actionSheetButtons", "map", "option", "getOptionValue", "copyClasses", "Array", "from", "classList", "filter", "cls", "join", "optClass", "OPTION_CLASS", "role", "text", "textContent", "cssClass", "handler", "push", "alertInputs", "type", "checked", "popoverOptions", "selected", "close", "_this3", "mode", "showBackdrop", "size", "hasFloatingOrStackedLabel", "Object", "assign", "detail", "ionShadowTarget", "nativeWrapperEl", "popoverOpts", "alignment", "component", "componentProps", "header", "subHeader", "message", "create", "_this4", "actionSheetOpts", "_this5", "alertOpts", "labelText", "<PERSON><PERSON><PERSON><PERSON>", "modalOpts", "Promise", "resolve", "dismiss", "hasValue", "getText", "querySelectorAll", "generateText", "focusEl", "focus", "style", "renderLabel", "class", "<PERSON><PERSON><PERSON><PERSON>", "part", "componentDidRender", "_a", "calculateNotchWidth", "renderLabelContainer", "hasOutlineFill", "ref", "renderSelectText", "displayValue", "addPlaceholderClass", "selectText", "selectTextClasses", "textPart", "renderSelectIcon", "icon", "defaultIcon", "aria<PERSON><PERSON><PERSON>", "defined<PERSON>abel", "<PERSON><PERSON><PERSON><PERSON>", "renderListbox", "id", "render", "justifyEnabled", "rtl", "inItem", "should<PERSON>ender<PERSON>ighlight", "hasStartEndSlots", "parseValue", "labelShouldFloat", "key", "watchers", "isArray", "toString", "opts", "v", "textForValue", "opt", "selectOpt", "find", "ios", "md", "selectOptionCss", "IonSelectOptionStyle0", "SelectOption", "selectOptionIds", "selectPopoverIosCss", "IonSelectPopoverIosStyle0", "selectPopoverMdCss", "IonSelectPopoverMdStyle0", "SelectPopover", "findOptionFromEvent", "callOptionHandler", "values", "getV<PERSON>ues", "dismissParentPopover", "setChecked", "renderOptions", "renderCheckboxOptions", "renderRadioOptions", "onIonChange", "onKeyUp", "hasSubHeaderOrMessage", "ion_select", "ion_select_option", "ion_select_popover"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/ion-select_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement, j as forceUpdate } from './index-28849c61.js';\nimport { c as createNotchController } from './notch-controller-55b09e11.js';\nimport { i as isOptionSelected, c as compareOptions } from './compare-with-utils-a96ff2ea.js';\nimport { h as inheritAttributes, f as focusVisibleElement, d as renderHiddenInput } from './helpers-da915de8.js';\nimport { c as popoverController, b as actionSheetController, a as alertController, m as modalController, s as safeCall } from './overlays-e7b9d6d9.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { h as hostContext, c as createColorClasses, g as getClassMap } from './theme-01f3f29c.js';\nimport { w as watchForOptions } from './watch-options-c2911ace.js';\nimport { w as chevronExpand, q as caretDownSharp } from './index-e2cf2ceb.js';\nimport { b as getIonMode } from './ionic-global-c81d82ab.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-06ef3c3e.js';\nimport './framework-delegate-63d1a679.js';\nimport './gesture-controller-314a54f6.js';\nimport './index-9b0d46f4.js';\n\nconst selectIosCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host{--highlight-height:0px}.select-icon{width:1.125rem;height:1.125rem;color:var(--ion-color-step-650, var(--ion-text-color-step-350, #595959))}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 1.125rem - 4px)}:host(.select-disabled){opacity:0.3}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;aspect-ratio:1}\";\nconst IonSelectIosStyle0 = selectIosCss;\n\nconst selectMdCss = \":host{--padding-top:0px;--padding-end:0px;--padding-bottom:0px;--padding-start:0px;--placeholder-color:currentColor;--placeholder-opacity:var(--ion-placeholder-opacity, 0.6);--background:transparent;--border-style:solid;--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;width:100%;min-height:44px;font-family:var(--ion-font-family, inherit);white-space:nowrap;cursor:pointer;z-index:2}:host(.select-label-placement-floating),:host(.select-label-placement-stacked){min-height:56px}:host(.ion-color){--highlight-color-focused:var(--ion-color-base)}:host(.in-item){-ms-flex:1 1 0px;flex:1 1 0}:host(.select-disabled){pointer-events:none}:host(.ion-focused) button{border:2px solid #5e9ed6}:host([slot=start]),:host([slot=end]){-ms-flex:initial;flex:initial;width:auto}.select-placeholder{color:var(--placeholder-color);opacity:var(--placeholder-opacity)}button{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.select-icon{-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0;position:relative;-ms-flex-negative:0;flex-shrink:0}:host(.in-item-color) .select-icon{color:inherit}:host(.select-label-placement-stacked) .select-icon,:host(.select-label-placement-floating) .select-icon{position:absolute;height:100%}:host(.select-ltr.select-label-placement-stacked) .select-icon,:host(.select-ltr.select-label-placement-floating) .select-icon{right:var(--padding-end, 0)}:host(.select-rtl.select-label-placement-stacked) .select-icon,:host(.select-rtl.select-label-placement-floating) .select-icon{left:var(--padding-start, 0)}.select-text{-ms-flex:1;flex:1;min-width:16px;font-size:inherit;text-overflow:ellipsis;white-space:inherit;overflow:hidden}.select-wrapper{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;height:inherit;min-height:inherit;-webkit-transition:background-color 15ms linear;transition:background-color 15ms linear;background:var(--background);line-height:normal;cursor:inherit;-webkit-box-sizing:border-box;box-sizing:border-box}.select-wrapper .select-placeholder{-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)}.select-wrapper-inner{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;overflow:hidden}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{-ms-flex-positive:1;flex-grow:1}:host(.ion-touched.ion-invalid){--highlight-color:var(--highlight-color-invalid)}:host(.ion-valid){--highlight-color:var(--highlight-color-valid)}.label-text-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;max-width:200px;-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);pointer-events:none}.label-text,::slotted([slot=label]){text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden,.select-outline-notch-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-webkit-transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);overflow:hidden}:host(.select-justify-space-between) .select-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.select-justify-start) .select-wrapper{-ms-flex-pack:start;justify-content:start}:host(.select-justify-end) .select-wrapper{-ms-flex-pack:end;justify-content:end}:host(.select-label-placement-start) .select-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.select-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-end) .select-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.select-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.select-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.select-label-placement-stacked) .select-wrapper,:host(.select-label-placement-floating) .select-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:start;align-items:start}:host(.select-label-placement-stacked) .label-text-wrapper,:host(.select-label-placement-floating) .label-text-wrapper{max-width:100%}:host(.select-ltr.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-label-placement-stacked) .native-wrapper,:host(.select-label-placement-floating) .native-wrapper{margin-left:0;margin-right:0;margin-top:1px;margin-bottom:0;-ms-flex-positive:1;flex-grow:1;width:100%}:host(.select-label-placement-floating) .label-text-wrapper{-webkit-transform:translateY(100%) scale(1);transform:translateY(100%) scale(1)}:host(.select-label-placement-floating:not(.label-floating)) .native-wrapper .select-placeholder{opacity:0}:host(.select-expanded.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.ion-focused.select-label-placement-floating) .native-wrapper .select-placeholder,:host(.has-value.select-label-placement-floating) .native-wrapper .select-placeholder{opacity:1}:host(.label-floating) .label-text-wrapper{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);max-width:calc(100% / 0.75)}::slotted([slot=start]),::slotted([slot=end]){-ms-flex-negative:0;flex-shrink:0}::slotted([slot=start]:last-of-type){-webkit-margin-end:16px;margin-inline-end:16px;-webkit-margin-start:0;margin-inline-start:0}::slotted([slot=end]:first-of-type){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.select-fill-solid){--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-500, var(--ion-background-color-step-500, gray));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-solid) .select-wrapper{border-bottom:var(--border-width) var(--border-style) var(--border-color)}:host(.has-focus.select-fill-solid.ion-valid),:host(.select-fill-solid.ion-touched.ion-invalid){--border-color:var(--highlight-color)}:host(.select-fill-solid) .select-bottom{border-top:none}@media (any-hover: hover){:host(.select-fill-solid:hover){--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-solid.select-expanded),:host(.select-fill-solid.ion-focused){--background:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}:host(.select-fill-solid) .select-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0px;border-end-start-radius:0px}:host(.label-floating.select-fill-solid) .label-text-wrapper{max-width:calc(100% / 0.75)}:host(.select-fill-outline){--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3));--border-radius:4px;--padding-start:16px;--padding-end:16px;min-height:56px}:host(.select-fill-outline.select-shape-round){--border-radius:28px;--padding-start:32px;--padding-end:32px}:host(.has-focus.select-fill-outline.ion-valid),:host(.select-fill-outline.ion-touched.ion-invalid){--border-color:var(--highlight-color)}@media (any-hover: hover){:host(.select-fill-outline:hover){--border-color:var(--ion-color-step-750, var(--ion-background-color-step-750, #404040))}}:host(.select-fill-outline.select-expanded),:host(.select-fill-outline.ion-focused){--border-width:var(--highlight-height);--border-color:var(--highlight-color)}:host(.select-fill-outline) .select-bottom{border-top:none}:host(.select-fill-outline) .select-wrapper{border-bottom:none}:host(.select-ltr.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-ltr.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host(.select-rtl.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-rtl.select-fill-outline.select-label-placement-floating) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}:host(.select-fill-outline.select-label-placement-stacked) .label-text-wrapper,:host(.select-fill-outline.select-label-placement-floating) .label-text-wrapper{position:absolute;max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .label-text-wrapper{position:relative;z-index:1}:host(.label-floating.select-fill-outline) .label-text-wrapper{-webkit-transform:translateY(-32%) scale(0.75);transform:translateY(-32%) scale(0.75);margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;max-width:calc((100% - var(--padding-start) - var(--padding-end) - 8px) / 0.75)}:host(.select-fill-outline.select-label-placement-stacked) select,:host(.select-fill-outline.select-label-placement-floating) select{margin-left:0;margin-right:0;margin-top:6px;margin-bottom:6px}:host(.select-fill-outline) .select-outline-container{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;width:100%;height:100%}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-end{pointer-events:none}:host(.select-fill-outline) .select-outline-start,:host(.select-fill-outline) .select-outline-notch,:host(.select-fill-outline) .select-outline-end{border-top:var(--border-width) var(--border-style) var(--border-color);border-bottom:var(--border-width) var(--border-style) var(--border-color);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.select-fill-outline) .select-outline-notch{max-width:calc(100% - var(--padding-start) - var(--padding-end))}:host(.select-fill-outline) .notch-spacer{-webkit-padding-end:8px;padding-inline-end:8px;font-size:calc(1em * 0.75);opacity:0;pointer-events:none}:host(.select-fill-outline) .select-outline-start{-webkit-border-start:var(--border-width) var(--border-style) var(--border-color);border-inline-start:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-start{border-start-start-radius:var(--border-radius);border-start-end-radius:0px;border-end-end-radius:0px;border-end-start-radius:var(--border-radius)}:host(.select-fill-outline) .select-outline-start{width:calc(var(--padding-start) - 4px)}:host(.select-fill-outline) .select-outline-end{-webkit-border-end:var(--border-width) var(--border-style) var(--border-color);border-inline-end:var(--border-width) var(--border-style) var(--border-color)}:host(.select-fill-outline) .select-outline-end{border-start-start-radius:0px;border-start-end-radius:var(--border-radius);border-end-end-radius:var(--border-radius);border-end-start-radius:0px}:host(.select-fill-outline) .select-outline-end{-ms-flex-positive:1;flex-grow:1}:host(.label-floating.select-fill-outline) .select-outline-notch{border-top:none}:host{--border-width:1px;--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.13)))));--highlight-height:2px}.select-icon{width:0.8125rem;-webkit-transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:-webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);transition:transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);color:var(--ion-color-step-500, var(--ion-text-color-step-500, gray))}:host(.select-label-placement-floating.select-expanded) .label-text-wrapper,:host(.select-label-placement-floating.ion-focused) .label-text-wrapper,:host(.select-label-placement-stacked.select-expanded) .label-text-wrapper,:host(.select-label-placement-stacked.ion-focused) .label-text-wrapper{color:var(--highlight-color)}:host(.has-focus.select-label-placement-floating.ion-valid) .label-text-wrapper,:host(.select-label-placement-floating.ion-touched.ion-invalid) .label-text-wrapper,:host(.has-focus.select-label-placement-stacked.ion-valid) .label-text-wrapper,:host(.select-label-placement-stacked.ion-touched.ion-invalid) .label-text-wrapper{color:var(--highlight-color)}.select-highlight{bottom:-1px;position:absolute;width:100%;height:var(--highlight-height);-webkit-transform:scale(0);transform:scale(0);-webkit-transition:-webkit-transform 200ms;transition:-webkit-transform 200ms;transition:transform 200ms;transition:transform 200ms, -webkit-transform 200ms;background:var(--highlight-color)}.select-highlight{inset-inline-start:0}:host(.select-expanded) .select-highlight,:host(.ion-focused) .select-highlight{-webkit-transform:scale(1);transform:scale(1)}:host(.in-item) .select-highlight{bottom:0}:host(.in-item) .select-highlight{inset-inline-start:0}:host(.select-expanded:not(.has-expanded-icon)) .select-icon{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.select-expanded) .select-wrapper .select-icon,:host(.has-focus.ion-valid) .select-wrapper .select-icon,:host(.ion-touched.ion-invalid) .select-wrapper .select-icon,:host(.ion-focused) .select-wrapper .select-icon{color:var(--highlight-color)}:host(.select-shape-round){--border-radius:16px}:host(.select-label-placement-stacked) .select-wrapper-inner,:host(.select-label-placement-floating) .select-wrapper-inner{width:calc(100% - 0.8125rem - 4px)}:host(.select-disabled){opacity:0.38}::slotted(ion-button[slot=start].button-has-icon-only),::slotted(ion-button[slot=end].button-has-icon-only){--border-radius:50%;--padding-start:8px;--padding-end:8px;--padding-top:8px;--padding-bottom:8px;aspect-ratio:1;min-height:40px}\";\nconst IonSelectMdStyle0 = selectMdCss;\n\nconst Select = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionCancel = createEvent(this, \"ionCancel\", 7);\n        this.ionDismiss = createEvent(this, \"ionDismiss\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inputId = `ion-sel-${selectIds++}`;\n        this.inheritedAttributes = {};\n        this.onClick = (ev) => {\n            const target = ev.target;\n            const closestSlot = target.closest('[slot=\"start\"], [slot=\"end\"]');\n            if (target === this.el || closestSlot === null) {\n                this.setFocus();\n                this.open(ev);\n            }\n            else {\n                /**\n                 * Prevent clicks to the start/end slots from opening the select.\n                 * We ensure the target isn't this element in case the select is slotted\n                 * in, for example, an item. This would prevent the select from ever\n                 * being opened since the element itself has slot=\"start\"/\"end\".\n                 *\n                 * Clicking a slotted element also causes a click\n                 * on the <label> element (since it wraps the slots).\n                 * Clicking <label> dispatches another click event on\n                 * the native form control that then bubbles up to this\n                 * listener. This additional event targets the host\n                 * element, so the select overlay is opened.\n                 *\n                 * When the slotted elements are clicked (and therefore\n                 * the ancestor <label> element) we want to prevent the label\n                 * from dispatching another click event.\n                 *\n                 * Do not call stopPropagation() because this will cause\n                 * click handlers on the slotted elements to never fire in React.\n                 * When developers do onClick in React a native \"click\" listener\n                 * is added on the root element, not the slotted element. When that\n                 * native click listener fires, React then dispatches the synthetic\n                 * click event on the slotted element. However, if stopPropagation\n                 * is called then the native click event will never bubble up\n                 * to the root element.\n                 */\n                ev.preventDefault();\n            }\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.isExpanded = false;\n        this.cancelText = 'Cancel';\n        this.color = undefined;\n        this.compareWith = undefined;\n        this.disabled = false;\n        this.fill = undefined;\n        this.interface = 'alert';\n        this.interfaceOptions = {};\n        this.justify = undefined;\n        this.label = undefined;\n        this.labelPlacement = 'start';\n        this.multiple = false;\n        this.name = this.inputId;\n        this.okText = 'OK';\n        this.placeholder = undefined;\n        this.selectedText = undefined;\n        this.toggleIcon = undefined;\n        this.expandedIcon = undefined;\n        this.shape = undefined;\n        this.value = undefined;\n    }\n    styleChanged() {\n        this.emitStyle();\n    }\n    setValue(value) {\n        this.value = value;\n        this.ionChange.emit({ value });\n    }\n    async connectedCallback() {\n        const { el } = this;\n        this.notchController = createNotchController(el, () => this.notchSpacerEl, () => this.labelSlot);\n        this.updateOverlayOptions();\n        this.emitStyle();\n        this.mutationO = watchForOptions(this.el, 'ion-select-option', async () => {\n            this.updateOverlayOptions();\n            /**\n             * We need to re-render the component\n             * because one of the new ion-select-option\n             * elements may match the value. In this case,\n             * the rendered selected text should be updated.\n             */\n            forceUpdate(this);\n        });\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAttributes(this.el, ['aria-label']);\n    }\n    componentDidLoad() {\n        /**\n         * If any of the conditions that trigger the styleChanged callback\n         * are met on component load, it is possible the event emitted\n         * prior to a parent web component registering an event listener.\n         *\n         * To ensure the parent web component receives the event, we\n         * emit the style event again after the component has loaded.\n         *\n         * This is often seen in Angular with the `dist` output target.\n         */\n        this.emitStyle();\n    }\n    disconnectedCallback() {\n        if (this.mutationO) {\n            this.mutationO.disconnect();\n            this.mutationO = undefined;\n        }\n        if (this.notchController) {\n            this.notchController.destroy();\n            this.notchController = undefined;\n        }\n    }\n    /**\n     * Open the select overlay. The overlay is either an alert, action sheet, or popover,\n     * depending on the `interface` property on the `ion-select`.\n     *\n     * @param event The user interface event that called the open.\n     */\n    async open(event) {\n        if (this.disabled || this.isExpanded) {\n            return undefined;\n        }\n        this.isExpanded = true;\n        const overlay = (this.overlay = await this.createOverlay(event));\n        overlay.onDidDismiss().then(() => {\n            this.overlay = undefined;\n            this.isExpanded = false;\n            this.ionDismiss.emit();\n            this.setFocus();\n        });\n        await overlay.present();\n        // focus selected option for popovers and modals\n        if (this.interface === 'popover' || this.interface === 'modal') {\n            const indexOfSelected = this.childOpts.findIndex((o) => o.value === this.value);\n            if (indexOfSelected > -1) {\n                const selectedItem = overlay.querySelector(`.select-interface-option:nth-child(${indexOfSelected + 1})`);\n                if (selectedItem) {\n                    /**\n                     * Browsers such as Firefox do not\n                     * correctly delegate focus when manually\n                     * focusing an element with delegatesFocus.\n                     * We work around this by manually focusing\n                     * the interactive element.\n                     * ion-radio and ion-checkbox are the only\n                     * elements that ion-select-popover uses, so\n                     * we only need to worry about those two components\n                     * when focusing.\n                     */\n                    const interactiveEl = selectedItem.querySelector('ion-radio, ion-checkbox');\n                    if (interactiveEl) {\n                        // Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n                        // and removing `ion-focused` style\n                        interactiveEl.setFocus();\n                    }\n                    focusVisibleElement(selectedItem);\n                }\n            }\n            else {\n                /**\n                 * If no value is set then focus the first enabled option.\n                 */\n                const firstEnabledOption = overlay.querySelector('ion-radio:not(.radio-disabled), ion-checkbox:not(.checkbox-disabled)');\n                if (firstEnabledOption) {\n                    /**\n                     * Focus the option for the same reason as we do above.\n                     *\n                     * Needs to be called before `focusVisibleElement` to prevent issue with focus event bubbling\n                     * and removing `ion-focused` style\n                     */\n                    firstEnabledOption.setFocus();\n                    focusVisibleElement(firstEnabledOption.closest('ion-item'));\n                }\n            }\n        }\n        return overlay;\n    }\n    createOverlay(ev) {\n        let selectInterface = this.interface;\n        if (selectInterface === 'action-sheet' && this.multiple) {\n            console.warn(`Select interface cannot be \"${selectInterface}\" with a multi-value select. Using the \"alert\" interface instead.`);\n            selectInterface = 'alert';\n        }\n        if (selectInterface === 'popover' && !ev) {\n            console.warn(`Select interface cannot be a \"${selectInterface}\" without passing an event. Using the \"alert\" interface instead.`);\n            selectInterface = 'alert';\n        }\n        if (selectInterface === 'action-sheet') {\n            return this.openActionSheet();\n        }\n        if (selectInterface === 'popover') {\n            return this.openPopover(ev);\n        }\n        if (selectInterface === 'modal') {\n            return this.openModal();\n        }\n        return this.openAlert();\n    }\n    updateOverlayOptions() {\n        const overlay = this.overlay;\n        if (!overlay) {\n            return;\n        }\n        const childOpts = this.childOpts;\n        const value = this.value;\n        switch (this.interface) {\n            case 'action-sheet':\n                overlay.buttons = this.createActionSheetButtons(childOpts, value);\n                break;\n            case 'popover':\n                const popover = overlay.querySelector('ion-select-popover');\n                if (popover) {\n                    popover.options = this.createOverlaySelectOptions(childOpts, value);\n                }\n                break;\n            case 'modal':\n                const modal = overlay.querySelector('ion-select-modal');\n                if (modal) {\n                    modal.options = this.createOverlaySelectOptions(childOpts, value);\n                }\n                break;\n            case 'alert':\n                const inputType = this.multiple ? 'checkbox' : 'radio';\n                overlay.inputs = this.createAlertInputs(childOpts, inputType, value);\n                break;\n        }\n    }\n    createActionSheetButtons(data, selectValue) {\n        const actionSheetButtons = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                role: isOptionSelected(selectValue, value, this.compareWith) ? 'selected' : '',\n                text: option.textContent,\n                cssClass: optClass,\n                handler: () => {\n                    this.setValue(value);\n                },\n            };\n        });\n        // Add \"cancel\" button\n        actionSheetButtons.push({\n            text: this.cancelText,\n            role: 'cancel',\n            handler: () => {\n                this.ionCancel.emit();\n            },\n        });\n        return actionSheetButtons;\n    }\n    createAlertInputs(data, inputType, selectValue) {\n        const alertInputs = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                type: inputType,\n                cssClass: optClass,\n                label: option.textContent || '',\n                value,\n                checked: isOptionSelected(selectValue, value, this.compareWith),\n                disabled: option.disabled,\n            };\n        });\n        return alertInputs;\n    }\n    createOverlaySelectOptions(data, selectValue) {\n        const popoverOptions = data.map((option) => {\n            const value = getOptionValue(option);\n            // Remove hydrated before copying over classes\n            const copyClasses = Array.from(option.classList)\n                .filter((cls) => cls !== 'hydrated')\n                .join(' ');\n            const optClass = `${OPTION_CLASS} ${copyClasses}`;\n            return {\n                text: option.textContent || '',\n                cssClass: optClass,\n                value,\n                checked: isOptionSelected(selectValue, value, this.compareWith),\n                disabled: option.disabled,\n                handler: (selected) => {\n                    this.setValue(selected);\n                    if (!this.multiple) {\n                        this.close();\n                    }\n                },\n            };\n        });\n        return popoverOptions;\n    }\n    async openPopover(ev) {\n        const { fill, labelPlacement } = this;\n        const interfaceOptions = this.interfaceOptions;\n        const mode = getIonMode(this);\n        const showBackdrop = mode === 'md' ? false : true;\n        const multiple = this.multiple;\n        const value = this.value;\n        let event = ev;\n        let size = 'auto';\n        const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n        /**\n         * The popover should take up the full width\n         * when using a fill in MD mode or if the\n         * label is floating/stacked.\n         */\n        if (hasFloatingOrStackedLabel || (mode === 'md' && fill !== undefined)) {\n            size = 'cover';\n            /**\n             * Otherwise the popover\n             * should be positioned relative\n             * to the native element.\n             */\n        }\n        else {\n            event = Object.assign(Object.assign({}, ev), { detail: {\n                    ionShadowTarget: this.nativeWrapperEl,\n                } });\n        }\n        const popoverOpts = Object.assign(Object.assign({ mode,\n            event, alignment: 'center', size,\n            showBackdrop }, interfaceOptions), { component: 'ion-select-popover', cssClass: ['select-popover', interfaceOptions.cssClass], componentProps: {\n                header: interfaceOptions.header,\n                subHeader: interfaceOptions.subHeader,\n                message: interfaceOptions.message,\n                multiple,\n                value,\n                options: this.createOverlaySelectOptions(this.childOpts, value),\n            } });\n        return popoverController.create(popoverOpts);\n    }\n    async openActionSheet() {\n        const mode = getIonMode(this);\n        const interfaceOptions = this.interfaceOptions;\n        const actionSheetOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { buttons: this.createActionSheetButtons(this.childOpts, this.value), cssClass: ['select-action-sheet', interfaceOptions.cssClass] });\n        return actionSheetController.create(actionSheetOpts);\n    }\n    async openAlert() {\n        const interfaceOptions = this.interfaceOptions;\n        const inputType = this.multiple ? 'checkbox' : 'radio';\n        const mode = getIonMode(this);\n        const alertOpts = Object.assign(Object.assign({ mode }, interfaceOptions), { header: interfaceOptions.header ? interfaceOptions.header : this.labelText, inputs: this.createAlertInputs(this.childOpts, inputType, this.value), buttons: [\n                {\n                    text: this.cancelText,\n                    role: 'cancel',\n                    handler: () => {\n                        this.ionCancel.emit();\n                    },\n                },\n                {\n                    text: this.okText,\n                    handler: (selectedValues) => {\n                        this.setValue(selectedValues);\n                    },\n                },\n            ], cssClass: [\n                'select-alert',\n                interfaceOptions.cssClass,\n                this.multiple ? 'multiple-select-alert' : 'single-select-alert',\n            ] });\n        return alertController.create(alertOpts);\n    }\n    openModal() {\n        const { multiple, value, interfaceOptions } = this;\n        const mode = getIonMode(this);\n        const modalOpts = Object.assign(Object.assign({}, interfaceOptions), { mode, cssClass: ['select-modal', interfaceOptions.cssClass], component: 'ion-select-modal', componentProps: {\n                header: interfaceOptions.header,\n                multiple,\n                value,\n                options: this.createOverlaySelectOptions(this.childOpts, value),\n            } });\n        return modalController.create(modalOpts);\n    }\n    /**\n     * Close the select interface.\n     */\n    close() {\n        if (!this.overlay) {\n            return Promise.resolve(false);\n        }\n        return this.overlay.dismiss();\n    }\n    hasValue() {\n        return this.getText() !== '';\n    }\n    get childOpts() {\n        return Array.from(this.el.querySelectorAll('ion-select-option'));\n    }\n    /**\n     * Returns any plaintext associated with\n     * the label (either prop or slot).\n     * Note: This will not return any custom\n     * HTML. Use the `hasLabel` getter if you\n     * want to know if any slotted label content\n     * was passed.\n     */\n    get labelText() {\n        const { label } = this;\n        if (label !== undefined) {\n            return label;\n        }\n        const { labelSlot } = this;\n        if (labelSlot !== null) {\n            return labelSlot.textContent;\n        }\n        return;\n    }\n    getText() {\n        const selectedText = this.selectedText;\n        if (selectedText != null && selectedText !== '') {\n            return selectedText;\n        }\n        return generateText(this.childOpts, this.value, this.compareWith);\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    emitStyle() {\n        const { disabled } = this;\n        const style = {\n            'interactive-disabled': disabled,\n        };\n        this.ionStyle.emit(style);\n    }\n    renderLabel() {\n        const { label } = this;\n        return (h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !this.hasLabel,\n            }, part: \"label\" }, label === undefined ? h(\"slot\", { name: \"label\" }) : h(\"div\", { class: \"label-text\" }, label)));\n    }\n    componentDidRender() {\n        var _a;\n        (_a = this.notchController) === null || _a === void 0 ? void 0 : _a.calculateNotchWidth();\n    }\n    /**\n     * Gets any content passed into the `label` slot,\n     * not the <slot> definition.\n     */\n    get labelSlot() {\n        return this.el.querySelector('[slot=\"label\"]');\n    }\n    /**\n     * Returns `true` if label content is provided\n     * either by a prop or a content. If you want\n     * to get the plaintext value of the label use\n     * the `labelText` getter instead.\n     */\n    get hasLabel() {\n        return this.label !== undefined || this.labelSlot !== null;\n    }\n    /**\n     * Renders the border container\n     * when fill=\"outline\".\n     */\n    renderLabelContainer() {\n        const mode = getIonMode(this);\n        const hasOutlineFill = mode === 'md' && this.fill === 'outline';\n        if (hasOutlineFill) {\n            /**\n             * The outline fill has a special outline\n             * that appears around the select and the label.\n             * Certain stacked and floating label placements cause the\n             * label to translate up and create a \"cut out\"\n             * inside of that border by using the notch-spacer element.\n             */\n            return [\n                h(\"div\", { class: \"select-outline-container\" }, h(\"div\", { class: \"select-outline-start\" }), h(\"div\", { class: {\n                        'select-outline-notch': true,\n                        'select-outline-notch-hidden': !this.hasLabel,\n                    } }, h(\"div\", { class: \"notch-spacer\", \"aria-hidden\": \"true\", ref: (el) => (this.notchSpacerEl = el) }, this.label)), h(\"div\", { class: \"select-outline-end\" })),\n                this.renderLabel(),\n            ];\n        }\n        /**\n         * If not using the outline style,\n         * we can render just the label.\n         */\n        return this.renderLabel();\n    }\n    /**\n     * Renders either the placeholder\n     * or the selected values based on\n     * the state of the select.\n     */\n    renderSelectText() {\n        const { placeholder } = this;\n        const displayValue = this.getText();\n        let addPlaceholderClass = false;\n        let selectText = displayValue;\n        if (selectText === '' && placeholder !== undefined) {\n            selectText = placeholder;\n            addPlaceholderClass = true;\n        }\n        const selectTextClasses = {\n            'select-text': true,\n            'select-placeholder': addPlaceholderClass,\n        };\n        const textPart = addPlaceholderClass ? 'placeholder' : 'text';\n        return (h(\"div\", { \"aria-hidden\": \"true\", class: selectTextClasses, part: textPart }, selectText));\n    }\n    /**\n     * Renders the chevron icon\n     * next to the select text.\n     */\n    renderSelectIcon() {\n        const mode = getIonMode(this);\n        const { isExpanded, toggleIcon, expandedIcon } = this;\n        let icon;\n        if (isExpanded && expandedIcon !== undefined) {\n            icon = expandedIcon;\n        }\n        else {\n            const defaultIcon = mode === 'ios' ? chevronExpand : caretDownSharp;\n            icon = toggleIcon !== null && toggleIcon !== void 0 ? toggleIcon : defaultIcon;\n        }\n        return h(\"ion-icon\", { class: \"select-icon\", part: \"icon\", \"aria-hidden\": \"true\", icon: icon });\n    }\n    get ariaLabel() {\n        var _a;\n        const { placeholder, inheritedAttributes } = this;\n        const displayValue = this.getText();\n        // The aria label should be preferred over visible text if both are specified\n        const definedLabel = (_a = inheritedAttributes['aria-label']) !== null && _a !== void 0 ? _a : this.labelText;\n        /**\n         * If developer has specified a placeholder\n         * and there is nothing selected, the selectText\n         * should have the placeholder value.\n         */\n        let renderedLabel = displayValue;\n        if (renderedLabel === '' && placeholder !== undefined) {\n            renderedLabel = placeholder;\n        }\n        /**\n         * If there is a developer-defined label,\n         * then we need to concatenate the developer label\n         * string with the current current value.\n         * The label for the control should be read\n         * before the values of the control.\n         */\n        if (definedLabel !== undefined) {\n            renderedLabel = renderedLabel === '' ? definedLabel : `${definedLabel}, ${renderedLabel}`;\n        }\n        return renderedLabel;\n    }\n    renderListbox() {\n        const { disabled, inputId, isExpanded } = this;\n        return (h(\"button\", { disabled: disabled, id: inputId, \"aria-label\": this.ariaLabel, \"aria-haspopup\": \"dialog\", \"aria-expanded\": `${isExpanded}`, onFocus: this.onFocus, onBlur: this.onBlur, ref: (focusEl) => (this.focusEl = focusEl) }));\n    }\n    render() {\n        const { disabled, el, isExpanded, expandedIcon, labelPlacement, justify, placeholder, fill, shape, name, value } = this;\n        const mode = getIonMode(this);\n        const hasFloatingOrStackedLabel = labelPlacement === 'floating' || labelPlacement === 'stacked';\n        const justifyEnabled = !hasFloatingOrStackedLabel && justify !== undefined;\n        const rtl = isRTL(el) ? 'rtl' : 'ltr';\n        const inItem = hostContext('ion-item', this.el);\n        const shouldRenderHighlight = mode === 'md' && fill !== 'outline' && !inItem;\n        const hasValue = this.hasValue();\n        const hasStartEndSlots = el.querySelector('[slot=\"start\"], [slot=\"end\"]') !== null;\n        renderHiddenInput(true, el, name, parseValue(value), disabled);\n        /**\n         * If the label is stacked, it should always sit above the select.\n         * For floating labels, the label should move above the select if\n         * the select has a value, is open, or has anything in either\n         * the start or end slot.\n         *\n         * If there is content in the start slot, the label would overlap\n         * it if not forced to float. This is also applied to the end slot\n         * because with the default or solid fills, the select is not\n         * vertically centered in the container, but the label is. This\n         * causes the slots and label to appear vertically offset from each\n         * other when the label isn't floating above the input. This doesn't\n         * apply to the outline fill, but this was not accounted for to keep\n         * things consistent.\n         *\n         * TODO(FW-5592): Remove hasStartEndSlots condition\n         */\n        const labelShouldFloat = labelPlacement === 'stacked' || (labelPlacement === 'floating' && (hasValue || isExpanded || hasStartEndSlots));\n        return (h(Host, { key: '144dfa5c49549a74fe516c65b9b8104a477ac789', onClick: this.onClick, class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item': inItem,\n                'in-item-color': hostContext('ion-item.ion-color', el),\n                'select-disabled': disabled,\n                'select-expanded': isExpanded,\n                'has-expanded-icon': expandedIcon !== undefined,\n                'has-value': hasValue,\n                'label-floating': labelShouldFloat,\n                'has-placeholder': placeholder !== undefined,\n                'ion-focusable': true,\n                [`select-${rtl}`]: true,\n                [`select-fill-${fill}`]: fill !== undefined,\n                [`select-justify-${justify}`]: justifyEnabled,\n                [`select-shape-${shape}`]: shape !== undefined,\n                [`select-label-placement-${labelPlacement}`]: true,\n            }) }, h(\"label\", { key: '0edcfcbac575a9dccc77991531b6980d1caebf42', class: \"select-wrapper\", id: \"select-label\" }, this.renderLabelContainer(), h(\"div\", { key: '348151d90cb093f5d21c7d4a834264ac4a312c40', class: \"select-wrapper-inner\" }, h(\"slot\", { key: '8b7708c7f81217435c58276da0c08bba766d9500', name: \"start\" }), h(\"div\", { key: '10c520a335da0a0d1cf40f9365597beb244d3b48', class: \"native-wrapper\", ref: (el) => (this.nativeWrapperEl = el), part: \"container\" }, this.renderSelectText(), this.renderListbox()), h(\"slot\", { key: '0f15c40a5495e98e29d2a21ba21e0bc6f1c0125a', name: \"end\" }), !hasFloatingOrStackedLabel && this.renderSelectIcon()), hasFloatingOrStackedLabel && this.renderSelectIcon(), shouldRenderHighlight && h(\"div\", { key: 'c87faad2e5ebf7f9453397d7ede43abd64d21294', class: \"select-highlight\" }))));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"disabled\": [\"styleChanged\"],\n        \"isExpanded\": [\"styleChanged\"],\n        \"placeholder\": [\"styleChanged\"],\n        \"value\": [\"styleChanged\"]\n    }; }\n};\nconst getOptionValue = (el) => {\n    const value = el.value;\n    return value === undefined ? el.textContent || '' : value;\n};\nconst parseValue = (value) => {\n    if (value == null) {\n        return undefined;\n    }\n    if (Array.isArray(value)) {\n        return value.join(',');\n    }\n    return value.toString();\n};\nconst generateText = (opts, value, compareWith) => {\n    if (value === undefined) {\n        return '';\n    }\n    if (Array.isArray(value)) {\n        return value\n            .map((v) => textForValue(opts, v, compareWith))\n            .filter((opt) => opt !== null)\n            .join(', ');\n    }\n    else {\n        return textForValue(opts, value, compareWith) || '';\n    }\n};\nconst textForValue = (opts, value, compareWith) => {\n    const selectOpt = opts.find((opt) => {\n        return compareOptions(value, getOptionValue(opt), compareWith);\n    });\n    return selectOpt ? selectOpt.textContent : null;\n};\nlet selectIds = 0;\nconst OPTION_CLASS = 'select-interface-option';\nSelect.style = {\n    ios: IonSelectIosStyle0,\n    md: IonSelectMdStyle0\n};\n\nconst selectOptionCss = \":host{display:none}\";\nconst IonSelectOptionStyle0 = selectOptionCss;\n\nconst SelectOption = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inputId = `ion-selopt-${selectOptionIds++}`;\n        this.disabled = false;\n        this.value = undefined;\n    }\n    render() {\n        return h(Host, { key: '2e6fa159464f04f6d8720f960141f67918bc7534', role: \"option\", id: this.inputId, class: getIonMode(this) });\n    }\n    get el() { return getElement(this); }\n};\nlet selectOptionIds = 0;\nSelectOption.style = IonSelectOptionStyle0;\n\nconst selectPopoverIosCss = \".sc-ion-select-popover-ios-h ion-list.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-ios,ion-label.sc-ion-select-popover-ios{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-ios-h{overflow-y:auto}\";\nconst IonSelectPopoverIosStyle0 = selectPopoverIosCss;\n\nconst selectPopoverMdCss = \".sc-ion-select-popover-md-h ion-list.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-list-header.sc-ion-select-popover-md,ion-label.sc-ion-select-popover-md{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}.sc-ion-select-popover-md-h{overflow-y:auto}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(container){display:none}ion-list.sc-ion-select-popover-md ion-radio.sc-ion-select-popover-md::part(label){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}ion-item.sc-ion-select-popover-md{--inner-border-width:0}.item-radio-checked.sc-ion-select-popover-md{--background:rgba(var(--ion-color-primary-rgb, 0, 84, 233), 0.08);--background-focused:var(--ion-color-primary, #0054e9);--background-focused-opacity:0.2;--background-hover:var(--ion-color-primary, #0054e9);--background-hover-opacity:0.12}.item-checkbox-checked.sc-ion-select-popover-md{--background-activated:var(--ion-item-color, var(--ion-text-color, #000));--background-focused:var(--ion-item-color, var(--ion-text-color, #000));--background-hover:var(--ion-item-color, var(--ion-text-color, #000));--color:var(--ion-color-primary, #0054e9)}\";\nconst IonSelectPopoverMdStyle0 = selectPopoverMdCss;\n\nconst SelectPopover = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.header = undefined;\n        this.subHeader = undefined;\n        this.message = undefined;\n        this.multiple = undefined;\n        this.options = [];\n    }\n    findOptionFromEvent(ev) {\n        const { options } = this;\n        return options.find((o) => o.value === ev.target.value);\n    }\n    /**\n     * When an option is selected we need to get the value(s)\n     * of the selected option(s) and return it in the option\n     * handler\n     */\n    callOptionHandler(ev) {\n        const option = this.findOptionFromEvent(ev);\n        const values = this.getValues(ev);\n        if (option === null || option === void 0 ? void 0 : option.handler) {\n            safeCall(option.handler, values);\n        }\n    }\n    /**\n     * Dismisses the host popover that the `ion-select-popover`\n     * is rendered within.\n     */\n    dismissParentPopover() {\n        const popover = this.el.closest('ion-popover');\n        if (popover) {\n            popover.dismiss();\n        }\n    }\n    setChecked(ev) {\n        const { multiple } = this;\n        const option = this.findOptionFromEvent(ev);\n        // this is a popover with checkboxes (multiple value select)\n        // we need to set the checked value for this option\n        if (multiple && option) {\n            option.checked = ev.detail.checked;\n        }\n    }\n    getValues(ev) {\n        const { multiple, options } = this;\n        if (multiple) {\n            // this is a popover with checkboxes (multiple value select)\n            // return an array of all the checked values\n            return options.filter((o) => o.checked).map((o) => o.value);\n        }\n        // this is a popover with radio buttons (single value select)\n        // return the value that was clicked, otherwise undefined\n        const option = this.findOptionFromEvent(ev);\n        return option ? option.value : undefined;\n    }\n    renderOptions(options) {\n        const { multiple } = this;\n        switch (multiple) {\n            case true:\n                return this.renderCheckboxOptions(options);\n            default:\n                return this.renderRadioOptions(options);\n        }\n    }\n    renderCheckboxOptions(options) {\n        return options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-checkbox-checked': option.checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-checkbox\", { value: option.value, disabled: option.disabled, checked: option.checked, justify: \"start\", labelPlacement: \"end\", onIonChange: (ev) => {\n                this.setChecked(ev);\n                this.callOptionHandler(ev);\n                // TODO FW-4784\n                forceUpdate(this);\n            } }, option.text))));\n    }\n    renderRadioOptions(options) {\n        const checked = options.filter((o) => o.checked).map((o) => o.value)[0];\n        return (h(\"ion-radio-group\", { value: checked, onIonChange: (ev) => this.callOptionHandler(ev) }, options.map((option) => (h(\"ion-item\", { class: Object.assign({\n                // TODO FW-4784\n                'item-radio-checked': option.value === checked\n            }, getClassMap(option.cssClass)) }, h(\"ion-radio\", { value: option.value, disabled: option.disabled, onClick: () => this.dismissParentPopover(), onKeyUp: (ev) => {\n                if (ev.key === ' ') {\n                    /**\n                     * Selecting a radio option with keyboard navigation,\n                     * either through the Enter or Space keys, should\n                     * dismiss the popover.\n                     */\n                    this.dismissParentPopover();\n                }\n            } }, option.text))))));\n    }\n    render() {\n        const { header, message, options, subHeader } = this;\n        const hasSubHeaderOrMessage = subHeader !== undefined || message !== undefined;\n        return (h(Host, { key: 'dd0990db4de4f175b176b27f35501dd1604ca400', class: getIonMode(this) }, h(\"ion-list\", { key: 'cea2bc3687b8b2490a2a9ff4a1e16cd34169558e' }, header !== undefined && h(\"ion-list-header\", { key: '194aebb53453c43f913daae45a1a3066a1708c4c' }, header), hasSubHeaderOrMessage && (h(\"ion-item\", { key: 'b706b07a3c63ad8104d2db413e210c735ec1bec9' }, h(\"ion-label\", { key: '6e52b5b4cf6b04ff3dd5671d64264233de4190d5', class: \"ion-text-wrap\" }, subHeader !== undefined && h(\"h3\", { key: '6ef4440d17f5db8c96c63b9aa5073f4fe4b8ad62' }, subHeader), message !== undefined && h(\"p\", { key: 'c7b3b76c30ecd606c0e985a0258c13d3a75756e7' }, message)))), this.renderOptions(options))));\n    }\n    get el() { return getElement(this); }\n};\nSelectPopover.style = {\n    ios: IonSelectPopoverIosStyle0,\n    md: IonSelectPopoverMdStyle0\n};\n\nexport { Select as ion_select, SelectOption as ion_select_option, SelectPopover as ion_select_popover };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,QAAQ,qBAAqB;AAC9H,SAASC,CAAC,IAAIC,qBAAqB,QAAQ,gCAAgC;AAC3E,SAASL,CAAC,IAAIM,gBAAgB,EAAEF,CAAC,IAAIG,cAAc,QAAQ,kCAAkC;AAC7F,SAASV,CAAC,IAAIW,iBAAiB,EAAEV,CAAC,IAAIW,mBAAmB,EAAEd,CAAC,IAAIe,iBAAiB,QAAQ,uBAAuB;AAChH,SAASN,CAAC,IAAIO,iBAAiB,EAAEC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,QAAQ,QAAQ,wBAAwB;AACtJ,SAASnB,CAAC,IAAIoB,KAAK,QAAQ,mBAAmB;AAC9C,SAASvB,CAAC,IAAIwB,WAAW,EAAEjB,CAAC,IAAIkB,kBAAkB,EAAEC,CAAC,IAAIC,WAAW,QAAQ,qBAAqB;AACjG,SAASC,CAAC,IAAIC,eAAe,QAAQ,6BAA6B;AAClE,SAASD,CAAC,IAAIE,aAAa,EAAEC,CAAC,IAAIC,cAAc,QAAQ,qBAAqB;AAC7E,SAASjB,CAAC,IAAIkB,UAAU,QAAQ,4BAA4B;AAC5D,OAAO,qBAAqB;AAC5B,OAAO,oCAAoC;AAC3C,OAAO,kCAAkC;AACzC,OAAO,kCAAkC;AACzC,OAAO,qBAAqB;AAE5B,MAAMC,YAAY,GAAG,qyPAAqyP;AAC1zP,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,uteAAute;AAC3ue,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IACjB3C,gBAAgB,CAAC,IAAI,EAAE2C,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAG1C,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC2C,SAAS,GAAG3C,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC4C,UAAU,GAAG5C,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACpD,IAAI,CAAC6C,QAAQ,GAAG7C,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC8C,OAAO,GAAG9C,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC+C,QAAQ,GAAG/C,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACgD,OAAO,GAAG,WAAWC,SAAS,EAAE,EAAE;IACvC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,OAAO,GAAIC,EAAE,IAAK;MACnB,MAAMC,MAAM,GAAGD,EAAE,CAACC,MAAM;MACxB,MAAMC,WAAW,GAAGD,MAAM,CAACE,OAAO,CAAC,8BAA8B,CAAC;MAClE,IAAIF,MAAM,KAAK,IAAI,CAACG,EAAE,IAAIF,WAAW,KAAK,IAAI,EAAE;QAC5C,IAAI,CAACG,QAAQ,CAAC,CAAC;QACf,IAAI,CAACC,IAAI,CAACN,EAAE,CAAC;MACjB,CAAC,MACI;QACD;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgBA,EAAE,CAACO,cAAc,CAAC,CAAC;MACvB;IACJ,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACf,QAAQ,CAACgB,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAAChB,OAAO,CAACe,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACE,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,UAAU,GAAG,QAAQ;IAC1B,IAAI,CAACC,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,WAAW,GAAGD,SAAS;IAC5B,IAAI,CAACE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAGH,SAAS;IACrB,IAAI,CAACI,SAAS,GAAG,OAAO;IACxB,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,OAAO,GAAGN,SAAS;IACxB,IAAI,CAACO,KAAK,GAAGP,SAAS;IACtB,IAAI,CAACQ,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC5B,OAAO;IACxB,IAAI,CAAC6B,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,WAAW,GAAGZ,SAAS;IAC5B,IAAI,CAACa,YAAY,GAAGb,SAAS;IAC7B,IAAI,CAACc,UAAU,GAAGd,SAAS;IAC3B,IAAI,CAACe,YAAY,GAAGf,SAAS;IAC7B,IAAI,CAACgB,KAAK,GAAGhB,SAAS;IACtB,IAAI,CAACiB,KAAK,GAAGjB,SAAS;EAC1B;EACAkB,YAAYA,CAAA,EAAG;IACX,IAAI,CAACC,SAAS,CAAC,CAAC;EACpB;EACAC,QAAQA,CAACH,KAAK,EAAE;IACZ,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACzC,SAAS,CAACmB,IAAI,CAAC;MAAEsB;IAAM,CAAC,CAAC;EAClC;EACMI,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,MAAM;QAAEjC;MAAG,CAAC,GAAGgC,KAAI;MACnBA,KAAI,CAACE,eAAe,GAAGjF,qBAAqB,CAAC+C,EAAE,EAAE,MAAMgC,KAAI,CAACG,aAAa,EAAE,MAAMH,KAAI,CAACI,SAAS,CAAC;MAChGJ,KAAI,CAACK,oBAAoB,CAAC,CAAC;MAC3BL,KAAI,CAACH,SAAS,CAAC,CAAC;MAChBG,KAAI,CAACM,SAAS,GAAGhE,eAAe,CAAC0D,KAAI,CAAChC,EAAE,EAAE,mBAAmB,eAAAiC,iBAAA,CAAE,aAAY;QACvED,KAAI,CAACK,oBAAoB,CAAC,CAAC;QAC3B;AACZ;AACA;AACA;AACA;AACA;QACYtF,WAAW,CAACiF,KAAI,CAAC;MACrB,CAAC,EAAC;IAAC;EACP;EACAO,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC7C,mBAAmB,GAAGtC,iBAAiB,CAAC,IAAI,CAAC4C,EAAE,EAAE,CAAC,YAAY,CAAC,CAAC;EACzE;EACAwC,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACX,SAAS,CAAC,CAAC;EACpB;EACAY,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACH,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACI,UAAU,CAAC,CAAC;MAC3B,IAAI,CAACJ,SAAS,GAAG5B,SAAS;IAC9B;IACA,IAAI,IAAI,CAACwB,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACS,OAAO,CAAC,CAAC;MAC9B,IAAI,CAACT,eAAe,GAAGxB,SAAS;IACpC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACUR,IAAIA,CAAC0C,KAAK,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAZ,iBAAA;MACd,IAAIY,MAAI,CAACjC,QAAQ,IAAIiC,MAAI,CAACtC,UAAU,EAAE;QAClC,OAAOG,SAAS;MACpB;MACAmC,MAAI,CAACtC,UAAU,GAAG,IAAI;MACtB,MAAMuC,OAAO,GAAID,MAAI,CAACC,OAAO,SAASD,MAAI,CAACE,aAAa,CAACH,KAAK,CAAE;MAChEE,OAAO,CAACE,YAAY,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC9BJ,MAAI,CAACC,OAAO,GAAGpC,SAAS;QACxBmC,MAAI,CAACtC,UAAU,GAAG,KAAK;QACvBsC,MAAI,CAACzD,UAAU,CAACiB,IAAI,CAAC,CAAC;QACtBwC,MAAI,CAAC5C,QAAQ,CAAC,CAAC;MACnB,CAAC,CAAC;MACF,MAAM6C,OAAO,CAACI,OAAO,CAAC,CAAC;MACvB;MACA,IAAIL,MAAI,CAAC/B,SAAS,KAAK,SAAS,IAAI+B,MAAI,CAAC/B,SAAS,KAAK,OAAO,EAAE;QAC5D,MAAMqC,eAAe,GAAGN,MAAI,CAACO,SAAS,CAACC,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAC3B,KAAK,KAAKkB,MAAI,CAAClB,KAAK,CAAC;QAC/E,IAAIwB,eAAe,GAAG,CAAC,CAAC,EAAE;UACtB,MAAMI,YAAY,GAAGT,OAAO,CAACU,aAAa,CAAC,sCAAsCL,eAAe,GAAG,CAAC,GAAG,CAAC;UACxG,IAAII,YAAY,EAAE;YACd;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;YACoB,MAAME,aAAa,GAAGF,YAAY,CAACC,aAAa,CAAC,yBAAyB,CAAC;YAC3E,IAAIC,aAAa,EAAE;cACf;cACA;cACAA,aAAa,CAACxD,QAAQ,CAAC,CAAC;YAC5B;YACA5C,mBAAmB,CAACkG,YAAY,CAAC;UACrC;QACJ,CAAC,MACI;UACD;AAChB;AACA;UACgB,MAAMG,kBAAkB,GAAGZ,OAAO,CAACU,aAAa,CAAC,sEAAsE,CAAC;UACxH,IAAIE,kBAAkB,EAAE;YACpB;AACpB;AACA;AACA;AACA;AACA;YACoBA,kBAAkB,CAACzD,QAAQ,CAAC,CAAC;YAC7B5C,mBAAmB,CAACqG,kBAAkB,CAAC3D,OAAO,CAAC,UAAU,CAAC,CAAC;UAC/D;QACJ;MACJ;MACA,OAAO+C,OAAO;IAAC;EACnB;EACAC,aAAaA,CAACnD,EAAE,EAAE;IACd,IAAI+D,eAAe,GAAG,IAAI,CAAC7C,SAAS;IACpC,IAAI6C,eAAe,KAAK,cAAc,IAAI,IAAI,CAACxC,QAAQ,EAAE;MACrDyC,OAAO,CAACC,IAAI,CAAC,+BAA+BF,eAAe,mEAAmE,CAAC;MAC/HA,eAAe,GAAG,OAAO;IAC7B;IACA,IAAIA,eAAe,KAAK,SAAS,IAAI,CAAC/D,EAAE,EAAE;MACtCgE,OAAO,CAACC,IAAI,CAAC,iCAAiCF,eAAe,kEAAkE,CAAC;MAChIA,eAAe,GAAG,OAAO;IAC7B;IACA,IAAIA,eAAe,KAAK,cAAc,EAAE;MACpC,OAAO,IAAI,CAACG,eAAe,CAAC,CAAC;IACjC;IACA,IAAIH,eAAe,KAAK,SAAS,EAAE;MAC/B,OAAO,IAAI,CAACI,WAAW,CAACnE,EAAE,CAAC;IAC/B;IACA,IAAI+D,eAAe,KAAK,OAAO,EAAE;MAC7B,OAAO,IAAI,CAACK,SAAS,CAAC,CAAC;IAC3B;IACA,OAAO,IAAI,CAACC,SAAS,CAAC,CAAC;EAC3B;EACA5B,oBAAoBA,CAAA,EAAG;IACnB,MAAMS,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACA,OAAO,EAAE;MACV;IACJ;IACA,MAAMM,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMzB,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,QAAQ,IAAI,CAACb,SAAS;MAClB,KAAK,cAAc;QACfgC,OAAO,CAACoB,OAAO,GAAG,IAAI,CAACC,wBAAwB,CAACf,SAAS,EAAEzB,KAAK,CAAC;QACjE;MACJ,KAAK,SAAS;QACV,MAAMyC,OAAO,GAAGtB,OAAO,CAACU,aAAa,CAAC,oBAAoB,CAAC;QAC3D,IAAIY,OAAO,EAAE;UACTA,OAAO,CAACC,OAAO,GAAG,IAAI,CAACC,0BAA0B,CAAClB,SAAS,EAAEzB,KAAK,CAAC;QACvE;QACA;MACJ,KAAK,OAAO;QACR,MAAM4C,KAAK,GAAGzB,OAAO,CAACU,aAAa,CAAC,kBAAkB,CAAC;QACvD,IAAIe,KAAK,EAAE;UACPA,KAAK,CAACF,OAAO,GAAG,IAAI,CAACC,0BAA0B,CAAClB,SAAS,EAAEzB,KAAK,CAAC;QACrE;QACA;MACJ,KAAK,OAAO;QACR,MAAM6C,SAAS,GAAG,IAAI,CAACrD,QAAQ,GAAG,UAAU,GAAG,OAAO;QACtD2B,OAAO,CAAC2B,MAAM,GAAG,IAAI,CAACC,iBAAiB,CAACtB,SAAS,EAAEoB,SAAS,EAAE7C,KAAK,CAAC;QACpE;IACR;EACJ;EACAwC,wBAAwBA,CAACQ,IAAI,EAAEC,WAAW,EAAE;IACxC,MAAMC,kBAAkB,GAAGF,IAAI,CAACG,GAAG,CAAEC,MAAM,IAAK;MAC5C,MAAMpD,KAAK,GAAGqD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAG,GAAGC,YAAY,IAAIR,WAAW,EAAE;MACjD,OAAO;QACHS,IAAI,EAAExI,gBAAgB,CAAC0H,WAAW,EAAEjD,KAAK,EAAE,IAAI,CAAChB,WAAW,CAAC,GAAG,UAAU,GAAG,EAAE;QAC9EgF,IAAI,EAAEZ,MAAM,CAACa,WAAW;QACxBC,QAAQ,EAAEL,QAAQ;QAClBM,OAAO,EAAEA,CAAA,KAAM;UACX,IAAI,CAAChE,QAAQ,CAACH,KAAK,CAAC;QACxB;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAkD,kBAAkB,CAACkB,IAAI,CAAC;MACpBJ,IAAI,EAAE,IAAI,CAACnF,UAAU;MACrBkF,IAAI,EAAE,QAAQ;MACdI,OAAO,EAAEA,CAAA,KAAM;QACX,IAAI,CAAC3G,SAAS,CAACkB,IAAI,CAAC,CAAC;MACzB;IACJ,CAAC,CAAC;IACF,OAAOwE,kBAAkB;EAC7B;EACAH,iBAAiBA,CAACC,IAAI,EAAEH,SAAS,EAAEI,WAAW,EAAE;IAC5C,MAAMoB,WAAW,GAAGrB,IAAI,CAACG,GAAG,CAAEC,MAAM,IAAK;MACrC,MAAMpD,KAAK,GAAGqD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAG,GAAGC,YAAY,IAAIR,WAAW,EAAE;MACjD,OAAO;QACHgB,IAAI,EAAEzB,SAAS;QACfqB,QAAQ,EAAEL,QAAQ;QAClBvE,KAAK,EAAE8D,MAAM,CAACa,WAAW,IAAI,EAAE;QAC/BjE,KAAK;QACLuE,OAAO,EAAEhJ,gBAAgB,CAAC0H,WAAW,EAAEjD,KAAK,EAAE,IAAI,CAAChB,WAAW,CAAC;QAC/DC,QAAQ,EAAEmE,MAAM,CAACnE;MACrB,CAAC;IACL,CAAC,CAAC;IACF,OAAOoF,WAAW;EACtB;EACA1B,0BAA0BA,CAACK,IAAI,EAAEC,WAAW,EAAE;IAC1C,MAAMuB,cAAc,GAAGxB,IAAI,CAACG,GAAG,CAAEC,MAAM,IAAK;MACxC,MAAMpD,KAAK,GAAGqD,cAAc,CAACD,MAAM,CAAC;MACpC;MACA,MAAME,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,CAC3CC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAK,UAAU,CAAC,CACnCC,IAAI,CAAC,GAAG,CAAC;MACd,MAAMC,QAAQ,GAAG,GAAGC,YAAY,IAAIR,WAAW,EAAE;MACjD,OAAO;QACHU,IAAI,EAAEZ,MAAM,CAACa,WAAW,IAAI,EAAE;QAC9BC,QAAQ,EAAEL,QAAQ;QAClB7D,KAAK;QACLuE,OAAO,EAAEhJ,gBAAgB,CAAC0H,WAAW,EAAEjD,KAAK,EAAE,IAAI,CAAChB,WAAW,CAAC;QAC/DC,QAAQ,EAAEmE,MAAM,CAACnE,QAAQ;QACzBkF,OAAO,EAAGM,QAAQ,IAAK;UACnB,IAAI,CAACtE,QAAQ,CAACsE,QAAQ,CAAC;UACvB,IAAI,CAAC,IAAI,CAACjF,QAAQ,EAAE;YAChB,IAAI,CAACkF,KAAK,CAAC,CAAC;UAChB;QACJ;MACJ,CAAC;IACL,CAAC,CAAC;IACF,OAAOF,cAAc;EACzB;EACMpC,WAAWA,CAACnE,EAAE,EAAE;IAAA,IAAA0G,MAAA;IAAA,OAAArE,iBAAA;MAClB,MAAM;QAAEpB,IAAI;QAAEK;MAAe,CAAC,GAAGoF,MAAI;MACrC,MAAMvF,gBAAgB,GAAGuF,MAAI,CAACvF,gBAAgB;MAC9C,MAAMwF,IAAI,GAAG7H,UAAU,CAAC4H,MAAI,CAAC;MAC7B,MAAME,YAAY,GAAGD,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI;MACjD,MAAMpF,QAAQ,GAAGmF,MAAI,CAACnF,QAAQ;MAC9B,MAAMQ,KAAK,GAAG2E,MAAI,CAAC3E,KAAK;MACxB,IAAIiB,KAAK,GAAGhD,EAAE;MACd,IAAI6G,IAAI,GAAG,MAAM;MACjB,MAAMC,yBAAyB,GAAGxF,cAAc,KAAK,UAAU,IAAIA,cAAc,KAAK,SAAS;MAC/F;AACR;AACA;AACA;AACA;MACQ,IAAIwF,yBAAyB,IAAKH,IAAI,KAAK,IAAI,IAAI1F,IAAI,KAAKH,SAAU,EAAE;QACpE+F,IAAI,GAAG,OAAO;QACd;AACZ;AACA;AACA;AACA;MACQ,CAAC,MACI;QACD7D,KAAK,GAAG+D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhH,EAAE,CAAC,EAAE;UAAEiH,MAAM,EAAE;YAC/CC,eAAe,EAAER,MAAI,CAACS;UAC1B;QAAE,CAAC,CAAC;MACZ;MACA,MAAMC,WAAW,GAAGL,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEL,IAAI;QAClD3D,KAAK;QAAEqE,SAAS,EAAE,QAAQ;QAAER,IAAI;QAChCD;MAAa,CAAC,EAAEzF,gBAAgB,CAAC,EAAE;QAAEmG,SAAS,EAAE,oBAAoB;QAAErB,QAAQ,EAAE,CAAC,gBAAgB,EAAE9E,gBAAgB,CAAC8E,QAAQ,CAAC;QAAEsB,cAAc,EAAE;UAC3IC,MAAM,EAAErG,gBAAgB,CAACqG,MAAM;UAC/BC,SAAS,EAAEtG,gBAAgB,CAACsG,SAAS;UACrCC,OAAO,EAAEvG,gBAAgB,CAACuG,OAAO;UACjCnG,QAAQ;UACRQ,KAAK;UACL0C,OAAO,EAAEiC,MAAI,CAAChC,0BAA0B,CAACgC,MAAI,CAAClD,SAAS,EAAEzB,KAAK;QAClE;MAAE,CAAC,CAAC;MACR,OAAOpE,iBAAiB,CAACgK,MAAM,CAACP,WAAW,CAAC;IAAC;EACjD;EACMlD,eAAeA,CAAA,EAAG;IAAA,IAAA0D,MAAA;IAAA,OAAAvF,iBAAA;MACpB,MAAMsE,IAAI,GAAG7H,UAAU,CAAC8I,MAAI,CAAC;MAC7B,MAAMzG,gBAAgB,GAAGyG,MAAI,CAACzG,gBAAgB;MAC9C,MAAM0G,eAAe,GAAGd,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEL;MAAK,CAAC,EAAExF,gBAAgB,CAAC,EAAE;QAAEmD,OAAO,EAAEsD,MAAI,CAACrD,wBAAwB,CAACqD,MAAI,CAACpE,SAAS,EAAEoE,MAAI,CAAC7F,KAAK,CAAC;QAAEkE,QAAQ,EAAE,CAAC,qBAAqB,EAAE9E,gBAAgB,CAAC8E,QAAQ;MAAE,CAAC,CAAC;MACtN,OAAOpI,qBAAqB,CAAC8J,MAAM,CAACE,eAAe,CAAC;IAAC;EACzD;EACMxD,SAASA,CAAA,EAAG;IAAA,IAAAyD,MAAA;IAAA,OAAAzF,iBAAA;MACd,MAAMlB,gBAAgB,GAAG2G,MAAI,CAAC3G,gBAAgB;MAC9C,MAAMyD,SAAS,GAAGkD,MAAI,CAACvG,QAAQ,GAAG,UAAU,GAAG,OAAO;MACtD,MAAMoF,IAAI,GAAG7H,UAAU,CAACgJ,MAAI,CAAC;MAC7B,MAAMC,SAAS,GAAGhB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAAEL;MAAK,CAAC,EAAExF,gBAAgB,CAAC,EAAE;QAAEqG,MAAM,EAAErG,gBAAgB,CAACqG,MAAM,GAAGrG,gBAAgB,CAACqG,MAAM,GAAGM,MAAI,CAACE,SAAS;QAAEnD,MAAM,EAAEiD,MAAI,CAAChD,iBAAiB,CAACgD,MAAI,CAACtE,SAAS,EAAEoB,SAAS,EAAEkD,MAAI,CAAC/F,KAAK,CAAC;QAAEuC,OAAO,EAAE,CACjO;UACIyB,IAAI,EAAE+B,MAAI,CAAClH,UAAU;UACrBkF,IAAI,EAAE,QAAQ;UACdI,OAAO,EAAEA,CAAA,KAAM;YACX4B,MAAI,CAACvI,SAAS,CAACkB,IAAI,CAAC,CAAC;UACzB;QACJ,CAAC,EACD;UACIsF,IAAI,EAAE+B,MAAI,CAACrG,MAAM;UACjByE,OAAO,EAAG+B,cAAc,IAAK;YACzBH,MAAI,CAAC5F,QAAQ,CAAC+F,cAAc,CAAC;UACjC;QACJ,CAAC,CACJ;QAAEhC,QAAQ,EAAE,CACT,cAAc,EACd9E,gBAAgB,CAAC8E,QAAQ,EACzB6B,MAAI,CAACvG,QAAQ,GAAG,uBAAuB,GAAG,qBAAqB;MACjE,CAAC,CAAC;MACR,OAAOxD,eAAe,CAAC4J,MAAM,CAACI,SAAS,CAAC;IAAC;EAC7C;EACA3D,SAASA,CAAA,EAAG;IACR,MAAM;MAAE7C,QAAQ;MAAEQ,KAAK;MAAEZ;IAAiB,CAAC,GAAG,IAAI;IAClD,MAAMwF,IAAI,GAAG7H,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMoJ,SAAS,GAAGnB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE7F,gBAAgB,CAAC,EAAE;MAAEwF,IAAI;MAAEV,QAAQ,EAAE,CAAC,cAAc,EAAE9E,gBAAgB,CAAC8E,QAAQ,CAAC;MAAEqB,SAAS,EAAE,kBAAkB;MAAEC,cAAc,EAAE;QAC3KC,MAAM,EAAErG,gBAAgB,CAACqG,MAAM;QAC/BjG,QAAQ;QACRQ,KAAK;QACL0C,OAAO,EAAE,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAAClB,SAAS,EAAEzB,KAAK;MAClE;IAAE,CAAC,CAAC;IACR,OAAO9D,eAAe,CAAC0J,MAAM,CAACO,SAAS,CAAC;EAC5C;EACA;AACJ;AACA;EACIzB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACvD,OAAO,EAAE;MACf,OAAOiF,OAAO,CAACC,OAAO,CAAC,KAAK,CAAC;IACjC;IACA,OAAO,IAAI,CAAClF,OAAO,CAACmF,OAAO,CAAC,CAAC;EACjC;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,KAAK,EAAE;EAChC;EACA,IAAI/E,SAASA,CAAA,EAAG;IACZ,OAAO8B,KAAK,CAACC,IAAI,CAAC,IAAI,CAACnF,EAAE,CAACoI,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIR,SAASA,CAAA,EAAG;IACZ,MAAM;MAAE3G;IAAM,CAAC,GAAG,IAAI;IACtB,IAAIA,KAAK,KAAKP,SAAS,EAAE;MACrB,OAAOO,KAAK;IAChB;IACA,MAAM;MAAEmB;IAAU,CAAC,GAAG,IAAI;IAC1B,IAAIA,SAAS,KAAK,IAAI,EAAE;MACpB,OAAOA,SAAS,CAACwD,WAAW;IAChC;IACA;EACJ;EACAuC,OAAOA,CAAA,EAAG;IACN,MAAM5G,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,IAAIA,YAAY,IAAI,IAAI,IAAIA,YAAY,KAAK,EAAE,EAAE;MAC7C,OAAOA,YAAY;IACvB;IACA,OAAO8G,YAAY,CAAC,IAAI,CAACjF,SAAS,EAAE,IAAI,CAACzB,KAAK,EAAE,IAAI,CAAChB,WAAW,CAAC;EACrE;EACAV,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACqI,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,KAAK,CAAC,CAAC;IACxB;EACJ;EACA1G,SAASA,CAAA,EAAG;IACR,MAAM;MAAEjB;IAAS,CAAC,GAAG,IAAI;IACzB,MAAM4H,KAAK,GAAG;MACV,sBAAsB,EAAE5H;IAC5B,CAAC;IACD,IAAI,CAACrB,QAAQ,CAACc,IAAI,CAACmI,KAAK,CAAC;EAC7B;EACAC,WAAWA,CAAA,EAAG;IACV,MAAM;MAAExH;IAAM,CAAC,GAAG,IAAI;IACtB,OAAQxE,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE;QAClB,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAAC,IAAI,CAACC;MACvC,CAAC;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAAE3H,KAAK,KAAKP,SAAS,GAAGjE,CAAC,CAAC,MAAM,EAAE;MAAE2E,IAAI,EAAE;IAAQ,CAAC,CAAC,GAAG3E,CAAC,CAAC,KAAK,EAAE;MAAEiM,KAAK,EAAE;IAAa,CAAC,EAAEzH,KAAK,CAAC,CAAC;EAC1H;EACA4H,kBAAkBA,CAAA,EAAG;IACjB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAAC5G,eAAe,MAAM,IAAI,IAAI4G,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,mBAAmB,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;EACI,IAAI3G,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACpC,EAAE,CAACwD,aAAa,CAAC,gBAAgB,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAImF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC1H,KAAK,KAAKP,SAAS,IAAI,IAAI,CAAC0B,SAAS,KAAK,IAAI;EAC9D;EACA;AACJ;AACA;AACA;EACI4G,oBAAoBA,CAAA,EAAG;IACnB,MAAMzC,IAAI,GAAG7H,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMuK,cAAc,GAAG1C,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC1F,IAAI,KAAK,SAAS;IAC/D,IAAIoI,cAAc,EAAE;MAChB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,OAAO,CACHxM,CAAC,CAAC,KAAK,EAAE;QAAEiM,KAAK,EAAE;MAA2B,CAAC,EAAEjM,CAAC,CAAC,KAAK,EAAE;QAAEiM,KAAK,EAAE;MAAuB,CAAC,CAAC,EAAEjM,CAAC,CAAC,KAAK,EAAE;QAAEiM,KAAK,EAAE;UACvG,sBAAsB,EAAE,IAAI;UAC5B,6BAA6B,EAAE,CAAC,IAAI,CAACC;QACzC;MAAE,CAAC,EAAElM,CAAC,CAAC,KAAK,EAAE;QAAEiM,KAAK,EAAE,cAAc;QAAE,aAAa,EAAE,MAAM;QAAEQ,GAAG,EAAGlJ,EAAE,IAAM,IAAI,CAACmC,aAAa,GAAGnC;MAAI,CAAC,EAAE,IAAI,CAACiB,KAAK,CAAC,CAAC,EAAExE,CAAC,CAAC,KAAK,EAAE;QAAEiM,KAAK,EAAE;MAAqB,CAAC,CAAC,CAAC,EACpK,IAAI,CAACD,WAAW,CAAC,CAAC,CACrB;IACL;IACA;AACR;AACA;AACA;IACQ,OAAO,IAAI,CAACA,WAAW,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIU,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAE7H;IAAY,CAAC,GAAG,IAAI;IAC5B,MAAM8H,YAAY,GAAG,IAAI,CAACjB,OAAO,CAAC,CAAC;IACnC,IAAIkB,mBAAmB,GAAG,KAAK;IAC/B,IAAIC,UAAU,GAAGF,YAAY;IAC7B,IAAIE,UAAU,KAAK,EAAE,IAAIhI,WAAW,KAAKZ,SAAS,EAAE;MAChD4I,UAAU,GAAGhI,WAAW;MACxB+H,mBAAmB,GAAG,IAAI;IAC9B;IACA,MAAME,iBAAiB,GAAG;MACtB,aAAa,EAAE,IAAI;MACnB,oBAAoB,EAAEF;IAC1B,CAAC;IACD,MAAMG,QAAQ,GAAGH,mBAAmB,GAAG,aAAa,GAAG,MAAM;IAC7D,OAAQ5M,CAAC,CAAC,KAAK,EAAE;MAAE,aAAa,EAAE,MAAM;MAAEiM,KAAK,EAAEa,iBAAiB;MAAEX,IAAI,EAAEY;IAAS,CAAC,EAAEF,UAAU,CAAC;EACrG;EACA;AACJ;AACA;AACA;EACIG,gBAAgBA,CAAA,EAAG;IACf,MAAMlD,IAAI,GAAG7H,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAE6B,UAAU;MAAEiB,UAAU;MAAEC;IAAa,CAAC,GAAG,IAAI;IACrD,IAAIiI,IAAI;IACR,IAAInJ,UAAU,IAAIkB,YAAY,KAAKf,SAAS,EAAE;MAC1CgJ,IAAI,GAAGjI,YAAY;IACvB,CAAC,MACI;MACD,MAAMkI,WAAW,GAAGpD,IAAI,KAAK,KAAK,GAAGhI,aAAa,GAAGE,cAAc;MACnEiL,IAAI,GAAGlI,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGmI,WAAW;IAClF;IACA,OAAOlN,CAAC,CAAC,UAAU,EAAE;MAAEiM,KAAK,EAAE,aAAa;MAAEE,IAAI,EAAE,MAAM;MAAE,aAAa,EAAE,MAAM;MAAEc,IAAI,EAAEA;IAAK,CAAC,CAAC;EACnG;EACA,IAAIE,SAASA,CAAA,EAAG;IACZ,IAAId,EAAE;IACN,MAAM;MAAExH,WAAW;MAAE5B;IAAoB,CAAC,GAAG,IAAI;IACjD,MAAM0J,YAAY,GAAG,IAAI,CAACjB,OAAO,CAAC,CAAC;IACnC;IACA,MAAM0B,YAAY,GAAG,CAACf,EAAE,GAAGpJ,mBAAmB,CAAC,YAAY,CAAC,MAAM,IAAI,IAAIoJ,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAClB,SAAS;IAC7G;AACR;AACA;AACA;AACA;IACQ,IAAIkC,aAAa,GAAGV,YAAY;IAChC,IAAIU,aAAa,KAAK,EAAE,IAAIxI,WAAW,KAAKZ,SAAS,EAAE;MACnDoJ,aAAa,GAAGxI,WAAW;IAC/B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIuI,YAAY,KAAKnJ,SAAS,EAAE;MAC5BoJ,aAAa,GAAGA,aAAa,KAAK,EAAE,GAAGD,YAAY,GAAG,GAAGA,YAAY,KAAKC,aAAa,EAAE;IAC7F;IACA,OAAOA,aAAa;EACxB;EACAC,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEnJ,QAAQ;MAAEpB,OAAO;MAAEe;IAAW,CAAC,GAAG,IAAI;IAC9C,OAAQ9D,CAAC,CAAC,QAAQ,EAAE;MAAEmE,QAAQ,EAAEA,QAAQ;MAAEoJ,EAAE,EAAExK,OAAO;MAAE,YAAY,EAAE,IAAI,CAACoK,SAAS;MAAE,eAAe,EAAE,QAAQ;MAAE,eAAe,EAAE,GAAGrJ,UAAU,EAAE;MAAEH,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA,MAAM;MAAE4I,GAAG,EAAGZ,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,CAAC;EAC/O;EACA2B,MAAMA,CAAA,EAAG;IACL,MAAM;MAAErJ,QAAQ;MAAEZ,EAAE;MAAEO,UAAU;MAAEkB,YAAY;MAAEP,cAAc;MAAEF,OAAO;MAAEM,WAAW;MAAET,IAAI;MAAEa,KAAK;MAAEN,IAAI;MAAEO;IAAM,CAAC,GAAG,IAAI;IACvH,MAAM4E,IAAI,GAAG7H,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMgI,yBAAyB,GAAGxF,cAAc,KAAK,UAAU,IAAIA,cAAc,KAAK,SAAS;IAC/F,MAAMgJ,cAAc,GAAG,CAACxD,yBAAyB,IAAI1F,OAAO,KAAKN,SAAS;IAC1E,MAAMyJ,GAAG,GAAGnM,KAAK,CAACgC,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;IACrC,MAAMoK,MAAM,GAAGnM,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC+B,EAAE,CAAC;IAC/C,MAAMqK,qBAAqB,GAAG9D,IAAI,KAAK,IAAI,IAAI1F,IAAI,KAAK,SAAS,IAAI,CAACuJ,MAAM;IAC5E,MAAMlC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IAChC,MAAMoC,gBAAgB,GAAGtK,EAAE,CAACwD,aAAa,CAAC,8BAA8B,CAAC,KAAK,IAAI;IAClFlG,iBAAiB,CAAC,IAAI,EAAE0C,EAAE,EAAEoB,IAAI,EAAEmJ,UAAU,CAAC5I,KAAK,CAAC,EAAEf,QAAQ,CAAC;IAC9D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAM4J,gBAAgB,GAAGtJ,cAAc,KAAK,SAAS,IAAKA,cAAc,KAAK,UAAU,KAAKgH,QAAQ,IAAI3H,UAAU,IAAI+J,gBAAgB,CAAE;IACxI,OAAQ7N,CAAC,CAACE,IAAI,EAAE;MAAE8N,GAAG,EAAE,0CAA0C;MAAE9K,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE+I,KAAK,EAAExK,kBAAkB,CAAC,IAAI,CAACuC,KAAK,EAAE;QACxH,CAAC8F,IAAI,GAAG,IAAI;QACZ,SAAS,EAAE6D,MAAM;QACjB,eAAe,EAAEnM,WAAW,CAAC,oBAAoB,EAAE+B,EAAE,CAAC;QACtD,iBAAiB,EAAEY,QAAQ;QAC3B,iBAAiB,EAAEL,UAAU;QAC7B,mBAAmB,EAAEkB,YAAY,KAAKf,SAAS;QAC/C,WAAW,EAAEwH,QAAQ;QACrB,gBAAgB,EAAEsC,gBAAgB;QAClC,iBAAiB,EAAElJ,WAAW,KAAKZ,SAAS;QAC5C,eAAe,EAAE,IAAI;QACrB,CAAC,UAAUyJ,GAAG,EAAE,GAAG,IAAI;QACvB,CAAC,eAAetJ,IAAI,EAAE,GAAGA,IAAI,KAAKH,SAAS;QAC3C,CAAC,kBAAkBM,OAAO,EAAE,GAAGkJ,cAAc;QAC7C,CAAC,gBAAgBxI,KAAK,EAAE,GAAGA,KAAK,KAAKhB,SAAS;QAC9C,CAAC,0BAA0BQ,cAAc,EAAE,GAAG;MAClD,CAAC;IAAE,CAAC,EAAEzE,CAAC,CAAC,OAAO,EAAE;MAAEgO,GAAG,EAAE,0CAA0C;MAAE/B,KAAK,EAAE,gBAAgB;MAAEsB,EAAE,EAAE;IAAe,CAAC,EAAE,IAAI,CAAChB,oBAAoB,CAAC,CAAC,EAAEvM,CAAC,CAAC,KAAK,EAAE;MAAEgO,GAAG,EAAE,0CAA0C;MAAE/B,KAAK,EAAE;IAAuB,CAAC,EAAEjM,CAAC,CAAC,MAAM,EAAE;MAAEgO,GAAG,EAAE,0CAA0C;MAAErJ,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE3E,CAAC,CAAC,KAAK,EAAE;MAAEgO,GAAG,EAAE,0CAA0C;MAAE/B,KAAK,EAAE,gBAAgB;MAAEQ,GAAG,EAAGlJ,EAAE,IAAM,IAAI,CAAC+G,eAAe,GAAG/G,EAAG;MAAE4I,IAAI,EAAE;IAAY,CAAC,EAAE,IAAI,CAACO,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACY,aAAa,CAAC,CAAC,CAAC,EAAEtN,CAAC,CAAC,MAAM,EAAE;MAAEgO,GAAG,EAAE,0CAA0C;MAAErJ,IAAI,EAAE;IAAM,CAAC,CAAC,EAAE,CAACsF,yBAAyB,IAAI,IAAI,CAAC+C,gBAAgB,CAAC,CAAC,CAAC,EAAE/C,yBAAyB,IAAI,IAAI,CAAC+C,gBAAgB,CAAC,CAAC,EAAEY,qBAAqB,IAAI5N,CAAC,CAAC,KAAK,EAAE;MAAEgO,GAAG,EAAE,0CAA0C;MAAE/B,KAAK,EAAE;IAAmB,CAAC,CAAC,CAAC,CAAC;EACtzB;EACA,IAAI1I,EAAEA,CAAA,EAAG;IAAE,OAAOnD,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW6N,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,cAAc,CAAC;MAC5B,YAAY,EAAE,CAAC,cAAc,CAAC;MAC9B,aAAa,EAAE,CAAC,cAAc,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,MAAM1F,cAAc,GAAIhF,EAAE,IAAK;EAC3B,MAAM2B,KAAK,GAAG3B,EAAE,CAAC2B,KAAK;EACtB,OAAOA,KAAK,KAAKjB,SAAS,GAAGV,EAAE,CAAC4F,WAAW,IAAI,EAAE,GAAGjE,KAAK;AAC7D,CAAC;AACD,MAAM4I,UAAU,GAAI5I,KAAK,IAAK;EAC1B,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAOjB,SAAS;EACpB;EACA,IAAIwE,KAAK,CAACyF,OAAO,CAAChJ,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CAAC4D,IAAI,CAAC,GAAG,CAAC;EAC1B;EACA,OAAO5D,KAAK,CAACiJ,QAAQ,CAAC,CAAC;AAC3B,CAAC;AACD,MAAMvC,YAAY,GAAGA,CAACwC,IAAI,EAAElJ,KAAK,EAAEhB,WAAW,KAAK;EAC/C,IAAIgB,KAAK,KAAKjB,SAAS,EAAE;IACrB,OAAO,EAAE;EACb;EACA,IAAIwE,KAAK,CAACyF,OAAO,CAAChJ,KAAK,CAAC,EAAE;IACtB,OAAOA,KAAK,CACPmD,GAAG,CAAEgG,CAAC,IAAKC,YAAY,CAACF,IAAI,EAAEC,CAAC,EAAEnK,WAAW,CAAC,CAAC,CAC9C0E,MAAM,CAAE2F,GAAG,IAAKA,GAAG,KAAK,IAAI,CAAC,CAC7BzF,IAAI,CAAC,IAAI,CAAC;EACnB,CAAC,MACI;IACD,OAAOwF,YAAY,CAACF,IAAI,EAAElJ,KAAK,EAAEhB,WAAW,CAAC,IAAI,EAAE;EACvD;AACJ,CAAC;AACD,MAAMoK,YAAY,GAAGA,CAACF,IAAI,EAAElJ,KAAK,EAAEhB,WAAW,KAAK;EAC/C,MAAMsK,SAAS,GAAGJ,IAAI,CAACK,IAAI,CAAEF,GAAG,IAAK;IACjC,OAAO7N,cAAc,CAACwE,KAAK,EAAEqD,cAAc,CAACgG,GAAG,CAAC,EAAErK,WAAW,CAAC;EAClE,CAAC,CAAC;EACF,OAAOsK,SAAS,GAAGA,SAAS,CAACrF,WAAW,GAAG,IAAI;AACnD,CAAC;AACD,IAAInG,SAAS,GAAG,CAAC;AACjB,MAAMgG,YAAY,GAAG,yBAAyB;AAC9C1G,MAAM,CAACyJ,KAAK,GAAG;EACX2C,GAAG,EAAEvM,kBAAkB;EACvBwM,EAAE,EAAEtM;AACR,CAAC;AAED,MAAMuM,eAAe,GAAG,qBAAqB;AAC7C,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,YAAY,GAAG,MAAM;EACvBvM,WAAWA,CAACC,OAAO,EAAE;IACjB3C,gBAAgB,CAAC,IAAI,EAAE2C,OAAO,CAAC;IAC/B,IAAI,CAACO,OAAO,GAAG,cAAcgM,eAAe,EAAE,EAAE;IAChD,IAAI,CAAC5K,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACe,KAAK,GAAGjB,SAAS;EAC1B;EACAuJ,MAAMA,CAAA,EAAG;IACL,OAAOxN,CAAC,CAACE,IAAI,EAAE;MAAE8N,GAAG,EAAE,0CAA0C;MAAE/E,IAAI,EAAE,QAAQ;MAAEsE,EAAE,EAAE,IAAI,CAACxK,OAAO;MAAEkJ,KAAK,EAAEhK,UAAU,CAAC,IAAI;IAAE,CAAC,CAAC;EAClI;EACA,IAAIsB,EAAEA,CAAA,EAAG;IAAE,OAAOnD,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACD,IAAI2O,eAAe,GAAG,CAAC;AACvBD,YAAY,CAAC/C,KAAK,GAAG8C,qBAAqB;AAE1C,MAAMG,mBAAmB,GAAG,iTAAiT;AAC7U,MAAMC,yBAAyB,GAAGD,mBAAmB;AAErD,MAAME,kBAAkB,GAAG,kqCAAkqC;AAC7rC,MAAMC,wBAAwB,GAAGD,kBAAkB;AAEnD,MAAME,aAAa,GAAG,MAAM;EACxB7M,WAAWA,CAACC,OAAO,EAAE;IACjB3C,gBAAgB,CAAC,IAAI,EAAE2C,OAAO,CAAC;IAC/B,IAAI,CAACmI,MAAM,GAAG1G,SAAS;IACvB,IAAI,CAAC2G,SAAS,GAAG3G,SAAS;IAC1B,IAAI,CAAC4G,OAAO,GAAG5G,SAAS;IACxB,IAAI,CAACS,QAAQ,GAAGT,SAAS;IACzB,IAAI,CAAC2D,OAAO,GAAG,EAAE;EACrB;EACAyH,mBAAmBA,CAAClM,EAAE,EAAE;IACpB,MAAM;MAAEyE;IAAQ,CAAC,GAAG,IAAI;IACxB,OAAOA,OAAO,CAAC6G,IAAI,CAAE5H,CAAC,IAAKA,CAAC,CAAC3B,KAAK,KAAK/B,EAAE,CAACC,MAAM,CAAC8B,KAAK,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;EACIoK,iBAAiBA,CAACnM,EAAE,EAAE;IAClB,MAAMmF,MAAM,GAAG,IAAI,CAAC+G,mBAAmB,CAAClM,EAAE,CAAC;IAC3C,MAAMoM,MAAM,GAAG,IAAI,CAACC,SAAS,CAACrM,EAAE,CAAC;IACjC,IAAImF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACe,OAAO,EAAE;MAChE/H,QAAQ,CAACgH,MAAM,CAACe,OAAO,EAAEkG,MAAM,CAAC;IACpC;EACJ;EACA;AACJ;AACA;AACA;EACIE,oBAAoBA,CAAA,EAAG;IACnB,MAAM9H,OAAO,GAAG,IAAI,CAACpE,EAAE,CAACD,OAAO,CAAC,aAAa,CAAC;IAC9C,IAAIqE,OAAO,EAAE;MACTA,OAAO,CAAC6D,OAAO,CAAC,CAAC;IACrB;EACJ;EACAkE,UAAUA,CAACvM,EAAE,EAAE;IACX,MAAM;MAAEuB;IAAS,CAAC,GAAG,IAAI;IACzB,MAAM4D,MAAM,GAAG,IAAI,CAAC+G,mBAAmB,CAAClM,EAAE,CAAC;IAC3C;IACA;IACA,IAAIuB,QAAQ,IAAI4D,MAAM,EAAE;MACpBA,MAAM,CAACmB,OAAO,GAAGtG,EAAE,CAACiH,MAAM,CAACX,OAAO;IACtC;EACJ;EACA+F,SAASA,CAACrM,EAAE,EAAE;IACV,MAAM;MAAEuB,QAAQ;MAAEkD;IAAQ,CAAC,GAAG,IAAI;IAClC,IAAIlD,QAAQ,EAAE;MACV;MACA;MACA,OAAOkD,OAAO,CAACgB,MAAM,CAAE/B,CAAC,IAAKA,CAAC,CAAC4C,OAAO,CAAC,CAACpB,GAAG,CAAExB,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC;IAC/D;IACA;IACA;IACA,MAAMoD,MAAM,GAAG,IAAI,CAAC+G,mBAAmB,CAAClM,EAAE,CAAC;IAC3C,OAAOmF,MAAM,GAAGA,MAAM,CAACpD,KAAK,GAAGjB,SAAS;EAC5C;EACA0L,aAAaA,CAAC/H,OAAO,EAAE;IACnB,MAAM;MAAElD;IAAS,CAAC,GAAG,IAAI;IACzB,QAAQA,QAAQ;MACZ,KAAK,IAAI;QACL,OAAO,IAAI,CAACkL,qBAAqB,CAAChI,OAAO,CAAC;MAC9C;QACI,OAAO,IAAI,CAACiI,kBAAkB,CAACjI,OAAO,CAAC;IAC/C;EACJ;EACAgI,qBAAqBA,CAAChI,OAAO,EAAE;IAC3B,OAAOA,OAAO,CAACS,GAAG,CAAEC,MAAM,IAAMtI,CAAC,CAAC,UAAU,EAAE;MAAEiM,KAAK,EAAE/B,MAAM,CAACC,MAAM,CAAC;QAC7D;QACA,uBAAuB,EAAE7B,MAAM,CAACmB;MACpC,CAAC,EAAE9H,WAAW,CAAC2G,MAAM,CAACc,QAAQ,CAAC;IAAE,CAAC,EAAEpJ,CAAC,CAAC,cAAc,EAAE;MAAEkF,KAAK,EAAEoD,MAAM,CAACpD,KAAK;MAAEf,QAAQ,EAAEmE,MAAM,CAACnE,QAAQ;MAAEsF,OAAO,EAAEnB,MAAM,CAACmB,OAAO;MAAElF,OAAO,EAAE,OAAO;MAAEE,cAAc,EAAE,KAAK;MAAEqL,WAAW,EAAG3M,EAAE,IAAK;QAC3L,IAAI,CAACuM,UAAU,CAACvM,EAAE,CAAC;QACnB,IAAI,CAACmM,iBAAiB,CAACnM,EAAE,CAAC;QAC1B;QACA7C,WAAW,CAAC,IAAI,CAAC;MACrB;IAAE,CAAC,EAAEgI,MAAM,CAACY,IAAI,CAAC,CAAE,CAAC;EAC5B;EACA2G,kBAAkBA,CAACjI,OAAO,EAAE;IACxB,MAAM6B,OAAO,GAAG7B,OAAO,CAACgB,MAAM,CAAE/B,CAAC,IAAKA,CAAC,CAAC4C,OAAO,CAAC,CAACpB,GAAG,CAAExB,CAAC,IAAKA,CAAC,CAAC3B,KAAK,CAAC,CAAC,CAAC,CAAC;IACvE,OAAQlF,CAAC,CAAC,iBAAiB,EAAE;MAAEkF,KAAK,EAAEuE,OAAO;MAAEqG,WAAW,EAAG3M,EAAE,IAAK,IAAI,CAACmM,iBAAiB,CAACnM,EAAE;IAAE,CAAC,EAAEyE,OAAO,CAACS,GAAG,CAAEC,MAAM,IAAMtI,CAAC,CAAC,UAAU,EAAE;MAAEiM,KAAK,EAAE/B,MAAM,CAACC,MAAM,CAAC;QACxJ;QACA,oBAAoB,EAAE7B,MAAM,CAACpD,KAAK,KAAKuE;MAC3C,CAAC,EAAE9H,WAAW,CAAC2G,MAAM,CAACc,QAAQ,CAAC;IAAE,CAAC,EAAEpJ,CAAC,CAAC,WAAW,EAAE;MAAEkF,KAAK,EAAEoD,MAAM,CAACpD,KAAK;MAAEf,QAAQ,EAAEmE,MAAM,CAACnE,QAAQ;MAAEjB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACuM,oBAAoB,CAAC,CAAC;MAAEM,OAAO,EAAG5M,EAAE,IAAK;QAC9J,IAAIA,EAAE,CAAC6K,GAAG,KAAK,GAAG,EAAE;UAChB;AACpB;AACA;AACA;AACA;UACoB,IAAI,CAACyB,oBAAoB,CAAC,CAAC;QAC/B;MACJ;IAAE,CAAC,EAAEnH,MAAM,CAACY,IAAI,CAAC,CAAE,CAAC,CAAC;EAC7B;EACAsE,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE7C,MAAM;MAAEE,OAAO;MAAEjD,OAAO;MAAEgD;IAAU,CAAC,GAAG,IAAI;IACpD,MAAMoF,qBAAqB,GAAGpF,SAAS,KAAK3G,SAAS,IAAI4G,OAAO,KAAK5G,SAAS;IAC9E,OAAQjE,CAAC,CAACE,IAAI,EAAE;MAAE8N,GAAG,EAAE,0CAA0C;MAAE/B,KAAK,EAAEhK,UAAU,CAAC,IAAI;IAAE,CAAC,EAAEjC,CAAC,CAAC,UAAU,EAAE;MAAEgO,GAAG,EAAE;IAA2C,CAAC,EAAErD,MAAM,KAAK1G,SAAS,IAAIjE,CAAC,CAAC,iBAAiB,EAAE;MAAEgO,GAAG,EAAE;IAA2C,CAAC,EAAErD,MAAM,CAAC,EAAEqF,qBAAqB,IAAKhQ,CAAC,CAAC,UAAU,EAAE;MAAEgO,GAAG,EAAE;IAA2C,CAAC,EAAEhO,CAAC,CAAC,WAAW,EAAE;MAAEgO,GAAG,EAAE,0CAA0C;MAAE/B,KAAK,EAAE;IAAgB,CAAC,EAAErB,SAAS,KAAK3G,SAAS,IAAIjE,CAAC,CAAC,IAAI,EAAE;MAAEgO,GAAG,EAAE;IAA2C,CAAC,EAAEpD,SAAS,CAAC,EAAEC,OAAO,KAAK5G,SAAS,IAAIjE,CAAC,CAAC,GAAG,EAAE;MAAEgO,GAAG,EAAE;IAA2C,CAAC,EAAEnD,OAAO,CAAC,CAAC,CAAE,EAAE,IAAI,CAAC8E,aAAa,CAAC/H,OAAO,CAAC,CAAC,CAAC;EAC5qB;EACA,IAAIrE,EAAEA,CAAA,EAAG;IAAE,OAAOnD,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDgP,aAAa,CAACrD,KAAK,GAAG;EAClB2C,GAAG,EAAEO,yBAAyB;EAC9BN,EAAE,EAAEQ;AACR,CAAC;AAED,SAAS7M,MAAM,IAAI2N,UAAU,EAAEnB,YAAY,IAAIoB,iBAAiB,EAAEd,aAAa,IAAIe,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}