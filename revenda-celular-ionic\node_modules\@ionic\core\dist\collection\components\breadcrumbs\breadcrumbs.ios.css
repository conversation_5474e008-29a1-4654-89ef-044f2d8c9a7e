/**
 * Convert a font size to a dynamic font size.
 * Fonts that participate in Dynamic Type should use
 * dynamic font sizes.
 * @param size - The initial font size including the unit (i.e. px or pt)
 * @param unit (optional) - The unit to convert to. Use this if you want to
 * convert to a unit other than $baselineUnit.
 */
/**
 * Convert a font size to a dynamic font size but impose
 * a maximum font size.
 * @param size - The initial font size including the unit (i.e. px or pt)
 * @param maxScale - The maximum scale of the font (i.e. 2.5 for a maximum 250% scale).
 * @param unit (optional) - The unit to convert the initial font size to. Use this if you want to
 * convert to a unit other than $baselineUnit.
 */
/**
 * Convert a font size to a dynamic font size but impose
 * a minimum font size.
 * @param size - The initial font size including the unit (i.e. px or pt)
 * @param minScale - The minimum scale of the font (i.e. 0.8 for a minimum 80% scale).
 * @param unit (optional) - The unit to convert the initial font size to. Use this if you want to
 * convert to a unit other than $baselineUnit.
 */
/**
 * Convert a font size to a dynamic font size but impose
 * maximum and minimum font sizes.
 * @param size - The initial font size including the unit (i.e. px or pt)
 * @param minScale - The minimum scale of the font (i.e. 0.8 for a minimum 80% scale).
 * @param maxScale - The maximum scale of the font (i.e. 2.5 for a maximum 250% scale).
 * @param unit (optional) - The unit to convert the initial font size to. Use this if you want to
 * convert to a unit other than $baselineUnit.
 */
/**
 * A heuristic that applies CSS to tablet
 * viewports.
 *
 * Usage:
 * @include tablet-viewport() {
 *   :host {
 *     background-color: green;
 *   }
 * }
 */
/**
 * A heuristic that applies CSS to mobile
 * viewports (i.e. phones, not tablets).
 *
 * Usage:
 * @include mobile-viewport() {
 *   :host {
 *     background-color: blue;
 *   }
 * }
 */
:host {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

:host(.in-toolbar-color),
:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon {
  color: var(--ion-color-contrast);
}

:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator {
  background: rgba(var(--ion-color-contrast-rgb), 0.11);
}

:host(.in-toolbar) {
  -webkit-padding-start: 20px;
  padding-inline-start: 20px;
  -webkit-padding-end: 20px;
  padding-inline-end: 20px;
  padding-top: 0;
  padding-bottom: 0;
  justify-content: center;
}