{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { j as clamp } from './helpers-da915de8.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { createGesture } from './index-39782642.js';\nimport './gesture-controller-314a54f6.js';\nconst createSwipeBackGesture = (el, canStartHand<PERSON>, onStartHandler, onMoveHandler, onEndHandler) => {\n  const win = el.ownerDocument.defaultView;\n  let rtl = isRTL(el);\n  /**\n   * Determine if a gesture is near the edge\n   * of the screen. If true, then the swipe\n   * to go back gesture should proceed.\n   */\n  const isAtEdge = detail => {\n    const threshold = 50;\n    const {\n      startX\n    } = detail;\n    if (rtl) {\n      return startX >= win.innerWidth - threshold;\n    }\n    return startX <= threshold;\n  };\n  const getDeltaX = detail => {\n    return rtl ? -detail.deltaX : detail.deltaX;\n  };\n  const getVelocityX = detail => {\n    return rtl ? -detail.velocityX : detail.velocityX;\n  };\n  const canStart = detail => {\n    /**\n     * The user's locale can change mid-session,\n     * so we need to check text direction at\n     * the beginning of every gesture.\n     */\n    rtl = isRTL(el);\n    return isAtEdge(detail) && canStartHandler();\n  };\n  const onMove = detail => {\n    // set the transition animation's progress\n    const delta = getDeltaX(detail);\n    const stepValue = delta / win.innerWidth;\n    onMoveHandler(stepValue);\n  };\n  const onEnd = detail => {\n    // the swipe back gesture has ended\n    const delta = getDeltaX(detail);\n    const width = win.innerWidth;\n    const stepValue = delta / width;\n    const velocity = getVelocityX(detail);\n    const z = width / 2.0;\n    const shouldComplete = velocity >= 0 && (velocity > 0.2 || delta > z);\n    const missing = shouldComplete ? 1 - stepValue : stepValue;\n    const missingDistance = missing * width;\n    let realDur = 0;\n    if (missingDistance > 5) {\n      const dur = missingDistance / Math.abs(velocity);\n      realDur = Math.min(dur, 540);\n    }\n    onEndHandler(shouldComplete, stepValue <= 0 ? 0.01 : clamp(0, stepValue, 0.9999), realDur);\n  };\n  return createGesture({\n    el,\n    gestureName: 'goback-swipe',\n    /**\n     * Swipe to go back should have priority over other horizontal swipe\n     * gestures. These gestures have a priority of 100 which is why 101 was chosen here.\n     */\n    gesturePriority: 101,\n    threshold: 10,\n    canStart,\n    onStart: onStartHandler,\n    onMove,\n    onEnd\n  });\n};\nexport { createSwipeBackGesture };", "map": {"version": 3, "names": ["j", "clamp", "i", "isRTL", "createGesture", "createSwipeBackGesture", "el", "canStartHandler", "onStartHandler", "onMoveHandler", "onEndHandler", "win", "ownerDocument", "defaultView", "rtl", "isAtEdge", "detail", "threshold", "startX", "innerWidth", "getDeltaX", "deltaX", "getVelocityX", "velocityX", "canStart", "onMove", "delta", "<PERSON><PERSON><PERSON><PERSON>", "onEnd", "width", "velocity", "z", "shouldComplete", "missing", "missingDistance", "realDur", "dur", "Math", "abs", "min", "<PERSON><PERSON><PERSON>", "gesturePriority", "onStart"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/swipe-back-e5394307.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { j as clamp } from './helpers-da915de8.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { createGesture } from './index-39782642.js';\nimport './gesture-controller-314a54f6.js';\n\nconst createSwipeBackGesture = (el, canStartHandler, onStartHandler, onMoveHandler, onEndHandler) => {\n    const win = el.ownerDocument.defaultView;\n    let rtl = isRTL(el);\n    /**\n     * Determine if a gesture is near the edge\n     * of the screen. If true, then the swipe\n     * to go back gesture should proceed.\n     */\n    const isAtEdge = (detail) => {\n        const threshold = 50;\n        const { startX } = detail;\n        if (rtl) {\n            return startX >= win.innerWidth - threshold;\n        }\n        return startX <= threshold;\n    };\n    const getDeltaX = (detail) => {\n        return rtl ? -detail.deltaX : detail.deltaX;\n    };\n    const getVelocityX = (detail) => {\n        return rtl ? -detail.velocityX : detail.velocityX;\n    };\n    const canStart = (detail) => {\n        /**\n         * The user's locale can change mid-session,\n         * so we need to check text direction at\n         * the beginning of every gesture.\n         */\n        rtl = isRTL(el);\n        return isAtEdge(detail) && canStartHandler();\n    };\n    const onMove = (detail) => {\n        // set the transition animation's progress\n        const delta = getDeltaX(detail);\n        const stepValue = delta / win.innerWidth;\n        onMoveHandler(stepValue);\n    };\n    const onEnd = (detail) => {\n        // the swipe back gesture has ended\n        const delta = getDeltaX(detail);\n        const width = win.innerWidth;\n        const stepValue = delta / width;\n        const velocity = getVelocityX(detail);\n        const z = width / 2.0;\n        const shouldComplete = velocity >= 0 && (velocity > 0.2 || delta > z);\n        const missing = shouldComplete ? 1 - stepValue : stepValue;\n        const missingDistance = missing * width;\n        let realDur = 0;\n        if (missingDistance > 5) {\n            const dur = missingDistance / Math.abs(velocity);\n            realDur = Math.min(dur, 540);\n        }\n        onEndHandler(shouldComplete, stepValue <= 0 ? 0.01 : clamp(0, stepValue, 0.9999), realDur);\n    };\n    return createGesture({\n        el,\n        gestureName: 'goback-swipe',\n        /**\n         * Swipe to go back should have priority over other horizontal swipe\n         * gestures. These gestures have a priority of 100 which is why 101 was chosen here.\n         */\n        gesturePriority: 101,\n        threshold: 10,\n        canStart,\n        onStart: onStartHandler,\n        onMove,\n        onEnd,\n    });\n};\n\nexport { createSwipeBackGesture };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,KAAK,QAAQ,uBAAuB;AAClD,SAASC,CAAC,IAAIC,KAAK,QAAQ,mBAAmB;AAC9C,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAO,kCAAkC;AAEzC,MAAMC,sBAAsB,GAAGA,CAACC,EAAE,EAAEC,eAAe,EAAEC,cAAc,EAAEC,aAAa,EAAEC,YAAY,KAAK;EACjG,MAAMC,GAAG,GAAGL,EAAE,CAACM,aAAa,CAACC,WAAW;EACxC,IAAIC,GAAG,GAAGX,KAAK,CAACG,EAAE,CAAC;EACnB;AACJ;AACA;AACA;AACA;EACI,MAAMS,QAAQ,GAAIC,MAAM,IAAK;IACzB,MAAMC,SAAS,GAAG,EAAE;IACpB,MAAM;MAAEC;IAAO,CAAC,GAAGF,MAAM;IACzB,IAAIF,GAAG,EAAE;MACL,OAAOI,MAAM,IAAIP,GAAG,CAACQ,UAAU,GAAGF,SAAS;IAC/C;IACA,OAAOC,MAAM,IAAID,SAAS;EAC9B,CAAC;EACD,MAAMG,SAAS,GAAIJ,MAAM,IAAK;IAC1B,OAAOF,GAAG,GAAG,CAACE,MAAM,CAACK,MAAM,GAAGL,MAAM,CAACK,MAAM;EAC/C,CAAC;EACD,MAAMC,YAAY,GAAIN,MAAM,IAAK;IAC7B,OAAOF,GAAG,GAAG,CAACE,MAAM,CAACO,SAAS,GAAGP,MAAM,CAACO,SAAS;EACrD,CAAC;EACD,MAAMC,QAAQ,GAAIR,MAAM,IAAK;IACzB;AACR;AACA;AACA;AACA;IACQF,GAAG,GAAGX,KAAK,CAACG,EAAE,CAAC;IACf,OAAOS,QAAQ,CAACC,MAAM,CAAC,IAAIT,eAAe,CAAC,CAAC;EAChD,CAAC;EACD,MAAMkB,MAAM,GAAIT,MAAM,IAAK;IACvB;IACA,MAAMU,KAAK,GAAGN,SAAS,CAACJ,MAAM,CAAC;IAC/B,MAAMW,SAAS,GAAGD,KAAK,GAAGf,GAAG,CAACQ,UAAU;IACxCV,aAAa,CAACkB,SAAS,CAAC;EAC5B,CAAC;EACD,MAAMC,KAAK,GAAIZ,MAAM,IAAK;IACtB;IACA,MAAMU,KAAK,GAAGN,SAAS,CAACJ,MAAM,CAAC;IAC/B,MAAMa,KAAK,GAAGlB,GAAG,CAACQ,UAAU;IAC5B,MAAMQ,SAAS,GAAGD,KAAK,GAAGG,KAAK;IAC/B,MAAMC,QAAQ,GAAGR,YAAY,CAACN,MAAM,CAAC;IACrC,MAAMe,CAAC,GAAGF,KAAK,GAAG,GAAG;IACrB,MAAMG,cAAc,GAAGF,QAAQ,IAAI,CAAC,KAAKA,QAAQ,GAAG,GAAG,IAAIJ,KAAK,GAAGK,CAAC,CAAC;IACrE,MAAME,OAAO,GAAGD,cAAc,GAAG,CAAC,GAAGL,SAAS,GAAGA,SAAS;IAC1D,MAAMO,eAAe,GAAGD,OAAO,GAAGJ,KAAK;IACvC,IAAIM,OAAO,GAAG,CAAC;IACf,IAAID,eAAe,GAAG,CAAC,EAAE;MACrB,MAAME,GAAG,GAAGF,eAAe,GAAGG,IAAI,CAACC,GAAG,CAACR,QAAQ,CAAC;MAChDK,OAAO,GAAGE,IAAI,CAACE,GAAG,CAACH,GAAG,EAAE,GAAG,CAAC;IAChC;IACA1B,YAAY,CAACsB,cAAc,EAAEL,SAAS,IAAI,CAAC,GAAG,IAAI,GAAG1B,KAAK,CAAC,CAAC,EAAE0B,SAAS,EAAE,MAAM,CAAC,EAAEQ,OAAO,CAAC;EAC9F,CAAC;EACD,OAAO/B,aAAa,CAAC;IACjBE,EAAE;IACFkC,WAAW,EAAE,cAAc;IAC3B;AACR;AACA;AACA;IACQC,eAAe,EAAE,GAAG;IACpBxB,SAAS,EAAE,EAAE;IACbO,QAAQ;IACRkB,OAAO,EAAElC,cAAc;IACvBiB,MAAM;IACNG;EACJ,CAAC,CAAC;AACN,CAAC;AAED,SAASvB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}