<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Clientes</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Clientes</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-list>
    @for(customer of customersList; track customer.id) {
    <ion-item>
      <ion-avatar slot="start">
        <div class="customer-avatar">
          <ion-icon name="person-outline"></ion-icon>
        </div>
      </ion-avatar>
      <ion-label>
        <h2>{{ customer.name }}</h2>
        <p><strong>Email:</strong> {{ customer.email }}</p>
        <p><strong>Telefone:</strong> {{ customer.phone }}</p>
        <p><strong>Nascimento:</strong> {{ customer.birthDate | date: 'dd/MM/yyyy' }}</p>
        <p><strong>Endereço:</strong> {{ customer.address }}</p>
        <div class="badges">
          <ion-badge [color]="getCustomerTypeColor(customer.customerType)">{{ getCustomerTypeLabel(customer.customerType) }}</ion-badge>
          @if(!customer.active) {
            <ion-badge color="danger">Inativo</ion-badge>
          }
        </div>
      </ion-label>
      <ion-button slot="end" size="small" [routerLink]="['edit', customer.id]">
        <ion-icon name="create" slot="icon-only"></ion-icon>
      </ion-button>
      <ion-button slot="end" size="small" color="danger" (click)="remove(customer)">
        <ion-icon name="trash" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-item>
    }
    @empty {
    <ion-item>Lista de clientes vazia, cadastre um novo cliente!</ion-item>
    }
  </ion-list>
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button [routerLink]="['new']">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
