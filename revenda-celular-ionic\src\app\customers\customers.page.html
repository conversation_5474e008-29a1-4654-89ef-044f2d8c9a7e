<ion-header [translucent]="true">
  <ion-toolbar color="secondary">
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Clientes</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Clientes</ion-title>
    </ion-toolbar>
  </ion-header>

  <ion-list>
    @for(customer of customersList; track customer.id) {
    <ion-item>
      <ion-text class="customer-info">
        <h2>{{ customer.name }}</h2>
        <h3><strong>Email:</strong> {{ customer.email }}</h3>
        <h4><strong>Telefone:</strong> {{ customer.phone }}</h4>
        <h4><strong>Data de Nascimento:</strong> {{ customer.birthDate | date: 'dd/MM/yyyy' }}</h4>
        <h4><strong>Endereço:</strong> {{ customer.address }}</h4>
        <h4>
          <ion-badge [color]="getCustomerTypeColor(customer.customerType)">{{ getCustomerTypeLabel(customer.customerType) }}</ion-badge>
          @if(!customer.active) {
            <ion-badge color="danger">Inativo</ion-badge>
          }
        </h4>
        <ion-button size="small" [routerLink]="['edit', customer.id]">
          <ion-icon name="create" slot="start"></ion-icon>
          Editar
        </ion-button>
        <ion-button size="small" (click)="remove(customer)">
          <ion-icon name="trash" slot="end"></ion-icon>
          Excluir
        </ion-button>
      </ion-text>
    </ion-item>
    }
    @empty {
    <ion-item>Lista de clientes vazia, cadastre um novo cliente!</ion-item>
    }
  </ion-list>
  <ion-fab vertical="bottom" horizontal="end" slot="fixed">
    <ion-fab-button [routerLink]="['new']">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>
