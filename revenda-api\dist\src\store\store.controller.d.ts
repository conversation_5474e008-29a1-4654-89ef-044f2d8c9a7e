import { StoreService } from './store.service';
import { CreateStoreDto } from './dto/create-store.dto';
import { UpdateStoreDto } from './dto/update-store.dto';
export declare class StoreController {
    private readonly storeService;
    constructor(storeService: StoreService);
    create(createStoreDto: CreateStoreDto): Promise<import("./store.entity").Store>;
    findAll(): Promise<import("./store.entity").Store[]>;
    findOne(id: string): Promise<import("./store.entity").Store | null>;
    findActive(): Promise<import("./store.entity").Store[]>;
    update(id: string, updateStoreDto: UpdateStoreDto): Promise<import("typeorm").UpdateResult>;
    remove(id: string): Promise<import("typeorm").DeleteResult>;
}
