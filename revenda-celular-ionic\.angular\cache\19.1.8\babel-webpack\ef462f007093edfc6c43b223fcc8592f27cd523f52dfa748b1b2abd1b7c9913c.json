{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Logs a warning to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n */\nconst printIonWarning = (message, ...params) => {\n  return console.warn(`[Ionic Warning]: ${message}`, ...params);\n};\n/*\n * Logs an error to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n * @param params - Additional arguments to supply to the console.error.\n */\nconst printIonError = (message, ...params) => {\n  return console.error(`[Ionic Error]: ${message}`, ...params);\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within a specific selector.\n *\n * @param el The web component element this is requiring the element.\n * @param targetSelectors The selector or selectors that were not found.\n */\nconst printRequiredElementError = (el, ...targetSelectors) => {\n  return console.error(`<${el.tagName.toLowerCase()}> must be used inside ${targetSelectors.join(' or ')}.`);\n};\nexport { printIonError as a, printRequiredElementError as b, printIonWarning as p };", "map": {"version": 3, "names": ["printIonWarning", "message", "params", "console", "warn", "printIonError", "error", "printRequiredElementError", "el", "targetSelectors", "tagName", "toLowerCase", "join", "a", "b", "p"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/index-9b0d46f4.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\n/**\n * Logs a warning to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n */\nconst printIonWarning = (message, ...params) => {\n    return console.warn(`[Ionic Warning]: ${message}`, ...params);\n};\n/*\n * Logs an error to the console with an Ionic prefix\n * to indicate the library that is warning the developer.\n *\n * @param message - The string message to be logged to the console.\n * @param params - Additional arguments to supply to the console.error.\n */\nconst printIonError = (message, ...params) => {\n    return console.error(`[Ionic Error]: ${message}`, ...params);\n};\n/**\n * Prints an error informing developers that an implementation requires an element to be used\n * within a specific selector.\n *\n * @param el The web component element this is requiring the element.\n * @param targetSelectors The selector or selectors that were not found.\n */\nconst printRequiredElementError = (el, ...targetSelectors) => {\n    return console.error(`<${el.tagName.toLowerCase()}> must be used inside ${targetSelectors.join(' or ')}.`);\n};\n\nexport { printIonError as a, printRequiredElementError as b, printIonWarning as p };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,eAAe,GAAGA,CAACC,OAAO,EAAE,GAAGC,MAAM,KAAK;EAC5C,OAAOC,OAAO,CAACC,IAAI,CAAC,oBAAoBH,OAAO,EAAE,EAAE,GAAGC,MAAM,CAAC;AACjE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,aAAa,GAAGA,CAACJ,OAAO,EAAE,GAAGC,MAAM,KAAK;EAC1C,OAAOC,OAAO,CAACG,KAAK,CAAC,kBAAkBL,OAAO,EAAE,EAAE,GAAGC,MAAM,CAAC;AAChE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,yBAAyB,GAAGA,CAACC,EAAE,EAAE,GAAGC,eAAe,KAAK;EAC1D,OAAON,OAAO,CAACG,KAAK,CAAC,IAAIE,EAAE,CAACE,OAAO,CAACC,WAAW,CAAC,CAAC,yBAAyBF,eAAe,CAACG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;AAC9G,CAAC;AAED,SAASP,aAAa,IAAIQ,CAAC,EAAEN,yBAAyB,IAAIO,CAAC,EAAEd,eAAe,IAAIe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}