import { Repository } from 'typeorm';
import { Accessory } from './accessory.entity';
import { CreateAccessoryDto } from './dto/create-accessory.dto';
import { UpdateAccessoryDto } from './dto/update-accessory.dto';
import { Phone } from '../phone/phone.entity';
export declare class AccessoryService {
    private accessoryRepository;
    private phoneRepository;
    constructor(accessoryRepository: Repository<Accessory>, phoneRepository: Repository<Phone>);
    create(createAccessoryDto: CreateAccessoryDto): Promise<Accessory>;
    findAllInStock(): Promise<Accessory[]>;
    findAll(): Promise<Accessory[]>;
    findOne(id: number): Promise<Accessory | null>;
    findByCategory(category: string): Promise<Accessory[]>;
    findInStock(): Promise<Accessory[]>;
    update(id: number, updateAccessoryDto: UpdateAccessoryDto): Promise<{
        message: string;
    }>;
    updateStock(id: number, quantity: number): Promise<import("typeorm").UpdateResult>;
    remove(id: number): Promise<import("typeorm").DeleteResult>;
}
