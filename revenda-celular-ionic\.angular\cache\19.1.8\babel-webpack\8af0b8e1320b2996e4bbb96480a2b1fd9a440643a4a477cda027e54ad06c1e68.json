{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host, i as getElement } from './index-28849c61.js';\nimport { a as addEventListener, c as componentOnReady } from './helpers-da915de8.js';\nimport { a as printIonError } from './index-9b0d46f4.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-c81d82ab.js';\nimport { s as parseDate, x as getToday, L as getHourCycle, N as getLocalizedDateTime, M as getLocalizedTime } from './data-ae11fd43.js';\nconst datetimeButtonIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:13px;padding-inline-start:13px;-webkit-padding-end:13px;padding-inline-end:13px;padding-top:7px;padding-bottom:7px}:host button.ion-activated{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}\";\nconst IonDatetimeButtonIosStyle0 = datetimeButtonIosCss;\nconst datetimeButtonMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}\";\nconst IonDatetimeButtonMdStyle0 = datetimeButtonMdCss;\nconst DatetimeButton = class {\n  constructor(hostRef) {\n    var _this = this;\n    registerInstance(this, hostRef);\n    this.datetimeEl = null;\n    this.overlayEl = null;\n    /**\n     * Accepts one or more string values and converts\n     * them to DatetimeParts. This is done so datetime-button\n     * can work with an array internally and not need\n     * to keep checking if the datetime value is `string` or `string[]`.\n     */\n    this.getParsedDateValues = value => {\n      if (value === undefined || value === null) {\n        return [];\n      }\n      if (Array.isArray(value)) {\n        return value;\n      }\n      return [value];\n    };\n    /**\n     * Check the value property on the linked\n     * ion-datetime and then format it according\n     * to the locale specified on ion-datetime.\n     */\n    this.setDateTimeText = () => {\n      var _a, _b, _c, _d, _e;\n      const {\n        datetimeEl,\n        datetimePresentation\n      } = this;\n      if (!datetimeEl) {\n        return;\n      }\n      const {\n        value,\n        locale,\n        formatOptions,\n        hourCycle,\n        preferWheel,\n        multiple,\n        titleSelectedDatesFormatter\n      } = datetimeEl;\n      const parsedValues = this.getParsedDateValues(value);\n      /**\n       * Both ion-datetime and ion-datetime-button default\n       * to today's date and time if no value is set.\n       */\n      const parsedDatetimes = parseDate(parsedValues.length > 0 ? parsedValues : [getToday()]);\n      if (!parsedDatetimes) {\n        return;\n      }\n      /**\n       * If developers incorrectly use multiple=\"true\"\n       * with non \"date\" datetimes, then just select\n       * the first value so the interface does\n       * not appear broken. Datetime will provide a\n       * warning in the console.\n       */\n      const firstParsedDatetime = parsedDatetimes[0];\n      const computedHourCycle = getHourCycle(locale, hourCycle);\n      this.dateText = this.timeText = undefined;\n      switch (datetimePresentation) {\n        case 'date-time':\n        case 'time-date':\n          const dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _a !== void 0 ? _a : {\n            month: 'short',\n            day: 'numeric',\n            year: 'numeric'\n          });\n          const timeText = getLocalizedTime(locale, firstParsedDatetime, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time);\n          if (preferWheel) {\n            this.dateText = `${dateText} ${timeText}`;\n          } else {\n            this.dateText = dateText;\n            this.timeText = timeText;\n          }\n          break;\n        case 'date':\n          if (multiple && parsedValues.length !== 1) {\n            let headerText = `${parsedValues.length} days`; // default/fallback for multiple selection\n            if (titleSelectedDatesFormatter !== undefined) {\n              try {\n                headerText = titleSelectedDatesFormatter(parsedValues);\n              } catch (e) {\n                printIonError('Exception in provided `titleSelectedDatesFormatter`: ', e);\n              }\n            }\n            this.dateText = headerText;\n          } else {\n            this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_b = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _b !== void 0 ? _b : {\n              month: 'short',\n              day: 'numeric',\n              year: 'numeric'\n            });\n          }\n          break;\n        case 'time':\n          this.timeText = getLocalizedTime(locale, firstParsedDatetime, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time);\n          break;\n        case 'month-year':\n          this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_c = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _c !== void 0 ? _c : {\n            month: 'long',\n            year: 'numeric'\n          });\n          break;\n        case 'month':\n          this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_d = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) !== null && _d !== void 0 ? _d : {\n            month: 'long'\n          });\n          break;\n        case 'year':\n          this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_e = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) !== null && _e !== void 0 ? _e : {\n            year: 'numeric'\n          });\n          break;\n      }\n    };\n    /**\n     * Waits for the ion-datetime to re-render.\n     * This is needed in order to correctly position\n     * a popover relative to the trigger element.\n     */\n    this.waitForDatetimeChanges = /*#__PURE__*/_asyncToGenerator(function* () {\n      const {\n        datetimeEl\n      } = _this;\n      if (!datetimeEl) {\n        return Promise.resolve();\n      }\n      return new Promise(resolve => {\n        addEventListener(datetimeEl, 'ionRender', resolve, {\n          once: true\n        });\n      });\n    });\n    this.handleDateClick = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(function* (ev) {\n        const {\n          datetimeEl,\n          datetimePresentation\n        } = _this;\n        if (!datetimeEl) {\n          return;\n        }\n        let needsPresentationChange = false;\n        /**\n         * When clicking the date button,\n         * we need to make sure that only a date\n         * picker is displayed. For presentation styles\n         * that display content other than a date picker,\n         * we need to update the presentation style.\n         */\n        switch (datetimePresentation) {\n          case 'date-time':\n          case 'time-date':\n            const needsChange = datetimeEl.presentation !== 'date';\n            /**\n             * The date+time wheel picker\n             * shows date and time together,\n             * so do not adjust the presentation\n             * in that case.\n             */\n            if (!datetimeEl.preferWheel && needsChange) {\n              datetimeEl.presentation = 'date';\n              needsPresentationChange = true;\n            }\n            break;\n        }\n        /**\n         * Track which button was clicked\n         * so that it can have the correct\n         * activated styles applied when\n         * the modal/popover containing\n         * the datetime is opened.\n         */\n        _this.selectedButton = 'date';\n        _this.presentOverlay(ev, needsPresentationChange, _this.dateTargetEl);\n      });\n      return function (_x) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    this.handleTimeClick = ev => {\n      const {\n        datetimeEl,\n        datetimePresentation\n      } = this;\n      if (!datetimeEl) {\n        return;\n      }\n      let needsPresentationChange = false;\n      /**\n       * When clicking the time button,\n       * we need to make sure that only a time\n       * picker is displayed. For presentation styles\n       * that display content other than a time picker,\n       * we need to update the presentation style.\n       */\n      switch (datetimePresentation) {\n        case 'date-time':\n        case 'time-date':\n          const needsChange = datetimeEl.presentation !== 'time';\n          if (needsChange) {\n            datetimeEl.presentation = 'time';\n            needsPresentationChange = true;\n          }\n          break;\n      }\n      /**\n       * Track which button was clicked\n       * so that it can have the correct\n       * activated styles applied when\n       * the modal/popover containing\n       * the datetime is opened.\n       */\n      this.selectedButton = 'time';\n      this.presentOverlay(ev, needsPresentationChange, this.timeTargetEl);\n    };\n    /**\n     * If the datetime is presented in an\n     * overlay, the datetime and overlay\n     * should be appropriately sized.\n     * These classes provide default sizing values\n     * that developers can customize.\n     * The goal is to provide an overlay that is\n     * reasonably sized with a datetime that\n     * fills the entire container.\n     */\n    this.presentOverlay = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(function* (ev, needsPresentationChange, triggerEl) {\n        const {\n          overlayEl\n        } = _this;\n        if (!overlayEl) {\n          return;\n        }\n        if (overlayEl.tagName === 'ION-POPOVER') {\n          /**\n           * When the presentation on datetime changes,\n           * we need to wait for the component to re-render\n           * otherwise the computed width/height of the\n           * popover content will be wrong, causing\n           * the popover to not align with the trigger element.\n           */\n          if (needsPresentationChange) {\n            yield _this.waitForDatetimeChanges();\n          }\n          /**\n           * We pass the trigger button element\n           * so that the popover aligns with the individual\n           * button that was clicked, not the component container.\n           */\n          overlayEl.present(Object.assign(Object.assign({}, ev), {\n            detail: {\n              ionShadowTarget: triggerEl\n            }\n          }));\n        } else {\n          overlayEl.present();\n        }\n      });\n      return function (_x2, _x3, _x4) {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    this.datetimePresentation = 'date-time';\n    this.dateText = undefined;\n    this.timeText = undefined;\n    this.datetimeActive = false;\n    this.selectedButton = undefined;\n    this.color = 'primary';\n    this.disabled = false;\n    this.datetime = undefined;\n  }\n  componentWillLoad() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        datetime\n      } = _this2;\n      if (!datetime) {\n        printIonError('An ID associated with an ion-datetime instance is required for ion-datetime-button to function properly.', _this2.el);\n        return;\n      }\n      const datetimeEl = _this2.datetimeEl = document.getElementById(datetime);\n      if (!datetimeEl) {\n        printIonError(`No ion-datetime instance found for ID '${datetime}'.`, _this2.el);\n        return;\n      }\n      /**\n       * The element reference must be an ion-datetime. Print an error\n       * if a non-datetime element was provided.\n       */\n      if (datetimeEl.tagName !== 'ION-DATETIME') {\n        printIonError(`Expected an ion-datetime instance for ID '${datetime}' but received '${datetimeEl.tagName.toLowerCase()}' instead.`, datetimeEl);\n        return;\n      }\n      /**\n       * Since the datetime can be used in any context (overlays, accordion, etc)\n       * we track when it is visible to determine when it is active.\n       * This informs which button is highlighted as well as the\n       * aria-expanded state.\n       */\n      const io = new IntersectionObserver(entries => {\n        const ev = entries[0];\n        _this2.datetimeActive = ev.isIntersecting;\n      }, {\n        threshold: 0.01\n      });\n      io.observe(datetimeEl);\n      /**\n       * Get a reference to any modal/popover\n       * the datetime is being used in so we can\n       * correctly size it when it is presented.\n       */\n      const overlayEl = _this2.overlayEl = datetimeEl.closest('ion-modal, ion-popover');\n      /**\n       * The .ion-datetime-button-overlay class contains\n       * styles that allow any modal/popover to be\n       * sized according to the dimensions of the datetime.\n       * If developers want a smaller/larger overlay all they need\n       * to do is change the width/height of the datetime.\n       * Additionally, this lets us avoid having to set\n       * explicit widths on each variant of datetime.\n       */\n      if (overlayEl) {\n        overlayEl.classList.add('ion-datetime-button-overlay');\n      }\n      componentOnReady(datetimeEl, () => {\n        const datetimePresentation = _this2.datetimePresentation = datetimeEl.presentation || 'date-time';\n        /**\n         * Set the initial display\n         * in the rendered buttons.\n         *\n         * From there, we need to listen\n         * for ionChange to be emitted\n         * from datetime so we know when\n         * to re-render the displayed\n         * text in the buttons.\n         */\n        _this2.setDateTimeText();\n        addEventListener(datetimeEl, 'ionValueChange', _this2.setDateTimeText);\n        /**\n         * Configure the initial selected button\n         * in the event that the datetime is displayed\n         * without clicking one of the datetime buttons.\n         * For example, a datetime could be expanded\n         * in an accordion. In this case users only\n         * need to click the accordion header to show\n         * the datetime.\n         */\n        switch (datetimePresentation) {\n          case 'date-time':\n          case 'date':\n          case 'month-year':\n          case 'month':\n          case 'year':\n            _this2.selectedButton = 'date';\n            break;\n          case 'time-date':\n          case 'time':\n            _this2.selectedButton = 'time';\n            break;\n        }\n      });\n    })();\n  }\n  render() {\n    const {\n      color,\n      dateText,\n      timeText,\n      selectedButton,\n      datetimeActive,\n      disabled\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '26e606af6f067a5774db37ed41387ffebb941777',\n      class: createColorClasses(color, {\n        [mode]: true,\n        [`${selectedButton}-active`]: datetimeActive,\n        ['datetime-button-disabled']: disabled\n      })\n    }, dateText && h(\"button\", {\n      key: '6b7aa66a15b4a6d89d411e40586de28a2ac9f343',\n      class: \"ion-activatable\",\n      id: \"date-button\",\n      \"aria-expanded\": datetimeActive ? 'true' : 'false',\n      onClick: this.handleDateClick,\n      disabled: disabled,\n      part: \"native\",\n      ref: el => this.dateTargetEl = el\n    }, h(\"slot\", {\n      key: 'd42f34fd167be34386319e7ea788c2ab03c90b87',\n      name: \"date-target\"\n    }, dateText), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '47dd34f3c2799064cac7a5fe25440ecc043950f0'\n    })), timeText && h(\"button\", {\n      key: 'd77424a20fae320654774c7bfc8a8e2369d3afe3',\n      class: \"ion-activatable\",\n      id: \"time-button\",\n      \"aria-expanded\": datetimeActive ? 'true' : 'false',\n      onClick: this.handleTimeClick,\n      disabled: disabled,\n      part: \"native\",\n      ref: el => this.timeTargetEl = el\n    }, h(\"slot\", {\n      key: 'ac088a78141bb53f2efd48dd7745f8954c92378b',\n      name: \"time-target\"\n    }, timeText), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: 'b3a58ddfd28b9396e2518ffd62a045ec13d8b9d0'\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nDatetimeButton.style = {\n  ios: IonDatetimeButtonIosStyle0,\n  md: IonDatetimeButtonMdStyle0\n};\nexport { DatetimeButton as ion_datetime_button };", "map": {"version": 3, "names": ["r", "registerInstance", "h", "f", "Host", "i", "getElement", "a", "addEventListener", "c", "componentOnReady", "printIonError", "createColorClasses", "b", "getIonMode", "s", "parseDate", "x", "get<PERSON><PERSON>y", "L", "getHourCycle", "N", "getLocalizedDateTime", "M", "getLocalizedTime", "datetimeButtonIosCss", "IonDatetimeButtonIosStyle0", "datetimeButtonMdCss", "IonDatetimeButtonMdStyle0", "DatetimeButton", "constructor", "hostRef", "_this", "datetimeEl", "overlayEl", "getParsedDateValues", "value", "undefined", "Array", "isArray", "setDateTimeText", "_a", "_b", "_c", "_d", "_e", "datetimePresentation", "locale", "formatOptions", "hourCycle", "preferWheel", "multiple", "titleSelectedDatesFormatter", "parsed<PERSON><PERSON>ues", "parsedDatetimes", "length", "firstParsedDatetime", "computedHourCycle", "dateText", "timeText", "date", "month", "day", "year", "time", "headerText", "e", "waitForDatetimeChanges", "_asyncToGenerator", "Promise", "resolve", "once", "handleDateClick", "_ref2", "ev", "needsPresentationChange", "needsChange", "presentation", "<PERSON><PERSON><PERSON><PERSON>", "presentOverlay", "dateTargetEl", "_x", "apply", "arguments", "handleTimeClick", "timeTargetEl", "_ref3", "triggerEl", "tagName", "present", "Object", "assign", "detail", "ionShadowTarget", "_x2", "_x3", "_x4", "datetimeActive", "color", "disabled", "datetime", "componentWillLoad", "_this2", "el", "document", "getElementById", "toLowerCase", "io", "IntersectionObserver", "entries", "isIntersecting", "threshold", "observe", "closest", "classList", "add", "render", "mode", "key", "class", "id", "onClick", "part", "ref", "name", "style", "ios", "md", "ion_datetime_button"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/ion-datetime-button.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host, i as getElement } from './index-28849c61.js';\nimport { a as addEventListener, c as componentOnReady } from './helpers-da915de8.js';\nimport { a as printIonError } from './index-9b0d46f4.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-c81d82ab.js';\nimport { s as parseDate, x as getToday, L as getHourCycle, N as getLocalizedDateTime, M as getLocalizedTime } from './data-ae11fd43.js';\n\nconst datetimeButtonIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:13px;padding-inline-start:13px;-webkit-padding-end:13px;padding-inline-end:13px;padding-top:7px;padding-bottom:7px}:host button.ion-activated{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}\";\nconst IonDatetimeButtonIosStyle0 = datetimeButtonIosCss;\n\nconst datetimeButtonMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}:host button{border-radius:8px;-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:2px;margin-inline-end:2px;margin-top:0px;margin-bottom:0px;position:relative;-webkit-transition:150ms color ease-in-out;transition:150ms color ease-in-out;border:none;background:var(--ion-color-step-300, var(--ion-background-color-step-300, #edeef0));color:var(--ion-text-color, #000);font-family:inherit;font-size:1rem;cursor:pointer;overflow:hidden;-webkit-appearance:none;-moz-appearance:none;appearance:none}:host(.time-active) #time-button,:host(.date-active) #date-button{color:var(--ion-color-base)}:host(.datetime-button-disabled){pointer-events:none}:host(.datetime-button-disabled) button{opacity:0.4}:host button{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}\";\nconst IonDatetimeButtonMdStyle0 = datetimeButtonMdCss;\n\nconst DatetimeButton = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.datetimeEl = null;\n        this.overlayEl = null;\n        /**\n         * Accepts one or more string values and converts\n         * them to DatetimeParts. This is done so datetime-button\n         * can work with an array internally and not need\n         * to keep checking if the datetime value is `string` or `string[]`.\n         */\n        this.getParsedDateValues = (value) => {\n            if (value === undefined || value === null) {\n                return [];\n            }\n            if (Array.isArray(value)) {\n                return value;\n            }\n            return [value];\n        };\n        /**\n         * Check the value property on the linked\n         * ion-datetime and then format it according\n         * to the locale specified on ion-datetime.\n         */\n        this.setDateTimeText = () => {\n            var _a, _b, _c, _d, _e;\n            const { datetimeEl, datetimePresentation } = this;\n            if (!datetimeEl) {\n                return;\n            }\n            const { value, locale, formatOptions, hourCycle, preferWheel, multiple, titleSelectedDatesFormatter } = datetimeEl;\n            const parsedValues = this.getParsedDateValues(value);\n            /**\n             * Both ion-datetime and ion-datetime-button default\n             * to today's date and time if no value is set.\n             */\n            const parsedDatetimes = parseDate(parsedValues.length > 0 ? parsedValues : [getToday()]);\n            if (!parsedDatetimes) {\n                return;\n            }\n            /**\n             * If developers incorrectly use multiple=\"true\"\n             * with non \"date\" datetimes, then just select\n             * the first value so the interface does\n             * not appear broken. Datetime will provide a\n             * warning in the console.\n             */\n            const firstParsedDatetime = parsedDatetimes[0];\n            const computedHourCycle = getHourCycle(locale, hourCycle);\n            this.dateText = this.timeText = undefined;\n            switch (datetimePresentation) {\n                case 'date-time':\n                case 'time-date':\n                    const dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_a = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _a !== void 0 ? _a : { month: 'short', day: 'numeric', year: 'numeric' });\n                    const timeText = getLocalizedTime(locale, firstParsedDatetime, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time);\n                    if (preferWheel) {\n                        this.dateText = `${dateText} ${timeText}`;\n                    }\n                    else {\n                        this.dateText = dateText;\n                        this.timeText = timeText;\n                    }\n                    break;\n                case 'date':\n                    if (multiple && parsedValues.length !== 1) {\n                        let headerText = `${parsedValues.length} days`; // default/fallback for multiple selection\n                        if (titleSelectedDatesFormatter !== undefined) {\n                            try {\n                                headerText = titleSelectedDatesFormatter(parsedValues);\n                            }\n                            catch (e) {\n                                printIonError('Exception in provided `titleSelectedDatesFormatter`: ', e);\n                            }\n                        }\n                        this.dateText = headerText;\n                    }\n                    else {\n                        this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_b = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _b !== void 0 ? _b : { month: 'short', day: 'numeric', year: 'numeric' });\n                    }\n                    break;\n                case 'time':\n                    this.timeText = getLocalizedTime(locale, firstParsedDatetime, computedHourCycle, formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time);\n                    break;\n                case 'month-year':\n                    this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_c = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.date) !== null && _c !== void 0 ? _c : { month: 'long', year: 'numeric' });\n                    break;\n                case 'month':\n                    this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_d = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) !== null && _d !== void 0 ? _d : { month: 'long' });\n                    break;\n                case 'year':\n                    this.dateText = getLocalizedDateTime(locale, firstParsedDatetime, (_e = formatOptions === null || formatOptions === void 0 ? void 0 : formatOptions.time) !== null && _e !== void 0 ? _e : { year: 'numeric' });\n                    break;\n            }\n        };\n        /**\n         * Waits for the ion-datetime to re-render.\n         * This is needed in order to correctly position\n         * a popover relative to the trigger element.\n         */\n        this.waitForDatetimeChanges = async () => {\n            const { datetimeEl } = this;\n            if (!datetimeEl) {\n                return Promise.resolve();\n            }\n            return new Promise((resolve) => {\n                addEventListener(datetimeEl, 'ionRender', resolve, { once: true });\n            });\n        };\n        this.handleDateClick = async (ev) => {\n            const { datetimeEl, datetimePresentation } = this;\n            if (!datetimeEl) {\n                return;\n            }\n            let needsPresentationChange = false;\n            /**\n             * When clicking the date button,\n             * we need to make sure that only a date\n             * picker is displayed. For presentation styles\n             * that display content other than a date picker,\n             * we need to update the presentation style.\n             */\n            switch (datetimePresentation) {\n                case 'date-time':\n                case 'time-date':\n                    const needsChange = datetimeEl.presentation !== 'date';\n                    /**\n                     * The date+time wheel picker\n                     * shows date and time together,\n                     * so do not adjust the presentation\n                     * in that case.\n                     */\n                    if (!datetimeEl.preferWheel && needsChange) {\n                        datetimeEl.presentation = 'date';\n                        needsPresentationChange = true;\n                    }\n                    break;\n            }\n            /**\n             * Track which button was clicked\n             * so that it can have the correct\n             * activated styles applied when\n             * the modal/popover containing\n             * the datetime is opened.\n             */\n            this.selectedButton = 'date';\n            this.presentOverlay(ev, needsPresentationChange, this.dateTargetEl);\n        };\n        this.handleTimeClick = (ev) => {\n            const { datetimeEl, datetimePresentation } = this;\n            if (!datetimeEl) {\n                return;\n            }\n            let needsPresentationChange = false;\n            /**\n             * When clicking the time button,\n             * we need to make sure that only a time\n             * picker is displayed. For presentation styles\n             * that display content other than a time picker,\n             * we need to update the presentation style.\n             */\n            switch (datetimePresentation) {\n                case 'date-time':\n                case 'time-date':\n                    const needsChange = datetimeEl.presentation !== 'time';\n                    if (needsChange) {\n                        datetimeEl.presentation = 'time';\n                        needsPresentationChange = true;\n                    }\n                    break;\n            }\n            /**\n             * Track which button was clicked\n             * so that it can have the correct\n             * activated styles applied when\n             * the modal/popover containing\n             * the datetime is opened.\n             */\n            this.selectedButton = 'time';\n            this.presentOverlay(ev, needsPresentationChange, this.timeTargetEl);\n        };\n        /**\n         * If the datetime is presented in an\n         * overlay, the datetime and overlay\n         * should be appropriately sized.\n         * These classes provide default sizing values\n         * that developers can customize.\n         * The goal is to provide an overlay that is\n         * reasonably sized with a datetime that\n         * fills the entire container.\n         */\n        this.presentOverlay = async (ev, needsPresentationChange, triggerEl) => {\n            const { overlayEl } = this;\n            if (!overlayEl) {\n                return;\n            }\n            if (overlayEl.tagName === 'ION-POPOVER') {\n                /**\n                 * When the presentation on datetime changes,\n                 * we need to wait for the component to re-render\n                 * otherwise the computed width/height of the\n                 * popover content will be wrong, causing\n                 * the popover to not align with the trigger element.\n                 */\n                if (needsPresentationChange) {\n                    await this.waitForDatetimeChanges();\n                }\n                /**\n                 * We pass the trigger button element\n                 * so that the popover aligns with the individual\n                 * button that was clicked, not the component container.\n                 */\n                overlayEl.present(Object.assign(Object.assign({}, ev), { detail: {\n                        ionShadowTarget: triggerEl,\n                    } }));\n            }\n            else {\n                overlayEl.present();\n            }\n        };\n        this.datetimePresentation = 'date-time';\n        this.dateText = undefined;\n        this.timeText = undefined;\n        this.datetimeActive = false;\n        this.selectedButton = undefined;\n        this.color = 'primary';\n        this.disabled = false;\n        this.datetime = undefined;\n    }\n    async componentWillLoad() {\n        const { datetime } = this;\n        if (!datetime) {\n            printIonError('An ID associated with an ion-datetime instance is required for ion-datetime-button to function properly.', this.el);\n            return;\n        }\n        const datetimeEl = (this.datetimeEl = document.getElementById(datetime));\n        if (!datetimeEl) {\n            printIonError(`No ion-datetime instance found for ID '${datetime}'.`, this.el);\n            return;\n        }\n        /**\n         * The element reference must be an ion-datetime. Print an error\n         * if a non-datetime element was provided.\n         */\n        if (datetimeEl.tagName !== 'ION-DATETIME') {\n            printIonError(`Expected an ion-datetime instance for ID '${datetime}' but received '${datetimeEl.tagName.toLowerCase()}' instead.`, datetimeEl);\n            return;\n        }\n        /**\n         * Since the datetime can be used in any context (overlays, accordion, etc)\n         * we track when it is visible to determine when it is active.\n         * This informs which button is highlighted as well as the\n         * aria-expanded state.\n         */\n        const io = new IntersectionObserver((entries) => {\n            const ev = entries[0];\n            this.datetimeActive = ev.isIntersecting;\n        }, {\n            threshold: 0.01,\n        });\n        io.observe(datetimeEl);\n        /**\n         * Get a reference to any modal/popover\n         * the datetime is being used in so we can\n         * correctly size it when it is presented.\n         */\n        const overlayEl = (this.overlayEl = datetimeEl.closest('ion-modal, ion-popover'));\n        /**\n         * The .ion-datetime-button-overlay class contains\n         * styles that allow any modal/popover to be\n         * sized according to the dimensions of the datetime.\n         * If developers want a smaller/larger overlay all they need\n         * to do is change the width/height of the datetime.\n         * Additionally, this lets us avoid having to set\n         * explicit widths on each variant of datetime.\n         */\n        if (overlayEl) {\n            overlayEl.classList.add('ion-datetime-button-overlay');\n        }\n        componentOnReady(datetimeEl, () => {\n            const datetimePresentation = (this.datetimePresentation = datetimeEl.presentation || 'date-time');\n            /**\n             * Set the initial display\n             * in the rendered buttons.\n             *\n             * From there, we need to listen\n             * for ionChange to be emitted\n             * from datetime so we know when\n             * to re-render the displayed\n             * text in the buttons.\n             */\n            this.setDateTimeText();\n            addEventListener(datetimeEl, 'ionValueChange', this.setDateTimeText);\n            /**\n             * Configure the initial selected button\n             * in the event that the datetime is displayed\n             * without clicking one of the datetime buttons.\n             * For example, a datetime could be expanded\n             * in an accordion. In this case users only\n             * need to click the accordion header to show\n             * the datetime.\n             */\n            switch (datetimePresentation) {\n                case 'date-time':\n                case 'date':\n                case 'month-year':\n                case 'month':\n                case 'year':\n                    this.selectedButton = 'date';\n                    break;\n                case 'time-date':\n                case 'time':\n                    this.selectedButton = 'time';\n                    break;\n            }\n        });\n    }\n    render() {\n        const { color, dateText, timeText, selectedButton, datetimeActive, disabled } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: '26e606af6f067a5774db37ed41387ffebb941777', class: createColorClasses(color, {\n                [mode]: true,\n                [`${selectedButton}-active`]: datetimeActive,\n                ['datetime-button-disabled']: disabled,\n            }) }, dateText && (h(\"button\", { key: '6b7aa66a15b4a6d89d411e40586de28a2ac9f343', class: \"ion-activatable\", id: \"date-button\", \"aria-expanded\": datetimeActive ? 'true' : 'false', onClick: this.handleDateClick, disabled: disabled, part: \"native\", ref: (el) => (this.dateTargetEl = el) }, h(\"slot\", { key: 'd42f34fd167be34386319e7ea788c2ab03c90b87', name: \"date-target\" }, dateText), mode === 'md' && h(\"ion-ripple-effect\", { key: '47dd34f3c2799064cac7a5fe25440ecc043950f0' }))), timeText && (h(\"button\", { key: 'd77424a20fae320654774c7bfc8a8e2369d3afe3', class: \"ion-activatable\", id: \"time-button\", \"aria-expanded\": datetimeActive ? 'true' : 'false', onClick: this.handleTimeClick, disabled: disabled, part: \"native\", ref: (el) => (this.timeTargetEl = el) }, h(\"slot\", { key: 'ac088a78141bb53f2efd48dd7745f8954c92378b', name: \"time-target\" }, timeText), mode === 'md' && h(\"ion-ripple-effect\", { key: 'b3a58ddfd28b9396e2518ffd62a045ec13d8b9d0' })))));\n    }\n    get el() { return getElement(this); }\n};\nDatetimeButton.style = {\n    ios: IonDatetimeButtonIosStyle0,\n    md: IonDatetimeButtonMdStyle0\n};\n\nexport { DatetimeButton as ion_datetime_button };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC1F,SAASC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,gBAAgB,QAAQ,uBAAuB;AACpF,SAASH,CAAC,IAAII,aAAa,QAAQ,qBAAqB;AACxD,SAASF,CAAC,IAAIG,kBAAkB,QAAQ,qBAAqB;AAC7D,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC5D,SAASC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,gBAAgB,QAAQ,oBAAoB;AAEvI,MAAMC,oBAAoB,GAAG,gmCAAgmC;AAC7nC,MAAMC,0BAA0B,GAAGD,oBAAoB;AAEvD,MAAME,mBAAmB,GAAG,4/BAA4/B;AACxhC,MAAMC,yBAAyB,GAAGD,mBAAmB;AAErD,MAAME,cAAc,GAAG,MAAM;EACzBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjB/B,gBAAgB,CAAC,IAAI,EAAE8B,OAAO,CAAC;IAC/B,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAIC,KAAK,IAAK;MAClC,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;QACvC,OAAO,EAAE;MACb;MACA,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;QACtB,OAAOA,KAAK;MAChB;MACA,OAAO,CAACA,KAAK,CAAC;IAClB,CAAC;IACD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACI,eAAe,GAAG,MAAM;MACzB,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MACtB,MAAM;QAAEZ,UAAU;QAAEa;MAAqB,CAAC,GAAG,IAAI;MACjD,IAAI,CAACb,UAAU,EAAE;QACb;MACJ;MACA,MAAM;QAAEG,KAAK;QAAEW,MAAM;QAAEC,aAAa;QAAEC,SAAS;QAAEC,WAAW;QAAEC,QAAQ;QAAEC;MAA4B,CAAC,GAAGnB,UAAU;MAClH,MAAMoB,YAAY,GAAG,IAAI,CAAClB,mBAAmB,CAACC,KAAK,CAAC;MACpD;AACZ;AACA;AACA;MACY,MAAMkB,eAAe,GAAGtC,SAAS,CAACqC,YAAY,CAACE,MAAM,GAAG,CAAC,GAAGF,YAAY,GAAG,CAACnC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACxF,IAAI,CAACoC,eAAe,EAAE;QAClB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,MAAME,mBAAmB,GAAGF,eAAe,CAAC,CAAC,CAAC;MAC9C,MAAMG,iBAAiB,GAAGrC,YAAY,CAAC2B,MAAM,EAAEE,SAAS,CAAC;MACzD,IAAI,CAACS,QAAQ,GAAG,IAAI,CAACC,QAAQ,GAAGtB,SAAS;MACzC,QAAQS,oBAAoB;QACxB,KAAK,WAAW;QAChB,KAAK,WAAW;UACZ,MAAMY,QAAQ,GAAGpC,oBAAoB,CAACyB,MAAM,EAAES,mBAAmB,EAAE,CAACf,EAAE,GAAGO,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACY,IAAI,MAAM,IAAI,IAAInB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;YAAEoB,KAAK,EAAE,OAAO;YAAEC,GAAG,EAAE,SAAS;YAAEC,IAAI,EAAE;UAAU,CAAC,CAAC;UAChP,MAAMJ,QAAQ,GAAGnC,gBAAgB,CAACuB,MAAM,EAAES,mBAAmB,EAAEC,iBAAiB,EAAET,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgB,IAAI,CAAC;UACnK,IAAId,WAAW,EAAE;YACb,IAAI,CAACQ,QAAQ,GAAG,GAAGA,QAAQ,IAAIC,QAAQ,EAAE;UAC7C,CAAC,MACI;YACD,IAAI,CAACD,QAAQ,GAAGA,QAAQ;YACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;UAC5B;UACA;QACJ,KAAK,MAAM;UACP,IAAIR,QAAQ,IAAIE,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;YACvC,IAAIU,UAAU,GAAG,GAAGZ,YAAY,CAACE,MAAM,OAAO,CAAC,CAAC;YAChD,IAAIH,2BAA2B,KAAKf,SAAS,EAAE;cAC3C,IAAI;gBACA4B,UAAU,GAAGb,2BAA2B,CAACC,YAAY,CAAC;cAC1D,CAAC,CACD,OAAOa,CAAC,EAAE;gBACNvD,aAAa,CAAC,uDAAuD,EAAEuD,CAAC,CAAC;cAC7E;YACJ;YACA,IAAI,CAACR,QAAQ,GAAGO,UAAU;UAC9B,CAAC,MACI;YACD,IAAI,CAACP,QAAQ,GAAGpC,oBAAoB,CAACyB,MAAM,EAAES,mBAAmB,EAAE,CAACd,EAAE,GAAGM,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACY,IAAI,MAAM,IAAI,IAAIlB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;cAAEmB,KAAK,EAAE,OAAO;cAAEC,GAAG,EAAE,SAAS;cAAEC,IAAI,EAAE;YAAU,CAAC,CAAC;UACnP;UACA;QACJ,KAAK,MAAM;UACP,IAAI,CAACJ,QAAQ,GAAGnC,gBAAgB,CAACuB,MAAM,EAAES,mBAAmB,EAAEC,iBAAiB,EAAET,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgB,IAAI,CAAC;UAClK;QACJ,KAAK,YAAY;UACb,IAAI,CAACN,QAAQ,GAAGpC,oBAAoB,CAACyB,MAAM,EAAES,mBAAmB,EAAE,CAACb,EAAE,GAAGK,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACY,IAAI,MAAM,IAAI,IAAIjB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;YAAEkB,KAAK,EAAE,MAAM;YAAEE,IAAI,EAAE;UAAU,CAAC,CAAC;UAC9N;QACJ,KAAK,OAAO;UACR,IAAI,CAACL,QAAQ,GAAGpC,oBAAoB,CAACyB,MAAM,EAAES,mBAAmB,EAAE,CAACZ,EAAE,GAAGI,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgB,IAAI,MAAM,IAAI,IAAIpB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;YAAEiB,KAAK,EAAE;UAAO,CAAC,CAAC;UAC7M;QACJ,KAAK,MAAM;UACP,IAAI,CAACH,QAAQ,GAAGpC,oBAAoB,CAACyB,MAAM,EAAES,mBAAmB,EAAE,CAACX,EAAE,GAAGG,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACgB,IAAI,MAAM,IAAI,IAAInB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG;YAAEkB,IAAI,EAAE;UAAU,CAAC,CAAC;UAC/M;MACR;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACI,sBAAsB,gBAAAC,iBAAA,CAAG,aAAY;MACtC,MAAM;QAAEnC;MAAW,CAAC,GAAGD,KAAI;MAC3B,IAAI,CAACC,UAAU,EAAE;QACb,OAAOoC,OAAO,CAACC,OAAO,CAAC,CAAC;MAC5B;MACA,OAAO,IAAID,OAAO,CAAEC,OAAO,IAAK;QAC5B9D,gBAAgB,CAACyB,UAAU,EAAE,WAAW,EAAEqC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MACtE,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACC,eAAe;MAAA,IAAAC,KAAA,GAAAL,iBAAA,CAAG,WAAOM,EAAE,EAAK;QACjC,MAAM;UAAEzC,UAAU;UAAEa;QAAqB,CAAC,GAAGd,KAAI;QACjD,IAAI,CAACC,UAAU,EAAE;UACb;QACJ;QACA,IAAI0C,uBAAuB,GAAG,KAAK;QACnC;AACZ;AACA;AACA;AACA;AACA;AACA;QACY,QAAQ7B,oBAAoB;UACxB,KAAK,WAAW;UAChB,KAAK,WAAW;YACZ,MAAM8B,WAAW,GAAG3C,UAAU,CAAC4C,YAAY,KAAK,MAAM;YACtD;AACpB;AACA;AACA;AACA;AACA;YACoB,IAAI,CAAC5C,UAAU,CAACiB,WAAW,IAAI0B,WAAW,EAAE;cACxC3C,UAAU,CAAC4C,YAAY,GAAG,MAAM;cAChCF,uBAAuB,GAAG,IAAI;YAClC;YACA;QACR;QACA;AACZ;AACA;AACA;AACA;AACA;AACA;QACY3C,KAAI,CAAC8C,cAAc,GAAG,MAAM;QAC5B9C,KAAI,CAAC+C,cAAc,CAACL,EAAE,EAAEC,uBAAuB,EAAE3C,KAAI,CAACgD,YAAY,CAAC;MACvE,CAAC;MAAA,iBAAAC,EAAA;QAAA,OAAAR,KAAA,CAAAS,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACC,eAAe,GAAIV,EAAE,IAAK;MAC3B,MAAM;QAAEzC,UAAU;QAAEa;MAAqB,CAAC,GAAG,IAAI;MACjD,IAAI,CAACb,UAAU,EAAE;QACb;MACJ;MACA,IAAI0C,uBAAuB,GAAG,KAAK;MACnC;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,QAAQ7B,oBAAoB;QACxB,KAAK,WAAW;QAChB,KAAK,WAAW;UACZ,MAAM8B,WAAW,GAAG3C,UAAU,CAAC4C,YAAY,KAAK,MAAM;UACtD,IAAID,WAAW,EAAE;YACb3C,UAAU,CAAC4C,YAAY,GAAG,MAAM;YAChCF,uBAAuB,GAAG,IAAI;UAClC;UACA;MACR;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACG,cAAc,GAAG,MAAM;MAC5B,IAAI,CAACC,cAAc,CAACL,EAAE,EAAEC,uBAAuB,EAAE,IAAI,CAACU,YAAY,CAAC;IACvE,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACN,cAAc;MAAA,IAAAO,KAAA,GAAAlB,iBAAA,CAAG,WAAOM,EAAE,EAAEC,uBAAuB,EAAEY,SAAS,EAAK;QACpE,MAAM;UAAErD;QAAU,CAAC,GAAGF,KAAI;QAC1B,IAAI,CAACE,SAAS,EAAE;UACZ;QACJ;QACA,IAAIA,SAAS,CAACsD,OAAO,KAAK,aAAa,EAAE;UACrC;AAChB;AACA;AACA;AACA;AACA;AACA;UACgB,IAAIb,uBAAuB,EAAE;YACzB,MAAM3C,KAAI,CAACmC,sBAAsB,CAAC,CAAC;UACvC;UACA;AAChB;AACA;AACA;AACA;UACgBjC,SAAS,CAACuD,OAAO,CAACC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjB,EAAE,CAAC,EAAE;YAAEkB,MAAM,EAAE;cACzDC,eAAe,EAAEN;YACrB;UAAE,CAAC,CAAC,CAAC;QACb,CAAC,MACI;UACDrD,SAAS,CAACuD,OAAO,CAAC,CAAC;QACvB;MACJ,CAAC;MAAA,iBAAAK,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAV,KAAA,CAAAJ,KAAA,OAAAC,SAAA;MAAA;IAAA;IACD,IAAI,CAACrC,oBAAoB,GAAG,WAAW;IACvC,IAAI,CAACY,QAAQ,GAAGrB,SAAS;IACzB,IAAI,CAACsB,QAAQ,GAAGtB,SAAS;IACzB,IAAI,CAAC4D,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACnB,cAAc,GAAGzC,SAAS;IAC/B,IAAI,CAAC6D,KAAK,GAAG,SAAS;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG/D,SAAS;EAC7B;EACMgE,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAlC,iBAAA;MACtB,MAAM;QAAEgC;MAAS,CAAC,GAAGE,MAAI;MACzB,IAAI,CAACF,QAAQ,EAAE;QACXzF,aAAa,CAAC,0GAA0G,EAAE2F,MAAI,CAACC,EAAE,CAAC;QAClI;MACJ;MACA,MAAMtE,UAAU,GAAIqE,MAAI,CAACrE,UAAU,GAAGuE,QAAQ,CAACC,cAAc,CAACL,QAAQ,CAAE;MACxE,IAAI,CAACnE,UAAU,EAAE;QACbtB,aAAa,CAAC,0CAA0CyF,QAAQ,IAAI,EAAEE,MAAI,CAACC,EAAE,CAAC;QAC9E;MACJ;MACA;AACR;AACA;AACA;MACQ,IAAItE,UAAU,CAACuD,OAAO,KAAK,cAAc,EAAE;QACvC7E,aAAa,CAAC,6CAA6CyF,QAAQ,mBAAmBnE,UAAU,CAACuD,OAAO,CAACkB,WAAW,CAAC,CAAC,YAAY,EAAEzE,UAAU,CAAC;QAC/I;MACJ;MACA;AACR;AACA;AACA;AACA;AACA;MACQ,MAAM0E,EAAE,GAAG,IAAIC,oBAAoB,CAAEC,OAAO,IAAK;QAC7C,MAAMnC,EAAE,GAAGmC,OAAO,CAAC,CAAC,CAAC;QACrBP,MAAI,CAACL,cAAc,GAAGvB,EAAE,CAACoC,cAAc;MAC3C,CAAC,EAAE;QACCC,SAAS,EAAE;MACf,CAAC,CAAC;MACFJ,EAAE,CAACK,OAAO,CAAC/E,UAAU,CAAC;MACtB;AACR;AACA;AACA;AACA;MACQ,MAAMC,SAAS,GAAIoE,MAAI,CAACpE,SAAS,GAAGD,UAAU,CAACgF,OAAO,CAAC,wBAAwB,CAAE;MACjF;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACQ,IAAI/E,SAAS,EAAE;QACXA,SAAS,CAACgF,SAAS,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1D;MACAzG,gBAAgB,CAACuB,UAAU,EAAE,MAAM;QAC/B,MAAMa,oBAAoB,GAAIwD,MAAI,CAACxD,oBAAoB,GAAGb,UAAU,CAAC4C,YAAY,IAAI,WAAY;QACjG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACYyB,MAAI,CAAC9D,eAAe,CAAC,CAAC;QACtBhC,gBAAgB,CAACyB,UAAU,EAAE,gBAAgB,EAAEqE,MAAI,CAAC9D,eAAe,CAAC;QACpE;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACY,QAAQM,oBAAoB;UACxB,KAAK,WAAW;UAChB,KAAK,MAAM;UACX,KAAK,YAAY;UACjB,KAAK,OAAO;UACZ,KAAK,MAAM;YACPwD,MAAI,CAACxB,cAAc,GAAG,MAAM;YAC5B;UACJ,KAAK,WAAW;UAChB,KAAK,MAAM;YACPwB,MAAI,CAACxB,cAAc,GAAG,MAAM;YAC5B;QACR;MACJ,CAAC,CAAC;IAAC;EACP;EACAsC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAElB,KAAK;MAAExC,QAAQ;MAAEC,QAAQ;MAAEmB,cAAc;MAAEmB,cAAc;MAAEE;IAAS,CAAC,GAAG,IAAI;IACpF,MAAMkB,IAAI,GAAGvG,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQZ,CAAC,CAACE,IAAI,EAAE;MAAEkH,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE3G,kBAAkB,CAACsF,KAAK,EAAE;QAC5F,CAACmB,IAAI,GAAG,IAAI;QACZ,CAAC,GAAGvC,cAAc,SAAS,GAAGmB,cAAc;QAC5C,CAAC,0BAA0B,GAAGE;MAClC,CAAC;IAAE,CAAC,EAAEzC,QAAQ,IAAKxD,CAAC,CAAC,QAAQ,EAAE;MAAEoH,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,EAAE,EAAE,aAAa;MAAE,eAAe,EAAEvB,cAAc,GAAG,MAAM,GAAG,OAAO;MAAEwB,OAAO,EAAE,IAAI,CAACjD,eAAe;MAAE2B,QAAQ,EAAEA,QAAQ;MAAEuB,IAAI,EAAE,QAAQ;MAAEC,GAAG,EAAGpB,EAAE,IAAM,IAAI,CAACvB,YAAY,GAAGuB;IAAI,CAAC,EAAErG,CAAC,CAAC,MAAM,EAAE;MAAEoH,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAc,CAAC,EAAElE,QAAQ,CAAC,EAAE2D,IAAI,KAAK,IAAI,IAAInH,CAAC,CAAC,mBAAmB,EAAE;MAAEoH,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAE,EAAE3D,QAAQ,IAAKzD,CAAC,CAAC,QAAQ,EAAE;MAAEoH,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,EAAE,EAAE,aAAa;MAAE,eAAe,EAAEvB,cAAc,GAAG,MAAM,GAAG,OAAO;MAAEwB,OAAO,EAAE,IAAI,CAACrC,eAAe;MAAEe,QAAQ,EAAEA,QAAQ;MAAEuB,IAAI,EAAE,QAAQ;MAAEC,GAAG,EAAGpB,EAAE,IAAM,IAAI,CAAClB,YAAY,GAAGkB;IAAI,CAAC,EAAErG,CAAC,CAAC,MAAM,EAAE;MAAEoH,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAc,CAAC,EAAEjE,QAAQ,CAAC,EAAE0D,IAAI,KAAK,IAAI,IAAInH,CAAC,CAAC,mBAAmB,EAAE;MAAEoH,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAE,CAAC;EAC77B;EACA,IAAIf,EAAEA,CAAA,EAAG;IAAE,OAAOjG,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDuB,cAAc,CAACgG,KAAK,GAAG;EACnBC,GAAG,EAAEpG,0BAA0B;EAC/BqG,EAAE,EAAEnG;AACR,CAAC;AAED,SAASC,cAAc,IAAImG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}