"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePhonesTable1703000002000 = void 0;
const typeorm_1 = require("typeorm");
class CreatePhonesTable1703000002000 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'phones',
            columns: [
                {
                    name: 'id',
                    type: 'int',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'model',
                    type: 'varchar',
                    length: '200',
                },
                {
                    name: 'image',
                    type: 'varchar',
                    length: '500',
                },
                {
                    name: 'releaseDate',
                    type: 'date',
                },
                {
                    name: 'price',
                    type: 'decimal',
                    precision: 10,
                    scale: 2,
                },
                {
                    name: 'category',
                    type: 'varchar',
                    length: '100',
                },
                {
                    name: 'brand_id',
                    type: 'int',
                },
            ],
        }), true);
        await queryRunner.createForeignKey('phones', new typeorm_1.TableForeignKey({
            columnNames: ['brand_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'brands',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('phones');
    }
}
exports.CreatePhonesTable1703000002000 = CreatePhonesTable1703000002000;
//# sourceMappingURL=1703000002000-CreatePhonesTable.js.map