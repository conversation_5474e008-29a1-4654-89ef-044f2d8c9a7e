{"ast": null, "code": "var _HomePage;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../sales/services/sale.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/router\";\nconst _c0 = a0 => [\"/sales/details\", a0];\nconst _forTrack0 = ($index, $item) => $item.id;\nfunction HomePage_For_65_ion_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 23);\n  }\n}\nfunction HomePage_For_65_ion_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 24);\n  }\n}\nfunction HomePage_For_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\");\n    i0.ɵɵtemplate(1, HomePage_For_65_ion_icon_1_Template, 1, 0, \"ion-icon\", 20)(2, HomePage_For_65_ion_icon_2_Template, 1, 0, \"ion-icon\", 21);\n    i0.ɵɵelementStart(3, \"ion-label\")(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"ion-note\", 22);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const product_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r1.type === \"phone\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r1.type === \"accessory\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", product_r1.quantity, \" unidades vendidas\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 5, product_r1.revenue, \"BRL\"));\n  }\n}\nfunction HomePage_ForEmpty_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\")(1, \"ion-label\");\n    i0.ɵɵtext(2, \"Nenhum produto vendido no per\\u00EDodo\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HomePage_For_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\", 19)(1, \"ion-label\")(2, \"h2\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ion-text\", 25);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 26)(12, \"ion-badge\", 27);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"ion-text\", 28)(15, \"h3\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"currency\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const sale_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(14, _c0, sale_r2.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Venda #\", sale_r2.id, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 8, sale_r2.date, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Cliente: \", sale_r2.customer.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Loja: \", sale_r2.store.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", ctx_r2.getStatusColor(sale_r2.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getStatusLabel(sale_r2.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 11, sale_r2.totalValue, \"BRL\"));\n  }\n}\nfunction HomePage_ForEmpty_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\")(1, \"ion-label\");\n    i0.ɵɵtext(2, \"Nenhuma venda recente\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class HomePage {\n  constructor(saleService) {\n    this.saleService = saleService;\n    this.totalRevenue = 0;\n    this.monthlyRevenue = 0;\n    this.weeklyRevenue = 0;\n    this.totalSalesMonth = 0;\n    this.totalSalesWeek = 0;\n    this.averageTicket = 0;\n    this.topProducts = [];\n    this.recentSales = [];\n  }\n  ngOnInit() {\n    this.loadDashboardData();\n  }\n  ionViewWillEnter() {\n    this.loadDashboardData();\n  }\n  loadDashboardData() {\n    this.saleService.getAll().subscribe({\n      next: sales => {\n        const completedSales = sales.filter(sale => sale.status === 'completed');\n        this.calculateFinancialSummary(completedSales);\n        this.calculateSalesStats(completedSales);\n        this.calculateTopProducts(completedSales);\n        this.recentSales = [...sales].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 5);\n      },\n      error: error => {\n        console.error('Erro ao carregar dados do dashboard', error);\n      }\n    });\n  }\n  calculateFinancialSummary(sales) {\n    this.totalRevenue = sales.reduce((sum, sale) => sum + sale.totalValue, 0);\n    const now = new Date();\n    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n    const startOfWeek = new Date(now);\n    startOfWeek.setDate(now.getDate() - now.getDay());\n    this.monthlyRevenue = sales.filter(sale => new Date(sale.date) >= startOfMonth).reduce((sum, sale) => sum + sale.totalValue, 0);\n    this.weeklyRevenue = sales.filter(sale => new Date(sale.date) >= startOfWeek).reduce((sum, sale) => sum + sale.totalValue, 0);\n  }\n  calculateSalesStats(sales) {\n    const now = new Date();\n    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n    const startOfWeek = new Date(now);\n    startOfWeek.setDate(now.getDate() - now.getDay());\n    const monthSales = sales.filter(sale => new Date(sale.date) >= startOfMonth);\n    this.totalSalesMonth = monthSales.length;\n    const weekSales = sales.filter(sale => new Date(sale.date) >= startOfWeek);\n    this.totalSalesWeek = weekSales.length;\n    // Ticket médio\n    this.averageTicket = sales.length > 0 ? this.totalRevenue / sales.length : 0;\n  }\n  calculateTopProducts(sales) {\n    const productMap = new Map();\n    sales.forEach(sale => {\n      sale.items.forEach(item => {\n        var _item$product$id;\n        const productId = ((_item$product$id = item.product.id) === null || _item$product$id === void 0 ? void 0 : _item$product$id.toString()) || '';\n        const productName = this.getProductName(item.product, item.productType);\n        const productType = item.productType;\n        const quantity = item.quantity;\n        const revenue = item.subtotal;\n        if (productMap.has(productId)) {\n          const existingProduct = productMap.get(productId);\n          existingProduct.quantity += quantity;\n          existingProduct.revenue += revenue;\n        } else {\n          productMap.set(productId, {\n            id: productId,\n            name: productName,\n            type: productType,\n            quantity: quantity,\n            revenue: revenue\n          });\n        }\n      });\n    });\n    this.topProducts = Array.from(productMap.values()).sort((a, b) => b.quantity - a.quantity).slice(0, 5);\n  }\n  getProductName(product, type) {\n    if (type === 'phone') {\n      return product.model || 'Celular';\n    } else {\n      return product.name || 'Acessório';\n    }\n  }\n  getStatusLabel(status) {\n    const statusMap = {\n      'pending': 'Pendente',\n      'completed': 'Concluída',\n      'canceled': 'Cancelada'\n    };\n    return statusMap[status] || status;\n  }\n  getStatusColor(status) {\n    const colorMap = {\n      'pending': 'warning',\n      'completed': 'success',\n      'canceled': 'danger'\n    };\n    return colorMap[status] || 'medium';\n  }\n}\n_HomePage = HomePage;\n_HomePage.ɵfac = function HomePage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HomePage)(i0.ɵɵdirectiveInject(i1.SaleService));\n};\n_HomePage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _HomePage,\n  selectors: [[\"app-home\"]],\n  standalone: false,\n  decls: 79,\n  vars: 22,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [3, \"fullscreen\"], [1, \"dashboard-container\"], [1, \"summary-card\"], [1, \"financial-summary\"], [1, \"summary-item\"], [1, \"stats-container\"], [1, \"stats-card\"], [1, \"stats-grid\"], [1, \"stats-item\"], [\"name\", \"calendar-outline\", \"color\", \"secondary\"], [\"name\", \"today-outline\", \"color\", \"secondary\"], [\"name\", \"trending-up-outline\", \"color\", \"secondary\"], [\"lines\", \"none\"], [1, \"recent-sales-card\"], [\"fill\", \"clear\", \"routerLink\", \"/sales\"], [\"name\", \"arrow-forward-outline\", \"slot\", \"end\"], [\"button\", \"\", 3, \"routerLink\"], [\"name\", \"phone-portrait-outline\", \"slot\", \"start\", 4, \"ngIf\"], [\"name\", \"hardware-chip-outline\", \"slot\", \"start\", 4, \"ngIf\"], [\"slot\", \"end\", \"color\", \"secondary\"], [\"name\", \"phone-portrait-outline\", \"slot\", \"start\"], [\"name\", \"hardware-chip-outline\", \"slot\", \"start\"], [\"color\", \"medium\"], [\"slot\", \"end\", 1, \"sale-info\"], [3, \"color\"], [\"color\", \"dark\"]],\n  template: function HomePage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-menu-button\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5, \"Dashboard\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 3)(7, \"div\", 4)(8, \"ion-card\", 5)(9, \"ion-card-header\")(10, \"ion-card-title\");\n      i0.ɵɵtext(11, \"Resumo Financeiro\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(12, \"ion-card-content\")(13, \"div\", 6)(14, \"div\", 7)(15, \"h2\");\n      i0.ɵɵtext(16);\n      i0.ɵɵpipe(17, \"currency\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(18, \"p\");\n      i0.ɵɵtext(19, \"Faturamento Total\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(20, \"div\", 7)(21, \"h2\");\n      i0.ɵɵtext(22);\n      i0.ɵɵpipe(23, \"currency\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"p\");\n      i0.ɵɵtext(25, \"Faturamento do M\\u00EAs\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(26, \"div\", 7)(27, \"h2\");\n      i0.ɵɵtext(28);\n      i0.ɵɵpipe(29, \"currency\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(30, \"p\");\n      i0.ɵɵtext(31, \"Faturamento da Semana\");\n      i0.ɵɵelementEnd()()()()();\n      i0.ɵɵelementStart(32, \"div\", 8)(33, \"ion-card\", 9)(34, \"ion-card-header\")(35, \"ion-card-title\");\n      i0.ɵɵtext(36, \"Vendas\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(37, \"ion-card-content\")(38, \"div\", 10)(39, \"div\", 11);\n      i0.ɵɵelement(40, \"ion-icon\", 12);\n      i0.ɵɵelementStart(41, \"h3\");\n      i0.ɵɵtext(42);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"p\");\n      i0.ɵɵtext(44, \"Vendas no M\\u00EAs\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(45, \"div\", 11);\n      i0.ɵɵelement(46, \"ion-icon\", 13);\n      i0.ɵɵelementStart(47, \"h3\");\n      i0.ɵɵtext(48);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"p\");\n      i0.ɵɵtext(50, \"Vendas na Semana\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(51, \"div\", 11);\n      i0.ɵɵelement(52, \"ion-icon\", 14);\n      i0.ɵɵelementStart(53, \"h3\");\n      i0.ɵɵtext(54);\n      i0.ɵɵpipe(55, \"currency\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(56, \"p\");\n      i0.ɵɵtext(57, \"Ticket M\\u00E9dio\");\n      i0.ɵɵelementEnd()()()()();\n      i0.ɵɵelementStart(58, \"ion-card\", 9)(59, \"ion-card-header\")(60, \"ion-card-title\");\n      i0.ɵɵtext(61, \"Produtos Mais Vendidos\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(62, \"ion-card-content\")(63, \"ion-list\", 15);\n      i0.ɵɵrepeaterCreate(64, HomePage_For_65_Template, 11, 8, \"ion-item\", null, _forTrack0, false, HomePage_ForEmpty_66_Template, 3, 0, \"ion-item\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(67, \"ion-card\", 16)(68, \"ion-card-header\")(69, \"ion-card-title\");\n      i0.ɵɵtext(70, \"\\u00DAltimas Vendas\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(71, \"ion-button\", 17);\n      i0.ɵɵtext(72, \" Ver Todas \");\n      i0.ɵɵelement(73, \"ion-icon\", 18);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(74, \"ion-card-content\")(75, \"ion-list\");\n      i0.ɵɵrepeaterCreate(76, HomePage_For_77_Template, 18, 16, \"ion-item\", 19, _forTrack0, false, HomePage_ForEmpty_78_Template, 3, 0, \"ion-item\");\n      i0.ɵɵelementEnd()()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(10);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 10, ctx.totalRevenue, \"BRL\"));\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 13, ctx.monthlyRevenue, \"BRL\"));\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(29, 16, ctx.weeklyRevenue, \"BRL\"));\n      i0.ɵɵadvance(14);\n      i0.ɵɵtextInterpolate(ctx.totalSalesMonth);\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate(ctx.totalSalesWeek);\n      i0.ɵɵadvance(6);\n      i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(55, 19, ctx.averageTicket, \"BRL\"));\n      i0.ɵɵadvance(10);\n      i0.ɵɵrepeater(ctx.topProducts);\n      i0.ɵɵadvance(12);\n      i0.ɵɵrepeater(ctx.recentSales);\n    }\n  },\n  dependencies: [i2.NgIf, i3.IonBadge, i3.IonButton, i3.IonButtons, i3.IonCard, i3.IonCardContent, i3.IonCardHeader, i3.IonCardTitle, i3.IonContent, i3.IonHeader, i3.IonIcon, i3.IonItem, i3.IonLabel, i3.IonList, i3.IonMenuButton, i3.IonNote, i3.IonText, i3.IonTitle, i3.IonToolbar, i3.RouterLinkDelegate, i4.RouterLink, i2.CurrencyPipe, i2.DatePipe],\n  styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.summary-card[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.summary-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\\n  background-color: var(--ion-color-secondary);\\n  color: white;\\n  border-radius: 12px 12px 0 0;\\n}\\n.summary-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  color: white;\\n}\\n\\n.financial-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 10px 0;\\n}\\n.financial-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 10px;\\n  min-width: 120px;\\n}\\n.financial-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  margin: 0;\\n  color: var(--ion-color-secondary);\\n}\\n.financial-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0;\\n  color: var(--ion-color-medium);\\n  font-size: 0.9rem;\\n}\\n\\n.stats-container[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n@media (min-width: 768px) {\\n  .stats-container[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr 1fr;\\n  }\\n}\\n\\n.stats-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  height: 100%;\\n}\\n.stats-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\\n  background-color: var(--ion-color-light);\\n}\\n.stats-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\\n  gap: 15px;\\n  padding: 10px 0;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stats-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 10px;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stats-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 8px;\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stats-item[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  margin: 0;\\n  color: var(--ion-color-dark);\\n}\\n.stats-grid[_ngcontent-%COMP%]   .stats-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0;\\n  color: var(--ion-color-medium);\\n  font-size: 0.9rem;\\n}\\n\\n.recent-sales-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n.recent-sales-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.recent-sales-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n}\\n.recent-sales-card[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%] {\\n  --padding-start: 16px;\\n  --padding-end: 16px;\\n  --padding-top: 12px;\\n  --padding-bottom: 12px;\\n}\\n.recent-sales-card[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-bottom: 4px;\\n}\\n\\n.sale-info[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n.sale-info[_ngcontent-%COMP%]   ion-badge[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  padding: 6px 8px;\\n  border-radius: 4px;\\n}\\n.sale-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  margin: 8px 0 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvaG9tZS9ob21lLnBhZ2Uuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7QUFDRjs7QUFHQTtFQUNFLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSx5Q0FBQTtBQUFGO0FBRUU7RUFDRSw0Q0FBQTtFQUNBLFlBQUE7RUFDQSw0QkFBQTtBQUFKO0FBRUk7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsWUFBQTtBQUFOOztBQUtBO0VBQ0UsYUFBQTtFQUNBLDZCQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7QUFGRjtBQUlFO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7QUFGSjtBQUlJO0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLFNBQUE7RUFDQSxpQ0FBQTtBQUZOO0FBS0k7RUFDRSxlQUFBO0VBQ0EsOEJBQUE7RUFDQSxpQkFBQTtBQUhOOztBQVNBO0VBQ0UsYUFBQTtFQUNBLDBCQUFBO0VBQ0EsU0FBQTtFQUNBLG1CQUFBO0FBTkY7QUFRRTtFQU5GO0lBT0ksOEJBQUE7RUFMRjtBQUNGOztBQVFBO0VBQ0UsbUJBQUE7RUFDQSx5Q0FBQTtFQUNBLFlBQUE7QUFMRjtBQU9FO0VBQ0Usd0NBQUE7QUFMSjtBQU9JO0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtBQUxOOztBQVVBO0VBQ0UsYUFBQTtFQUNBLDJEQUFBO0VBQ0EsU0FBQTtFQUNBLGVBQUE7QUFQRjtBQVNFO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0FBUEo7QUFTSTtFQUNFLGVBQUE7RUFDQSxrQkFBQTtBQVBOO0FBVUk7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EsU0FBQTtFQUNBLDRCQUFBO0FBUk47QUFXSTtFQUNFLGVBQUE7RUFDQSw4QkFBQTtFQUNBLGlCQUFBO0FBVE47O0FBZUE7RUFDRSxtQkFBQTtFQUNBLHlDQUFBO0FBWkY7QUFjRTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0FBWko7QUFjSTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7QUFaTjtBQWdCRTtFQUNFLHFCQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtFQUNBLHNCQUFBO0FBZEo7QUFnQkk7RUFDRSxnQkFBQTtFQUNBLGtCQUFBO0FBZE47O0FBbUJBO0VBQ0UsaUJBQUE7QUFoQkY7QUFrQkU7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QUFoQko7QUFtQkU7RUFDRSxnQkFBQTtFQUNBLGVBQUE7QUFqQkoiLCJzb3VyY2VzQ29udGVudCI6WyIuZGFzaGJvYXJkLWNvbnRhaW5lciB7XHJcbiAgcGFkZGluZzogMTZweDtcclxuICBtYXgtd2lkdGg6IDEyMDBweDtcclxuICBtYXJnaW46IDAgYXV0bztcclxufVxyXG5cclxuLy8gUmVzdW1vIEZpbmFuY2Vpcm9cclxuLnN1bW1hcnktY2FyZCB7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG4gIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gIFxyXG4gIGlvbi1jYXJkLWhlYWRlciB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1pb24tY29sb3Itc2Vjb25kYXJ5KTtcclxuICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDEycHggMTJweCAwIDA7XHJcbiAgICBcclxuICAgIGlvbi1jYXJkLXRpdGxlIHtcclxuICAgICAgZm9udC1zaXplOiAxLjNyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi5maW5hbmNpYWwtc3VtbWFyeSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWFyb3VuZDtcclxuICBmbGV4LXdyYXA6IHdyYXA7XHJcbiAgcGFkZGluZzogMTBweCAwO1xyXG4gIFxyXG4gIC5zdW1tYXJ5LWl0ZW0ge1xyXG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgcGFkZGluZzogMTBweDtcclxuICAgIG1pbi13aWR0aDogMTIwcHg7XHJcbiAgICBcclxuICAgIGgyIHtcclxuICAgICAgZm9udC1zaXplOiAxLjhyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgICAgY29sb3I6IHZhcigtLWlvbi1jb2xvci1zZWNvbmRhcnkpO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBwIHtcclxuICAgICAgbWFyZ2luOiA1cHggMCAwO1xyXG4gICAgICBjb2xvcjogdmFyKC0taW9uLWNvbG9yLW1lZGl1bSk7XHJcbiAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gRXN0YXTDg8Ktc3RpY2FzXHJcbi5zdGF0cy1jb250YWluZXIge1xyXG4gIGRpc3BsYXk6IGdyaWQ7XHJcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XHJcbiAgZ2FwOiAyMHB4O1xyXG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XHJcbiAgXHJcbiAgQG1lZGlhIChtaW4td2lkdGg6IDc2OHB4KSB7XHJcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmciAxZnI7XHJcbiAgfVxyXG59XHJcblxyXG4uc3RhdHMtY2FyZCB7XHJcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICBoZWlnaHQ6IDEwMCU7XHJcbiAgXHJcbiAgaW9uLWNhcmQtaGVhZGVyIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWlvbi1jb2xvci1saWdodCk7XHJcbiAgICBcclxuICAgIGlvbi1jYXJkLXRpdGxlIHtcclxuICAgICAgZm9udC1zaXplOiAxLjJyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4uc3RhdHMtZ3JpZCB7XHJcbiAgZGlzcGxheTogZ3JpZDtcclxuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDEwMHB4LCAxZnIpKTtcclxuICBnYXA6IDE1cHg7XHJcbiAgcGFkZGluZzogMTBweCAwO1xyXG4gIFxyXG4gIC5zdGF0cy1pdGVtIHtcclxuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgIHBhZGRpbmc6IDEwcHg7XHJcbiAgICBcclxuICAgIGlvbi1pY29uIHtcclxuICAgICAgZm9udC1zaXplOiAycmVtO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGgzIHtcclxuICAgICAgZm9udC1zaXplOiAxLjVyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgICAgY29sb3I6IHZhcigtLWlvbi1jb2xvci1kYXJrKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgcCB7XHJcbiAgICAgIG1hcmdpbjogNXB4IDAgMDtcclxuICAgICAgY29sb3I6IHZhcigtLWlvbi1jb2xvci1tZWRpdW0pO1xyXG4gICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIMODwppsdGltYXMgVmVuZGFzXHJcbi5yZWNlbnQtc2FsZXMtY2FyZCB7XHJcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICBcclxuICBpb24tY2FyZC1oZWFkZXIge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBcclxuICAgIGlvbi1jYXJkLXRpdGxlIHtcclxuICAgICAgZm9udC1zaXplOiAxLjJyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICB9XHJcbiAgfVxyXG4gIFxyXG4gIGlvbi1pdGVtIHtcclxuICAgIC0tcGFkZGluZy1zdGFydDogMTZweDtcclxuICAgIC0tcGFkZGluZy1lbmQ6IDE2cHg7XHJcbiAgICAtLXBhZGRpbmctdG9wOiAxMnB4O1xyXG4gICAgLS1wYWRkaW5nLWJvdHRvbTogMTJweDtcclxuICAgIFxyXG4gICAgaDIge1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiA0cHg7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4uc2FsZS1pbmZvIHtcclxuICB0ZXh0LWFsaWduOiByaWdodDtcclxuICBcclxuICBpb24tYmFkZ2Uge1xyXG4gICAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgcGFkZGluZzogNnB4IDhweDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICB9XHJcbiAgXHJcbiAgaDMge1xyXG4gICAgZm9udC13ZWlnaHQ6IDcwMDtcclxuICAgIG1hcmdpbjogOHB4IDAgMDtcclxuICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n});", "map": {"version": 3, "names": ["i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtemplate", "HomePage_For_65_ion_icon_1_Template", "HomePage_For_65_ion_icon_2_Template", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "product_r1", "type", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "quantity", "ɵɵpipeBind2", "revenue", "ɵɵpureFunction1", "_c0", "sale_r2", "id", "date", "customer", "store", "ctx_r2", "getStatusColor", "status", "getStatusLabel", "totalValue", "HomePage", "constructor", "saleService", "totalRevenue", "monthlyRevenue", "weeklyRevenue", "totalSalesMonth", "totalSalesWeek", "averageTicket", "topProducts", "recentSales", "ngOnInit", "loadDashboardData", "ionViewWillEnter", "getAll", "subscribe", "next", "sales", "completedSales", "filter", "sale", "calculateFinancialSummary", "calculateSalesStats", "calculateTopProducts", "sort", "a", "b", "Date", "getTime", "slice", "error", "console", "reduce", "sum", "now", "startOfMonth", "getFullYear", "getMonth", "startOfWeek", "setDate", "getDate", "getDay", "monthSales", "length", "weekSales", "productMap", "Map", "for<PERSON>ach", "items", "item", "_item$product$id", "productId", "product", "toString", "productName", "getProductName", "productType", "subtotal", "has", "existingProduct", "get", "set", "Array", "from", "values", "model", "statusMap", "colorMap", "ɵɵdirectiveInject", "i1", "SaleService", "selectors", "standalone", "decls", "vars", "consts", "template", "HomePage_Template", "rf", "ctx", "ɵɵrepeaterCreate", "HomePage_For_65_Template", "_forTrack0", "HomePage_ForEmpty_66_Template", "HomePage_For_77_Template", "HomePage_ForEmpty_78_Template", "ɵɵrepeater"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\home\\home.page.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\home\\home.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { SaleService } from '../sales/services/sale.service';\r\nimport { Sale, SaleStatus } from '../sales/models/sale.type';\r\n\r\ninterface TopProduct {\r\n  id: number | string;\r\n  name: string;\r\n  type: 'phone' | 'accessory';\r\n  quantity: number;\r\n  revenue: number;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: 'home.page.html',\r\n  styleUrls: ['home.page.scss'],\r\n  standalone: false,\r\n})\r\nexport class HomePage implements OnInit {\r\n  totalRevenue: number = 0;\r\n  monthlyRevenue: number = 0;\r\n  weeklyRevenue: number = 0;\r\n  \r\n  totalSalesMonth: number = 0;\r\n  totalSalesWeek: number = 0;\r\n  averageTicket: number = 0;\r\n\r\n  topProducts: TopProduct[] = [];\r\n  \r\n  recentSales: Sale[] = [];\r\n  \r\n  constructor(private saleService: SaleService) {}\r\n  \r\n  ngOnInit() {\r\n    this.loadDashboardData();\r\n  }\r\n  \r\n  ionViewWillEnter() {\r\n    this.loadDashboardData();\r\n  }\r\n  \r\n  loadDashboardData() {\r\n    this.saleService.getAll().subscribe({\r\n      next: (sales) => {\r\n        const completedSales = sales.filter(sale => sale.status === 'completed');\r\n        \r\n        this.calculateFinancialSummary(completedSales);\r\n        \r\n        this.calculateSalesStats(completedSales);\r\n        \r\n        this.calculateTopProducts(completedSales);\r\n        \r\n        this.recentSales = [...sales]\r\n          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())\r\n          .slice(0, 5);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erro ao carregar dados do dashboard', error);\r\n      }\r\n    });\r\n  }\r\n  \r\n  calculateFinancialSummary(sales: Sale[]) {\r\n    this.totalRevenue = sales.reduce((sum, sale) => sum + sale.totalValue, 0);\r\n    \r\n    const now = new Date();\r\n    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\r\n    const startOfWeek = new Date(now);\r\n    startOfWeek.setDate(now.getDate() - now.getDay());\r\n    \r\n    this.monthlyRevenue = sales\r\n      .filter(sale => new Date(sale.date) >= startOfMonth)\r\n      .reduce((sum, sale) => sum + sale.totalValue, 0);\r\n    \r\n    this.weeklyRevenue = sales\r\n      .filter(sale => new Date(sale.date) >= startOfWeek)\r\n      .reduce((sum, sale) => sum + sale.totalValue, 0);\r\n  }\r\n  \r\n  calculateSalesStats(sales: Sale[]) {\r\n    const now = new Date();\r\n    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\r\n    const startOfWeek = new Date(now);\r\n    startOfWeek.setDate(now.getDate() - now.getDay());\r\n    \r\n    const monthSales = sales.filter(sale => new Date(sale.date) >= startOfMonth);\r\n    this.totalSalesMonth = monthSales.length;\r\n    \r\n    const weekSales = sales.filter(sale => new Date(sale.date) >= startOfWeek);\r\n    this.totalSalesWeek = weekSales.length;\r\n    \r\n    // Ticket médio\r\n    this.averageTicket = sales.length > 0 ? this.totalRevenue / sales.length : 0;\r\n  }\r\n  \r\n  calculateTopProducts(sales: Sale[]) {\r\n    const productMap = new Map<string, TopProduct>();\r\n\r\n    sales.forEach(sale => {\r\n      sale.items.forEach(item => {\r\n        const productId = item.product.id?.toString() || '';\r\n        const productName = this.getProductName(item.product, item.productType);\r\n        const productType = item.productType as 'phone' | 'accessory';\r\n        const quantity = item.quantity;\r\n        const revenue = item.subtotal;\r\n        \r\n        if (productMap.has(productId)) {\r\n          const existingProduct = productMap.get(productId)!;\r\n          existingProduct.quantity += quantity;\r\n          existingProduct.revenue += revenue;\r\n        } else {\r\n          productMap.set(productId, {\r\n            id: productId,\r\n            name: productName,\r\n            type: productType,\r\n            quantity: quantity,\r\n            revenue: revenue\r\n          });\r\n        }\r\n      });\r\n    });\r\n    \r\n    this.topProducts = Array.from(productMap.values())\r\n      .sort((a, b) => b.quantity - a.quantity)\r\n      .slice(0, 5);\r\n  }\r\n  \r\n  getProductName(product: any, type: string): string {\r\n    if (type === 'phone') {\r\n      return product.model || 'Celular';\r\n    } else {\r\n      return product.name || 'Acessório';\r\n    }\r\n  }\r\n  \r\n  getStatusLabel(status: string): string {\r\n    const statusMap: {[key: string]: string} = {\r\n      'pending': 'Pendente',\r\n      'completed': 'Concluída',\r\n      'canceled': 'Cancelada'\r\n    };\r\n    return statusMap[status] || status;\r\n  }\r\n  \r\n  getStatusColor(status: string): string {\r\n    const colorMap: {[key: string]: string} = {\r\n      'pending': 'warning',\r\n      'completed': 'success',\r\n      'canceled': 'danger'\r\n    };\r\n    return colorMap[status] || 'medium';\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-menu-button></ion-menu-button>\r\n    </ion-buttons>\r\n    <ion-title>Dashboard</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <div class=\"dashboard-container\">\r\n    <ion-card class=\"summary-card\">\r\n      <ion-card-header>\r\n        <ion-card-title>Resumo Financeiro</ion-card-title>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <div class=\"financial-summary\">\r\n          <div class=\"summary-item\">\r\n            <h2>{{ totalRevenue | currency:'BRL' }}</h2>\r\n            <p>Faturamento Total</p>\r\n          </div>\r\n          <div class=\"summary-item\">\r\n            <h2>{{ monthlyRevenue | currency:'BRL' }}</h2>\r\n            <p>Faturamento do Mês</p>\r\n          </div>\r\n          <div class=\"summary-item\">\r\n            <h2>{{ weeklyRevenue | currency:'BRL' }}</h2>\r\n            <p>Faturamento da Semana</p>\r\n          </div>\r\n        </div>\r\n      </ion-card-content>\r\n    </ion-card>\r\n\r\n    <div class=\"stats-container\">\r\n      <ion-card class=\"stats-card\">\r\n        <ion-card-header>\r\n          <ion-card-title>Vendas</ion-card-title>\r\n        </ion-card-header>\r\n        <ion-card-content>\r\n          <div class=\"stats-grid\">\r\n            <div class=\"stats-item\">\r\n              <ion-icon name=\"calendar-outline\" color=\"secondary\"></ion-icon>\r\n              <h3>{{ totalSalesMonth }}</h3>\r\n              <p>Vendas no Mês</p>\r\n            </div>\r\n            <div class=\"stats-item\">\r\n              <ion-icon name=\"today-outline\" color=\"secondary\"></ion-icon>\r\n              <h3>{{ totalSalesWeek }}</h3>\r\n              <p>Vendas na Semana</p>\r\n            </div>\r\n            <div class=\"stats-item\">\r\n              <ion-icon name=\"trending-up-outline\" color=\"secondary\"></ion-icon>\r\n              <h3>{{ averageTicket | currency:'BRL' }}</h3>\r\n              <p>Ticket Médio</p>\r\n            </div>\r\n          </div>\r\n        </ion-card-content>\r\n      </ion-card>\r\n\r\n      <ion-card class=\"stats-card\">\r\n        <ion-card-header>\r\n          <ion-card-title>Produtos Mais Vendidos</ion-card-title>\r\n        </ion-card-header>\r\n        <ion-card-content>\r\n          <ion-list lines=\"none\">\r\n            @for(product of topProducts; track product.id) {\r\n              <ion-item>\r\n                <ion-icon name=\"phone-portrait-outline\" slot=\"start\" *ngIf=\"product.type === 'phone'\"></ion-icon>\r\n                <ion-icon name=\"hardware-chip-outline\" slot=\"start\" *ngIf=\"product.type === 'accessory'\"></ion-icon>\r\n                <ion-label>\r\n                  <h3>{{ product.name }}</h3>\r\n                  <p>{{ product.quantity }} unidades vendidas</p>\r\n                </ion-label>\r\n                <ion-note slot=\"end\" color=\"secondary\">{{ product.revenue | currency:'BRL' }}</ion-note>\r\n              </ion-item>\r\n            }\r\n            @empty {\r\n              <ion-item>\r\n                <ion-label>Nenhum produto vendido no período</ion-label>\r\n              </ion-item>\r\n            }\r\n          </ion-list>\r\n        </ion-card-content>\r\n      </ion-card>\r\n    </div>\r\n\r\n    <ion-card class=\"recent-sales-card\">\r\n      <ion-card-header>\r\n        <ion-card-title>Últimas Vendas</ion-card-title>\r\n        <ion-button fill=\"clear\" routerLink=\"/sales\">\r\n          Ver Todas\r\n          <ion-icon name=\"arrow-forward-outline\" slot=\"end\"></ion-icon>\r\n        </ion-button>\r\n      </ion-card-header>\r\n      <ion-card-content>\r\n        <ion-list>\r\n          @for(sale of recentSales; track sale.id) {\r\n            <ion-item button [routerLink]=\"['/sales/details', sale.id]\">\r\n              <ion-label>\r\n                <h2>Venda #{{ sale.id }}</h2>\r\n                <ion-text color=\"medium\">{{ sale.date | date:'dd/MM/yyyy' }}</ion-text>\r\n                <p>Cliente: {{ sale.customer.name }}</p>\r\n                <p>Loja: {{ sale.store.name }}</p>\r\n              </ion-label>\r\n              <div class=\"sale-info\" slot=\"end\">\r\n                <ion-badge [color]=\"getStatusColor(sale.status)\">{{ getStatusLabel(sale.status) }}</ion-badge>\r\n                <ion-text color=\"dark\">\r\n                  <h3>{{ sale.totalValue | currency:'BRL' }}</h3>\r\n                </ion-text>\r\n              </div>\r\n            </ion-item>\r\n          }\r\n          @empty {\r\n            <ion-item>\r\n              <ion-label>Nenhuma venda recente</ion-label>\r\n            </ion-item>\r\n          }\r\n        </ion-list>\r\n      </ion-card-content>\r\n    </ion-card>\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;ICmEgBA,EAAA,CAAAC,SAAA,mBAAiG;;;;;IACjGD,EAAA,CAAAC,SAAA,mBAAoG;;;;;IAFtGD,EAAA,CAAAE,cAAA,eAAU;IAERF,EADA,CAAAG,UAAA,IAAAC,mCAAA,uBAAsF,IAAAC,mCAAA,uBACG;IAEvFL,EADF,CAAAE,cAAA,gBAAW,SACL;IAAAF,EAAA,CAAAM,MAAA,GAAkB;IAAAN,EAAA,CAAAO,YAAA,EAAK;IAC3BP,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAM,MAAA,GAAwC;IAC7CN,EAD6C,CAAAO,YAAA,EAAI,EACrC;IACZP,EAAA,CAAAE,cAAA,mBAAuC;IAAAF,EAAA,CAAAM,MAAA,GAAsC;;IAC/EN,EAD+E,CAAAO,YAAA,EAAW,EAC/E;;;;IAP6CP,EAAA,CAAAQ,SAAA,EAA8B;IAA9BR,EAAA,CAAAS,UAAA,SAAAC,UAAA,CAAAC,IAAA,aAA8B;IAC/BX,EAAA,CAAAQ,SAAA,EAAkC;IAAlCR,EAAA,CAAAS,UAAA,SAAAC,UAAA,CAAAC,IAAA,iBAAkC;IAEjFX,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAAY,iBAAA,CAAAF,UAAA,CAAAG,IAAA,CAAkB;IACnBb,EAAA,CAAAQ,SAAA,GAAwC;IAAxCR,EAAA,CAAAc,kBAAA,KAAAJ,UAAA,CAAAK,QAAA,uBAAwC;IAENf,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAgB,WAAA,QAAAN,UAAA,CAAAO,OAAA,SAAsC;;;;;IAK7EjB,EADF,CAAAE,cAAA,eAAU,gBACG;IAAAF,EAAA,CAAAM,MAAA,6CAAiC;IAC9CN,EAD8C,CAAAO,YAAA,EAAY,EAC/C;;;;;IAoBTP,EAFJ,CAAAE,cAAA,mBAA4D,gBAC/C,SACL;IAAAF,EAAA,CAAAM,MAAA,GAAoB;IAAAN,EAAA,CAAAO,YAAA,EAAK;IAC7BP,EAAA,CAAAE,cAAA,mBAAyB;IAAAF,EAAA,CAAAM,MAAA,GAAmC;;IAAAN,EAAA,CAAAO,YAAA,EAAW;IACvEP,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAM,MAAA,GAAiC;IAAAN,EAAA,CAAAO,YAAA,EAAI;IACxCP,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAM,MAAA,IAA2B;IAChCN,EADgC,CAAAO,YAAA,EAAI,EACxB;IAEVP,EADF,CAAAE,cAAA,eAAkC,qBACiB;IAAAF,EAAA,CAAAM,MAAA,IAAiC;IAAAN,EAAA,CAAAO,YAAA,EAAY;IAE5FP,EADF,CAAAE,cAAA,oBAAuB,UACjB;IAAAF,EAAA,CAAAM,MAAA,IAAsC;;IAGhDN,EAHgD,CAAAO,YAAA,EAAK,EACtC,EACP,EACG;;;;;IAbMP,EAAA,CAAAS,UAAA,eAAAT,EAAA,CAAAkB,eAAA,KAAAC,GAAA,EAAAC,OAAA,CAAAC,EAAA,EAA0C;IAEnDrB,EAAA,CAAAQ,SAAA,GAAoB;IAApBR,EAAA,CAAAc,kBAAA,YAAAM,OAAA,CAAAC,EAAA,KAAoB;IACCrB,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAgB,WAAA,OAAAI,OAAA,CAAAE,IAAA,gBAAmC;IACzDtB,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAc,kBAAA,cAAAM,OAAA,CAAAG,QAAA,CAAAV,IAAA,KAAiC;IACjCb,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAc,kBAAA,WAAAM,OAAA,CAAAI,KAAA,CAAAX,IAAA,KAA2B;IAGnBb,EAAA,CAAAQ,SAAA,GAAqC;IAArCR,EAAA,CAAAS,UAAA,UAAAgB,MAAA,CAAAC,cAAA,CAAAN,OAAA,CAAAO,MAAA,EAAqC;IAAC3B,EAAA,CAAAQ,SAAA,EAAiC;IAAjCR,EAAA,CAAAY,iBAAA,CAAAa,MAAA,CAAAG,cAAA,CAAAR,OAAA,CAAAO,MAAA,EAAiC;IAE5E3B,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAgB,WAAA,SAAAI,OAAA,CAAAS,UAAA,SAAsC;;;;;IAO9C7B,EADF,CAAAE,cAAA,eAAU,gBACG;IAAAF,EAAA,CAAAM,MAAA,4BAAqB;IAClCN,EADkC,CAAAO,YAAA,EAAY,EACnC;;;ADjGvB,OAAM,MAAOuB,QAAQ;EAanBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAZ/B,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,cAAc,GAAW,CAAC;IAC1B,KAAAC,aAAa,GAAW,CAAC;IAEzB,KAAAC,eAAe,GAAW,CAAC;IAC3B,KAAAC,cAAc,GAAW,CAAC;IAC1B,KAAAC,aAAa,GAAW,CAAC;IAEzB,KAAAC,WAAW,GAAiB,EAAE;IAE9B,KAAAC,WAAW,GAAW,EAAE;EAEuB;EAE/CC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACD,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACf,IAAI,CAACV,WAAW,CAACY,MAAM,EAAE,CAACC,SAAS,CAAC;MAClCC,IAAI,EAAGC,KAAK,IAAI;QACd,MAAMC,cAAc,GAAGD,KAAK,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACvB,MAAM,KAAK,WAAW,CAAC;QAExE,IAAI,CAACwB,yBAAyB,CAACH,cAAc,CAAC;QAE9C,IAAI,CAACI,mBAAmB,CAACJ,cAAc,CAAC;QAExC,IAAI,CAACK,oBAAoB,CAACL,cAAc,CAAC;QAEzC,IAAI,CAACR,WAAW,GAAG,CAAC,GAAGO,KAAK,CAAC,CAC1BO,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAAClC,IAAI,CAAC,CAACoC,OAAO,EAAE,GAAG,IAAID,IAAI,CAACF,CAAC,CAACjC,IAAI,CAAC,CAACoC,OAAO,EAAE,CAAC,CACvEC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC7D;KACD,CAAC;EACJ;EAEAT,yBAAyBA,CAACJ,KAAa;IACrC,IAAI,CAACd,YAAY,GAAGc,KAAK,CAACe,MAAM,CAAC,CAACC,GAAG,EAAEb,IAAI,KAAKa,GAAG,GAAGb,IAAI,CAACrB,UAAU,EAAE,CAAC,CAAC;IAEzE,MAAMmC,GAAG,GAAG,IAAIP,IAAI,EAAE;IACtB,MAAMQ,YAAY,GAAG,IAAIR,IAAI,CAACO,GAAG,CAACE,WAAW,EAAE,EAAEF,GAAG,CAACG,QAAQ,EAAE,EAAE,CAAC,CAAC;IACnE,MAAMC,WAAW,GAAG,IAAIX,IAAI,CAACO,GAAG,CAAC;IACjCI,WAAW,CAACC,OAAO,CAACL,GAAG,CAACM,OAAO,EAAE,GAAGN,GAAG,CAACO,MAAM,EAAE,CAAC;IAEjD,IAAI,CAACrC,cAAc,GAAGa,KAAK,CACxBE,MAAM,CAACC,IAAI,IAAI,IAAIO,IAAI,CAACP,IAAI,CAAC5B,IAAI,CAAC,IAAI2C,YAAY,CAAC,CACnDH,MAAM,CAAC,CAACC,GAAG,EAAEb,IAAI,KAAKa,GAAG,GAAGb,IAAI,CAACrB,UAAU,EAAE,CAAC,CAAC;IAElD,IAAI,CAACM,aAAa,GAAGY,KAAK,CACvBE,MAAM,CAACC,IAAI,IAAI,IAAIO,IAAI,CAACP,IAAI,CAAC5B,IAAI,CAAC,IAAI8C,WAAW,CAAC,CAClDN,MAAM,CAAC,CAACC,GAAG,EAAEb,IAAI,KAAKa,GAAG,GAAGb,IAAI,CAACrB,UAAU,EAAE,CAAC,CAAC;EACpD;EAEAuB,mBAAmBA,CAACL,KAAa;IAC/B,MAAMiB,GAAG,GAAG,IAAIP,IAAI,EAAE;IACtB,MAAMQ,YAAY,GAAG,IAAIR,IAAI,CAACO,GAAG,CAACE,WAAW,EAAE,EAAEF,GAAG,CAACG,QAAQ,EAAE,EAAE,CAAC,CAAC;IACnE,MAAMC,WAAW,GAAG,IAAIX,IAAI,CAACO,GAAG,CAAC;IACjCI,WAAW,CAACC,OAAO,CAACL,GAAG,CAACM,OAAO,EAAE,GAAGN,GAAG,CAACO,MAAM,EAAE,CAAC;IAEjD,MAAMC,UAAU,GAAGzB,KAAK,CAACE,MAAM,CAACC,IAAI,IAAI,IAAIO,IAAI,CAACP,IAAI,CAAC5B,IAAI,CAAC,IAAI2C,YAAY,CAAC;IAC5E,IAAI,CAAC7B,eAAe,GAAGoC,UAAU,CAACC,MAAM;IAExC,MAAMC,SAAS,GAAG3B,KAAK,CAACE,MAAM,CAACC,IAAI,IAAI,IAAIO,IAAI,CAACP,IAAI,CAAC5B,IAAI,CAAC,IAAI8C,WAAW,CAAC;IAC1E,IAAI,CAAC/B,cAAc,GAAGqC,SAAS,CAACD,MAAM;IAEtC;IACA,IAAI,CAACnC,aAAa,GAAGS,KAAK,CAAC0B,MAAM,GAAG,CAAC,GAAG,IAAI,CAACxC,YAAY,GAAGc,KAAK,CAAC0B,MAAM,GAAG,CAAC;EAC9E;EAEApB,oBAAoBA,CAACN,KAAa;IAChC,MAAM4B,UAAU,GAAG,IAAIC,GAAG,EAAsB;IAEhD7B,KAAK,CAAC8B,OAAO,CAAC3B,IAAI,IAAG;MACnBA,IAAI,CAAC4B,KAAK,CAACD,OAAO,CAACE,IAAI,IAAG;QAAA,IAAAC,gBAAA;QACxB,MAAMC,SAAS,GAAG,EAAAD,gBAAA,GAAAD,IAAI,CAACG,OAAO,CAAC7D,EAAE,cAAA2D,gBAAA,uBAAfA,gBAAA,CAAiBG,QAAQ,EAAE,KAAI,EAAE;QACnD,MAAMC,WAAW,GAAG,IAAI,CAACC,cAAc,CAACN,IAAI,CAACG,OAAO,EAAEH,IAAI,CAACO,WAAW,CAAC;QACvE,MAAMA,WAAW,GAAGP,IAAI,CAACO,WAAoC;QAC7D,MAAMvE,QAAQ,GAAGgE,IAAI,CAAChE,QAAQ;QAC9B,MAAME,OAAO,GAAG8D,IAAI,CAACQ,QAAQ;QAE7B,IAAIZ,UAAU,CAACa,GAAG,CAACP,SAAS,CAAC,EAAE;UAC7B,MAAMQ,eAAe,GAAGd,UAAU,CAACe,GAAG,CAACT,SAAS,CAAE;UAClDQ,eAAe,CAAC1E,QAAQ,IAAIA,QAAQ;UACpC0E,eAAe,CAACxE,OAAO,IAAIA,OAAO;QACpC,CAAC,MAAM;UACL0D,UAAU,CAACgB,GAAG,CAACV,SAAS,EAAE;YACxB5D,EAAE,EAAE4D,SAAS;YACbpE,IAAI,EAAEuE,WAAW;YACjBzE,IAAI,EAAE2E,WAAW;YACjBvE,QAAQ,EAAEA,QAAQ;YAClBE,OAAO,EAAEA;WACV,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAI,CAACsB,WAAW,GAAGqD,KAAK,CAACC,IAAI,CAAClB,UAAU,CAACmB,MAAM,EAAE,CAAC,CAC/CxC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACzC,QAAQ,GAAGwC,CAAC,CAACxC,QAAQ,CAAC,CACvC4C,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEA0B,cAAcA,CAACH,OAAY,EAAEvE,IAAY;IACvC,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,OAAOuE,OAAO,CAACa,KAAK,IAAI,SAAS;IACnC,CAAC,MAAM;MACL,OAAOb,OAAO,CAACrE,IAAI,IAAI,WAAW;IACpC;EACF;EAEAe,cAAcA,CAACD,MAAc;IAC3B,MAAMqE,SAAS,GAA4B;MACzC,SAAS,EAAE,UAAU;MACrB,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE;KACb;IACD,OAAOA,SAAS,CAACrE,MAAM,CAAC,IAAIA,MAAM;EACpC;EAEAD,cAAcA,CAACC,MAAc;IAC3B,MAAMsE,QAAQ,GAA4B;MACxC,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE;KACb;IACD,OAAOA,QAAQ,CAACtE,MAAM,CAAC,IAAI,QAAQ;EACrC;;YArIWG,QAAQ;;mCAARA,SAAQ,EAAA9B,EAAA,CAAAkG,iBAAA,CAAAC,EAAA,CAAAC,WAAA;AAAA;;QAARtE,SAAQ;EAAAuE,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MChBjB5G,EAFJ,CAAAE,cAAA,oBAAiC,qBACA,qBACH;MACxBF,EAAA,CAAAC,SAAA,sBAAmC;MACrCD,EAAA,CAAAO,YAAA,EAAc;MACdP,EAAA,CAAAE,cAAA,gBAAW;MAAAF,EAAA,CAAAM,MAAA,gBAAS;MAExBN,EAFwB,CAAAO,YAAA,EAAY,EACpB,EACH;MAMLP,EAJR,CAAAE,cAAA,qBAAiC,aACE,kBACA,sBACZ,sBACC;MAAAF,EAAA,CAAAM,MAAA,yBAAiB;MACnCN,EADmC,CAAAO,YAAA,EAAiB,EAClC;MAIZP,EAHN,CAAAE,cAAA,wBAAkB,cACe,cACH,UACpB;MAAAF,EAAA,CAAAM,MAAA,IAAmC;;MAAAN,EAAA,CAAAO,YAAA,EAAK;MAC5CP,EAAA,CAAAE,cAAA,SAAG;MAAAF,EAAA,CAAAM,MAAA,yBAAiB;MACtBN,EADsB,CAAAO,YAAA,EAAI,EACpB;MAEJP,EADF,CAAAE,cAAA,cAA0B,UACpB;MAAAF,EAAA,CAAAM,MAAA,IAAqC;;MAAAN,EAAA,CAAAO,YAAA,EAAK;MAC9CP,EAAA,CAAAE,cAAA,SAAG;MAAAF,EAAA,CAAAM,MAAA,+BAAkB;MACvBN,EADuB,CAAAO,YAAA,EAAI,EACrB;MAEJP,EADF,CAAAE,cAAA,cAA0B,UACpB;MAAAF,EAAA,CAAAM,MAAA,IAAoC;;MAAAN,EAAA,CAAAO,YAAA,EAAK;MAC7CP,EAAA,CAAAE,cAAA,SAAG;MAAAF,EAAA,CAAAM,MAAA,6BAAqB;MAIhCN,EAJgC,CAAAO,YAAA,EAAI,EACxB,EACF,EACW,EACV;MAKLP,EAHN,CAAAE,cAAA,cAA6B,mBACE,uBACV,sBACC;MAAAF,EAAA,CAAAM,MAAA,cAAM;MACxBN,EADwB,CAAAO,YAAA,EAAiB,EACvB;MAGdP,EAFJ,CAAAE,cAAA,wBAAkB,eACQ,eACE;MACtBF,EAAA,CAAAC,SAAA,oBAA+D;MAC/DD,EAAA,CAAAE,cAAA,UAAI;MAAAF,EAAA,CAAAM,MAAA,IAAqB;MAAAN,EAAA,CAAAO,YAAA,EAAK;MAC9BP,EAAA,CAAAE,cAAA,SAAG;MAAAF,EAAA,CAAAM,MAAA,0BAAa;MAClBN,EADkB,CAAAO,YAAA,EAAI,EAChB;MACNP,EAAA,CAAAE,cAAA,eAAwB;MACtBF,EAAA,CAAAC,SAAA,oBAA4D;MAC5DD,EAAA,CAAAE,cAAA,UAAI;MAAAF,EAAA,CAAAM,MAAA,IAAoB;MAAAN,EAAA,CAAAO,YAAA,EAAK;MAC7BP,EAAA,CAAAE,cAAA,SAAG;MAAAF,EAAA,CAAAM,MAAA,wBAAgB;MACrBN,EADqB,CAAAO,YAAA,EAAI,EACnB;MACNP,EAAA,CAAAE,cAAA,eAAwB;MACtBF,EAAA,CAAAC,SAAA,oBAAkE;MAClED,EAAA,CAAAE,cAAA,UAAI;MAAAF,EAAA,CAAAM,MAAA,IAAoC;;MAAAN,EAAA,CAAAO,YAAA,EAAK;MAC7CP,EAAA,CAAAE,cAAA,SAAG;MAAAF,EAAA,CAAAM,MAAA,yBAAY;MAIvBN,EAJuB,CAAAO,YAAA,EAAI,EACf,EACF,EACW,EACV;MAIPP,EAFJ,CAAAE,cAAA,mBAA6B,uBACV,sBACC;MAAAF,EAAA,CAAAM,MAAA,8BAAsB;MACxCN,EADwC,CAAAO,YAAA,EAAiB,EACvC;MAEhBP,EADF,CAAAE,cAAA,wBAAkB,oBACO;MACrBF,EAAA,CAAA8G,gBAAA,KAAAC,wBAAA,2BAAAC,UAAA,SAAAC,6BAAA,mBAeC;MAITjH,EAHM,CAAAO,YAAA,EAAW,EACM,EACV,EACP;MAIFP,EAFJ,CAAAE,cAAA,oBAAoC,uBACjB,sBACC;MAAAF,EAAA,CAAAM,MAAA,2BAAc;MAAAN,EAAA,CAAAO,YAAA,EAAiB;MAC/CP,EAAA,CAAAE,cAAA,sBAA6C;MAC3CF,EAAA,CAAAM,MAAA,mBACA;MAAAN,EAAA,CAAAC,SAAA,oBAA6D;MAEjED,EADE,CAAAO,YAAA,EAAa,EACG;MAEhBP,EADF,CAAAE,cAAA,wBAAkB,gBACN;MACRF,EAAA,CAAA8G,gBAAA,KAAAI,wBAAA,0BAAAF,UAAA,SAAAG,6BAAA,mBAoBC;MAKXnH,EAJQ,CAAAO,YAAA,EAAW,EACM,EACV,EACP,EACM;;;MAzHFP,EAAA,CAAAS,UAAA,qBAAoB;MASnBT,EAAA,CAAAQ,SAAA,GAAmB;MAAnBR,EAAA,CAAAS,UAAA,oBAAmB;MAShBT,EAAA,CAAAQ,SAAA,IAAmC;MAAnCR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAgB,WAAA,SAAA6F,GAAA,CAAA5E,YAAA,SAAmC;MAInCjC,EAAA,CAAAQ,SAAA,GAAqC;MAArCR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAgB,WAAA,SAAA6F,GAAA,CAAA3E,cAAA,SAAqC;MAIrClC,EAAA,CAAAQ,SAAA,GAAoC;MAApCR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAgB,WAAA,SAAA6F,GAAA,CAAA1E,aAAA,SAAoC;MAgBlCnC,EAAA,CAAAQ,SAAA,IAAqB;MAArBR,EAAA,CAAAY,iBAAA,CAAAiG,GAAA,CAAAzE,eAAA,CAAqB;MAKrBpC,EAAA,CAAAQ,SAAA,GAAoB;MAApBR,EAAA,CAAAY,iBAAA,CAAAiG,GAAA,CAAAxE,cAAA,CAAoB;MAKpBrC,EAAA,CAAAQ,SAAA,GAAoC;MAApCR,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAgB,WAAA,SAAA6F,GAAA,CAAAvE,aAAA,SAAoC;MAa1CtC,EAAA,CAAAQ,SAAA,IAeC;MAfDR,EAAA,CAAAoH,UAAA,CAAAP,GAAA,CAAAtE,WAAA,CAeC;MAgBHvC,EAAA,CAAAQ,SAAA,IAoBC;MApBDR,EAAA,CAAAoH,UAAA,CAAAP,GAAA,CAAArE,WAAA,CAoBC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}