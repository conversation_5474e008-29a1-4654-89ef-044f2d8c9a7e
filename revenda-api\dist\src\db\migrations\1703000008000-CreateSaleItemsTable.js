"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSaleItemsTable1703000008000 = void 0;
const typeorm_1 = require("typeorm");
class CreateSaleItemsTable1703000008000 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'sale_items',
            columns: [
                {
                    name: 'id',
                    type: 'int',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'increment',
                },
                {
                    name: 'sale_id',
                    type: 'int',
                },
                {
                    name: 'product_id',
                    type: 'int',
                },
                {
                    name: 'productType',
                    type: 'enum',
                    enum: ['phone', 'accessory'],
                },
                {
                    name: 'quantity',
                    type: 'int',
                },
                {
                    name: 'unitPrice',
                    type: 'decimal',
                    precision: 10,
                    scale: 2,
                },
                {
                    name: 'subtotal',
                    type: 'decimal',
                    precision: 10,
                    scale: 2,
                },
            ],
        }), true);
        await queryRunner.createForeignKey('sale_items', new typeorm_1.TableForeignKey({
            columnNames: ['sale_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'sales',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('sale_items');
    }
}
exports.CreateSaleItemsTable1703000008000 = CreateSaleItemsTable1703000008000;
//# sourceMappingURL=1703000008000-CreateSaleItemsTable.js.map