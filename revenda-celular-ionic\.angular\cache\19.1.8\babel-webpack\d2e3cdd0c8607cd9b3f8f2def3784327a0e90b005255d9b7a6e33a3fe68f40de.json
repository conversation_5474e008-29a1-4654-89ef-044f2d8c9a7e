{"ast": null, "code": "var _PhoneFormComponent;\nimport { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { dateMask, maskitoElement, parseDateMask, formatDateMask } from '../../core/constants/mask.constants';\nimport { ApplicationValidators } from '../../core/validators/url.validator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/phone.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../brands/services/brand.service\";\nimport * as i4 from \"@ionic/angular\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@maskito/angular\";\nfunction PhoneFormComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction PhoneFormComponent_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00EDnimo 3 caracteres \");\n  }\n}\nfunction PhoneFormComponent_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo deve ter no m\\u00E1ximo 150 caracteres \");\n  }\n}\nfunction PhoneFormComponent_Conditional_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Modelo n\\u00E3o pode conter palavras como 'teste' ou 'exemplo' \");\n  }\n}\nfunction PhoneFormComponent_Conditional_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction PhoneFormComponent_Conditional_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo de imagem deve ser uma URL v\\u00E1lida \");\n  }\n}\nfunction PhoneFormComponent_Conditional_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Data de lan\\u00E7amento n\\u00E3o pode ser no futuro \");\n  }\n}\nfunction PhoneFormComponent_Conditional_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Data de lan\\u00E7amento deve ser posterior ao ano 2000 \");\n  }\n}\nfunction PhoneFormComponent_Conditional_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction PhoneFormComponent_Conditional_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O pre\\u00E7o deve ser maior ou igual a zero \");\n  }\n}\nfunction PhoneFormComponent_Conditional_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Pre\\u00E7o n\\u00E3o pode ser superior a R$ 50.000 \");\n  }\n}\nfunction PhoneFormComponent_For_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(category_r1);\n  }\n}\nfunction PhoneFormComponent_Conditional_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nfunction PhoneFormComponent_For_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-select-option\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", brand_r2.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(brand_r2.name);\n  }\n}\nfunction PhoneFormComponent_Conditional_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" O campo \\u00E9 obrigat\\u00F3rio \");\n  }\n}\nexport class PhoneFormComponent {\n  constructor(phoneService, router, activatedRoute, brandService, toastController) {\n    this.phoneService = phoneService;\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    this.brandService = brandService;\n    this.toastController = toastController;\n    this.dateMask = dateMask;\n    this.maskitoElement = maskitoElement;\n    // Lista de categorias predefinidas em português do Brasil\n    this.categories = ['Smartphone', 'Celular Básico', 'Premium', 'Intermediário', 'Entrada', 'Gamer', 'Corporativo', 'Resistente'];\n    this.phoneForm = new FormGroup({\n      model: new FormControl('', [Validators.required, Validators.minLength(3), Validators.maxLength(150), this.modelValidator]),\n      image: new FormControl('', [Validators.required, ApplicationValidators.urlValidator]),\n      releaseDate: new FormControl('', [this.releaseDateValidator]),\n      price: new FormControl(0, [Validators.required, Validators.min(0), this.priceValidator]),\n      category: new FormControl('', Validators.required),\n      brandId: new FormControl(null, Validators.required)\n    });\n    this.brands = [];\n    const phoneId = this.activatedRoute.snapshot.params['id'];\n    if (phoneId) {\n      this.phoneService.getById(+phoneId).subscribe({\n        next: phone => {\n          if (phone) {\n            this.phoneId = +phoneId;\n            if (phone.releaseDate instanceof Date) {\n              phone.releaseDate = formatDateMask(phone.releaseDate);\n            }\n            if (typeof phone.releaseDate === 'string') {\n              const parsedDate = parseDateMask(phone.releaseDate, 'yyyy/mm/dd');\n              if (parsedDate) {\n                phone.releaseDate = formatDateMask(parsedDate);\n              }\n            }\n            // Tratar preço como number\n            let priceValue = 0;\n            if (phone.price) {\n              if (typeof phone.price === 'number') {\n                priceValue = phone.price;\n              } else if (typeof phone.price === 'string') {\n                priceValue = parseFloat(phone.price) || 0;\n              }\n            }\n            // Usar brandId em vez de brands\n            this.phoneForm.patchValue({\n              model: phone.model,\n              image: phone.image,\n              releaseDate: phone.releaseDate,\n              price: priceValue,\n              category: phone.category,\n              brandId: phone.brandId\n            });\n          }\n        },\n        error: error => {\n          alert('Erro ao carregar o celular com id ' + phoneId);\n          console.error(error);\n        }\n      });\n    }\n  }\n  ngOnInit() {\n    this.brandService.getBrands().subscribe({\n      next: data => {\n        console.log('brands: ', data);\n        this.brands = data;\n      },\n      error: error => {\n        alert('Erro ao carregar marcas.');\n        console.error(error);\n      }\n    });\n  }\n  compareWith(o1, o2) {\n    return o1 === o2;\n  }\n  save() {\n    let {\n      value\n    } = this.phoneForm;\n    if (value.releaseDate) {\n      const parsedDate = parseDateMask(value.releaseDate);\n      if (parsedDate) {\n        value.releaseDate = parsedDate;\n      }\n    }\n    // Garantir que o preço seja number\n    value.price = Number(value.price) || 0;\n    // Garantir que brandId seja number\n    if (value.brandId) {\n      value.brandId = +value.brandId;\n    }\n    console.log(value);\n    this.phoneService.save({\n      ...value,\n      id: this.phoneId\n    }).subscribe({\n      next: () => {\n        this.toastController.create({\n          message: 'Celular salvo com sucesso!',\n          duration: 3000\n        }).then(toast => toast.present());\n        this.router.navigate(['/phones']);\n      },\n      error: error => {\n        var _error$error;\n        let errorMessage = 'Erro ao salvar o celular ' + value.model + '!';\n        if ((_error$error = error.error) !== null && _error$error !== void 0 && _error$error.message) {\n          errorMessage = error.error.message;\n        }\n        alert(errorMessage);\n        console.error(error);\n      }\n    });\n  }\n  hasError(field, error) {\n    var _formControl$errors;\n    const formControl = this.phoneForm.get(field);\n    return !!(formControl !== null && formControl !== void 0 && formControl.touched) && !!(formControl !== null && formControl !== void 0 && (_formControl$errors = formControl.errors) !== null && _formControl$errors !== void 0 && _formControl$errors[error]);\n  }\n  // Validators customizados\n  modelValidator(control) {\n    if (!control.value) return null;\n    const model = control.value.toLowerCase();\n    const forbiddenWords = ['teste', 'test', 'exemplo'];\n    if (forbiddenWords.some(word => model.includes(word))) {\n      return {\n        invalidModel: true\n      };\n    }\n    return null;\n  }\n  releaseDateValidator(control) {\n    if (!control.value) return null;\n    const releaseDate = new Date(control.value);\n    const currentDate = new Date();\n    const minDate = new Date('2000-01-01');\n    if (releaseDate > currentDate) {\n      return {\n        futureDate: true\n      };\n    }\n    if (releaseDate < minDate) {\n      return {\n        tooOld: true\n      };\n    }\n    return null;\n  }\n  priceValidator(control) {\n    if (!control.value) return null;\n    const price = +control.value;\n    if (price > 50000) {\n      return {\n        tooExpensive: true\n      };\n    }\n    return null;\n  }\n}\n_PhoneFormComponent = PhoneFormComponent;\n_PhoneFormComponent.ɵfac = function PhoneFormComponent_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _PhoneFormComponent)(i0.ɵɵdirectiveInject(i1.PhoneService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.BrandService), i0.ɵɵdirectiveInject(i4.ToastController));\n};\n_PhoneFormComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _PhoneFormComponent,\n  selectors: [[\"app-phone-form\"]],\n  standalone: false,\n  decls: 48,\n  vars: 19,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [1, \"form-container\"], [3, \"formGroup\"], [\"formControlName\", \"model\", \"labelPlacement\", \"floating\", \"label\", \"Modelo: \", \"type\", \"text\"], [\"formControlName\", \"image\", \"labelPlacement\", \"floating\", \"label\", \"Imagem (URL)\", \"type\", \"url\"], [\"formControlName\", \"releaseDate\", \"labelPlacement\", \"floating\", \"label\", \"Lan\\u00E7amento\", 3, \"maskito\", \"maskitoElement\"], [\"formControlName\", \"price\", \"labelPlacement\", \"floating\", \"label\", \"Pre\\u00E7o (R$)\", \"type\", \"number\", \"step\", \"0.01\", \"min\", \"0\"], [\"formControlName\", \"category\", \"labelPlacement\", \"floating\", \"label\", \"Categoria\"], [3, \"value\"], [\"formControlName\", \"brandId\", \"label\", \"Marca\", \"label-placement\", \"floating\", 3, \"compareWith\"], [\"vertical\", \"bottom\", \"horizontal\", \"end\", \"slot\", \"fixed\"], [3, \"click\", \"disabled\"], [\"name\", \"checkmark\"]],\n  template: function PhoneFormComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-menu-button\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5, \"Cadastro de Celulares\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\")(7, \"div\", 3)(8, \"form\", 4)(9, \"ion-list\")(10, \"ion-item\");\n      i0.ɵɵelement(11, \"ion-input\", 5);\n      i0.ɵɵelementStart(12, \"p\");\n      i0.ɵɵtemplate(13, PhoneFormComponent_Conditional_13_Template, 1, 0)(14, PhoneFormComponent_Conditional_14_Template, 1, 0)(15, PhoneFormComponent_Conditional_15_Template, 1, 0)(16, PhoneFormComponent_Conditional_16_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(17, \"ion-item\");\n      i0.ɵɵelement(18, \"ion-input\", 6);\n      i0.ɵɵelementStart(19, \"p\");\n      i0.ɵɵtemplate(20, PhoneFormComponent_Conditional_20_Template, 1, 0)(21, PhoneFormComponent_Conditional_21_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(22, \"ion-item\");\n      i0.ɵɵelement(23, \"ion-input\", 7);\n      i0.ɵɵelementStart(24, \"p\");\n      i0.ɵɵtemplate(25, PhoneFormComponent_Conditional_25_Template, 1, 0)(26, PhoneFormComponent_Conditional_26_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(27, \"ion-item\");\n      i0.ɵɵelement(28, \"ion-input\", 8);\n      i0.ɵɵelementStart(29, \"p\");\n      i0.ɵɵtemplate(30, PhoneFormComponent_Conditional_30_Template, 1, 0)(31, PhoneFormComponent_Conditional_31_Template, 1, 0)(32, PhoneFormComponent_Conditional_32_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(33, \"ion-item\")(34, \"ion-select\", 9);\n      i0.ɵɵrepeaterCreate(35, PhoneFormComponent_For_36_Template, 2, 2, \"ion-select-option\", 10, i0.ɵɵrepeaterTrackByIdentity);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(37, \"p\");\n      i0.ɵɵtemplate(38, PhoneFormComponent_Conditional_38_Template, 1, 0);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(39, \"ion-item\")(40, \"ion-select\", 11);\n      i0.ɵɵrepeaterCreate(41, PhoneFormComponent_For_42_Template, 2, 2, \"ion-select-option\", 10, i0.ɵɵrepeaterTrackByIdentity);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"p\");\n      i0.ɵɵtemplate(44, PhoneFormComponent_Conditional_44_Template, 1, 0);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(45, \"ion-fab\", 12)(46, \"ion-fab-button\", 13);\n      i0.ɵɵlistener(\"click\", function PhoneFormComponent_Template_ion_fab_button_click_46_listener() {\n        return ctx.save();\n      });\n      i0.ɵɵelement(47, \"ion-icon\", 14);\n      i0.ɵɵelementEnd()()()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(8);\n      i0.ɵɵproperty(\"formGroup\", ctx.phoneForm);\n      i0.ɵɵadvance(5);\n      i0.ɵɵconditional(ctx.hasError(\"model\", \"required\") ? 13 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"model\", \"minlength\") ? 14 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"model\", \"maxlength\") ? 15 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"model\", \"invalidModel\") ? 16 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"image\", \"required\") ? 20 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"image\", \"invalidUrl\") ? 21 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"maskito\", ctx.dateMask)(\"maskitoElement\", ctx.maskitoElement);\n      i0.ɵɵadvance(2);\n      i0.ɵɵconditional(ctx.hasError(\"releaseDate\", \"futureDate\") ? 25 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"releaseDate\", \"tooOld\") ? 26 : -1);\n      i0.ɵɵadvance(4);\n      i0.ɵɵconditional(ctx.hasError(\"price\", \"required\") ? 30 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"price\", \"min\") ? 31 : -1);\n      i0.ɵɵadvance();\n      i0.ɵɵconditional(ctx.hasError(\"price\", \"tooExpensive\") ? 32 : -1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵrepeater(ctx.categories);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"category\", \"required\") ? 38 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"compareWith\", ctx.compareWith);\n      i0.ɵɵadvance();\n      i0.ɵɵrepeater(ctx.brands);\n      i0.ɵɵadvance(3);\n      i0.ɵɵconditional(ctx.hasError(\"brandId\", \"required\") ? 44 : -1);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"disabled\", ctx.phoneForm.invalid);\n    }\n  },\n  dependencies: [i5.ɵNgNoValidate, i5.NgControlStatus, i5.NgControlStatusGroup, i4.IonButtons, i4.IonContent, i4.IonFab, i4.IonFabButton, i4.IonHeader, i4.IonIcon, i4.IonInput, i4.IonItem, i4.IonList, i4.IonMenuButton, i4.IonSelect, i4.IonSelectOption, i4.IonTitle, i4.IonToolbar, i4.NumericValueAccessor, i4.SelectValueAccessor, i4.TextValueAccessor, i4.IonMinValidator, i6.MaskitoDirective, i5.FormGroupDirective, i5.FormControlName],\n  styles: [\".form-container[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\nion-item[_ngcontent-%COMP%] {\\n  --padding-start: 0;\\n  margin-bottom: 8px;\\n}\\nion-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: var(--ion-color-danger);\\n  padding-left: 16px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGhvbmVzL3Bob25lLWZvcm0vcGhvbmUtZm9ybS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFBZ0IsZ0JBQUE7RUFDaEIsY0FBQTtBQUVGOztBQURBO0VBQ0Usa0JBQUE7RUFBcUIsa0JBQUE7QUFLdkI7QUFKSTtFQUNBLGVBQUE7RUFBb0IsOEJBQUE7RUFDcEIsa0JBQUE7QUFPSiIsInNvdXJjZXNDb250ZW50IjpbIi5mb3JtLWNvbnRhaW5lciB7XHJcbiAgcGFkZGluZzogMTZweDsgIG1heC13aWR0aDogODAwcHg7XHJcbiAgbWFyZ2luOiAwIGF1dG87fVxyXG5pb24taXRlbSB7XHJcbiAgLS1wYWRkaW5nLXN0YXJ0OiAwOyAgbWFyZ2luLWJvdHRvbTogOHB4O1xyXG4gICAgcCB7XHJcbiAgICBmb250LXNpemU6IDEycHg7ICAgIGNvbG9yOiB2YXIoLS1pb24tY29sb3ItZGFuZ2VyKTtcclxuICAgIHBhZGRpbmctbGVmdDogMTZweDtcclxuICB9XHJcbn1cclxuXHJcblxyXG5cclxuXHJcblxyXG5cclxuXHJcblxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n});", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "dateMask", "maskitoElement", "parseDateMask", "formatDateMask", "ApplicationValidators", "i0", "ɵɵtext", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵproperty", "category_r1", "ɵɵadvance", "ɵɵtextInterpolate", "brand_r2", "id", "name", "PhoneFormComponent", "constructor", "phoneService", "router", "activatedRoute", "brandService", "toastController", "categories", "phoneForm", "model", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "modelValidator", "image", "urlValidator", "releaseDate", "releaseDateValidator", "price", "min", "priceValidator", "category", "brandId", "brands", "phoneId", "snapshot", "params", "getById", "subscribe", "next", "phone", "Date", "parsedDate", "priceValue", "parseFloat", "patchValue", "error", "alert", "console", "ngOnInit", "getBrands", "data", "log", "compareWith", "o1", "o2", "save", "value", "Number", "create", "message", "duration", "then", "toast", "present", "navigate", "_error$error", "errorMessage", "<PERSON><PERSON><PERSON><PERSON>", "field", "_formControl$errors", "formControl", "get", "touched", "errors", "control", "toLowerCase", "forbidden<PERSON><PERSON><PERSON>", "some", "word", "includes", "invalidModel", "currentDate", "minDate", "futureDate", "tooOld", "tooExpensive", "ɵɵdirectiveInject", "i1", "PhoneService", "i2", "Router", "ActivatedRoute", "i3", "BrandService", "i4", "ToastController", "selectors", "standalone", "decls", "vars", "consts", "template", "PhoneFormComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "PhoneFormComponent_Conditional_13_Template", "PhoneFormComponent_Conditional_14_Template", "PhoneFormComponent_Conditional_15_Template", "PhoneFormComponent_Conditional_16_Template", "PhoneFormComponent_Conditional_20_Template", "PhoneFormComponent_Conditional_21_Template", "PhoneFormComponent_Conditional_25_Template", "PhoneFormComponent_Conditional_26_Template", "PhoneFormComponent_Conditional_30_Template", "PhoneFormComponent_Conditional_31_Template", "PhoneFormComponent_Conditional_32_Template", "ɵɵrepeaterCreate", "PhoneFormComponent_For_36_Template", "ɵɵrepeaterTrackByIdentity", "PhoneFormComponent_Conditional_38_Template", "PhoneFormComponent_For_42_Template", "PhoneFormComponent_Conditional_44_Template", "ɵɵlistener", "PhoneFormComponent_Template_ion_fab_button_click_46_listener", "ɵɵconditional", "ɵɵrepeater", "invalid"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\phones\\phone-form\\phone-form.component.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\phones\\phone-form\\phone-form.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { FormControl, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';\r\nimport { dateMask, maskitoElement, parseDateMask, formatDateMask } from '../../core/constants/mask.constants';\r\nimport { ApplicationValidators } from '../../core/validators/url.validator';\r\nimport { PhoneService } from '../services/phone.service';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { BrandService } from '../../brands/services/brand.service';\r\nimport { Brand } from '../../brands/models/brand.type';\r\nimport { ToastController } from '@ionic/angular';\r\n\r\n\r\n@Component({\r\n  selector: 'app-phone-form',\r\n  templateUrl: './phone-form.component.html',\r\n  styleUrls: ['./phone-form.component.scss'],\r\n  standalone: false,\r\n})\r\nexport class PhoneFormComponent implements OnInit {\r\n\r\n  dateMask = dateMask;\r\n  maskitoElement = maskitoElement;\r\n  \r\n  // Lista de categorias predefinidas em português do Brasil\r\n  categories: string[] = [\r\n    'Smartphone', \r\n    'Celular Básico', \r\n    'Premium', \r\n    'Intermediário', \r\n    'Entrada', \r\n    'Gamer', \r\n    'Corporativo', \r\n    'Resistente'\r\n  ];\r\n\r\n  phoneForm: FormGroup = new FormGroup({\r\n    model: new FormControl('', [\r\n      Validators.required,\r\n      Validators.minLength(3),\r\n      Validators.maxLength(150),\r\n      this.modelValidator\r\n    ]),\r\n    image: new FormControl('', [\r\n      Validators.required,\r\n      ApplicationValidators.urlValidator\r\n    ]),\r\n    releaseDate: new FormControl('', [this.releaseDateValidator]),\r\n    price: new FormControl(0, [\r\n      Validators.required,\r\n      Validators.min(0),\r\n      this.priceValidator\r\n    ]),\r\n    category: new FormControl('', Validators.required),\r\n    brandId: new FormControl(null, Validators.required)\r\n  });\r\n  phoneId!: number;\r\n  brands: Brand[] = []\r\n\r\n  constructor(\r\n    private phoneService: PhoneService,\r\n    private router: Router,\r\n    private activatedRoute: ActivatedRoute,\r\n    private brandService: BrandService,\r\n    private toastController: ToastController\r\n  ) {\r\n    const phoneId = this.activatedRoute.snapshot.params['id'];\r\n    if (phoneId) {\r\n      this.phoneService.getById(+phoneId).subscribe({\r\n        next: (phone) => {\r\n          if (phone) {\r\n            this.phoneId = +phoneId;\r\n            if (phone.releaseDate instanceof Date) {\r\n              phone.releaseDate = formatDateMask(phone.releaseDate);\r\n            }\r\n            if (typeof phone.releaseDate === 'string') {\r\n              const parsedDate = parseDateMask(phone.releaseDate, 'yyyy/mm/dd');\r\n              if (parsedDate) {\r\n                phone.releaseDate = formatDateMask(parsedDate);\r\n              }\r\n            }\r\n            // Tratar preço como number\r\n            let priceValue = 0;\r\n            if (phone.price) {\r\n              if (typeof phone.price === 'number') {\r\n                priceValue = phone.price;\r\n              } else if (typeof phone.price === 'string') {\r\n                priceValue = parseFloat(phone.price) || 0;\r\n              }\r\n            }\r\n\r\n            // Usar brandId em vez de brands\r\n            this.phoneForm.patchValue({\r\n              model: phone.model,\r\n              image: phone.image,\r\n              releaseDate: phone.releaseDate,\r\n              price: priceValue,\r\n              category: phone.category,\r\n              brandId: phone.brandId\r\n            });\r\n          }\r\n        },\r\n        error: (error) => {\r\n          alert('Erro ao carregar o celular com id ' + phoneId)\r\n          console.error(error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n  ngOnInit() {\r\n    this.brandService.getBrands().subscribe({\r\n      next: (data: Brand[]) => {\r\n        console.log('brands: ', data);\r\n        this.brands = data;\r\n      },\r\n      error: (error) => {\r\n        alert('Erro ao carregar marcas.');\r\n        console.error(error)\r\n      }\r\n    });\r\n  }\r\n\r\n  compareWith(o1: number | null, o2: number | null): boolean {\r\n    return o1 === o2;\r\n  }\r\n\r\n\r\n\r\n\r\n  save() {\r\n    let { value } = this.phoneForm;\r\n    if (value.releaseDate) {\r\n      const parsedDate = parseDateMask(value.releaseDate);\r\n      if (parsedDate) {\r\n        value.releaseDate = parsedDate;\r\n      }\r\n    }\r\n    // Garantir que o preço seja number\r\n    value.price = Number(value.price) || 0;\r\n    // Garantir que brandId seja number\r\n    if (value.brandId) {\r\n      value.brandId = +value.brandId;\r\n    }\r\n    console.log(value);\r\n    this.phoneService.save({\r\n      ...value,\r\n      id: this.phoneId\r\n    }).subscribe({\r\n      next: () => {\r\n        this.toastController.create({\r\n          message: 'Celular salvo com sucesso!',\r\n          duration: 3000,\r\n        }).then(toast => toast.present());\r\n        this.router.navigate(['/phones']);\r\n      },\r\n      error: (error) => {\r\n        let errorMessage = 'Erro ao salvar o celular ' + value.model + '!';\r\n        if (error.error?.message) {\r\n          errorMessage = error.error.message;\r\n        }\r\n        alert(errorMessage);\r\n        console.error(error);\r\n      }\r\n    });\r\n  }\r\n\r\n  hasError(field: string, error: string): boolean {\r\n    const formControl = this.phoneForm.get(field);\r\n    return !!formControl?.touched && !!formControl?.errors?.[error];\r\n  }\r\n\r\n  // Validators customizados\r\n  modelValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const model = control.value.toLowerCase();\r\n    const forbiddenWords = ['teste', 'test', 'exemplo'];\r\n\r\n    if (forbiddenWords.some(word => model.includes(word))) {\r\n      return { invalidModel: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  releaseDateValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const releaseDate = new Date(control.value);\r\n    const currentDate = new Date();\r\n    const minDate = new Date('2000-01-01');\r\n\r\n    if (releaseDate > currentDate) {\r\n      return { futureDate: true };\r\n    }\r\n    if (releaseDate < minDate) {\r\n      return { tooOld: true };\r\n    }\r\n    return null;\r\n  }\r\n\r\n  priceValidator(control: AbstractControl): ValidationErrors | null {\r\n    if (!control.value) return null;\r\n\r\n    const price = +control.value;\r\n    if (price > 50000) {\r\n      return { tooExpensive: true };\r\n    }\r\n    return null;\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-menu-button></ion-menu-button>\r\n    </ion-buttons>\r\n    <ion-title>Cadastro de Celulares</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content>\r\n  <div class=\"form-container\">\r\n    <form [formGroup]=\"phoneForm\">\r\n      <ion-list>\r\n        <ion-item>\r\n          <ion-input formControlName=\"model\" labelPlacement=\"floating\" label=\"Modelo: \" type=\"text\"></ion-input>\r\n          <p>\r\n          @if(hasError('model', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          @if(hasError('model', 'minlength')) {\r\n            O campo deve ter no mínimo 3 caracteres\r\n          }\r\n          @if(hasError('model', 'maxlength')) {\r\n            O campo deve ter no máximo 150 caracteres\r\n          }\r\n          @if(hasError('model', 'invalidModel')) {\r\n            Modelo não pode conter palavras como 'teste' ou 'exemplo'\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        <ion-item>\r\n          <ion-input formControlName=\"image\" labelPlacement=\"floating\" label=\"Imagem (URL)\" type=\"url\"></ion-input>\r\n          <p>\r\n          @if(hasError('image', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          @if(hasError('image', 'invalidUrl')) {\r\n            O campo de imagem deve ser uma URL válida\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        <ion-item>\r\n          <ion-input formControlName=\"releaseDate\" labelPlacement=\"floating\" label=\"Lançamento\" [maskito]=\"dateMask\"\r\n            [maskitoElement]=\"maskitoElement\" />\r\n          <p>\r\n          @if(hasError('releaseDate', 'futureDate')) {\r\n            Data de lançamento não pode ser no futuro\r\n          }\r\n          @if(hasError('releaseDate', 'tooOld')) {\r\n            Data de lançamento deve ser posterior ao ano 2000\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        <ion-item>\r\n          <ion-input formControlName=\"price\" labelPlacement=\"floating\" label=\"Preço (R$)\" type=\"number\" step=\"0.01\" min=\"0\" />\r\n          <p>\r\n          @if(hasError('price', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          @if(hasError('price', 'min')) {\r\n            O preço deve ser maior ou igual a zero\r\n          }\r\n          @if(hasError('price', 'tooExpensive')) {\r\n            Preço não pode ser superior a R$ 50.000\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        <ion-item>\r\n          <ion-select formControlName=\"category\" labelPlacement=\"floating\" label=\"Categoria\">\r\n            @for(category of categories; track category) {\r\n              <ion-select-option [value]=\"category\">{{ category }}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n          @if(hasError('category', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          </p>\r\n        </ion-item>\r\n        <ion-item>\r\n          <ion-select formControlName=\"brandId\" [compareWith]=\"compareWith\" label=\"Marca\" label-placement=\"floating\">\r\n            @for(brand of brands; track brand) {\r\n              <ion-select-option [value]=\"brand.id\">{{brand.name}}</ion-select-option>\r\n            }\r\n          </ion-select>\r\n          <p>\r\n          @if(hasError('brandId', 'required')) {\r\n            O campo é obrigatório\r\n          }\r\n          </p>\r\n        </ion-item>\r\n      </ion-list>\r\n      <ion-fab vertical=\"bottom\" horizontal=\"end\" slot=\"fixed\">\r\n        <ion-fab-button [disabled]=\"phoneForm.invalid\" (click)=\"save()\">\r\n          <ion-icon name=\"checkmark\"></ion-icon>\r\n        </ion-fab-button>\r\n      </ion-fab>\r\n    </form>\r\n  </div>\r\n</ion-content>\r\n"], "mappings": ";AACA,SAASA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAA2C,gBAAgB;AACtG,SAASC,QAAQ,EAAEC,cAAc,EAAEC,aAAa,EAAEC,cAAc,QAAQ,qCAAqC;AAC7G,SAASC,qBAAqB,QAAQ,qCAAqC;;;;;;;;;;ICc/DC,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,qDACF;;;;;IAEED,EAAA,CAAAC,MAAA,uDACF;;;;;IAEED,EAAA,CAAAC,MAAA,uEACF;;;;;IAOED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,uDACF;;;;;IAQED,EAAA,CAAAC,MAAA,4DACF;;;;;IAEED,EAAA,CAAAC,MAAA,+DACF;;;;;IAOED,EAAA,CAAAC,MAAA,wCACF;;;;;IAEED,EAAA,CAAAC,MAAA,oDACF;;;;;IAEED,EAAA,CAAAC,MAAA,0DACF;;;;;IAMID,EAAA,CAAAE,cAAA,4BAAsC;IAAAF,EAAA,CAAAC,MAAA,GAAc;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAArDH,EAAA,CAAAI,UAAA,UAAAC,WAAA,CAAkB;IAACL,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,iBAAA,CAAAF,WAAA,CAAc;;;;;IAKtDL,EAAA,CAAAC,MAAA,wCACF;;;;;IAMID,EAAA,CAAAE,cAAA,4BAAsC;IAAAF,EAAA,CAAAC,MAAA,GAAc;IAAAD,EAAA,CAAAG,YAAA,EAAoB;;;;IAArDH,EAAA,CAAAI,UAAA,UAAAI,QAAA,CAAAC,EAAA,CAAkB;IAACT,EAAA,CAAAM,SAAA,EAAc;IAAdN,EAAA,CAAAO,iBAAA,CAAAC,QAAA,CAAAE,IAAA,CAAc;;;;;IAKtDV,EAAA,CAAAC,MAAA,wCACF;;;ADvEV,OAAM,MAAOU,kBAAkB;EAwC7BC,YACUC,YAA0B,EAC1BC,MAAc,EACdC,cAA8B,EAC9BC,YAA0B,EAC1BC,eAAgC;IAJhC,KAAAJ,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IA3CzB,KAAAtB,QAAQ,GAAGA,QAAQ;IACnB,KAAAC,cAAc,GAAGA,cAAc;IAE/B;IACA,KAAAsB,UAAU,GAAa,CACrB,YAAY,EACZ,gBAAgB,EAChB,SAAS,EACT,eAAe,EACf,SAAS,EACT,OAAO,EACP,aAAa,EACb,YAAY,CACb;IAED,KAAAC,SAAS,GAAc,IAAI1B,SAAS,CAAC;MACnC2B,KAAK,EAAE,IAAI5B,WAAW,CAAC,EAAE,EAAE,CACzBE,UAAU,CAAC2B,QAAQ,EACnB3B,UAAU,CAAC4B,SAAS,CAAC,CAAC,CAAC,EACvB5B,UAAU,CAAC6B,SAAS,CAAC,GAAG,CAAC,EACzB,IAAI,CAACC,cAAc,CACpB,CAAC;MACFC,KAAK,EAAE,IAAIjC,WAAW,CAAC,EAAE,EAAE,CACzBE,UAAU,CAAC2B,QAAQ,EACnBtB,qBAAqB,CAAC2B,YAAY,CACnC,CAAC;MACFC,WAAW,EAAE,IAAInC,WAAW,CAAC,EAAE,EAAE,CAAC,IAAI,CAACoC,oBAAoB,CAAC,CAAC;MAC7DC,KAAK,EAAE,IAAIrC,WAAW,CAAC,CAAC,EAAE,CACxBE,UAAU,CAAC2B,QAAQ,EACnB3B,UAAU,CAACoC,GAAG,CAAC,CAAC,CAAC,EACjB,IAAI,CAACC,cAAc,CACpB,CAAC;MACFC,QAAQ,EAAE,IAAIxC,WAAW,CAAC,EAAE,EAAEE,UAAU,CAAC2B,QAAQ,CAAC;MAClDY,OAAO,EAAE,IAAIzC,WAAW,CAAC,IAAI,EAAEE,UAAU,CAAC2B,QAAQ;KACnD,CAAC;IAEF,KAAAa,MAAM,GAAY,EAAE;IASlB,MAAMC,OAAO,GAAG,IAAI,CAACpB,cAAc,CAACqB,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;IACzD,IAAIF,OAAO,EAAE;MACX,IAAI,CAACtB,YAAY,CAACyB,OAAO,CAAC,CAACH,OAAO,CAAC,CAACI,SAAS,CAAC;QAC5CC,IAAI,EAAGC,KAAK,IAAI;UACd,IAAIA,KAAK,EAAE;YACT,IAAI,CAACN,OAAO,GAAG,CAACA,OAAO;YACvB,IAAIM,KAAK,CAACd,WAAW,YAAYe,IAAI,EAAE;cACrCD,KAAK,CAACd,WAAW,GAAG7B,cAAc,CAAC2C,KAAK,CAACd,WAAW,CAAC;YACvD;YACA,IAAI,OAAOc,KAAK,CAACd,WAAW,KAAK,QAAQ,EAAE;cACzC,MAAMgB,UAAU,GAAG9C,aAAa,CAAC4C,KAAK,CAACd,WAAW,EAAE,YAAY,CAAC;cACjE,IAAIgB,UAAU,EAAE;gBACdF,KAAK,CAACd,WAAW,GAAG7B,cAAc,CAAC6C,UAAU,CAAC;cAChD;YACF;YACA;YACA,IAAIC,UAAU,GAAG,CAAC;YAClB,IAAIH,KAAK,CAACZ,KAAK,EAAE;cACf,IAAI,OAAOY,KAAK,CAACZ,KAAK,KAAK,QAAQ,EAAE;gBACnCe,UAAU,GAAGH,KAAK,CAACZ,KAAK;cAC1B,CAAC,MAAM,IAAI,OAAOY,KAAK,CAACZ,KAAK,KAAK,QAAQ,EAAE;gBAC1Ce,UAAU,GAAGC,UAAU,CAACJ,KAAK,CAACZ,KAAK,CAAC,IAAI,CAAC;cAC3C;YACF;YAEA;YACA,IAAI,CAACV,SAAS,CAAC2B,UAAU,CAAC;cACxB1B,KAAK,EAAEqB,KAAK,CAACrB,KAAK;cAClBK,KAAK,EAAEgB,KAAK,CAAChB,KAAK;cAClBE,WAAW,EAAEc,KAAK,CAACd,WAAW;cAC9BE,KAAK,EAAEe,UAAU;cACjBZ,QAAQ,EAAES,KAAK,CAACT,QAAQ;cACxBC,OAAO,EAAEQ,KAAK,CAACR;aAChB,CAAC;UACJ;QACF,CAAC;QACDc,KAAK,EAAGA,KAAK,IAAI;UACfC,KAAK,CAAC,oCAAoC,GAAGb,OAAO,CAAC;UACrDc,OAAO,CAACF,KAAK,CAACA,KAAK,CAAC;QACtB;OACD,CAAC;IACJ;EACF;EACAG,QAAQA,CAAA;IACN,IAAI,CAAClC,YAAY,CAACmC,SAAS,EAAE,CAACZ,SAAS,CAAC;MACtCC,IAAI,EAAGY,IAAa,IAAI;QACtBH,OAAO,CAACI,GAAG,CAAC,UAAU,EAAED,IAAI,CAAC;QAC7B,IAAI,CAAClB,MAAM,GAAGkB,IAAI;MACpB,CAAC;MACDL,KAAK,EAAGA,KAAK,IAAI;QACfC,KAAK,CAAC,0BAA0B,CAAC;QACjCC,OAAO,CAACF,KAAK,CAACA,KAAK,CAAC;MACtB;KACD,CAAC;EACJ;EAEAO,WAAWA,CAACC,EAAiB,EAAEC,EAAiB;IAC9C,OAAOD,EAAE,KAAKC,EAAE;EAClB;EAKAC,IAAIA,CAAA;IACF,IAAI;MAAEC;IAAK,CAAE,GAAG,IAAI,CAACvC,SAAS;IAC9B,IAAIuC,KAAK,CAAC/B,WAAW,EAAE;MACrB,MAAMgB,UAAU,GAAG9C,aAAa,CAAC6D,KAAK,CAAC/B,WAAW,CAAC;MACnD,IAAIgB,UAAU,EAAE;QACde,KAAK,CAAC/B,WAAW,GAAGgB,UAAU;MAChC;IACF;IACA;IACAe,KAAK,CAAC7B,KAAK,GAAG8B,MAAM,CAACD,KAAK,CAAC7B,KAAK,CAAC,IAAI,CAAC;IACtC;IACA,IAAI6B,KAAK,CAACzB,OAAO,EAAE;MACjByB,KAAK,CAACzB,OAAO,GAAG,CAACyB,KAAK,CAACzB,OAAO;IAChC;IACAgB,OAAO,CAACI,GAAG,CAACK,KAAK,CAAC;IAClB,IAAI,CAAC7C,YAAY,CAAC4C,IAAI,CAAC;MACrB,GAAGC,KAAK;MACRjD,EAAE,EAAE,IAAI,CAAC0B;KACV,CAAC,CAACI,SAAS,CAAC;MACXC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvB,eAAe,CAAC2C,MAAM,CAAC;UAC1BC,OAAO,EAAE,4BAA4B;UACrCC,QAAQ,EAAE;SACX,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;QACjC,IAAI,CAACnD,MAAM,CAACoD,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACnC,CAAC;MACDnB,KAAK,EAAGA,KAAK,IAAI;QAAA,IAAAoB,YAAA;QACf,IAAIC,YAAY,GAAG,2BAA2B,GAAGV,KAAK,CAACtC,KAAK,GAAG,GAAG;QAClE,KAAA+C,YAAA,GAAIpB,KAAK,CAACA,KAAK,cAAAoB,YAAA,eAAXA,YAAA,CAAaN,OAAO,EAAE;UACxBO,YAAY,GAAGrB,KAAK,CAACA,KAAK,CAACc,OAAO;QACpC;QACAb,KAAK,CAACoB,YAAY,CAAC;QACnBnB,OAAO,CAACF,KAAK,CAACA,KAAK,CAAC;MACtB;KACD,CAAC;EACJ;EAEAsB,QAAQA,CAACC,KAAa,EAAEvB,KAAa;IAAA,IAAAwB,mBAAA;IACnC,MAAMC,WAAW,GAAG,IAAI,CAACrD,SAAS,CAACsD,GAAG,CAACH,KAAK,CAAC;IAC7C,OAAO,CAAC,EAACE,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEE,OAAO,KAAI,CAAC,EAACF,WAAW,aAAXA,WAAW,gBAAAD,mBAAA,GAAXC,WAAW,CAAEG,MAAM,cAAAJ,mBAAA,eAAnBA,mBAAA,CAAsBxB,KAAK,CAAC;EACjE;EAEA;EACAvB,cAAcA,CAACoD,OAAwB;IACrC,IAAI,CAACA,OAAO,CAAClB,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAMtC,KAAK,GAAGwD,OAAO,CAAClB,KAAK,CAACmB,WAAW,EAAE;IACzC,MAAMC,cAAc,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;IAEnD,IAAIA,cAAc,CAACC,IAAI,CAACC,IAAI,IAAI5D,KAAK,CAAC6D,QAAQ,CAACD,IAAI,CAAC,CAAC,EAAE;MACrD,OAAO;QAAEE,YAAY,EAAE;MAAI,CAAE;IAC/B;IACA,OAAO,IAAI;EACb;EAEAtD,oBAAoBA,CAACgD,OAAwB;IAC3C,IAAI,CAACA,OAAO,CAAClB,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAM/B,WAAW,GAAG,IAAIe,IAAI,CAACkC,OAAO,CAAClB,KAAK,CAAC;IAC3C,MAAMyB,WAAW,GAAG,IAAIzC,IAAI,EAAE;IAC9B,MAAM0C,OAAO,GAAG,IAAI1C,IAAI,CAAC,YAAY,CAAC;IAEtC,IAAIf,WAAW,GAAGwD,WAAW,EAAE;MAC7B,OAAO;QAAEE,UAAU,EAAE;MAAI,CAAE;IAC7B;IACA,IAAI1D,WAAW,GAAGyD,OAAO,EAAE;MACzB,OAAO;QAAEE,MAAM,EAAE;MAAI,CAAE;IACzB;IACA,OAAO,IAAI;EACb;EAEAvD,cAAcA,CAAC6C,OAAwB;IACrC,IAAI,CAACA,OAAO,CAAClB,KAAK,EAAE,OAAO,IAAI;IAE/B,MAAM7B,KAAK,GAAG,CAAC+C,OAAO,CAAClB,KAAK;IAC5B,IAAI7B,KAAK,GAAG,KAAK,EAAE;MACjB,OAAO;QAAE0D,YAAY,EAAE;MAAI,CAAE;IAC/B;IACA,OAAO,IAAI;EACb;;sBA7LW5E,kBAAkB;;mCAAlBA,mBAAkB,EAAAX,EAAA,CAAAwF,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA1F,EAAA,CAAAwF,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA5F,EAAA,CAAAwF,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAA7F,EAAA,CAAAwF,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAA/F,EAAA,CAAAwF,iBAAA,CAAAQ,EAAA,CAAAC,eAAA;AAAA;;QAAlBtF,mBAAkB;EAAAuF,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCf3BzG,EAFJ,CAAAE,cAAA,oBAAiC,qBACA,qBACH;MACxBF,EAAA,CAAA2G,SAAA,sBAAmC;MACrC3G,EAAA,CAAAG,YAAA,EAAc;MACdH,EAAA,CAAAE,cAAA,gBAAW;MAAAF,EAAA,CAAAC,MAAA,4BAAqB;MAEpCD,EAFoC,CAAAG,YAAA,EAAY,EAChC,EACH;MAMLH,EAJR,CAAAE,cAAA,kBAAa,aACiB,cACI,eAClB,gBACE;MACRF,EAAA,CAAA2G,SAAA,oBAAsG;MACtG3G,EAAA,CAAAE,cAAA,SAAG;MAUHF,EATA,CAAA4G,UAAA,KAAAC,0CAAA,OAAoC,KAAAC,0CAAA,OAGC,KAAAC,0CAAA,OAGA,KAAAC,0CAAA,OAGG;MAI1ChH,EADE,CAAAG,YAAA,EAAI,EACK;MACXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA2G,SAAA,oBAAyG;MACzG3G,EAAA,CAAAE,cAAA,SAAG;MAIHF,EAHA,CAAA4G,UAAA,KAAAK,0CAAA,OAAoC,KAAAC,0CAAA,OAGE;MAIxClH,EADE,CAAAG,YAAA,EAAI,EACK;MACXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA2G,SAAA,oBACsC;MACtC3G,EAAA,CAAAE,cAAA,SAAG;MAIHF,EAHA,CAAA4G,UAAA,KAAAO,0CAAA,OAA4C,KAAAC,0CAAA,OAGJ;MAI1CpH,EADE,CAAAG,YAAA,EAAI,EACK;MACXH,EAAA,CAAAE,cAAA,gBAAU;MACRF,EAAA,CAAA2G,SAAA,oBAAoH;MACpH3G,EAAA,CAAAE,cAAA,SAAG;MAOHF,EANA,CAAA4G,UAAA,KAAAS,0CAAA,OAAoC,KAAAC,0CAAA,OAGL,KAAAC,0CAAA,OAGS;MAI1CvH,EADE,CAAAG,YAAA,EAAI,EACK;MAETH,EADF,CAAAE,cAAA,gBAAU,qBAC2E;MACjFF,EAAA,CAAAwH,gBAAA,KAAAC,kCAAA,iCAAAzH,EAAA,CAAA0H,yBAAA,CAEC;MACH1H,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACHF,EAAA,CAAA4G,UAAA,KAAAe,0CAAA,OAAuC;MAIzC3H,EADE,CAAAG,YAAA,EAAI,EACK;MAETH,EADF,CAAAE,cAAA,gBAAU,sBACmG;MACzGF,EAAA,CAAAwH,gBAAA,KAAAI,kCAAA,iCAAA5H,EAAA,CAAA0H,yBAAA,CAEC;MACH1H,EAAA,CAAAG,YAAA,EAAa;MACbH,EAAA,CAAAE,cAAA,SAAG;MACHF,EAAA,CAAA4G,UAAA,KAAAiB,0CAAA,OAAsC;MAK1C7H,EAFI,CAAAG,YAAA,EAAI,EACK,EACF;MAETH,EADF,CAAAE,cAAA,mBAAyD,0BACS;MAAjBF,EAAA,CAAA8H,UAAA,mBAAAC,6DAAA;QAAA,OAASrB,GAAA,CAAAjD,IAAA,EAAM;MAAA,EAAC;MAC7DzD,EAAA,CAAA2G,SAAA,oBAAsC;MAKhD3G,EAJQ,CAAAG,YAAA,EAAiB,EACT,EACL,EACH,EACM;;;MAnGFH,EAAA,CAAAI,UAAA,qBAAoB;MAWtBJ,EAAA,CAAAM,SAAA,GAAuB;MAAvBN,EAAA,CAAAI,UAAA,cAAAsG,GAAA,CAAAvF,SAAA,CAAuB;MAKvBnB,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,gCAEC;MACDrE,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,iCAEC;MACDrE,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,iCAEC;MACDrE,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,oCAEC;MAMDrE,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,gCAEC;MACDrE,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,kCAEC;MAIqFrE,EAAA,CAAAM,SAAA,GAAoB;MACxGN,EADoF,CAAAI,UAAA,YAAAsG,GAAA,CAAA/G,QAAA,CAAoB,mBAAA+G,GAAA,CAAA9G,cAAA,CACvE;MAEnCI,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,wCAEC;MACDrE,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,oCAEC;MAMDrE,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,gCAEC;MACDrE,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,2BAEC;MACDrE,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,oCAEC;MAKCrE,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAiI,UAAA,CAAAvB,GAAA,CAAAxF,UAAA,CAEC;MAGHlB,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,mCAEC;MAIqCrE,EAAA,CAAAM,SAAA,GAA2B;MAA3BN,EAAA,CAAAI,UAAA,gBAAAsG,GAAA,CAAApD,WAAA,CAA2B;MAC/DtD,EAAA,CAAAM,SAAA,EAEC;MAFDN,EAAA,CAAAiI,UAAA,CAAAvB,GAAA,CAAAxE,MAAA,CAEC;MAGHlC,EAAA,CAAAM,SAAA,GAEC;MAFDN,EAAA,CAAAgI,aAAA,CAAAtB,GAAA,CAAArC,QAAA,kCAEC;MAKarE,EAAA,CAAAM,SAAA,GAA8B;MAA9BN,EAAA,CAAAI,UAAA,aAAAsG,GAAA,CAAAvF,SAAA,CAAA+G,OAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}