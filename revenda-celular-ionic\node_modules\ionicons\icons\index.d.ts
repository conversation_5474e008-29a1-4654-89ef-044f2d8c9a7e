/* Ionicons v7.4.0, Types */

export declare var accessibility: string;
export declare var accessibilityOutline: string;
export declare var accessibilitySharp: string;
export declare var add: string;
export declare var addCircle: string;
export declare var addCircleOutline: string;
export declare var addCircleSharp: string;
export declare var addOutline: string;
export declare var addSharp: string;
export declare var airplane: string;
export declare var airplaneOutline: string;
export declare var airplaneSharp: string;
export declare var alarm: string;
export declare var alarmOutline: string;
export declare var alarmSharp: string;
export declare var albums: string;
export declare var albumsOutline: string;
export declare var albumsSharp: string;
export declare var alert: string;
export declare var alertCircle: string;
export declare var alertCircleOutline: string;
export declare var alertCircleSharp: string;
export declare var alertOutline: string;
export declare var alertSharp: string;
export declare var americanFootball: string;
export declare var americanFootballOutline: string;
export declare var americanFootballSharp: string;
export declare var analytics: string;
export declare var analyticsOutline: string;
export declare var analyticsSharp: string;
export declare var aperture: string;
export declare var apertureOutline: string;
export declare var apertureSharp: string;
export declare var apps: string;
export declare var appsOutline: string;
export declare var appsSharp: string;
export declare var archive: string;
export declare var archiveOutline: string;
export declare var archiveSharp: string;
export declare var arrowBack: string;
export declare var arrowBackCircle: string;
export declare var arrowBackCircleOutline: string;
export declare var arrowBackCircleSharp: string;
export declare var arrowBackOutline: string;
export declare var arrowBackSharp: string;
export declare var arrowDown: string;
export declare var arrowDownCircle: string;
export declare var arrowDownCircleOutline: string;
export declare var arrowDownCircleSharp: string;
export declare var arrowDownLeftBox: string;
export declare var arrowDownLeftBoxOutline: string;
export declare var arrowDownLeftBoxSharp: string;
export declare var arrowDownOutline: string;
export declare var arrowDownRightBox: string;
export declare var arrowDownRightBoxOutline: string;
export declare var arrowDownRightBoxSharp: string;
export declare var arrowDownSharp: string;
export declare var arrowForward: string;
export declare var arrowForwardCircle: string;
export declare var arrowForwardCircleOutline: string;
export declare var arrowForwardCircleSharp: string;
export declare var arrowForwardOutline: string;
export declare var arrowForwardSharp: string;
export declare var arrowRedo: string;
export declare var arrowRedoCircle: string;
export declare var arrowRedoCircleOutline: string;
export declare var arrowRedoCircleSharp: string;
export declare var arrowRedoOutline: string;
export declare var arrowRedoSharp: string;
export declare var arrowUndo: string;
export declare var arrowUndoCircle: string;
export declare var arrowUndoCircleOutline: string;
export declare var arrowUndoCircleSharp: string;
export declare var arrowUndoOutline: string;
export declare var arrowUndoSharp: string;
export declare var arrowUp: string;
export declare var arrowUpCircle: string;
export declare var arrowUpCircleOutline: string;
export declare var arrowUpCircleSharp: string;
export declare var arrowUpLeftBox: string;
export declare var arrowUpLeftBoxOutline: string;
export declare var arrowUpLeftBoxSharp: string;
export declare var arrowUpOutline: string;
export declare var arrowUpRightBox: string;
export declare var arrowUpRightBoxOutline: string;
export declare var arrowUpRightBoxSharp: string;
export declare var arrowUpSharp: string;
export declare var at: string;
export declare var atCircle: string;
export declare var atCircleOutline: string;
export declare var atCircleSharp: string;
export declare var atOutline: string;
export declare var atSharp: string;
export declare var attach: string;
export declare var attachOutline: string;
export declare var attachSharp: string;
export declare var backspace: string;
export declare var backspaceOutline: string;
export declare var backspaceSharp: string;
export declare var bag: string;
export declare var bagAdd: string;
export declare var bagAddOutline: string;
export declare var bagAddSharp: string;
export declare var bagCheck: string;
export declare var bagCheckOutline: string;
export declare var bagCheckSharp: string;
export declare var bagHandle: string;
export declare var bagHandleOutline: string;
export declare var bagHandleSharp: string;
export declare var bagOutline: string;
export declare var bagRemove: string;
export declare var bagRemoveOutline: string;
export declare var bagRemoveSharp: string;
export declare var bagSharp: string;
export declare var balloon: string;
export declare var balloonOutline: string;
export declare var balloonSharp: string;
export declare var ban: string;
export declare var banOutline: string;
export declare var banSharp: string;
export declare var bandage: string;
export declare var bandageOutline: string;
export declare var bandageSharp: string;
export declare var barChart: string;
export declare var barChartOutline: string;
export declare var barChartSharp: string;
export declare var barbell: string;
export declare var barbellOutline: string;
export declare var barbellSharp: string;
export declare var barcode: string;
export declare var barcodeOutline: string;
export declare var barcodeSharp: string;
export declare var baseball: string;
export declare var baseballOutline: string;
export declare var baseballSharp: string;
export declare var basket: string;
export declare var basketOutline: string;
export declare var basketSharp: string;
export declare var basketball: string;
export declare var basketballOutline: string;
export declare var basketballSharp: string;
export declare var batteryCharging: string;
export declare var batteryChargingOutline: string;
export declare var batteryChargingSharp: string;
export declare var batteryDead: string;
export declare var batteryDeadOutline: string;
export declare var batteryDeadSharp: string;
export declare var batteryFull: string;
export declare var batteryFullOutline: string;
export declare var batteryFullSharp: string;
export declare var batteryHalf: string;
export declare var batteryHalfOutline: string;
export declare var batteryHalfSharp: string;
export declare var beaker: string;
export declare var beakerOutline: string;
export declare var beakerSharp: string;
export declare var bed: string;
export declare var bedOutline: string;
export declare var bedSharp: string;
export declare var beer: string;
export declare var beerOutline: string;
export declare var beerSharp: string;
export declare var bicycle: string;
export declare var bicycleOutline: string;
export declare var bicycleSharp: string;
export declare var binoculars: string;
export declare var binocularsOutline: string;
export declare var binocularsSharp: string;
export declare var bluetooth: string;
export declare var bluetoothOutline: string;
export declare var bluetoothSharp: string;
export declare var boat: string;
export declare var boatOutline: string;
export declare var boatSharp: string;
export declare var body: string;
export declare var bodyOutline: string;
export declare var bodySharp: string;
export declare var bonfire: string;
export declare var bonfireOutline: string;
export declare var bonfireSharp: string;
export declare var book: string;
export declare var bookOutline: string;
export declare var bookSharp: string;
export declare var bookmark: string;
export declare var bookmarkOutline: string;
export declare var bookmarkSharp: string;
export declare var bookmarks: string;
export declare var bookmarksOutline: string;
export declare var bookmarksSharp: string;
export declare var bowlingBall: string;
export declare var bowlingBallOutline: string;
export declare var bowlingBallSharp: string;
export declare var briefcase: string;
export declare var briefcaseOutline: string;
export declare var briefcaseSharp: string;
export declare var browsers: string;
export declare var browsersOutline: string;
export declare var browsersSharp: string;
export declare var brush: string;
export declare var brushOutline: string;
export declare var brushSharp: string;
export declare var bug: string;
export declare var bugOutline: string;
export declare var bugSharp: string;
export declare var build: string;
export declare var buildOutline: string;
export declare var buildSharp: string;
export declare var bulb: string;
export declare var bulbOutline: string;
export declare var bulbSharp: string;
export declare var bus: string;
export declare var busOutline: string;
export declare var busSharp: string;
export declare var business: string;
export declare var businessOutline: string;
export declare var businessSharp: string;
export declare var cafe: string;
export declare var cafeOutline: string;
export declare var cafeSharp: string;
export declare var calculator: string;
export declare var calculatorOutline: string;
export declare var calculatorSharp: string;
export declare var calendar: string;
export declare var calendarClear: string;
export declare var calendarClearOutline: string;
export declare var calendarClearSharp: string;
export declare var calendarNumber: string;
export declare var calendarNumberOutline: string;
export declare var calendarNumberSharp: string;
export declare var calendarOutline: string;
export declare var calendarSharp: string;
export declare var call: string;
export declare var callOutline: string;
export declare var callSharp: string;
export declare var camera: string;
export declare var cameraOutline: string;
export declare var cameraReverse: string;
export declare var cameraReverseOutline: string;
export declare var cameraReverseSharp: string;
export declare var cameraSharp: string;
export declare var car: string;
export declare var carOutline: string;
export declare var carSharp: string;
export declare var carSport: string;
export declare var carSportOutline: string;
export declare var carSportSharp: string;
export declare var card: string;
export declare var cardOutline: string;
export declare var cardSharp: string;
export declare var caretBack: string;
export declare var caretBackCircle: string;
export declare var caretBackCircleOutline: string;
export declare var caretBackCircleSharp: string;
export declare var caretBackOutline: string;
export declare var caretBackSharp: string;
export declare var caretDown: string;
export declare var caretDownCircle: string;
export declare var caretDownCircleOutline: string;
export declare var caretDownCircleSharp: string;
export declare var caretDownOutline: string;
export declare var caretDownSharp: string;
export declare var caretForward: string;
export declare var caretForwardCircle: string;
export declare var caretForwardCircleOutline: string;
export declare var caretForwardCircleSharp: string;
export declare var caretForwardOutline: string;
export declare var caretForwardSharp: string;
export declare var caretUp: string;
export declare var caretUpCircle: string;
export declare var caretUpCircleOutline: string;
export declare var caretUpCircleSharp: string;
export declare var caretUpOutline: string;
export declare var caretUpSharp: string;
export declare var cart: string;
export declare var cartOutline: string;
export declare var cartSharp: string;
export declare var cash: string;
export declare var cashOutline: string;
export declare var cashSharp: string;
export declare var cellular: string;
export declare var cellularOutline: string;
export declare var cellularSharp: string;
export declare var chatbox: string;
export declare var chatboxEllipses: string;
export declare var chatboxEllipsesOutline: string;
export declare var chatboxEllipsesSharp: string;
export declare var chatboxOutline: string;
export declare var chatboxSharp: string;
export declare var chatbubble: string;
export declare var chatbubbleEllipses: string;
export declare var chatbubbleEllipsesOutline: string;
export declare var chatbubbleEllipsesSharp: string;
export declare var chatbubbleOutline: string;
export declare var chatbubbleSharp: string;
export declare var chatbubbles: string;
export declare var chatbubblesOutline: string;
export declare var chatbubblesSharp: string;
export declare var checkbox: string;
export declare var checkboxOutline: string;
export declare var checkboxSharp: string;
export declare var checkmark: string;
export declare var checkmarkCircle: string;
export declare var checkmarkCircleOutline: string;
export declare var checkmarkCircleSharp: string;
export declare var checkmarkDone: string;
export declare var checkmarkDoneCircle: string;
export declare var checkmarkDoneCircleOutline: string;
export declare var checkmarkDoneCircleSharp: string;
export declare var checkmarkDoneOutline: string;
export declare var checkmarkDoneSharp: string;
export declare var checkmarkOutline: string;
export declare var checkmarkSharp: string;
export declare var chevronBack: string;
export declare var chevronBackCircle: string;
export declare var chevronBackCircleOutline: string;
export declare var chevronBackCircleSharp: string;
export declare var chevronBackOutline: string;
export declare var chevronBackSharp: string;
export declare var chevronCollapse: string;
export declare var chevronCollapseOutline: string;
export declare var chevronCollapseSharp: string;
export declare var chevronDown: string;
export declare var chevronDownCircle: string;
export declare var chevronDownCircleOutline: string;
export declare var chevronDownCircleSharp: string;
export declare var chevronDownOutline: string;
export declare var chevronDownSharp: string;
export declare var chevronExpand: string;
export declare var chevronExpandOutline: string;
export declare var chevronExpandSharp: string;
export declare var chevronForward: string;
export declare var chevronForwardCircle: string;
export declare var chevronForwardCircleOutline: string;
export declare var chevronForwardCircleSharp: string;
export declare var chevronForwardOutline: string;
export declare var chevronForwardSharp: string;
export declare var chevronUp: string;
export declare var chevronUpCircle: string;
export declare var chevronUpCircleOutline: string;
export declare var chevronUpCircleSharp: string;
export declare var chevronUpOutline: string;
export declare var chevronUpSharp: string;
export declare var clipboard: string;
export declare var clipboardOutline: string;
export declare var clipboardSharp: string;
export declare var close: string;
export declare var closeCircle: string;
export declare var closeCircleOutline: string;
export declare var closeCircleSharp: string;
export declare var closeOutline: string;
export declare var closeSharp: string;
export declare var cloud: string;
export declare var cloudCircle: string;
export declare var cloudCircleOutline: string;
export declare var cloudCircleSharp: string;
export declare var cloudDone: string;
export declare var cloudDoneOutline: string;
export declare var cloudDoneSharp: string;
export declare var cloudDownload: string;
export declare var cloudDownloadOutline: string;
export declare var cloudDownloadSharp: string;
export declare var cloudOffline: string;
export declare var cloudOfflineOutline: string;
export declare var cloudOfflineSharp: string;
export declare var cloudOutline: string;
export declare var cloudSharp: string;
export declare var cloudUpload: string;
export declare var cloudUploadOutline: string;
export declare var cloudUploadSharp: string;
export declare var cloudy: string;
export declare var cloudyNight: string;
export declare var cloudyNightOutline: string;
export declare var cloudyNightSharp: string;
export declare var cloudyOutline: string;
export declare var cloudySharp: string;
export declare var code: string;
export declare var codeDownload: string;
export declare var codeDownloadOutline: string;
export declare var codeDownloadSharp: string;
export declare var codeOutline: string;
export declare var codeSharp: string;
export declare var codeSlash: string;
export declare var codeSlashOutline: string;
export declare var codeSlashSharp: string;
export declare var codeWorking: string;
export declare var codeWorkingOutline: string;
export declare var codeWorkingSharp: string;
export declare var cog: string;
export declare var cogOutline: string;
export declare var cogSharp: string;
export declare var colorFill: string;
export declare var colorFillOutline: string;
export declare var colorFillSharp: string;
export declare var colorFilter: string;
export declare var colorFilterOutline: string;
export declare var colorFilterSharp: string;
export declare var colorPalette: string;
export declare var colorPaletteOutline: string;
export declare var colorPaletteSharp: string;
export declare var colorWand: string;
export declare var colorWandOutline: string;
export declare var colorWandSharp: string;
export declare var compass: string;
export declare var compassOutline: string;
export declare var compassSharp: string;
export declare var construct: string;
export declare var constructOutline: string;
export declare var constructSharp: string;
export declare var contract: string;
export declare var contractOutline: string;
export declare var contractSharp: string;
export declare var contrast: string;
export declare var contrastOutline: string;
export declare var contrastSharp: string;
export declare var copy: string;
export declare var copyOutline: string;
export declare var copySharp: string;
export declare var create: string;
export declare var createOutline: string;
export declare var createSharp: string;
export declare var crop: string;
export declare var cropOutline: string;
export declare var cropSharp: string;
export declare var cube: string;
export declare var cubeOutline: string;
export declare var cubeSharp: string;
export declare var cut: string;
export declare var cutOutline: string;
export declare var cutSharp: string;
export declare var desktop: string;
export declare var desktopOutline: string;
export declare var desktopSharp: string;
export declare var diamond: string;
export declare var diamondOutline: string;
export declare var diamondSharp: string;
export declare var dice: string;
export declare var diceOutline: string;
export declare var diceSharp: string;
export declare var disc: string;
export declare var discOutline: string;
export declare var discSharp: string;
export declare var document: string;
export declare var documentAttach: string;
export declare var documentAttachOutline: string;
export declare var documentAttachSharp: string;
export declare var documentLock: string;
export declare var documentLockOutline: string;
export declare var documentLockSharp: string;
export declare var documentOutline: string;
export declare var documentSharp: string;
export declare var documentText: string;
export declare var documentTextOutline: string;
export declare var documentTextSharp: string;
export declare var documents: string;
export declare var documentsOutline: string;
export declare var documentsSharp: string;
export declare var download: string;
export declare var downloadOutline: string;
export declare var downloadSharp: string;
export declare var duplicate: string;
export declare var duplicateOutline: string;
export declare var duplicateSharp: string;
export declare var ear: string;
export declare var earOutline: string;
export declare var earSharp: string;
export declare var earth: string;
export declare var earthOutline: string;
export declare var earthSharp: string;
export declare var easel: string;
export declare var easelOutline: string;
export declare var easelSharp: string;
export declare var egg: string;
export declare var eggOutline: string;
export declare var eggSharp: string;
export declare var ellipse: string;
export declare var ellipseOutline: string;
export declare var ellipseSharp: string;
export declare var ellipsisHorizontal: string;
export declare var ellipsisHorizontalCircle: string;
export declare var ellipsisHorizontalCircleOutline: string;
export declare var ellipsisHorizontalCircleSharp: string;
export declare var ellipsisHorizontalOutline: string;
export declare var ellipsisHorizontalSharp: string;
export declare var ellipsisVertical: string;
export declare var ellipsisVerticalCircle: string;
export declare var ellipsisVerticalCircleOutline: string;
export declare var ellipsisVerticalCircleSharp: string;
export declare var ellipsisVerticalOutline: string;
export declare var ellipsisVerticalSharp: string;
export declare var enter: string;
export declare var enterOutline: string;
export declare var enterSharp: string;
export declare var exit: string;
export declare var exitOutline: string;
export declare var exitSharp: string;
export declare var expand: string;
export declare var expandOutline: string;
export declare var expandSharp: string;
export declare var extensionPuzzle: string;
export declare var extensionPuzzleOutline: string;
export declare var extensionPuzzleSharp: string;
export declare var eye: string;
export declare var eyeOff: string;
export declare var eyeOffOutline: string;
export declare var eyeOffSharp: string;
export declare var eyeOutline: string;
export declare var eyeSharp: string;
export declare var eyedrop: string;
export declare var eyedropOutline: string;
export declare var eyedropSharp: string;
export declare var fastFood: string;
export declare var fastFoodOutline: string;
export declare var fastFoodSharp: string;
export declare var female: string;
export declare var femaleOutline: string;
export declare var femaleSharp: string;
export declare var fileTray: string;
export declare var fileTrayFull: string;
export declare var fileTrayFullOutline: string;
export declare var fileTrayFullSharp: string;
export declare var fileTrayOutline: string;
export declare var fileTraySharp: string;
export declare var fileTrayStacked: string;
export declare var fileTrayStackedOutline: string;
export declare var fileTrayStackedSharp: string;
export declare var film: string;
export declare var filmOutline: string;
export declare var filmSharp: string;
export declare var filter: string;
export declare var filterCircle: string;
export declare var filterCircleOutline: string;
export declare var filterCircleSharp: string;
export declare var filterOutline: string;
export declare var filterSharp: string;
export declare var fingerPrint: string;
export declare var fingerPrintOutline: string;
export declare var fingerPrintSharp: string;
export declare var fish: string;
export declare var fishOutline: string;
export declare var fishSharp: string;
export declare var fitness: string;
export declare var fitnessOutline: string;
export declare var fitnessSharp: string;
export declare var flag: string;
export declare var flagOutline: string;
export declare var flagSharp: string;
export declare var flame: string;
export declare var flameOutline: string;
export declare var flameSharp: string;
export declare var flash: string;
export declare var flashOff: string;
export declare var flashOffOutline: string;
export declare var flashOffSharp: string;
export declare var flashOutline: string;
export declare var flashSharp: string;
export declare var flashlight: string;
export declare var flashlightOutline: string;
export declare var flashlightSharp: string;
export declare var flask: string;
export declare var flaskOutline: string;
export declare var flaskSharp: string;
export declare var flower: string;
export declare var flowerOutline: string;
export declare var flowerSharp: string;
export declare var folder: string;
export declare var folderOpen: string;
export declare var folderOpenOutline: string;
export declare var folderOpenSharp: string;
export declare var folderOutline: string;
export declare var folderSharp: string;
export declare var football: string;
export declare var footballOutline: string;
export declare var footballSharp: string;
export declare var footsteps: string;
export declare var footstepsOutline: string;
export declare var footstepsSharp: string;
export declare var funnel: string;
export declare var funnelOutline: string;
export declare var funnelSharp: string;
export declare var gameController: string;
export declare var gameControllerOutline: string;
export declare var gameControllerSharp: string;
export declare var gift: string;
export declare var giftOutline: string;
export declare var giftSharp: string;
export declare var gitBranch: string;
export declare var gitBranchOutline: string;
export declare var gitBranchSharp: string;
export declare var gitCommit: string;
export declare var gitCommitOutline: string;
export declare var gitCommitSharp: string;
export declare var gitCompare: string;
export declare var gitCompareOutline: string;
export declare var gitCompareSharp: string;
export declare var gitMerge: string;
export declare var gitMergeOutline: string;
export declare var gitMergeSharp: string;
export declare var gitNetwork: string;
export declare var gitNetworkOutline: string;
export declare var gitNetworkSharp: string;
export declare var gitPullRequest: string;
export declare var gitPullRequestOutline: string;
export declare var gitPullRequestSharp: string;
export declare var glasses: string;
export declare var glassesOutline: string;
export declare var glassesSharp: string;
export declare var globe: string;
export declare var globeOutline: string;
export declare var globeSharp: string;
export declare var golf: string;
export declare var golfOutline: string;
export declare var golfSharp: string;
export declare var grid: string;
export declare var gridOutline: string;
export declare var gridSharp: string;
export declare var hammer: string;
export declare var hammerOutline: string;
export declare var hammerSharp: string;
export declare var handLeft: string;
export declare var handLeftOutline: string;
export declare var handLeftSharp: string;
export declare var handRight: string;
export declare var handRightOutline: string;
export declare var handRightSharp: string;
export declare var happy: string;
export declare var happyOutline: string;
export declare var happySharp: string;
export declare var hardwareChip: string;
export declare var hardwareChipOutline: string;
export declare var hardwareChipSharp: string;
export declare var headset: string;
export declare var headsetOutline: string;
export declare var headsetSharp: string;
export declare var heart: string;
export declare var heartCircle: string;
export declare var heartCircleOutline: string;
export declare var heartCircleSharp: string;
export declare var heartDislike: string;
export declare var heartDislikeCircle: string;
export declare var heartDislikeCircleOutline: string;
export declare var heartDislikeCircleSharp: string;
export declare var heartDislikeOutline: string;
export declare var heartDislikeSharp: string;
export declare var heartHalf: string;
export declare var heartHalfOutline: string;
export declare var heartHalfSharp: string;
export declare var heartOutline: string;
export declare var heartSharp: string;
export declare var help: string;
export declare var helpBuoy: string;
export declare var helpBuoyOutline: string;
export declare var helpBuoySharp: string;
export declare var helpCircle: string;
export declare var helpCircleOutline: string;
export declare var helpCircleSharp: string;
export declare var helpOutline: string;
export declare var helpSharp: string;
export declare var home: string;
export declare var homeOutline: string;
export declare var homeSharp: string;
export declare var hourglass: string;
export declare var hourglassOutline: string;
export declare var hourglassSharp: string;
export declare var iceCream: string;
export declare var iceCreamOutline: string;
export declare var iceCreamSharp: string;
export declare var idCard: string;
export declare var idCardOutline: string;
export declare var idCardSharp: string;
export declare var image: string;
export declare var imageOutline: string;
export declare var imageSharp: string;
export declare var images: string;
export declare var imagesOutline: string;
export declare var imagesSharp: string;
export declare var infinite: string;
export declare var infiniteOutline: string;
export declare var infiniteSharp: string;
export declare var information: string;
export declare var informationCircle: string;
export declare var informationCircleOutline: string;
export declare var informationCircleSharp: string;
export declare var informationOutline: string;
export declare var informationSharp: string;
export declare var invertMode: string;
export declare var invertModeOutline: string;
export declare var invertModeSharp: string;
export declare var journal: string;
export declare var journalOutline: string;
export declare var journalSharp: string;
export declare var key: string;
export declare var keyOutline: string;
export declare var keySharp: string;
export declare var keypad: string;
export declare var keypadOutline: string;
export declare var keypadSharp: string;
export declare var language: string;
export declare var languageOutline: string;
export declare var languageSharp: string;
export declare var laptop: string;
export declare var laptopOutline: string;
export declare var laptopSharp: string;
export declare var layers: string;
export declare var layersOutline: string;
export declare var layersSharp: string;
export declare var leaf: string;
export declare var leafOutline: string;
export declare var leafSharp: string;
export declare var library: string;
export declare var libraryOutline: string;
export declare var librarySharp: string;
export declare var link: string;
export declare var linkOutline: string;
export declare var linkSharp: string;
export declare var list: string;
export declare var listCircle: string;
export declare var listCircleOutline: string;
export declare var listCircleSharp: string;
export declare var listOutline: string;
export declare var listSharp: string;
export declare var locate: string;
export declare var locateOutline: string;
export declare var locateSharp: string;
export declare var location: string;
export declare var locationOutline: string;
export declare var locationSharp: string;
export declare var lockClosed: string;
export declare var lockClosedOutline: string;
export declare var lockClosedSharp: string;
export declare var lockOpen: string;
export declare var lockOpenOutline: string;
export declare var lockOpenSharp: string;
export declare var logIn: string;
export declare var logInOutline: string;
export declare var logInSharp: string;
export declare var logOut: string;
export declare var logOutOutline: string;
export declare var logOutSharp: string;
export declare var logoAlipay: string;
export declare var logoAmazon: string;
export declare var logoAmplify: string;
export declare var logoAndroid: string;
export declare var logoAngular: string;
export declare var logoAppflow: string;
export declare var logoApple: string;
export declare var logoAppleAppstore: string;
export declare var logoAppleAr: string;
export declare var logoBehance: string;
export declare var logoBitbucket: string;
export declare var logoBitcoin: string;
export declare var logoBuffer: string;
export declare var logoCapacitor: string;
export declare var logoChrome: string;
export declare var logoClosedCaptioning: string;
export declare var logoCodepen: string;
export declare var logoCss3: string;
export declare var logoDesignernews: string;
export declare var logoDeviantart: string;
export declare var logoDiscord: string;
export declare var logoDocker: string;
export declare var logoDribbble: string;
export declare var logoDropbox: string;
export declare var logoEdge: string;
export declare var logoElectron: string;
export declare var logoEuro: string;
export declare var logoFacebook: string;
export declare var logoFigma: string;
export declare var logoFirebase: string;
export declare var logoFirefox: string;
export declare var logoFlickr: string;
export declare var logoFoursquare: string;
export declare var logoGithub: string;
export declare var logoGitlab: string;
export declare var logoGoogle: string;
export declare var logoGooglePlaystore: string;
export declare var logoHackernews: string;
export declare var logoHtml5: string;
export declare var logoInstagram: string;
export declare var logoIonic: string;
export declare var logoIonitron: string;
export declare var logoJavascript: string;
export declare var logoLaravel: string;
export declare var logoLinkedin: string;
export declare var logoMarkdown: string;
export declare var logoMastodon: string;
export declare var logoMedium: string;
export declare var logoMicrosoft: string;
export declare var logoNoSmoking: string;
export declare var logoNodejs: string;
export declare var logoNpm: string;
export declare var logoOctocat: string;
export declare var logoPaypal: string;
export declare var logoPinterest: string;
export declare var logoPlaystation: string;
export declare var logoPwa: string;
export declare var logoPython: string;
export declare var logoReact: string;
export declare var logoReddit: string;
export declare var logoRss: string;
export declare var logoSass: string;
export declare var logoSkype: string;
export declare var logoSlack: string;
export declare var logoSnapchat: string;
export declare var logoSoundcloud: string;
export declare var logoStackoverflow: string;
export declare var logoSteam: string;
export declare var logoStencil: string;
export declare var logoTableau: string;
export declare var logoTiktok: string;
export declare var logoTrapeze: string;
export declare var logoTumblr: string;
export declare var logoTux: string;
export declare var logoTwitch: string;
export declare var logoTwitter: string;
export declare var logoUsd: string;
export declare var logoVenmo: string;
export declare var logoVercel: string;
export declare var logoVimeo: string;
export declare var logoVk: string;
export declare var logoVue: string;
export declare var logoWebComponent: string;
export declare var logoWechat: string;
export declare var logoWhatsapp: string;
export declare var logoWindows: string;
export declare var logoWordpress: string;
export declare var logoX: string;
export declare var logoXbox: string;
export declare var logoXing: string;
export declare var logoYahoo: string;
export declare var logoYen: string;
export declare var logoYoutube: string;
export declare var magnet: string;
export declare var magnetOutline: string;
export declare var magnetSharp: string;
export declare var mail: string;
export declare var mailOpen: string;
export declare var mailOpenOutline: string;
export declare var mailOpenSharp: string;
export declare var mailOutline: string;
export declare var mailSharp: string;
export declare var mailUnread: string;
export declare var mailUnreadOutline: string;
export declare var mailUnreadSharp: string;
export declare var male: string;
export declare var maleFemale: string;
export declare var maleFemaleOutline: string;
export declare var maleFemaleSharp: string;
export declare var maleOutline: string;
export declare var maleSharp: string;
export declare var man: string;
export declare var manOutline: string;
export declare var manSharp: string;
export declare var map: string;
export declare var mapOutline: string;
export declare var mapSharp: string;
export declare var medal: string;
export declare var medalOutline: string;
export declare var medalSharp: string;
export declare var medical: string;
export declare var medicalOutline: string;
export declare var medicalSharp: string;
export declare var medkit: string;
export declare var medkitOutline: string;
export declare var medkitSharp: string;
export declare var megaphone: string;
export declare var megaphoneOutline: string;
export declare var megaphoneSharp: string;
export declare var menu: string;
export declare var menuOutline: string;
export declare var menuSharp: string;
export declare var mic: string;
export declare var micCircle: string;
export declare var micCircleOutline: string;
export declare var micCircleSharp: string;
export declare var micOff: string;
export declare var micOffCircle: string;
export declare var micOffCircleOutline: string;
export declare var micOffCircleSharp: string;
export declare var micOffOutline: string;
export declare var micOffSharp: string;
export declare var micOutline: string;
export declare var micSharp: string;
export declare var moon: string;
export declare var moonOutline: string;
export declare var moonSharp: string;
export declare var move: string;
export declare var moveOutline: string;
export declare var moveSharp: string;
export declare var musicalNote: string;
export declare var musicalNoteOutline: string;
export declare var musicalNoteSharp: string;
export declare var musicalNotes: string;
export declare var musicalNotesOutline: string;
export declare var musicalNotesSharp: string;
export declare var navigate: string;
export declare var navigateCircle: string;
export declare var navigateCircleOutline: string;
export declare var navigateCircleSharp: string;
export declare var navigateOutline: string;
export declare var navigateSharp: string;
export declare var newspaper: string;
export declare var newspaperOutline: string;
export declare var newspaperSharp: string;
export declare var notifications: string;
export declare var notificationsCircle: string;
export declare var notificationsCircleOutline: string;
export declare var notificationsCircleSharp: string;
export declare var notificationsOff: string;
export declare var notificationsOffCircle: string;
export declare var notificationsOffCircleOutline: string;
export declare var notificationsOffCircleSharp: string;
export declare var notificationsOffOutline: string;
export declare var notificationsOffSharp: string;
export declare var notificationsOutline: string;
export declare var notificationsSharp: string;
export declare var nuclear: string;
export declare var nuclearOutline: string;
export declare var nuclearSharp: string;
export declare var nutrition: string;
export declare var nutritionOutline: string;
export declare var nutritionSharp: string;
export declare var open: string;
export declare var openOutline: string;
export declare var openSharp: string;
export declare var options: string;
export declare var optionsOutline: string;
export declare var optionsSharp: string;
export declare var paperPlane: string;
export declare var paperPlaneOutline: string;
export declare var paperPlaneSharp: string;
export declare var partlySunny: string;
export declare var partlySunnyOutline: string;
export declare var partlySunnySharp: string;
export declare var pause: string;
export declare var pauseCircle: string;
export declare var pauseCircleOutline: string;
export declare var pauseCircleSharp: string;
export declare var pauseOutline: string;
export declare var pauseSharp: string;
export declare var paw: string;
export declare var pawOutline: string;
export declare var pawSharp: string;
export declare var pencil: string;
export declare var pencilOutline: string;
export declare var pencilSharp: string;
export declare var people: string;
export declare var peopleCircle: string;
export declare var peopleCircleOutline: string;
export declare var peopleCircleSharp: string;
export declare var peopleOutline: string;
export declare var peopleSharp: string;
export declare var person: string;
export declare var personAdd: string;
export declare var personAddOutline: string;
export declare var personAddSharp: string;
export declare var personCircle: string;
export declare var personCircleOutline: string;
export declare var personCircleSharp: string;
export declare var personOutline: string;
export declare var personRemove: string;
export declare var personRemoveOutline: string;
export declare var personRemoveSharp: string;
export declare var personSharp: string;
export declare var phoneLandscape: string;
export declare var phoneLandscapeOutline: string;
export declare var phoneLandscapeSharp: string;
export declare var phonePortrait: string;
export declare var phonePortraitOutline: string;
export declare var phonePortraitSharp: string;
export declare var pieChart: string;
export declare var pieChartOutline: string;
export declare var pieChartSharp: string;
export declare var pin: string;
export declare var pinOutline: string;
export declare var pinSharp: string;
export declare var pint: string;
export declare var pintOutline: string;
export declare var pintSharp: string;
export declare var pizza: string;
export declare var pizzaOutline: string;
export declare var pizzaSharp: string;
export declare var planet: string;
export declare var planetOutline: string;
export declare var planetSharp: string;
export declare var play: string;
export declare var playBack: string;
export declare var playBackCircle: string;
export declare var playBackCircleOutline: string;
export declare var playBackCircleSharp: string;
export declare var playBackOutline: string;
export declare var playBackSharp: string;
export declare var playCircle: string;
export declare var playCircleOutline: string;
export declare var playCircleSharp: string;
export declare var playForward: string;
export declare var playForwardCircle: string;
export declare var playForwardCircleOutline: string;
export declare var playForwardCircleSharp: string;
export declare var playForwardOutline: string;
export declare var playForwardSharp: string;
export declare var playOutline: string;
export declare var playSharp: string;
export declare var playSkipBack: string;
export declare var playSkipBackCircle: string;
export declare var playSkipBackCircleOutline: string;
export declare var playSkipBackCircleSharp: string;
export declare var playSkipBackOutline: string;
export declare var playSkipBackSharp: string;
export declare var playSkipForward: string;
export declare var playSkipForwardCircle: string;
export declare var playSkipForwardCircleOutline: string;
export declare var playSkipForwardCircleSharp: string;
export declare var playSkipForwardOutline: string;
export declare var playSkipForwardSharp: string;
export declare var podium: string;
export declare var podiumOutline: string;
export declare var podiumSharp: string;
export declare var power: string;
export declare var powerOutline: string;
export declare var powerSharp: string;
export declare var pricetag: string;
export declare var pricetagOutline: string;
export declare var pricetagSharp: string;
export declare var pricetags: string;
export declare var pricetagsOutline: string;
export declare var pricetagsSharp: string;
export declare var print: string;
export declare var printOutline: string;
export declare var printSharp: string;
export declare var prism: string;
export declare var prismOutline: string;
export declare var prismSharp: string;
export declare var pulse: string;
export declare var pulseOutline: string;
export declare var pulseSharp: string;
export declare var push: string;
export declare var pushOutline: string;
export declare var pushSharp: string;
export declare var qrCode: string;
export declare var qrCodeOutline: string;
export declare var qrCodeSharp: string;
export declare var radio: string;
export declare var radioButtonOff: string;
export declare var radioButtonOffOutline: string;
export declare var radioButtonOffSharp: string;
export declare var radioButtonOn: string;
export declare var radioButtonOnOutline: string;
export declare var radioButtonOnSharp: string;
export declare var radioOutline: string;
export declare var radioSharp: string;
export declare var rainy: string;
export declare var rainyOutline: string;
export declare var rainySharp: string;
export declare var reader: string;
export declare var readerOutline: string;
export declare var readerSharp: string;
export declare var receipt: string;
export declare var receiptOutline: string;
export declare var receiptSharp: string;
export declare var recording: string;
export declare var recordingOutline: string;
export declare var recordingSharp: string;
export declare var refresh: string;
export declare var refreshCircle: string;
export declare var refreshCircleOutline: string;
export declare var refreshCircleSharp: string;
export declare var refreshOutline: string;
export declare var refreshSharp: string;
export declare var reload: string;
export declare var reloadCircle: string;
export declare var reloadCircleOutline: string;
export declare var reloadCircleSharp: string;
export declare var reloadOutline: string;
export declare var reloadSharp: string;
export declare var remove: string;
export declare var removeCircle: string;
export declare var removeCircleOutline: string;
export declare var removeCircleSharp: string;
export declare var removeOutline: string;
export declare var removeSharp: string;
export declare var reorderFour: string;
export declare var reorderFourOutline: string;
export declare var reorderFourSharp: string;
export declare var reorderThree: string;
export declare var reorderThreeOutline: string;
export declare var reorderThreeSharp: string;
export declare var reorderTwo: string;
export declare var reorderTwoOutline: string;
export declare var reorderTwoSharp: string;
export declare var repeat: string;
export declare var repeatOutline: string;
export declare var repeatSharp: string;
export declare var resize: string;
export declare var resizeOutline: string;
export declare var resizeSharp: string;
export declare var restaurant: string;
export declare var restaurantOutline: string;
export declare var restaurantSharp: string;
export declare var returnDownBack: string;
export declare var returnDownBackOutline: string;
export declare var returnDownBackSharp: string;
export declare var returnDownForward: string;
export declare var returnDownForwardOutline: string;
export declare var returnDownForwardSharp: string;
export declare var returnUpBack: string;
export declare var returnUpBackOutline: string;
export declare var returnUpBackSharp: string;
export declare var returnUpForward: string;
export declare var returnUpForwardOutline: string;
export declare var returnUpForwardSharp: string;
export declare var ribbon: string;
export declare var ribbonOutline: string;
export declare var ribbonSharp: string;
export declare var rocket: string;
export declare var rocketOutline: string;
export declare var rocketSharp: string;
export declare var rose: string;
export declare var roseOutline: string;
export declare var roseSharp: string;
export declare var sad: string;
export declare var sadOutline: string;
export declare var sadSharp: string;
export declare var save: string;
export declare var saveOutline: string;
export declare var saveSharp: string;
export declare var scale: string;
export declare var scaleOutline: string;
export declare var scaleSharp: string;
export declare var scan: string;
export declare var scanCircle: string;
export declare var scanCircleOutline: string;
export declare var scanCircleSharp: string;
export declare var scanOutline: string;
export declare var scanSharp: string;
export declare var school: string;
export declare var schoolOutline: string;
export declare var schoolSharp: string;
export declare var search: string;
export declare var searchCircle: string;
export declare var searchCircleOutline: string;
export declare var searchCircleSharp: string;
export declare var searchOutline: string;
export declare var searchSharp: string;
export declare var send: string;
export declare var sendOutline: string;
export declare var sendSharp: string;
export declare var server: string;
export declare var serverOutline: string;
export declare var serverSharp: string;
export declare var settings: string;
export declare var settingsOutline: string;
export declare var settingsSharp: string;
export declare var shapes: string;
export declare var shapesOutline: string;
export declare var shapesSharp: string;
export declare var share: string;
export declare var shareOutline: string;
export declare var shareSharp: string;
export declare var shareSocial: string;
export declare var shareSocialOutline: string;
export declare var shareSocialSharp: string;
export declare var shield: string;
export declare var shieldCheckmark: string;
export declare var shieldCheckmarkOutline: string;
export declare var shieldCheckmarkSharp: string;
export declare var shieldHalf: string;
export declare var shieldHalfOutline: string;
export declare var shieldHalfSharp: string;
export declare var shieldOutline: string;
export declare var shieldSharp: string;
export declare var shirt: string;
export declare var shirtOutline: string;
export declare var shirtSharp: string;
export declare var shuffle: string;
export declare var shuffleOutline: string;
export declare var shuffleSharp: string;
export declare var skull: string;
export declare var skullOutline: string;
export declare var skullSharp: string;
export declare var snow: string;
export declare var snowOutline: string;
export declare var snowSharp: string;
export declare var sparkles: string;
export declare var sparklesOutline: string;
export declare var sparklesSharp: string;
export declare var speedometer: string;
export declare var speedometerOutline: string;
export declare var speedometerSharp: string;
export declare var square: string;
export declare var squareOutline: string;
export declare var squareSharp: string;
export declare var star: string;
export declare var starHalf: string;
export declare var starHalfOutline: string;
export declare var starHalfSharp: string;
export declare var starOutline: string;
export declare var starSharp: string;
export declare var statsChart: string;
export declare var statsChartOutline: string;
export declare var statsChartSharp: string;
export declare var stop: string;
export declare var stopCircle: string;
export declare var stopCircleOutline: string;
export declare var stopCircleSharp: string;
export declare var stopOutline: string;
export declare var stopSharp: string;
export declare var stopwatch: string;
export declare var stopwatchOutline: string;
export declare var stopwatchSharp: string;
export declare var storefront: string;
export declare var storefrontOutline: string;
export declare var storefrontSharp: string;
export declare var subway: string;
export declare var subwayOutline: string;
export declare var subwaySharp: string;
export declare var sunny: string;
export declare var sunnyOutline: string;
export declare var sunnySharp: string;
export declare var swapHorizontal: string;
export declare var swapHorizontalOutline: string;
export declare var swapHorizontalSharp: string;
export declare var swapVertical: string;
export declare var swapVerticalOutline: string;
export declare var swapVerticalSharp: string;
export declare var sync: string;
export declare var syncCircle: string;
export declare var syncCircleOutline: string;
export declare var syncCircleSharp: string;
export declare var syncOutline: string;
export declare var syncSharp: string;
export declare var tabletLandscape: string;
export declare var tabletLandscapeOutline: string;
export declare var tabletLandscapeSharp: string;
export declare var tabletPortrait: string;
export declare var tabletPortraitOutline: string;
export declare var tabletPortraitSharp: string;
export declare var telescope: string;
export declare var telescopeOutline: string;
export declare var telescopeSharp: string;
export declare var tennisball: string;
export declare var tennisballOutline: string;
export declare var tennisballSharp: string;
export declare var terminal: string;
export declare var terminalOutline: string;
export declare var terminalSharp: string;
export declare var text: string;
export declare var textOutline: string;
export declare var textSharp: string;
export declare var thermometer: string;
export declare var thermometerOutline: string;
export declare var thermometerSharp: string;
export declare var thumbsDown: string;
export declare var thumbsDownOutline: string;
export declare var thumbsDownSharp: string;
export declare var thumbsUp: string;
export declare var thumbsUpOutline: string;
export declare var thumbsUpSharp: string;
export declare var thunderstorm: string;
export declare var thunderstormOutline: string;
export declare var thunderstormSharp: string;
export declare var ticket: string;
export declare var ticketOutline: string;
export declare var ticketSharp: string;
export declare var time: string;
export declare var timeOutline: string;
export declare var timeSharp: string;
export declare var timer: string;
export declare var timerOutline: string;
export declare var timerSharp: string;
export declare var today: string;
export declare var todayOutline: string;
export declare var todaySharp: string;
export declare var toggle: string;
export declare var toggleOutline: string;
export declare var toggleSharp: string;
export declare var trailSign: string;
export declare var trailSignOutline: string;
export declare var trailSignSharp: string;
export declare var train: string;
export declare var trainOutline: string;
export declare var trainSharp: string;
export declare var transgender: string;
export declare var transgenderOutline: string;
export declare var transgenderSharp: string;
export declare var trash: string;
export declare var trashBin: string;
export declare var trashBinOutline: string;
export declare var trashBinSharp: string;
export declare var trashOutline: string;
export declare var trashSharp: string;
export declare var trendingDown: string;
export declare var trendingDownOutline: string;
export declare var trendingDownSharp: string;
export declare var trendingUp: string;
export declare var trendingUpOutline: string;
export declare var trendingUpSharp: string;
export declare var triangle: string;
export declare var triangleOutline: string;
export declare var triangleSharp: string;
export declare var trophy: string;
export declare var trophyOutline: string;
export declare var trophySharp: string;
export declare var tv: string;
export declare var tvOutline: string;
export declare var tvSharp: string;
export declare var umbrella: string;
export declare var umbrellaOutline: string;
export declare var umbrellaSharp: string;
export declare var unlink: string;
export declare var unlinkOutline: string;
export declare var unlinkSharp: string;
export declare var videocam: string;
export declare var videocamOff: string;
export declare var videocamOffOutline: string;
export declare var videocamOffSharp: string;
export declare var videocamOutline: string;
export declare var videocamSharp: string;
export declare var volumeHigh: string;
export declare var volumeHighOutline: string;
export declare var volumeHighSharp: string;
export declare var volumeLow: string;
export declare var volumeLowOutline: string;
export declare var volumeLowSharp: string;
export declare var volumeMedium: string;
export declare var volumeMediumOutline: string;
export declare var volumeMediumSharp: string;
export declare var volumeMute: string;
export declare var volumeMuteOutline: string;
export declare var volumeMuteSharp: string;
export declare var volumeOff: string;
export declare var volumeOffOutline: string;
export declare var volumeOffSharp: string;
export declare var walk: string;
export declare var walkOutline: string;
export declare var walkSharp: string;
export declare var wallet: string;
export declare var walletOutline: string;
export declare var walletSharp: string;
export declare var warning: string;
export declare var warningOutline: string;
export declare var warningSharp: string;
export declare var watch: string;
export declare var watchOutline: string;
export declare var watchSharp: string;
export declare var water: string;
export declare var waterOutline: string;
export declare var waterSharp: string;
export declare var wifi: string;
export declare var wifiOutline: string;
export declare var wifiSharp: string;
export declare var wine: string;
export declare var wineOutline: string;
export declare var wineSharp: string;
export declare var woman: string;
export declare var womanOutline: string;
export declare var womanSharp: string;
