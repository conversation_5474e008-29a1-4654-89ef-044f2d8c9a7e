{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host, i as getElement, d as createEvent } from './index-28849c61.js';\nimport { r as raf, t as transitionEndAsync, a as addEventListener, b as removeEventListener, g as getElementRoot } from './helpers-da915de8.js';\nimport { l as chevronDown } from './index-e2cf2ceb.js';\nimport { c as config, b as getIonMode } from './ionic-global-c81d82ab.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nconst accordionIosCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}:host(.accordion-next) ::slotted(ion-item[slot=header]){--border-width:0.55px 0px 0.55px 0px}\";\nconst IonAccordionIosStyle0 = accordionIosCss;\nconst accordionMdCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}\";\nconst IonAccordionMdStyle0 = accordionMdCss;\nconst Accordion = class {\n  constructor(hostRef) {\n    var _this = this;\n    registerInstance(this, hostRef);\n    this.updateListener = () => this.updateState(false);\n    this.setItemDefaults = () => {\n      const ionItem = this.getSlottedHeaderIonItem();\n      if (!ionItem) {\n        return;\n      }\n      /**\n       * For a11y purposes, we make\n       * the ion-item a button so users\n       * can tab to it and use keyboard\n       * navigation to get around.\n       */\n      ionItem.button = true;\n      ionItem.detail = false;\n      /**\n       * By default, the lines in an\n       * item should be full here, but\n       * only do that if a user has\n       * not explicitly overridden them\n       */\n      if (ionItem.lines === undefined) {\n        ionItem.lines = 'full';\n      }\n    };\n    this.getSlottedHeaderIonItem = () => {\n      const {\n        headerEl\n      } = this;\n      if (!headerEl) {\n        return;\n      }\n      /**\n       * Get the first ion-item\n       * slotted in the header slot\n       */\n      const slot = headerEl.querySelector('slot');\n      if (!slot) {\n        return;\n      }\n      // This is not defined in unit tests\n      if (slot.assignedElements === undefined) return;\n      return slot.assignedElements().find(el => el.tagName === 'ION-ITEM');\n    };\n    this.setAria = (expanded = false) => {\n      const ionItem = this.getSlottedHeaderIonItem();\n      if (!ionItem) {\n        return;\n      }\n      /**\n       * Get the native <button> element inside of\n       * ion-item because that is what will be focused\n       */\n      const root = getElementRoot(ionItem);\n      const button = root.querySelector('button');\n      if (!button) {\n        return;\n      }\n      button.setAttribute('aria-expanded', `${expanded}`);\n    };\n    this.slotToggleIcon = () => {\n      const ionItem = this.getSlottedHeaderIonItem();\n      if (!ionItem) {\n        return;\n      }\n      const {\n        toggleIconSlot,\n        toggleIcon\n      } = this;\n      /**\n       * Check if there already is a toggle icon.\n       * If so, do not add another one.\n       */\n      const existingToggleIcon = ionItem.querySelector('.ion-accordion-toggle-icon');\n      if (existingToggleIcon) {\n        return;\n      }\n      const iconEl = document.createElement('ion-icon');\n      iconEl.slot = toggleIconSlot;\n      iconEl.lazy = false;\n      iconEl.classList.add('ion-accordion-toggle-icon');\n      iconEl.icon = toggleIcon;\n      iconEl.setAttribute('aria-hidden', 'true');\n      ionItem.appendChild(iconEl);\n    };\n    this.expandAccordion = (initialUpdate = false) => {\n      const {\n        contentEl,\n        contentElWrapper\n      } = this;\n      if (initialUpdate || contentEl === undefined || contentElWrapper === undefined) {\n        this.state = 4 /* AccordionState.Expanded */;\n        return;\n      }\n      if (this.state === 4 /* AccordionState.Expanded */) {\n        return;\n      }\n      if (this.currentRaf !== undefined) {\n        cancelAnimationFrame(this.currentRaf);\n      }\n      if (this.shouldAnimate()) {\n        raf(() => {\n          this.state = 8 /* AccordionState.Expanding */;\n          this.currentRaf = raf(/*#__PURE__*/_asyncToGenerator(function* () {\n            const contentHeight = contentElWrapper.offsetHeight;\n            const waitForTransition = transitionEndAsync(contentEl, 2000);\n            contentEl.style.setProperty('max-height', `${contentHeight}px`);\n            yield waitForTransition;\n            _this.state = 4 /* AccordionState.Expanded */;\n            contentEl.style.removeProperty('max-height');\n          }));\n        });\n      } else {\n        this.state = 4 /* AccordionState.Expanded */;\n      }\n    };\n    this.collapseAccordion = (initialUpdate = false) => {\n      const {\n        contentEl\n      } = this;\n      if (initialUpdate || contentEl === undefined) {\n        this.state = 1 /* AccordionState.Collapsed */;\n        return;\n      }\n      if (this.state === 1 /* AccordionState.Collapsed */) {\n        return;\n      }\n      if (this.currentRaf !== undefined) {\n        cancelAnimationFrame(this.currentRaf);\n      }\n      if (this.shouldAnimate()) {\n        this.currentRaf = raf(/*#__PURE__*/_asyncToGenerator(function* () {\n          const contentHeight = contentEl.offsetHeight;\n          contentEl.style.setProperty('max-height', `${contentHeight}px`);\n          raf(/*#__PURE__*/_asyncToGenerator(function* () {\n            const waitForTransition = transitionEndAsync(contentEl, 2000);\n            _this.state = 2 /* AccordionState.Collapsing */;\n            yield waitForTransition;\n            _this.state = 1 /* AccordionState.Collapsed */;\n            contentEl.style.removeProperty('max-height');\n          }));\n        }));\n      } else {\n        this.state = 1 /* AccordionState.Collapsed */;\n      }\n    };\n    /**\n     * Helper function to determine if\n     * something should animate.\n     * If prefers-reduced-motion is set\n     * then we should not animate, regardless\n     * of what is set in the config.\n     */\n    this.shouldAnimate = () => {\n      if (typeof window === 'undefined') {\n        return false;\n      }\n      const prefersReducedMotion = matchMedia('(prefers-reduced-motion: reduce)').matches;\n      if (prefersReducedMotion) {\n        return false;\n      }\n      const animated = config.get('animated', true);\n      if (!animated) {\n        return false;\n      }\n      if (this.accordionGroupEl && !this.accordionGroupEl.animated) {\n        return false;\n      }\n      return true;\n    };\n    this.updateState = /*#__PURE__*/_asyncToGenerator(function* (initialUpdate = false) {\n      const accordionGroup = _this.accordionGroupEl;\n      const accordionValue = _this.value;\n      if (!accordionGroup) {\n        return;\n      }\n      const value = accordionGroup.value;\n      const shouldExpand = Array.isArray(value) ? value.includes(accordionValue) : value === accordionValue;\n      if (shouldExpand) {\n        _this.expandAccordion(initialUpdate);\n        _this.isNext = _this.isPrevious = false;\n      } else {\n        _this.collapseAccordion(initialUpdate);\n        /**\n         * When using popout or inset,\n         * the collapsed accordion items\n         * may need additional border radius\n         * applied. Check to see if the\n         * next or previous accordion is selected.\n         */\n        const nextAccordion = _this.getNextSibling();\n        const nextAccordionValue = nextAccordion === null || nextAccordion === void 0 ? void 0 : nextAccordion.value;\n        if (nextAccordionValue !== undefined) {\n          _this.isPrevious = Array.isArray(value) ? value.includes(nextAccordionValue) : value === nextAccordionValue;\n        }\n        const previousAccordion = _this.getPreviousSibling();\n        const previousAccordionValue = previousAccordion === null || previousAccordion === void 0 ? void 0 : previousAccordion.value;\n        if (previousAccordionValue !== undefined) {\n          _this.isNext = Array.isArray(value) ? value.includes(previousAccordionValue) : value === previousAccordionValue;\n        }\n      }\n    });\n    this.getNextSibling = () => {\n      if (!this.el) {\n        return;\n      }\n      const nextSibling = this.el.nextElementSibling;\n      if ((nextSibling === null || nextSibling === void 0 ? void 0 : nextSibling.tagName) !== 'ION-ACCORDION') {\n        return;\n      }\n      return nextSibling;\n    };\n    this.getPreviousSibling = () => {\n      if (!this.el) {\n        return;\n      }\n      const previousSibling = this.el.previousElementSibling;\n      if ((previousSibling === null || previousSibling === void 0 ? void 0 : previousSibling.tagName) !== 'ION-ACCORDION') {\n        return;\n      }\n      return previousSibling;\n    };\n    this.state = 1 /* AccordionState.Collapsed */;\n    this.isNext = false;\n    this.isPrevious = false;\n    this.value = `ion-accordion-${accordionIds++}`;\n    this.disabled = false;\n    this.readonly = false;\n    this.toggleIcon = chevronDown;\n    this.toggleIconSlot = 'end';\n  }\n  valueChanged() {\n    this.updateState();\n  }\n  connectedCallback() {\n    var _a;\n    const accordionGroupEl = this.accordionGroupEl = (_a = this.el) === null || _a === void 0 ? void 0 : _a.closest('ion-accordion-group');\n    if (accordionGroupEl) {\n      this.updateState(true);\n      addEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n    }\n  }\n  disconnectedCallback() {\n    const accordionGroupEl = this.accordionGroupEl;\n    if (accordionGroupEl) {\n      removeEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n    }\n  }\n  componentDidLoad() {\n    this.setItemDefaults();\n    this.slotToggleIcon();\n    /**\n     * We need to wait a tick because we\n     * just set ionItem.button = true and\n     * the button has not have been rendered yet.\n     */\n    raf(() => {\n      /**\n       * Set aria label on button inside of ion-item\n       * once the inner content has been rendered.\n       */\n      const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n      this.setAria(expanded);\n    });\n  }\n  toggleExpanded() {\n    const {\n      accordionGroupEl,\n      disabled,\n      readonly,\n      value,\n      state\n    } = this;\n    if (disabled || readonly) return;\n    if (accordionGroupEl) {\n      /**\n       * Because the accordion group may or may\n       * not allow multiple accordions open, we\n       * need to request the toggling of this\n       * accordion and the accordion group will\n       * make the decision on whether or not\n       * to allow it.\n       */\n      const expand = state === 1 /* AccordionState.Collapsed */ || state === 2 /* AccordionState.Collapsing */;\n      accordionGroupEl.requestAccordionToggle(value, expand);\n    }\n  }\n  render() {\n    const {\n      disabled,\n      readonly\n    } = this;\n    const mode = getIonMode(this);\n    const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n    const headerPart = expanded ? 'header expanded' : 'header';\n    const contentPart = expanded ? 'content expanded' : 'content';\n    this.setAria(expanded);\n    return h(Host, {\n      key: '073e1d02c18dcbc20c68648426e87c14750c031d',\n      class: {\n        [mode]: true,\n        'accordion-expanding': this.state === 8 /* AccordionState.Expanding */,\n        'accordion-expanded': this.state === 4 /* AccordionState.Expanded */,\n        'accordion-collapsing': this.state === 2 /* AccordionState.Collapsing */,\n        'accordion-collapsed': this.state === 1 /* AccordionState.Collapsed */,\n        'accordion-next': this.isNext,\n        'accordion-previous': this.isPrevious,\n        'accordion-disabled': disabled,\n        'accordion-readonly': readonly,\n        'accordion-animated': this.shouldAnimate()\n      }\n    }, h(\"div\", {\n      key: '9b4cf326de8bb6b4033992903c0c1bfd7eea9bcc',\n      onClick: () => this.toggleExpanded(),\n      id: \"header\",\n      part: headerPart,\n      \"aria-controls\": \"content\",\n      ref: headerEl => this.headerEl = headerEl\n    }, h(\"slot\", {\n      key: '464c32a37f64655eacf4218284214f5f30b14a1e',\n      name: \"header\"\n    })), h(\"div\", {\n      key: '8bb52e6a62d7de0106b253201a89a32e79d9a594',\n      id: \"content\",\n      part: contentPart,\n      role: \"region\",\n      \"aria-labelledby\": \"header\",\n      ref: contentEl => this.contentEl = contentEl\n    }, h(\"div\", {\n      key: '1d9dfd952ad493754aaeea7a8f625b33c2dd90a0',\n      id: \"content-wrapper\",\n      ref: contentElWrapper => this.contentElWrapper = contentElWrapper\n    }, h(\"slot\", {\n      key: '970dfbc55a612d739d0ca3b7b1a08e5c96d0c479',\n      name: \"content\"\n    }))));\n  }\n  static get delegatesFocus() {\n    return true;\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nlet accordionIds = 0;\nAccordion.style = {\n  ios: IonAccordionIosStyle0,\n  md: IonAccordionMdStyle0\n};\nconst accordionGroupIosCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){border-bottom:none}\";\nconst IonAccordionGroupIosStyle0 = accordionGroupIosCss;\nconst accordionGroupMdCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion){-webkit-box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;border-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-previous){border-end-end-radius:6px;border-end-start-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-next){border-start-start-radius:6px;border-start-end-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion):first-of-type{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\nconst IonAccordionGroupMdStyle0 = accordionGroupMdCss;\nconst AccordionGroup = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n    this.animated = true;\n    this.multiple = undefined;\n    this.value = undefined;\n    this.disabled = false;\n    this.readonly = false;\n    this.expand = 'compact';\n  }\n  valueChanged() {\n    const {\n      value,\n      multiple\n    } = this;\n    if (!multiple && Array.isArray(value)) {\n      /**\n       * We do some processing on the `value` array so\n       * that it looks more like an array when logged to\n       * the console.\n       * Example given ['a', 'b']\n       * Default toString() behavior: a,b\n       * Custom behavior: ['a', 'b']\n       */\n      printIonWarning(`ion-accordion-group was passed an array of values, but multiple=\"false\". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the \"value\" property when multiple=\"false\".\n\n  Value Passed: [${value.map(v => `'${v}'`).join(', ')}]\n`, this.el);\n    }\n    /**\n     * Do not use `value` here as that will be\n     * not account for the adjustment we make above.\n     */\n    this.ionValueChange.emit({\n      value: this.value\n    });\n  }\n  disabledChanged() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        disabled\n      } = _this2;\n      const accordions = yield _this2.getAccordions();\n      for (const accordion of accordions) {\n        accordion.disabled = disabled;\n      }\n    })();\n  }\n  readonlyChanged() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        readonly\n      } = _this3;\n      const accordions = yield _this3.getAccordions();\n      for (const accordion of accordions) {\n        accordion.readonly = readonly;\n      }\n    })();\n  }\n  onKeydown(ev) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const activeElement = document.activeElement;\n      if (!activeElement) {\n        return;\n      }\n      /**\n       * Make sure focus is in the header, not the body, of the accordion. This ensures\n       * that if there are any interactable elements in the body, their keyboard\n       * interaction doesn't get stolen by the accordion. Example: using up/down keys\n       * in ion-textarea.\n       */\n      const activeAccordionHeader = activeElement.closest('ion-accordion [slot=\"header\"]');\n      if (!activeAccordionHeader) {\n        return;\n      }\n      const accordionEl = activeElement.tagName === 'ION-ACCORDION' ? activeElement : activeElement.closest('ion-accordion');\n      if (!accordionEl) {\n        return;\n      }\n      const closestGroup = accordionEl.closest('ion-accordion-group');\n      if (closestGroup !== _this4.el) {\n        return;\n      }\n      // If the active accordion is not in the current array of accordions, do not do anything\n      const accordions = yield _this4.getAccordions();\n      const startingIndex = accordions.findIndex(a => a === accordionEl);\n      if (startingIndex === -1) {\n        return;\n      }\n      let accordion;\n      if (ev.key === 'ArrowDown') {\n        accordion = _this4.findNextAccordion(accordions, startingIndex);\n      } else if (ev.key === 'ArrowUp') {\n        accordion = _this4.findPreviousAccordion(accordions, startingIndex);\n      } else if (ev.key === 'Home') {\n        accordion = accordions[0];\n      } else if (ev.key === 'End') {\n        accordion = accordions[accordions.length - 1];\n      }\n      if (accordion !== undefined && accordion !== activeElement) {\n        accordion.focus();\n      }\n    })();\n  }\n  componentDidLoad() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (_this5.disabled) {\n        _this5.disabledChanged();\n      }\n      if (_this5.readonly) {\n        _this5.readonlyChanged();\n      }\n      /**\n       * When binding values in frameworks such as Angular\n       * it is possible for the value to be set after the Web Component\n       * initializes but before the value watcher is set up in Stencil.\n       * As a result, the watcher callback may not be fired.\n       * We work around this by manually calling the watcher\n       * callback when the component has loaded and the watcher\n       * is configured.\n       */\n      _this5.valueChanged();\n    })();\n  }\n  /**\n   * Sets the value property and emits ionChange.\n   * This should only be called when the user interacts\n   * with the accordion and not for any update\n   * to the value property. The exception is when\n   * the app sets the value of a single-select\n   * accordion group to an array.\n   */\n  setValue(accordionValue) {\n    const value = this.value = accordionValue;\n    this.ionChange.emit({\n      value\n    });\n  }\n  /**\n   * This method is used to ensure that the value\n   * of ion-accordion-group is being set in a valid\n   * way. This method should only be called in\n   * response to a user generated action.\n   * @internal\n   */\n  requestAccordionToggle(accordionValue, accordionExpand) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        multiple,\n        value,\n        readonly,\n        disabled\n      } = _this6;\n      if (readonly || disabled) {\n        return;\n      }\n      if (accordionExpand) {\n        /**\n         * If group accepts multiple values\n         * check to see if value is already in\n         * in values array. If not, add it\n         * to the array.\n         */\n        if (multiple) {\n          const groupValue = value !== null && value !== void 0 ? value : [];\n          const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n          const valueExists = processedValue.find(v => v === accordionValue);\n          if (valueExists === undefined && accordionValue !== undefined) {\n            _this6.setValue([...processedValue, accordionValue]);\n          }\n        } else {\n          _this6.setValue(accordionValue);\n        }\n      } else {\n        /**\n         * If collapsing accordion, either filter the value\n         * out of the values array or unset the value.\n         */\n        if (multiple) {\n          const groupValue = value !== null && value !== void 0 ? value : [];\n          const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n          _this6.setValue(processedValue.filter(v => v !== accordionValue));\n        } else {\n          _this6.setValue(undefined);\n        }\n      }\n    })();\n  }\n  findNextAccordion(accordions, startingIndex) {\n    const nextAccordion = accordions[startingIndex + 1];\n    if (nextAccordion === undefined) {\n      return accordions[0];\n    }\n    return nextAccordion;\n  }\n  findPreviousAccordion(accordions, startingIndex) {\n    const prevAccordion = accordions[startingIndex - 1];\n    if (prevAccordion === undefined) {\n      return accordions[accordions.length - 1];\n    }\n    return prevAccordion;\n  }\n  /**\n   * @internal\n   */\n  getAccordions() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      return Array.from(_this7.el.querySelectorAll(':scope > ion-accordion'));\n    })();\n  }\n  render() {\n    const {\n      disabled,\n      readonly,\n      expand\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '82f3e77066fabb4736638ee4c487ad56efd39c63',\n      class: {\n        [mode]: true,\n        'accordion-group-disabled': disabled,\n        'accordion-group-readonly': readonly,\n        [`accordion-group-expand-${expand}`]: true\n      },\n      role: \"presentation\"\n    }, h(\"slot\", {\n      key: 'a3c791ea887fc640b512f81d429be465ae902b3d'\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"value\": [\"valueChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"readonly\": [\"readonlyChanged\"]\n    };\n  }\n};\nAccordionGroup.style = {\n  ios: IonAccordionGroupIosStyle0,\n  md: IonAccordionGroupMdStyle0\n};\nexport { Accordion as ion_accordion, AccordionGroup as ion_accordion_group };", "map": {"version": 3, "names": ["r", "registerInstance", "h", "f", "Host", "i", "getElement", "d", "createEvent", "raf", "t", "transitionEndAsync", "a", "addEventListener", "b", "removeEventListener", "g", "getElementRoot", "l", "chevronDown", "c", "config", "getIonMode", "p", "printIonWarning", "accordionIosCss", "IonAccordionIosStyle0", "accordionMdCss", "IonAccordionMdStyle0", "Accordion", "constructor", "hostRef", "_this", "updateListener", "updateState", "setItemDefaults", "ionItem", "getSlottedHeaderIonItem", "button", "detail", "lines", "undefined", "headerEl", "slot", "querySelector", "assignedElements", "find", "el", "tagName", "setAria", "expanded", "root", "setAttribute", "slotToggleIcon", "toggleIconSlot", "toggleIcon", "existingToggleIcon", "iconEl", "document", "createElement", "lazy", "classList", "add", "icon", "append<PERSON><PERSON><PERSON>", "expandAccordion", "initialUpdate", "contentEl", "contentElWrapper", "state", "currentRaf", "cancelAnimationFrame", "shouldAnimate", "_asyncToGenerator", "contentHeight", "offsetHeight", "waitForTransition", "style", "setProperty", "removeProperty", "collapseAccordion", "window", "prefersReducedMotion", "matchMedia", "matches", "animated", "get", "accordionGroupEl", "accordionGroup", "accordionValue", "value", "shouldExpand", "Array", "isArray", "includes", "isNext", "isPrevious", "nextAccordion", "getNextSibling", "nextAccordionValue", "previousAccordion", "getPrevious<PERSON><PERSON>ling", "previousAccordionValue", "nextS<PERSON>ling", "nextElement<PERSON><PERSON>ling", "previousSibling", "previousElementSibling", "accordionIds", "disabled", "readonly", "valueChanged", "connectedCallback", "_a", "closest", "disconnectedCallback", "componentDidLoad", "toggleExpanded", "expand", "requestAccordion<PERSON><PERSON>gle", "render", "mode", "headerPart", "contentPart", "key", "class", "onClick", "id", "part", "ref", "name", "role", "delegatesFocus", "watchers", "ios", "md", "accordionGroupIosCss", "IonAccordionGroupIosStyle0", "accordionGroupMdCss", "IonAccordionGroupMdStyle0", "AccordionGroup", "ionChange", "ionValueChange", "multiple", "map", "v", "join", "emit", "disabled<PERSON><PERSON>ed", "_this2", "accordions", "getAccordions", "accordion", "readonly<PERSON><PERSON>ed", "_this3", "onKeydown", "ev", "_this4", "activeElement", "activeAccordionHeader", "accordion<PERSON>l", "closestGroup", "startingIndex", "findIndex", "findNextAccordion", "find<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "focus", "_this5", "setValue", "accordionExpand", "_this6", "groupValue", "processedValue", "valueExists", "filter", "prevAccordion", "_this7", "from", "querySelectorAll", "ion_accordion", "ion_accordion_group"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/ion-accordion_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, f as Host, i as getElement, d as createEvent } from './index-28849c61.js';\nimport { r as raf, t as transitionEndAsync, a as addEventListener, b as removeEventListener, g as getElementRoot } from './helpers-da915de8.js';\nimport { l as chevronDown } from './index-e2cf2ceb.js';\nimport { c as config, b as getIonMode } from './ionic-global-c81d82ab.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\n\nconst accordionIosCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}:host(.accordion-next) ::slotted(ion-item[slot=header]){--border-width:0.55px 0px 0.55px 0px}\";\nconst IonAccordionIosStyle0 = accordionIosCss;\n\nconst accordionMdCss = \":host{display:block;position:relative;width:100%;background-color:var(--ion-background-color, #ffffff);overflow:hidden;z-index:0}:host(.accordion-expanding) ::slotted(ion-item[slot=header]),:host(.accordion-expanded) ::slotted(ion-item[slot=header]){--border-width:0px}:host(.accordion-animated){-webkit-transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:all 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}:host(.accordion-animated) #content{-webkit-transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1);transition:max-height 300ms cubic-bezier(0.25, 0.8, 0.5, 1)}#content{overflow:hidden;will-change:max-height}:host(.accordion-collapsing) #content{max-height:0 !important}:host(.accordion-collapsed) #content{display:none}:host(.accordion-expanding) #content{max-height:0}:host(.accordion-expanding) #content-wrapper{overflow:auto}:host(.accordion-disabled) #header,:host(.accordion-readonly) #header,:host(.accordion-disabled) #content,:host(.accordion-readonly) #content{pointer-events:none}:host(.accordion-disabled) #header,:host(.accordion-disabled) #content{opacity:0.4}@media (prefers-reduced-motion: reduce){:host,#content{-webkit-transition:none !important;transition:none !important}}\";\nconst IonAccordionMdStyle0 = accordionMdCss;\n\nconst Accordion = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.updateListener = () => this.updateState(false);\n        this.setItemDefaults = () => {\n            const ionItem = this.getSlottedHeaderIonItem();\n            if (!ionItem) {\n                return;\n            }\n            /**\n             * For a11y purposes, we make\n             * the ion-item a button so users\n             * can tab to it and use keyboard\n             * navigation to get around.\n             */\n            ionItem.button = true;\n            ionItem.detail = false;\n            /**\n             * By default, the lines in an\n             * item should be full here, but\n             * only do that if a user has\n             * not explicitly overridden them\n             */\n            if (ionItem.lines === undefined) {\n                ionItem.lines = 'full';\n            }\n        };\n        this.getSlottedHeaderIonItem = () => {\n            const { headerEl } = this;\n            if (!headerEl) {\n                return;\n            }\n            /**\n             * Get the first ion-item\n             * slotted in the header slot\n             */\n            const slot = headerEl.querySelector('slot');\n            if (!slot) {\n                return;\n            }\n            // This is not defined in unit tests\n            if (slot.assignedElements === undefined)\n                return;\n            return slot.assignedElements().find((el) => el.tagName === 'ION-ITEM');\n        };\n        this.setAria = (expanded = false) => {\n            const ionItem = this.getSlottedHeaderIonItem();\n            if (!ionItem) {\n                return;\n            }\n            /**\n             * Get the native <button> element inside of\n             * ion-item because that is what will be focused\n             */\n            const root = getElementRoot(ionItem);\n            const button = root.querySelector('button');\n            if (!button) {\n                return;\n            }\n            button.setAttribute('aria-expanded', `${expanded}`);\n        };\n        this.slotToggleIcon = () => {\n            const ionItem = this.getSlottedHeaderIonItem();\n            if (!ionItem) {\n                return;\n            }\n            const { toggleIconSlot, toggleIcon } = this;\n            /**\n             * Check if there already is a toggle icon.\n             * If so, do not add another one.\n             */\n            const existingToggleIcon = ionItem.querySelector('.ion-accordion-toggle-icon');\n            if (existingToggleIcon) {\n                return;\n            }\n            const iconEl = document.createElement('ion-icon');\n            iconEl.slot = toggleIconSlot;\n            iconEl.lazy = false;\n            iconEl.classList.add('ion-accordion-toggle-icon');\n            iconEl.icon = toggleIcon;\n            iconEl.setAttribute('aria-hidden', 'true');\n            ionItem.appendChild(iconEl);\n        };\n        this.expandAccordion = (initialUpdate = false) => {\n            const { contentEl, contentElWrapper } = this;\n            if (initialUpdate || contentEl === undefined || contentElWrapper === undefined) {\n                this.state = 4 /* AccordionState.Expanded */;\n                return;\n            }\n            if (this.state === 4 /* AccordionState.Expanded */) {\n                return;\n            }\n            if (this.currentRaf !== undefined) {\n                cancelAnimationFrame(this.currentRaf);\n            }\n            if (this.shouldAnimate()) {\n                raf(() => {\n                    this.state = 8 /* AccordionState.Expanding */;\n                    this.currentRaf = raf(async () => {\n                        const contentHeight = contentElWrapper.offsetHeight;\n                        const waitForTransition = transitionEndAsync(contentEl, 2000);\n                        contentEl.style.setProperty('max-height', `${contentHeight}px`);\n                        await waitForTransition;\n                        this.state = 4 /* AccordionState.Expanded */;\n                        contentEl.style.removeProperty('max-height');\n                    });\n                });\n            }\n            else {\n                this.state = 4 /* AccordionState.Expanded */;\n            }\n        };\n        this.collapseAccordion = (initialUpdate = false) => {\n            const { contentEl } = this;\n            if (initialUpdate || contentEl === undefined) {\n                this.state = 1 /* AccordionState.Collapsed */;\n                return;\n            }\n            if (this.state === 1 /* AccordionState.Collapsed */) {\n                return;\n            }\n            if (this.currentRaf !== undefined) {\n                cancelAnimationFrame(this.currentRaf);\n            }\n            if (this.shouldAnimate()) {\n                this.currentRaf = raf(async () => {\n                    const contentHeight = contentEl.offsetHeight;\n                    contentEl.style.setProperty('max-height', `${contentHeight}px`);\n                    raf(async () => {\n                        const waitForTransition = transitionEndAsync(contentEl, 2000);\n                        this.state = 2 /* AccordionState.Collapsing */;\n                        await waitForTransition;\n                        this.state = 1 /* AccordionState.Collapsed */;\n                        contentEl.style.removeProperty('max-height');\n                    });\n                });\n            }\n            else {\n                this.state = 1 /* AccordionState.Collapsed */;\n            }\n        };\n        /**\n         * Helper function to determine if\n         * something should animate.\n         * If prefers-reduced-motion is set\n         * then we should not animate, regardless\n         * of what is set in the config.\n         */\n        this.shouldAnimate = () => {\n            if (typeof window === 'undefined') {\n                return false;\n            }\n            const prefersReducedMotion = matchMedia('(prefers-reduced-motion: reduce)').matches;\n            if (prefersReducedMotion) {\n                return false;\n            }\n            const animated = config.get('animated', true);\n            if (!animated) {\n                return false;\n            }\n            if (this.accordionGroupEl && !this.accordionGroupEl.animated) {\n                return false;\n            }\n            return true;\n        };\n        this.updateState = async (initialUpdate = false) => {\n            const accordionGroup = this.accordionGroupEl;\n            const accordionValue = this.value;\n            if (!accordionGroup) {\n                return;\n            }\n            const value = accordionGroup.value;\n            const shouldExpand = Array.isArray(value) ? value.includes(accordionValue) : value === accordionValue;\n            if (shouldExpand) {\n                this.expandAccordion(initialUpdate);\n                this.isNext = this.isPrevious = false;\n            }\n            else {\n                this.collapseAccordion(initialUpdate);\n                /**\n                 * When using popout or inset,\n                 * the collapsed accordion items\n                 * may need additional border radius\n                 * applied. Check to see if the\n                 * next or previous accordion is selected.\n                 */\n                const nextAccordion = this.getNextSibling();\n                const nextAccordionValue = nextAccordion === null || nextAccordion === void 0 ? void 0 : nextAccordion.value;\n                if (nextAccordionValue !== undefined) {\n                    this.isPrevious = Array.isArray(value) ? value.includes(nextAccordionValue) : value === nextAccordionValue;\n                }\n                const previousAccordion = this.getPreviousSibling();\n                const previousAccordionValue = previousAccordion === null || previousAccordion === void 0 ? void 0 : previousAccordion.value;\n                if (previousAccordionValue !== undefined) {\n                    this.isNext = Array.isArray(value) ? value.includes(previousAccordionValue) : value === previousAccordionValue;\n                }\n            }\n        };\n        this.getNextSibling = () => {\n            if (!this.el) {\n                return;\n            }\n            const nextSibling = this.el.nextElementSibling;\n            if ((nextSibling === null || nextSibling === void 0 ? void 0 : nextSibling.tagName) !== 'ION-ACCORDION') {\n                return;\n            }\n            return nextSibling;\n        };\n        this.getPreviousSibling = () => {\n            if (!this.el) {\n                return;\n            }\n            const previousSibling = this.el.previousElementSibling;\n            if ((previousSibling === null || previousSibling === void 0 ? void 0 : previousSibling.tagName) !== 'ION-ACCORDION') {\n                return;\n            }\n            return previousSibling;\n        };\n        this.state = 1 /* AccordionState.Collapsed */;\n        this.isNext = false;\n        this.isPrevious = false;\n        this.value = `ion-accordion-${accordionIds++}`;\n        this.disabled = false;\n        this.readonly = false;\n        this.toggleIcon = chevronDown;\n        this.toggleIconSlot = 'end';\n    }\n    valueChanged() {\n        this.updateState();\n    }\n    connectedCallback() {\n        var _a;\n        const accordionGroupEl = (this.accordionGroupEl = (_a = this.el) === null || _a === void 0 ? void 0 : _a.closest('ion-accordion-group'));\n        if (accordionGroupEl) {\n            this.updateState(true);\n            addEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n        }\n    }\n    disconnectedCallback() {\n        const accordionGroupEl = this.accordionGroupEl;\n        if (accordionGroupEl) {\n            removeEventListener(accordionGroupEl, 'ionValueChange', this.updateListener);\n        }\n    }\n    componentDidLoad() {\n        this.setItemDefaults();\n        this.slotToggleIcon();\n        /**\n         * We need to wait a tick because we\n         * just set ionItem.button = true and\n         * the button has not have been rendered yet.\n         */\n        raf(() => {\n            /**\n             * Set aria label on button inside of ion-item\n             * once the inner content has been rendered.\n             */\n            const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n            this.setAria(expanded);\n        });\n    }\n    toggleExpanded() {\n        const { accordionGroupEl, disabled, readonly, value, state } = this;\n        if (disabled || readonly)\n            return;\n        if (accordionGroupEl) {\n            /**\n             * Because the accordion group may or may\n             * not allow multiple accordions open, we\n             * need to request the toggling of this\n             * accordion and the accordion group will\n             * make the decision on whether or not\n             * to allow it.\n             */\n            const expand = state === 1 /* AccordionState.Collapsed */ || state === 2 /* AccordionState.Collapsing */;\n            accordionGroupEl.requestAccordionToggle(value, expand);\n        }\n    }\n    render() {\n        const { disabled, readonly } = this;\n        const mode = getIonMode(this);\n        const expanded = this.state === 4 /* AccordionState.Expanded */ || this.state === 8 /* AccordionState.Expanding */;\n        const headerPart = expanded ? 'header expanded' : 'header';\n        const contentPart = expanded ? 'content expanded' : 'content';\n        this.setAria(expanded);\n        return (h(Host, { key: '073e1d02c18dcbc20c68648426e87c14750c031d', class: {\n                [mode]: true,\n                'accordion-expanding': this.state === 8 /* AccordionState.Expanding */,\n                'accordion-expanded': this.state === 4 /* AccordionState.Expanded */,\n                'accordion-collapsing': this.state === 2 /* AccordionState.Collapsing */,\n                'accordion-collapsed': this.state === 1 /* AccordionState.Collapsed */,\n                'accordion-next': this.isNext,\n                'accordion-previous': this.isPrevious,\n                'accordion-disabled': disabled,\n                'accordion-readonly': readonly,\n                'accordion-animated': this.shouldAnimate(),\n            } }, h(\"div\", { key: '9b4cf326de8bb6b4033992903c0c1bfd7eea9bcc', onClick: () => this.toggleExpanded(), id: \"header\", part: headerPart, \"aria-controls\": \"content\", ref: (headerEl) => (this.headerEl = headerEl) }, h(\"slot\", { key: '464c32a37f64655eacf4218284214f5f30b14a1e', name: \"header\" })), h(\"div\", { key: '8bb52e6a62d7de0106b253201a89a32e79d9a594', id: \"content\", part: contentPart, role: \"region\", \"aria-labelledby\": \"header\", ref: (contentEl) => (this.contentEl = contentEl) }, h(\"div\", { key: '1d9dfd952ad493754aaeea7a8f625b33c2dd90a0', id: \"content-wrapper\", ref: (contentElWrapper) => (this.contentElWrapper = contentElWrapper) }, h(\"slot\", { key: '970dfbc55a612d739d0ca3b7b1a08e5c96d0c479', name: \"content\" })))));\n    }\n    static get delegatesFocus() { return true; }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nlet accordionIds = 0;\nAccordion.style = {\n    ios: IonAccordionIosStyle0,\n    md: IonAccordionMdStyle0\n};\n\nconst accordionGroupIosCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){border-bottom:none}\";\nconst IonAccordionGroupIosStyle0 = accordionGroupIosCss;\n\nconst accordionGroupMdCss = \":host{display:block}:host(.accordion-group-expand-inset){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:16px;margin-bottom:16px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion){-webkit-box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanding),:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-expanded){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;border-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-previous){border-end-end-radius:6px;border-end-start-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion.accordion-next){border-start-start-radius:6px;border-start-end-radius:6px}:host(.accordion-group-expand-inset) ::slotted(ion-accordion):first-of-type{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}\";\nconst IonAccordionGroupMdStyle0 = accordionGroupMdCss;\n\nconst AccordionGroup = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionValueChange = createEvent(this, \"ionValueChange\", 7);\n        this.animated = true;\n        this.multiple = undefined;\n        this.value = undefined;\n        this.disabled = false;\n        this.readonly = false;\n        this.expand = 'compact';\n    }\n    valueChanged() {\n        const { value, multiple } = this;\n        if (!multiple && Array.isArray(value)) {\n            /**\n             * We do some processing on the `value` array so\n             * that it looks more like an array when logged to\n             * the console.\n             * Example given ['a', 'b']\n             * Default toString() behavior: a,b\n             * Custom behavior: ['a', 'b']\n             */\n            printIonWarning(`ion-accordion-group was passed an array of values, but multiple=\"false\". This is incorrect usage and may result in unexpected behaviors. To dismiss this warning, pass a string to the \"value\" property when multiple=\"false\".\n\n  Value Passed: [${value.map((v) => `'${v}'`).join(', ')}]\n`, this.el);\n        }\n        /**\n         * Do not use `value` here as that will be\n         * not account for the adjustment we make above.\n         */\n        this.ionValueChange.emit({ value: this.value });\n    }\n    async disabledChanged() {\n        const { disabled } = this;\n        const accordions = await this.getAccordions();\n        for (const accordion of accordions) {\n            accordion.disabled = disabled;\n        }\n    }\n    async readonlyChanged() {\n        const { readonly } = this;\n        const accordions = await this.getAccordions();\n        for (const accordion of accordions) {\n            accordion.readonly = readonly;\n        }\n    }\n    async onKeydown(ev) {\n        const activeElement = document.activeElement;\n        if (!activeElement) {\n            return;\n        }\n        /**\n         * Make sure focus is in the header, not the body, of the accordion. This ensures\n         * that if there are any interactable elements in the body, their keyboard\n         * interaction doesn't get stolen by the accordion. Example: using up/down keys\n         * in ion-textarea.\n         */\n        const activeAccordionHeader = activeElement.closest('ion-accordion [slot=\"header\"]');\n        if (!activeAccordionHeader) {\n            return;\n        }\n        const accordionEl = activeElement.tagName === 'ION-ACCORDION' ? activeElement : activeElement.closest('ion-accordion');\n        if (!accordionEl) {\n            return;\n        }\n        const closestGroup = accordionEl.closest('ion-accordion-group');\n        if (closestGroup !== this.el) {\n            return;\n        }\n        // If the active accordion is not in the current array of accordions, do not do anything\n        const accordions = await this.getAccordions();\n        const startingIndex = accordions.findIndex((a) => a === accordionEl);\n        if (startingIndex === -1) {\n            return;\n        }\n        let accordion;\n        if (ev.key === 'ArrowDown') {\n            accordion = this.findNextAccordion(accordions, startingIndex);\n        }\n        else if (ev.key === 'ArrowUp') {\n            accordion = this.findPreviousAccordion(accordions, startingIndex);\n        }\n        else if (ev.key === 'Home') {\n            accordion = accordions[0];\n        }\n        else if (ev.key === 'End') {\n            accordion = accordions[accordions.length - 1];\n        }\n        if (accordion !== undefined && accordion !== activeElement) {\n            accordion.focus();\n        }\n    }\n    async componentDidLoad() {\n        if (this.disabled) {\n            this.disabledChanged();\n        }\n        if (this.readonly) {\n            this.readonlyChanged();\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.valueChanged();\n    }\n    /**\n     * Sets the value property and emits ionChange.\n     * This should only be called when the user interacts\n     * with the accordion and not for any update\n     * to the value property. The exception is when\n     * the app sets the value of a single-select\n     * accordion group to an array.\n     */\n    setValue(accordionValue) {\n        const value = (this.value = accordionValue);\n        this.ionChange.emit({ value });\n    }\n    /**\n     * This method is used to ensure that the value\n     * of ion-accordion-group is being set in a valid\n     * way. This method should only be called in\n     * response to a user generated action.\n     * @internal\n     */\n    async requestAccordionToggle(accordionValue, accordionExpand) {\n        const { multiple, value, readonly, disabled } = this;\n        if (readonly || disabled) {\n            return;\n        }\n        if (accordionExpand) {\n            /**\n             * If group accepts multiple values\n             * check to see if value is already in\n             * in values array. If not, add it\n             * to the array.\n             */\n            if (multiple) {\n                const groupValue = value !== null && value !== void 0 ? value : [];\n                const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n                const valueExists = processedValue.find((v) => v === accordionValue);\n                if (valueExists === undefined && accordionValue !== undefined) {\n                    this.setValue([...processedValue, accordionValue]);\n                }\n            }\n            else {\n                this.setValue(accordionValue);\n            }\n        }\n        else {\n            /**\n             * If collapsing accordion, either filter the value\n             * out of the values array or unset the value.\n             */\n            if (multiple) {\n                const groupValue = value !== null && value !== void 0 ? value : [];\n                const processedValue = Array.isArray(groupValue) ? groupValue : [groupValue];\n                this.setValue(processedValue.filter((v) => v !== accordionValue));\n            }\n            else {\n                this.setValue(undefined);\n            }\n        }\n    }\n    findNextAccordion(accordions, startingIndex) {\n        const nextAccordion = accordions[startingIndex + 1];\n        if (nextAccordion === undefined) {\n            return accordions[0];\n        }\n        return nextAccordion;\n    }\n    findPreviousAccordion(accordions, startingIndex) {\n        const prevAccordion = accordions[startingIndex - 1];\n        if (prevAccordion === undefined) {\n            return accordions[accordions.length - 1];\n        }\n        return prevAccordion;\n    }\n    /**\n     * @internal\n     */\n    async getAccordions() {\n        return Array.from(this.el.querySelectorAll(':scope > ion-accordion'));\n    }\n    render() {\n        const { disabled, readonly, expand } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: '82f3e77066fabb4736638ee4c487ad56efd39c63', class: {\n                [mode]: true,\n                'accordion-group-disabled': disabled,\n                'accordion-group-readonly': readonly,\n                [`accordion-group-expand-${expand}`]: true,\n            }, role: \"presentation\" }, h(\"slot\", { key: 'a3c791ea887fc640b512f81d429be465ae902b3d' })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"value\": [\"valueChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"readonly\": [\"readonlyChanged\"]\n    }; }\n};\nAccordionGroup.style = {\n    ios: IonAccordionGroupIosStyle0,\n    md: IonAccordionGroupMdStyle0\n};\n\nexport { Accordion as ion_accordion, AccordionGroup as ion_accordion_group };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,WAAW,QAAQ,qBAAqB;AAC5G,SAASR,CAAC,IAAIS,GAAG,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,cAAc,QAAQ,uBAAuB;AAC/I,SAASC,CAAC,IAAIC,WAAW,QAAQ,qBAAqB;AACtD,SAASC,CAAC,IAAIC,MAAM,EAAEP,CAAC,IAAIQ,UAAU,QAAQ,4BAA4B;AACzE,SAASC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAE1D,MAAMC,eAAe,GAAG,qxCAAqxC;AAC7yC,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,cAAc,GAAG,wrCAAwrC;AAC/sC,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,SAAS,GAAG,MAAM;EACpBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjB/B,gBAAgB,CAAC,IAAI,EAAE8B,OAAO,CAAC;IAC/B,IAAI,CAACE,cAAc,GAAG,MAAM,IAAI,CAACC,WAAW,CAAC,KAAK,CAAC;IACnD,IAAI,CAACC,eAAe,GAAG,MAAM;MACzB,MAAMC,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;MACYA,OAAO,CAACE,MAAM,GAAG,IAAI;MACrBF,OAAO,CAACG,MAAM,GAAG,KAAK;MACtB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAIH,OAAO,CAACI,KAAK,KAAKC,SAAS,EAAE;QAC7BL,OAAO,CAACI,KAAK,GAAG,MAAM;MAC1B;IACJ,CAAC;IACD,IAAI,CAACH,uBAAuB,GAAG,MAAM;MACjC,MAAM;QAAEK;MAAS,CAAC,GAAG,IAAI;MACzB,IAAI,CAACA,QAAQ,EAAE;QACX;MACJ;MACA;AACZ;AACA;AACA;MACY,MAAMC,IAAI,GAAGD,QAAQ,CAACE,aAAa,CAAC,MAAM,CAAC;MAC3C,IAAI,CAACD,IAAI,EAAE;QACP;MACJ;MACA;MACA,IAAIA,IAAI,CAACE,gBAAgB,KAAKJ,SAAS,EACnC;MACJ,OAAOE,IAAI,CAACE,gBAAgB,CAAC,CAAC,CAACC,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACC,OAAO,KAAK,UAAU,CAAC;IAC1E,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,CAACC,QAAQ,GAAG,KAAK,KAAK;MACjC,MAAMd,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV;MACJ;MACA;AACZ;AACA;AACA;MACY,MAAMe,IAAI,GAAGlC,cAAc,CAACmB,OAAO,CAAC;MACpC,MAAME,MAAM,GAAGa,IAAI,CAACP,aAAa,CAAC,QAAQ,CAAC;MAC3C,IAAI,CAACN,MAAM,EAAE;QACT;MACJ;MACAA,MAAM,CAACc,YAAY,CAAC,eAAe,EAAE,GAAGF,QAAQ,EAAE,CAAC;IACvD,CAAC;IACD,IAAI,CAACG,cAAc,GAAG,MAAM;MACxB,MAAMjB,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;MAC9C,IAAI,CAACD,OAAO,EAAE;QACV;MACJ;MACA,MAAM;QAAEkB,cAAc;QAAEC;MAAW,CAAC,GAAG,IAAI;MAC3C;AACZ;AACA;AACA;MACY,MAAMC,kBAAkB,GAAGpB,OAAO,CAACQ,aAAa,CAAC,4BAA4B,CAAC;MAC9E,IAAIY,kBAAkB,EAAE;QACpB;MACJ;MACA,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MACjDF,MAAM,CAACd,IAAI,GAAGW,cAAc;MAC5BG,MAAM,CAACG,IAAI,GAAG,KAAK;MACnBH,MAAM,CAACI,SAAS,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACjDL,MAAM,CAACM,IAAI,GAAGR,UAAU;MACxBE,MAAM,CAACL,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;MAC1ChB,OAAO,CAAC4B,WAAW,CAACP,MAAM,CAAC;IAC/B,CAAC;IACD,IAAI,CAACQ,eAAe,GAAG,CAACC,aAAa,GAAG,KAAK,KAAK;MAC9C,MAAM;QAAEC,SAAS;QAAEC;MAAiB,CAAC,GAAG,IAAI;MAC5C,IAAIF,aAAa,IAAIC,SAAS,KAAK1B,SAAS,IAAI2B,gBAAgB,KAAK3B,SAAS,EAAE;QAC5E,IAAI,CAAC4B,KAAK,GAAG,CAAC,CAAC;QACf;MACJ;MACA,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC,+BAA+B;QAChD;MACJ;MACA,IAAI,IAAI,CAACC,UAAU,KAAK7B,SAAS,EAAE;QAC/B8B,oBAAoB,CAAC,IAAI,CAACD,UAAU,CAAC;MACzC;MACA,IAAI,IAAI,CAACE,aAAa,CAAC,CAAC,EAAE;QACtB/D,GAAG,CAAC,MAAM;UACN,IAAI,CAAC4D,KAAK,GAAG,CAAC,CAAC;UACf,IAAI,CAACC,UAAU,GAAG7D,GAAG,cAAAgE,iBAAA,CAAC,aAAY;YAC9B,MAAMC,aAAa,GAAGN,gBAAgB,CAACO,YAAY;YACnD,MAAMC,iBAAiB,GAAGjE,kBAAkB,CAACwD,SAAS,EAAE,IAAI,CAAC;YAC7DA,SAAS,CAACU,KAAK,CAACC,WAAW,CAAC,YAAY,EAAE,GAAGJ,aAAa,IAAI,CAAC;YAC/D,MAAME,iBAAiB;YACvB5C,KAAI,CAACqC,KAAK,GAAG,CAAC,CAAC;YACfF,SAAS,CAACU,KAAK,CAACE,cAAc,CAAC,YAAY,CAAC;UAChD,CAAC,EAAC;QACN,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAACV,KAAK,GAAG,CAAC,CAAC;MACnB;IACJ,CAAC;IACD,IAAI,CAACW,iBAAiB,GAAG,CAACd,aAAa,GAAG,KAAK,KAAK;MAChD,MAAM;QAAEC;MAAU,CAAC,GAAG,IAAI;MAC1B,IAAID,aAAa,IAAIC,SAAS,KAAK1B,SAAS,EAAE;QAC1C,IAAI,CAAC4B,KAAK,GAAG,CAAC,CAAC;QACf;MACJ;MACA,IAAI,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC,gCAAgC;QACjD;MACJ;MACA,IAAI,IAAI,CAACC,UAAU,KAAK7B,SAAS,EAAE;QAC/B8B,oBAAoB,CAAC,IAAI,CAACD,UAAU,CAAC;MACzC;MACA,IAAI,IAAI,CAACE,aAAa,CAAC,CAAC,EAAE;QACtB,IAAI,CAACF,UAAU,GAAG7D,GAAG,cAAAgE,iBAAA,CAAC,aAAY;UAC9B,MAAMC,aAAa,GAAGP,SAAS,CAACQ,YAAY;UAC5CR,SAAS,CAACU,KAAK,CAACC,WAAW,CAAC,YAAY,EAAE,GAAGJ,aAAa,IAAI,CAAC;UAC/DjE,GAAG,cAAAgE,iBAAA,CAAC,aAAY;YACZ,MAAMG,iBAAiB,GAAGjE,kBAAkB,CAACwD,SAAS,EAAE,IAAI,CAAC;YAC7DnC,KAAI,CAACqC,KAAK,GAAG,CAAC,CAAC;YACf,MAAMO,iBAAiB;YACvB5C,KAAI,CAACqC,KAAK,GAAG,CAAC,CAAC;YACfF,SAAS,CAACU,KAAK,CAACE,cAAc,CAAC,YAAY,CAAC;UAChD,CAAC,EAAC;QACN,CAAC,EAAC;MACN,CAAC,MACI;QACD,IAAI,CAACV,KAAK,GAAG,CAAC,CAAC;MACnB;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACG,aAAa,GAAG,MAAM;MACvB,IAAI,OAAOS,MAAM,KAAK,WAAW,EAAE;QAC/B,OAAO,KAAK;MAChB;MACA,MAAMC,oBAAoB,GAAGC,UAAU,CAAC,kCAAkC,CAAC,CAACC,OAAO;MACnF,IAAIF,oBAAoB,EAAE;QACtB,OAAO,KAAK;MAChB;MACA,MAAMG,QAAQ,GAAGhE,MAAM,CAACiE,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;MAC7C,IAAI,CAACD,QAAQ,EAAE;QACX,OAAO,KAAK;MAChB;MACA,IAAI,IAAI,CAACE,gBAAgB,IAAI,CAAC,IAAI,CAACA,gBAAgB,CAACF,QAAQ,EAAE;QAC1D,OAAO,KAAK;MAChB;MACA,OAAO,IAAI;IACf,CAAC;IACD,IAAI,CAACnD,WAAW,gBAAAuC,iBAAA,CAAG,WAAOP,aAAa,GAAG,KAAK,EAAK;MAChD,MAAMsB,cAAc,GAAGxD,KAAI,CAACuD,gBAAgB;MAC5C,MAAME,cAAc,GAAGzD,KAAI,CAAC0D,KAAK;MACjC,IAAI,CAACF,cAAc,EAAE;QACjB;MACJ;MACA,MAAME,KAAK,GAAGF,cAAc,CAACE,KAAK;MAClC,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,CAACI,QAAQ,CAACL,cAAc,CAAC,GAAGC,KAAK,KAAKD,cAAc;MACrG,IAAIE,YAAY,EAAE;QACd3D,KAAI,CAACiC,eAAe,CAACC,aAAa,CAAC;QACnClC,KAAI,CAAC+D,MAAM,GAAG/D,KAAI,CAACgE,UAAU,GAAG,KAAK;MACzC,CAAC,MACI;QACDhE,KAAI,CAACgD,iBAAiB,CAACd,aAAa,CAAC;QACrC;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB,MAAM+B,aAAa,GAAGjE,KAAI,CAACkE,cAAc,CAAC,CAAC;QAC3C,MAAMC,kBAAkB,GAAGF,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACP,KAAK;QAC5G,IAAIS,kBAAkB,KAAK1D,SAAS,EAAE;UAClCT,KAAI,CAACgE,UAAU,GAAGJ,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,CAACI,QAAQ,CAACK,kBAAkB,CAAC,GAAGT,KAAK,KAAKS,kBAAkB;QAC9G;QACA,MAAMC,iBAAiB,GAAGpE,KAAI,CAACqE,kBAAkB,CAAC,CAAC;QACnD,MAAMC,sBAAsB,GAAGF,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACV,KAAK;QAC5H,IAAIY,sBAAsB,KAAK7D,SAAS,EAAE;UACtCT,KAAI,CAAC+D,MAAM,GAAGH,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,GAAGA,KAAK,CAACI,QAAQ,CAACQ,sBAAsB,CAAC,GAAGZ,KAAK,KAAKY,sBAAsB;QAClH;MACJ;IACJ,CAAC;IACD,IAAI,CAACJ,cAAc,GAAG,MAAM;MACxB,IAAI,CAAC,IAAI,CAACnD,EAAE,EAAE;QACV;MACJ;MACA,MAAMwD,WAAW,GAAG,IAAI,CAACxD,EAAE,CAACyD,kBAAkB;MAC9C,IAAI,CAACD,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACvD,OAAO,MAAM,eAAe,EAAE;QACrG;MACJ;MACA,OAAOuD,WAAW;IACtB,CAAC;IACD,IAAI,CAACF,kBAAkB,GAAG,MAAM;MAC5B,IAAI,CAAC,IAAI,CAACtD,EAAE,EAAE;QACV;MACJ;MACA,MAAM0D,eAAe,GAAG,IAAI,CAAC1D,EAAE,CAAC2D,sBAAsB;MACtD,IAAI,CAACD,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACzD,OAAO,MAAM,eAAe,EAAE;QACjH;MACJ;MACA,OAAOyD,eAAe;IAC1B,CAAC;IACD,IAAI,CAACpC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAAC0B,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACN,KAAK,GAAG,iBAAiBiB,YAAY,EAAE,EAAE;IAC9C,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACtD,UAAU,GAAGpC,WAAW;IAC7B,IAAI,CAACmC,cAAc,GAAG,KAAK;EAC/B;EACAwD,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC5E,WAAW,CAAC,CAAC;EACtB;EACA6E,iBAAiBA,CAAA,EAAG;IAChB,IAAIC,EAAE;IACN,MAAMzB,gBAAgB,GAAI,IAAI,CAACA,gBAAgB,GAAG,CAACyB,EAAE,GAAG,IAAI,CAACjE,EAAE,MAAM,IAAI,IAAIiE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,OAAO,CAAC,qBAAqB,CAAE;IACxI,IAAI1B,gBAAgB,EAAE;MAClB,IAAI,CAACrD,WAAW,CAAC,IAAI,CAAC;MACtBrB,gBAAgB,CAAC0E,gBAAgB,EAAE,gBAAgB,EAAE,IAAI,CAACtD,cAAc,CAAC;IAC7E;EACJ;EACAiF,oBAAoBA,CAAA,EAAG;IACnB,MAAM3B,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAIA,gBAAgB,EAAE;MAClBxE,mBAAmB,CAACwE,gBAAgB,EAAE,gBAAgB,EAAE,IAAI,CAACtD,cAAc,CAAC;IAChF;EACJ;EACAkF,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAChF,eAAe,CAAC,CAAC;IACtB,IAAI,CAACkB,cAAc,CAAC,CAAC;IACrB;AACR;AACA;AACA;AACA;IACQ5C,GAAG,CAAC,MAAM;MACN;AACZ;AACA;AACA;MACY,MAAMyC,QAAQ,GAAG,IAAI,CAACmB,KAAK,KAAK,CAAC,CAAC,iCAAiC,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;MACpF,IAAI,CAACpB,OAAO,CAACC,QAAQ,CAAC;IAC1B,CAAC,CAAC;EACN;EACAkE,cAAcA,CAAA,EAAG;IACb,MAAM;MAAE7B,gBAAgB;MAAEqB,QAAQ;MAAEC,QAAQ;MAAEnB,KAAK;MAAErB;IAAM,CAAC,GAAG,IAAI;IACnE,IAAIuC,QAAQ,IAAIC,QAAQ,EACpB;IACJ,IAAItB,gBAAgB,EAAE;MAClB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAM8B,MAAM,GAAGhD,KAAK,KAAK,CAAC,CAAC,kCAAkCA,KAAK,KAAK,CAAC,CAAC;MACzEkB,gBAAgB,CAAC+B,sBAAsB,CAAC5B,KAAK,EAAE2B,MAAM,CAAC;IAC1D;EACJ;EACAE,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEX,QAAQ;MAAEC;IAAS,CAAC,GAAG,IAAI;IACnC,MAAMW,IAAI,GAAGlG,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM4B,QAAQ,GAAG,IAAI,CAACmB,KAAK,KAAK,CAAC,CAAC,iCAAiC,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;IACpF,MAAMoD,UAAU,GAAGvE,QAAQ,GAAG,iBAAiB,GAAG,QAAQ;IAC1D,MAAMwE,WAAW,GAAGxE,QAAQ,GAAG,kBAAkB,GAAG,SAAS;IAC7D,IAAI,CAACD,OAAO,CAACC,QAAQ,CAAC;IACtB,OAAQhD,CAAC,CAACE,IAAI,EAAE;MAAEuH,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACJ,IAAI,GAAG,IAAI;QACZ,qBAAqB,EAAE,IAAI,CAACnD,KAAK,KAAK,CAAC,CAAC;QACxC,oBAAoB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACvC,sBAAsB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACzC,qBAAqB,EAAE,IAAI,CAACA,KAAK,KAAK,CAAC,CAAC;QACxC,gBAAgB,EAAE,IAAI,CAAC0B,MAAM;QAC7B,oBAAoB,EAAE,IAAI,CAACC,UAAU;QACrC,oBAAoB,EAAEY,QAAQ;QAC9B,oBAAoB,EAAEC,QAAQ;QAC9B,oBAAoB,EAAE,IAAI,CAACrC,aAAa,CAAC;MAC7C;IAAE,CAAC,EAAEtE,CAAC,CAAC,KAAK,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAEE,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACT,cAAc,CAAC,CAAC;MAAEU,EAAE,EAAE,QAAQ;MAAEC,IAAI,EAAEN,UAAU;MAAE,eAAe,EAAE,SAAS;MAAEO,GAAG,EAAGtF,QAAQ,IAAM,IAAI,CAACA,QAAQ,GAAGA;IAAU,CAAC,EAAExC,CAAC,CAAC,MAAM,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAS,CAAC,CAAC,CAAC,EAAE/H,CAAC,CAAC,KAAK,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAEG,EAAE,EAAE,SAAS;MAAEC,IAAI,EAAEL,WAAW;MAAEQ,IAAI,EAAE,QAAQ;MAAE,iBAAiB,EAAE,QAAQ;MAAEF,GAAG,EAAG7D,SAAS,IAAM,IAAI,CAACA,SAAS,GAAGA;IAAW,CAAC,EAAEjE,CAAC,CAAC,KAAK,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAEG,EAAE,EAAE,iBAAiB;MAAEE,GAAG,EAAG5D,gBAAgB,IAAM,IAAI,CAACA,gBAAgB,GAAGA;IAAkB,CAAC,EAAElE,CAAC,CAAC,MAAM,EAAE;MAAEyH,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE;IAAU,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1tB;EACA,WAAWE,cAAcA,CAAA,EAAG;IAAE,OAAO,IAAI;EAAE;EAC3C,IAAIpF,EAAEA,CAAA,EAAG;IAAE,OAAOzC,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW8H,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,IAAIzB,YAAY,GAAG,CAAC;AACpB9E,SAAS,CAACgD,KAAK,GAAG;EACdwD,GAAG,EAAE3G,qBAAqB;EAC1B4G,EAAE,EAAE1G;AACR,CAAC;AAED,MAAM2G,oBAAoB,GAAG,sXAAsX;AACnZ,MAAMC,0BAA0B,GAAGD,oBAAoB;AAEvD,MAAME,mBAAmB,GAAG,ioCAAioC;AAC7pC,MAAMC,yBAAyB,GAAGD,mBAAmB;AAErD,MAAME,cAAc,GAAG,MAAM;EACzB7G,WAAWA,CAACC,OAAO,EAAE;IACjB9B,gBAAgB,CAAC,IAAI,EAAE8B,OAAO,CAAC;IAC/B,IAAI,CAAC6G,SAAS,GAAGpI,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACqI,cAAc,GAAGrI,WAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC6E,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACyD,QAAQ,GAAGrG,SAAS;IACzB,IAAI,CAACiD,KAAK,GAAGjD,SAAS;IACtB,IAAI,CAACmE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACQ,MAAM,GAAG,SAAS;EAC3B;EACAP,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEpB,KAAK;MAAEoD;IAAS,CAAC,GAAG,IAAI;IAChC,IAAI,CAACA,QAAQ,IAAIlD,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MACnC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACYlE,eAAe,CAAC;AAC5B;AACA,mBAAmBkE,KAAK,CAACqD,GAAG,CAAEC,CAAC,IAAK,IAAIA,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;AACxD,CAAC,EAAE,IAAI,CAAClG,EAAE,CAAC;IACH;IACA;AACR;AACA;AACA;IACQ,IAAI,CAAC8F,cAAc,CAACK,IAAI,CAAC;MAAExD,KAAK,EAAE,IAAI,CAACA;IAAM,CAAC,CAAC;EACnD;EACMyD,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA3E,iBAAA;MACpB,MAAM;QAAEmC;MAAS,CAAC,GAAGwC,MAAI;MACzB,MAAMC,UAAU,SAASD,MAAI,CAACE,aAAa,CAAC,CAAC;MAC7C,KAAK,MAAMC,SAAS,IAAIF,UAAU,EAAE;QAChCE,SAAS,CAAC3C,QAAQ,GAAGA,QAAQ;MACjC;IAAC;EACL;EACM4C,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAhF,iBAAA;MACpB,MAAM;QAAEoC;MAAS,CAAC,GAAG4C,MAAI;MACzB,MAAMJ,UAAU,SAASI,MAAI,CAACH,aAAa,CAAC,CAAC;MAC7C,KAAK,MAAMC,SAAS,IAAIF,UAAU,EAAE;QAChCE,SAAS,CAAC1C,QAAQ,GAAGA,QAAQ;MACjC;IAAC;EACL;EACM6C,SAASA,CAACC,EAAE,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAnF,iBAAA;MAChB,MAAMoF,aAAa,GAAGnG,QAAQ,CAACmG,aAAa;MAC5C,IAAI,CAACA,aAAa,EAAE;QAChB;MACJ;MACA;AACR;AACA;AACA;AACA;AACA;MACQ,MAAMC,qBAAqB,GAAGD,aAAa,CAAC5C,OAAO,CAAC,+BAA+B,CAAC;MACpF,IAAI,CAAC6C,qBAAqB,EAAE;QACxB;MACJ;MACA,MAAMC,WAAW,GAAGF,aAAa,CAAC7G,OAAO,KAAK,eAAe,GAAG6G,aAAa,GAAGA,aAAa,CAAC5C,OAAO,CAAC,eAAe,CAAC;MACtH,IAAI,CAAC8C,WAAW,EAAE;QACd;MACJ;MACA,MAAMC,YAAY,GAAGD,WAAW,CAAC9C,OAAO,CAAC,qBAAqB,CAAC;MAC/D,IAAI+C,YAAY,KAAKJ,MAAI,CAAC7G,EAAE,EAAE;QAC1B;MACJ;MACA;MACA,MAAMsG,UAAU,SAASO,MAAI,CAACN,aAAa,CAAC,CAAC;MAC7C,MAAMW,aAAa,GAAGZ,UAAU,CAACa,SAAS,CAAEtJ,CAAC,IAAKA,CAAC,KAAKmJ,WAAW,CAAC;MACpE,IAAIE,aAAa,KAAK,CAAC,CAAC,EAAE;QACtB;MACJ;MACA,IAAIV,SAAS;MACb,IAAII,EAAE,CAAChC,GAAG,KAAK,WAAW,EAAE;QACxB4B,SAAS,GAAGK,MAAI,CAACO,iBAAiB,CAACd,UAAU,EAAEY,aAAa,CAAC;MACjE,CAAC,MACI,IAAIN,EAAE,CAAChC,GAAG,KAAK,SAAS,EAAE;QAC3B4B,SAAS,GAAGK,MAAI,CAACQ,qBAAqB,CAACf,UAAU,EAAEY,aAAa,CAAC;MACrE,CAAC,MACI,IAAIN,EAAE,CAAChC,GAAG,KAAK,MAAM,EAAE;QACxB4B,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;MAC7B,CAAC,MACI,IAAIM,EAAE,CAAChC,GAAG,KAAK,KAAK,EAAE;QACvB4B,SAAS,GAAGF,UAAU,CAACA,UAAU,CAACgB,MAAM,GAAG,CAAC,CAAC;MACjD;MACA,IAAId,SAAS,KAAK9G,SAAS,IAAI8G,SAAS,KAAKM,aAAa,EAAE;QACxDN,SAAS,CAACe,KAAK,CAAC,CAAC;MACrB;IAAC;EACL;EACMnD,gBAAgBA,CAAA,EAAG;IAAA,IAAAoD,MAAA;IAAA,OAAA9F,iBAAA;MACrB,IAAI8F,MAAI,CAAC3D,QAAQ,EAAE;QACf2D,MAAI,CAACpB,eAAe,CAAC,CAAC;MAC1B;MACA,IAAIoB,MAAI,CAAC1D,QAAQ,EAAE;QACf0D,MAAI,CAACf,eAAe,CAAC,CAAC;MAC1B;MACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACQe,MAAI,CAACzD,YAAY,CAAC,CAAC;IAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI0D,QAAQA,CAAC/E,cAAc,EAAE;IACrB,MAAMC,KAAK,GAAI,IAAI,CAACA,KAAK,GAAGD,cAAe;IAC3C,IAAI,CAACmD,SAAS,CAACM,IAAI,CAAC;MAAExD;IAAM,CAAC,CAAC;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACU4B,sBAAsBA,CAAC7B,cAAc,EAAEgF,eAAe,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAjG,iBAAA;MAC1D,MAAM;QAAEqE,QAAQ;QAAEpD,KAAK;QAAEmB,QAAQ;QAAED;MAAS,CAAC,GAAG8D,MAAI;MACpD,IAAI7D,QAAQ,IAAID,QAAQ,EAAE;QACtB;MACJ;MACA,IAAI6D,eAAe,EAAE;QACjB;AACZ;AACA;AACA;AACA;AACA;QACY,IAAI3B,QAAQ,EAAE;UACV,MAAM6B,UAAU,GAAGjF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;UAClE,MAAMkF,cAAc,GAAGhF,KAAK,CAACC,OAAO,CAAC8E,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;UAC5E,MAAME,WAAW,GAAGD,cAAc,CAAC9H,IAAI,CAAEkG,CAAC,IAAKA,CAAC,KAAKvD,cAAc,CAAC;UACpE,IAAIoF,WAAW,KAAKpI,SAAS,IAAIgD,cAAc,KAAKhD,SAAS,EAAE;YAC3DiI,MAAI,CAACF,QAAQ,CAAC,CAAC,GAAGI,cAAc,EAAEnF,cAAc,CAAC,CAAC;UACtD;QACJ,CAAC,MACI;UACDiF,MAAI,CAACF,QAAQ,CAAC/E,cAAc,CAAC;QACjC;MACJ,CAAC,MACI;QACD;AACZ;AACA;AACA;QACY,IAAIqD,QAAQ,EAAE;UACV,MAAM6B,UAAU,GAAGjF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;UAClE,MAAMkF,cAAc,GAAGhF,KAAK,CAACC,OAAO,CAAC8E,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;UAC5ED,MAAI,CAACF,QAAQ,CAACI,cAAc,CAACE,MAAM,CAAE9B,CAAC,IAAKA,CAAC,KAAKvD,cAAc,CAAC,CAAC;QACrE,CAAC,MACI;UACDiF,MAAI,CAACF,QAAQ,CAAC/H,SAAS,CAAC;QAC5B;MACJ;IAAC;EACL;EACA0H,iBAAiBA,CAACd,UAAU,EAAEY,aAAa,EAAE;IACzC,MAAMhE,aAAa,GAAGoD,UAAU,CAACY,aAAa,GAAG,CAAC,CAAC;IACnD,IAAIhE,aAAa,KAAKxD,SAAS,EAAE;MAC7B,OAAO4G,UAAU,CAAC,CAAC,CAAC;IACxB;IACA,OAAOpD,aAAa;EACxB;EACAmE,qBAAqBA,CAACf,UAAU,EAAEY,aAAa,EAAE;IAC7C,MAAMc,aAAa,GAAG1B,UAAU,CAACY,aAAa,GAAG,CAAC,CAAC;IACnD,IAAIc,aAAa,KAAKtI,SAAS,EAAE;MAC7B,OAAO4G,UAAU,CAACA,UAAU,CAACgB,MAAM,GAAG,CAAC,CAAC;IAC5C;IACA,OAAOU,aAAa;EACxB;EACA;AACJ;AACA;EACUzB,aAAaA,CAAA,EAAG;IAAA,IAAA0B,MAAA;IAAA,OAAAvG,iBAAA;MAClB,OAAOmB,KAAK,CAACqF,IAAI,CAACD,MAAI,CAACjI,EAAE,CAACmI,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;IAAC;EAC1E;EACA3D,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEX,QAAQ;MAAEC,QAAQ;MAAEQ;IAAO,CAAC,GAAG,IAAI;IAC3C,MAAMG,IAAI,GAAGlG,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQpB,CAAC,CAACE,IAAI,EAAE;MAAEuH,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAE;QAClE,CAACJ,IAAI,GAAG,IAAI;QACZ,0BAA0B,EAAEZ,QAAQ;QACpC,0BAA0B,EAAEC,QAAQ;QACpC,CAAC,0BAA0BQ,MAAM,EAAE,GAAG;MAC1C,CAAC;MAAEa,IAAI,EAAE;IAAe,CAAC,EAAEhI,CAAC,CAAC,MAAM,EAAE;MAAEyH,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAClG;EACA,IAAI5E,EAAEA,CAAA,EAAG;IAAE,OAAOzC,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW8H,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,OAAO,EAAE,CAAC,cAAc,CAAC;MACzB,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,UAAU,EAAE,CAAC,iBAAiB;IAClC,CAAC;EAAE;AACP,CAAC;AACDO,cAAc,CAAC9D,KAAK,GAAG;EACnBwD,GAAG,EAAEG,0BAA0B;EAC/BF,EAAE,EAAEI;AACR,CAAC;AAED,SAAS7G,SAAS,IAAIsJ,aAAa,EAAExC,cAAc,IAAIyC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}