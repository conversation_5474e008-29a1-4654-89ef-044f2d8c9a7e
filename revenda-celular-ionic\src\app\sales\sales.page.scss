// Estilos padronizados para lista
ion-item {
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 120px;
  --border-radius: 12px;
  --border-width: 1px;
  --border-style: solid;
  --border-color: var(--ion-color-light-shade);
  margin: 8px 16px;
  --background: var(--ion-color-light);
  animation: fadeIn 0.3s ease-in-out;
}

ion-avatar {
  width: 50px;
  height: 50px;
  margin-right: 16px;

  .sale-avatar {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--ion-color-medium-tint);
    border-radius: 8px;
    background: transparent;

    ion-icon {
      font-size: 24px;
      color: var(--ion-color-medium);
    }
  }
}

ion-label {
  h2 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 4px;
    color: var(--ion-color-primary);
  }

  p {
    font-size: 13px;
    margin: 2px 0;
    color: var(--ion-color-dark);

    strong {
      color: var(--ion-color-primary);
    }
  }

  .badges {
    margin-top: 8px;

    ion-badge {
      margin-right: 8px;
      margin-bottom: 4px;
      font-size: 12px;
      letter-spacing: 0.5px;
    }
  }
}

ion-button {
  margin-left: 4px;

  &[color="danger"] {
    --color: var(--ion-color-danger);
  }

  &[disabled] {
    opacity: 0.5;
  }
}

// Manter estilos antigos para compatibilidade
.sale-info {
  width: 100%;

  h2 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 6px;
    color: var(--ion-color-secondary);
  }

  h3, h4 {
    font-size: 14px;
    margin: 4px 0;
    font-weight: normal;
    display: flex;
    align-items: center;

    strong {
      min-width: 70px;
      font-weight: 600;
    }
  }

  ion-badge {
    margin-right: 8px;
    font-size: 12px;
    letter-spacing: 0.5px;
  }

  ion-button {
    margin-top: 10px;
    --padding-start: 12px;
    --padding-end: 12px;

    ion-icon {
      margin-right: 4px;
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}












