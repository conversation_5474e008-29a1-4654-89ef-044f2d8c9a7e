.sale-info {
  width: 100%;
  
  h2 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 6px;
    color: var(--ion-color-secondary);
  }
  
  h3, h4 {
    font-size: 14px;
    margin: 4px 0;
    font-weight: normal;
    display: flex;
    align-items: center;
    
    strong {
      min-width: 70px;
      font-weight: 600;
    }
  }
  
  ion-badge {
    margin-right: 8px;
    font-size: 12px;
    letter-spacing: 0.5px;
  }
  
  ion-button {
    margin-top: 10px;
    --padding-start: 12px;
    --padding-end: 12px;
    
    ion-icon {
      margin-right: 4px;
    }
  }
}

ion-item {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}












