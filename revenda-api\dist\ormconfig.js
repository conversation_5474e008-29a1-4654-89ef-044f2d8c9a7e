"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const typeorm_1 = require("typeorm");
const dotenv_1 = require("dotenv");
(0, dotenv_1.config)();
exports.default = new typeorm_1.DataSource({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || '050625gui',
    database: process.env.DB_DATABASE || 'revenda',
    entities: ['src/**/*.entity.ts'],
    migrations: ['src/db/migrations/*.ts', 'src/db/seeds/*.ts'],
    synchronize: false,
    logging: true,
});
//# sourceMappingURL=ormconfig.js.map