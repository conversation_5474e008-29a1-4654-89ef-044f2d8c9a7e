{"ast": null, "code": "var _AccessoriesPage;\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/accessory.service\";\nimport * as i2 from \"@ionic/angular\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = () => [\"new\"];\nconst _c1 = a0 => [\"edit\", a0];\nconst _forTrack0 = ($index, $item) => $item.id;\nfunction AccessoriesPage_For_13_Conditional_18_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const phone_r3 = ctx.$implicit;\n    const ɵ$index_54_r4 = ctx.$index;\n    const ɵ$count_54_r5 = ctx.$count;\n    i0.ɵɵtextInterpolate2(\" \", phone_r3.model, \"\", !(ɵ$index_54_r4 === ɵ$count_54_r5 - 1) ? \", \" : \"\", \" \");\n  }\n}\nfunction AccessoriesPage_For_13_Conditional_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, AccessoriesPage_For_13_Conditional_18_For_1_Template, 1, 2, null, null, _forTrack0);\n  }\n  if (rf & 2) {\n    const accessory_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵrepeater(accessory_r6.compatiblePhones);\n  }\n}\nfunction AccessoriesPage_For_13_Conditional_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Todos os modelos \");\n  }\n}\nfunction AccessoriesPage_For_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-item\")(1, \"ion-avatar\", 2)(2, \"img\", 9);\n    i0.ɵɵlistener(\"error\", function AccessoriesPage_For_13_Template_img_error_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onImageError($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"ion-label\")(4, \"h2\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\")(7, \"strong\");\n    i0.ɵɵtext(8, \"Categoria:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\")(11, \"strong\");\n    i0.ɵɵtext(12, \"Pre\\u00E7o:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\")(16, \"strong\");\n    i0.ɵɵtext(17, \"Compat\\u00EDvel com:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, AccessoriesPage_For_13_Conditional_18_Template, 2, 0)(19, AccessoriesPage_For_13_Conditional_19_Template, 1, 0);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\")(21, \"strong\");\n    i0.ɵɵtext(22, \"Estoque:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\", 10)(25, \"strong\");\n    i0.ɵɵtext(26, \"Descri\\u00E7\\u00E3o:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"ion-button\", 11);\n    i0.ɵɵelement(29, \"ion-icon\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"ion-button\", 13);\n    i0.ɵɵlistener(\"click\", function AccessoriesPage_For_13_Template_ion_button_click_30_listener() {\n      const accessory_r6 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.remove(accessory_r6));\n    });\n    i0.ɵɵelement(31, \"ion-icon\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const accessory_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", accessory_r6.image, i0.ɵɵsanitizeUrl)(\"alt\", accessory_r6.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(accessory_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", accessory_r6.category, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(14, 9, accessory_r6.price, \"BRL\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵconditional(accessory_r6.compatiblePhones && accessory_r6.compatiblePhones.length > 0 ? 18 : 19);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", accessory_r6.stock, \" unidades\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", accessory_r6.description, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(12, _c1, accessory_r6.id));\n  }\n}\nfunction AccessoriesPage_ForEmpty_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ion-item\");\n    i0.ɵɵtext(1, \"Lista de acess\\u00F3rios vazia, cadastre um novo acess\\u00F3rio!\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class AccessoriesPage {\n  constructor(accessoryService, alertController, toastController) {\n    this.accessoryService = accessoryService;\n    this.alertController = alertController;\n    this.toastController = toastController;\n    this.accessoriesList = [];\n  }\n  ionViewDidLeave() {\n    console.log('ionViewDidLeave');\n  }\n  ionViewWillLeave() {\n    console.log('ionViewWillLeave');\n  }\n  ionViewDidEnter() {\n    console.log('ionViewDidEnter');\n  }\n  ionViewWillEnter() {\n    console.log('ionViewWillEnter');\n    this.accessoryService.getList().subscribe({\n      next: response => {\n        this.accessoriesList = response;\n      },\n      error: error => {\n        alert('Erro ao carregar lista de acessórios');\n        console.error(error);\n      }\n    });\n  }\n  ngOnInit() {}\n  remove(accessory) {\n    this.alertController.create({\n      header: 'Exclusão',\n      message: `Confirma a exclusão do acessório ${accessory.name}?`,\n      buttons: [{\n        text: 'Sim',\n        handler: () => {\n          this.accessoryService.remove(accessory).subscribe({\n            next: () => {\n              // Remover da lista usando o ID do acessório original\n              this.accessoriesList = this.accessoriesList.filter(a => a.id !== accessory.id);\n              this.toastController.create({\n                message: `Acessório ${accessory.name} excluído com sucesso!`,\n                duration: 3000,\n                color: 'secondary',\n                keyboardClose: true\n              }).then(toast => toast.present());\n            },\n            error: error => {\n              var _error$error;\n              let errorMessage = 'Erro ao excluir o acessório ' + accessory.name;\n              if ((_error$error = error.error) !== null && _error$error !== void 0 && _error$error.message) {\n                errorMessage = error.error.message;\n              }\n              alert(errorMessage);\n              console.error(error);\n            }\n          });\n        }\n      }, 'Não']\n    }).then(alert => alert.present());\n  }\n  onImageError(event) {\n    // Substituir por imagem padrão quando houver erro\n    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xNSAxNUgyNVYyNUgxNVYxNVoiIGZpbGw9IiNDQ0NDQ0MiLz4KPHBhdGggZD0iTTE4IDE4SDIyVjIySDE4VjE4WiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4K';\n  }\n}\n_AccessoriesPage = AccessoriesPage;\n_AccessoriesPage.ɵfac = function AccessoriesPage_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _AccessoriesPage)(i0.ɵɵdirectiveInject(i1.AccessoryService), i0.ɵɵdirectiveInject(i2.AlertController), i0.ɵɵdirectiveInject(i2.ToastController));\n};\n_AccessoriesPage.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: _AccessoriesPage,\n  selectors: [[\"app-accessories\"]],\n  standalone: false,\n  decls: 18,\n  vars: 5,\n  consts: [[3, \"translucent\"], [\"color\", \"secondary\"], [\"slot\", \"start\"], [3, \"fullscreen\"], [\"collapse\", \"condense\"], [\"size\", \"large\"], [\"vertical\", \"bottom\", \"horizontal\", \"end\", \"slot\", \"fixed\"], [3, \"routerLink\"], [\"name\", \"add\"], [3, \"error\", \"src\", \"alt\"], [1, \"description\"], [\"slot\", \"end\", \"size\", \"small\", 3, \"routerLink\"], [\"name\", \"create\", \"slot\", \"icon-only\"], [\"slot\", \"end\", \"size\", \"small\", \"color\", \"danger\", 3, \"click\"], [\"name\", \"trash\", \"slot\", \"icon-only\"]],\n  template: function AccessoriesPage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"ion-toolbar\", 1)(2, \"ion-buttons\", 2);\n      i0.ɵɵelement(3, \"ion-menu-button\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"ion-title\");\n      i0.ɵɵtext(5, \"Acess\\u00F3rios\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(6, \"ion-content\", 3)(7, \"ion-header\", 4)(8, \"ion-toolbar\")(9, \"ion-title\", 5);\n      i0.ɵɵtext(10, \"Acess\\u00F3rios\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(11, \"ion-list\");\n      i0.ɵɵrepeaterCreate(12, AccessoriesPage_For_13_Template, 32, 14, \"ion-item\", null, _forTrack0, false, AccessoriesPage_ForEmpty_14_Template, 2, 0, \"ion-item\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(15, \"ion-fab\", 6)(16, \"ion-fab-button\", 7);\n      i0.ɵɵelement(17, \"ion-icon\", 8);\n      i0.ɵɵelementEnd()()();\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"translucent\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"fullscreen\", true);\n      i0.ɵɵadvance(6);\n      i0.ɵɵrepeater(ctx.accessoriesList);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(4, _c0));\n    }\n  },\n  dependencies: [i2.IonAvatar, i2.IonButton, i2.IonButtons, i2.IonContent, i2.IonFab, i2.IonFabButton, i2.IonHeader, i2.IonIcon, i2.IonItem, i2.IonLabel, i2.IonList, i2.IonMenuButton, i2.IonTitle, i2.IonToolbar, i2.RouterLinkDelegate, i3.RouterLink, i4.CurrencyPipe],\n  styles: [\"ion-item[_ngcontent-%COMP%] {\\n  --padding-start: 16px;\\n  --padding-end: 16px;\\n  --min-height: 100px;\\n}\\n\\nion-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  margin-right: 16px;\\n}\\nion-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  border-radius: 8px;\\n}\\n\\nion-label[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n  margin-bottom: 4px;\\n  color: var(--ion-color-primary);\\n}\\nion-label[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 2px 0;\\n  color: var(--ion-color-medium);\\n}\\nion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  margin: 2px 0;\\n  color: var(--ion-color-dark);\\n}\\nion-label[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: var(--ion-color-primary);\\n}\\nion-label[_ngcontent-%COMP%]   p.description[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: var(--ion-color-medium);\\n  max-width: 300px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\nion-button[_ngcontent-%COMP%] {\\n  margin-left: 4px;\\n}\\nion-button[color=danger][_ngcontent-%COMP%] {\\n  --color: var(--ion-color-danger);\\n}\\n\\nh2[_ngcontent-%COMP%], h3[_ngcontent-%COMP%], h4[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0.25rem;\\n}\\n\\n.phones[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding-inline-start: 0;\\n  margin: 0;\\n}\\n\\nh2[_ngcontent-%COMP%] {\\n  padding-top: 1rem;\\n}\\n\\n.accessory-info[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.accessory-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n  margin-bottom: 4px;\\n}\\n.accessory-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .accessory-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin: 4px 0;\\n  font-weight: normal;\\n}\\n.accessory-info[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  margin-right: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n});", "map": {"version": 3, "names": ["i0", "ɵɵtext", "ɵɵtextInterpolate2", "phone_r3", "model", "ɵ$index_54_r4", "ɵ$count_54_r5", "ɵɵrepeaterCreate", "AccessoriesPage_For_13_Conditional_18_For_1_Template", "_forTrack0", "ɵɵrepeater", "accessory_r6", "compatiblePhones", "ɵɵelementStart", "ɵɵlistener", "AccessoriesPage_For_13_Template_img_error_2_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onImageError", "ɵɵelementEnd", "ɵɵtemplate", "AccessoriesPage_For_13_Conditional_18_Template", "AccessoriesPage_For_13_Conditional_19_Template", "ɵɵelement", "AccessoriesPage_For_13_Template_ion_button_click_30_listener", "$implicit", "remove", "ɵɵadvance", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "name", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "category", "ɵɵpipeBind2", "price", "ɵɵconditional", "length", "stock", "description", "ɵɵpureFunction1", "_c1", "id", "AccessoriesPage", "constructor", "accessoryService", "alertController", "toastController", "accessoriesList", "ionViewDidLeave", "console", "log", "ionViewWillLeave", "ionViewDidEnter", "ionViewWillEnter", "getList", "subscribe", "next", "response", "error", "alert", "ngOnInit", "accessory", "create", "header", "message", "buttons", "text", "handler", "filter", "a", "duration", "color", "keyboardClose", "then", "toast", "present", "_error$error", "errorMessage", "event", "target", "src", "ɵɵdirectiveInject", "i1", "AccessoryService", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ToastController", "selectors", "standalone", "decls", "vars", "consts", "template", "AccessoriesPage_Template", "rf", "ctx", "AccessoriesPage_For_13_Template", "AccessoriesPage_ForEmpty_14_Template", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\accessories\\accessories.page.ts", "C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\accessories\\accessories.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { AlertController, ToastController, ViewDidEnter, ViewDidLeave, ViewWillEnter, ViewWillLeave } from '@ionic/angular';\r\nimport { Accessory } from './models/accessory.type';\r\nimport { AccessoryService } from './services/accessory.service';\r\nimport { IonHeader } from \"@ionic/angular/standalone\";\r\n\r\n@Component({\r\n  selector: 'app-accessories',\r\n  templateUrl: './accessories.page.html',\r\n  styleUrls: ['./accessories.page.scss'],\r\n  standalone: false,\r\n})\r\nexport class AccessoriesPage implements OnInit, ViewWillEnter,\r\n  ViewDidEnter, ViewWillLeave, ViewDidLeave {\r\n\r\n  accessoriesList: Accessory[] = [];\r\n\r\n  constructor(\r\n    private accessoryService: AccessoryService,\r\n    private alertController: AlertController,\r\n    private toastController: ToastController,\r\n  ) { }\r\n\r\n  ionViewDidLeave(): void {\r\n    console.log('ionViewDidLeave');\r\n  }\r\n  \r\n  ionViewWillLeave(): void {\r\n    console.log('ionViewWillLeave');\r\n  }\r\n  \r\n  ionViewDidEnter(): void {\r\n    console.log('ionViewDidEnter');\r\n  }\r\n  \r\n  ionViewWillEnter(): void {\r\n    console.log('ionViewWillEnter');\r\n\r\n    this.accessoryService.getList().subscribe({\r\n      next: (response) => {\r\n        this.accessoriesList = response;\r\n      },\r\n      error: (error) => {\r\n        alert('Erro ao carregar lista de acessórios');\r\n        console.error(error);\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnInit() { }\r\n\r\n  remove(accessory: Accessory) {\r\n    this.alertController.create({\r\n      header: 'Exclusão',\r\n      message: `Confirma a exclusão do acessório ${accessory.name}?`,\r\n      buttons: [\r\n        {\r\n          text: 'Sim',\r\n          handler: () => {\r\n            this.accessoryService.remove(accessory).subscribe({\r\n              next: () => {\r\n                // Remover da lista usando o ID do acessório original\r\n                this.accessoriesList = this.accessoriesList.filter(a => a.id !== accessory.id);\r\n                this.toastController.create({\r\n                  message: `Acessório ${accessory.name} excluído com sucesso!`,\r\n                  duration: 3000,\r\n                  color: 'secondary',\r\n                  keyboardClose: true,\r\n                }).then(toast => toast.present());\r\n              },\r\n              error: (error) => {\r\n                let errorMessage = 'Erro ao excluir o acessório ' + accessory.name;\r\n                if (error.error?.message) {\r\n                  errorMessage = error.error.message;\r\n                }\r\n                alert(errorMessage);\r\n                console.error(error);\r\n              }\r\n            });\r\n          }\r\n        },\r\n        'Não'\r\n      ]\r\n    }).then(alert => alert.present());\r\n  }\r\n\r\n  onImageError(event: any) {\r\n    // Substituir por imagem padrão quando houver erro\r\n    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xNSAxNUgyNVYyNUgxNVYxNVoiIGZpbGw9IiNDQ0NDQ0MiLz4KPHBhdGggZD0iTTE4IDE4SDIyVjIySDE4VjE4WiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4K';\r\n  }\r\n}\r\n", "<ion-header [translucent]=\"true\">\r\n  <ion-toolbar color=\"secondary\">\r\n    <ion-buttons slot=\"start\">\r\n      <ion-menu-button></ion-menu-button>\r\n    </ion-buttons>\r\n    <ion-title>Acessórios</ion-title>\r\n  </ion-toolbar>\r\n</ion-header>\r\n\r\n<ion-content [fullscreen]=\"true\">\r\n  <ion-header collapse=\"condense\">\r\n    <ion-toolbar>\r\n      <ion-title size=\"large\">Acessórios</ion-title>\r\n    </ion-toolbar>\r\n  </ion-header>\r\n\r\n  <ion-list>\r\n    @for(accessory of accessoriesList; track accessory.id) {\r\n    <ion-item>\r\n      <ion-avatar slot=\"start\">\r\n        <img [src]=\"accessory.image\" [alt]=\"accessory.name\" (error)=\"onImageError($event)\" />\r\n      </ion-avatar>\r\n      <ion-label>\r\n        <h2>{{ accessory.name }}</h2>\r\n        <h3><strong>Categoria:</strong> {{ accessory.category }}</h3>\r\n        <p><strong>Preço:</strong> {{ accessory.price | currency: 'BRL' }}</p>\r\n        <p><strong>Compatível com:</strong>\r\n          @if(accessory.compatiblePhones && accessory.compatiblePhones.length > 0) {\r\n            @for(phone of accessory.compatiblePhones; track phone.id; let last = $last) {\r\n              {{ phone.model }}{{ !last ? ', ' : '' }}\r\n            }\r\n          } @else {\r\n            Todos os modelos\r\n          }\r\n        </p>\r\n        <p><strong>Estoque:</strong> {{ accessory.stock }} unidades</p>\r\n        <p class=\"description\"><strong>Descrição:</strong> {{ accessory.description }}</p>\r\n      </ion-label>\r\n      <ion-button slot=\"end\" size=\"small\" [routerLink]=\"['edit', accessory.id]\">\r\n        <ion-icon name=\"create\" slot=\"icon-only\"></ion-icon>\r\n      </ion-button>\r\n      <ion-button slot=\"end\" size=\"small\" color=\"danger\" (click)=\"remove(accessory)\">\r\n        <ion-icon name=\"trash\" slot=\"icon-only\"></ion-icon>\r\n      </ion-button>\r\n    </ion-item>\r\n    }\r\n    @empty {\r\n    <ion-item>Lista de acessórios vazia, cadastre um novo acessório!</ion-item>\r\n    }\r\n  </ion-list>\r\n  <ion-fab vertical=\"bottom\" horizontal=\"end\" slot=\"fixed\">\r\n    <ion-fab-button [routerLink]=\"['new']\">\r\n      <ion-icon name=\"add\"></ion-icon>\r\n    </ion-fab-button>\r\n  </ion-fab>\r\n</ion-content>\r\n"], "mappings": ";;;;;;;;;;;IC6BcA,EAAA,CAAAC,MAAA,GACF;;;;;;IADED,EAAA,CAAAE,kBAAA,MAAAC,QAAA,CAAAC,KAAA,QAAAC,aAAA,KAAAC,aAAA,uBACF;;;;;IAFAN,EAAA,CAAAO,gBAAA,IAAAC,oDAAA,oBAAAC,UAAA,CAEC;;;;IAFDT,EAAA,CAAAU,UAAA,CAAAC,YAAA,CAAAC,gBAAA,CAEC;;;;;IAEDZ,EAAA,CAAAC,MAAA,yBACF;;;;;;IAbFD,EAFJ,CAAAa,cAAA,eAAU,oBACiB,aAC8D;IAAjCb,EAAA,CAAAc,UAAA,mBAAAC,qDAAAC,MAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAN,MAAA,CAAoB;IAAA,EAAC;IACpFhB,EADE,CAAAuB,YAAA,EAAqF,EAC1E;IAEXvB,EADF,CAAAa,cAAA,gBAAW,SACL;IAAAb,EAAA,CAAAC,MAAA,GAAoB;IAAAD,EAAA,CAAAuB,YAAA,EAAK;IACzBvB,EAAJ,CAAAa,cAAA,SAAI,aAAQ;IAAAb,EAAA,CAAAC,MAAA,iBAAU;IAAAD,EAAA,CAAAuB,YAAA,EAAS;IAACvB,EAAA,CAAAC,MAAA,GAAwB;IAAAD,EAAA,CAAAuB,YAAA,EAAK;IAC1DvB,EAAH,CAAAa,cAAA,SAAG,cAAQ;IAAAb,EAAA,CAAAC,MAAA,mBAAM;IAAAD,EAAA,CAAAuB,YAAA,EAAS;IAACvB,EAAA,CAAAC,MAAA,IAAuC;;IAAAD,EAAA,CAAAuB,YAAA,EAAI;IACnEvB,EAAH,CAAAa,cAAA,SAAG,cAAQ;IAAAb,EAAA,CAAAC,MAAA,4BAAe;IAAAD,EAAA,CAAAuB,YAAA,EAAS;IAK/BvB,EAJF,CAAAwB,UAAA,KAAAC,8CAAA,OAA0E,KAAAC,8CAAA,OAIjE;IAGX1B,EAAA,CAAAuB,YAAA,EAAI;IACDvB,EAAH,CAAAa,cAAA,SAAG,cAAQ;IAAAb,EAAA,CAAAC,MAAA,gBAAQ;IAAAD,EAAA,CAAAuB,YAAA,EAAS;IAACvB,EAAA,CAAAC,MAAA,IAA8B;IAAAD,EAAA,CAAAuB,YAAA,EAAI;IACxCvB,EAAvB,CAAAa,cAAA,aAAuB,cAAQ;IAAAb,EAAA,CAAAC,MAAA,4BAAU;IAAAD,EAAA,CAAAuB,YAAA,EAAS;IAACvB,EAAA,CAAAC,MAAA,IAA2B;IAChFD,EADgF,CAAAuB,YAAA,EAAI,EACxE;IACZvB,EAAA,CAAAa,cAAA,sBAA0E;IACxEb,EAAA,CAAA2B,SAAA,oBAAoD;IACtD3B,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAa,cAAA,sBAA+E;IAA5Bb,EAAA,CAAAc,UAAA,mBAAAc,6DAAA;MAAA,MAAAjB,YAAA,GAAAX,EAAA,CAAAiB,aAAA,CAAAC,GAAA,EAAAW,SAAA;MAAA,MAAAV,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASF,MAAA,CAAAW,MAAA,CAAAnB,YAAA,CAAiB;IAAA,EAAC;IAC5EX,EAAA,CAAA2B,SAAA,oBAAmD;IAEvD3B,EADE,CAAAuB,YAAA,EAAa,EACJ;;;;IAxBFvB,EAAA,CAAA+B,SAAA,GAAuB;IAAC/B,EAAxB,CAAAgC,UAAA,QAAArB,YAAA,CAAAsB,KAAA,EAAAjC,EAAA,CAAAkC,aAAA,CAAuB,QAAAvB,YAAA,CAAAwB,IAAA,CAAuB;IAG/CnC,EAAA,CAAA+B,SAAA,GAAoB;IAApB/B,EAAA,CAAAoC,iBAAA,CAAAzB,YAAA,CAAAwB,IAAA,CAAoB;IACQnC,EAAA,CAAA+B,SAAA,GAAwB;IAAxB/B,EAAA,CAAAqC,kBAAA,MAAA1B,YAAA,CAAA2B,QAAA,KAAwB;IAC7BtC,EAAA,CAAA+B,SAAA,GAAuC;IAAvC/B,EAAA,CAAAqC,kBAAA,MAAArC,EAAA,CAAAuC,WAAA,QAAA5B,YAAA,CAAA6B,KAAA,aAAuC;IAEhExC,EAAA,CAAA+B,SAAA,GAMC;IAND/B,EAAA,CAAAyC,aAAA,CAAA9B,YAAA,CAAAC,gBAAA,IAAAD,YAAA,CAAAC,gBAAA,CAAA8B,MAAA,eAMC;IAE0B1C,EAAA,CAAA+B,SAAA,GAA8B;IAA9B/B,EAAA,CAAAqC,kBAAA,MAAA1B,YAAA,CAAAgC,KAAA,cAA8B;IACR3C,EAAA,CAAA+B,SAAA,GAA2B;IAA3B/B,EAAA,CAAAqC,kBAAA,MAAA1B,YAAA,CAAAiC,WAAA,KAA2B;IAE5C5C,EAAA,CAAA+B,SAAA,EAAqC;IAArC/B,EAAA,CAAAgC,UAAA,eAAAhC,EAAA,CAAA6C,eAAA,KAAAC,GAAA,EAAAnC,YAAA,CAAAoC,EAAA,EAAqC;;;;;IAS3E/C,EAAA,CAAAa,cAAA,eAAU;IAAAb,EAAA,CAAAC,MAAA,uEAAsD;IAAAD,EAAA,CAAAuB,YAAA,EAAW;;;ADnC/E,OAAM,MAAOyB,eAAe;EAK1BC,YACUC,gBAAkC,EAClCC,eAAgC,EAChCC,eAAgC;IAFhC,KAAAF,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IALzB,KAAAC,eAAe,GAAgB,EAAE;EAM7B;EAEJC,eAAeA,CAAA;IACbC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;EAChC;EAEAC,gBAAgBA,CAAA;IACdF,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;EACjC;EAEAE,eAAeA,CAAA;IACbH,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;EAChC;EAEAG,gBAAgBA,CAAA;IACdJ,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAE/B,IAAI,CAACN,gBAAgB,CAACU,OAAO,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACV,eAAe,GAAGU,QAAQ;MACjC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,KAAK,CAAC,sCAAsC,CAAC;QAC7CV,OAAO,CAACS,KAAK,CAACA,KAAK,CAAC;MACtB;KACD,CAAC;EACJ;EAEAE,QAAQA,CAAA,GAAK;EAEbpC,MAAMA,CAACqC,SAAoB;IACzB,IAAI,CAAChB,eAAe,CAACiB,MAAM,CAAC;MAC1BC,MAAM,EAAE,UAAU;MAClBC,OAAO,EAAE,oCAAoCH,SAAS,CAAChC,IAAI,GAAG;MAC9DoC,OAAO,EAAE,CACP;QACEC,IAAI,EAAE,KAAK;QACXC,OAAO,EAAEA,CAAA,KAAK;UACZ,IAAI,CAACvB,gBAAgB,CAACpB,MAAM,CAACqC,SAAS,CAAC,CAACN,SAAS,CAAC;YAChDC,IAAI,EAAEA,CAAA,KAAK;cACT;cACA,IAAI,CAACT,eAAe,GAAG,IAAI,CAACA,eAAe,CAACqB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAKoB,SAAS,CAACpB,EAAE,CAAC;cAC9E,IAAI,CAACK,eAAe,CAACgB,MAAM,CAAC;gBAC1BE,OAAO,EAAE,aAAaH,SAAS,CAAChC,IAAI,wBAAwB;gBAC5DyC,QAAQ,EAAE,IAAI;gBACdC,KAAK,EAAE,WAAW;gBAClBC,aAAa,EAAE;eAChB,CAAC,CAACC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,OAAO,EAAE,CAAC;YACnC,CAAC;YACDjB,KAAK,EAAGA,KAAK,IAAI;cAAA,IAAAkB,YAAA;cACf,IAAIC,YAAY,GAAG,8BAA8B,GAAGhB,SAAS,CAAChC,IAAI;cAClE,KAAA+C,YAAA,GAAIlB,KAAK,CAACA,KAAK,cAAAkB,YAAA,eAAXA,YAAA,CAAaZ,OAAO,EAAE;gBACxBa,YAAY,GAAGnB,KAAK,CAACA,KAAK,CAACM,OAAO;cACpC;cACAL,KAAK,CAACkB,YAAY,CAAC;cACnB5B,OAAO,CAACS,KAAK,CAACA,KAAK,CAAC;YACtB;WACD,CAAC;QACJ;OACD,EACD,KAAK;KAER,CAAC,CAACe,IAAI,CAACd,KAAK,IAAIA,KAAK,CAACgB,OAAO,EAAE,CAAC;EACnC;EAEA3D,YAAYA,CAAC8D,KAAU;IACrB;IACAA,KAAK,CAACC,MAAM,CAACC,GAAG,GAAG,gWAAgW;EACrX;;mBA7EWtC,eAAe;;mCAAfA,gBAAe,EAAAhD,EAAA,CAAAuF,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAzF,EAAA,CAAAuF,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA3F,EAAA,CAAAuF,iBAAA,CAAAG,EAAA,CAAAE,eAAA;AAAA;;QAAf5C,gBAAe;EAAA6C,SAAA;EAAAC,UAAA;EAAAC,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MCVxBpG,EAFJ,CAAAa,cAAA,oBAAiC,qBACA,qBACH;MACxBb,EAAA,CAAA2B,SAAA,sBAAmC;MACrC3B,EAAA,CAAAuB,YAAA,EAAc;MACdvB,EAAA,CAAAa,cAAA,gBAAW;MAAAb,EAAA,CAAAC,MAAA,sBAAU;MAEzBD,EAFyB,CAAAuB,YAAA,EAAY,EACrB,EACH;MAKPvB,EAHN,CAAAa,cAAA,qBAAiC,oBACC,kBACjB,mBACa;MAAAb,EAAA,CAAAC,MAAA,uBAAU;MAEtCD,EAFsC,CAAAuB,YAAA,EAAY,EAClC,EACH;MAEbvB,EAAA,CAAAa,cAAA,gBAAU;MACRb,EAAA,CAAAO,gBAAA,KAAA+F,+BAAA,4BAAA7F,UAAA,SAAA8F,oCAAA,mBA+BC;MACHvG,EAAA,CAAAuB,YAAA,EAAW;MAETvB,EADF,CAAAa,cAAA,kBAAyD,yBAChB;MACrCb,EAAA,CAAA2B,SAAA,mBAAgC;MAGtC3B,EAFI,CAAAuB,YAAA,EAAiB,EACT,EACE;;;MAvDFvB,EAAA,CAAAgC,UAAA,qBAAoB;MASnBhC,EAAA,CAAA+B,SAAA,GAAmB;MAAnB/B,EAAA,CAAAgC,UAAA,oBAAmB;MAQ5BhC,EAAA,CAAA+B,SAAA,GA+BC;MA/BD/B,EAAA,CAAAU,UAAA,CAAA2F,GAAA,CAAAhD,eAAA,CA+BC;MAGerD,EAAA,CAAA+B,SAAA,GAAsB;MAAtB/B,EAAA,CAAAgC,UAAA,eAAAhC,EAAA,CAAAwG,eAAA,IAAAC,GAAA,EAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}