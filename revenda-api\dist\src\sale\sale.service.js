"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaleService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const sale_entity_1 = require("./sale.entity");
const sale_item_entity_1 = require("./sale-item.entity");
let SaleService = class SaleService {
    saleRepository;
    saleItemRepository;
    constructor(saleRepository, saleItemRepository) {
        this.saleRepository = saleRepository;
        this.saleItemRepository = saleItemRepository;
    }
    async create(createSaleDto) {
        const { items, ...saleData } = createSaleDto;
        const totalValue = items.reduce((sum, item) => sum + item.subtotal, 0);
        const sale = this.saleRepository.create({
            ...saleData,
            totalValue,
        });
        const savedSale = await this.saleRepository.save(sale);
        const saleItems = items.map(item => this.saleItemRepository.create({
            ...item,
            saleId: savedSale.id,
        }));
        await this.saleItemRepository.save(saleItems);
        return this.findOne(savedSale.id);
    }
    findAll(status) {
        const where = status ? { status } : {};
        return this.saleRepository.find({
            where,
            relations: ['customer', 'store', 'items'],
            order: { date: 'DESC' },
        });
    }
    findOne(id) {
        return this.saleRepository.findOne({
            where: { id },
            relations: ['customer', 'store', 'items'],
        });
    }
    findByCustomer(customerId) {
        return this.saleRepository.find({
            where: { customerId },
            relations: ['customer', 'store', 'items'],
            order: { date: 'DESC' },
        });
    }
    findByStore(storeId) {
        return this.saleRepository.find({
            where: { storeId },
            relations: ['customer', 'store', 'items'],
            order: { date: 'DESC' },
        });
    }
    async update(id, updateSaleDto) {
        const existingSale = await this.saleRepository.findOne({ where: { id } });
        if (!existingSale) {
            throw new Error('Sale not found');
        }
        const result = await this.saleRepository.update(id, updateSaleDto);
        return this.findOne(id);
    }
    updateStatus(id, status) {
        return this.saleRepository.update(id, { status });
    }
    remove(id) {
        return this.saleRepository.delete(id);
    }
};
exports.SaleService = SaleService;
exports.SaleService = SaleService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(sale_entity_1.Sale)),
    __param(1, (0, typeorm_1.InjectRepository)(sale_item_entity_1.SaleItem)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], SaleService);
//# sourceMappingURL=sale.service.js.map