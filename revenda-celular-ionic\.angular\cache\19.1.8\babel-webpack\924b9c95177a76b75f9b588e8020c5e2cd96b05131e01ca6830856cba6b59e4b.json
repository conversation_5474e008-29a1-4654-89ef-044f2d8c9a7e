{"ast": null, "code": "var _CustomerService;\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CustomerService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = `${environment.baseUrl}/customers`;\n  }\n  getAll() {\n    return this.http.get(this.apiUrl);\n  }\n  getById(id) {\n    return this.http.get(`${this.apiUrl}/${id}`);\n  }\n  save(customer) {\n    if (customer.id) {\n      return this.http.put(`${this.apiUrl}/${customer.id}`, customer);\n    }\n    return this.http.post(this.apiUrl, customer);\n  }\n  delete(id) {\n    return this.http.delete(`${this.apiUrl}/${id}`);\n  }\n}\n_CustomerService = CustomerService;\n_CustomerService.ɵfac = function CustomerService_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _CustomerService)(i0.ɵɵinject(i1.HttpClient));\n};\n_CustomerService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: _CustomerService,\n  factory: _CustomerService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["environment", "CustomerService", "constructor", "http", "apiUrl", "baseUrl", "getAll", "get", "getById", "id", "save", "customer", "put", "post", "delete", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\sources\\integracao-revenda\\revenda-celular-ionic\\src\\app\\customers\\services\\customer.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { Customer } from '../models/customer.type';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CustomerService {\r\n  private apiUrl = `${environment.baseUrl}/customers`;\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  getAll(): Observable<Customer[]> {\r\n    return this.http.get<Customer[]>(this.apiUrl);\r\n  }\r\n\r\n  getById(id: number): Observable<Customer> {\r\n    return this.http.get<Customer>(`${this.apiUrl}/${id}`);\r\n  }\r\n\r\n  save(customer: Customer): Observable<Customer> {\r\n    if (customer.id) {\r\n      return this.http.put<Customer>(`${this.apiUrl}/${customer.id}`, customer);\r\n    }\r\n    return this.http.post<Customer>(this.apiUrl, customer);\r\n  }\r\n\r\n  delete(id: number): Observable<void> {\r\n    return this.http.delete<void>(`${this.apiUrl}/${id}`);\r\n  }\r\n}\r\n"], "mappings": ";AAIA,SAASA,WAAW,QAAQ,8BAA8B;;;AAK1D,OAAM,MAAOC,eAAe;EAG1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGJ,WAAW,CAACK,OAAO,YAAY;EAEX;EAExCC,MAAMA,CAAA;IACJ,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAa,IAAI,CAACH,MAAM,CAAC;EAC/C;EAEAI,OAAOA,CAACC,EAAU;IAChB,OAAO,IAAI,CAACN,IAAI,CAACI,GAAG,CAAW,GAAG,IAAI,CAACH,MAAM,IAAIK,EAAE,EAAE,CAAC;EACxD;EAEAC,IAAIA,CAACC,QAAkB;IACrB,IAAIA,QAAQ,CAACF,EAAE,EAAE;MACf,OAAO,IAAI,CAACN,IAAI,CAACS,GAAG,CAAW,GAAG,IAAI,CAACR,MAAM,IAAIO,QAAQ,CAACF,EAAE,EAAE,EAAEE,QAAQ,CAAC;IAC3E;IACA,OAAO,IAAI,CAACR,IAAI,CAACU,IAAI,CAAW,IAAI,CAACT,MAAM,EAAEO,QAAQ,CAAC;EACxD;EAEAG,MAAMA,CAACL,EAAU;IACf,OAAO,IAAI,CAACN,IAAI,CAACW,MAAM,CAAO,GAAG,IAAI,CAACV,MAAM,IAAIK,EAAE,EAAE,CAAC;EACvD;;mBAtBWR,eAAe;;mCAAfA,gBAAe,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;AAAA;;SAAfjB,gBAAe;EAAAkB,OAAA,EAAflB,gBAAe,CAAAmB,IAAA;EAAAC,UAAA,EAFd;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}