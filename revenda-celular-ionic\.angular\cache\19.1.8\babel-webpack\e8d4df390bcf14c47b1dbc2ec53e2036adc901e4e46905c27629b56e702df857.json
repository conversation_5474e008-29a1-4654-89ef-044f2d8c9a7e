{"ast": null, "code": "import _defineProperty from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/defineProperty.js\";\nvar _BrowserXhr, _EventManager, _SharedStylesHost, _DomRendererFactory, _DomEventsPlugin, _KeyEventsPlugin, _BrowserModule, _Meta, _Title, _HammerGestureConfig, _HammerGesturesPlugin, _HammerModule, _DomSanitizer, _DomSanitizerImpl;\n/**\n * @license Angular v19.1.7\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, isPlatformServer, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵglobal, ɵRuntimeError, Injectable, InjectionToken, Inject, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, ɵTracingService, RendererStyleFlags2, ɵinternalCreateApplication, <PERSON>rror<PERSON>and<PERSON>, ɵsetDocument, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, inject, ApplicationModule, NgModule, ApplicationRef, ɵConsole, Injector, forwardRef, ɵXSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, ɵwithI18nSupport, ɵwithEventReplay, ɵwithIncrementalHydration, ENVIRONMENT_INITIALIZER, ɵZONELESS_ENABLED, ɵformatRuntimeError, makeEnvironmentProviders, ɵwithDomHydration, Version } from '@angular/core';\nimport { ɵwithHttpTransferCache } from '@angular/common/http';\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n  constructor(...args) {\n    super(...args);\n    _defineProperty(this, \"supportsDOMEvents\", true);\n  }\n}\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n  static makeCurrent() {\n    ɵsetRootDomAdapter(new BrowserDomAdapter());\n  }\n  onAndCancel(el, evt, listener, options) {\n    el.addEventListener(evt, listener, options);\n    return () => {\n      el.removeEventListener(evt, listener, options);\n    };\n  }\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n  remove(node) {\n    node.remove();\n  }\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n  getDefaultDocument() {\n    return document;\n  }\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return doc;\n    }\n    if (target === 'body') {\n      return doc.body;\n    }\n    return null;\n  }\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n  resetBaseElement() {\n    baseElement = null;\n  }\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n  getCookie(name) {\n    return ɵparseCookieValue(document.cookie, name);\n  }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n}\nfunction relativePath(url) {\n  // The base URL doesn't really matter, we just need it so relative paths have something\n  // to resolve against. In the browser `HTMLBaseElement.href` is always absolute.\n  return new URL(url, document.baseURI).pathname;\n}\nclass BrowserGetTestability {\n  addToWindow(registry) {\n    ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new ɵRuntimeError(5103 /* RuntimeErrorCode.TESTABILITY_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Could not find testability for element.');\n      }\n      return testability;\n    };\n    ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n    ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n    const whenAllStable = callback => {\n      const testabilities = ɵglobal['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      const decrement = function () {\n        count--;\n        if (count == 0) {\n          callback();\n        }\n      };\n      testabilities.forEach(testability => {\n        testability.whenStable(decrement);\n      });\n    };\n    if (!ɵglobal['frameworkStabilizers']) {\n      ɵglobal['frameworkStabilizers'] = [];\n    }\n    ɵglobal['frameworkStabilizers'].push(whenAllStable);\n  }\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n    const t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (ɵgetDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n  build() {\n    return new XMLHttpRequest();\n  }\n}\n_BrowserXhr = BrowserXhr;\n_defineProperty(BrowserXhr, \"\\u0275fac\", function _BrowserXhr_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BrowserXhr)();\n});\n_defineProperty(BrowserXhr, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _BrowserXhr,\n  factory: _BrowserXhr.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserXhr, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    _defineProperty(this, \"_zone\", void 0);\n    _defineProperty(this, \"_plugins\", void 0);\n    _defineProperty(this, \"_eventNameToPlugin\", new Map());\n    this._zone = _zone;\n    plugins.forEach(plugin => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @param options Options that configure how the event listener is bound.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(element, eventName, handler, options) {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler, options);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n  _findPluginFor(eventName) {\n    let plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n    const plugins = this._plugins;\n    plugin = plugins.find(plugin => plugin.supports(eventName));\n    if (!plugin) {\n      throw new ɵRuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) && `No event manager plugin found for event ${eventName}`);\n    }\n    this._eventNameToPlugin.set(eventName, plugin);\n    return plugin;\n  }\n}\n_EventManager = EventManager;\n_defineProperty(EventManager, \"\\u0275fac\", function _EventManager_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n});\n_defineProperty(EventManager, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _EventManager,\n  factory: _EventManager.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [EVENT_MANAGER_PLUGINS]\n    }]\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n  // TODO: remove (has some usage in G3)\n  constructor(_doc) {\n    _defineProperty(this, \"_doc\", void 0);\n    // Using non-null assertion because it's set by EventManager's constructor\n    _defineProperty(this, \"manager\", void 0);\n    this._doc = _doc;\n  }\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\n/**\n * Removes all provided elements from the document.\n * @param elements An array of HTML Elements.\n */\nfunction removeElements(elements) {\n  for (const element of elements) {\n    element.remove();\n  }\n}\n/**\n * Creates a `style` element with the provided inline style content.\n * @param style A string of the inline style content.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLStyleElement instance.\n */\nfunction createStyleElement(style, doc) {\n  const styleElement = doc.createElement('style');\n  styleElement.textContent = style;\n  return styleElement;\n}\n/**\n * Searches a DOM document's head element for style elements with a matching application\n * identifier attribute (`ng-app-id`) to the provide identifier and adds usage records for each.\n * @param doc An HTML DOM document instance.\n * @param appId A string containing an Angular application identifer.\n * @param inline A Map object for tracking inline (defined via `styles` in component decorator) style usage.\n * @param external A Map object for tracking external (defined via `styleUrls` in component decorator) style usage.\n */\nfunction addServerStyles(doc, appId, inline, external) {\n  var _doc$head;\n  const elements = (_doc$head = doc.head) === null || _doc$head === void 0 ? void 0 : _doc$head.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"],link[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"]`);\n  if (elements) {\n    for (const styleElement of elements) {\n      styleElement.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n      if (styleElement instanceof HTMLLinkElement) {\n        // Only use filename from href\n        // The href is build time generated with a unique value to prevent duplicates.\n        external.set(styleElement.href.slice(styleElement.href.lastIndexOf('/') + 1), {\n          usage: 0,\n          elements: [styleElement]\n        });\n      } else if (styleElement.textContent) {\n        inline.set(styleElement.textContent, {\n          usage: 0,\n          elements: [styleElement]\n        });\n      }\n    }\n  }\n}\n/**\n * Creates a `link` element for the provided external style URL.\n * @param url A string of the URL for the stylesheet.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLLinkElement instance.\n */\nfunction createLinkElement(url, doc) {\n  const linkElement = doc.createElement('link');\n  linkElement.setAttribute('rel', 'stylesheet');\n  linkElement.setAttribute('href', url);\n  return linkElement;\n}\nclass SharedStylesHost {\n  constructor(doc, appId, nonce, platformId = {}) {\n    _defineProperty(this, \"doc\", void 0);\n    _defineProperty(this, \"appId\", void 0);\n    _defineProperty(this, \"nonce\", void 0);\n    /**\n     * Provides usage information for active inline style content and associated HTML <style> elements.\n     * Embedded styles typically originate from the `styles` metadata of a rendered component.\n     */\n    _defineProperty(this, \"inline\", new Map());\n    /**\n     * Provides usage information for active external style URLs and the associated HTML <link> elements.\n     * External styles typically originate from the `ɵɵExternalStylesFeature` of a rendered component.\n     */\n    _defineProperty(this, \"external\", new Map());\n    /**\n     * Set of host DOM nodes that will have styles attached.\n     */\n    _defineProperty(this, \"hosts\", new Set());\n    /**\n     * Whether the application code is currently executing on a server.\n     */\n    _defineProperty(this, \"isServer\", void 0);\n    this.doc = doc;\n    this.appId = appId;\n    this.nonce = nonce;\n    this.isServer = isPlatformServer(platformId);\n    addServerStyles(doc, appId, this.inline, this.external);\n    this.hosts.add(doc.head);\n  }\n  /**\n   * Adds embedded styles to the DOM via HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  addStyles(styles, urls) {\n    for (const value of styles) {\n      this.addUsage(value, this.inline, createStyleElement);\n    }\n    urls === null || urls === void 0 || urls.forEach(value => this.addUsage(value, this.external, createLinkElement));\n  }\n  /**\n   * Removes embedded styles from the DOM that were added as HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  removeStyles(styles, urls) {\n    for (const value of styles) {\n      this.removeUsage(value, this.inline);\n    }\n    urls === null || urls === void 0 || urls.forEach(value => this.removeUsage(value, this.external));\n  }\n  addUsage(value, usages, creator) {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n    // If existing, just increment the usage count\n    if (record) {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && record.usage === 0) {\n        // A usage count of zero indicates a preexisting server generated style.\n        // This attribute is solely used for debugging purposes of SSR style reuse.\n        record.elements.forEach(element => element.setAttribute('ng-style-reused', ''));\n      }\n      record.usage++;\n    } else {\n      // Otherwise, create an entry to track the elements and add element for each host\n      usages.set(value, {\n        usage: 1,\n        elements: [...this.hosts].map(host => this.addElement(host, creator(value, this.doc)))\n      });\n    }\n  }\n  removeUsage(value, usages) {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n    // If there is a record, reduce the usage count and if no longer used,\n    // remove from DOM and delete usage record.\n    if (record) {\n      record.usage--;\n      if (record.usage <= 0) {\n        removeElements(record.elements);\n        usages.delete(value);\n      }\n    }\n  }\n  ngOnDestroy() {\n    for (const [, {\n      elements\n    }] of [...this.inline, ...this.external]) {\n      removeElements(elements);\n    }\n    this.hosts.clear();\n  }\n  /**\n   * Adds a host node to the set of style hosts and adds all existing style usage to\n   * the newly added host node.\n   *\n   * This is currently only used for Shadow DOM encapsulation mode.\n   */\n  addHost(hostNode) {\n    this.hosts.add(hostNode);\n    // Add existing styles to new host\n    for (const [style, {\n      elements\n    }] of this.inline) {\n      elements.push(this.addElement(hostNode, createStyleElement(style, this.doc)));\n    }\n    for (const [url, {\n      elements\n    }] of this.external) {\n      elements.push(this.addElement(hostNode, createLinkElement(url, this.doc)));\n    }\n  }\n  removeHost(hostNode) {\n    this.hosts.delete(hostNode);\n  }\n  addElement(host, element) {\n    // Add a nonce if present\n    if (this.nonce) {\n      element.setAttribute('nonce', this.nonce);\n    }\n    // Add application identifier when on the server to support client-side reuse\n    if (this.isServer) {\n      element.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n    }\n    // Insert the element into the DOM with the host node as parent\n    return host.appendChild(element);\n  }\n}\n_SharedStylesHost = SharedStylesHost;\n_defineProperty(SharedStylesHost, \"\\u0275fac\", function _SharedStylesHost_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _SharedStylesHost)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(APP_ID), i0.ɵɵinject(CSP_NONCE, 8), i0.ɵɵinject(PLATFORM_ID));\n});\n_defineProperty(SharedStylesHost, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _SharedStylesHost,\n  factory: _SharedStylesHost.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], null);\n})();\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/Math/MathML'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst SOURCEMAP_URL_REGEXP = /\\/\\*#\\s*sourceMappingURL=(.+?)\\s*\\*\\//;\nconst PROTOCOL_REGEXP = /^https?:/;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n  providedIn: 'root',\n  factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT\n});\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n  return styles.map(s => s.replace(COMPONENT_REGEX, compId));\n}\n/**\n * Prepends a baseHref to the `sourceMappingURL` within the provided CSS content.\n * If the `sourceMappingURL` contains an inline (encoded) map, the function skips processing.\n *\n * @note For inline stylesheets, the `sourceMappingURL` is relative to the page's origin\n * and not the provided baseHref. This function is needed as when accessing the page with a URL\n * containing two or more segments.\n * For example, if the baseHref is set to `/`, and you visit a URL like `http://localhost/foo/bar`,\n * the map would be requested from `http://localhost/foo/bar/comp.css.map` instead of what you'd expect,\n * which is `http://localhost/comp.css.map`. This behavior is corrected by modifying the `sourceMappingURL`\n * to ensure external source maps are loaded relative to the baseHref.\n *\n\n * @param baseHref - The base URL to prepend to the `sourceMappingURL`.\n * @param styles - An array of CSS content strings, each potentially containing a `sourceMappingURL`.\n * @returns The updated array of CSS content strings with modified `sourceMappingURL` values,\n * or the original content if no modification is needed.\n */\nfunction addBaseHrefToCssSourceMap(baseHref, styles) {\n  if (!baseHref) {\n    return styles;\n  }\n  const absoluteBaseHrefUrl = new URL(baseHref, 'http://localhost');\n  return styles.map(cssContent => {\n    if (!cssContent.includes('sourceMappingURL=')) {\n      return cssContent;\n    }\n    return cssContent.replace(SOURCEMAP_URL_REGEXP, (_, sourceMapUrl) => {\n      if (sourceMapUrl[0] === '/' || sourceMapUrl.startsWith('data:') || PROTOCOL_REGEXP.test(sourceMapUrl)) {\n        return `/*# sourceMappingURL=${sourceMapUrl} */`;\n      }\n      const {\n        pathname: resolvedSourceMapUrl\n      } = new URL(sourceMapUrl, absoluteBaseHrefUrl);\n      return `/*# sourceMappingURL=${resolvedSourceMapUrl} */`;\n    });\n  });\n}\nclass DomRendererFactory2 {\n  constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null, tracingService = null) {\n    _defineProperty(this, \"eventManager\", void 0);\n    _defineProperty(this, \"sharedStylesHost\", void 0);\n    _defineProperty(this, \"appId\", void 0);\n    _defineProperty(this, \"removeStylesOnCompDestroy\", void 0);\n    _defineProperty(this, \"doc\", void 0);\n    _defineProperty(this, \"platformId\", void 0);\n    _defineProperty(this, \"ngZone\", void 0);\n    _defineProperty(this, \"nonce\", void 0);\n    _defineProperty(this, \"tracingService\", void 0);\n    _defineProperty(this, \"rendererByCompId\", new Map());\n    _defineProperty(this, \"defaultRenderer\", void 0);\n    _defineProperty(this, \"platformIsServer\", void 0);\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    this.doc = doc;\n    this.platformId = platformId;\n    this.ngZone = ngZone;\n    this.nonce = nonce;\n    this.tracingService = tracingService;\n    this.platformIsServer = isPlatformServer(platformId);\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer, this.tracingService);\n  }\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n      // Domino does not support shadow DOM.\n      type = {\n        ...type,\n        encapsulation: ViewEncapsulation.Emulated\n      };\n    }\n    const renderer = this.getOrCreateRenderer(element, type);\n    // Renderers have different logic due to different encapsulation behaviours.\n    // Ex: for emulated, an attribute is added to the element.\n    if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n      renderer.applyToHost(element);\n    } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n      renderer.applyStyles();\n    }\n    return renderer;\n  }\n  getOrCreateRenderer(element, type) {\n    const rendererByCompId = this.rendererByCompId;\n    let renderer = rendererByCompId.get(type.id);\n    if (!renderer) {\n      const doc = this.doc;\n      const ngZone = this.ngZone;\n      const eventManager = this.eventManager;\n      const sharedStylesHost = this.sharedStylesHost;\n      const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n      const platformIsServer = this.platformIsServer;\n      const tracingService = this.tracingService;\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n          break;\n        case ViewEncapsulation.ShadowDom:\n          return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer, tracingService);\n        default:\n          renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n          break;\n      }\n      rendererByCompId.set(type.id, renderer);\n    }\n    return renderer;\n  }\n  ngOnDestroy() {\n    this.rendererByCompId.clear();\n  }\n  /**\n   * Used during HMR to clear any cached data about a component.\n   * @param componentId ID of the component that is being replaced.\n   */\n  componentReplaced(componentId) {\n    this.rendererByCompId.delete(componentId);\n  }\n}\n_DomRendererFactory = DomRendererFactory2;\n_defineProperty(DomRendererFactory2, \"\\u0275fac\", function _DomRendererFactory_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _DomRendererFactory)(i0.ɵɵinject(EventManager), i0.ɵɵinject(SharedStylesHost), i0.ɵɵinject(APP_ID), i0.ɵɵinject(REMOVE_STYLES_ON_COMPONENT_DESTROY), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(CSP_NONCE), i0.ɵɵinject(ɵTracingService, 8));\n});\n_defineProperty(DomRendererFactory2, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _DomRendererFactory,\n  factory: _DomRendererFactory.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], () => [{\n    type: EventManager\n  }, {\n    type: SharedStylesHost\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n    }]\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }]\n  }, {\n    type: i0.ɵTracingService,\n    decorators: [{\n      type: Inject,\n      args: [ɵTracingService]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\nclass DefaultDomRenderer2 {\n  constructor(eventManager, doc, ngZone, platformIsServer, tracingService) {\n    _defineProperty(this, \"eventManager\", void 0);\n    _defineProperty(this, \"doc\", void 0);\n    _defineProperty(this, \"ngZone\", void 0);\n    _defineProperty(this, \"platformIsServer\", void 0);\n    _defineProperty(this, \"tracingService\", void 0);\n    _defineProperty(this, \"data\", Object.create(null));\n    /**\n     * By default this renderer throws when encountering synthetic properties\n     * This can be disabled for example by the AsyncAnimationRendererFactory\n     */\n    _defineProperty(this, \"throwOnSyntheticProps\", true);\n    _defineProperty(this, \"destroyNode\", null);\n    this.eventManager = eventManager;\n    this.doc = doc;\n    this.ngZone = ngZone;\n    this.platformIsServer = platformIsServer;\n    this.tracingService = tracingService;\n  }\n  destroy() {}\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n    return this.doc.createElement(name);\n  }\n  createComment(value) {\n    return this.doc.createComment(value);\n  }\n  createText(value) {\n    return this.doc.createTextNode(value);\n  }\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n  removeChild(_parent, oldChild) {\n    oldChild.remove();\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n    if (!el) {\n      throw new ɵRuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && `The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n  parentNode(node) {\n    return node.parentNode;\n  }\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n  setProperty(el, name, value) {\n    if (el == null) {\n      return;\n    }\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n  listen(target, event, callback, options) {\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      target = ɵgetDOM().getGlobalEventTarget(this.doc, target);\n      if (!target) {\n        throw new Error(`Unsupported event target ${target} for event ${event}`);\n      }\n    }\n    let wrappedCallback = this.decoratePreventDefault(callback);\n    if (this.tracingService !== null && this.tracingService.wrapEventListener) {\n      wrappedCallback = this.tracingService.wrapEventListener(target, event, wrappedCallback);\n    }\n    return this.eventManager.addEventListener(target, event, wrappedCallback, options);\n  }\n  decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n    // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n    // unwrap the listener (see below).\n    return event => {\n      // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n      // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n      // debug_node can inspect the listener toString contents for the existence of this special\n      // token. Because the token is a string literal, it is ensured to not be modified by compiled\n      // code.\n      if (event === '__ngUnwrap__') {\n        return eventHandler;\n      }\n      // Run the event handler inside the ngZone because event handlers are not patched\n      // by Zone on the server. This is required only for tests.\n      const allowDefaultBehavior = this.platformIsServer ? this.ngZone.runGuarded(() => eventHandler(event)) : eventHandler(event);\n      if (allowDefaultBehavior === false) {\n        event.preventDefault();\n      }\n      return undefined;\n    };\n  }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new ɵRuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Make sure \\`provideAnimationsAsync()\\`, \\`provideAnimations()\\` or \\`provideNoopAnimations()\\` call was added to a list of providers used to bootstrap an application.\n  - There is a corresponding animation configuration named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.dev/api/core/Component#animations).`);\n  }\n}\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer, tracingService) {\n    var _component$getExterna;\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    _defineProperty(this, \"sharedStylesHost\", void 0);\n    _defineProperty(this, \"hostEl\", void 0);\n    _defineProperty(this, \"shadowRoot\", void 0);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    let styles = component.styles;\n    if (ngDevMode) {\n      var _ɵgetDOM$getBaseHref;\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = (_ɵgetDOM$getBaseHref = ɵgetDOM().getBaseHref(doc)) !== null && _ɵgetDOM$getBaseHref !== void 0 ? _ɵgetDOM$getBaseHref : '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n    styles = shimStylesContent(component.id, styles);\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n      if (nonce) {\n        styleEl.setAttribute('nonce', nonce);\n      }\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n    // Apply any external component styles to the shadow root for the component's element.\n    // The ShadowDOM renderer uses an alternative execution path for component styles that\n    // does not use the SharedStylesHost that other encapsulation modes leverage. Much like\n    // the manual addition of embedded styles directly above, any external stylesheets\n    // must be manually added here to ensure ShadowDOM components are correctly styled.\n    // TODO: Consider reworking the DOM Renderers to consolidate style handling.\n    const styleUrls = (_component$getExterna = component.getExternalStyles) === null || _component$getExterna === void 0 ? void 0 : _component$getExterna.call(component);\n    if (styleUrls) {\n      for (const styleUrl of styleUrls) {\n        const linkEl = createLinkElement(styleUrl, doc);\n        if (nonce) {\n          linkEl.setAttribute('nonce', nonce);\n        }\n        this.shadowRoot.appendChild(linkEl);\n      }\n    }\n  }\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  removeChild(_parent, oldChild) {\n    return super.removeChild(null, oldChild);\n  }\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId) {\n    var _component$getExterna2;\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    _defineProperty(this, \"sharedStylesHost\", void 0);\n    _defineProperty(this, \"removeStylesOnCompDestroy\", void 0);\n    _defineProperty(this, \"styles\", void 0);\n    _defineProperty(this, \"styleUrls\", void 0);\n    this.sharedStylesHost = sharedStylesHost;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    let styles = component.styles;\n    if (ngDevMode) {\n      var _ɵgetDOM$getBaseHref2;\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = (_ɵgetDOM$getBaseHref2 = ɵgetDOM().getBaseHref(doc)) !== null && _ɵgetDOM$getBaseHref2 !== void 0 ? _ɵgetDOM$getBaseHref2 : '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n    this.styles = compId ? shimStylesContent(compId, styles) : styles;\n    this.styleUrls = (_component$getExterna2 = component.getExternalStyles) === null || _component$getExterna2 === void 0 ? void 0 : _component$getExterna2.call(component, compId);\n  }\n  applyStyles() {\n    this.sharedStylesHost.addStyles(this.styles, this.styleUrls);\n  }\n  destroy() {\n    if (!this.removeStylesOnCompDestroy) {\n      return;\n    }\n    this.sharedStylesHost.removeStyles(this.styles, this.styleUrls);\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService) {\n    const compId = appId + '-' + component.id;\n    super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId);\n    _defineProperty(this, \"contentAttr\", void 0);\n    _defineProperty(this, \"hostAttr\", void 0);\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n  applyToHost(element) {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\nclass DomEventsPlugin extends EventManagerPlugin {\n  constructor(doc) {\n    super(doc);\n  }\n  // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n  supports(eventName) {\n    return true;\n  }\n  addEventListener(element, eventName, handler, options) {\n    element.addEventListener(eventName, handler, options);\n    return () => this.removeEventListener(element, eventName, handler, options);\n  }\n  removeEventListener(target, eventName, callback, options) {\n    return target.removeEventListener(eventName, callback, options);\n  }\n}\n_DomEventsPlugin = DomEventsPlugin;\n_defineProperty(DomEventsPlugin, \"\\u0275fac\", function _DomEventsPlugin_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n});\n_defineProperty(DomEventsPlugin, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _DomEventsPlugin,\n  factory: _DomEventsPlugin.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomEventsPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(doc) {\n    super(doc);\n  }\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n  supports(eventName) {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n  addEventListener(element, eventName, handler, options) {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n    const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n    return this.manager.getZone().runOutsideAngular(() => {\n      return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler, options);\n    });\n  }\n  /**\n   * Parses the user provided full keyboard event definition and normalizes it for\n   * later internal use. It ensures the string is all lowercase, converts special\n   * characters to a standard spelling, and orders all the values consistently.\n   *\n   * @param eventName The name of the key event to listen for.\n   * @returns an object with the full, normalized string, and the dom event name\n   * or null in the case when the event doesn't match a keyboard event.\n   */\n  static parseEventName(eventName) {\n    const parts = eventName.toLowerCase().split('.');\n    const domEventName = parts.shift();\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n    const key = KeyEventsPlugin._normalizeKey(parts.pop());\n    let fullKey = '';\n    let codeIX = parts.indexOf('code');\n    if (codeIX > -1) {\n      parts.splice(codeIX, 1);\n      fullKey = 'code.';\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n    // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n    const result = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n  /**\n   * Determines whether the actual keys pressed match the configured key code string.\n   * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n   * event is attached to the DOM during the `addEventListener` call. This is unseen\n   * by the end user and is normalized for internal consistency and parsing.\n   *\n   * @param event The keyboard event.\n   * @param fullKeyCode The normalized user defined expected key event string\n   * @returns boolean.\n   */\n  static matchEventFullKeyCode(event, fullKeyCode) {\n    let keycode = _keyMap[event.key] || event.key;\n    let key = '';\n    if (fullKeyCode.indexOf('code.') > -1) {\n      keycode = event.code;\n      key = 'code.';\n    }\n    // the keycode could be unidentified so we have to check here\n    if (keycode == null || !keycode) return false;\n    keycode = keycode.toLowerCase();\n    if (keycode === ' ') {\n      keycode = 'space'; // for readability\n    } else if (keycode === '.') {\n      keycode = 'dot'; // because '.' is used as a separator in event names\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName !== keycode) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          key += modifierName + '.';\n        }\n      }\n    });\n    key += keycode;\n    return key === fullKeyCode;\n  }\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n  static eventCallback(fullKey, handler, zone) {\n    return event => {\n      if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n  /** @internal */\n  static _normalizeKey(keyName) {\n    return keyName === 'esc' ? 'escape' : keyName;\n  }\n}\n_KeyEventsPlugin = KeyEventsPlugin;\n_defineProperty(KeyEventsPlugin, \"\\u0275fac\", function _KeyEventsPlugin_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n});\n_defineProperty(KeyEventsPlugin, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _KeyEventsPlugin,\n  factory: _KeyEventsPlugin.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyEventsPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```angular-ts\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```ts\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n  return ɵinternalCreateApplication({\n    rootComponent,\n    ...createProvidersConfig(options)\n  });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n  return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n  var _options$providers;\n  return {\n    appProviders: [...BROWSER_MODULE_PROVIDERS, ...((_options$providers = options === null || options === void 0 ? void 0 : options.providers) !== null && _options$providers !== void 0 ? _options$providers : [])],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n  };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app\n  // code.\n  return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n  return new ErrorHandler();\n}\nfunction _document() {\n  // Tell ivy about the global document\n  ɵsetDocument(document);\n  return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document,\n  deps: []\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [{\n  provide: ɵTESTABILITY_GETTER,\n  useClass: BrowserGetTestability,\n  deps: []\n}, {\n  provide: ɵTESTABILITY,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}, {\n  provide: Testability,\n  // Also provide as `Testability` for backwards-compatibility.\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}];\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: ɵINJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler,\n  deps: []\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, DomRendererFactory2, SharedStylesHost, EventManager, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr,\n  deps: []\n}, typeof ngDevMode === 'undefined' || ngDevMode ? {\n  provide: BROWSER_MODULE_PROVIDERS_MARKER,\n  useValue: true\n} : []];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const providersAlreadyPresent = inject(BROWSER_MODULE_PROVIDERS_MARKER, {\n        optional: true,\n        skipSelf: true\n      });\n      if (providersAlreadyPresent) {\n        throw new ɵRuntimeError(5100 /* RuntimeErrorCode.BROWSER_MODULE_ALREADY_LOADED */, `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` + `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n      }\n    }\n  }\n}\n_BrowserModule = BrowserModule;\n_defineProperty(BrowserModule, \"\\u0275fac\", function _BrowserModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _BrowserModule)();\n});\n_defineProperty(BrowserModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _BrowserModule\n}));\n_defineProperty(BrowserModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n  imports: [CommonModule, ApplicationModule]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserModule, [{\n    type: NgModule,\n    args: [{\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      exports: [CommonModule, ApplicationModule]\n    }]\n  }], () => [], null);\n})();\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n  constructor(_doc) {\n    _defineProperty(this, \"_doc\", void 0);\n    _defineProperty(this, \"_dom\", void 0);\n    this._doc = _doc;\n    this._dom = ɵgetDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element = this._dom.createElement('meta');\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n}\n_Meta = Meta;\n_defineProperty(Meta, \"\\u0275fac\", function _Meta_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _Meta)(i0.ɵɵinject(DOCUMENT));\n});\n_defineProperty(Meta, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _Meta,\n  factory: _Meta.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n  constructor(_doc) {\n    _defineProperty(this, \"_doc\", void 0);\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n}\n_Title = Title;\n_defineProperty(Title, \"\\u0275fac\", function _Title_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _Title)(i0.ɵɵinject(DOCUMENT));\n});\n_defineProperty(Title, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _Title,\n  factory: _Title.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n    ng[name] = value;\n  }\n}\nclass ChangeDetectionPerfRecord {\n  constructor(msPerTick, numTicks) {\n    _defineProperty(this, \"msPerTick\", void 0);\n    _defineProperty(this, \"numTicks\", void 0);\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n  constructor(ref) {\n    _defineProperty(this, \"appRef\", void 0);\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```ts\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    if (record && 'profile' in console && typeof console.profile === 'function') {\n      console.profile(profileName);\n    }\n    const start = performance.now();\n    let numTicks = 0;\n    while (numTicks < 5 || performance.now() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performance.now();\n    if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n      console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    console.log(`ran ${numTicks} change detection cycles`);\n    console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n}\nfunction elementMatches(n, selector) {\n  if (ɵgetDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n  return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerGestureConfig' : '');\n/**\n * Injection token used to provide a HammerLoader to Angular.\n *\n * @see {@link HammerLoader}\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerLoader' : '');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n  constructor() {\n    /**\n     * A set of supported event names for gestures to be used in Angular.\n     * Angular supports all built-in recognizers, as listed in\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    _defineProperty(this, \"events\", []);\n    /**\n     * Maps gesture event names to a set of configuration options\n     * that specify overrides to the default values for specific properties.\n     *\n     * The key is a supported event name to be configured,\n     * and the options object contains a set of properties, with override values\n     * to be applied to the named recognizer event.\n     * For example, to disable recognition of the rotate event, specify\n     *  `{\"rotate\": {\"enable\": false}}`.\n     *\n     * Properties that are not present take the HammerJS default values.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     *\n     */\n    _defineProperty(this, \"overrides\", {});\n    /**\n     * Properties whose default values can be overridden for a given event.\n     * Different sets of properties apply to different events.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    _defineProperty(this, \"options\", void 0);\n  }\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n    return mc;\n  }\n}\n_HammerGestureConfig = HammerGestureConfig;\n_defineProperty(HammerGestureConfig, \"\\u0275fac\", function _HammerGestureConfig_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HammerGestureConfig)();\n});\n_defineProperty(HammerGestureConfig, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _HammerGestureConfig,\n  factory: _HammerGestureConfig.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  constructor(doc, _config, _injector, loader) {\n    super(doc);\n    _defineProperty(this, \"_config\", void 0);\n    _defineProperty(this, \"_injector\", void 0);\n    _defineProperty(this, \"loader\", void 0);\n    _defineProperty(this, \"_loaderPromise\", null);\n    this._config = _config;\n    this._injector = _injector;\n    this.loader = loader;\n  }\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // Get a `Console` through an injector to tree-shake the\n        // class when it is unused in production.\n        const _console = this._injector.get(ɵConsole);\n        _console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n      return false;\n    }\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const _console = this._injector.get(ɵConsole);\n            _console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n          deregister = () => {};\n          return;\n        }\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          const _console = this._injector.get(ɵConsole);\n          _console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n        deregister = () => {};\n      }));\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n}\n_HammerGesturesPlugin = HammerGesturesPlugin;\n_defineProperty(HammerGesturesPlugin, \"\\u0275fac\", function _HammerGesturesPlugin_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(HAMMER_LOADER, 8));\n});\n_defineProperty(HammerGesturesPlugin, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _HammerGesturesPlugin,\n  factory: _HammerGesturesPlugin.ɵfac\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: HammerGestureConfig,\n    decorators: [{\n      type: Inject,\n      args: [HAMMER_GESTURE_CONFIG]\n    }]\n  }, {\n    type: i0.Injector\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [HAMMER_LOADER]\n    }]\n  }], null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n */\nclass HammerModule {}\n_HammerModule = HammerModule;\n_defineProperty(HammerModule, \"\\u0275fac\", function _HammerModule_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _HammerModule)();\n});\n_defineProperty(HammerModule, \"\\u0275mod\", /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: _HammerModule\n}));\n_defineProperty(HammerModule, \"\\u0275inj\", /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: EVENT_MANAGER_PLUGINS,\n    useClass: HammerGesturesPlugin,\n    multi: true,\n    deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]]\n  }, {\n    provide: HAMMER_GESTURE_CONFIG,\n    useClass: HammerGestureConfig,\n    deps: []\n  }]\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig,\n        deps: []\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {}\n_DomSanitizer = DomSanitizer;\n_defineProperty(DomSanitizer, \"\\u0275fac\", function _DomSanitizer_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _DomSanitizer)();\n});\n_defineProperty(DomSanitizer, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _DomSanitizer,\n  factory: function _DomSanitizer_Factory(__ngFactoryType__) {\n    let __ngConditionalFactory__ = null;\n    if (__ngFactoryType__) {\n      __ngConditionalFactory__ = new (__ngFactoryType__ || _DomSanitizer)();\n    } else {\n      __ngConditionalFactory__ = i0.ɵɵinject(DomSanitizerImpl);\n    }\n    return __ngConditionalFactory__;\n  },\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\nclass DomSanitizerImpl extends DomSanitizer {\n  constructor(_doc) {\n    super();\n    _defineProperty(this, \"_doc\", void 0);\n    this._doc = _doc;\n  }\n  sanitize(ctx, value) {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n      case SecurityContext.HTML:\n        if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return value;\n      case SecurityContext.SCRIPT:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        throw new ɵRuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return ɵ_sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        throw new ɵRuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) && `unsafe value used in a resource URL context (see ${ɵXSS_SECURITY_URL})`);\n      default:\n        throw new ɵRuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unexpected SecurityContext ${ctx} (see ${ɵXSS_SECURITY_URL})`);\n    }\n  }\n  bypassSecurityTrustHtml(value) {\n    return ɵbypassSanitizationTrustHtml(value);\n  }\n  bypassSecurityTrustStyle(value) {\n    return ɵbypassSanitizationTrustStyle(value);\n  }\n  bypassSecurityTrustScript(value) {\n    return ɵbypassSanitizationTrustScript(value);\n  }\n  bypassSecurityTrustUrl(value) {\n    return ɵbypassSanitizationTrustUrl(value);\n  }\n  bypassSecurityTrustResourceUrl(value) {\n    return ɵbypassSanitizationTrustResourceUrl(value);\n  }\n}\n_DomSanitizerImpl = DomSanitizerImpl;\n_defineProperty(DomSanitizerImpl, \"\\u0275fac\", function _DomSanitizerImpl_Factory(__ngFactoryType__) {\n  return new (__ngFactoryType__ || _DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n});\n_defineProperty(DomSanitizerImpl, \"\\u0275prov\", /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: _DomSanitizerImpl,\n  factory: _DomSanitizerImpl.ɵfac,\n  providedIn: 'root'\n}));\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nvar HydrationFeatureKind;\n(function (HydrationFeatureKind) {\n  HydrationFeatureKind[HydrationFeatureKind[\"NoHttpTransferCache\"] = 0] = \"NoHttpTransferCache\";\n  HydrationFeatureKind[HydrationFeatureKind[\"HttpTransferCacheOptions\"] = 1] = \"HttpTransferCacheOptions\";\n  HydrationFeatureKind[HydrationFeatureKind[\"I18nSupport\"] = 2] = \"I18nSupport\";\n  HydrationFeatureKind[HydrationFeatureKind[\"EventReplay\"] = 3] = \"EventReplay\";\n  HydrationFeatureKind[HydrationFeatureKind[\"IncrementalHydration\"] = 4] = \"IncrementalHydration\";\n})(HydrationFeatureKind || (HydrationFeatureKind = {}));\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(ɵkind, ɵproviders = [], ɵoptions = {}) {\n  return {\n    ɵkind,\n    ɵproviders\n  };\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nfunction withNoHttpTransferCache() {\n  // This feature has no providers and acts as a flag that turns off\n  // HTTP transfer cache (which otherwise is turned on by default).\n  return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n/**\n * The function accepts an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * whether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nfunction withHttpTransferCacheOptions(options) {\n  // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n  return hydrationFeature(HydrationFeatureKind.HttpTransferCacheOptions, ɵwithHttpTransferCache(options));\n}\n/**\n * Enables support for hydrating i18n blocks.\n *\n * @developerPreview\n * @publicApi\n */\nfunction withI18nSupport() {\n  return hydrationFeature(HydrationFeatureKind.I18nSupport, ɵwithI18nSupport());\n}\n/**\n * Enables support for replaying user events (e.g. `click`s) that happened on a page\n * before hydration logic has completed. Once an application is hydrated, all captured\n * events are replayed and relevant event listeners are executed.\n *\n * @usageNotes\n *\n * Basic example of how you can enable event replay in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withEventReplay())]\n * });\n * ```\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withEventReplay() {\n  return hydrationFeature(HydrationFeatureKind.EventReplay, ɵwithEventReplay());\n}\n/**\n * Enables support for incremental hydration using the `hydrate` trigger syntax.\n *\n * @usageNotes\n *\n * Basic example of how you can enable incremental hydration in your application when\n * the `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withIncrementalHydration())]\n * });\n * ```\n * @experimental\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withIncrementalHydration() {\n  return hydrationFeature(HydrationFeatureKind.IncrementalHydration, ɵwithIncrementalHydration());\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n  return [{\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      const ngZone = inject(NgZone);\n      const isZoneless = inject(ɵZONELESS_ENABLED);\n      // Checking `ngZone instanceof NgZone` would be insufficient here,\n      // because custom implementations might use NgZone as a base class.\n      if (!isZoneless && ngZone.constructor !== NgZone) {\n        const console = inject(ɵConsole);\n        const message = ɵformatRuntimeError(-5000 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' + 'that uses a custom or a noop Zone.js implementation. ' + 'This is not yet a fully supported configuration.');\n        console.warn(message);\n      }\n    },\n    multi: true\n  }];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or enable new ones:\n *\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n * * {@link withI18nSupport} to enable hydration support for i18n blocks\n * * {@link withEventReplay} to enable support for replaying user events\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```ts\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n * @see {@link withI18nSupport}\n * @see {@link withEventReplay}\n *\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi\n */\nfunction provideClientHydration(...features) {\n  const providers = [];\n  const featuresKind = new Set();\n  const hasHttpTransferCacheOptions = featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n  for (const {\n    ɵproviders,\n    ɵkind\n  } of features) {\n    featuresKind.add(ɵkind);\n    if (ɵproviders.length) {\n      providers.push(ɵproviders);\n    }\n  }\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) && hasHttpTransferCacheOptions) {\n    // TODO: Make this a runtime error\n    throw new Error('Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n  }\n  return makeEnvironmentProviders([typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [], ɵwithDomHydration(), featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions ? [] : ɵwithHttpTransferCache({}), providers]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('19.1.7');\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, HydrationFeatureKind, Meta, REMOVE_STYLES_ON_COMPONENT_DESTROY, Title, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, platformBrowser, provideClientHydration, provideProtractorTestingSupport, withEventReplay, withHttpTransferCacheOptions, withI18nSupport, withIncrementalHydration, withNoHttpTransferCache, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, SharedStylesHost as ɵSharedStylesHost, initDomAdapter as ɵinitDomAdapter };", "map": {"version": 3, "names": ["ɵDomAdapter", "ɵsetRootDomAdapter", "ɵparseCookieValue", "ɵgetDOM", "isPlatformServer", "DOCUMENT", "ɵPLATFORM_BROWSER_ID", "XhrFactory", "CommonModule", "i0", "ɵglobal", "ɵRuntimeError", "Injectable", "InjectionToken", "Inject", "APP_ID", "CSP_NONCE", "PLATFORM_ID", "Optional", "ViewEncapsulation", "ɵTracingService", "RendererStyleFlags2", "ɵinternalCreateApplication", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵsetDocument", "PLATFORM_INITIALIZER", "createPlatformFactory", "platformCore", "ɵTESTABILITY_GETTER", "ɵTESTABILITY", "Testability", "NgZone", "TestabilityRegistry", "ɵINJECTOR_SCOPE", "RendererFactory2", "inject", "ApplicationModule", "NgModule", "ApplicationRef", "ɵConsole", "Injector", "forwardRef", "ɵXSS_SECURITY_URL", "SecurityContext", "ɵallowSanitizationBypassAndThrow", "ɵunwrapSafeValue", "ɵ_sanitizeUrl", "ɵ_sanitizeHtml", "ɵbypassSanitizationTrustHtml", "ɵbypassSanitizationTrustStyle", "ɵbypassSanitizationTrustScript", "ɵbypassSanitizationTrustUrl", "ɵbypassSanitizationTrustResourceUrl", "ɵwithI18nSupport", "ɵwithEventReplay", "ɵwithIncrementalHydration", "ENVIRONMENT_INITIALIZER", "ɵZONELESS_ENABLED", "ɵformatRuntimeError", "makeEnvironmentProviders", "ɵwithDomHydration", "Version", "ɵwithHttpTransferCache", "GenericBrowserDomAdapter", "constructor", "args", "_defineProperty", "BrowserDomAdapter", "makeCurrent", "onAndCancel", "el", "evt", "listener", "options", "addEventListener", "removeEventListener", "dispatchEvent", "remove", "node", "createElement", "tagName", "doc", "getDefaultDocument", "createHtmlDocument", "document", "implementation", "createHTMLDocument", "isElementNode", "nodeType", "Node", "ELEMENT_NODE", "isShadowRoot", "DocumentFragment", "getGlobalEventTarget", "target", "window", "body", "getBaseHref", "href", "getBaseElementHref", "relativePath", "resetBaseElement", "baseElement", "getUserAgent", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "name", "cookie", "querySelector", "getAttribute", "url", "URL", "baseURI", "pathname", "BrowserGetTestability", "addToWindow", "registry", "elem", "findInAncestors", "testability", "findTestabilityInTree", "ngDevMode", "getAllTestabilities", "getAllRootElements", "whenAllStable", "callback", "testabilities", "count", "length", "decrement", "for<PERSON>ach", "whenStable", "push", "t", "getTestability", "host", "parentElement", "BrowserXhr", "build", "XMLHttpRequest", "_BrowserXhr", "_BrowserXhr_Factory", "__ngFactoryType__", "ɵɵdefineInjectable", "token", "factory", "ɵfac", "ɵsetClassMetadata", "type", "EVENT_MANAGER_PLUGINS", "EventManager", "plugins", "_zone", "Map", "plugin", "manager", "_plugins", "slice", "reverse", "element", "eventName", "handler", "_findPluginFor", "getZone", "_eventNameToPlugin", "get", "find", "supports", "set", "_EventManager", "_EventManager_Factory", "ɵɵinject", "undefined", "decorators", "EventManagerPlugin", "_doc", "APP_ID_ATTRIBUTE_NAME", "removeElements", "elements", "createStyleElement", "style", "styleElement", "textContent", "addServerStyles", "appId", "inline", "external", "_doc$head", "head", "querySelectorAll", "removeAttribute", "HTMLLinkElement", "lastIndexOf", "usage", "createLinkElement", "linkElement", "setAttribute", "SharedStylesHost", "nonce", "platformId", "Set", "isServer", "hosts", "add", "addStyles", "styles", "urls", "value", "addUsage", "removeStyles", "removeUsage", "usages", "creator", "record", "map", "addElement", "delete", "ngOnDestroy", "clear", "addHost", "hostNode", "removeHost", "append<PERSON><PERSON><PERSON>", "_SharedStylesHost", "_SharedStylesHost_Factory", "Document", "NAMESPACE_URIS", "COMPONENT_REGEX", "SOURCEMAP_URL_REGEXP", "PROTOCOL_REGEXP", "COMPONENT_VARIABLE", "HOST_ATTR", "CONTENT_ATTR", "REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT", "REMOVE_STYLES_ON_COMPONENT_DESTROY", "providedIn", "shimContentAttribute", "componentShortId", "replace", "shimHostAttribute", "shim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "compId", "s", "addBaseHrefToCssSourceMap", "baseHref", "absoluteBaseHrefUrl", "cssContent", "includes", "_", "sourceMapUrl", "startsWith", "test", "resolvedSourceMapUrl", "DomRendererFactory2", "eventManager", "sharedStylesHost", "removeStylesOnCompDestroy", "ngZone", "tracingService", "platformIsServer", "defaultRenderer", "DefaultDomRenderer2", "<PERSON><PERSON><PERSON><PERSON>", "encapsulation", "ShadowDom", "Emulated", "renderer", "getOr<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmulatedEncapsulationDomRenderer2", "applyToHost", "NoneEncapsulationDomRenderer", "applyStyles", "rendererByCompId", "id", "ShadowDom<PERSON><PERSON><PERSON>", "componentReplaced", "componentId", "_DomRendererFactory", "_DomRendererFactory_Factory", "Object", "create", "destroy", "namespace", "createElementNS", "createComment", "createText", "createTextNode", "parent", "<PERSON><PERSON><PERSON><PERSON>", "targetParent", "isTemplateNode", "content", "insertBefore", "refChild", "<PERSON><PERSON><PERSON><PERSON>", "_parent", "<PERSON><PERSON><PERSON><PERSON>", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "parentNode", "nextS<PERSON>ling", "namespaceUri", "setAttributeNS", "removeAttributeNS", "addClass", "classList", "removeClass", "setStyle", "flags", "DashCase", "Important", "setProperty", "removeStyle", "removeProperty", "throwOnSyntheticProps", "checkNoSyntheticProp", "setValue", "nodeValue", "listen", "event", "Error", "wrappedCallback", "decoratePreventDefault", "wrapEventListener", "<PERSON><PERSON><PERSON><PERSON>", "allowDefaultBehavior", "runGuarded", "preventDefault", "AT_CHARCODE", "charCodeAt", "<PERSON><PERSON><PERSON>", "hostEl", "component", "_component$getExterna", "shadowRoot", "attachShadow", "mode", "_ɵgetDOM$getBaseHref", "styleEl", "styleUrls", "getExternalStyles", "call", "styleUrl", "linkEl", "nodeOrShadowRoot", "_component$getExterna2", "_ɵgetDOM$getBaseHref2", "contentAttr", "hostAttr", "DomEventsPlugin", "_DomEventsPlugin", "_DomEventsPlugin_Factory", "MODIFIER_KEYS", "_keyMap", "MODIFIER_KEY_GETTERS", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "KeyEventsPlugin", "parseEventName", "parsedEvent", "outsideH<PERSON>ler", "eventCallback", "runOutsideAngular", "parts", "toLowerCase", "split", "domEventName", "shift", "key", "_normalizeKey", "pop", "<PERSON><PERSON><PERSON>", "codeIX", "indexOf", "splice", "modifierName", "index", "result", "matchEventFullKeyCode", "fullKeyCode", "keycode", "code", "modifierGetter", "zone", "keyName", "_KeyEventsPlugin", "_KeyEventsPlugin_Factory", "bootstrapApplication", "rootComponent", "createProvidersConfig", "createApplication", "_options$providers", "appProviders", "BROWSER_MODULE_PROVIDERS", "providers", "platformProviders", "INTERNAL_BROWSER_PLATFORM_PROVIDERS", "provideProtractorTestingSupport", "TESTABILITY_PROVIDERS", "initDomAdapter", "<PERSON><PERSON><PERSON><PERSON>", "_document", "provide", "useValue", "multi", "useFactory", "deps", "platformBrowser", "BROWSER_MODULE_PROVIDERS_MARKER", "useClass", "useExisting", "BrowserModule", "providersAlreadyPresent", "optional", "skipSelf", "_BrowserModule", "_BrowserModule_Factory", "ɵɵdefineNgModule", "ɵɵdefineInjector", "imports", "exports", "Meta", "_dom", "addTag", "tag", "forceCreation", "_getOrCreateElement", "addTags", "tags", "reduce", "getTag", "attrSelector", "getTags", "list", "updateTag", "selector", "_parseSelector", "meta", "_setMetaElementAttributes", "removeTag", "removeTagElement", "filter", "_containsAttributes", "getElementsByTagName", "keys", "prop", "_getMetaKeyMap", "attr", "every", "META_KEYS_MAP", "_Meta", "_Meta_Factory", "httpEquiv", "Title", "getTitle", "title", "setTitle", "newTitle", "_Title", "_Title_Factory", "exportNgVar", "COMPILED", "ng", "ChangeDetectionPerfRecord", "msPerTick", "numTicks", "AngularProfiler", "ref", "appRef", "injector", "timeChangeDetection", "config", "profileName", "console", "profile", "start", "performance", "now", "tick", "end", "profileEnd", "log", "toFixed", "PROFILER_GLOBAL_NAME", "enableDebugTools", "disableDebugTools", "By", "all", "css", "debugElement", "nativeElement", "elementMatches", "directive", "debugNode", "providerTokens", "n", "matches", "msMatchesSelector", "webkitMatchesSelector", "EVENT_NAMES", "HAMMER_GESTURE_CONFIG", "HAMMER_LOADER", "HammerGestureConfig", "buildHammer", "mc", "Hammer", "enable", "overrides", "_HammerGestureConfig", "_HammerGestureConfig_Factory", "HammerGesturesPlugin", "_config", "_injector", "loader", "hasOwnProperty", "isCustomEvent", "_console", "warn", "_loaderPromise", "cancelRegistration", "deregister", "then", "catch", "eventObj", "on", "off", "events", "_HammerGesturesPlugin", "_HammerGesturesPlugin_Factory", "HammerModule", "_HammerModule", "_HammerModule_Factory", "Dom<PERSON><PERSON><PERSON>zer", "_DomSani<PERSON>zer", "_DomSanitizer_Factory", "__ngConditionalFactory__", "DomSanitizerImpl", "sanitize", "ctx", "NONE", "HTML", "String", "toString", "STYLE", "SCRIPT", "RESOURCE_URL", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustScript", "bypassSecurityTrustUrl", "bypassSecurityTrustResourceUrl", "_DomSanitizerImpl", "_DomSanitizerImpl_Factory", "HydrationFeatureKind", "hydrationFeature", "ɵkind", "ɵproviders", "ɵoptions", "withNoHttpTransferCache", "NoHttpTransferCache", "withHttpTransferCacheOptions", "HttpTransferCacheOptions", "withI18nSupport", "I18nSupport", "withEventReplay", "EventReplay", "withIncrementalHydration", "IncrementalHydration", "provideZoneJsCompatibilityDetector", "isZoneless", "message", "provideClientHydration", "features", "featuresKind", "hasHttpTransferCacheOptions", "has", "VERSION", "ɵBrowserDomAdapter", "ɵBrowserGetTestability", "ɵDomEventsPlugin", "ɵDomRendererFactory2", "ɵDomSanitizerImpl", "ɵHammerGesturesPlugin", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "ɵKeyEventsPlugin", "ɵSharedStylesHost", "ɵinitDomAdapter"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v19.1.7\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, isPlatformServer, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵglobal, ɵRuntimeError, Injectable, InjectionToken, Inject, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, ɵTracingService, RendererStyleFlags2, ɵinternalCreateApplication, <PERSON>rror<PERSON>and<PERSON>, ɵsetDocument, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, inject, ApplicationModule, NgModule, ApplicationRef, ɵConsole, Injector, forwardRef, ɵXSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, ɵwithI18nSupport, ɵwithEventReplay, ɵwithIncrementalHydration, ENVIRONMENT_INITIALIZER, ɵZONELESS_ENABLED, ɵformatRuntimeError, makeEnvironmentProviders, ɵwithDomHydration, Version } from '@angular/core';\nimport { ɵwithHttpTransferCache } from '@angular/common/http';\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n    supportsDOMEvents = true;\n}\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n    static makeCurrent() {\n        ɵsetRootDomAdapter(new BrowserDomAdapter());\n    }\n    onAndCancel(el, evt, listener, options) {\n        el.addEventListener(evt, listener, options);\n        return () => {\n            el.removeEventListener(evt, listener, options);\n        };\n    }\n    dispatchEvent(el, evt) {\n        el.dispatchEvent(evt);\n    }\n    remove(node) {\n        node.remove();\n    }\n    createElement(tagName, doc) {\n        doc = doc || this.getDefaultDocument();\n        return doc.createElement(tagName);\n    }\n    createHtmlDocument() {\n        return document.implementation.createHTMLDocument('fakeTitle');\n    }\n    getDefaultDocument() {\n        return document;\n    }\n    isElementNode(node) {\n        return node.nodeType === Node.ELEMENT_NODE;\n    }\n    isShadowRoot(node) {\n        return node instanceof DocumentFragment;\n    }\n    /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n    getGlobalEventTarget(doc, target) {\n        if (target === 'window') {\n            return window;\n        }\n        if (target === 'document') {\n            return doc;\n        }\n        if (target === 'body') {\n            return doc.body;\n        }\n        return null;\n    }\n    getBaseHref(doc) {\n        const href = getBaseElementHref();\n        return href == null ? null : relativePath(href);\n    }\n    resetBaseElement() {\n        baseElement = null;\n    }\n    getUserAgent() {\n        return window.navigator.userAgent;\n    }\n    getCookie(name) {\n        return ɵparseCookieValue(document.cookie, name);\n    }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n    baseElement = baseElement || document.querySelector('base');\n    return baseElement ? baseElement.getAttribute('href') : null;\n}\nfunction relativePath(url) {\n    // The base URL doesn't really matter, we just need it so relative paths have something\n    // to resolve against. In the browser `HTMLBaseElement.href` is always absolute.\n    return new URL(url, document.baseURI).pathname;\n}\n\nclass BrowserGetTestability {\n    addToWindow(registry) {\n        ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n            const testability = registry.findTestabilityInTree(elem, findInAncestors);\n            if (testability == null) {\n                throw new ɵRuntimeError(5103 /* RuntimeErrorCode.TESTABILITY_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    'Could not find testability for element.');\n            }\n            return testability;\n        };\n        ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n        ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n        const whenAllStable = (callback) => {\n            const testabilities = ɵglobal['getAllAngularTestabilities']();\n            let count = testabilities.length;\n            const decrement = function () {\n                count--;\n                if (count == 0) {\n                    callback();\n                }\n            };\n            testabilities.forEach((testability) => {\n                testability.whenStable(decrement);\n            });\n        };\n        if (!ɵglobal['frameworkStabilizers']) {\n            ɵglobal['frameworkStabilizers'] = [];\n        }\n        ɵglobal['frameworkStabilizers'].push(whenAllStable);\n    }\n    findTestabilityInTree(registry, elem, findInAncestors) {\n        if (elem == null) {\n            return null;\n        }\n        const t = registry.getTestability(elem);\n        if (t != null) {\n            return t;\n        }\n        else if (!findInAncestors) {\n            return null;\n        }\n        if (ɵgetDOM().isShadowRoot(elem)) {\n            return this.findTestabilityInTree(registry, elem.host, true);\n        }\n        return this.findTestabilityInTree(registry, elem.parentElement, true);\n    }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n    build() {\n        return new XMLHttpRequest();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: BrowserXhr, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: BrowserXhr });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: BrowserXhr, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n    _zone;\n    _plugins;\n    _eventNameToPlugin = new Map();\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n        this._zone = _zone;\n        plugins.forEach((plugin) => {\n            plugin.manager = this;\n        });\n        this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @param options Options that configure how the event listener is bound.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n    addEventListener(element, eventName, handler, options) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addEventListener(element, eventName, handler, options);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n    getZone() {\n        return this._zone;\n    }\n    /** @internal */\n    _findPluginFor(eventName) {\n        let plugin = this._eventNameToPlugin.get(eventName);\n        if (plugin) {\n            return plugin;\n        }\n        const plugins = this._plugins;\n        plugin = plugins.find((plugin) => plugin.supports(eventName));\n        if (!plugin) {\n            throw new ɵRuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `No event manager plugin found for event ${eventName}`);\n        }\n        this._eventNameToPlugin.set(eventName, plugin);\n        return plugin;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: EventManager, deps: [{ token: EVENT_MANAGER_PLUGINS }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: EventManager });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: EventManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [EVENT_MANAGER_PLUGINS]\n                }] }, { type: i0.NgZone }] });\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n    _doc;\n    // TODO: remove (has some usage in G3)\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    // Using non-null assertion because it's set by EventManager's constructor\n    manager;\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\n/**\n * Removes all provided elements from the document.\n * @param elements An array of HTML Elements.\n */\nfunction removeElements(elements) {\n    for (const element of elements) {\n        element.remove();\n    }\n}\n/**\n * Creates a `style` element with the provided inline style content.\n * @param style A string of the inline style content.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLStyleElement instance.\n */\nfunction createStyleElement(style, doc) {\n    const styleElement = doc.createElement('style');\n    styleElement.textContent = style;\n    return styleElement;\n}\n/**\n * Searches a DOM document's head element for style elements with a matching application\n * identifier attribute (`ng-app-id`) to the provide identifier and adds usage records for each.\n * @param doc An HTML DOM document instance.\n * @param appId A string containing an Angular application identifer.\n * @param inline A Map object for tracking inline (defined via `styles` in component decorator) style usage.\n * @param external A Map object for tracking external (defined via `styleUrls` in component decorator) style usage.\n */\nfunction addServerStyles(doc, appId, inline, external) {\n    const elements = doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"],link[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"]`);\n    if (elements) {\n        for (const styleElement of elements) {\n            styleElement.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n            if (styleElement instanceof HTMLLinkElement) {\n                // Only use filename from href\n                // The href is build time generated with a unique value to prevent duplicates.\n                external.set(styleElement.href.slice(styleElement.href.lastIndexOf('/') + 1), {\n                    usage: 0,\n                    elements: [styleElement],\n                });\n            }\n            else if (styleElement.textContent) {\n                inline.set(styleElement.textContent, { usage: 0, elements: [styleElement] });\n            }\n        }\n    }\n}\n/**\n * Creates a `link` element for the provided external style URL.\n * @param url A string of the URL for the stylesheet.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLLinkElement instance.\n */\nfunction createLinkElement(url, doc) {\n    const linkElement = doc.createElement('link');\n    linkElement.setAttribute('rel', 'stylesheet');\n    linkElement.setAttribute('href', url);\n    return linkElement;\n}\nclass SharedStylesHost {\n    doc;\n    appId;\n    nonce;\n    /**\n     * Provides usage information for active inline style content and associated HTML <style> elements.\n     * Embedded styles typically originate from the `styles` metadata of a rendered component.\n     */\n    inline = new Map();\n    /**\n     * Provides usage information for active external style URLs and the associated HTML <link> elements.\n     * External styles typically originate from the `ɵɵExternalStylesFeature` of a rendered component.\n     */\n    external = new Map();\n    /**\n     * Set of host DOM nodes that will have styles attached.\n     */\n    hosts = new Set();\n    /**\n     * Whether the application code is currently executing on a server.\n     */\n    isServer;\n    constructor(doc, appId, nonce, platformId = {}) {\n        this.doc = doc;\n        this.appId = appId;\n        this.nonce = nonce;\n        this.isServer = isPlatformServer(platformId);\n        addServerStyles(doc, appId, this.inline, this.external);\n        this.hosts.add(doc.head);\n    }\n    /**\n     * Adds embedded styles to the DOM via HTML `style` elements.\n     * @param styles An array of style content strings.\n     */\n    addStyles(styles, urls) {\n        for (const value of styles) {\n            this.addUsage(value, this.inline, createStyleElement);\n        }\n        urls?.forEach((value) => this.addUsage(value, this.external, createLinkElement));\n    }\n    /**\n     * Removes embedded styles from the DOM that were added as HTML `style` elements.\n     * @param styles An array of style content strings.\n     */\n    removeStyles(styles, urls) {\n        for (const value of styles) {\n            this.removeUsage(value, this.inline);\n        }\n        urls?.forEach((value) => this.removeUsage(value, this.external));\n    }\n    addUsage(value, usages, creator) {\n        // Attempt to get any current usage of the value\n        const record = usages.get(value);\n        // If existing, just increment the usage count\n        if (record) {\n            if ((typeof ngDevMode === 'undefined' || ngDevMode) && record.usage === 0) {\n                // A usage count of zero indicates a preexisting server generated style.\n                // This attribute is solely used for debugging purposes of SSR style reuse.\n                record.elements.forEach((element) => element.setAttribute('ng-style-reused', ''));\n            }\n            record.usage++;\n        }\n        else {\n            // Otherwise, create an entry to track the elements and add element for each host\n            usages.set(value, {\n                usage: 1,\n                elements: [...this.hosts].map((host) => this.addElement(host, creator(value, this.doc))),\n            });\n        }\n    }\n    removeUsage(value, usages) {\n        // Attempt to get any current usage of the value\n        const record = usages.get(value);\n        // If there is a record, reduce the usage count and if no longer used,\n        // remove from DOM and delete usage record.\n        if (record) {\n            record.usage--;\n            if (record.usage <= 0) {\n                removeElements(record.elements);\n                usages.delete(value);\n            }\n        }\n    }\n    ngOnDestroy() {\n        for (const [, { elements }] of [...this.inline, ...this.external]) {\n            removeElements(elements);\n        }\n        this.hosts.clear();\n    }\n    /**\n     * Adds a host node to the set of style hosts and adds all existing style usage to\n     * the newly added host node.\n     *\n     * This is currently only used for Shadow DOM encapsulation mode.\n     */\n    addHost(hostNode) {\n        this.hosts.add(hostNode);\n        // Add existing styles to new host\n        for (const [style, { elements }] of this.inline) {\n            elements.push(this.addElement(hostNode, createStyleElement(style, this.doc)));\n        }\n        for (const [url, { elements }] of this.external) {\n            elements.push(this.addElement(hostNode, createLinkElement(url, this.doc)));\n        }\n    }\n    removeHost(hostNode) {\n        this.hosts.delete(hostNode);\n    }\n    addElement(host, element) {\n        // Add a nonce if present\n        if (this.nonce) {\n            element.setAttribute('nonce', this.nonce);\n        }\n        // Add application identifier when on the server to support client-side reuse\n        if (this.isServer) {\n            element.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n        }\n        // Insert the element into the DOM with the host node as parent\n        return host.appendChild(element);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: SharedStylesHost, deps: [{ token: DOCUMENT }, { token: APP_ID }, { token: CSP_NONCE, optional: true }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: SharedStylesHost });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: SharedStylesHost, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }] });\n\nconst NAMESPACE_URIS = {\n    'svg': 'http://www.w3.org/2000/svg',\n    'xhtml': 'http://www.w3.org/1999/xhtml',\n    'xlink': 'http://www.w3.org/1999/xlink',\n    'xml': 'http://www.w3.org/XML/1998/namespace',\n    'xmlns': 'http://www.w3.org/2000/xmlns/',\n    'math': 'http://www.w3.org/1998/Math/MathML',\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst SOURCEMAP_URL_REGEXP = /\\/\\*#\\s*sourceMappingURL=(.+?)\\s*\\*\\//;\nconst PROTOCOL_REGEXP = /^https?:/;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n    providedIn: 'root',\n    factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT,\n});\nfunction shimContentAttribute(componentShortId) {\n    return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n    return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n    return styles.map((s) => s.replace(COMPONENT_REGEX, compId));\n}\n/**\n * Prepends a baseHref to the `sourceMappingURL` within the provided CSS content.\n * If the `sourceMappingURL` contains an inline (encoded) map, the function skips processing.\n *\n * @note For inline stylesheets, the `sourceMappingURL` is relative to the page's origin\n * and not the provided baseHref. This function is needed as when accessing the page with a URL\n * containing two or more segments.\n * For example, if the baseHref is set to `/`, and you visit a URL like `http://localhost/foo/bar`,\n * the map would be requested from `http://localhost/foo/bar/comp.css.map` instead of what you'd expect,\n * which is `http://localhost/comp.css.map`. This behavior is corrected by modifying the `sourceMappingURL`\n * to ensure external source maps are loaded relative to the baseHref.\n *\n\n * @param baseHref - The base URL to prepend to the `sourceMappingURL`.\n * @param styles - An array of CSS content strings, each potentially containing a `sourceMappingURL`.\n * @returns The updated array of CSS content strings with modified `sourceMappingURL` values,\n * or the original content if no modification is needed.\n */\nfunction addBaseHrefToCssSourceMap(baseHref, styles) {\n    if (!baseHref) {\n        return styles;\n    }\n    const absoluteBaseHrefUrl = new URL(baseHref, 'http://localhost');\n    return styles.map((cssContent) => {\n        if (!cssContent.includes('sourceMappingURL=')) {\n            return cssContent;\n        }\n        return cssContent.replace(SOURCEMAP_URL_REGEXP, (_, sourceMapUrl) => {\n            if (sourceMapUrl[0] === '/' ||\n                sourceMapUrl.startsWith('data:') ||\n                PROTOCOL_REGEXP.test(sourceMapUrl)) {\n                return `/*# sourceMappingURL=${sourceMapUrl} */`;\n            }\n            const { pathname: resolvedSourceMapUrl } = new URL(sourceMapUrl, absoluteBaseHrefUrl);\n            return `/*# sourceMappingURL=${resolvedSourceMapUrl} */`;\n        });\n    });\n}\nclass DomRendererFactory2 {\n    eventManager;\n    sharedStylesHost;\n    appId;\n    removeStylesOnCompDestroy;\n    doc;\n    platformId;\n    ngZone;\n    nonce;\n    tracingService;\n    rendererByCompId = new Map();\n    defaultRenderer;\n    platformIsServer;\n    constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null, tracingService = null) {\n        this.eventManager = eventManager;\n        this.sharedStylesHost = sharedStylesHost;\n        this.appId = appId;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        this.doc = doc;\n        this.platformId = platformId;\n        this.ngZone = ngZone;\n        this.nonce = nonce;\n        this.tracingService = tracingService;\n        this.platformIsServer = isPlatformServer(platformId);\n        this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer, this.tracingService);\n    }\n    createRenderer(element, type) {\n        if (!element || !type) {\n            return this.defaultRenderer;\n        }\n        if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n            // Domino does not support shadow DOM.\n            type = { ...type, encapsulation: ViewEncapsulation.Emulated };\n        }\n        const renderer = this.getOrCreateRenderer(element, type);\n        // Renderers have different logic due to different encapsulation behaviours.\n        // Ex: for emulated, an attribute is added to the element.\n        if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n            renderer.applyToHost(element);\n        }\n        else if (renderer instanceof NoneEncapsulationDomRenderer) {\n            renderer.applyStyles();\n        }\n        return renderer;\n    }\n    getOrCreateRenderer(element, type) {\n        const rendererByCompId = this.rendererByCompId;\n        let renderer = rendererByCompId.get(type.id);\n        if (!renderer) {\n            const doc = this.doc;\n            const ngZone = this.ngZone;\n            const eventManager = this.eventManager;\n            const sharedStylesHost = this.sharedStylesHost;\n            const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n            const platformIsServer = this.platformIsServer;\n            const tracingService = this.tracingService;\n            switch (type.encapsulation) {\n                case ViewEncapsulation.Emulated:\n                    renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n                    break;\n                case ViewEncapsulation.ShadowDom:\n                    return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer, tracingService);\n                default:\n                    renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n                    break;\n            }\n            rendererByCompId.set(type.id, renderer);\n        }\n        return renderer;\n    }\n    ngOnDestroy() {\n        this.rendererByCompId.clear();\n    }\n    /**\n     * Used during HMR to clear any cached data about a component.\n     * @param componentId ID of the component that is being replaced.\n     */\n    componentReplaced(componentId) {\n        this.rendererByCompId.delete(componentId);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: DomRendererFactory2, deps: [{ token: EventManager }, { token: SharedStylesHost }, { token: APP_ID }, { token: REMOVE_STYLES_ON_COMPONENT_DESTROY }, { token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.NgZone }, { token: CSP_NONCE }, { token: ɵTracingService, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: DomRendererFactory2 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: DomRendererFactory2, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: EventManager }, { type: SharedStylesHost }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n                }] }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }] }, { type: i0.ɵTracingService, decorators: [{\n                    type: Inject,\n                    args: [ɵTracingService]\n                }, {\n                    type: Optional\n                }] }] });\nclass DefaultDomRenderer2 {\n    eventManager;\n    doc;\n    ngZone;\n    platformIsServer;\n    tracingService;\n    data = Object.create(null);\n    /**\n     * By default this renderer throws when encountering synthetic properties\n     * This can be disabled for example by the AsyncAnimationRendererFactory\n     */\n    throwOnSyntheticProps = true;\n    constructor(eventManager, doc, ngZone, platformIsServer, tracingService) {\n        this.eventManager = eventManager;\n        this.doc = doc;\n        this.ngZone = ngZone;\n        this.platformIsServer = platformIsServer;\n        this.tracingService = tracingService;\n    }\n    destroy() { }\n    destroyNode = null;\n    createElement(name, namespace) {\n        if (namespace) {\n            // TODO: `|| namespace` was added in\n            // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n            // support how Ivy passed around the namespace URI rather than short name at the time. It did\n            // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n            // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n            // namespaces should be and make it consistent.\n            // Related issues:\n            // https://github.com/angular/angular/issues/44028\n            // https://github.com/angular/angular/issues/44883\n            return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n        }\n        return this.doc.createElement(name);\n    }\n    createComment(value) {\n        return this.doc.createComment(value);\n    }\n    createText(value) {\n        return this.doc.createTextNode(value);\n    }\n    appendChild(parent, newChild) {\n        const targetParent = isTemplateNode(parent) ? parent.content : parent;\n        targetParent.appendChild(newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        if (parent) {\n            const targetParent = isTemplateNode(parent) ? parent.content : parent;\n            targetParent.insertBefore(newChild, refChild);\n        }\n    }\n    removeChild(_parent, oldChild) {\n        oldChild.remove();\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n        if (!el) {\n            throw new ɵRuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `The selector \"${selectorOrNode}\" did not match any elements`);\n        }\n        if (!preserveContent) {\n            el.textContent = '';\n        }\n        return el;\n    }\n    parentNode(node) {\n        return node.parentNode;\n    }\n    nextSibling(node) {\n        return node.nextSibling;\n    }\n    setAttribute(el, name, value, namespace) {\n        if (namespace) {\n            name = namespace + ':' + name;\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.setAttributeNS(namespaceUri, name, value);\n            }\n            else {\n                el.setAttribute(name, value);\n            }\n        }\n        else {\n            el.setAttribute(name, value);\n        }\n    }\n    removeAttribute(el, name, namespace) {\n        if (namespace) {\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.removeAttributeNS(namespaceUri, name);\n            }\n            else {\n                el.removeAttribute(`${namespace}:${name}`);\n            }\n        }\n        else {\n            el.removeAttribute(name);\n        }\n    }\n    addClass(el, name) {\n        el.classList.add(name);\n    }\n    removeClass(el, name) {\n        el.classList.remove(name);\n    }\n    setStyle(el, style, value, flags) {\n        if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n            el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n        }\n        else {\n            el.style[style] = value;\n        }\n    }\n    removeStyle(el, style, flags) {\n        if (flags & RendererStyleFlags2.DashCase) {\n            // removeProperty has no effect when used on camelCased properties.\n            el.style.removeProperty(style);\n        }\n        else {\n            el.style[style] = '';\n        }\n    }\n    setProperty(el, name, value) {\n        if (el == null) {\n            return;\n        }\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this.throwOnSyntheticProps &&\n            checkNoSyntheticProp(name, 'property');\n        el[name] = value;\n    }\n    setValue(node, value) {\n        node.nodeValue = value;\n    }\n    listen(target, event, callback, options) {\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this.throwOnSyntheticProps &&\n            checkNoSyntheticProp(event, 'listener');\n        if (typeof target === 'string') {\n            target = ɵgetDOM().getGlobalEventTarget(this.doc, target);\n            if (!target) {\n                throw new Error(`Unsupported event target ${target} for event ${event}`);\n            }\n        }\n        let wrappedCallback = this.decoratePreventDefault(callback);\n        if (this.tracingService !== null && this.tracingService.wrapEventListener) {\n            wrappedCallback = this.tracingService.wrapEventListener(target, event, wrappedCallback);\n        }\n        return this.eventManager.addEventListener(target, event, wrappedCallback, options);\n    }\n    decoratePreventDefault(eventHandler) {\n        // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n        // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n        // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n        // unwrap the listener (see below).\n        return (event) => {\n            // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n            // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n            // debug_node can inspect the listener toString contents for the existence of this special\n            // token. Because the token is a string literal, it is ensured to not be modified by compiled\n            // code.\n            if (event === '__ngUnwrap__') {\n                return eventHandler;\n            }\n            // Run the event handler inside the ngZone because event handlers are not patched\n            // by Zone on the server. This is required only for tests.\n            const allowDefaultBehavior = this.platformIsServer\n                ? this.ngZone.runGuarded(() => eventHandler(event))\n                : eventHandler(event);\n            if (allowDefaultBehavior === false) {\n                event.preventDefault();\n            }\n            return undefined;\n        };\n    }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n    if (name.charCodeAt(0) === AT_CHARCODE) {\n        throw new ɵRuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Make sure \\`provideAnimationsAsync()\\`, \\`provideAnimations()\\` or \\`provideNoopAnimations()\\` call was added to a list of providers used to bootstrap an application.\n  - There is a corresponding animation configuration named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.dev/api/core/Component#animations).`);\n    }\n}\nfunction isTemplateNode(node) {\n    return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n    sharedStylesHost;\n    hostEl;\n    shadowRoot;\n    constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer, tracingService) {\n        super(eventManager, doc, ngZone, platformIsServer, tracingService);\n        this.sharedStylesHost = sharedStylesHost;\n        this.hostEl = hostEl;\n        this.shadowRoot = hostEl.attachShadow({ mode: 'open' });\n        this.sharedStylesHost.addHost(this.shadowRoot);\n        let styles = component.styles;\n        if (ngDevMode) {\n            // We only do this in development, as for production users should not add CSS sourcemaps to components.\n            const baseHref = ɵgetDOM().getBaseHref(doc) ?? '';\n            styles = addBaseHrefToCssSourceMap(baseHref, styles);\n        }\n        styles = shimStylesContent(component.id, styles);\n        for (const style of styles) {\n            const styleEl = document.createElement('style');\n            if (nonce) {\n                styleEl.setAttribute('nonce', nonce);\n            }\n            styleEl.textContent = style;\n            this.shadowRoot.appendChild(styleEl);\n        }\n        // Apply any external component styles to the shadow root for the component's element.\n        // The ShadowDOM renderer uses an alternative execution path for component styles that\n        // does not use the SharedStylesHost that other encapsulation modes leverage. Much like\n        // the manual addition of embedded styles directly above, any external stylesheets\n        // must be manually added here to ensure ShadowDOM components are correctly styled.\n        // TODO: Consider reworking the DOM Renderers to consolidate style handling.\n        const styleUrls = component.getExternalStyles?.();\n        if (styleUrls) {\n            for (const styleUrl of styleUrls) {\n                const linkEl = createLinkElement(styleUrl, doc);\n                if (nonce) {\n                    linkEl.setAttribute('nonce', nonce);\n                }\n                this.shadowRoot.appendChild(linkEl);\n            }\n        }\n    }\n    nodeOrShadowRoot(node) {\n        return node === this.hostEl ? this.shadowRoot : node;\n    }\n    appendChild(parent, newChild) {\n        return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n    }\n    removeChild(_parent, oldChild) {\n        return super.removeChild(null, oldChild);\n    }\n    parentNode(node) {\n        return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n    }\n    destroy() {\n        this.sharedStylesHost.removeHost(this.shadowRoot);\n    }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n    sharedStylesHost;\n    removeStylesOnCompDestroy;\n    styles;\n    styleUrls;\n    constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId) {\n        super(eventManager, doc, ngZone, platformIsServer, tracingService);\n        this.sharedStylesHost = sharedStylesHost;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        let styles = component.styles;\n        if (ngDevMode) {\n            // We only do this in development, as for production users should not add CSS sourcemaps to components.\n            const baseHref = ɵgetDOM().getBaseHref(doc) ?? '';\n            styles = addBaseHrefToCssSourceMap(baseHref, styles);\n        }\n        this.styles = compId ? shimStylesContent(compId, styles) : styles;\n        this.styleUrls = component.getExternalStyles?.(compId);\n    }\n    applyStyles() {\n        this.sharedStylesHost.addStyles(this.styles, this.styleUrls);\n    }\n    destroy() {\n        if (!this.removeStylesOnCompDestroy) {\n            return;\n        }\n        this.sharedStylesHost.removeStyles(this.styles, this.styleUrls);\n    }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n    contentAttr;\n    hostAttr;\n    constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService) {\n        const compId = appId + '-' + component.id;\n        super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId);\n        this.contentAttr = shimContentAttribute(compId);\n        this.hostAttr = shimHostAttribute(compId);\n    }\n    applyToHost(element) {\n        this.applyStyles();\n        this.setAttribute(element, this.hostAttr, '');\n    }\n    createElement(parent, name) {\n        const el = super.createElement(parent, name);\n        super.setAttribute(el, this.contentAttr, '');\n        return el;\n    }\n}\n\nclass DomEventsPlugin extends EventManagerPlugin {\n    constructor(doc) {\n        super(doc);\n    }\n    // This plugin should come last in the list of plugins, because it accepts all\n    // events.\n    supports(eventName) {\n        return true;\n    }\n    addEventListener(element, eventName, handler, options) {\n        element.addEventListener(eventName, handler, options);\n        return () => this.removeEventListener(element, eventName, handler, options);\n    }\n    removeEventListener(target, eventName, callback, options) {\n        return target.removeEventListener(eventName, callback, options);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: DomEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: DomEventsPlugin });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: DomEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n    '\\b': 'Backspace',\n    '\\t': 'Tab',\n    '\\x7F': 'Delete',\n    '\\x1B': 'Escape',\n    'Del': 'Delete',\n    'Esc': 'Escape',\n    'Left': 'ArrowLeft',\n    'Right': 'ArrowRight',\n    'Up': 'ArrowUp',\n    'Down': 'ArrowDown',\n    'Menu': 'ContextMenu',\n    'Scroll': 'ScrollLock',\n    'Win': 'OS',\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n    'alt': (event) => event.altKey,\n    'control': (event) => event.ctrlKey,\n    'meta': (event) => event.metaKey,\n    'shift': (event) => event.shiftKey,\n};\n/**\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n    /**\n     * Initializes an instance of the browser plug-in.\n     * @param doc The document in which key events will be detected.\n     */\n    constructor(doc) {\n        super(doc);\n    }\n    /**\n     * Reports whether a named key event is supported.\n     * @param eventName The event name to query.\n     * @return True if the named key event is supported.\n     */\n    supports(eventName) {\n        return KeyEventsPlugin.parseEventName(eventName) != null;\n    }\n    /**\n     * Registers a handler for a specific element and key event.\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the key event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns The key event that was registered.\n     */\n    addEventListener(element, eventName, handler, options) {\n        const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n        const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n        return this.manager.getZone().runOutsideAngular(() => {\n            return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler, options);\n        });\n    }\n    /**\n     * Parses the user provided full keyboard event definition and normalizes it for\n     * later internal use. It ensures the string is all lowercase, converts special\n     * characters to a standard spelling, and orders all the values consistently.\n     *\n     * @param eventName The name of the key event to listen for.\n     * @returns an object with the full, normalized string, and the dom event name\n     * or null in the case when the event doesn't match a keyboard event.\n     */\n    static parseEventName(eventName) {\n        const parts = eventName.toLowerCase().split('.');\n        const domEventName = parts.shift();\n        if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n            return null;\n        }\n        const key = KeyEventsPlugin._normalizeKey(parts.pop());\n        let fullKey = '';\n        let codeIX = parts.indexOf('code');\n        if (codeIX > -1) {\n            parts.splice(codeIX, 1);\n            fullKey = 'code.';\n        }\n        MODIFIER_KEYS.forEach((modifierName) => {\n            const index = parts.indexOf(modifierName);\n            if (index > -1) {\n                parts.splice(index, 1);\n                fullKey += modifierName + '.';\n            }\n        });\n        fullKey += key;\n        if (parts.length != 0 || key.length === 0) {\n            // returning null instead of throwing to let another plugin process the event\n            return null;\n        }\n        // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n        //       The code must remain in the `result['domEventName']` form.\n        // return {domEventName, fullKey};\n        const result = {};\n        result['domEventName'] = domEventName;\n        result['fullKey'] = fullKey;\n        return result;\n    }\n    /**\n     * Determines whether the actual keys pressed match the configured key code string.\n     * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n     * event is attached to the DOM during the `addEventListener` call. This is unseen\n     * by the end user and is normalized for internal consistency and parsing.\n     *\n     * @param event The keyboard event.\n     * @param fullKeyCode The normalized user defined expected key event string\n     * @returns boolean.\n     */\n    static matchEventFullKeyCode(event, fullKeyCode) {\n        let keycode = _keyMap[event.key] || event.key;\n        let key = '';\n        if (fullKeyCode.indexOf('code.') > -1) {\n            keycode = event.code;\n            key = 'code.';\n        }\n        // the keycode could be unidentified so we have to check here\n        if (keycode == null || !keycode)\n            return false;\n        keycode = keycode.toLowerCase();\n        if (keycode === ' ') {\n            keycode = 'space'; // for readability\n        }\n        else if (keycode === '.') {\n            keycode = 'dot'; // because '.' is used as a separator in event names\n        }\n        MODIFIER_KEYS.forEach((modifierName) => {\n            if (modifierName !== keycode) {\n                const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n                if (modifierGetter(event)) {\n                    key += modifierName + '.';\n                }\n            }\n        });\n        key += keycode;\n        return key === fullKeyCode;\n    }\n    /**\n     * Configures a handler callback for a key event.\n     * @param fullKey The event name that combines all simultaneous keystrokes.\n     * @param handler The function that responds to the key event.\n     * @param zone The zone in which the event occurred.\n     * @returns A callback function.\n     */\n    static eventCallback(fullKey, handler, zone) {\n        return (event) => {\n            if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n                zone.runGuarded(() => handler(event));\n            }\n        };\n    }\n    /** @internal */\n    static _normalizeKey(keyName) {\n        return keyName === 'esc' ? 'escape' : keyName;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: KeyEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: KeyEventsPlugin });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: KeyEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```angular-ts\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```ts\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n    return ɵinternalCreateApplication({ rootComponent, ...createProvidersConfig(options) });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n    return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n    return {\n        appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n        platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS,\n    };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideProtractorTestingSupport` call results in app\n    // code.\n    return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n    BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n    return new ErrorHandler();\n}\nfunction _document() {\n    // Tell ivy about the global document\n    ɵsetDocument(document);\n    return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [\n    { provide: PLATFORM_ID, useValue: ɵPLATFORM_BROWSER_ID },\n    { provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true },\n    { provide: DOCUMENT, useFactory: _document, deps: [] },\n];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [\n    {\n        provide: ɵTESTABILITY_GETTER,\n        useClass: BrowserGetTestability,\n        deps: [],\n    },\n    {\n        provide: ɵTESTABILITY,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER],\n    },\n    {\n        provide: Testability, // Also provide as `Testability` for backwards-compatibility.\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER],\n    },\n];\nconst BROWSER_MODULE_PROVIDERS = [\n    { provide: ɵINJECTOR_SCOPE, useValue: 'root' },\n    { provide: ErrorHandler, useFactory: errorHandler, deps: [] },\n    {\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: DomEventsPlugin,\n        multi: true,\n        deps: [DOCUMENT],\n    },\n    { provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true, deps: [DOCUMENT] },\n    DomRendererFactory2,\n    SharedStylesHost,\n    EventManager,\n    { provide: RendererFactory2, useExisting: DomRendererFactory2 },\n    { provide: XhrFactory, useClass: BrowserXhr, deps: [] },\n    typeof ngDevMode === 'undefined' || ngDevMode\n        ? { provide: BROWSER_MODULE_PROVIDERS_MARKER, useValue: true }\n        : [],\n];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n    constructor() {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const providersAlreadyPresent = inject(BROWSER_MODULE_PROVIDERS_MARKER, {\n                optional: true,\n                skipSelf: true,\n            });\n            if (providersAlreadyPresent) {\n                throw new ɵRuntimeError(5100 /* RuntimeErrorCode.BROWSER_MODULE_ALREADY_LOADED */, `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` +\n                    `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n            }\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: BrowserModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.1.7\", ngImport: i0, type: BrowserModule, exports: [CommonModule, ApplicationModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: BrowserModule, providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS], imports: [CommonModule, ApplicationModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: BrowserModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n                    exports: [CommonModule, ApplicationModule],\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n    _doc;\n    _dom;\n    constructor(_doc) {\n        this._doc = _doc;\n        this._dom = ɵgetDOM();\n    }\n    /**\n     * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * If an existing element is found, it is returned and is not modified in any way.\n     * @param tag The definition of a `<meta>` element to match or create.\n     * @param forceCreation True to create a new element without checking whether one already exists.\n     * @returns The existing element with the same attributes and values if found,\n     * the new element if no match is found, or `null` if the tag parameter is not defined.\n     */\n    addTag(tag, forceCreation = false) {\n        if (!tag)\n            return null;\n        return this._getOrCreateElement(tag, forceCreation);\n    }\n    /**\n     * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * @param tags An array of tag definitions to match or create.\n     * @param forceCreation True to create new elements without checking whether they already exist.\n     * @returns The matching elements if found, or the new elements.\n     */\n    addTags(tags, forceCreation = false) {\n        if (!tags)\n            return [];\n        return tags.reduce((result, tag) => {\n            if (tag) {\n                result.push(this._getOrCreateElement(tag, forceCreation));\n            }\n            return result;\n        }, []);\n    }\n    /**\n     * Retrieves a `<meta>` tag element in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching element, if any.\n     */\n    getTag(attrSelector) {\n        if (!attrSelector)\n            return null;\n        return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n    }\n    /**\n     * Retrieves a set of `<meta>` tag elements in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching elements, if any.\n     */\n    getTags(attrSelector) {\n        if (!attrSelector)\n            return [];\n        const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n        return list ? [].slice.call(list) : [];\n    }\n    /**\n     * Modifies an existing `<meta>` tag element in the current HTML document.\n     * @param tag The tag description with which to replace the existing tag content.\n     * @param selector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n     * replacement tag.\n     * @return The modified element.\n     */\n    updateTag(tag, selector) {\n        if (!tag)\n            return null;\n        selector = selector || this._parseSelector(tag);\n        const meta = this.getTag(selector);\n        if (meta) {\n            return this._setMetaElementAttributes(tag, meta);\n        }\n        return this._getOrCreateElement(tag, true);\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param attrSelector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     */\n    removeTag(attrSelector) {\n        this.removeTagElement(this.getTag(attrSelector));\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param meta The tag definition to match against to identify an existing tag.\n     */\n    removeTagElement(meta) {\n        if (meta) {\n            this._dom.remove(meta);\n        }\n    }\n    _getOrCreateElement(meta, forceCreation = false) {\n        if (!forceCreation) {\n            const selector = this._parseSelector(meta);\n            // It's allowed to have multiple elements with the same name so it's not enough to\n            // just check that element with the same name already present on the page. We also need to\n            // check if element has tag attributes\n            const elem = this.getTags(selector).filter((elem) => this._containsAttributes(meta, elem))[0];\n            if (elem !== undefined)\n                return elem;\n        }\n        const element = this._dom.createElement('meta');\n        this._setMetaElementAttributes(meta, element);\n        const head = this._doc.getElementsByTagName('head')[0];\n        head.appendChild(element);\n        return element;\n    }\n    _setMetaElementAttributes(tag, el) {\n        Object.keys(tag).forEach((prop) => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n        return el;\n    }\n    _parseSelector(tag) {\n        const attr = tag.name ? 'name' : 'property';\n        return `${attr}=\"${tag[attr]}\"`;\n    }\n    _containsAttributes(tag, elem) {\n        return Object.keys(tag).every((key) => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n    }\n    _getMetaKeyMap(prop) {\n        return META_KEYS_MAP[prop] || prop;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: Meta, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: Meta, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: Meta, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n    httpEquiv: 'http-equiv',\n};\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n    _doc;\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    /**\n     * Get the title of the current HTML document.\n     */\n    getTitle() {\n        return this._doc.title;\n    }\n    /**\n     * Set the title of the current HTML document.\n     * @param newTitle\n     */\n    setTitle(newTitle) {\n        this._doc.title = newTitle || '';\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: Title, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: Title, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: Title, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n    if (typeof COMPILED === 'undefined' || !COMPILED) {\n        // Note: we can't export `ng` when using closure enhanced optimization as:\n        // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n        // - we can't declare a closure extern as the namespace `ng` is already used within Google\n        //   for typings for angularJS (via `goog.provide('ng....')`).\n        const ng = (ɵglobal['ng'] = ɵglobal['ng'] || {});\n        ng[name] = value;\n    }\n}\n\nclass ChangeDetectionPerfRecord {\n    msPerTick;\n    numTicks;\n    constructor(msPerTick, numTicks) {\n        this.msPerTick = msPerTick;\n        this.numTicks = numTicks;\n    }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n    appRef;\n    constructor(ref) {\n        this.appRef = ref.injector.get(ApplicationRef);\n    }\n    // tslint:disable:no-console\n    /**\n     * Exercises change detection in a loop and then prints the average amount of\n     * time in milliseconds how long a single round of change detection takes for\n     * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n     * of 500 milliseconds.\n     *\n     * Optionally, a user may pass a `config` parameter containing a map of\n     * options. Supported options are:\n     *\n     * `record` (boolean) - causes the profiler to record a CPU profile while\n     * it exercises the change detector. Example:\n     *\n     * ```ts\n     * ng.profiler.timeChangeDetection({record: true})\n     * ```\n     */\n    timeChangeDetection(config) {\n        const record = config && config['record'];\n        const profileName = 'Change Detection';\n        // Profiler is not available in Android browsers without dev tools opened\n        if (record && 'profile' in console && typeof console.profile === 'function') {\n            console.profile(profileName);\n        }\n        const start = performance.now();\n        let numTicks = 0;\n        while (numTicks < 5 || performance.now() - start < 500) {\n            this.appRef.tick();\n            numTicks++;\n        }\n        const end = performance.now();\n        if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n            console.profileEnd(profileName);\n        }\n        const msPerTick = (end - start) / numTicks;\n        console.log(`ran ${numTicks} change detection cycles`);\n        console.log(`${msPerTick.toFixed(2)} ms per check`);\n        return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n    }\n}\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n    exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n    return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n    exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n    /**\n     * Match all nodes.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n     */\n    static all() {\n        return () => true;\n    }\n    /**\n     * Match elements by the given CSS selector.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n     */\n    static css(selector) {\n        return (debugElement) => {\n            return debugElement.nativeElement != null\n                ? elementMatches(debugElement.nativeElement, selector)\n                : false;\n        };\n    }\n    /**\n     * Match nodes that have the given directive present.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n     */\n    static directive(type) {\n        return (debugNode) => debugNode.providerTokens.indexOf(type) !== -1;\n    }\n}\nfunction elementMatches(n, selector) {\n    if (ɵgetDOM().isElementNode(n)) {\n        return ((n.matches && n.matches(selector)) ||\n            (n.msMatchesSelector && n.msMatchesSelector(selector)) ||\n            (n.webkitMatchesSelector && n.webkitMatchesSelector(selector)));\n    }\n    return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n    // pan\n    'pan': true,\n    'panstart': true,\n    'panmove': true,\n    'panend': true,\n    'pancancel': true,\n    'panleft': true,\n    'panright': true,\n    'panup': true,\n    'pandown': true,\n    // pinch\n    'pinch': true,\n    'pinchstart': true,\n    'pinchmove': true,\n    'pinchend': true,\n    'pinchcancel': true,\n    'pinchin': true,\n    'pinchout': true,\n    // press\n    'press': true,\n    'pressup': true,\n    // rotate\n    'rotate': true,\n    'rotatestart': true,\n    'rotatemove': true,\n    'rotateend': true,\n    'rotatecancel': true,\n    // swipe\n    'swipe': true,\n    'swipeleft': true,\n    'swiperight': true,\n    'swipeup': true,\n    'swipedown': true,\n    // tap\n    'tap': true,\n    'doubletap': true,\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerGestureConfig' : '');\n/**\n * Injection token used to provide a HammerLoader to Angular.\n *\n * @see {@link HammerLoader}\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'HammerLoader' : '');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n    /**\n     * A set of supported event names for gestures to be used in Angular.\n     * Angular supports all built-in recognizers, as listed in\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    events = [];\n    /**\n     * Maps gesture event names to a set of configuration options\n     * that specify overrides to the default values for specific properties.\n     *\n     * The key is a supported event name to be configured,\n     * and the options object contains a set of properties, with override values\n     * to be applied to the named recognizer event.\n     * For example, to disable recognition of the rotate event, specify\n     *  `{\"rotate\": {\"enable\": false}}`.\n     *\n     * Properties that are not present take the HammerJS default values.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     *\n     */\n    overrides = {};\n    /**\n     * Properties whose default values can be overridden for a given event.\n     * Different sets of properties apply to different events.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    options;\n    /**\n     * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n     * and attaches it to a given HTML element.\n     * @param element The element that will recognize gestures.\n     * @returns A HammerJS event-manager object.\n     */\n    buildHammer(element) {\n        const mc = new Hammer(element, this.options);\n        mc.get('pinch').set({ enable: true });\n        mc.get('rotate').set({ enable: true });\n        for (const eventName in this.overrides) {\n            mc.get(eventName).set(this.overrides[eventName]);\n        }\n        return mc;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: HammerGestureConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: HammerGestureConfig });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: HammerGestureConfig, decorators: [{\n            type: Injectable\n        }] });\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n    _config;\n    _injector;\n    loader;\n    _loaderPromise = null;\n    constructor(doc, _config, _injector, loader) {\n        super(doc);\n        this._config = _config;\n        this._injector = _injector;\n        this.loader = loader;\n    }\n    supports(eventName) {\n        if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n            return false;\n        }\n        if (!window.Hammer && !this.loader) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                // Get a `Console` through an injector to tree-shake the\n                // class when it is unused in production.\n                const _console = this._injector.get(ɵConsole);\n                _console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` +\n                    `loaded and no custom loader has been specified.`);\n            }\n            return false;\n        }\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        const zone = this.manager.getZone();\n        eventName = eventName.toLowerCase();\n        // If Hammer is not present but a loader is specified, we defer adding the event listener\n        // until Hammer is loaded.\n        if (!window.Hammer && this.loader) {\n            this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n            // This `addEventListener` method returns a function to remove the added listener.\n            // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n            // than remove anything.\n            let cancelRegistration = false;\n            let deregister = () => {\n                cancelRegistration = true;\n            };\n            zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n                // If Hammer isn't actually loaded when the custom loader resolves, give up.\n                if (!window.Hammer) {\n                    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                        const _console = this._injector.get(ɵConsole);\n                        _console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n                    }\n                    deregister = () => { };\n                    return;\n                }\n                if (!cancelRegistration) {\n                    // Now that Hammer is loaded and the listener is being loaded for real,\n                    // the deregistration function changes from canceling registration to\n                    // removal.\n                    deregister = this.addEventListener(element, eventName, handler);\n                }\n            }).catch(() => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    const _console = this._injector.get(ɵConsole);\n                    _console.warn(`The \"${eventName}\" event cannot be bound because the custom ` +\n                        `Hammer.JS loader failed.`);\n                }\n                deregister = () => { };\n            }));\n            // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n            // can change the behavior of `deregister` once the listener is added. Using a closure in\n            // this way allows us to avoid any additional data structures to track listener removal.\n            return () => {\n                deregister();\n            };\n        }\n        return zone.runOutsideAngular(() => {\n            // Creating the manager bind events, must be done outside of angular\n            const mc = this._config.buildHammer(element);\n            const callback = function (eventObj) {\n                zone.runGuarded(function () {\n                    handler(eventObj);\n                });\n            };\n            mc.on(eventName, callback);\n            return () => {\n                mc.off(eventName, callback);\n                // destroy mc to prevent memory leak\n                if (typeof mc.destroy === 'function') {\n                    mc.destroy();\n                }\n            };\n        });\n    }\n    isCustomEvent(eventName) {\n        return this._config.events.indexOf(eventName) > -1;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: HammerGesturesPlugin, deps: [{ token: DOCUMENT }, { token: HAMMER_GESTURE_CONFIG }, { token: i0.Injector }, { token: HAMMER_LOADER, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: HammerGesturesPlugin });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: HammerGesturesPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: HammerGestureConfig, decorators: [{\n                    type: Inject,\n                    args: [HAMMER_GESTURE_CONFIG]\n                }] }, { type: i0.Injector }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [HAMMER_LOADER]\n                }] }] });\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n */\nclass HammerModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: HammerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.1.7\", ngImport: i0, type: HammerModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: HammerModule, providers: [\n            {\n                provide: EVENT_MANAGER_PLUGINS,\n                useClass: HammerGesturesPlugin,\n                multi: true,\n                deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]],\n            },\n            { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n        ] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: HammerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        {\n                            provide: EVENT_MANAGER_PLUGINS,\n                            useClass: HammerGesturesPlugin,\n                            multi: true,\n                            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Injector, [new Optional(), HAMMER_LOADER]],\n                        },\n                        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n                    ],\n                }]\n        }] });\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: DomSanitizer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: DomSanitizer, providedIn: 'root', useExisting: i0.forwardRef(() => DomSanitizerImpl) });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: DomSanitizer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useExisting: forwardRef(() => DomSanitizerImpl) }]\n        }] });\nclass DomSanitizerImpl extends DomSanitizer {\n    _doc;\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    sanitize(ctx, value) {\n        if (value == null)\n            return null;\n        switch (ctx) {\n            case SecurityContext.NONE:\n                return value;\n            case SecurityContext.HTML:\n                if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n            case SecurityContext.STYLE:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return value;\n            case SecurityContext.SCRIPT:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new ɵRuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    'unsafe value used in a script context');\n            case SecurityContext.URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeUrl(String(value));\n            case SecurityContext.RESOURCE_URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new ɵRuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `unsafe value used in a resource URL context (see ${ɵXSS_SECURITY_URL})`);\n            default:\n                throw new ɵRuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `Unexpected SecurityContext ${ctx} (see ${ɵXSS_SECURITY_URL})`);\n        }\n    }\n    bypassSecurityTrustHtml(value) {\n        return ɵbypassSanitizationTrustHtml(value);\n    }\n    bypassSecurityTrustStyle(value) {\n        return ɵbypassSanitizationTrustStyle(value);\n    }\n    bypassSecurityTrustScript(value) {\n        return ɵbypassSanitizationTrustScript(value);\n    }\n    bypassSecurityTrustUrl(value) {\n        return ɵbypassSanitizationTrustUrl(value);\n    }\n    bypassSecurityTrustResourceUrl(value) {\n        return ɵbypassSanitizationTrustResourceUrl(value);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: DomSanitizerImpl, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: DomSanitizerImpl, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.1.7\", ngImport: i0, type: DomSanitizerImpl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nvar HydrationFeatureKind;\n(function (HydrationFeatureKind) {\n    HydrationFeatureKind[HydrationFeatureKind[\"NoHttpTransferCache\"] = 0] = \"NoHttpTransferCache\";\n    HydrationFeatureKind[HydrationFeatureKind[\"HttpTransferCacheOptions\"] = 1] = \"HttpTransferCacheOptions\";\n    HydrationFeatureKind[HydrationFeatureKind[\"I18nSupport\"] = 2] = \"I18nSupport\";\n    HydrationFeatureKind[HydrationFeatureKind[\"EventReplay\"] = 3] = \"EventReplay\";\n    HydrationFeatureKind[HydrationFeatureKind[\"IncrementalHydration\"] = 4] = \"IncrementalHydration\";\n})(HydrationFeatureKind || (HydrationFeatureKind = {}));\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(ɵkind, ɵproviders = [], ɵoptions = {}) {\n    return { ɵkind, ɵproviders };\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nfunction withNoHttpTransferCache() {\n    // This feature has no providers and acts as a flag that turns off\n    // HTTP transfer cache (which otherwise is turned on by default).\n    return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n/**\n * The function accepts an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * whether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nfunction withHttpTransferCacheOptions(options) {\n    // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n    return hydrationFeature(HydrationFeatureKind.HttpTransferCacheOptions, ɵwithHttpTransferCache(options));\n}\n/**\n * Enables support for hydrating i18n blocks.\n *\n * @developerPreview\n * @publicApi\n */\nfunction withI18nSupport() {\n    return hydrationFeature(HydrationFeatureKind.I18nSupport, ɵwithI18nSupport());\n}\n/**\n * Enables support for replaying user events (e.g. `click`s) that happened on a page\n * before hydration logic has completed. Once an application is hydrated, all captured\n * events are replayed and relevant event listeners are executed.\n *\n * @usageNotes\n *\n * Basic example of how you can enable event replay in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withEventReplay())]\n * });\n * ```\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withEventReplay() {\n    return hydrationFeature(HydrationFeatureKind.EventReplay, ɵwithEventReplay());\n}\n/**\n * Enables support for incremental hydration using the `hydrate` trigger syntax.\n *\n * @usageNotes\n *\n * Basic example of how you can enable incremental hydration in your application when\n * the `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withIncrementalHydration())]\n * });\n * ```\n * @experimental\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withIncrementalHydration() {\n    return hydrationFeature(HydrationFeatureKind.IncrementalHydration, ɵwithIncrementalHydration());\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n    return [\n        {\n            provide: ENVIRONMENT_INITIALIZER,\n            useValue: () => {\n                const ngZone = inject(NgZone);\n                const isZoneless = inject(ɵZONELESS_ENABLED);\n                // Checking `ngZone instanceof NgZone` would be insufficient here,\n                // because custom implementations might use NgZone as a base class.\n                if (!isZoneless && ngZone.constructor !== NgZone) {\n                    const console = inject(ɵConsole);\n                    const message = ɵformatRuntimeError(-5000 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' +\n                        'that uses a custom or a noop Zone.js implementation. ' +\n                        'This is not yet a fully supported configuration.');\n                    console.warn(message);\n                }\n            },\n            multi: true,\n        },\n    ];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or enable new ones:\n *\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n * * {@link withI18nSupport} to enable hydration support for i18n blocks\n * * {@link withEventReplay} to enable support for replaying user events\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```ts\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n * @see {@link withI18nSupport}\n * @see {@link withEventReplay}\n *\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi\n */\nfunction provideClientHydration(...features) {\n    const providers = [];\n    const featuresKind = new Set();\n    const hasHttpTransferCacheOptions = featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n    for (const { ɵproviders, ɵkind } of features) {\n        featuresKind.add(ɵkind);\n        if (ɵproviders.length) {\n            providers.push(ɵproviders);\n        }\n    }\n    if (typeof ngDevMode !== 'undefined' &&\n        ngDevMode &&\n        featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) &&\n        hasHttpTransferCacheOptions) {\n        // TODO: Make this a runtime error\n        throw new Error('Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n    }\n    return makeEnvironmentProviders([\n        typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [],\n        ɵwithDomHydration(),\n        featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions\n            ? []\n            : ɵwithHttpTransferCache({}),\n        providers,\n    ]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('19.1.7');\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, HydrationFeatureKind, Meta, REMOVE_STYLES_ON_COMPONENT_DESTROY, Title, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, platformBrowser, provideClientHydration, provideProtractorTestingSupport, withEventReplay, withHttpTransferCacheOptions, withI18nSupport, withIncrementalHydration, withNoHttpTransferCache, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, SharedStylesHost as ɵSharedStylesHost, initDomAdapter as ɵinitDomAdapter };\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,WAAW,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,UAAU,EAAEC,YAAY,QAAQ,iBAAiB;AACzK,SAASL,OAAO,QAAQ,iBAAiB;AACzC,OAAO,KAAKM,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,aAAa,EAAEC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,0BAA0B,EAAEC,YAAY,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,YAAY,EAAEC,WAAW,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,gCAAgC,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,4BAA4B,EAAEC,6BAA6B,EAAEC,8BAA8B,EAAEC,2BAA2B,EAAEC,mCAAmC,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,yBAAyB,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,OAAO,QAAQ,eAAe;AACj8B,SAASC,sBAAsB,QAAQ,sBAAsB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,SAAS/D,WAAW,CAAC;EAAAgE,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAC,eAAA,4BAC3B,IAAI;EAAA;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,SAASJ,wBAAwB,CAAC;EACrD,OAAOK,WAAWA,CAAA,EAAG;IACjBnE,kBAAkB,CAAC,IAAIkE,iBAAiB,CAAC,CAAC,CAAC;EAC/C;EACAE,WAAWA,CAACC,EAAE,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IACpCH,EAAE,CAACI,gBAAgB,CAACH,GAAG,EAAEC,QAAQ,EAAEC,OAAO,CAAC;IAC3C,OAAO,MAAM;MACTH,EAAE,CAACK,mBAAmB,CAACJ,GAAG,EAAEC,QAAQ,EAAEC,OAAO,CAAC;IAClD,CAAC;EACL;EACAG,aAAaA,CAACN,EAAE,EAAEC,GAAG,EAAE;IACnBD,EAAE,CAACM,aAAa,CAACL,GAAG,CAAC;EACzB;EACAM,MAAMA,CAACC,IAAI,EAAE;IACTA,IAAI,CAACD,MAAM,CAAC,CAAC;EACjB;EACAE,aAAaA,CAACC,OAAO,EAAEC,GAAG,EAAE;IACxBA,GAAG,GAAGA,GAAG,IAAI,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACtC,OAAOD,GAAG,CAACF,aAAa,CAACC,OAAO,CAAC;EACrC;EACAG,kBAAkBA,CAAA,EAAG;IACjB,OAAOC,QAAQ,CAACC,cAAc,CAACC,kBAAkB,CAAC,WAAW,CAAC;EAClE;EACAJ,kBAAkBA,CAAA,EAAG;IACjB,OAAOE,QAAQ;EACnB;EACAG,aAAaA,CAACT,IAAI,EAAE;IAChB,OAAOA,IAAI,CAACU,QAAQ,KAAKC,IAAI,CAACC,YAAY;EAC9C;EACAC,YAAYA,CAACb,IAAI,EAAE;IACf,OAAOA,IAAI,YAAYc,gBAAgB;EAC3C;EACA;EACAC,oBAAoBA,CAACZ,GAAG,EAAEa,MAAM,EAAE;IAC9B,IAAIA,MAAM,KAAK,QAAQ,EAAE;MACrB,OAAOC,MAAM;IACjB;IACA,IAAID,MAAM,KAAK,UAAU,EAAE;MACvB,OAAOb,GAAG;IACd;IACA,IAAIa,MAAM,KAAK,MAAM,EAAE;MACnB,OAAOb,GAAG,CAACe,IAAI;IACnB;IACA,OAAO,IAAI;EACf;EACAC,WAAWA,CAAChB,GAAG,EAAE;IACb,MAAMiB,IAAI,GAAGC,kBAAkB,CAAC,CAAC;IACjC,OAAOD,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGE,YAAY,CAACF,IAAI,CAAC;EACnD;EACAG,gBAAgBA,CAAA,EAAG;IACfC,WAAW,GAAG,IAAI;EACtB;EACAC,YAAYA,CAAA,EAAG;IACX,OAAOR,MAAM,CAACS,SAAS,CAACC,SAAS;EACrC;EACAC,SAASA,CAACC,IAAI,EAAE;IACZ,OAAOzG,iBAAiB,CAACkF,QAAQ,CAACwB,MAAM,EAAED,IAAI,CAAC;EACnD;AACJ;AACA,IAAIL,WAAW,GAAG,IAAI;AACtB,SAASH,kBAAkBA,CAAA,EAAG;EAC1BG,WAAW,GAAGA,WAAW,IAAIlB,QAAQ,CAACyB,aAAa,CAAC,MAAM,CAAC;EAC3D,OAAOP,WAAW,GAAGA,WAAW,CAACQ,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI;AAChE;AACA,SAASV,YAAYA,CAACW,GAAG,EAAE;EACvB;EACA;EACA,OAAO,IAAIC,GAAG,CAACD,GAAG,EAAE3B,QAAQ,CAAC6B,OAAO,CAAC,CAACC,QAAQ;AAClD;AAEA,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,QAAQ,EAAE;IAClB3G,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAAC4G,IAAI,EAAEC,eAAe,GAAG,IAAI,KAAK;MACjE,MAAMC,WAAW,GAAGH,QAAQ,CAACI,qBAAqB,CAACH,IAAI,EAAEC,eAAe,CAAC;MACzE,IAAIC,WAAW,IAAI,IAAI,EAAE;QACrB,MAAM,IAAI7G,aAAa,CAAC,IAAI,CAAC,8CAA8C,CAAC,OAAO+G,SAAS,KAAK,WAAW,IAAIA,SAAS,KACrH,yCAAyC,CAAC;MAClD;MACA,OAAOF,WAAW;IACtB,CAAC;IACD9G,OAAO,CAAC,4BAA4B,CAAC,GAAG,MAAM2G,QAAQ,CAACM,mBAAmB,CAAC,CAAC;IAC5EjH,OAAO,CAAC,2BAA2B,CAAC,GAAG,MAAM2G,QAAQ,CAACO,kBAAkB,CAAC,CAAC;IAC1E,MAAMC,aAAa,GAAIC,QAAQ,IAAK;MAChC,MAAMC,aAAa,GAAGrH,OAAO,CAAC,4BAA4B,CAAC,CAAC,CAAC;MAC7D,IAAIsH,KAAK,GAAGD,aAAa,CAACE,MAAM;MAChC,MAAMC,SAAS,GAAG,SAAAA,CAAA,EAAY;QAC1BF,KAAK,EAAE;QACP,IAAIA,KAAK,IAAI,CAAC,EAAE;UACZF,QAAQ,CAAC,CAAC;QACd;MACJ,CAAC;MACDC,aAAa,CAACI,OAAO,CAAEX,WAAW,IAAK;QACnCA,WAAW,CAACY,UAAU,CAACF,SAAS,CAAC;MACrC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACxH,OAAO,CAAC,sBAAsB,CAAC,EAAE;MAClCA,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAAE;IACxC;IACAA,OAAO,CAAC,sBAAsB,CAAC,CAAC2H,IAAI,CAACR,aAAa,CAAC;EACvD;EACAJ,qBAAqBA,CAACJ,QAAQ,EAAEC,IAAI,EAAEC,eAAe,EAAE;IACnD,IAAID,IAAI,IAAI,IAAI,EAAE;MACd,OAAO,IAAI;IACf;IACA,MAAMgB,CAAC,GAAGjB,QAAQ,CAACkB,cAAc,CAACjB,IAAI,CAAC;IACvC,IAAIgB,CAAC,IAAI,IAAI,EAAE;MACX,OAAOA,CAAC;IACZ,CAAC,MACI,IAAI,CAACf,eAAe,EAAE;MACvB,OAAO,IAAI;IACf;IACA,IAAIpH,OAAO,CAAC,CAAC,CAACwF,YAAY,CAAC2B,IAAI,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACG,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACkB,IAAI,EAAE,IAAI,CAAC;IAChE;IACA,OAAO,IAAI,CAACf,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACmB,aAAa,EAAE,IAAI,CAAC;EACzE;AACJ;;AAEA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAIC,cAAc,CAAC,CAAC;EAC/B;AAGJ;AAACC,WAAA,GANKH,UAAU;AAAAxE,eAAA,CAAVwE,UAAU,wBAAAI,oBAAAC,iBAAA;EAAA,YAAAA,iBAAA,IAIuFL,WAAU;AAAA;AAAAxE,eAAA,CAJ3GwE,UAAU,+BAOiEjI,EAAE,CAAAuI,kBAAA;EAAAC,KAAA,EAFwBP,WAAU;EAAAQ,OAAA,EAAVR,WAAU,CAAAS;AAAA;AAErH;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KAAiFjH,EAAE,CAAA2I,iBAAA,CAAQV,UAAU,EAAc,CAAC;IACxGW,IAAI,EAAEzI;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAM0I,qBAAqB,GAAG,IAAIzI,cAAc,CAAC6G,SAAS,GAAG,qBAAqB,GAAG,EAAE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6B,YAAY,CAAC;EAIf;AACJ;AACA;EACIvF,WAAWA,CAACwF,OAAO,EAAEC,KAAK,EAAE;IAAAvF,eAAA;IAAAA,eAAA;IAAAA,eAAA,6BAJP,IAAIwF,GAAG,CAAC,CAAC;IAK1B,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClBD,OAAO,CAACrB,OAAO,CAAEwB,MAAM,IAAK;MACxBA,MAAM,CAACC,OAAO,GAAG,IAAI;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,GAAGL,OAAO,CAACM,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrF,gBAAgBA,CAACsF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEzF,OAAO,EAAE;IACnD,MAAMkF,MAAM,GAAG,IAAI,CAACQ,cAAc,CAACF,SAAS,CAAC;IAC7C,OAAON,MAAM,CAACjF,gBAAgB,CAACsF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEzF,OAAO,CAAC;EACxE;EACA;AACJ;AACA;EACI2F,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACX,KAAK;EACrB;EACA;EACAU,cAAcA,CAACF,SAAS,EAAE;IACtB,IAAIN,MAAM,GAAG,IAAI,CAACU,kBAAkB,CAACC,GAAG,CAACL,SAAS,CAAC;IACnD,IAAIN,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;IACA,MAAMH,OAAO,GAAG,IAAI,CAACK,QAAQ;IAC7BF,MAAM,GAAGH,OAAO,CAACe,IAAI,CAAEZ,MAAM,IAAKA,MAAM,CAACa,QAAQ,CAACP,SAAS,CAAC,CAAC;IAC7D,IAAI,CAACN,MAAM,EAAE;MACT,MAAM,IAAIhJ,aAAa,CAAC,IAAI,CAAC,4CAA4C,CAAC,OAAO+G,SAAS,KAAK,WAAW,IAAIA,SAAS,KACnH,2CAA2CuC,SAAS,EAAE,CAAC;IAC/D;IACA,IAAI,CAACI,kBAAkB,CAACI,GAAG,CAACR,SAAS,EAAEN,MAAM,CAAC;IAC9C,OAAOA,MAAM;EACjB;AAGJ;AAACe,aAAA,GAnDKnB,YAAY;AAAArF,eAAA,CAAZqF,YAAY,wBAAAoB,sBAAA5B,iBAAA;EAAA,YAAAA,iBAAA,IAiDqFQ,aAAY,EAjElC9I,EAAE,CAAAmK,QAAA,CAiEkDtB,qBAAqB,GAjEzE7I,EAAE,CAAAmK,QAAA,CAiEoFnK,EAAE,CAACsB,MAAM;AAAA;AAAAmC,eAAA,CAjD1KqF,YAAY,+BAhB+D9I,EAAE,CAAAuI,kBAAA;EAAAC,KAAA,EAkEwBM,aAAY;EAAAL,OAAA,EAAZK,aAAY,CAAAJ;AAAA;AAEvH;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KApEiFjH,EAAE,CAAA2I,iBAAA,CAoEQG,YAAY,EAAc,CAAC;IAC1GF,IAAI,EAAEzI;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEyI,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAACqF,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAED,IAAI,EAAE5I,EAAE,CAACsB;EAAO,CAAC,CAAC;AAAA;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgJ,kBAAkB,CAAC;EAErB;EACA/G,WAAWA,CAACgH,IAAI,EAAE;IAAA9G,eAAA;IAGlB;IAAAA,eAAA;IAFI,IAAI,CAAC8G,IAAI,GAAGA,IAAI;EACpB;AAGJ;;AAEA;AACA,MAAMC,qBAAqB,GAAG,WAAW;AACzC;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,QAAQ,EAAE;EAC9B,KAAK,MAAMnB,OAAO,IAAImB,QAAQ,EAAE;IAC5BnB,OAAO,CAACnF,MAAM,CAAC,CAAC;EACpB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuG,kBAAkBA,CAACC,KAAK,EAAEpG,GAAG,EAAE;EACpC,MAAMqG,YAAY,GAAGrG,GAAG,CAACF,aAAa,CAAC,OAAO,CAAC;EAC/CuG,YAAY,CAACC,WAAW,GAAGF,KAAK;EAChC,OAAOC,YAAY;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,eAAeA,CAACvG,GAAG,EAAEwG,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAAA,IAAAC,SAAA;EACnD,MAAMT,QAAQ,IAAAS,SAAA,GAAG3G,GAAG,CAAC4G,IAAI,cAAAD,SAAA,uBAARA,SAAA,CAAUE,gBAAgB,CAAC,SAASb,qBAAqB,KAAKQ,KAAK,WAAWR,qBAAqB,KAAKQ,KAAK,IAAI,CAAC;EACnI,IAAIN,QAAQ,EAAE;IACV,KAAK,MAAMG,YAAY,IAAIH,QAAQ,EAAE;MACjCG,YAAY,CAACS,eAAe,CAACd,qBAAqB,CAAC;MACnD,IAAIK,YAAY,YAAYU,eAAe,EAAE;QACzC;QACA;QACAL,QAAQ,CAAClB,GAAG,CAACa,YAAY,CAACpF,IAAI,CAAC4D,KAAK,CAACwB,YAAY,CAACpF,IAAI,CAAC+F,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;UAC1EC,KAAK,EAAE,CAAC;UACRf,QAAQ,EAAE,CAACG,YAAY;QAC3B,CAAC,CAAC;MACN,CAAC,MACI,IAAIA,YAAY,CAACC,WAAW,EAAE;QAC/BG,MAAM,CAACjB,GAAG,CAACa,YAAY,CAACC,WAAW,EAAE;UAAEW,KAAK,EAAE,CAAC;UAAEf,QAAQ,EAAE,CAACG,YAAY;QAAE,CAAC,CAAC;MAChF;IACJ;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,iBAAiBA,CAACpF,GAAG,EAAE9B,GAAG,EAAE;EACjC,MAAMmH,WAAW,GAAGnH,GAAG,CAACF,aAAa,CAAC,MAAM,CAAC;EAC7CqH,WAAW,CAACC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC;EAC7CD,WAAW,CAACC,YAAY,CAAC,MAAM,EAAEtF,GAAG,CAAC;EACrC,OAAOqF,WAAW;AACtB;AACA,MAAME,gBAAgB,CAAC;EAsBnBtI,WAAWA,CAACiB,GAAG,EAAEwG,KAAK,EAAEc,KAAK,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;IAAAtI,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAlBhD;AACJ;AACA;AACA;IAHIA,eAAA,iBAIS,IAAIwF,GAAG,CAAC,CAAC;IAClB;AACJ;AACA;AACA;IAHIxF,eAAA,mBAIW,IAAIwF,GAAG,CAAC,CAAC;IACpB;AACJ;AACA;IAFIxF,eAAA,gBAGQ,IAAIuI,GAAG,CAAC,CAAC;IACjB;AACJ;AACA;IAFIvI,eAAA;IAKI,IAAI,CAACe,GAAG,GAAGA,GAAG;IACd,IAAI,CAACwG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACc,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,QAAQ,GAAGtM,gBAAgB,CAACoM,UAAU,CAAC;IAC5ChB,eAAe,CAACvG,GAAG,EAAEwG,KAAK,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAAC;IACvD,IAAI,CAACgB,KAAK,CAACC,GAAG,CAAC3H,GAAG,CAAC4G,IAAI,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACIgB,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAE;IACpB,KAAK,MAAMC,KAAK,IAAIF,MAAM,EAAE;MACxB,IAAI,CAACG,QAAQ,CAACD,KAAK,EAAE,IAAI,CAACtB,MAAM,EAAEN,kBAAkB,CAAC;IACzD;IACA2B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE5E,OAAO,CAAE6E,KAAK,IAAK,IAAI,CAACC,QAAQ,CAACD,KAAK,EAAE,IAAI,CAACrB,QAAQ,EAAEQ,iBAAiB,CAAC,CAAC;EACpF;EACA;AACJ;AACA;AACA;EACIe,YAAYA,CAACJ,MAAM,EAAEC,IAAI,EAAE;IACvB,KAAK,MAAMC,KAAK,IAAIF,MAAM,EAAE;MACxB,IAAI,CAACK,WAAW,CAACH,KAAK,EAAE,IAAI,CAACtB,MAAM,CAAC;IACxC;IACAqB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE5E,OAAO,CAAE6E,KAAK,IAAK,IAAI,CAACG,WAAW,CAACH,KAAK,EAAE,IAAI,CAACrB,QAAQ,CAAC,CAAC;EACpE;EACAsB,QAAQA,CAACD,KAAK,EAAEI,MAAM,EAAEC,OAAO,EAAE;IAC7B;IACA,MAAMC,MAAM,GAAGF,MAAM,CAAC9C,GAAG,CAAC0C,KAAK,CAAC;IAChC;IACA,IAAIM,MAAM,EAAE;MACR,IAAI,CAAC,OAAO5F,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAK4F,MAAM,CAACpB,KAAK,KAAK,CAAC,EAAE;QACvE;QACA;QACAoB,MAAM,CAACnC,QAAQ,CAAChD,OAAO,CAAE6B,OAAO,IAAKA,OAAO,CAACqC,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;MACrF;MACAiB,MAAM,CAACpB,KAAK,EAAE;IAClB,CAAC,MACI;MACD;MACAkB,MAAM,CAAC3C,GAAG,CAACuC,KAAK,EAAE;QACdd,KAAK,EAAE,CAAC;QACRf,QAAQ,EAAE,CAAC,GAAG,IAAI,CAACwB,KAAK,CAAC,CAACY,GAAG,CAAE/E,IAAI,IAAK,IAAI,CAACgF,UAAU,CAAChF,IAAI,EAAE6E,OAAO,CAACL,KAAK,EAAE,IAAI,CAAC/H,GAAG,CAAC,CAAC;MAC3F,CAAC,CAAC;IACN;EACJ;EACAkI,WAAWA,CAACH,KAAK,EAAEI,MAAM,EAAE;IACvB;IACA,MAAME,MAAM,GAAGF,MAAM,CAAC9C,GAAG,CAAC0C,KAAK,CAAC;IAChC;IACA;IACA,IAAIM,MAAM,EAAE;MACRA,MAAM,CAACpB,KAAK,EAAE;MACd,IAAIoB,MAAM,CAACpB,KAAK,IAAI,CAAC,EAAE;QACnBhB,cAAc,CAACoC,MAAM,CAACnC,QAAQ,CAAC;QAC/BiC,MAAM,CAACK,MAAM,CAACT,KAAK,CAAC;MACxB;IACJ;EACJ;EACAU,WAAWA,CAAA,EAAG;IACV,KAAK,MAAM,GAAG;MAAEvC;IAAS,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAACO,MAAM,EAAE,GAAG,IAAI,CAACC,QAAQ,CAAC,EAAE;MAC/DT,cAAc,CAACC,QAAQ,CAAC;IAC5B;IACA,IAAI,CAACwB,KAAK,CAACgB,KAAK,CAAC,CAAC;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAI,CAAClB,KAAK,CAACC,GAAG,CAACiB,QAAQ,CAAC;IACxB;IACA,KAAK,MAAM,CAACxC,KAAK,EAAE;MAAEF;IAAS,CAAC,CAAC,IAAI,IAAI,CAACO,MAAM,EAAE;MAC7CP,QAAQ,CAAC9C,IAAI,CAAC,IAAI,CAACmF,UAAU,CAACK,QAAQ,EAAEzC,kBAAkB,CAACC,KAAK,EAAE,IAAI,CAACpG,GAAG,CAAC,CAAC,CAAC;IACjF;IACA,KAAK,MAAM,CAAC8B,GAAG,EAAE;MAAEoE;IAAS,CAAC,CAAC,IAAI,IAAI,CAACQ,QAAQ,EAAE;MAC7CR,QAAQ,CAAC9C,IAAI,CAAC,IAAI,CAACmF,UAAU,CAACK,QAAQ,EAAE1B,iBAAiB,CAACpF,GAAG,EAAE,IAAI,CAAC9B,GAAG,CAAC,CAAC,CAAC;IAC9E;EACJ;EACA6I,UAAUA,CAACD,QAAQ,EAAE;IACjB,IAAI,CAAClB,KAAK,CAACc,MAAM,CAACI,QAAQ,CAAC;EAC/B;EACAL,UAAUA,CAAChF,IAAI,EAAEwB,OAAO,EAAE;IACtB;IACA,IAAI,IAAI,CAACuC,KAAK,EAAE;MACZvC,OAAO,CAACqC,YAAY,CAAC,OAAO,EAAE,IAAI,CAACE,KAAK,CAAC;IAC7C;IACA;IACA,IAAI,IAAI,CAACG,QAAQ,EAAE;MACf1C,OAAO,CAACqC,YAAY,CAACpB,qBAAqB,EAAE,IAAI,CAACQ,KAAK,CAAC;IAC3D;IACA;IACA,OAAOjD,IAAI,CAACuF,WAAW,CAAC/D,OAAO,CAAC;EACpC;AAGJ;AAACgE,iBAAA,GA1HK1B,gBAAgB;AAAApI,eAAA,CAAhBoI,gBAAgB,wBAAA2B,0BAAAlF,iBAAA;EAAA,YAAAA,iBAAA,IAwHiFuD,iBAAgB,EAjRtC7L,EAAE,CAAAmK,QAAA,CAiRsDvK,QAAQ,GAjRhEI,EAAE,CAAAmK,QAAA,CAiR2E7J,MAAM,GAjRnFN,EAAE,CAAAmK,QAAA,CAiR8F5J,SAAS,MAjRzGP,EAAE,CAAAmK,QAAA,CAiRoI3J,WAAW;AAAA;AAAAiD,eAAA,CAxH5NoI,gBAAgB,+BAzJ2D7L,EAAE,CAAAuI,kBAAA;EAAAC,KAAA,EAkRwBqD,iBAAgB;EAAApD,OAAA,EAAhBoD,iBAAgB,CAAAnD;AAAA;AAE3H;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KApRiFjH,EAAE,CAAA2I,iBAAA,CAoRQkD,gBAAgB,EAAc,CAAC;IAC9GjD,IAAI,EAAEzI;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEyI,IAAI,EAAE6E,QAAQ;IAAEpD,UAAU,EAAE,CAAC;MAC9CzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAC5D,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEgJ,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAClD,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAEsI,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAACjD,SAAS;IACpB,CAAC,EAAE;MACCqI,IAAI,EAAEnI;IACV,CAAC;EAAE,CAAC,EAAE;IAAEmI,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAChD,WAAW;IACtB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMkN,cAAc,GAAG;EACnB,KAAK,EAAE,4BAA4B;EACnC,OAAO,EAAE,8BAA8B;EACvC,OAAO,EAAE,8BAA8B;EACvC,KAAK,EAAE,sCAAsC;EAC7C,OAAO,EAAE,+BAA+B;EACxC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,eAAe,GAAG,SAAS;AACjC,MAAMC,oBAAoB,GAAG,uCAAuC;AACpE,MAAMC,eAAe,GAAG,UAAU;AAClC,MAAMC,kBAAkB,GAAG,QAAQ;AACnC,MAAMC,SAAS,GAAG,WAAWD,kBAAkB,EAAE;AACjD,MAAME,YAAY,GAAG,cAAcF,kBAAkB,EAAE;AACvD;AACA;AACA;AACA,MAAMG,0CAA0C,GAAG,IAAI;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kCAAkC,GAAG,IAAI9N,cAAc,CAAC6G,SAAS,GAAG,2BAA2B,GAAG,EAAE,EAAE;EACxGkH,UAAU,EAAE,MAAM;EAClB1F,OAAO,EAAEA,CAAA,KAAMwF;AACnB,CAAC,CAAC;AACF,SAASG,oBAAoBA,CAACC,gBAAgB,EAAE;EAC5C,OAAOL,YAAY,CAACM,OAAO,CAACX,eAAe,EAAEU,gBAAgB,CAAC;AAClE;AACA,SAASE,iBAAiBA,CAACF,gBAAgB,EAAE;EACzC,OAAON,SAAS,CAACO,OAAO,CAACX,eAAe,EAAEU,gBAAgB,CAAC;AAC/D;AACA,SAASG,iBAAiBA,CAACC,MAAM,EAAEpC,MAAM,EAAE;EACvC,OAAOA,MAAM,CAACS,GAAG,CAAE4B,CAAC,IAAKA,CAAC,CAACJ,OAAO,CAACX,eAAe,EAAEc,MAAM,CAAC,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,yBAAyBA,CAACC,QAAQ,EAAEvC,MAAM,EAAE;EACjD,IAAI,CAACuC,QAAQ,EAAE;IACX,OAAOvC,MAAM;EACjB;EACA,MAAMwC,mBAAmB,GAAG,IAAItI,GAAG,CAACqI,QAAQ,EAAE,kBAAkB,CAAC;EACjE,OAAOvC,MAAM,CAACS,GAAG,CAAEgC,UAAU,IAAK;IAC9B,IAAI,CAACA,UAAU,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MAC3C,OAAOD,UAAU;IACrB;IACA,OAAOA,UAAU,CAACR,OAAO,CAACV,oBAAoB,EAAE,CAACoB,CAAC,EAAEC,YAAY,KAAK;MACjE,IAAIA,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,IACvBA,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC,IAChCrB,eAAe,CAACsB,IAAI,CAACF,YAAY,CAAC,EAAE;QACpC,OAAO,wBAAwBA,YAAY,KAAK;MACpD;MACA,MAAM;QAAExI,QAAQ,EAAE2I;MAAqB,CAAC,GAAG,IAAI7I,GAAG,CAAC0I,YAAY,EAAEJ,mBAAmB,CAAC;MACrF,OAAO,wBAAwBO,oBAAoB,KAAK;IAC5D,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,MAAMC,mBAAmB,CAAC;EAatB9L,WAAWA,CAAC+L,YAAY,EAAEC,gBAAgB,EAAEvE,KAAK,EAAEwE,yBAAyB,EAAEhL,GAAG,EAAEuH,UAAU,EAAE0D,MAAM,EAAE3D,KAAK,GAAG,IAAI,EAAE4D,cAAc,GAAG,IAAI,EAAE;IAAAjM,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,2BAHzH,IAAIwF,GAAG,CAAC,CAAC;IAAAxF,eAAA;IAAAA,eAAA;IAIxB,IAAI,CAAC6L,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACvE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACwE,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAAChL,GAAG,GAAGA,GAAG;IACd,IAAI,CAACuH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC0D,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC3D,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC4D,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,gBAAgB,GAAGhQ,gBAAgB,CAACoM,UAAU,CAAC;IACpD,IAAI,CAAC6D,eAAe,GAAG,IAAIC,mBAAmB,CAACP,YAAY,EAAE9K,GAAG,EAAEiL,MAAM,EAAE,IAAI,CAACE,gBAAgB,EAAE,IAAI,CAACD,cAAc,CAAC;EACzH;EACAI,cAAcA,CAACvG,OAAO,EAAEX,IAAI,EAAE;IAC1B,IAAI,CAACW,OAAO,IAAI,CAACX,IAAI,EAAE;MACnB,OAAO,IAAI,CAACgH,eAAe;IAC/B;IACA,IAAI,IAAI,CAACD,gBAAgB,IAAI/G,IAAI,CAACmH,aAAa,KAAKrP,iBAAiB,CAACsP,SAAS,EAAE;MAC7E;MACApH,IAAI,GAAG;QAAE,GAAGA,IAAI;QAAEmH,aAAa,EAAErP,iBAAiB,CAACuP;MAAS,CAAC;IACjE;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,mBAAmB,CAAC5G,OAAO,EAAEX,IAAI,CAAC;IACxD;IACA;IACA,IAAIsH,QAAQ,YAAYE,iCAAiC,EAAE;MACvDF,QAAQ,CAACG,WAAW,CAAC9G,OAAO,CAAC;IACjC,CAAC,MACI,IAAI2G,QAAQ,YAAYI,4BAA4B,EAAE;MACvDJ,QAAQ,CAACK,WAAW,CAAC,CAAC;IAC1B;IACA,OAAOL,QAAQ;EACnB;EACAC,mBAAmBA,CAAC5G,OAAO,EAAEX,IAAI,EAAE;IAC/B,MAAM4H,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAIN,QAAQ,GAAGM,gBAAgB,CAAC3G,GAAG,CAACjB,IAAI,CAAC6H,EAAE,CAAC;IAC5C,IAAI,CAACP,QAAQ,EAAE;MACX,MAAM1L,GAAG,GAAG,IAAI,CAACA,GAAG;MACpB,MAAMiL,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,MAAMH,YAAY,GAAG,IAAI,CAACA,YAAY;MACtC,MAAMC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,MAAMC,yBAAyB,GAAG,IAAI,CAACA,yBAAyB;MAChE,MAAMG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,MAAMD,cAAc,GAAG,IAAI,CAACA,cAAc;MAC1C,QAAQ9G,IAAI,CAACmH,aAAa;QACtB,KAAKrP,iBAAiB,CAACuP,QAAQ;UAC3BC,QAAQ,GAAG,IAAIE,iCAAiC,CAACd,YAAY,EAAEC,gBAAgB,EAAE3G,IAAI,EAAE,IAAI,CAACoC,KAAK,EAAEwE,yBAAyB,EAAEhL,GAAG,EAAEiL,MAAM,EAAEE,gBAAgB,EAAED,cAAc,CAAC;UAC5K;QACJ,KAAKhP,iBAAiB,CAACsP,SAAS;UAC5B,OAAO,IAAIU,iBAAiB,CAACpB,YAAY,EAAEC,gBAAgB,EAAEhG,OAAO,EAAEX,IAAI,EAAEpE,GAAG,EAAEiL,MAAM,EAAE,IAAI,CAAC3D,KAAK,EAAE6D,gBAAgB,EAAED,cAAc,CAAC;QAC1I;UACIQ,QAAQ,GAAG,IAAII,4BAA4B,CAAChB,YAAY,EAAEC,gBAAgB,EAAE3G,IAAI,EAAE4G,yBAAyB,EAAEhL,GAAG,EAAEiL,MAAM,EAAEE,gBAAgB,EAAED,cAAc,CAAC;UAC3J;MACR;MACAc,gBAAgB,CAACxG,GAAG,CAACpB,IAAI,CAAC6H,EAAE,EAAEP,QAAQ,CAAC;IAC3C;IACA,OAAOA,QAAQ;EACnB;EACAjD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuD,gBAAgB,CAACtD,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;EACIyD,iBAAiBA,CAACC,WAAW,EAAE;IAC3B,IAAI,CAACJ,gBAAgB,CAACxD,MAAM,CAAC4D,WAAW,CAAC;EAC7C;AAGJ;AAACC,mBAAA,GAlFKxB,mBAAmB;AAAA5L,eAAA,CAAnB4L,mBAAmB,wBAAAyB,4BAAAxI,iBAAA;EAAA,YAAAA,iBAAA,IAgF8E+G,mBAAmB,EAlczCrP,EAAE,CAAAmK,QAAA,CAkcyDrB,YAAY,GAlcvE9I,EAAE,CAAAmK,QAAA,CAkckF0B,gBAAgB,GAlcpG7L,EAAE,CAAAmK,QAAA,CAkc+G7J,MAAM,GAlcvHN,EAAE,CAAAmK,QAAA,CAkckI+D,kCAAkC,GAlctKlO,EAAE,CAAAmK,QAAA,CAkciLvK,QAAQ,GAlc3LI,EAAE,CAAAmK,QAAA,CAkcsM3J,WAAW,GAlcnNR,EAAE,CAAAmK,QAAA,CAkc8NnK,EAAE,CAACsB,MAAM,GAlczOtB,EAAE,CAAAmK,QAAA,CAkcoP5J,SAAS,GAlc/PP,EAAE,CAAAmK,QAAA,CAkc0QxJ,eAAe;AAAA;AAAA8C,eAAA,CAhFtW4L,mBAAmB,+BAlXwDrP,EAAE,CAAAuI,kBAAA;EAAAC,KAAA,EAmcwB6G,mBAAmB;EAAA5G,OAAA,EAAnB4G,mBAAmB,CAAA3G;AAAA;AAE9H;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KArciFjH,EAAE,CAAA2I,iBAAA,CAqcQ0G,mBAAmB,EAAc,CAAC;IACjHzG,IAAI,EAAEzI;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEyI,IAAI,EAAEE;EAAa,CAAC,EAAE;IAAEF,IAAI,EAAEiD;EAAiB,CAAC,EAAE;IAAEjD,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MACnGzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAClD,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAEsI,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAC0K,kCAAkC;IAC7C,CAAC;EAAE,CAAC,EAAE;IAAEtF,IAAI,EAAE6E,QAAQ;IAAEpD,UAAU,EAAE,CAAC;MACjCzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAC5D,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEgJ,IAAI,EAAEmI,MAAM;IAAE1G,UAAU,EAAE,CAAC;MAC/BzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAChD,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEoI,IAAI,EAAE5I,EAAE,CAACsB;EAAO,CAAC,EAAE;IAAEsH,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MACvDzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAACjD,SAAS;IACpB,CAAC;EAAE,CAAC,EAAE;IAAEqI,IAAI,EAAE5I,EAAE,CAACW,eAAe;IAAE0J,UAAU,EAAE,CAAC;MAC3CzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAC7C,eAAe;IAC1B,CAAC,EAAE;MACCiI,IAAI,EAAEnI;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB,MAAMoP,mBAAmB,CAAC;EAYtBtM,WAAWA,CAAC+L,YAAY,EAAE9K,GAAG,EAAEiL,MAAM,EAAEE,gBAAgB,EAAED,cAAc,EAAE;IAAAjM,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,eANlEsN,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAC1B;AACJ;AACA;AACA;IAHIvN,eAAA,gCAIwB,IAAI;IAAAA,eAAA,sBASd,IAAI;IAPd,IAAI,CAAC6L,YAAY,GAAGA,YAAY;IAChC,IAAI,CAAC9K,GAAG,GAAGA,GAAG;IACd,IAAI,CAACiL,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACD,cAAc,GAAGA,cAAc;EACxC;EACAuB,OAAOA,CAAA,EAAG,CAAE;EAEZ3M,aAAaA,CAAC4B,IAAI,EAAEgL,SAAS,EAAE;IAC3B,IAAIA,SAAS,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,IAAI,CAAC1M,GAAG,CAAC2M,eAAe,CAACzD,cAAc,CAACwD,SAAS,CAAC,IAAIA,SAAS,EAAEhL,IAAI,CAAC;IACjF;IACA,OAAO,IAAI,CAAC1B,GAAG,CAACF,aAAa,CAAC4B,IAAI,CAAC;EACvC;EACAkL,aAAaA,CAAC7E,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC/H,GAAG,CAAC4M,aAAa,CAAC7E,KAAK,CAAC;EACxC;EACA8E,UAAUA,CAAC9E,KAAK,EAAE;IACd,OAAO,IAAI,CAAC/H,GAAG,CAAC8M,cAAc,CAAC/E,KAAK,CAAC;EACzC;EACAe,WAAWA,CAACiE,MAAM,EAAEC,QAAQ,EAAE;IAC1B,MAAMC,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;IACrEE,YAAY,CAACnE,WAAW,CAACkE,QAAQ,CAAC;EACtC;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,IAAIN,MAAM,EAAE;MACR,MAAME,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;MACrEE,YAAY,CAACG,YAAY,CAACJ,QAAQ,EAAEK,QAAQ,CAAC;IACjD;EACJ;EACAC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC3BA,QAAQ,CAAC5N,MAAM,CAAC,CAAC;EACrB;EACA6N,iBAAiBA,CAACC,cAAc,EAAEC,eAAe,EAAE;IAC/C,IAAItO,EAAE,GAAG,OAAOqO,cAAc,KAAK,QAAQ,GAAG,IAAI,CAAC1N,GAAG,CAAC4B,aAAa,CAAC8L,cAAc,CAAC,GAAGA,cAAc;IACrG,IAAI,CAACrO,EAAE,EAAE;MACL,MAAM,IAAI3D,aAAa,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,OAAO+G,SAAS,KAAK,WAAW,IAAIA,SAAS,KACpH,iBAAiBiL,cAAc,8BAA8B,CAAC;IACtE;IACA,IAAI,CAACC,eAAe,EAAE;MAClBtO,EAAE,CAACiH,WAAW,GAAG,EAAE;IACvB;IACA,OAAOjH,EAAE;EACb;EACAuO,UAAUA,CAAC/N,IAAI,EAAE;IACb,OAAOA,IAAI,CAAC+N,UAAU;EAC1B;EACAC,WAAWA,CAAChO,IAAI,EAAE;IACd,OAAOA,IAAI,CAACgO,WAAW;EAC3B;EACAzG,YAAYA,CAAC/H,EAAE,EAAEqC,IAAI,EAAEqG,KAAK,EAAE2E,SAAS,EAAE;IACrC,IAAIA,SAAS,EAAE;MACXhL,IAAI,GAAGgL,SAAS,GAAG,GAAG,GAAGhL,IAAI;MAC7B,MAAMoM,YAAY,GAAG5E,cAAc,CAACwD,SAAS,CAAC;MAC9C,IAAIoB,YAAY,EAAE;QACdzO,EAAE,CAAC0O,cAAc,CAACD,YAAY,EAAEpM,IAAI,EAAEqG,KAAK,CAAC;MAChD,CAAC,MACI;QACD1I,EAAE,CAAC+H,YAAY,CAAC1F,IAAI,EAAEqG,KAAK,CAAC;MAChC;IACJ,CAAC,MACI;MACD1I,EAAE,CAAC+H,YAAY,CAAC1F,IAAI,EAAEqG,KAAK,CAAC;IAChC;EACJ;EACAjB,eAAeA,CAACzH,EAAE,EAAEqC,IAAI,EAAEgL,SAAS,EAAE;IACjC,IAAIA,SAAS,EAAE;MACX,MAAMoB,YAAY,GAAG5E,cAAc,CAACwD,SAAS,CAAC;MAC9C,IAAIoB,YAAY,EAAE;QACdzO,EAAE,CAAC2O,iBAAiB,CAACF,YAAY,EAAEpM,IAAI,CAAC;MAC5C,CAAC,MACI;QACDrC,EAAE,CAACyH,eAAe,CAAC,GAAG4F,SAAS,IAAIhL,IAAI,EAAE,CAAC;MAC9C;IACJ,CAAC,MACI;MACDrC,EAAE,CAACyH,eAAe,CAACpF,IAAI,CAAC;IAC5B;EACJ;EACAuM,QAAQA,CAAC5O,EAAE,EAAEqC,IAAI,EAAE;IACfrC,EAAE,CAAC6O,SAAS,CAACvG,GAAG,CAACjG,IAAI,CAAC;EAC1B;EACAyM,WAAWA,CAAC9O,EAAE,EAAEqC,IAAI,EAAE;IAClBrC,EAAE,CAAC6O,SAAS,CAACtO,MAAM,CAAC8B,IAAI,CAAC;EAC7B;EACA0M,QAAQA,CAAC/O,EAAE,EAAE+G,KAAK,EAAE2B,KAAK,EAAEsG,KAAK,EAAE;IAC9B,IAAIA,KAAK,IAAIjS,mBAAmB,CAACkS,QAAQ,GAAGlS,mBAAmB,CAACmS,SAAS,CAAC,EAAE;MACxElP,EAAE,CAAC+G,KAAK,CAACoI,WAAW,CAACpI,KAAK,EAAE2B,KAAK,EAAEsG,KAAK,GAAGjS,mBAAmB,CAACmS,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;IAChG,CAAC,MACI;MACDlP,EAAE,CAAC+G,KAAK,CAACA,KAAK,CAAC,GAAG2B,KAAK;IAC3B;EACJ;EACA0G,WAAWA,CAACpP,EAAE,EAAE+G,KAAK,EAAEiI,KAAK,EAAE;IAC1B,IAAIA,KAAK,GAAGjS,mBAAmB,CAACkS,QAAQ,EAAE;MACtC;MACAjP,EAAE,CAAC+G,KAAK,CAACsI,cAAc,CAACtI,KAAK,CAAC;IAClC,CAAC,MACI;MACD/G,EAAE,CAAC+G,KAAK,CAACA,KAAK,CAAC,GAAG,EAAE;IACxB;EACJ;EACAoI,WAAWA,CAACnP,EAAE,EAAEqC,IAAI,EAAEqG,KAAK,EAAE;IACzB,IAAI1I,EAAE,IAAI,IAAI,EAAE;MACZ;IACJ;IACA,CAAC,OAAOoD,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1C,IAAI,CAACkM,qBAAqB,IAC1BC,oBAAoB,CAAClN,IAAI,EAAE,UAAU,CAAC;IAC1CrC,EAAE,CAACqC,IAAI,CAAC,GAAGqG,KAAK;EACpB;EACA8G,QAAQA,CAAChP,IAAI,EAAEkI,KAAK,EAAE;IAClBlI,IAAI,CAACiP,SAAS,GAAG/G,KAAK;EAC1B;EACAgH,MAAMA,CAAClO,MAAM,EAAEmO,KAAK,EAAEnM,QAAQ,EAAErD,OAAO,EAAE;IACrC,CAAC,OAAOiD,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1C,IAAI,CAACkM,qBAAqB,IAC1BC,oBAAoB,CAACI,KAAK,EAAE,UAAU,CAAC;IAC3C,IAAI,OAAOnO,MAAM,KAAK,QAAQ,EAAE;MAC5BA,MAAM,GAAG3F,OAAO,CAAC,CAAC,CAAC0F,oBAAoB,CAAC,IAAI,CAACZ,GAAG,EAAEa,MAAM,CAAC;MACzD,IAAI,CAACA,MAAM,EAAE;QACT,MAAM,IAAIoO,KAAK,CAAC,4BAA4BpO,MAAM,cAAcmO,KAAK,EAAE,CAAC;MAC5E;IACJ;IACA,IAAIE,eAAe,GAAG,IAAI,CAACC,sBAAsB,CAACtM,QAAQ,CAAC;IAC3D,IAAI,IAAI,CAACqI,cAAc,KAAK,IAAI,IAAI,IAAI,CAACA,cAAc,CAACkE,iBAAiB,EAAE;MACvEF,eAAe,GAAG,IAAI,CAAChE,cAAc,CAACkE,iBAAiB,CAACvO,MAAM,EAAEmO,KAAK,EAAEE,eAAe,CAAC;IAC3F;IACA,OAAO,IAAI,CAACpE,YAAY,CAACrL,gBAAgB,CAACoB,MAAM,EAAEmO,KAAK,EAAEE,eAAe,EAAE1P,OAAO,CAAC;EACtF;EACA2P,sBAAsBA,CAACE,YAAY,EAAE;IACjC;IACA;IACA;IACA;IACA,OAAQL,KAAK,IAAK;MACd;MACA;MACA;MACA;MACA;MACA,IAAIA,KAAK,KAAK,cAAc,EAAE;QAC1B,OAAOK,YAAY;MACvB;MACA;MACA;MACA,MAAMC,oBAAoB,GAAG,IAAI,CAACnE,gBAAgB,GAC5C,IAAI,CAACF,MAAM,CAACsE,UAAU,CAAC,MAAMF,YAAY,CAACL,KAAK,CAAC,CAAC,GACjDK,YAAY,CAACL,KAAK,CAAC;MACzB,IAAIM,oBAAoB,KAAK,KAAK,EAAE;QAChCN,KAAK,CAACQ,cAAc,CAAC,CAAC;MAC1B;MACA,OAAO5J,SAAS;IACpB,CAAC;EACL;AACJ;AACA,MAAM6J,WAAW,GAAG,CAAC,MAAM,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;AAC/C,SAASd,oBAAoBA,CAAClN,IAAI,EAAEiO,QAAQ,EAAE;EAC1C,IAAIjO,IAAI,CAACgO,UAAU,CAAC,CAAC,CAAC,KAAKD,WAAW,EAAE;IACpC,MAAM,IAAI/T,aAAa,CAAC,IAAI,CAAC,sDAAsD,wBAAwBiU,QAAQ,IAAIjO,IAAI;AACnI;AACA,+DAA+DA,IAAI,iIAAiI,CAAC;EACjM;AACJ;AACA,SAASwL,cAAcA,CAACrN,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACE,OAAO,KAAK,UAAU,IAAIF,IAAI,CAACsN,OAAO,KAAKvH,SAAS;AACpE;AACA,MAAMsG,iBAAiB,SAASb,mBAAmB,CAAC;EAIhDtM,WAAWA,CAAC+L,YAAY,EAAEC,gBAAgB,EAAE6E,MAAM,EAAEC,SAAS,EAAE7P,GAAG,EAAEiL,MAAM,EAAE3D,KAAK,EAAE6D,gBAAgB,EAAED,cAAc,EAAE;IAAA,IAAA4E,qBAAA;IACjH,KAAK,CAAChF,YAAY,EAAE9K,GAAG,EAAEiL,MAAM,EAAEE,gBAAgB,EAAED,cAAc,CAAC;IAACjM,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACnE,IAAI,CAAC8L,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC6E,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,UAAU,GAAGH,MAAM,CAACI,YAAY,CAAC;MAAEC,IAAI,EAAE;IAAO,CAAC,CAAC;IACvD,IAAI,CAAClF,gBAAgB,CAACpC,OAAO,CAAC,IAAI,CAACoH,UAAU,CAAC;IAC9C,IAAIlI,MAAM,GAAGgI,SAAS,CAAChI,MAAM;IAC7B,IAAIpF,SAAS,EAAE;MAAA,IAAAyN,oBAAA;MACX;MACA,MAAM9F,QAAQ,IAAA8F,oBAAA,GAAGhV,OAAO,CAAC,CAAC,CAAC8F,WAAW,CAAChB,GAAG,CAAC,cAAAkQ,oBAAA,cAAAA,oBAAA,GAAI,EAAE;MACjDrI,MAAM,GAAGsC,yBAAyB,CAACC,QAAQ,EAAEvC,MAAM,CAAC;IACxD;IACAA,MAAM,GAAGmC,iBAAiB,CAAC6F,SAAS,CAAC5D,EAAE,EAAEpE,MAAM,CAAC;IAChD,KAAK,MAAMzB,KAAK,IAAIyB,MAAM,EAAE;MACxB,MAAMsI,OAAO,GAAGhQ,QAAQ,CAACL,aAAa,CAAC,OAAO,CAAC;MAC/C,IAAIwH,KAAK,EAAE;QACP6I,OAAO,CAAC/I,YAAY,CAAC,OAAO,EAAEE,KAAK,CAAC;MACxC;MACA6I,OAAO,CAAC7J,WAAW,GAAGF,KAAK;MAC3B,IAAI,CAAC2J,UAAU,CAACjH,WAAW,CAACqH,OAAO,CAAC;IACxC;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,SAAS,IAAAN,qBAAA,GAAGD,SAAS,CAACQ,iBAAiB,cAAAP,qBAAA,uBAA3BA,qBAAA,CAAAQ,IAAA,CAAAT,SAA8B,CAAC;IACjD,IAAIO,SAAS,EAAE;MACX,KAAK,MAAMG,QAAQ,IAAIH,SAAS,EAAE;QAC9B,MAAMI,MAAM,GAAGtJ,iBAAiB,CAACqJ,QAAQ,EAAEvQ,GAAG,CAAC;QAC/C,IAAIsH,KAAK,EAAE;UACPkJ,MAAM,CAACpJ,YAAY,CAAC,OAAO,EAAEE,KAAK,CAAC;QACvC;QACA,IAAI,CAACyI,UAAU,CAACjH,WAAW,CAAC0H,MAAM,CAAC;MACvC;IACJ;EACJ;EACAC,gBAAgBA,CAAC5Q,IAAI,EAAE;IACnB,OAAOA,IAAI,KAAK,IAAI,CAAC+P,MAAM,GAAG,IAAI,CAACG,UAAU,GAAGlQ,IAAI;EACxD;EACAiJ,WAAWA,CAACiE,MAAM,EAAEC,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAAClE,WAAW,CAAC,IAAI,CAAC2H,gBAAgB,CAAC1D,MAAM,CAAC,EAAEC,QAAQ,CAAC;EACrE;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,OAAO,KAAK,CAACD,YAAY,CAAC,IAAI,CAACqD,gBAAgB,CAAC1D,MAAM,CAAC,EAAEC,QAAQ,EAAEK,QAAQ,CAAC;EAChF;EACAC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC3B,OAAO,KAAK,CAACF,WAAW,CAAC,IAAI,EAAEE,QAAQ,CAAC;EAC5C;EACAI,UAAUA,CAAC/N,IAAI,EAAE;IACb,OAAO,IAAI,CAAC4Q,gBAAgB,CAAC,KAAK,CAAC7C,UAAU,CAAC,IAAI,CAAC6C,gBAAgB,CAAC5Q,IAAI,CAAC,CAAC,CAAC;EAC/E;EACA4M,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC1B,gBAAgB,CAAClC,UAAU,CAAC,IAAI,CAACkH,UAAU,CAAC;EACrD;AACJ;AACA,MAAMjE,4BAA4B,SAAST,mBAAmB,CAAC;EAK3DtM,WAAWA,CAAC+L,YAAY,EAAEC,gBAAgB,EAAE8E,SAAS,EAAE7E,yBAAyB,EAAEhL,GAAG,EAAEiL,MAAM,EAAEE,gBAAgB,EAAED,cAAc,EAAEjB,MAAM,EAAE;IAAA,IAAAyG,sBAAA;IACrI,KAAK,CAAC5F,YAAY,EAAE9K,GAAG,EAAEiL,MAAM,EAAEE,gBAAgB,EAAED,cAAc,CAAC;IAACjM,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IACnE,IAAI,CAAC8L,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAInD,MAAM,GAAGgI,SAAS,CAAChI,MAAM;IAC7B,IAAIpF,SAAS,EAAE;MAAA,IAAAkO,qBAAA;MACX;MACA,MAAMvG,QAAQ,IAAAuG,qBAAA,GAAGzV,OAAO,CAAC,CAAC,CAAC8F,WAAW,CAAChB,GAAG,CAAC,cAAA2Q,qBAAA,cAAAA,qBAAA,GAAI,EAAE;MACjD9I,MAAM,GAAGsC,yBAAyB,CAACC,QAAQ,EAAEvC,MAAM,CAAC;IACxD;IACA,IAAI,CAACA,MAAM,GAAGoC,MAAM,GAAGD,iBAAiB,CAACC,MAAM,EAAEpC,MAAM,CAAC,GAAGA,MAAM;IACjE,IAAI,CAACuI,SAAS,IAAAM,sBAAA,GAAGb,SAAS,CAACQ,iBAAiB,cAAAK,sBAAA,uBAA3BA,sBAAA,CAAAJ,IAAA,CAAAT,SAAS,EAAqB5F,MAAM,CAAC;EAC1D;EACA8B,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChB,gBAAgB,CAACnD,SAAS,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACuI,SAAS,CAAC;EAChE;EACA3D,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACzB,yBAAyB,EAAE;MACjC;IACJ;IACA,IAAI,CAACD,gBAAgB,CAAC9C,YAAY,CAAC,IAAI,CAACJ,MAAM,EAAE,IAAI,CAACuI,SAAS,CAAC;EACnE;AACJ;AACA,MAAMxE,iCAAiC,SAASE,4BAA4B,CAAC;EAGzE/M,WAAWA,CAAC+L,YAAY,EAAEC,gBAAgB,EAAE8E,SAAS,EAAErJ,KAAK,EAAEwE,yBAAyB,EAAEhL,GAAG,EAAEiL,MAAM,EAAEE,gBAAgB,EAAED,cAAc,EAAE;IACpI,MAAMjB,MAAM,GAAGzD,KAAK,GAAG,GAAG,GAAGqJ,SAAS,CAAC5D,EAAE;IACzC,KAAK,CAACnB,YAAY,EAAEC,gBAAgB,EAAE8E,SAAS,EAAE7E,yBAAyB,EAAEhL,GAAG,EAAEiL,MAAM,EAAEE,gBAAgB,EAAED,cAAc,EAAEjB,MAAM,CAAC;IAAChL,eAAA;IAAAA,eAAA;IACnI,IAAI,CAAC2R,WAAW,GAAGhH,oBAAoB,CAACK,MAAM,CAAC;IAC/C,IAAI,CAAC4G,QAAQ,GAAG9G,iBAAiB,CAACE,MAAM,CAAC;EAC7C;EACA4B,WAAWA,CAAC9G,OAAO,EAAE;IACjB,IAAI,CAACgH,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC3E,YAAY,CAACrC,OAAO,EAAE,IAAI,CAAC8L,QAAQ,EAAE,EAAE,CAAC;EACjD;EACA/Q,aAAaA,CAACiN,MAAM,EAAErL,IAAI,EAAE;IACxB,MAAMrC,EAAE,GAAG,KAAK,CAACS,aAAa,CAACiN,MAAM,EAAErL,IAAI,CAAC;IAC5C,KAAK,CAAC0F,YAAY,CAAC/H,EAAE,EAAE,IAAI,CAACuR,WAAW,EAAE,EAAE,CAAC;IAC5C,OAAOvR,EAAE;EACb;AACJ;AAEA,MAAMyR,eAAe,SAAShL,kBAAkB,CAAC;EAC7C/G,WAAWA,CAACiB,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;EACA;EACAuF,QAAQA,CAACP,SAAS,EAAE;IAChB,OAAO,IAAI;EACf;EACAvF,gBAAgBA,CAACsF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEzF,OAAO,EAAE;IACnDuF,OAAO,CAACtF,gBAAgB,CAACuF,SAAS,EAAEC,OAAO,EAAEzF,OAAO,CAAC;IACrD,OAAO,MAAM,IAAI,CAACE,mBAAmB,CAACqF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEzF,OAAO,CAAC;EAC/E;EACAE,mBAAmBA,CAACmB,MAAM,EAAEmE,SAAS,EAAEnC,QAAQ,EAAErD,OAAO,EAAE;IACtD,OAAOqB,MAAM,CAACnB,mBAAmB,CAACsF,SAAS,EAAEnC,QAAQ,EAAErD,OAAO,CAAC;EACnE;AAGJ;AAACuR,gBAAA,GAlBKD,eAAe;AAAA7R,eAAA,CAAf6R,eAAe,wBAAAE,yBAAAlN,iBAAA;EAAA,YAAAA,iBAAA,IAgBkFgN,gBAAe,EAtxBrCtV,EAAE,CAAAmK,QAAA,CAsxBqDvK,QAAQ;AAAA;AAAA6D,eAAA,CAhB1I6R,eAAe,+BAtwB4DtV,EAAE,CAAAuI,kBAAA;EAAAC,KAAA,EAuxBwB8M,gBAAe;EAAA7M,OAAA,EAAf6M,gBAAe,CAAA5M;AAAA;AAE1H;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KAzxBiFjH,EAAE,CAAA2I,iBAAA,CAyxBQ2M,eAAe,EAAc,CAAC;IAC7G1M,IAAI,EAAEzI;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEyI,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAC5D,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA,MAAM6V,aAAa,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AACzD;AACA;AACA,MAAMC,OAAO,GAAG;EACZ,IAAI,EAAE,WAAW;EACjB,IAAI,EAAE,KAAK;EACX,MAAM,EAAE,QAAQ;EAChB,MAAM,EAAE,QAAQ;EAChB,KAAK,EAAE,QAAQ;EACf,KAAK,EAAE,QAAQ;EACf,MAAM,EAAE,WAAW;EACnB,OAAO,EAAE,YAAY;EACrB,IAAI,EAAE,SAAS;EACf,MAAM,EAAE,WAAW;EACnB,MAAM,EAAE,aAAa;EACrB,QAAQ,EAAE,YAAY;EACtB,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG;EACzB,KAAK,EAAGnC,KAAK,IAAKA,KAAK,CAACoC,MAAM;EAC9B,SAAS,EAAGpC,KAAK,IAAKA,KAAK,CAACqC,OAAO;EACnC,MAAM,EAAGrC,KAAK,IAAKA,KAAK,CAACsC,OAAO;EAChC,OAAO,EAAGtC,KAAK,IAAKA,KAAK,CAACuC;AAC9B,CAAC;AACD;AACA;AACA;AACA,MAAMC,eAAe,SAAS1L,kBAAkB,CAAC;EAC7C;AACJ;AACA;AACA;EACI/G,WAAWA,CAACiB,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;AACJ;AACA;AACA;AACA;EACIuF,QAAQA,CAACP,SAAS,EAAE;IAChB,OAAOwM,eAAe,CAACC,cAAc,CAACzM,SAAS,CAAC,IAAI,IAAI;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIvF,gBAAgBA,CAACsF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEzF,OAAO,EAAE;IACnD,MAAMkS,WAAW,GAAGF,eAAe,CAACC,cAAc,CAACzM,SAAS,CAAC;IAC7D,MAAM2M,cAAc,GAAGH,eAAe,CAACI,aAAa,CAACF,WAAW,CAAC,SAAS,CAAC,EAAEzM,OAAO,EAAE,IAAI,CAACN,OAAO,CAACQ,OAAO,CAAC,CAAC,CAAC;IAC7G,OAAO,IAAI,CAACR,OAAO,CAACQ,OAAO,CAAC,CAAC,CAAC0M,iBAAiB,CAAC,MAAM;MAClD,OAAO3W,OAAO,CAAC,CAAC,CAACkE,WAAW,CAAC2F,OAAO,EAAE2M,WAAW,CAAC,cAAc,CAAC,EAAEC,cAAc,EAAEnS,OAAO,CAAC;IAC/F,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOiS,cAAcA,CAACzM,SAAS,EAAE;IAC7B,MAAM8M,KAAK,GAAG9M,SAAS,CAAC+M,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IAChD,MAAMC,YAAY,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAC;IAClC,IAAIJ,KAAK,CAAC9O,MAAM,KAAK,CAAC,IAAI,EAAEiP,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,OAAO,CAAC,EAAE;MACjF,OAAO,IAAI;IACf;IACA,MAAME,GAAG,GAAGX,eAAe,CAACY,aAAa,CAACN,KAAK,CAACO,GAAG,CAAC,CAAC,CAAC;IACtD,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAGT,KAAK,CAACU,OAAO,CAAC,MAAM,CAAC;IAClC,IAAID,MAAM,GAAG,CAAC,CAAC,EAAE;MACbT,KAAK,CAACW,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;MACvBD,OAAO,GAAG,OAAO;IACrB;IACArB,aAAa,CAAC/N,OAAO,CAAEwP,YAAY,IAAK;MACpC,MAAMC,KAAK,GAAGb,KAAK,CAACU,OAAO,CAACE,YAAY,CAAC;MACzC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QACZb,KAAK,CAACW,MAAM,CAACE,KAAK,EAAE,CAAC,CAAC;QACtBL,OAAO,IAAII,YAAY,GAAG,GAAG;MACjC;IACJ,CAAC,CAAC;IACFJ,OAAO,IAAIH,GAAG;IACd,IAAIL,KAAK,CAAC9O,MAAM,IAAI,CAAC,IAAImP,GAAG,CAACnP,MAAM,KAAK,CAAC,EAAE;MACvC;MACA,OAAO,IAAI;IACf;IACA;IACA;IACA;IACA,MAAM4P,MAAM,GAAG,CAAC,CAAC;IACjBA,MAAM,CAAC,cAAc,CAAC,GAAGX,YAAY;IACrCW,MAAM,CAAC,SAAS,CAAC,GAAGN,OAAO;IAC3B,OAAOM,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,qBAAqBA,CAAC7D,KAAK,EAAE8D,WAAW,EAAE;IAC7C,IAAIC,OAAO,GAAG7B,OAAO,CAAClC,KAAK,CAACmD,GAAG,CAAC,IAAInD,KAAK,CAACmD,GAAG;IAC7C,IAAIA,GAAG,GAAG,EAAE;IACZ,IAAIW,WAAW,CAACN,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MACnCO,OAAO,GAAG/D,KAAK,CAACgE,IAAI;MACpBb,GAAG,GAAG,OAAO;IACjB;IACA;IACA,IAAIY,OAAO,IAAI,IAAI,IAAI,CAACA,OAAO,EAC3B,OAAO,KAAK;IAChBA,OAAO,GAAGA,OAAO,CAAChB,WAAW,CAAC,CAAC;IAC/B,IAAIgB,OAAO,KAAK,GAAG,EAAE;MACjBA,OAAO,GAAG,OAAO,CAAC,CAAC;IACvB,CAAC,MACI,IAAIA,OAAO,KAAK,GAAG,EAAE;MACtBA,OAAO,GAAG,KAAK,CAAC,CAAC;IACrB;IACA9B,aAAa,CAAC/N,OAAO,CAAEwP,YAAY,IAAK;MACpC,IAAIA,YAAY,KAAKK,OAAO,EAAE;QAC1B,MAAME,cAAc,GAAG9B,oBAAoB,CAACuB,YAAY,CAAC;QACzD,IAAIO,cAAc,CAACjE,KAAK,CAAC,EAAE;UACvBmD,GAAG,IAAIO,YAAY,GAAG,GAAG;QAC7B;MACJ;IACJ,CAAC,CAAC;IACFP,GAAG,IAAIY,OAAO;IACd,OAAOZ,GAAG,KAAKW,WAAW;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOlB,aAAaA,CAACU,OAAO,EAAErN,OAAO,EAAEiO,IAAI,EAAE;IACzC,OAAQlE,KAAK,IAAK;MACd,IAAIwC,eAAe,CAACqB,qBAAqB,CAAC7D,KAAK,EAAEsD,OAAO,CAAC,EAAE;QACvDY,IAAI,CAAC3D,UAAU,CAAC,MAAMtK,OAAO,CAAC+J,KAAK,CAAC,CAAC;MACzC;IACJ,CAAC;EACL;EACA;EACA,OAAOoD,aAAaA,CAACe,OAAO,EAAE;IAC1B,OAAOA,OAAO,KAAK,KAAK,GAAG,QAAQ,GAAGA,OAAO;EACjD;AAGJ;AAACC,gBAAA,GAnIK5B,eAAe;AAAAvS,eAAA,CAAfuS,eAAe,wBAAA6B,yBAAAvP,iBAAA;EAAA,YAAAA,iBAAA,IAiIkF0N,gBAAe,EAl8BrChW,EAAE,CAAAmK,QAAA,CAk8BqDvK,QAAQ;AAAA;AAAA6D,eAAA,CAjI1IuS,eAAe,+BAj0B4DhW,EAAE,CAAAuI,kBAAA;EAAAC,KAAA,EAm8BwBwN,gBAAe;EAAAvN,OAAA,EAAfuN,gBAAe,CAAAtN;AAAA;AAE1H;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KAr8BiFjH,EAAE,CAAA2I,iBAAA,CAq8BQqN,eAAe,EAAc,CAAC;IAC7GpN,IAAI,EAAEzI;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEyI,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAC5D,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkY,oBAAoBA,CAACC,aAAa,EAAE/T,OAAO,EAAE;EAClD,OAAOnD,0BAA0B,CAAC;IAAEkX,aAAa;IAAE,GAAGC,qBAAqB,CAAChU,OAAO;EAAE,CAAC,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiU,iBAAiBA,CAACjU,OAAO,EAAE;EAChC,OAAOnD,0BAA0B,CAACmX,qBAAqB,CAAChU,OAAO,CAAC,CAAC;AACrE;AACA,SAASgU,qBAAqBA,CAAChU,OAAO,EAAE;EAAA,IAAAkU,kBAAA;EACpC,OAAO;IACHC,YAAY,EAAE,CAAC,GAAGC,wBAAwB,EAAE,KAAAF,kBAAA,GAAIlU,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqU,SAAS,cAAAH,kBAAA,cAAAA,kBAAA,GAAI,EAAE,CAAC,CAAC;IAC1EI,iBAAiB,EAAEC;EACvB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,+BAA+BA,CAAA,EAAG;EACvC;EACA;EACA;EACA,OAAO,CAAC,GAAGC,qBAAqB,CAAC;AACrC;AACA,SAASC,cAAcA,CAAA,EAAG;EACtBhV,iBAAiB,CAACC,WAAW,CAAC,CAAC;AACnC;AACA,SAASgV,YAAYA,CAAA,EAAG;EACpB,OAAO,IAAI7X,YAAY,CAAC,CAAC;AAC7B;AACA,SAAS8X,SAASA,CAAA,EAAG;EACjB;EACA7X,YAAY,CAAC4D,QAAQ,CAAC;EACtB,OAAOA,QAAQ;AACnB;AACA,MAAM4T,mCAAmC,GAAG,CACxC;EAAEM,OAAO,EAAErY,WAAW;EAAEsY,QAAQ,EAAEjZ;AAAqB,CAAC,EACxD;EAAEgZ,OAAO,EAAE7X,oBAAoB;EAAE8X,QAAQ,EAAEJ,cAAc;EAAEK,KAAK,EAAE;AAAK,CAAC,EACxE;EAAEF,OAAO,EAAEjZ,QAAQ;EAAEoZ,UAAU,EAAEJ,SAAS;EAAEK,IAAI,EAAE;AAAG,CAAC,CACzD;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGjY,qBAAqB,CAACC,YAAY,EAAE,SAAS,EAAEqX,mCAAmC,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,+BAA+B,GAAG,IAAI/Y,cAAc,CAAC,OAAO6G,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,gCAAgC,GAAG,EAAE,CAAC;AACjJ,MAAMwR,qBAAqB,GAAG,CAC1B;EACII,OAAO,EAAE1X,mBAAmB;EAC5BiY,QAAQ,EAAE1S,qBAAqB;EAC/BuS,IAAI,EAAE;AACV,CAAC,EACD;EACIJ,OAAO,EAAEzX,YAAY;EACrBgY,QAAQ,EAAE/X,WAAW;EACrB4X,IAAI,EAAE,CAAC3X,MAAM,EAAEC,mBAAmB,EAAEJ,mBAAmB;AAC3D,CAAC,EACD;EACI0X,OAAO,EAAExX,WAAW;EAAE;EACtB+X,QAAQ,EAAE/X,WAAW;EACrB4X,IAAI,EAAE,CAAC3X,MAAM,EAAEC,mBAAmB,EAAEJ,mBAAmB;AAC3D,CAAC,CACJ;AACD,MAAMiX,wBAAwB,GAAG,CAC7B;EAAES,OAAO,EAAErX,eAAe;EAAEsX,QAAQ,EAAE;AAAO,CAAC,EAC9C;EAAED,OAAO,EAAE/X,YAAY;EAAEkY,UAAU,EAAEL,YAAY;EAAEM,IAAI,EAAE;AAAG,CAAC,EAC7D;EACIJ,OAAO,EAAEhQ,qBAAqB;EAC9BuQ,QAAQ,EAAE9D,eAAe;EACzByD,KAAK,EAAE,IAAI;EACXE,IAAI,EAAE,CAACrZ,QAAQ;AACnB,CAAC,EACD;EAAEiZ,OAAO,EAAEhQ,qBAAqB;EAAEuQ,QAAQ,EAAEpD,eAAe;EAAE+C,KAAK,EAAE,IAAI;EAAEE,IAAI,EAAE,CAACrZ,QAAQ;AAAE,CAAC,EAC5FyP,mBAAmB,EACnBxD,gBAAgB,EAChB/C,YAAY,EACZ;EAAE+P,OAAO,EAAEpX,gBAAgB;EAAE4X,WAAW,EAAEhK;AAAoB,CAAC,EAC/D;EAAEwJ,OAAO,EAAE/Y,UAAU;EAAEsZ,QAAQ,EAAEnR,UAAU;EAAEgR,IAAI,EAAE;AAAG,CAAC,EACvD,OAAOhS,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvC;EAAE4R,OAAO,EAAEM,+BAA+B;EAAEL,QAAQ,EAAE;AAAK,CAAC,GAC5D,EAAE,CACX;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,aAAa,CAAC;EAChB/V,WAAWA,CAAA,EAAG;IACV,IAAI,OAAO0D,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAMsS,uBAAuB,GAAG7X,MAAM,CAACyX,+BAA+B,EAAE;QACpEK,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE;MACd,CAAC,CAAC;MACF,IAAIF,uBAAuB,EAAE;QACzB,MAAM,IAAIrZ,aAAa,CAAC,IAAI,CAAC,sDAAsD,oFAAoF,GACnK,mFAAmF,CAAC;MAC5F;IACJ;EACJ;AAIJ;AAACwZ,cAAA,GAhBKJ,aAAa;AAAA7V,eAAA,CAAb6V,aAAa,wBAAAK,uBAAArR,iBAAA;EAAA,YAAAA,iBAAA,IAaoFgR,cAAa;AAAA;AAAA7V,eAAA,CAb9G6V,aAAa,8BA3nC8DtZ,EAAE,CAAA4Z,gBAAA;EAAAhR,IAAA,EAyoCqB0Q;AAAa;AAAA7V,eAAA,CAd/G6V,aAAa,8BA3nC8DtZ,EAAE,CAAA6Z,gBAAA;EAAAxB,SAAA,EA0oC+C,CAAC,GAAGD,wBAAwB,EAAE,GAAGK,qBAAqB,CAAC;EAAAqB,OAAA,GAAY/Z,YAAY,EAAE4B,iBAAiB;AAAA;AAEpO;EAAA,QAAAsF,SAAA,oBAAAA,SAAA,KA5oCiFjH,EAAE,CAAA2I,iBAAA,CA4oCQ2Q,aAAa,EAAc,CAAC;IAC3G1Q,IAAI,EAAEhH,QAAQ;IACd4B,IAAI,EAAE,CAAC;MACC6U,SAAS,EAAE,CAAC,GAAGD,wBAAwB,EAAE,GAAGK,qBAAqB,CAAC;MAClEsB,OAAO,EAAE,CAACha,YAAY,EAAE4B,iBAAiB;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqY,IAAI,CAAC;EAGPzW,WAAWA,CAACgH,IAAI,EAAE;IAAA9G,eAAA;IAAAA,eAAA;IACd,IAAI,CAAC8G,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC0P,IAAI,GAAGva,OAAO,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIwa,MAAMA,CAACC,GAAG,EAAEC,aAAa,GAAG,KAAK,EAAE;IAC/B,IAAI,CAACD,GAAG,EACJ,OAAO,IAAI;IACf,OAAO,IAAI,CAACE,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,OAAOA,CAACC,IAAI,EAAEH,aAAa,GAAG,KAAK,EAAE;IACjC,IAAI,CAACG,IAAI,EACL,OAAO,EAAE;IACb,OAAOA,IAAI,CAACC,MAAM,CAAC,CAACpD,MAAM,EAAE+C,GAAG,KAAK;MAChC,IAAIA,GAAG,EAAE;QACL/C,MAAM,CAACxP,IAAI,CAAC,IAAI,CAACyS,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC,CAAC;MAC7D;MACA,OAAOhD,MAAM;IACjB,CAAC,EAAE,EAAE,CAAC;EACV;EACA;AACJ;AACA;AACA;AACA;AACA;EACIqD,MAAMA,CAACC,YAAY,EAAE;IACjB,IAAI,CAACA,YAAY,EACb,OAAO,IAAI;IACf,OAAO,IAAI,CAACnQ,IAAI,CAACnE,aAAa,CAAC,QAAQsU,YAAY,GAAG,CAAC,IAAI,IAAI;EACnE;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACD,YAAY,EAAE;IAClB,IAAI,CAACA,YAAY,EACb,OAAO,EAAE;IACb,MAAME,IAAI,CAAC,eAAe,IAAI,CAACrQ,IAAI,CAACc,gBAAgB,CAAC,QAAQqP,YAAY,GAAG,CAAC;IAC7E,OAAOE,IAAI,GAAG,EAAE,CAACvR,KAAK,CAACyL,IAAI,CAAC8F,IAAI,CAAC,GAAG,EAAE;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACV,GAAG,EAAEW,QAAQ,EAAE;IACrB,IAAI,CAACX,GAAG,EACJ,OAAO,IAAI;IACfW,QAAQ,GAAGA,QAAQ,IAAI,IAAI,CAACC,cAAc,CAACZ,GAAG,CAAC;IAC/C,MAAMa,IAAI,GAAG,IAAI,CAACP,MAAM,CAACK,QAAQ,CAAC;IAClC,IAAIE,IAAI,EAAE;MACN,OAAO,IAAI,CAACC,yBAAyB,CAACd,GAAG,EAAEa,IAAI,CAAC;IACpD;IACA,OAAO,IAAI,CAACX,mBAAmB,CAACF,GAAG,EAAE,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIe,SAASA,CAACR,YAAY,EAAE;IACpB,IAAI,CAACS,gBAAgB,CAAC,IAAI,CAACV,MAAM,CAACC,YAAY,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;EACIS,gBAAgBA,CAACH,IAAI,EAAE;IACnB,IAAIA,IAAI,EAAE;MACN,IAAI,CAACf,IAAI,CAAC7V,MAAM,CAAC4W,IAAI,CAAC;IAC1B;EACJ;EACAX,mBAAmBA,CAACW,IAAI,EAAEZ,aAAa,GAAG,KAAK,EAAE;IAC7C,IAAI,CAACA,aAAa,EAAE;MAChB,MAAMU,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC;MAC1C;MACA;MACA;MACA,MAAMnU,IAAI,GAAG,IAAI,CAAC8T,OAAO,CAACG,QAAQ,CAAC,CAACM,MAAM,CAAEvU,IAAI,IAAK,IAAI,CAACwU,mBAAmB,CAACL,IAAI,EAAEnU,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7F,IAAIA,IAAI,KAAKuD,SAAS,EAClB,OAAOvD,IAAI;IACnB;IACA,MAAM0C,OAAO,GAAG,IAAI,CAAC0Q,IAAI,CAAC3V,aAAa,CAAC,MAAM,CAAC;IAC/C,IAAI,CAAC2W,yBAAyB,CAACD,IAAI,EAAEzR,OAAO,CAAC;IAC7C,MAAM6B,IAAI,GAAG,IAAI,CAACb,IAAI,CAAC+Q,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACtDlQ,IAAI,CAACkC,WAAW,CAAC/D,OAAO,CAAC;IACzB,OAAOA,OAAO;EAClB;EACA0R,yBAAyBA,CAACd,GAAG,EAAEtW,EAAE,EAAE;IAC/BkN,MAAM,CAACwK,IAAI,CAACpB,GAAG,CAAC,CAACzS,OAAO,CAAE8T,IAAI,IAAK3X,EAAE,CAAC+H,YAAY,CAAC,IAAI,CAAC6P,cAAc,CAACD,IAAI,CAAC,EAAErB,GAAG,CAACqB,IAAI,CAAC,CAAC,CAAC;IACzF,OAAO3X,EAAE;EACb;EACAkX,cAAcA,CAACZ,GAAG,EAAE;IAChB,MAAMuB,IAAI,GAAGvB,GAAG,CAACjU,IAAI,GAAG,MAAM,GAAG,UAAU;IAC3C,OAAO,GAAGwV,IAAI,KAAKvB,GAAG,CAACuB,IAAI,CAAC,GAAG;EACnC;EACAL,mBAAmBA,CAAClB,GAAG,EAAEtT,IAAI,EAAE;IAC3B,OAAOkK,MAAM,CAACwK,IAAI,CAACpB,GAAG,CAAC,CAACwB,KAAK,CAAEhF,GAAG,IAAK9P,IAAI,CAACR,YAAY,CAAC,IAAI,CAACoV,cAAc,CAAC9E,GAAG,CAAC,CAAC,KAAKwD,GAAG,CAACxD,GAAG,CAAC,CAAC;EACpG;EACA8E,cAAcA,CAACD,IAAI,EAAE;IACjB,OAAOI,aAAa,CAACJ,IAAI,CAAC,IAAIA,IAAI;EACtC;AAGJ;AAACK,KAAA,GAnIK7B,IAAI;AAAAvW,eAAA,CAAJuW,IAAI,wBAAA8B,cAAAxT,iBAAA;EAAA,YAAAA,iBAAA,IAiI6F0R,KAAI,EA3yC1Bha,EAAE,CAAAmK,QAAA,CA2yC0CvK,QAAQ;AAAA;AAAA6D,eAAA,CAjI/HuW,IAAI,+BA1qCuEha,EAAE,CAAAuI,kBAAA;EAAAC,KAAA,EA4yCwBwR,KAAI;EAAAvR,OAAA,EAAJuR,KAAI,CAAAtR,IAAA;EAAAyF,UAAA,EAAc;AAAM;AAEnI;EAAA,QAAAlH,SAAA,oBAAAA,SAAA,KA9yCiFjH,EAAE,CAAA2I,iBAAA,CA8yCQqR,IAAI,EAAc,CAAC;IAClGpR,IAAI,EAAEzI,UAAU;IAChBqD,IAAI,EAAE,CAAC;MAAE2K,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvF,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAC5D,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA,MAAMgc,aAAa,GAAG;EAClBG,SAAS,EAAE;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,CAAC;EAERzY,WAAWA,CAACgH,IAAI,EAAE;IAAA9G,eAAA;IACd,IAAI,CAAC8G,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;EACI0R,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC1R,IAAI,CAAC2R,KAAK;EAC1B;EACA;AACJ;AACA;AACA;EACIC,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,CAAC7R,IAAI,CAAC2R,KAAK,GAAGE,QAAQ,IAAI,EAAE;EACpC;AAGJ;AAACC,MAAA,GApBKL,KAAK;AAAAvY,eAAA,CAALuY,KAAK,wBAAAM,eAAAhU,iBAAA;EAAA,YAAAA,iBAAA,IAkB4F0T,MAAK,EAx1C3Bhc,EAAE,CAAAmK,QAAA,CAw1C2CvK,QAAQ;AAAA;AAAA6D,eAAA,CAlBhIuY,KAAK,+BAt0CsEhc,EAAE,CAAAuI,kBAAA;EAAAC,KAAA,EAy1CwBwT,MAAK;EAAAvT,OAAA,EAALuT,MAAK,CAAAtT,IAAA;EAAAyF,UAAA,EAAc;AAAM;AAEpI;EAAA,QAAAlH,SAAA,oBAAAA,SAAA,KA31CiFjH,EAAE,CAAA2I,iBAAA,CA21CQqT,KAAK,EAAc,CAAC;IACnGpT,IAAI,EAAEzI,UAAU;IAChBqD,IAAI,EAAE,CAAC;MAAE2K,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvF,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAC5D,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2c,WAAWA,CAACrW,IAAI,EAAEqG,KAAK,EAAE;EAC9B,IAAI,OAAOiQ,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,EAAE;IAC9C;IACA;IACA;IACA;IACA,MAAMC,EAAE,GAAIxc,OAAO,CAAC,IAAI,CAAC,GAAGA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE;IAChDwc,EAAE,CAACvW,IAAI,CAAC,GAAGqG,KAAK;EACpB;AACJ;AAEA,MAAMmQ,yBAAyB,CAAC;EAG5BnZ,WAAWA,CAACoZ,SAAS,EAAEC,QAAQ,EAAE;IAAAnZ,eAAA;IAAAA,eAAA;IAC7B,IAAI,CAACkZ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAElBtZ,WAAWA,CAACuZ,GAAG,EAAE;IAAArZ,eAAA;IACb,IAAI,CAACsZ,MAAM,GAAGD,GAAG,CAACE,QAAQ,CAACnT,GAAG,CAAChI,cAAc,CAAC;EAClD;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIob,mBAAmBA,CAACC,MAAM,EAAE;IACxB,MAAMrQ,MAAM,GAAGqQ,MAAM,IAAIA,MAAM,CAAC,QAAQ,CAAC;IACzC,MAAMC,WAAW,GAAG,kBAAkB;IACtC;IACA,IAAItQ,MAAM,IAAI,SAAS,IAAIuQ,OAAO,IAAI,OAAOA,OAAO,CAACC,OAAO,KAAK,UAAU,EAAE;MACzED,OAAO,CAACC,OAAO,CAACF,WAAW,CAAC;IAChC;IACA,MAAMG,KAAK,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;IAC/B,IAAIZ,QAAQ,GAAG,CAAC;IAChB,OAAOA,QAAQ,GAAG,CAAC,IAAIW,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK,GAAG,GAAG,EAAE;MACpD,IAAI,CAACP,MAAM,CAACU,IAAI,CAAC,CAAC;MAClBb,QAAQ,EAAE;IACd;IACA,MAAMc,GAAG,GAAGH,WAAW,CAACC,GAAG,CAAC,CAAC;IAC7B,IAAI3Q,MAAM,IAAI,YAAY,IAAIuQ,OAAO,IAAI,OAAOA,OAAO,CAACO,UAAU,KAAK,UAAU,EAAE;MAC/EP,OAAO,CAACO,UAAU,CAACR,WAAW,CAAC;IACnC;IACA,MAAMR,SAAS,GAAG,CAACe,GAAG,GAAGJ,KAAK,IAAIV,QAAQ;IAC1CQ,OAAO,CAACQ,GAAG,CAAC,OAAOhB,QAAQ,0BAA0B,CAAC;IACtDQ,OAAO,CAACQ,GAAG,CAAC,GAAGjB,SAAS,CAACkB,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;IACnD,OAAO,IAAInB,yBAAyB,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAC7D;AACJ;AAEA,MAAMkB,oBAAoB,GAAG,UAAU;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACjB,GAAG,EAAE;EAC3BP,WAAW,CAACuB,oBAAoB,EAAE,IAAIjB,eAAe,CAACC,GAAG,CAAC,CAAC;EAC3D,OAAOA,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,iBAAiBA,CAAA,EAAG;EACzBzB,WAAW,CAACuB,oBAAoB,EAAE,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,EAAE,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAGA,CAAA,EAAG;IACT,OAAO,MAAM,IAAI;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAGA,CAACrD,QAAQ,EAAE;IACjB,OAAQsD,YAAY,IAAK;MACrB,OAAOA,YAAY,CAACC,aAAa,IAAI,IAAI,GACnCC,cAAc,CAACF,YAAY,CAACC,aAAa,EAAEvD,QAAQ,CAAC,GACpD,KAAK;IACf,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOyD,SAASA,CAAC3V,IAAI,EAAE;IACnB,OAAQ4V,SAAS,IAAKA,SAAS,CAACC,cAAc,CAACzH,OAAO,CAACpO,IAAI,CAAC,KAAK,CAAC,CAAC;EACvE;AACJ;AACA,SAAS0V,cAAcA,CAACI,CAAC,EAAE5D,QAAQ,EAAE;EACjC,IAAIpb,OAAO,CAAC,CAAC,CAACoF,aAAa,CAAC4Z,CAAC,CAAC,EAAE;IAC5B,OAASA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACC,OAAO,CAAC7D,QAAQ,CAAC,IACpC4D,CAAC,CAACE,iBAAiB,IAAIF,CAAC,CAACE,iBAAiB,CAAC9D,QAAQ,CAAE,IACrD4D,CAAC,CAACG,qBAAqB,IAAIH,CAAC,CAACG,qBAAqB,CAAC/D,QAAQ,CAAE;EACtE;EACA,OAAO,KAAK;AAChB;;AAEA;AACA;AACA;AACA,MAAMgE,WAAW,GAAG;EAChB;EACA,KAAK,EAAE,IAAI;EACX,UAAU,EAAE,IAAI;EAChB,SAAS,EAAE,IAAI;EACf,QAAQ,EAAE,IAAI;EACd,WAAW,EAAE,IAAI;EACjB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,OAAO,EAAE,IAAI;EACb,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,UAAU,EAAE,IAAI;EAChB,aAAa,EAAE,IAAI;EACnB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB;EACA,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,QAAQ,EAAE,IAAI;EACd,aAAa,EAAE,IAAI;EACnB,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,cAAc,EAAE,IAAI;EACpB;EACA,OAAO,EAAE,IAAI;EACb,WAAW,EAAE,IAAI;EACjB,YAAY,EAAE,IAAI;EAClB,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,IAAI;EACjB;EACA,KAAK,EAAE,IAAI;EACX,WAAW,EAAE;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,IAAI3e,cAAc,CAAC,OAAO6G,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,qBAAqB,GAAG,EAAE,CAAC;AAC5H;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+X,aAAa,GAAG,IAAI5e,cAAc,CAAC,OAAO6G,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,cAAc,GAAG,EAAE,CAAC;AAC7G;AACA;AACA;AACA;AACA;AACA,MAAMgY,mBAAmB,CAAC;EAAA1b,YAAA;IACtB;AACJ;AACA;AACA;AACA;IAJIE,eAAA,iBAKS,EAAE;IACX;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAfIA,eAAA,oBAgBY,CAAC,CAAC;IACd;AACJ;AACA;AACA;AACA;AACA;AACA;IANIA,eAAA;EAAA;EAQA;AACJ;AACA;AACA;AACA;AACA;EACIyb,WAAWA,CAAC3V,OAAO,EAAE;IACjB,MAAM4V,EAAE,GAAG,IAAIC,MAAM,CAAC7V,OAAO,EAAE,IAAI,CAACvF,OAAO,CAAC;IAC5Cmb,EAAE,CAACtV,GAAG,CAAC,OAAO,CAAC,CAACG,GAAG,CAAC;MAAEqV,MAAM,EAAE;IAAK,CAAC,CAAC;IACrCF,EAAE,CAACtV,GAAG,CAAC,QAAQ,CAAC,CAACG,GAAG,CAAC;MAAEqV,MAAM,EAAE;IAAK,CAAC,CAAC;IACtC,KAAK,MAAM7V,SAAS,IAAI,IAAI,CAAC8V,SAAS,EAAE;MACpCH,EAAE,CAACtV,GAAG,CAACL,SAAS,CAAC,CAACQ,GAAG,CAAC,IAAI,CAACsV,SAAS,CAAC9V,SAAS,CAAC,CAAC;IACpD;IACA,OAAO2V,EAAE;EACb;AAGJ;AAACI,oBAAA,GAjDKN,mBAAmB;AAAAxb,eAAA,CAAnBwb,mBAAmB,wBAAAO,6BAAAlX,iBAAA;EAAA,YAAAA,iBAAA,IA+C8E2W,oBAAmB;AAAA;AAAAxb,eAAA,CA/CpHwb,mBAAmB,+BA7jDwDjf,EAAE,CAAAuI,kBAAA;EAAAC,KAAA,EA6mDwByW,oBAAmB;EAAAxW,OAAA,EAAnBwW,oBAAmB,CAAAvW;AAAA;AAE9H;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KA/mDiFjH,EAAE,CAAA2I,iBAAA,CA+mDQsW,mBAAmB,EAAc,CAAC;IACjHrW,IAAI,EAAEzI;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMsf,oBAAoB,SAASnV,kBAAkB,CAAC;EAKlD/G,WAAWA,CAACiB,GAAG,EAAEkb,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAE;IACzC,KAAK,CAACpb,GAAG,CAAC;IAACf,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,yBAFE,IAAI;IAGjB,IAAI,CAACic,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACA7V,QAAQA,CAACP,SAAS,EAAE;IAChB,IAAI,CAACsV,WAAW,CAACe,cAAc,CAACrW,SAAS,CAAC+M,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACuJ,aAAa,CAACtW,SAAS,CAAC,EAAE;MACxF,OAAO,KAAK;IAChB;IACA,IAAI,CAAClE,MAAM,CAAC8Z,MAAM,IAAI,CAAC,IAAI,CAACQ,MAAM,EAAE;MAChC,IAAI,OAAO3Y,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C;QACA;QACA,MAAM8Y,QAAQ,GAAG,IAAI,CAACJ,SAAS,CAAC9V,GAAG,CAAC/H,QAAQ,CAAC;QAC7Cie,QAAQ,CAACC,IAAI,CAAC,QAAQxW,SAAS,mDAAmD,GAC9E,iDAAiD,CAAC;MAC1D;MACA,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACAvF,gBAAgBA,CAACsF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAMiO,IAAI,GAAG,IAAI,CAACvO,OAAO,CAACQ,OAAO,CAAC,CAAC;IACnCH,SAAS,GAAGA,SAAS,CAAC+M,WAAW,CAAC,CAAC;IACnC;IACA;IACA,IAAI,CAACjR,MAAM,CAAC8Z,MAAM,IAAI,IAAI,CAACQ,MAAM,EAAE;MAC/B,IAAI,CAACK,cAAc,GAAG,IAAI,CAACA,cAAc,IAAIvI,IAAI,CAACrB,iBAAiB,CAAC,MAAM,IAAI,CAACuJ,MAAM,CAAC,CAAC,CAAC;MACxF;MACA;MACA;MACA,IAAIM,kBAAkB,GAAG,KAAK;MAC9B,IAAIC,UAAU,GAAGA,CAAA,KAAM;QACnBD,kBAAkB,GAAG,IAAI;MAC7B,CAAC;MACDxI,IAAI,CAACrB,iBAAiB,CAAC,MAAM,IAAI,CAAC4J,cAAc,CAACG,IAAI,CAAC,MAAM;QACxD;QACA,IAAI,CAAC9a,MAAM,CAAC8Z,MAAM,EAAE;UAChB,IAAI,OAAOnY,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;YAC/C,MAAM8Y,QAAQ,GAAG,IAAI,CAACJ,SAAS,CAAC9V,GAAG,CAAC/H,QAAQ,CAAC;YAC7Cie,QAAQ,CAACC,IAAI,CAAC,mEAAmE,CAAC;UACtF;UACAG,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;UACtB;QACJ;QACA,IAAI,CAACD,kBAAkB,EAAE;UACrB;UACA;UACA;UACAC,UAAU,GAAG,IAAI,CAAClc,gBAAgB,CAACsF,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;QACnE;MACJ,CAAC,CAAC,CAAC4W,KAAK,CAAC,MAAM;QACX,IAAI,OAAOpZ,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C,MAAM8Y,QAAQ,GAAG,IAAI,CAACJ,SAAS,CAAC9V,GAAG,CAAC/H,QAAQ,CAAC;UAC7Cie,QAAQ,CAACC,IAAI,CAAC,QAAQxW,SAAS,6CAA6C,GACxE,0BAA0B,CAAC;QACnC;QACA2W,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;MAC1B,CAAC,CAAC,CAAC;MACH;MACA;MACA;MACA,OAAO,MAAM;QACTA,UAAU,CAAC,CAAC;MAChB,CAAC;IACL;IACA,OAAOzI,IAAI,CAACrB,iBAAiB,CAAC,MAAM;MAChC;MACA,MAAM8I,EAAE,GAAG,IAAI,CAACO,OAAO,CAACR,WAAW,CAAC3V,OAAO,CAAC;MAC5C,MAAMlC,QAAQ,GAAG,SAAAA,CAAUiZ,QAAQ,EAAE;QACjC5I,IAAI,CAAC3D,UAAU,CAAC,YAAY;UACxBtK,OAAO,CAAC6W,QAAQ,CAAC;QACrB,CAAC,CAAC;MACN,CAAC;MACDnB,EAAE,CAACoB,EAAE,CAAC/W,SAAS,EAAEnC,QAAQ,CAAC;MAC1B,OAAO,MAAM;QACT8X,EAAE,CAACqB,GAAG,CAAChX,SAAS,EAAEnC,QAAQ,CAAC;QAC3B;QACA,IAAI,OAAO8X,EAAE,CAAClO,OAAO,KAAK,UAAU,EAAE;UAClCkO,EAAE,CAAClO,OAAO,CAAC,CAAC;QAChB;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACA6O,aAAaA,CAACtW,SAAS,EAAE;IACrB,OAAO,IAAI,CAACkW,OAAO,CAACe,MAAM,CAACzJ,OAAO,CAACxN,SAAS,CAAC,GAAG,CAAC,CAAC;EACtD;AAGJ;AAACkX,qBAAA,GA/FKjB,oBAAoB;AAAAhc,eAAA,CAApBgc,oBAAoB,wBAAAkB,8BAAArY,iBAAA;EAAA,YAAAA,iBAAA,IA6F6EmX,qBAAoB,EAptD1Czf,EAAE,CAAAmK,QAAA,CAotD0DvK,QAAQ,GAptDpEI,EAAE,CAAAmK,QAAA,CAotD+E4U,qBAAqB,GAptDtG/e,EAAE,CAAAmK,QAAA,CAotDiHnK,EAAE,CAAC+B,QAAQ,GAptD9H/B,EAAE,CAAAmK,QAAA,CAotDyI6U,aAAa;AAAA;AAAAvb,eAAA,CA7FnOgc,oBAAoB,+BAvnDuDzf,EAAE,CAAAuI,kBAAA;EAAAC,KAAA,EAqtDwBiX,qBAAoB;EAAAhX,OAAA,EAApBgX,qBAAoB,CAAA/W;AAAA;AAE/H;EAAA,QAAAzB,SAAA,oBAAAA,SAAA,KAvtDiFjH,EAAE,CAAA2I,iBAAA,CAutDQ8W,oBAAoB,EAAc,CAAC;IAClH7W,IAAI,EAAEzI;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEyI,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAC5D,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEgJ,IAAI,EAAEqW,mBAAmB;IAAE5U,UAAU,EAAE,CAAC;MAC5CzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAACub,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEnW,IAAI,EAAE5I,EAAE,CAAC+B;EAAS,CAAC,EAAE;IAAE6G,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MACzDzB,IAAI,EAAEnI;IACV,CAAC,EAAE;MACCmI,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAACwb,aAAa;IACxB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4B,YAAY,CAAC;AAYlBC,aAAA,GAZKD,YAAY;AAAAnd,eAAA,CAAZmd,YAAY,wBAAAE,sBAAAxY,iBAAA;EAAA,YAAAA,iBAAA,IACqFsY,aAAY;AAAA;AAAAnd,eAAA,CAD7Gmd,YAAY,8BAhvD+D5gB,EAAE,CAAA4Z,gBAAA;EAAAhR,IAAA,EAkvDqBgY;AAAY;AAAAnd,eAAA,CAF9Gmd,YAAY,8BAhvD+D5gB,EAAE,CAAA6Z,gBAAA;EAAAxB,SAAA,EAmvD8C,CACrH;IACIQ,OAAO,EAAEhQ,qBAAqB;IAC9BuQ,QAAQ,EAAEqG,oBAAoB;IAC9B1G,KAAK,EAAE,IAAI;IACXE,IAAI,EAAE,CAACrZ,QAAQ,EAAEmf,qBAAqB,EAAEhd,QAAQ,EAAE,CAAC,IAAItB,QAAQ,CAAC,CAAC,EAAEue,aAAa,CAAC;EACrF,CAAC,EACD;IAAEnG,OAAO,EAAEkG,qBAAqB;IAAE3F,QAAQ,EAAE6F,mBAAmB;IAAEhG,IAAI,EAAE;EAAG,CAAC;AAC9E;AAET;EAAA,QAAAhS,SAAA,oBAAAA,SAAA,KA7vDiFjH,EAAE,CAAA2I,iBAAA,CA6vDQiY,YAAY,EAAc,CAAC;IAC1GhY,IAAI,EAAEhH,QAAQ;IACd4B,IAAI,EAAE,CAAC;MACC6U,SAAS,EAAE,CACP;QACIQ,OAAO,EAAEhQ,qBAAqB;QAC9BuQ,QAAQ,EAAEqG,oBAAoB;QAC9B1G,KAAK,EAAE,IAAI;QACXE,IAAI,EAAE,CAACrZ,QAAQ,EAAEmf,qBAAqB,EAAEhd,QAAQ,EAAE,CAAC,IAAItB,QAAQ,CAAC,CAAC,EAAEue,aAAa,CAAC;MACrF,CAAC,EACD;QAAEnG,OAAO,EAAEkG,qBAAqB;QAAE3F,QAAQ,EAAE6F,mBAAmB;QAAEhG,IAAI,EAAE;MAAG,CAAC;IAEnF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8H,YAAY,CAAC;AAGlBC,aAAA,GAHKD,YAAY;AAAAtd,eAAA,CAAZsd,YAAY,wBAAAE,sBAAA3Y,iBAAA;EAAA,YAAAA,iBAAA,IACqFyY,aAAY;AAAA;AAAAtd,eAAA,CAD7Gsd,YAAY,+BA3yD+D/gB,EAAE,CAAAuI,kBAAA;EAAAC,KAAA,EA6yDwBuY,aAAY;EAAAtY,OAAA,WAAAwY,sBAAA3Y,iBAAA;IAAA,IAAA4Y,wBAAA;IAAA,IAAA5Y,iBAAA;MAAA4Y,wBAAA,QAAA5Y,iBAAA,IAAZyY,aAAY;IAAA;MAAAG,wBAAA,GA7yDtClhB,EAAE,CAAAmK,QAAA,CA6yD2FgX,gBAAgB;IAAA;IAAA,OAAAD,wBAAA;EAAA;EAAA/S,UAAA,EAAzD;AAAM;AAE3I;EAAA,QAAAlH,SAAA,oBAAAA,SAAA,KA/yDiFjH,EAAE,CAAA2I,iBAAA,CA+yDQoY,YAAY,EAAc,CAAC;IAC1GnY,IAAI,EAAEzI,UAAU;IAChBqD,IAAI,EAAE,CAAC;MAAE2K,UAAU,EAAE,MAAM;MAAEkL,WAAW,EAAErX,UAAU,CAAC,MAAMmf,gBAAgB;IAAE,CAAC;EAClF,CAAC,CAAC;AAAA;AACV,MAAMA,gBAAgB,SAASJ,YAAY,CAAC;EAExCxd,WAAWA,CAACgH,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IAAC9G,eAAA;IACR,IAAI,CAAC8G,IAAI,GAAGA,IAAI;EACpB;EACA6W,QAAQA,CAACC,GAAG,EAAE9U,KAAK,EAAE;IACjB,IAAIA,KAAK,IAAI,IAAI,EACb,OAAO,IAAI;IACf,QAAQ8U,GAAG;MACP,KAAKnf,eAAe,CAACof,IAAI;QACrB,OAAO/U,KAAK;MAChB,KAAKrK,eAAe,CAACqf,IAAI;QACrB,IAAIpf,gCAAgC,CAACoK,KAAK,EAAE,MAAM,CAAC,qBAAqB,CAAC,EAAE;UACvE,OAAOnK,gBAAgB,CAACmK,KAAK,CAAC;QAClC;QACA,OAAOjK,cAAc,CAAC,IAAI,CAACiI,IAAI,EAAEiX,MAAM,CAACjV,KAAK,CAAC,CAAC,CAACkV,QAAQ,CAAC,CAAC;MAC9D,KAAKvf,eAAe,CAACwf,KAAK;QACtB,IAAIvf,gCAAgC,CAACoK,KAAK,EAAE,OAAO,CAAC,sBAAsB,CAAC,EAAE;UACzE,OAAOnK,gBAAgB,CAACmK,KAAK,CAAC;QAClC;QACA,OAAOA,KAAK;MAChB,KAAKrK,eAAe,CAACyf,MAAM;QACvB,IAAIxf,gCAAgC,CAACoK,KAAK,EAAE,QAAQ,CAAC,uBAAuB,CAAC,EAAE;UAC3E,OAAOnK,gBAAgB,CAACmK,KAAK,CAAC;QAClC;QACA,MAAM,IAAIrM,aAAa,CAAC,IAAI,CAAC,mDAAmD,CAAC,OAAO+G,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1H,uCAAuC,CAAC;MAChD,KAAK/E,eAAe,CAACqE,GAAG;QACpB,IAAIpE,gCAAgC,CAACoK,KAAK,EAAE,KAAK,CAAC,oBAAoB,CAAC,EAAE;UACrE,OAAOnK,gBAAgB,CAACmK,KAAK,CAAC;QAClC;QACA,OAAOlK,aAAa,CAACmf,MAAM,CAACjV,KAAK,CAAC,CAAC;MACvC,KAAKrK,eAAe,CAAC0f,YAAY;QAC7B,IAAIzf,gCAAgC,CAACoK,KAAK,EAAE,aAAa,CAAC,4BAA4B,CAAC,EAAE;UACrF,OAAOnK,gBAAgB,CAACmK,KAAK,CAAC;QAClC;QACA,MAAM,IAAIrM,aAAa,CAAC,IAAI,CAAC,yDAAyD,CAAC,OAAO+G,SAAS,KAAK,WAAW,IAAIA,SAAS,KAChI,oDAAoDhF,iBAAiB,GAAG,CAAC;MACjF;QACI,MAAM,IAAI/B,aAAa,CAAC,IAAI,CAAC,oDAAoD,CAAC,OAAO+G,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC3H,8BAA8Boa,GAAG,SAASpf,iBAAiB,GAAG,CAAC;IAC3E;EACJ;EACA4f,uBAAuBA,CAACtV,KAAK,EAAE;IAC3B,OAAOhK,4BAA4B,CAACgK,KAAK,CAAC;EAC9C;EACAuV,wBAAwBA,CAACvV,KAAK,EAAE;IAC5B,OAAO/J,6BAA6B,CAAC+J,KAAK,CAAC;EAC/C;EACAwV,yBAAyBA,CAACxV,KAAK,EAAE;IAC7B,OAAO9J,8BAA8B,CAAC8J,KAAK,CAAC;EAChD;EACAyV,sBAAsBA,CAACzV,KAAK,EAAE;IAC1B,OAAO7J,2BAA2B,CAAC6J,KAAK,CAAC;EAC7C;EACA0V,8BAA8BA,CAAC1V,KAAK,EAAE;IAClC,OAAO5J,mCAAmC,CAAC4J,KAAK,CAAC;EACrD;AAGJ;AAAC2V,iBAAA,GA7DKf,gBAAgB;AAAA1d,eAAA,CAAhB0d,gBAAgB,wBAAAgB,0BAAA7Z,iBAAA;EAAA,YAAAA,iBAAA,IA2DiF6Y,iBAAgB,EA92DtCnhB,EAAE,CAAAmK,QAAA,CA82DsDvK,QAAQ;AAAA;AAAA6D,eAAA,CA3D3I0d,gBAAgB,+BAnzD2DnhB,EAAE,CAAAuI,kBAAA;EAAAC,KAAA,EA+2DwB2Y,iBAAgB;EAAA1Y,OAAA,EAAhB0Y,iBAAgB,CAAAzY,IAAA;EAAAyF,UAAA,EAAc;AAAM;AAE/I;EAAA,QAAAlH,SAAA,oBAAAA,SAAA,KAj3DiFjH,EAAE,CAAA2I,iBAAA,CAi3DQwY,gBAAgB,EAAc,CAAC;IAC9GvY,IAAI,EAAEzI,UAAU;IAChBqD,IAAI,EAAE,CAAC;MAAE2K,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvF,IAAI,EAAEwB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CzB,IAAI,EAAEvI,MAAM;MACZmD,IAAI,EAAE,CAAC5D,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,IAAIwiB,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7BA,oBAAoB,CAACA,oBAAoB,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,GAAG,qBAAqB;EAC7FA,oBAAoB,CAACA,oBAAoB,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,GAAG,0BAA0B;EACvGA,oBAAoB,CAACA,oBAAoB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC7EA,oBAAoB,CAACA,oBAAoB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC7EA,oBAAoB,CAACA,oBAAoB,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,GAAG,sBAAsB;AACnG,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,UAAU,GAAG,EAAE,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAE;EAC7D,OAAO;IAAEF,KAAK;IAAEC;EAAW,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,uBAAuBA,CAAA,EAAG;EAC/B;EACA;EACA,OAAOJ,gBAAgB,CAACD,oBAAoB,CAACM,mBAAmB,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAAC3e,OAAO,EAAE;EAC3C;EACA,OAAOqe,gBAAgB,CAACD,oBAAoB,CAACQ,wBAAwB,EAAEvf,sBAAsB,CAACW,OAAO,CAAC,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6e,eAAeA,CAAA,EAAG;EACvB,OAAOR,gBAAgB,CAACD,oBAAoB,CAACU,WAAW,EAAElgB,gBAAgB,CAAC,CAAC,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmgB,eAAeA,CAAA,EAAG;EACvB,OAAOV,gBAAgB,CAACD,oBAAoB,CAACY,WAAW,EAAEngB,gBAAgB,CAAC,CAAC,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASogB,wBAAwBA,CAAA,EAAG;EAChC,OAAOZ,gBAAgB,CAACD,oBAAoB,CAACc,oBAAoB,EAAEpgB,yBAAyB,CAAC,CAAC,CAAC;AACnG;AACA;AACA;AACA;AACA;AACA;AACA,SAASqgB,kCAAkCA,CAAA,EAAG;EAC1C,OAAO,CACH;IACItK,OAAO,EAAE9V,uBAAuB;IAChC+V,QAAQ,EAAEA,CAAA,KAAM;MACZ,MAAMrJ,MAAM,GAAG/N,MAAM,CAACJ,MAAM,CAAC;MAC7B,MAAM8hB,UAAU,GAAG1hB,MAAM,CAACsB,iBAAiB,CAAC;MAC5C;MACA;MACA,IAAI,CAACogB,UAAU,IAAI3T,MAAM,CAAClM,WAAW,KAAKjC,MAAM,EAAE;QAC9C,MAAM8b,OAAO,GAAG1b,MAAM,CAACI,QAAQ,CAAC;QAChC,MAAMuhB,OAAO,GAAGpgB,mBAAmB,CAAC,CAAC,IAAI,CAAC,oDAAoD,iEAAiE,GAC3J,uDAAuD,GACvD,kDAAkD,CAAC;QACvDma,OAAO,CAAC4C,IAAI,CAACqD,OAAO,CAAC;MACzB;IACJ,CAAC;IACDtK,KAAK,EAAE;EACX,CAAC,CACJ;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuK,sBAAsBA,CAAC,GAAGC,QAAQ,EAAE;EACzC,MAAMlL,SAAS,GAAG,EAAE;EACpB,MAAMmL,YAAY,GAAG,IAAIxX,GAAG,CAAC,CAAC;EAC9B,MAAMyX,2BAA2B,GAAGD,YAAY,CAACE,GAAG,CAACtB,oBAAoB,CAACQ,wBAAwB,CAAC;EACnG,KAAK,MAAM;IAAEL,UAAU;IAAED;EAAM,CAAC,IAAIiB,QAAQ,EAAE;IAC1CC,YAAY,CAACrX,GAAG,CAACmW,KAAK,CAAC;IACvB,IAAIC,UAAU,CAAC/a,MAAM,EAAE;MACnB6Q,SAAS,CAACzQ,IAAI,CAAC2a,UAAU,CAAC;IAC9B;EACJ;EACA,IAAI,OAAOtb,SAAS,KAAK,WAAW,IAChCA,SAAS,IACTuc,YAAY,CAACE,GAAG,CAACtB,oBAAoB,CAACM,mBAAmB,CAAC,IAC1De,2BAA2B,EAAE;IAC7B;IACA,MAAM,IAAIhQ,KAAK,CAAC,sKAAsK,CAAC;EAC3L;EACA,OAAOvQ,wBAAwB,CAAC,CAC5B,OAAO+D,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAGkc,kCAAkC,CAAC,CAAC,GAAG,EAAE,EACzFhgB,iBAAiB,CAAC,CAAC,EACnBqgB,YAAY,CAACE,GAAG,CAACtB,oBAAoB,CAACM,mBAAmB,CAAC,IAAIe,2BAA2B,GACnF,EAAE,GACFpgB,sBAAsB,CAAC,CAAC,CAAC,CAAC,EAChCgV,SAAS,CACZ,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsL,OAAO,GAAG,IAAIvgB,OAAO,CAAC,QAAQ,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASkW,aAAa,EAAE2E,EAAE,EAAE8C,YAAY,EAAElY,qBAAqB,EAAEC,YAAY,EAAEwB,kBAAkB,EAAEyU,qBAAqB,EAAEC,aAAa,EAAEC,mBAAmB,EAAE2B,YAAY,EAAEwB,oBAAoB,EAAEpI,IAAI,EAAE9L,kCAAkC,EAAE8N,KAAK,EAAE2H,OAAO,EAAE7L,oBAAoB,EAAEG,iBAAiB,EAAE+F,iBAAiB,EAAED,gBAAgB,EAAE7E,eAAe,EAAEoK,sBAAsB,EAAE9K,+BAA+B,EAAEuK,eAAe,EAAEJ,4BAA4B,EAAEE,eAAe,EAAEI,wBAAwB,EAAER,uBAAuB,EAAE/e,iBAAiB,IAAIkgB,kBAAkB,EAAEld,qBAAqB,IAAImd,sBAAsB,EAAEvO,eAAe,IAAIwO,gBAAgB,EAAEzU,mBAAmB,IAAI0U,oBAAoB,EAAE5C,gBAAgB,IAAI6C,iBAAiB,EAAEvE,oBAAoB,IAAIwE,qBAAqB,EAAE1L,mCAAmC,IAAI2L,oCAAoC,EAAElO,eAAe,IAAImO,gBAAgB,EAAEtY,gBAAgB,IAAIuY,iBAAiB,EAAE1L,cAAc,IAAI2L,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}