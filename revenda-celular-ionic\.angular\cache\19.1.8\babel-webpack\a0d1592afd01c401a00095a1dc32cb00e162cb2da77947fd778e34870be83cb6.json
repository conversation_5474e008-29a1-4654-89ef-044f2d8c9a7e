{"ast": null, "code": "import _asyncToGenerator from \"C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-28849c61.js';\nimport { d as doc } from './index-a5d50daf.js';\nimport { r as raf, g as getElementRoot } from './helpers-da915de8.js';\nimport { a as hapticSelectionStart, b as hapticSelectionChanged, h as hapticSelectionEnd } from './haptic-ac164e4c.js';\nimport { a as isPlatform, b as getIonMode } from './ionic-global-c81d82ab.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport './capacitor-59395cbd.js';\nconst pickerColumnCss = \":host{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;max-width:100%;height:200px;font-size:22px;text-align:center}.assistive-focusable{left:0;right:0;top:0;bottom:0;position:absolute;z-index:1;pointer-events:none}.assistive-focusable:focus{outline:none}.picker-opts{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;min-width:26px;max-height:200px;outline:none;text-align:inherit;-webkit-scroll-snap-type:y mandatory;-ms-scroll-snap-type:y mandatory;scroll-snap-type:y mandatory;overflow-x:hidden;overflow-y:scroll;scrollbar-width:none}.picker-item-empty{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.picker-opts::-webkit-scrollbar{display:none}::slotted(ion-picker-column-option){display:block;scroll-snap-align:center}.picker-item-empty,:host(:not([disabled])) ::slotted(ion-picker-column-option.option-disabled){scroll-snap-align:none}::slotted([slot=prefix]),::slotted([slot=suffix]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}::slotted([slot=prefix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:end;justify-content:end}::slotted([slot=suffix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:start;justify-content:start}:host(.picker-column-disabled) .picker-opts{overflow-y:hidden}:host(.picker-column-disabled) ::slotted(ion-picker-column-option){cursor:default;opacity:0.4;pointer-events:none}@media (any-hover: hover){:host(:focus) .picker-opts{outline:none;background:rgba(var(--ion-color-base-rgb), 0.2)}}\";\nconst IonPickerColumnStyle0 = pickerColumnCss;\nconst PickerColumn = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.isScrolling = false;\n    this.isColumnVisible = false;\n    this.canExitInputMode = true;\n    this.updateValueTextOnScroll = false;\n    this.centerPickerItemInView = (target, smooth = true, canExitInputMode = true) => {\n      const {\n        isColumnVisible,\n        scrollEl\n      } = this;\n      if (isColumnVisible && scrollEl) {\n        // (Vertical offset from parent) - (three empty picker rows) + (half the height of the target to ensure the scroll triggers)\n        const top = target.offsetTop - 3 * target.clientHeight + target.clientHeight / 2;\n        if (scrollEl.scrollTop !== top) {\n          /**\n           * Setting this flag prevents input\n           * mode from exiting in the picker column's\n           * scroll callback. This is useful when the user manually\n           * taps an item or types on the keyboard as both\n           * of these can cause a scroll to occur.\n           */\n          this.canExitInputMode = canExitInputMode;\n          this.updateValueTextOnScroll = false;\n          scrollEl.scroll({\n            top,\n            left: 0,\n            behavior: smooth ? 'smooth' : undefined\n          });\n        }\n      }\n    };\n    this.setPickerItemActiveState = (item, isActive) => {\n      if (isActive) {\n        item.classList.add(PICKER_ITEM_ACTIVE_CLASS);\n      } else {\n        item.classList.remove(PICKER_ITEM_ACTIVE_CLASS);\n      }\n    };\n    /**\n     * When ionInputModeChange is emitted, each column\n     * needs to check if it is the one being made available\n     * for text entry.\n     */\n    this.inputModeChange = ev => {\n      if (!this.numericInput) {\n        return;\n      }\n      const {\n        useInputMode,\n        inputModeColumn\n      } = ev.detail;\n      /**\n       * If inputModeColumn is undefined then this means\n       * all numericInput columns are being selected.\n       */\n      const isColumnActive = inputModeColumn === undefined || inputModeColumn === this.el;\n      if (!useInputMode || !isColumnActive) {\n        this.setInputModeActive(false);\n        return;\n      }\n      this.setInputModeActive(true);\n    };\n    /**\n     * Setting isActive will cause a re-render.\n     * As a result, we do not want to cause the\n     * re-render mid scroll as this will cause\n     * the picker column to jump back to\n     * whatever value was selected at the\n     * start of the scroll interaction.\n     */\n    this.setInputModeActive = state => {\n      if (this.isScrolling) {\n        this.scrollEndCallback = () => {\n          this.isActive = state;\n        };\n        return;\n      }\n      this.isActive = state;\n    };\n    /**\n     * When the column scrolls, the component\n     * needs to determine which item is centered\n     * in the view and will emit an ionChange with\n     * the item object.\n     */\n    this.initializeScrollListener = () => {\n      /**\n       * The haptics for the wheel picker are\n       * an iOS-only feature. As a result, they should\n       * be disabled on Android.\n       */\n      const enableHaptics = isPlatform('ios');\n      const {\n        el,\n        scrollEl\n      } = this;\n      let timeout;\n      let activeEl = this.activeItem;\n      const scrollCallback = () => {\n        raf(() => {\n          var _a;\n          if (!scrollEl) return;\n          if (timeout) {\n            clearTimeout(timeout);\n            timeout = undefined;\n          }\n          if (!this.isScrolling) {\n            enableHaptics && hapticSelectionStart();\n            this.isScrolling = true;\n          }\n          /**\n           * Select item in the center of the column\n           * which is the month/year that we want to select\n           */\n          const bbox = scrollEl.getBoundingClientRect();\n          const centerX = bbox.x + bbox.width / 2;\n          const centerY = bbox.y + bbox.height / 2;\n          /**\n           * elementFromPoint returns the top-most element.\n           * This means that if an ion-backdrop is overlaying the\n           * picker then the appropriate picker column option will\n           * not be selected. To account for this, we use elementsFromPoint\n           * and use an Array.find to find the appropriate column option\n           * at that point.\n           *\n           * Additionally, the picker column could be used in the\n           * Shadow DOM (i.e. in ion-datetime) so we need to make\n           * sure we are choosing the correct host otherwise\n           * the elements returns by elementsFromPoint will be\n           * retargeted. To account for this, we check to see\n           * if the picker column has a parent shadow root. If\n           * so, we use that shadow root when doing elementsFromPoint.\n           * Otherwise, we just use the document.\n           */\n          const rootNode = el.getRootNode();\n          const hasParentShadow = rootNode instanceof ShadowRoot;\n          const referenceNode = hasParentShadow ? rootNode : doc;\n          /**\n           * If the reference node is undefined\n           * then it's likely that doc is undefined\n           * due to being in an SSR environment.\n           */\n          if (referenceNode === undefined) {\n            return;\n          }\n          const elementsAtPoint = referenceNode.elementsFromPoint(centerX, centerY);\n          /**\n           * elementsFromPoint can returns multiple elements\n           * so find the relevant picker column option if one exists.\n           */\n          const newActiveElement = elementsAtPoint.find(el => el.tagName === 'ION-PICKER-COLUMN-OPTION');\n          if (activeEl !== undefined) {\n            this.setPickerItemActiveState(activeEl, false);\n          }\n          if (newActiveElement === undefined || newActiveElement.disabled) {\n            return;\n          }\n          /**\n           * If we are selecting a new value,\n           * we need to run haptics again.\n           */\n          if (newActiveElement !== activeEl) {\n            enableHaptics && hapticSelectionChanged();\n            if (this.canExitInputMode) {\n              /**\n               * The native iOS wheel picker\n               * only dismisses the keyboard\n               * once the selected item has changed\n               * as a result of a swipe\n               * from the user. If `canExitInputMode` is\n               * `false` then this means that the\n               * scroll is happening as a result of\n               * the `value` property programmatically changing\n               * either by an application or by the user via the keyboard.\n               */\n              this.exitInputMode();\n            }\n          }\n          activeEl = newActiveElement;\n          this.setPickerItemActiveState(newActiveElement, true);\n          /**\n           * Set the aria-valuetext even though the value prop has not been updated yet.\n           * This enables some screen readers to announce the value as the users drag\n           * as opposed to when their release their pointer from the screen.\n           *\n           * When the value is programmatically updated, we will smoothly scroll\n           * to the new option. However, we do not want to update aria-valuetext mid-scroll\n           * as that can cause the old value to be briefly set before being set to the\n           * correct option. This will cause some screen readers to announce the old value\n           * again before announcing the new value. The correct valuetext will be set on render.\n           */\n          if (this.updateValueTextOnScroll) {\n            (_a = this.assistiveFocusable) === null || _a === void 0 ? void 0 : _a.setAttribute('aria-valuetext', this.getOptionValueText(newActiveElement));\n          }\n          timeout = setTimeout(() => {\n            this.isScrolling = false;\n            this.updateValueTextOnScroll = true;\n            enableHaptics && hapticSelectionEnd();\n            /**\n             * Certain tasks (such as those that\n             * cause re-renders) should only be done\n             * once scrolling has finished, otherwise\n             * flickering may occur.\n             */\n            const {\n              scrollEndCallback\n            } = this;\n            if (scrollEndCallback) {\n              scrollEndCallback();\n              this.scrollEndCallback = undefined;\n            }\n            /**\n             * Reset this flag as the\n             * next scroll interaction could\n             * be a scroll from the user. In this\n             * case, we should exit input mode.\n             */\n            this.canExitInputMode = true;\n            this.setValue(newActiveElement.value);\n          }, 250);\n        });\n      };\n      /**\n       * Wrap this in an raf so that the scroll callback\n       * does not fire when component is initially shown.\n       */\n      raf(() => {\n        if (!scrollEl) return;\n        scrollEl.addEventListener('scroll', scrollCallback);\n        this.destroyScrollListener = () => {\n          scrollEl.removeEventListener('scroll', scrollCallback);\n        };\n      });\n    };\n    /**\n     * Tells the parent picker to\n     * exit text entry mode. This is only called\n     * when the selected item changes during scroll, so\n     * we know that the user likely wants to scroll\n     * instead of type.\n     */\n    this.exitInputMode = () => {\n      const {\n        parentEl\n      } = this;\n      if (parentEl == null) return;\n      parentEl.exitInputMode();\n      /**\n       * setInputModeActive only takes\n       * effect once scrolling stops to avoid\n       * a component re-render while scrolling.\n       * However, we want the visual active\n       * indicator to go away immediately, so\n       * we call classList.remove here.\n       */\n      this.el.classList.remove('picker-column-active');\n    };\n    /**\n     * Find the next enabled option after the active option.\n     * @param stride - How many options to \"jump\" over in order to select the next option.\n     * This can be used to implement PageUp/PageDown behaviors where pressing these keys\n     * scrolls the picker by more than 1 option. For example, a stride of 5 means select\n     * the enabled option 5 options after the active one. Note that the actual option selected\n     * may be past the stride if the option at the stride is disabled.\n     */\n    this.findNextOption = (stride = 1) => {\n      const {\n        activeItem\n      } = this;\n      if (!activeItem) return null;\n      let prevNode = activeItem;\n      let node = activeItem.nextElementSibling;\n      while (node != null) {\n        if (stride > 0) {\n          stride--;\n        }\n        if (node.tagName === 'ION-PICKER-COLUMN-OPTION' && !node.disabled && stride === 0) {\n          return node;\n        }\n        prevNode = node;\n        // Use nextElementSibling instead of nextSibling to avoid text/comment nodes\n        node = node.nextElementSibling;\n      }\n      return prevNode;\n    };\n    /**\n     * Find the next enabled option after the active option.\n     * @param stride - How many options to \"jump\" over in order to select the next option.\n     * This can be used to implement PageUp/PageDown behaviors where pressing these keys\n     * scrolls the picker by more than 1 option. For example, a stride of 5 means select\n     * the enabled option 5 options before the active one. Note that the actual option selected\n     *  may be past the stride if the option at the stride is disabled.\n     */\n    this.findPreviousOption = (stride = 1) => {\n      const {\n        activeItem\n      } = this;\n      if (!activeItem) return null;\n      let nextNode = activeItem;\n      let node = activeItem.previousElementSibling;\n      while (node != null) {\n        if (stride > 0) {\n          stride--;\n        }\n        if (node.tagName === 'ION-PICKER-COLUMN-OPTION' && !node.disabled && stride === 0) {\n          return node;\n        }\n        nextNode = node;\n        // Use previousElementSibling instead of previousSibling to avoid text/comment nodes\n        node = node.previousElementSibling;\n      }\n      return nextNode;\n    };\n    this.onKeyDown = ev => {\n      /**\n       * The below operations should be inverted when running on a mobile device.\n       * For example, swiping up will dispatch an \"ArrowUp\" event. On desktop,\n       * this should cause the previous option to be selected. On mobile, swiping\n       * up causes a view to scroll down. As a result, swiping up on mobile should\n       * cause the next option to be selected. The Home/End operations remain\n       * unchanged because those always represent the first/last options, respectively.\n       */\n      const mobile = isPlatform('mobile');\n      let newOption = null;\n      switch (ev.key) {\n        case 'ArrowDown':\n          newOption = mobile ? this.findPreviousOption() : this.findNextOption();\n          break;\n        case 'ArrowUp':\n          newOption = mobile ? this.findNextOption() : this.findPreviousOption();\n          break;\n        case 'PageUp':\n          newOption = mobile ? this.findNextOption(5) : this.findPreviousOption(5);\n          break;\n        case 'PageDown':\n          newOption = mobile ? this.findPreviousOption(5) : this.findNextOption(5);\n          break;\n        case 'Home':\n          /**\n           * There is no guarantee that the first child will be an ion-picker-column-option,\n           * so we do not use firstElementChild.\n           */\n          newOption = this.el.querySelector('ion-picker-column-option:first-of-type');\n          break;\n        case 'End':\n          /**\n           * There is no guarantee that the last child will be an ion-picker-column-option,\n           * so we do not use lastElementChild.\n           */\n          newOption = this.el.querySelector('ion-picker-column-option:last-of-type');\n          break;\n      }\n      if (newOption !== null) {\n        this.setValue(newOption.value);\n        // This stops any default browser behavior such as scrolling\n        ev.preventDefault();\n      }\n    };\n    /**\n     * Utility to generate the correct text for aria-valuetext.\n     */\n    this.getOptionValueText = el => {\n      var _a;\n      return el ? (_a = el.getAttribute('aria-label')) !== null && _a !== void 0 ? _a : el.innerText : '';\n    };\n    /**\n     * Render an element that overlays the column. This element is for assistive\n     * tech to allow users to navigate the column up/down. This element should receive\n     * focus as it listens for synthesized keyboard events as required by the\n     * slider role: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/slider_role\n     */\n    this.renderAssistiveFocusable = () => {\n      const {\n        activeItem\n      } = this;\n      const valueText = this.getOptionValueText(activeItem);\n      /**\n       * When using the picker, the valuetext provides important context that valuenow\n       * does not. Additionally, using non-zero valuemin/valuemax values can cause\n       * WebKit to incorrectly announce numeric valuetext values (such as a year\n       * like \"2024\") as percentages: https://bugs.webkit.org/show_bug.cgi?id=273126\n       */\n      return h(\"div\", {\n        ref: el => this.assistiveFocusable = el,\n        class: \"assistive-focusable\",\n        role: \"slider\",\n        tabindex: this.disabled ? undefined : 0,\n        \"aria-label\": this.ariaLabel,\n        \"aria-valuemin\": 0,\n        \"aria-valuemax\": 0,\n        \"aria-valuenow\": 0,\n        \"aria-valuetext\": valueText,\n        \"aria-orientation\": \"vertical\",\n        onKeyDown: ev => this.onKeyDown(ev)\n      });\n    };\n    this.ariaLabel = null;\n    this.isActive = false;\n    this.disabled = false;\n    this.value = undefined;\n    this.color = 'primary';\n    this.numericInput = false;\n  }\n  ariaLabelChanged(newValue) {\n    this.ariaLabel = newValue;\n  }\n  valueChange() {\n    if (this.isColumnVisible) {\n      /**\n       * Only scroll the active item into view when the picker column\n       * is actively visible to the user.\n       */\n      this.scrollActiveItemIntoView(true);\n    }\n  }\n  /**\n   * Only setup scroll listeners\n   * when the picker is visible, otherwise\n   * the container will have a scroll\n   * height of 0px.\n   */\n  componentWillLoad() {\n    /**\n     * We cache parentEl in a local variable\n     * so we don't need to keep accessing\n     * the class variable (which comes with\n     * a small performance hit)\n     */\n    const parentEl = this.parentEl = this.el.closest('ion-picker');\n    const visibleCallback = entries => {\n      /**\n       * Browsers will sometimes group multiple IO events into a single callback.\n       * As a result, we want to grab the last/most recent event in case there are multiple events.\n       */\n      const ev = entries[entries.length - 1];\n      if (ev.isIntersecting) {\n        const {\n          activeItem,\n          el\n        } = this;\n        this.isColumnVisible = true;\n        /**\n         * Because this initial call to scrollActiveItemIntoView has to fire before\n         * the scroll listener is set up, we need to manage the active class manually.\n         */\n        const oldActive = getElementRoot(el).querySelector(`.${PICKER_ITEM_ACTIVE_CLASS}`);\n        if (oldActive) {\n          this.setPickerItemActiveState(oldActive, false);\n        }\n        this.scrollActiveItemIntoView();\n        if (activeItem) {\n          this.setPickerItemActiveState(activeItem, true);\n        }\n        this.initializeScrollListener();\n      } else {\n        this.isColumnVisible = false;\n        if (this.destroyScrollListener) {\n          this.destroyScrollListener();\n          this.destroyScrollListener = undefined;\n        }\n      }\n    };\n    /**\n     * Set the root to be the parent picker element\n     * This causes the IO callback\n     * to be fired in WebKit as soon as the element\n     * is visible. If we used the default root value\n     * then WebKit would only fire the IO callback\n     * after any animations (such as a modal transition)\n     * finished, and there would potentially be a flicker.\n     */\n    new IntersectionObserver(visibleCallback, {\n      threshold: 0.001,\n      root: this.parentEl\n    }).observe(this.el);\n    if (parentEl !== null) {\n      // TODO(FW-2832): type\n      parentEl.addEventListener('ionInputModeChange', ev => this.inputModeChange(ev));\n    }\n  }\n  componentDidRender() {\n    const {\n      el,\n      activeItem,\n      isColumnVisible,\n      value\n    } = this;\n    if (isColumnVisible && !activeItem) {\n      const firstOption = el.querySelector('ion-picker-column-option');\n      /**\n       * If the picker column does not have an active item and the current value\n       * does not match the first item in the picker column, that means\n       * the value is out of bounds. In this case, we assign the value to the\n       * first item to match the scroll position of the column.\n       *\n       */\n      if (firstOption !== null && firstOption.value !== value) {\n        this.setValue(firstOption.value);\n      }\n    }\n  }\n  /** @internal  */\n  scrollActiveItemIntoView(smooth = false) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const activeEl = _this.activeItem;\n      if (activeEl) {\n        _this.centerPickerItemInView(activeEl, smooth, false);\n      }\n    })();\n  }\n  /**\n   * Sets the value prop and fires the ionChange event.\n   * This is used when we need to fire ionChange from\n   * user-generated events that cannot be caught with normal\n   * input/change event listeners.\n   * @internal\n   */\n  setValue(value) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.disabled === true || _this2.value === value) {\n        return;\n      }\n      _this2.value = value;\n      _this2.ionChange.emit({\n        value\n      });\n    })();\n  }\n  /**\n   * Sets focus on the scrollable container within the picker column.\n   * Use this method instead of the global `pickerColumn.focus()`.\n   */\n  setFocus() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (_this3.assistiveFocusable) {\n        _this3.assistiveFocusable.focus();\n      }\n    })();\n  }\n  connectedCallback() {\n    var _a;\n    this.ariaLabel = (_a = this.el.getAttribute('aria-label')) !== null && _a !== void 0 ? _a : 'Select a value';\n  }\n  get activeItem() {\n    const {\n      value\n    } = this;\n    const options = Array.from(this.el.querySelectorAll('ion-picker-column-option'));\n    return options.find(option => {\n      /**\n       * If the whole picker column is disabled, the current value should appear active\n       * If the current value item is specifically disabled, it should not appear active\n       */\n      if (!this.disabled && option.disabled) {\n        return false;\n      }\n      return option.value === value;\n    });\n  }\n  render() {\n    const {\n      color,\n      disabled,\n      isActive,\n      numericInput\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'a221dc10f1eb7c41637a16d2c7167c16939822fd',\n      class: createColorClasses(color, {\n        [mode]: true,\n        ['picker-column-active']: isActive,\n        ['picker-column-numeric-input']: numericInput,\n        ['picker-column-disabled']: disabled\n      })\n    }, this.renderAssistiveFocusable(), h(\"slot\", {\n      key: '81b0656f606856f3dc0a657bf167d81a5011405e',\n      name: \"prefix\"\n    }), h(\"div\", {\n      key: '71b9de67c04150255dd66592601c9d926db0c31c',\n      \"aria-hidden\": \"true\",\n      class: \"picker-opts\",\n      ref: el => {\n        this.scrollEl = el;\n      },\n      /**\n       * When an element has an overlay scroll style and\n       * a fixed height, Firefox will focus the scrollable\n       * container if the content exceeds the container's\n       * dimensions.\n       *\n       * This causes keyboard navigation to focus to this\n       * element instead of going to the next element in\n       * the tab order.\n       *\n       * The desired behavior is for the user to be able to\n       * focus the assistive focusable element and tab to\n       * the next element in the tab order. Instead of tabbing\n       * to this element.\n       *\n       * To prevent this, we set the tabIndex to -1. This\n       * will match the behavior of the other browsers.\n       */\n      tabIndex: -1\n    }, h(\"div\", {\n      key: 'ebdc2f08c83db0cf17b4be29f28fcb00f529601e',\n      class: \"picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"div\", {\n      key: '04ab56fcb8e6a7d6af00204c4560feb99ff34a56',\n      class: \"picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"div\", {\n      key: '6cf8f538903faf0fe1e4130f3eaf7b4e2e17cb52',\n      class: \"picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"slot\", {\n      key: '1cc392307b70c576be5b81b5226ceba735957f0f'\n    }), h(\"div\", {\n      key: '23e3f28e2a99b9aa8b7c8f68ad9583e3ca63e9e2',\n      class: \"picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"div\", {\n      key: '8a0563f09780c3116af0caebe4f40587ec1f041f',\n      class: \"picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\"), h(\"div\", {\n      key: '13207e248fc0009f37e0c90a3ee2bac2f130b856',\n      class: \"picker-item-empty\",\n      \"aria-hidden\": \"true\"\n    }, \"\\u00A0\")), h(\"slot\", {\n      key: '55ecf2ab5f214f936c2468cbdb7952daf89416b8',\n      name: \"suffix\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"aria-label\": [\"ariaLabelChanged\"],\n      \"value\": [\"valueChange\"]\n    };\n  }\n};\nconst PICKER_ITEM_ACTIVE_CLASS = 'option-active';\nPickerColumn.style = IonPickerColumnStyle0;\nexport { PickerColumn as ion_picker_column };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "f", "Host", "i", "getElement", "doc", "raf", "g", "getElementRoot", "a", "hapticSelectionStart", "b", "hapticSelectionChanged", "hapticSelectionEnd", "isPlatform", "getIonMode", "c", "createColorClasses", "pickerColumnCss", "IonPickerColumnStyle0", "PickerColumn", "constructor", "hostRef", "ionChange", "isScrolling", "isColumnVisible", "canExitInputMode", "updateValueTextOnScroll", "centerPickerItemInView", "target", "smooth", "scrollEl", "top", "offsetTop", "clientHeight", "scrollTop", "scroll", "left", "behavior", "undefined", "setPickerItemActiveState", "item", "isActive", "classList", "add", "PICKER_ITEM_ACTIVE_CLASS", "remove", "inputModeChange", "ev", "numericInput", "useInputMode", "inputModeColumn", "detail", "isColumnActive", "el", "setInputModeActive", "state", "scrollEndCallback", "initializeScrollListener", "enableHaptics", "timeout", "activeEl", "activeItem", "scrollCallback", "_a", "clearTimeout", "bbox", "getBoundingClientRect", "centerX", "x", "width", "centerY", "y", "height", "rootNode", "getRootNode", "has<PERSON>arentShadow", "ShadowRoot", "referenceNode", "elementsAtPoint", "elementsFromPoint", "newActiveElement", "find", "tagName", "disabled", "exitInputMode", "assistiveFocusable", "setAttribute", "getOptionValueText", "setTimeout", "setValue", "value", "addEventListener", "destroyScrollListener", "removeEventListener", "parentEl", "findNextOption", "stride", "prevNode", "node", "nextElement<PERSON><PERSON>ling", "findPreviousOption", "nextNode", "previousElementSibling", "onKeyDown", "mobile", "newOption", "key", "querySelector", "preventDefault", "getAttribute", "innerText", "renderAssistiveFocusable", "valueText", "ref", "class", "role", "tabindex", "aria<PERSON><PERSON><PERSON>", "color", "aria<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newValue", "valueChange", "scrollActiveItemIntoView", "componentWillLoad", "closest", "visibleCallback", "entries", "length", "isIntersecting", "oldActive", "IntersectionObserver", "threshold", "root", "observe", "componentDidRender", "firstOption", "_this", "_asyncToGenerator", "_this2", "emit", "setFocus", "_this3", "focus", "connectedCallback", "options", "Array", "from", "querySelectorAll", "option", "render", "mode", "name", "tabIndex", "watchers", "style", "ion_picker_column"], "sources": ["C:/sources/integracao-revenda/revenda-celular-ionic/node_modules/@ionic/core/dist/esm/ion-picker-column.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, f as Host, i as getElement } from './index-28849c61.js';\nimport { d as doc } from './index-a5d50daf.js';\nimport { r as raf, g as getElementRoot } from './helpers-da915de8.js';\nimport { a as hapticSelectionStart, b as hapticSelectionChanged, h as hapticSelectionEnd } from './haptic-ac164e4c.js';\nimport { a as isPlatform, b as getIonMode } from './ionic-global-c81d82ab.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nimport './capacitor-59395cbd.js';\n\nconst pickerColumnCss = \":host{display:-ms-flexbox;display:flex;position:relative;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;max-width:100%;height:200px;font-size:22px;text-align:center}.assistive-focusable{left:0;right:0;top:0;bottom:0;position:absolute;z-index:1;pointer-events:none}.assistive-focusable:focus{outline:none}.picker-opts{-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0px;padding-bottom:0px;min-width:26px;max-height:200px;outline:none;text-align:inherit;-webkit-scroll-snap-type:y mandatory;-ms-scroll-snap-type:y mandatory;scroll-snap-type:y mandatory;overflow-x:hidden;overflow-y:scroll;scrollbar-width:none}.picker-item-empty{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:block;width:100%;height:34px;border:0px;outline:none;background:transparent;color:inherit;font-family:var(--ion-font-family, inherit);font-size:inherit;line-height:34px;text-align:inherit;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.picker-opts::-webkit-scrollbar{display:none}::slotted(ion-picker-column-option){display:block;scroll-snap-align:center}.picker-item-empty,:host(:not([disabled])) ::slotted(ion-picker-column-option.option-disabled){scroll-snap-align:none}::slotted([slot=prefix]),::slotted([slot=suffix]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}::slotted([slot=prefix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:end;justify-content:end}::slotted([slot=suffix]){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:0;padding-bottom:0;-ms-flex-pack:start;justify-content:start}:host(.picker-column-disabled) .picker-opts{overflow-y:hidden}:host(.picker-column-disabled) ::slotted(ion-picker-column-option){cursor:default;opacity:0.4;pointer-events:none}@media (any-hover: hover){:host(:focus) .picker-opts{outline:none;background:rgba(var(--ion-color-base-rgb), 0.2)}}\";\nconst IonPickerColumnStyle0 = pickerColumnCss;\n\nconst PickerColumn = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.isScrolling = false;\n        this.isColumnVisible = false;\n        this.canExitInputMode = true;\n        this.updateValueTextOnScroll = false;\n        this.centerPickerItemInView = (target, smooth = true, canExitInputMode = true) => {\n            const { isColumnVisible, scrollEl } = this;\n            if (isColumnVisible && scrollEl) {\n                // (Vertical offset from parent) - (three empty picker rows) + (half the height of the target to ensure the scroll triggers)\n                const top = target.offsetTop - 3 * target.clientHeight + target.clientHeight / 2;\n                if (scrollEl.scrollTop !== top) {\n                    /**\n                     * Setting this flag prevents input\n                     * mode from exiting in the picker column's\n                     * scroll callback. This is useful when the user manually\n                     * taps an item or types on the keyboard as both\n                     * of these can cause a scroll to occur.\n                     */\n                    this.canExitInputMode = canExitInputMode;\n                    this.updateValueTextOnScroll = false;\n                    scrollEl.scroll({\n                        top,\n                        left: 0,\n                        behavior: smooth ? 'smooth' : undefined,\n                    });\n                }\n            }\n        };\n        this.setPickerItemActiveState = (item, isActive) => {\n            if (isActive) {\n                item.classList.add(PICKER_ITEM_ACTIVE_CLASS);\n            }\n            else {\n                item.classList.remove(PICKER_ITEM_ACTIVE_CLASS);\n            }\n        };\n        /**\n         * When ionInputModeChange is emitted, each column\n         * needs to check if it is the one being made available\n         * for text entry.\n         */\n        this.inputModeChange = (ev) => {\n            if (!this.numericInput) {\n                return;\n            }\n            const { useInputMode, inputModeColumn } = ev.detail;\n            /**\n             * If inputModeColumn is undefined then this means\n             * all numericInput columns are being selected.\n             */\n            const isColumnActive = inputModeColumn === undefined || inputModeColumn === this.el;\n            if (!useInputMode || !isColumnActive) {\n                this.setInputModeActive(false);\n                return;\n            }\n            this.setInputModeActive(true);\n        };\n        /**\n         * Setting isActive will cause a re-render.\n         * As a result, we do not want to cause the\n         * re-render mid scroll as this will cause\n         * the picker column to jump back to\n         * whatever value was selected at the\n         * start of the scroll interaction.\n         */\n        this.setInputModeActive = (state) => {\n            if (this.isScrolling) {\n                this.scrollEndCallback = () => {\n                    this.isActive = state;\n                };\n                return;\n            }\n            this.isActive = state;\n        };\n        /**\n         * When the column scrolls, the component\n         * needs to determine which item is centered\n         * in the view and will emit an ionChange with\n         * the item object.\n         */\n        this.initializeScrollListener = () => {\n            /**\n             * The haptics for the wheel picker are\n             * an iOS-only feature. As a result, they should\n             * be disabled on Android.\n             */\n            const enableHaptics = isPlatform('ios');\n            const { el, scrollEl } = this;\n            let timeout;\n            let activeEl = this.activeItem;\n            const scrollCallback = () => {\n                raf(() => {\n                    var _a;\n                    if (!scrollEl)\n                        return;\n                    if (timeout) {\n                        clearTimeout(timeout);\n                        timeout = undefined;\n                    }\n                    if (!this.isScrolling) {\n                        enableHaptics && hapticSelectionStart();\n                        this.isScrolling = true;\n                    }\n                    /**\n                     * Select item in the center of the column\n                     * which is the month/year that we want to select\n                     */\n                    const bbox = scrollEl.getBoundingClientRect();\n                    const centerX = bbox.x + bbox.width / 2;\n                    const centerY = bbox.y + bbox.height / 2;\n                    /**\n                     * elementFromPoint returns the top-most element.\n                     * This means that if an ion-backdrop is overlaying the\n                     * picker then the appropriate picker column option will\n                     * not be selected. To account for this, we use elementsFromPoint\n                     * and use an Array.find to find the appropriate column option\n                     * at that point.\n                     *\n                     * Additionally, the picker column could be used in the\n                     * Shadow DOM (i.e. in ion-datetime) so we need to make\n                     * sure we are choosing the correct host otherwise\n                     * the elements returns by elementsFromPoint will be\n                     * retargeted. To account for this, we check to see\n                     * if the picker column has a parent shadow root. If\n                     * so, we use that shadow root when doing elementsFromPoint.\n                     * Otherwise, we just use the document.\n                     */\n                    const rootNode = el.getRootNode();\n                    const hasParentShadow = rootNode instanceof ShadowRoot;\n                    const referenceNode = hasParentShadow ? rootNode : doc;\n                    /**\n                     * If the reference node is undefined\n                     * then it's likely that doc is undefined\n                     * due to being in an SSR environment.\n                     */\n                    if (referenceNode === undefined) {\n                        return;\n                    }\n                    const elementsAtPoint = referenceNode.elementsFromPoint(centerX, centerY);\n                    /**\n                     * elementsFromPoint can returns multiple elements\n                     * so find the relevant picker column option if one exists.\n                     */\n                    const newActiveElement = elementsAtPoint.find((el) => el.tagName === 'ION-PICKER-COLUMN-OPTION');\n                    if (activeEl !== undefined) {\n                        this.setPickerItemActiveState(activeEl, false);\n                    }\n                    if (newActiveElement === undefined || newActiveElement.disabled) {\n                        return;\n                    }\n                    /**\n                     * If we are selecting a new value,\n                     * we need to run haptics again.\n                     */\n                    if (newActiveElement !== activeEl) {\n                        enableHaptics && hapticSelectionChanged();\n                        if (this.canExitInputMode) {\n                            /**\n                             * The native iOS wheel picker\n                             * only dismisses the keyboard\n                             * once the selected item has changed\n                             * as a result of a swipe\n                             * from the user. If `canExitInputMode` is\n                             * `false` then this means that the\n                             * scroll is happening as a result of\n                             * the `value` property programmatically changing\n                             * either by an application or by the user via the keyboard.\n                             */\n                            this.exitInputMode();\n                        }\n                    }\n                    activeEl = newActiveElement;\n                    this.setPickerItemActiveState(newActiveElement, true);\n                    /**\n                     * Set the aria-valuetext even though the value prop has not been updated yet.\n                     * This enables some screen readers to announce the value as the users drag\n                     * as opposed to when their release their pointer from the screen.\n                     *\n                     * When the value is programmatically updated, we will smoothly scroll\n                     * to the new option. However, we do not want to update aria-valuetext mid-scroll\n                     * as that can cause the old value to be briefly set before being set to the\n                     * correct option. This will cause some screen readers to announce the old value\n                     * again before announcing the new value. The correct valuetext will be set on render.\n                     */\n                    if (this.updateValueTextOnScroll) {\n                        (_a = this.assistiveFocusable) === null || _a === void 0 ? void 0 : _a.setAttribute('aria-valuetext', this.getOptionValueText(newActiveElement));\n                    }\n                    timeout = setTimeout(() => {\n                        this.isScrolling = false;\n                        this.updateValueTextOnScroll = true;\n                        enableHaptics && hapticSelectionEnd();\n                        /**\n                         * Certain tasks (such as those that\n                         * cause re-renders) should only be done\n                         * once scrolling has finished, otherwise\n                         * flickering may occur.\n                         */\n                        const { scrollEndCallback } = this;\n                        if (scrollEndCallback) {\n                            scrollEndCallback();\n                            this.scrollEndCallback = undefined;\n                        }\n                        /**\n                         * Reset this flag as the\n                         * next scroll interaction could\n                         * be a scroll from the user. In this\n                         * case, we should exit input mode.\n                         */\n                        this.canExitInputMode = true;\n                        this.setValue(newActiveElement.value);\n                    }, 250);\n                });\n            };\n            /**\n             * Wrap this in an raf so that the scroll callback\n             * does not fire when component is initially shown.\n             */\n            raf(() => {\n                if (!scrollEl)\n                    return;\n                scrollEl.addEventListener('scroll', scrollCallback);\n                this.destroyScrollListener = () => {\n                    scrollEl.removeEventListener('scroll', scrollCallback);\n                };\n            });\n        };\n        /**\n         * Tells the parent picker to\n         * exit text entry mode. This is only called\n         * when the selected item changes during scroll, so\n         * we know that the user likely wants to scroll\n         * instead of type.\n         */\n        this.exitInputMode = () => {\n            const { parentEl } = this;\n            if (parentEl == null)\n                return;\n            parentEl.exitInputMode();\n            /**\n             * setInputModeActive only takes\n             * effect once scrolling stops to avoid\n             * a component re-render while scrolling.\n             * However, we want the visual active\n             * indicator to go away immediately, so\n             * we call classList.remove here.\n             */\n            this.el.classList.remove('picker-column-active');\n        };\n        /**\n         * Find the next enabled option after the active option.\n         * @param stride - How many options to \"jump\" over in order to select the next option.\n         * This can be used to implement PageUp/PageDown behaviors where pressing these keys\n         * scrolls the picker by more than 1 option. For example, a stride of 5 means select\n         * the enabled option 5 options after the active one. Note that the actual option selected\n         * may be past the stride if the option at the stride is disabled.\n         */\n        this.findNextOption = (stride = 1) => {\n            const { activeItem } = this;\n            if (!activeItem)\n                return null;\n            let prevNode = activeItem;\n            let node = activeItem.nextElementSibling;\n            while (node != null) {\n                if (stride > 0) {\n                    stride--;\n                }\n                if (node.tagName === 'ION-PICKER-COLUMN-OPTION' && !node.disabled && stride === 0) {\n                    return node;\n                }\n                prevNode = node;\n                // Use nextElementSibling instead of nextSibling to avoid text/comment nodes\n                node = node.nextElementSibling;\n            }\n            return prevNode;\n        };\n        /**\n         * Find the next enabled option after the active option.\n         * @param stride - How many options to \"jump\" over in order to select the next option.\n         * This can be used to implement PageUp/PageDown behaviors where pressing these keys\n         * scrolls the picker by more than 1 option. For example, a stride of 5 means select\n         * the enabled option 5 options before the active one. Note that the actual option selected\n         *  may be past the stride if the option at the stride is disabled.\n         */\n        this.findPreviousOption = (stride = 1) => {\n            const { activeItem } = this;\n            if (!activeItem)\n                return null;\n            let nextNode = activeItem;\n            let node = activeItem.previousElementSibling;\n            while (node != null) {\n                if (stride > 0) {\n                    stride--;\n                }\n                if (node.tagName === 'ION-PICKER-COLUMN-OPTION' && !node.disabled && stride === 0) {\n                    return node;\n                }\n                nextNode = node;\n                // Use previousElementSibling instead of previousSibling to avoid text/comment nodes\n                node = node.previousElementSibling;\n            }\n            return nextNode;\n        };\n        this.onKeyDown = (ev) => {\n            /**\n             * The below operations should be inverted when running on a mobile device.\n             * For example, swiping up will dispatch an \"ArrowUp\" event. On desktop,\n             * this should cause the previous option to be selected. On mobile, swiping\n             * up causes a view to scroll down. As a result, swiping up on mobile should\n             * cause the next option to be selected. The Home/End operations remain\n             * unchanged because those always represent the first/last options, respectively.\n             */\n            const mobile = isPlatform('mobile');\n            let newOption = null;\n            switch (ev.key) {\n                case 'ArrowDown':\n                    newOption = mobile ? this.findPreviousOption() : this.findNextOption();\n                    break;\n                case 'ArrowUp':\n                    newOption = mobile ? this.findNextOption() : this.findPreviousOption();\n                    break;\n                case 'PageUp':\n                    newOption = mobile ? this.findNextOption(5) : this.findPreviousOption(5);\n                    break;\n                case 'PageDown':\n                    newOption = mobile ? this.findPreviousOption(5) : this.findNextOption(5);\n                    break;\n                case 'Home':\n                    /**\n                     * There is no guarantee that the first child will be an ion-picker-column-option,\n                     * so we do not use firstElementChild.\n                     */\n                    newOption = this.el.querySelector('ion-picker-column-option:first-of-type');\n                    break;\n                case 'End':\n                    /**\n                     * There is no guarantee that the last child will be an ion-picker-column-option,\n                     * so we do not use lastElementChild.\n                     */\n                    newOption = this.el.querySelector('ion-picker-column-option:last-of-type');\n                    break;\n            }\n            if (newOption !== null) {\n                this.setValue(newOption.value);\n                // This stops any default browser behavior such as scrolling\n                ev.preventDefault();\n            }\n        };\n        /**\n         * Utility to generate the correct text for aria-valuetext.\n         */\n        this.getOptionValueText = (el) => {\n            var _a;\n            return el ? (_a = el.getAttribute('aria-label')) !== null && _a !== void 0 ? _a : el.innerText : '';\n        };\n        /**\n         * Render an element that overlays the column. This element is for assistive\n         * tech to allow users to navigate the column up/down. This element should receive\n         * focus as it listens for synthesized keyboard events as required by the\n         * slider role: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/slider_role\n         */\n        this.renderAssistiveFocusable = () => {\n            const { activeItem } = this;\n            const valueText = this.getOptionValueText(activeItem);\n            /**\n             * When using the picker, the valuetext provides important context that valuenow\n             * does not. Additionally, using non-zero valuemin/valuemax values can cause\n             * WebKit to incorrectly announce numeric valuetext values (such as a year\n             * like \"2024\") as percentages: https://bugs.webkit.org/show_bug.cgi?id=273126\n             */\n            return (h(\"div\", { ref: (el) => (this.assistiveFocusable = el), class: \"assistive-focusable\", role: \"slider\", tabindex: this.disabled ? undefined : 0, \"aria-label\": this.ariaLabel, \"aria-valuemin\": 0, \"aria-valuemax\": 0, \"aria-valuenow\": 0, \"aria-valuetext\": valueText, \"aria-orientation\": \"vertical\", onKeyDown: (ev) => this.onKeyDown(ev) }));\n        };\n        this.ariaLabel = null;\n        this.isActive = false;\n        this.disabled = false;\n        this.value = undefined;\n        this.color = 'primary';\n        this.numericInput = false;\n    }\n    ariaLabelChanged(newValue) {\n        this.ariaLabel = newValue;\n    }\n    valueChange() {\n        if (this.isColumnVisible) {\n            /**\n             * Only scroll the active item into view when the picker column\n             * is actively visible to the user.\n             */\n            this.scrollActiveItemIntoView(true);\n        }\n    }\n    /**\n     * Only setup scroll listeners\n     * when the picker is visible, otherwise\n     * the container will have a scroll\n     * height of 0px.\n     */\n    componentWillLoad() {\n        /**\n         * We cache parentEl in a local variable\n         * so we don't need to keep accessing\n         * the class variable (which comes with\n         * a small performance hit)\n         */\n        const parentEl = (this.parentEl = this.el.closest('ion-picker'));\n        const visibleCallback = (entries) => {\n            /**\n             * Browsers will sometimes group multiple IO events into a single callback.\n             * As a result, we want to grab the last/most recent event in case there are multiple events.\n             */\n            const ev = entries[entries.length - 1];\n            if (ev.isIntersecting) {\n                const { activeItem, el } = this;\n                this.isColumnVisible = true;\n                /**\n                 * Because this initial call to scrollActiveItemIntoView has to fire before\n                 * the scroll listener is set up, we need to manage the active class manually.\n                 */\n                const oldActive = getElementRoot(el).querySelector(`.${PICKER_ITEM_ACTIVE_CLASS}`);\n                if (oldActive) {\n                    this.setPickerItemActiveState(oldActive, false);\n                }\n                this.scrollActiveItemIntoView();\n                if (activeItem) {\n                    this.setPickerItemActiveState(activeItem, true);\n                }\n                this.initializeScrollListener();\n            }\n            else {\n                this.isColumnVisible = false;\n                if (this.destroyScrollListener) {\n                    this.destroyScrollListener();\n                    this.destroyScrollListener = undefined;\n                }\n            }\n        };\n        /**\n         * Set the root to be the parent picker element\n         * This causes the IO callback\n         * to be fired in WebKit as soon as the element\n         * is visible. If we used the default root value\n         * then WebKit would only fire the IO callback\n         * after any animations (such as a modal transition)\n         * finished, and there would potentially be a flicker.\n         */\n        new IntersectionObserver(visibleCallback, { threshold: 0.001, root: this.parentEl }).observe(this.el);\n        if (parentEl !== null) {\n            // TODO(FW-2832): type\n            parentEl.addEventListener('ionInputModeChange', (ev) => this.inputModeChange(ev));\n        }\n    }\n    componentDidRender() {\n        const { el, activeItem, isColumnVisible, value } = this;\n        if (isColumnVisible && !activeItem) {\n            const firstOption = el.querySelector('ion-picker-column-option');\n            /**\n             * If the picker column does not have an active item and the current value\n             * does not match the first item in the picker column, that means\n             * the value is out of bounds. In this case, we assign the value to the\n             * first item to match the scroll position of the column.\n             *\n             */\n            if (firstOption !== null && firstOption.value !== value) {\n                this.setValue(firstOption.value);\n            }\n        }\n    }\n    /** @internal  */\n    async scrollActiveItemIntoView(smooth = false) {\n        const activeEl = this.activeItem;\n        if (activeEl) {\n            this.centerPickerItemInView(activeEl, smooth, false);\n        }\n    }\n    /**\n     * Sets the value prop and fires the ionChange event.\n     * This is used when we need to fire ionChange from\n     * user-generated events that cannot be caught with normal\n     * input/change event listeners.\n     * @internal\n     */\n    async setValue(value) {\n        if (this.disabled === true || this.value === value) {\n            return;\n        }\n        this.value = value;\n        this.ionChange.emit({ value });\n    }\n    /**\n     * Sets focus on the scrollable container within the picker column.\n     * Use this method instead of the global `pickerColumn.focus()`.\n     */\n    async setFocus() {\n        if (this.assistiveFocusable) {\n            this.assistiveFocusable.focus();\n        }\n    }\n    connectedCallback() {\n        var _a;\n        this.ariaLabel = (_a = this.el.getAttribute('aria-label')) !== null && _a !== void 0 ? _a : 'Select a value';\n    }\n    get activeItem() {\n        const { value } = this;\n        const options = Array.from(this.el.querySelectorAll('ion-picker-column-option'));\n        return options.find((option) => {\n            /**\n             * If the whole picker column is disabled, the current value should appear active\n             * If the current value item is specifically disabled, it should not appear active\n             */\n            if (!this.disabled && option.disabled) {\n                return false;\n            }\n            return option.value === value;\n        });\n    }\n    render() {\n        const { color, disabled, isActive, numericInput } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'a221dc10f1eb7c41637a16d2c7167c16939822fd', class: createColorClasses(color, {\n                [mode]: true,\n                ['picker-column-active']: isActive,\n                ['picker-column-numeric-input']: numericInput,\n                ['picker-column-disabled']: disabled,\n            }) }, this.renderAssistiveFocusable(), h(\"slot\", { key: '81b0656f606856f3dc0a657bf167d81a5011405e', name: \"prefix\" }), h(\"div\", { key: '71b9de67c04150255dd66592601c9d926db0c31c', \"aria-hidden\": \"true\", class: \"picker-opts\", ref: (el) => {\n                this.scrollEl = el;\n            },\n            /**\n             * When an element has an overlay scroll style and\n             * a fixed height, Firefox will focus the scrollable\n             * container if the content exceeds the container's\n             * dimensions.\n             *\n             * This causes keyboard navigation to focus to this\n             * element instead of going to the next element in\n             * the tab order.\n             *\n             * The desired behavior is for the user to be able to\n             * focus the assistive focusable element and tab to\n             * the next element in the tab order. Instead of tabbing\n             * to this element.\n             *\n             * To prevent this, we set the tabIndex to -1. This\n             * will match the behavior of the other browsers.\n             */\n            tabIndex: -1 }, h(\"div\", { key: 'ebdc2f08c83db0cf17b4be29f28fcb00f529601e', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"div\", { key: '04ab56fcb8e6a7d6af00204c4560feb99ff34a56', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"div\", { key: '6cf8f538903faf0fe1e4130f3eaf7b4e2e17cb52', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"slot\", { key: '1cc392307b70c576be5b81b5226ceba735957f0f' }), h(\"div\", { key: '23e3f28e2a99b9aa8b7c8f68ad9583e3ca63e9e2', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"div\", { key: '8a0563f09780c3116af0caebe4f40587ec1f041f', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\"), h(\"div\", { key: '13207e248fc0009f37e0c90a3ee2bac2f130b856', class: \"picker-item-empty\", \"aria-hidden\": \"true\" }, \"\\u00A0\")), h(\"slot\", { key: '55ecf2ab5f214f936c2468cbdb7952daf89416b8', name: \"suffix\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"aria-label\": [\"ariaLabelChanged\"],\n        \"value\": [\"valueChange\"]\n    }; }\n};\nconst PICKER_ITEM_ACTIVE_CLASS = 'option-active';\nPickerColumn.style = IonPickerColumnStyle0;\n\nexport { PickerColumn as ion_picker_column };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC5G,SAASN,CAAC,IAAIO,GAAG,QAAQ,qBAAqB;AAC9C,SAAST,CAAC,IAAIU,GAAG,EAAEC,CAAC,IAAIC,cAAc,QAAQ,uBAAuB;AACrE,SAASC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,sBAAsB,EAAEZ,CAAC,IAAIa,kBAAkB,QAAQ,sBAAsB;AACtH,SAASJ,CAAC,IAAIK,UAAU,EAAEH,CAAC,IAAII,UAAU,QAAQ,4BAA4B;AAC7E,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,qBAAqB;AAC7D,OAAO,yBAAyB;AAEhC,MAAMC,eAAe,GAAG,4mEAA4mE;AACpoE,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,YAAY,GAAG,MAAM;EACvBC,WAAWA,CAACC,OAAO,EAAE;IACjBzB,gBAAgB,CAAC,IAAI,EAAEyB,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAGxB,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACyB,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACpC,IAAI,CAACC,sBAAsB,GAAG,CAACC,MAAM,EAAEC,MAAM,GAAG,IAAI,EAAEJ,gBAAgB,GAAG,IAAI,KAAK;MAC9E,MAAM;QAAED,eAAe;QAAEM;MAAS,CAAC,GAAG,IAAI;MAC1C,IAAIN,eAAe,IAAIM,QAAQ,EAAE;QAC7B;QACA,MAAMC,GAAG,GAAGH,MAAM,CAACI,SAAS,GAAG,CAAC,GAAGJ,MAAM,CAACK,YAAY,GAAGL,MAAM,CAACK,YAAY,GAAG,CAAC;QAChF,IAAIH,QAAQ,CAACI,SAAS,KAAKH,GAAG,EAAE;UAC5B;AACpB;AACA;AACA;AACA;AACA;AACA;UACoB,IAAI,CAACN,gBAAgB,GAAGA,gBAAgB;UACxC,IAAI,CAACC,uBAAuB,GAAG,KAAK;UACpCI,QAAQ,CAACK,MAAM,CAAC;YACZJ,GAAG;YACHK,IAAI,EAAE,CAAC;YACPC,QAAQ,EAAER,MAAM,GAAG,QAAQ,GAAGS;UAClC,CAAC,CAAC;QACN;MACJ;IACJ,CAAC;IACD,IAAI,CAACC,wBAAwB,GAAG,CAACC,IAAI,EAAEC,QAAQ,KAAK;MAChD,IAAIA,QAAQ,EAAE;QACVD,IAAI,CAACE,SAAS,CAACC,GAAG,CAACC,wBAAwB,CAAC;MAChD,CAAC,MACI;QACDJ,IAAI,CAACE,SAAS,CAACG,MAAM,CAACD,wBAAwB,CAAC;MACnD;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACE,eAAe,GAAIC,EAAE,IAAK;MAC3B,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;QACpB;MACJ;MACA,MAAM;QAAEC,YAAY;QAAEC;MAAgB,CAAC,GAAGH,EAAE,CAACI,MAAM;MACnD;AACZ;AACA;AACA;MACY,MAAMC,cAAc,GAAGF,eAAe,KAAKZ,SAAS,IAAIY,eAAe,KAAK,IAAI,CAACG,EAAE;MACnF,IAAI,CAACJ,YAAY,IAAI,CAACG,cAAc,EAAE;QAClC,IAAI,CAACE,kBAAkB,CAAC,KAAK,CAAC;QAC9B;MACJ;MACA,IAAI,CAACA,kBAAkB,CAAC,IAAI,CAAC;IACjC,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACA,kBAAkB,GAAIC,KAAK,IAAK;MACjC,IAAI,IAAI,CAAChC,WAAW,EAAE;QAClB,IAAI,CAACiC,iBAAiB,GAAG,MAAM;UAC3B,IAAI,CAACf,QAAQ,GAAGc,KAAK;QACzB,CAAC;QACD;MACJ;MACA,IAAI,CAACd,QAAQ,GAAGc,KAAK;IACzB,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACE,wBAAwB,GAAG,MAAM;MAClC;AACZ;AACA;AACA;AACA;MACY,MAAMC,aAAa,GAAG7C,UAAU,CAAC,KAAK,CAAC;MACvC,MAAM;QAAEwC,EAAE;QAAEvB;MAAS,CAAC,GAAG,IAAI;MAC7B,IAAI6B,OAAO;MACX,IAAIC,QAAQ,GAAG,IAAI,CAACC,UAAU;MAC9B,MAAMC,cAAc,GAAGA,CAAA,KAAM;QACzBzD,GAAG,CAAC,MAAM;UACN,IAAI0D,EAAE;UACN,IAAI,CAACjC,QAAQ,EACT;UACJ,IAAI6B,OAAO,EAAE;YACTK,YAAY,CAACL,OAAO,CAAC;YACrBA,OAAO,GAAGrB,SAAS;UACvB;UACA,IAAI,CAAC,IAAI,CAACf,WAAW,EAAE;YACnBmC,aAAa,IAAIjD,oBAAoB,CAAC,CAAC;YACvC,IAAI,CAACc,WAAW,GAAG,IAAI;UAC3B;UACA;AACpB;AACA;AACA;UACoB,MAAM0C,IAAI,GAAGnC,QAAQ,CAACoC,qBAAqB,CAAC,CAAC;UAC7C,MAAMC,OAAO,GAAGF,IAAI,CAACG,CAAC,GAAGH,IAAI,CAACI,KAAK,GAAG,CAAC;UACvC,MAAMC,OAAO,GAAGL,IAAI,CAACM,CAAC,GAAGN,IAAI,CAACO,MAAM,GAAG,CAAC;UACxC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,MAAMC,QAAQ,GAAGpB,EAAE,CAACqB,WAAW,CAAC,CAAC;UACjC,MAAMC,eAAe,GAAGF,QAAQ,YAAYG,UAAU;UACtD,MAAMC,aAAa,GAAGF,eAAe,GAAGF,QAAQ,GAAGrE,GAAG;UACtD;AACpB;AACA;AACA;AACA;UACoB,IAAIyE,aAAa,KAAKvC,SAAS,EAAE;YAC7B;UACJ;UACA,MAAMwC,eAAe,GAAGD,aAAa,CAACE,iBAAiB,CAACZ,OAAO,EAAEG,OAAO,CAAC;UACzE;AACpB;AACA;AACA;UACoB,MAAMU,gBAAgB,GAAGF,eAAe,CAACG,IAAI,CAAE5B,EAAE,IAAKA,EAAE,CAAC6B,OAAO,KAAK,0BAA0B,CAAC;UAChG,IAAItB,QAAQ,KAAKtB,SAAS,EAAE;YACxB,IAAI,CAACC,wBAAwB,CAACqB,QAAQ,EAAE,KAAK,CAAC;UAClD;UACA,IAAIoB,gBAAgB,KAAK1C,SAAS,IAAI0C,gBAAgB,CAACG,QAAQ,EAAE;YAC7D;UACJ;UACA;AACpB;AACA;AACA;UACoB,IAAIH,gBAAgB,KAAKpB,QAAQ,EAAE;YAC/BF,aAAa,IAAI/C,sBAAsB,CAAC,CAAC;YACzC,IAAI,IAAI,CAACc,gBAAgB,EAAE;cACvB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;cAC4B,IAAI,CAAC2D,aAAa,CAAC,CAAC;YACxB;UACJ;UACAxB,QAAQ,GAAGoB,gBAAgB;UAC3B,IAAI,CAACzC,wBAAwB,CAACyC,gBAAgB,EAAE,IAAI,CAAC;UACrD;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACoB,IAAI,IAAI,CAACtD,uBAAuB,EAAE;YAC9B,CAACqC,EAAE,GAAG,IAAI,CAACsB,kBAAkB,MAAM,IAAI,IAAItB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuB,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAACC,kBAAkB,CAACP,gBAAgB,CAAC,CAAC;UACpJ;UACArB,OAAO,GAAG6B,UAAU,CAAC,MAAM;YACvB,IAAI,CAACjE,WAAW,GAAG,KAAK;YACxB,IAAI,CAACG,uBAAuB,GAAG,IAAI;YACnCgC,aAAa,IAAI9C,kBAAkB,CAAC,CAAC;YACrC;AACxB;AACA;AACA;AACA;AACA;YACwB,MAAM;cAAE4C;YAAkB,CAAC,GAAG,IAAI;YAClC,IAAIA,iBAAiB,EAAE;cACnBA,iBAAiB,CAAC,CAAC;cACnB,IAAI,CAACA,iBAAiB,GAAGlB,SAAS;YACtC;YACA;AACxB;AACA;AACA;AACA;AACA;YACwB,IAAI,CAACb,gBAAgB,GAAG,IAAI;YAC5B,IAAI,CAACgE,QAAQ,CAACT,gBAAgB,CAACU,KAAK,CAAC;UACzC,CAAC,EAAE,GAAG,CAAC;QACX,CAAC,CAAC;MACN,CAAC;MACD;AACZ;AACA;AACA;MACYrF,GAAG,CAAC,MAAM;QACN,IAAI,CAACyB,QAAQ,EACT;QACJA,QAAQ,CAAC6D,gBAAgB,CAAC,QAAQ,EAAE7B,cAAc,CAAC;QACnD,IAAI,CAAC8B,qBAAqB,GAAG,MAAM;UAC/B9D,QAAQ,CAAC+D,mBAAmB,CAAC,QAAQ,EAAE/B,cAAc,CAAC;QAC1D,CAAC;MACL,CAAC,CAAC;IACN,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACsB,aAAa,GAAG,MAAM;MACvB,MAAM;QAAEU;MAAS,CAAC,GAAG,IAAI;MACzB,IAAIA,QAAQ,IAAI,IAAI,EAChB;MACJA,QAAQ,CAACV,aAAa,CAAC,CAAC;MACxB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAAC/B,EAAE,CAACX,SAAS,CAACG,MAAM,CAAC,sBAAsB,CAAC;IACpD,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkD,cAAc,GAAG,CAACC,MAAM,GAAG,CAAC,KAAK;MAClC,MAAM;QAAEnC;MAAW,CAAC,GAAG,IAAI;MAC3B,IAAI,CAACA,UAAU,EACX,OAAO,IAAI;MACf,IAAIoC,QAAQ,GAAGpC,UAAU;MACzB,IAAIqC,IAAI,GAAGrC,UAAU,CAACsC,kBAAkB;MACxC,OAAOD,IAAI,IAAI,IAAI,EAAE;QACjB,IAAIF,MAAM,GAAG,CAAC,EAAE;UACZA,MAAM,EAAE;QACZ;QACA,IAAIE,IAAI,CAAChB,OAAO,KAAK,0BAA0B,IAAI,CAACgB,IAAI,CAACf,QAAQ,IAAIa,MAAM,KAAK,CAAC,EAAE;UAC/E,OAAOE,IAAI;QACf;QACAD,QAAQ,GAAGC,IAAI;QACf;QACAA,IAAI,GAAGA,IAAI,CAACC,kBAAkB;MAClC;MACA,OAAOF,QAAQ;IACnB,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACG,kBAAkB,GAAG,CAACJ,MAAM,GAAG,CAAC,KAAK;MACtC,MAAM;QAAEnC;MAAW,CAAC,GAAG,IAAI;MAC3B,IAAI,CAACA,UAAU,EACX,OAAO,IAAI;MACf,IAAIwC,QAAQ,GAAGxC,UAAU;MACzB,IAAIqC,IAAI,GAAGrC,UAAU,CAACyC,sBAAsB;MAC5C,OAAOJ,IAAI,IAAI,IAAI,EAAE;QACjB,IAAIF,MAAM,GAAG,CAAC,EAAE;UACZA,MAAM,EAAE;QACZ;QACA,IAAIE,IAAI,CAAChB,OAAO,KAAK,0BAA0B,IAAI,CAACgB,IAAI,CAACf,QAAQ,IAAIa,MAAM,KAAK,CAAC,EAAE;UAC/E,OAAOE,IAAI;QACf;QACAG,QAAQ,GAAGH,IAAI;QACf;QACAA,IAAI,GAAGA,IAAI,CAACI,sBAAsB;MACtC;MACA,OAAOD,QAAQ;IACnB,CAAC;IACD,IAAI,CAACE,SAAS,GAAIxD,EAAE,IAAK;MACrB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMyD,MAAM,GAAG3F,UAAU,CAAC,QAAQ,CAAC;MACnC,IAAI4F,SAAS,GAAG,IAAI;MACpB,QAAQ1D,EAAE,CAAC2D,GAAG;QACV,KAAK,WAAW;UACZD,SAAS,GAAGD,MAAM,GAAG,IAAI,CAACJ,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACL,cAAc,CAAC,CAAC;UACtE;QACJ,KAAK,SAAS;UACVU,SAAS,GAAGD,MAAM,GAAG,IAAI,CAACT,cAAc,CAAC,CAAC,GAAG,IAAI,CAACK,kBAAkB,CAAC,CAAC;UACtE;QACJ,KAAK,QAAQ;UACTK,SAAS,GAAGD,MAAM,GAAG,IAAI,CAACT,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAACK,kBAAkB,CAAC,CAAC,CAAC;UACxE;QACJ,KAAK,UAAU;UACXK,SAAS,GAAGD,MAAM,GAAG,IAAI,CAACJ,kBAAkB,CAAC,CAAC,CAAC,GAAG,IAAI,CAACL,cAAc,CAAC,CAAC,CAAC;UACxE;QACJ,KAAK,MAAM;UACP;AACpB;AACA;AACA;UACoBU,SAAS,GAAG,IAAI,CAACpD,EAAE,CAACsD,aAAa,CAAC,wCAAwC,CAAC;UAC3E;QACJ,KAAK,KAAK;UACN;AACpB;AACA;AACA;UACoBF,SAAS,GAAG,IAAI,CAACpD,EAAE,CAACsD,aAAa,CAAC,uCAAuC,CAAC;UAC1E;MACR;MACA,IAAIF,SAAS,KAAK,IAAI,EAAE;QACpB,IAAI,CAAChB,QAAQ,CAACgB,SAAS,CAACf,KAAK,CAAC;QAC9B;QACA3C,EAAE,CAAC6D,cAAc,CAAC,CAAC;MACvB;IACJ,CAAC;IACD;AACR;AACA;IACQ,IAAI,CAACrB,kBAAkB,GAAIlC,EAAE,IAAK;MAC9B,IAAIU,EAAE;MACN,OAAOV,EAAE,GAAG,CAACU,EAAE,GAAGV,EAAE,CAACwD,YAAY,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI9C,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGV,EAAE,CAACyD,SAAS,GAAG,EAAE;IACvG,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,wBAAwB,GAAG,MAAM;MAClC,MAAM;QAAElD;MAAW,CAAC,GAAG,IAAI;MAC3B,MAAMmD,SAAS,GAAG,IAAI,CAACzB,kBAAkB,CAAC1B,UAAU,CAAC;MACrD;AACZ;AACA;AACA;AACA;AACA;MACY,OAAQ9D,CAAC,CAAC,KAAK,EAAE;QAAEkH,GAAG,EAAG5D,EAAE,IAAM,IAAI,CAACgC,kBAAkB,GAAGhC,EAAG;QAAE6D,KAAK,EAAE,qBAAqB;QAAEC,IAAI,EAAE,QAAQ;QAAEC,QAAQ,EAAE,IAAI,CAACjC,QAAQ,GAAG7C,SAAS,GAAG,CAAC;QAAE,YAAY,EAAE,IAAI,CAAC+E,SAAS;QAAE,eAAe,EAAE,CAAC;QAAE,eAAe,EAAE,CAAC;QAAE,eAAe,EAAE,CAAC;QAAE,gBAAgB,EAAEL,SAAS;QAAE,kBAAkB,EAAE,UAAU;QAAET,SAAS,EAAGxD,EAAE,IAAK,IAAI,CAACwD,SAAS,CAACxD,EAAE;MAAE,CAAC,CAAC;IAC1V,CAAC;IACD,IAAI,CAACsE,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC5E,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC0C,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACO,KAAK,GAAGpD,SAAS;IACtB,IAAI,CAACgF,KAAK,GAAG,SAAS;IACtB,IAAI,CAACtE,YAAY,GAAG,KAAK;EAC7B;EACAuE,gBAAgBA,CAACC,QAAQ,EAAE;IACvB,IAAI,CAACH,SAAS,GAAGG,QAAQ;EAC7B;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACjG,eAAe,EAAE;MACtB;AACZ;AACA;AACA;MACY,IAAI,CAACkG,wBAAwB,CAAC,IAAI,CAAC;IACvC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAAA,EAAG;IAChB;AACR;AACA;AACA;AACA;AACA;IACQ,MAAM7B,QAAQ,GAAI,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACzC,EAAE,CAACuE,OAAO,CAAC,YAAY,CAAE;IAChE,MAAMC,eAAe,GAAIC,OAAO,IAAK;MACjC;AACZ;AACA;AACA;MACY,MAAM/E,EAAE,GAAG+E,OAAO,CAACA,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC;MACtC,IAAIhF,EAAE,CAACiF,cAAc,EAAE;QACnB,MAAM;UAAEnE,UAAU;UAAER;QAAG,CAAC,GAAG,IAAI;QAC/B,IAAI,CAAC7B,eAAe,GAAG,IAAI;QAC3B;AAChB;AACA;AACA;QACgB,MAAMyG,SAAS,GAAG1H,cAAc,CAAC8C,EAAE,CAAC,CAACsD,aAAa,CAAC,IAAI/D,wBAAwB,EAAE,CAAC;QAClF,IAAIqF,SAAS,EAAE;UACX,IAAI,CAAC1F,wBAAwB,CAAC0F,SAAS,EAAE,KAAK,CAAC;QACnD;QACA,IAAI,CAACP,wBAAwB,CAAC,CAAC;QAC/B,IAAI7D,UAAU,EAAE;UACZ,IAAI,CAACtB,wBAAwB,CAACsB,UAAU,EAAE,IAAI,CAAC;QACnD;QACA,IAAI,CAACJ,wBAAwB,CAAC,CAAC;MACnC,CAAC,MACI;QACD,IAAI,CAACjC,eAAe,GAAG,KAAK;QAC5B,IAAI,IAAI,CAACoE,qBAAqB,EAAE;UAC5B,IAAI,CAACA,qBAAqB,CAAC,CAAC;UAC5B,IAAI,CAACA,qBAAqB,GAAGtD,SAAS;QAC1C;MACJ;IACJ,CAAC;IACD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI4F,oBAAoB,CAACL,eAAe,EAAE;MAAEM,SAAS,EAAE,KAAK;MAAEC,IAAI,EAAE,IAAI,CAACtC;IAAS,CAAC,CAAC,CAACuC,OAAO,CAAC,IAAI,CAAChF,EAAE,CAAC;IACrG,IAAIyC,QAAQ,KAAK,IAAI,EAAE;MACnB;MACAA,QAAQ,CAACH,gBAAgB,CAAC,oBAAoB,EAAG5C,EAAE,IAAK,IAAI,CAACD,eAAe,CAACC,EAAE,CAAC,CAAC;IACrF;EACJ;EACAuF,kBAAkBA,CAAA,EAAG;IACjB,MAAM;MAAEjF,EAAE;MAAEQ,UAAU;MAAErC,eAAe;MAAEkE;IAAM,CAAC,GAAG,IAAI;IACvD,IAAIlE,eAAe,IAAI,CAACqC,UAAU,EAAE;MAChC,MAAM0E,WAAW,GAAGlF,EAAE,CAACsD,aAAa,CAAC,0BAA0B,CAAC;MAChE;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI4B,WAAW,KAAK,IAAI,IAAIA,WAAW,CAAC7C,KAAK,KAAKA,KAAK,EAAE;QACrD,IAAI,CAACD,QAAQ,CAAC8C,WAAW,CAAC7C,KAAK,CAAC;MACpC;IACJ;EACJ;EACA;EACMgC,wBAAwBA,CAAC7F,MAAM,GAAG,KAAK,EAAE;IAAA,IAAA2G,KAAA;IAAA,OAAAC,iBAAA;MAC3C,MAAM7E,QAAQ,GAAG4E,KAAI,CAAC3E,UAAU;MAChC,IAAID,QAAQ,EAAE;QACV4E,KAAI,CAAC7G,sBAAsB,CAACiC,QAAQ,EAAE/B,MAAM,EAAE,KAAK,CAAC;MACxD;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACU4D,QAAQA,CAACC,KAAK,EAAE;IAAA,IAAAgD,MAAA;IAAA,OAAAD,iBAAA;MAClB,IAAIC,MAAI,CAACvD,QAAQ,KAAK,IAAI,IAAIuD,MAAI,CAAChD,KAAK,KAAKA,KAAK,EAAE;QAChD;MACJ;MACAgD,MAAI,CAAChD,KAAK,GAAGA,KAAK;MAClBgD,MAAI,CAACpH,SAAS,CAACqH,IAAI,CAAC;QAAEjD;MAAM,CAAC,CAAC;IAAC;EACnC;EACA;AACJ;AACA;AACA;EACUkD,QAAQA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAJ,iBAAA;MACb,IAAII,MAAI,CAACxD,kBAAkB,EAAE;QACzBwD,MAAI,CAACxD,kBAAkB,CAACyD,KAAK,CAAC,CAAC;MACnC;IAAC;EACL;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAIhF,EAAE;IACN,IAAI,CAACsD,SAAS,GAAG,CAACtD,EAAE,GAAG,IAAI,CAACV,EAAE,CAACwD,YAAY,CAAC,YAAY,CAAC,MAAM,IAAI,IAAI9C,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,gBAAgB;EAChH;EACA,IAAIF,UAAUA,CAAA,EAAG;IACb,MAAM;MAAE6B;IAAM,CAAC,GAAG,IAAI;IACtB,MAAMsD,OAAO,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7F,EAAE,CAAC8F,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;IAChF,OAAOH,OAAO,CAAC/D,IAAI,CAAEmE,MAAM,IAAK;MAC5B;AACZ;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAACjE,QAAQ,IAAIiE,MAAM,CAACjE,QAAQ,EAAE;QACnC,OAAO,KAAK;MAChB;MACA,OAAOiE,MAAM,CAAC1D,KAAK,KAAKA,KAAK;IACjC,CAAC,CAAC;EACN;EACA2D,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE/B,KAAK;MAAEnC,QAAQ;MAAE1C,QAAQ;MAAEO;IAAa,CAAC,GAAG,IAAI;IACxD,MAAMsG,IAAI,GAAGxI,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQf,CAAC,CAACE,IAAI,EAAE;MAAEyG,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAElG,kBAAkB,CAACsG,KAAK,EAAE;QAC5F,CAACgC,IAAI,GAAG,IAAI;QACZ,CAAC,sBAAsB,GAAG7G,QAAQ;QAClC,CAAC,6BAA6B,GAAGO,YAAY;QAC7C,CAAC,wBAAwB,GAAGmC;MAChC,CAAC;IAAE,CAAC,EAAE,IAAI,CAAC4B,wBAAwB,CAAC,CAAC,EAAEhH,CAAC,CAAC,MAAM,EAAE;MAAE2G,GAAG,EAAE,0CAA0C;MAAE6C,IAAI,EAAE;IAAS,CAAC,CAAC,EAAExJ,CAAC,CAAC,KAAK,EAAE;MAAE2G,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE,MAAM;MAAEQ,KAAK,EAAE,aAAa;MAAED,GAAG,EAAG5D,EAAE,IAAK;QACzO,IAAI,CAACvB,QAAQ,GAAGuB,EAAE;MACtB,CAAC;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACYmG,QAAQ,EAAE,CAAC;IAAE,CAAC,EAAEzJ,CAAC,CAAC,KAAK,EAAE;MAAE2G,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAE,mBAAmB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE,QAAQ,CAAC,EAAEnH,CAAC,CAAC,KAAK,EAAE;MAAE2G,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAE,mBAAmB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE,QAAQ,CAAC,EAAEnH,CAAC,CAAC,KAAK,EAAE;MAAE2G,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAE,mBAAmB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE,QAAQ,CAAC,EAAEnH,CAAC,CAAC,MAAM,EAAE;MAAE2G,GAAG,EAAE;IAA2C,CAAC,CAAC,EAAE3G,CAAC,CAAC,KAAK,EAAE;MAAE2G,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAE,mBAAmB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE,QAAQ,CAAC,EAAEnH,CAAC,CAAC,KAAK,EAAE;MAAE2G,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAE,mBAAmB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE,QAAQ,CAAC,EAAEnH,CAAC,CAAC,KAAK,EAAE;MAAE2G,GAAG,EAAE,0CAA0C;MAAEQ,KAAK,EAAE,mBAAmB;MAAE,aAAa,EAAE;IAAO,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAEnH,CAAC,CAAC,MAAM,EAAE;MAAE2G,GAAG,EAAE,0CAA0C;MAAE6C,IAAI,EAAE;IAAS,CAAC,CAAC,CAAC;EACh5B;EACA,IAAIlG,EAAEA,CAAA,EAAG;IAAE,OAAOlD,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWsJ,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,YAAY,EAAE,CAAC,kBAAkB,CAAC;MAClC,OAAO,EAAE,CAAC,aAAa;IAC3B,CAAC;EAAE;AACP,CAAC;AACD,MAAM7G,wBAAwB,GAAG,eAAe;AAChDzB,YAAY,CAACuI,KAAK,GAAGxI,qBAAqB;AAE1C,SAASC,YAAY,IAAIwI,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}